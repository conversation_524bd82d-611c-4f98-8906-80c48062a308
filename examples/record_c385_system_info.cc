/*************************************************************************
 > File Name: examples/record_c385_system_info.cc
 > Author: yafeng.zhao
 > Mail: <EMAIL>
 > Created Time: 2023年11月15日 星期三 19时16分05秒
 ************************************************************************/


#include "cyber/cyber.h"
#include "cyber/time/rate.h"
#include "cyber/time/time.h"
#include "modules/monitor/cpp/subprocess.hpp"
//#include "cyber/base/subprocess.hpp"

#include <unistd.h>
#include <thread>
#include <chrono>
#include <random>

inline void record_process(){
  using namespace subprocess;
  {
  AINFO << "cmd[============================================]";
  auto top = Popen({"top", "-d", "-i", "1","-z", "40", "-b", "-t"},output(PIPE));
  auto grep_avm = Popen({"grep", "ais_vision_server"}, input{top.output()}, output{PIPE});
  auto obuf_avm = grep_avm.communicate().first;
  AINFO << "cmd[top -d -i -z 40 -b -t | grep ais_vision_server] :\n" << obuf_avm.buf.data();
  }
  {
  auto top = Popen({"top", "-d", "-i", "1","-z", "40", "-b", "-t"},output(PIPE));
  auto grep_apa = Popen({"grep", "ais_apa_server"}, input{top.output()}, output{PIPE});
  auto obuf_apa = grep_apa.communicate().first;
  AINFO << "cmd[top -d -i -z 40 -b -t | grep ais_apa_server] :\n" << obuf_apa.buf.data();
  }
  {
  auto hogs = Popen({"hogs", "-i", "1"},output(PIPE));
  auto grep_hogs = Popen({"tail", "-n", "21"}, input{hogs.output()}, output{PIPE});
  auto obuf_hogs = grep_hogs.communicate().first;
  AINFO << "cmd[hogs -i 1 | tail -n 21] :\n" << obuf_hogs.buf.data();
  }
  auto obuf = check_output("aps show");
  AINFO << "cmd[aps show] :\n" << obuf.buf.data();
}

int main(int argc, char *argv[]) {
  apollo::cyber::Init(argv[0]);
  auto node = apollo::cyber::CreateNode("system_info");
  //static_assert(argc == 2, "argv[1] not exist");
  if(argc < 2){
      std::cout <<"error args time : [./exec 2]"<< std::endl;
      return 1;
  }
  int second = std::atoi(argv[1]);
  std::cout <<"sleep time : "<<second<<"s"<< std::endl;
  AINFO <<"sleep time : "<<second<<"s";

  bool exit = false;
  std::thread t([second, &exit](){
    for(;;) {
        if(exit) break;
        record_process();
        if(exit) break;
        std::this_thread::sleep_for(std::chrono::seconds(second));
        if(exit) break;
    }
  });
  apollo::cyber::WaitForShutdown();
  exit = true;
  t.join();
  return 0;
}

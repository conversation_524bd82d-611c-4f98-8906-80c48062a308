/*************************************************************************
 > File Name: test_gprof.cpp
 > Author: ya<PERSON>.zhao 
 > Mail: <EMAIL>
 > Created Time: 2022年12月04日 星期日 23时27分22秒
 ************************************************************************/

#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>

# g++ test_gprof.cpp -pg -o test_pg
# ./test_pg
# grpof test_pg gmon.out


void fun2()
{
    int i = 0;
    int j = 0;
    int sum = 0;
    for(i = 0; i < 1024; i++)
        for(j = 0; j < 10240; j++)
            sum = i + j;
}

void fun1()
{
    int i = 0;
    int j = 0;
    int sum = 0;
    int index = 0;

    for(index = 0; index < 4; index++)
        fun2();
    for(i = 0; i < 2048*2; i++)
        for(j = 0; j < 1024; j++)
            sum = i + j;
}

int main(int argc, char **argv)
{
    int index = 0;
    for(index = 0; index < 3; index++)
        fun1();
    return 0;
}

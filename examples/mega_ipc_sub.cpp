#include <iostream>
#include "base/cmdline.h"
#include "ipc/megaipc_api.h"

bool is_print_hex = false;

void printHex(const uint8_t *data, uint8_t len)
{
    char buff[512] = {0};
    for (int i = 0; i < len; i++)
    {
        sprintf(buff + i*3, "%02x ", data[i]);
    }
    std::cout << ""<< std::string(buff, strlen(buff)) << std::endl;
}

void on_message_arrival(const void *context, const char *topic, const struct IpcMessage *message)
{
   if(message && message->data)
      if(is_print_hex)
         printHex(message->data, message->length);
      else
         std::cout << std::string(topic) << ": " << std::string((char*)message->data, message->length) << std::endl;
   else
      std::cout << "on_message_arrival topic: " <<  std::string(topic) << std::endl;
}

int main(int argc, char *argv[])
{
   cmdline::parser args;
   args.add<std::string>("topic", 't', "subscribe topic", true);
   args.add<bool>("bytes", 'b', "print hex bytes", false);
   args.add<std::string>("help", 'h', "print this message", false);
   args.parse_check(argc, argv);

   std::string topic = args.get<std::string>("topic");
   is_print_hex = args.get<bool>("bytes");

   auto context = megaipc_pubsub_create_context();
   bool r = megaipc_pubsub_init(context);
   if (!r){
      std::cout << "Error: megaipc_pubsub_init ret = " << r << std::endl;
      megaipc_pubsub_destroy(context);
      return 0;
   }

   megaipc_pubsub_set_message_arrival_callback(&on_message_arrival);

   r = megaipc_pubsub_subscribe(context, topic.c_str());
   if (!r){
      std::cout << "Error: megaipc_pubsub_subscribe("<< topic <<") ret = " << r << std::endl;
      megaipc_pubsub_destroy(context);
      return 0;
   }

   while (1)
   {
      sleep(1);
   }

   megaipc_pubsub_destroy(context);

   return 0;
}
#include <iostream>
#include <chrono>
#include <thread>
#include "base/cmdline.h"
#include "ipc/megaipc_api.h"

bool is_delivered = false;

void on_message_delivered(const void* context, const char *msg_id)
{
   std::cout << "on_message_delivered msg_id: " <<  std::string(msg_id) << std::endl;
   is_delivered = true;
}

int main(int argc, char *argv[])
{
   cmdline::parser args;
   args.add<std::string>("topic", 't', "publish topic", true);
   args.add<std::string>("message", 'm', "publish message", true);
   args.add<std::string>("help", 'h', "print this message", false);
   args.parse_check(argc, argv);

   std::string topic = args.get<std::string>("topic");
   std::string msg = args.get<std::string>("message");

   auto context = megaipc_pubsub_create_context();
   bool r = megaipc_pubsub_init(context);
   if (!r){
      std::cout << "Error: megaipc_pubsub_init ret = " << r << std::endl;
      megaipc_pubsub_destroy(context);
      return 0;
   }

   megaipc_pubsub_set_message_delivered_callback(&on_message_delivered);

   IpcMessage message;
   message.data = (uint8_t*)msg.c_str();
   message.length = msg.size();
   message.retain = true;
   megaipc_pubsub_publish(context, topic.c_str(), &message);

   while (!is_delivered)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
   }


   megaipc_pubsub_destroy(context);

   return 0;
}
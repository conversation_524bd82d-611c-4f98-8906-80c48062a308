#include <iostream>
#include <sstream>
#include <functional>
#include <memory>
#include <thread>
#include <array>
#include <cstdlib>
#include <boost/asio.hpp>
#include "person/person_request_generated.h"
#include "person/person_response_generated.h"
#include <flatbuffers/flatbuffers.h>


using namespace adas::example::person;
typedef std::shared_ptr<boost::asio::ip::tcp::socket> socket_ptr;
class Server{
public:
    static void start_server(std::function<void(socket_ptr)> session, boost::asio::io_service& io_service, short port){
        boost::asio::ip::tcp::acceptor ser(io_service, boost::asio::ip::tcp::endpoint(boost::asio::ip::tcp::v4(), port));
        for (;;) {
            socket_ptr sock(new boost::asio::ip::tcp::socket(io_service));
            ser.accept(*sock);
            std::thread(std::bind(session, sock)).join();
        }
    }
};



void session(socket_ptr sock){
    boost::system::error_code ec;
    boost::asio::streambuf buffer;
    //boost::asio::read_until(sock, buffer, '\n');
    //std::string req(boost::asio::buffers_begin(buffer.data(), boost::asio::buffers_begin(buffer.data()) + buffer.size());
    //std::string req;
    //char req[1024] = {0};   
    //std::cout <<"req size = "<<buffer.size()<< std::endl;
    //boost::asio::read(*sock, buffer, ec);
    //std::cout <<"req size = "<<buffer.size()<< std::endl;
    /*sock->async_read_some(boost::asio::buffer(req), [](boost::system::error_code ec, std::size_t length){
        if(!ec){
            std::cout <<"legth : "<<length<< std::endl;
        }
         std::cout <<"legth : "<<length<< std::endl;
    });*/
    int loop = 0;
    label:loop++;
    uint8_t data_[1028] = {0};
    std::size_t length = sock->read_some(boost::asio::buffer(data_, 4), ec);
    std::size_t body_size_  = 0;
    if (ec == boost::asio::error::eof) {
        std::cout << "connection closed cleanly by peer" << std::endl;
        return;
    }else{
        std::cout <<"read header length : "<< length<< std::endl;
        /*
        for(int i = 0; i < 132; i++){
            std::cout <<i<<","<<int(data_[i]) << " ";
        }
        std::cout <<"\n";
        */
        char header[5] = {0};
        std::copy(data_, data_ + 4, header);
        body_size_ = header[3] | (header[2] << 8) | (header[1] << 16) | (header[0] << 24);
        std::cout <<"body size = "<<body_size_<< std::endl;
        length = sock->read_some(boost::asio::buffer(data_ + 4, body_size_), ec);
        std::cout <<"read body length : "<< length<< std::endl;
    }
    
    flatbuffers::Verifier vf(reinterpret_cast<const uint8_t *>(data_ + 4), body_size_);
    if (!VerifyPersonRequestBuffer(vf)) {
        std::cout <<"flatbuffer error!"<< std::endl;
        return;
    }
    const PersonRequest* request = GetPersonRequest(data_ + 4);
    auto builder = std::make_shared<flatbuffers::FlatBufferBuilder>();
    flatbuffers::Offset<PersonResponse> response;
    std::string firstName = request->firstName()->str();
    std::ostringstream sout;
    sout << "request: ";
    sout << "personId = " << request->personId() << ", ";
    sout << "firstName = " << firstName << ".";
    sout << std::endl;
    std::cerr << sout.str();
    if (firstName == "Alice")
    {
        response = CreatePersonResponse(
            *builder,
            builder->CreateString("Green #" + std::to_string(request->personId())),
            10);
    }
    else
    {
        response = CreatePersonResponse(
            *builder,
            builder->CreateString("Smith #" + std::to_string(request->personId())),
            20);
    }
    builder->Finish(response);
    //std::string res((char*)builder->GetBufferPointer(), builder->GetSize());
    //cb(res, [builder](std::string res) { });
    uint8_t *buf = builder->GetBufferPointer();
    int size = builder->GetSize();
    char resp_header[5] = {0};
    uint8_t resp_data_[1028] = {0};
    //std::sprintf(resp_header, "%4d", static_cast<int>(size));
    resp_header[0] = char((size & 0xff000000) >> 24) ;
    resp_header[1] = char((size & 0xff0000) >> 16);
    resp_header[2] = char((size & 0xff00) >> 8);
    resp_header[3] = char(size & 0xff);
    std::copy(resp_header, resp_header + 4, resp_data_);
    
    /*
    for(int i = 0; i < 132; i++){
        std::cout <<i<<","<<int(resp_data_[i]) << " ";
    }
    std::cout << std::endl;
    */
    std::copy(buf, buf + size, resp_data_ + 4);
    boost::asio::write(*sock, boost::asio::buffer(resp_data_, size + 4));
    std::cout <<"============= read loop : "<<loop<<" ==============="<< std::endl;
    goto label;
}


int main(int argc, char **argv)
{
    boost::asio::io_service io_service;
    std::cout << "Starting boost server" << std::endl;
    std::cout <<__cplusplus<< std::endl;
    Server::start_server(session, io_service, 12307);
    return 0;
}

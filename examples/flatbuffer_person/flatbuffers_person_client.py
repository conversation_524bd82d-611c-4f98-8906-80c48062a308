#!/usr/bin/env python3.6

import socket
import struct
import flatbuffers

from adas.example.person.PersonRequest import *
from adas.example.person.PersonResponse import *

def request_person(client, person_id, first_name):
    builder = flatbuffers.Builder(0)
    first_name = builder.CreateString(first_name)
    PersonRequestStart(builder)
    PersonRequestAddPersonId(builder, person_id)
    PersonRequestAddFirstName(builder, first_name)
    request = PersonRequestEnd(builder)
    builder.Finish(request)
    data = builder.Output()
    client.send(struct.pack('>I', len(data)))
    client.send(data)
    header = client.recv(4)
    if len(header) != 4:
        message = "fail to receive response length"
        raise RuntimeError(message)
    length, = struct.unpack('>I', header)
    buf = client.recv(length)
    #print("buf : ", len(buf))
    #print("length : ", length)
    if len(buf) != length:
        message = "fail to receive response body"
        raise RuntimeError(message)
    response = PersonResponse.GetRootAsPersonResponse(buf, 0)
    last_name = response.LastName()
    age = response.Age()
    return last_name.decode(), age

def run_simple_auto_tests(args, client):
    req = 1, 'Alice'
    print('req: %s' % (req,))
    res = request_person(client, *req)
    print('res: %s' % (res,))
    assert res == ('Green #1', 10)

    req = 2, 'Bob'
    print('req: %s' % (req,))
    res = request_person(client, *req)
    print('res: %s' % (res,))
    assert res == ('Smith #2', 20)



def run_simple_tests(args, client):
    if args.auto:
        run_simple_repl_tests(args, client)
    else:
        run_simple_auto_tests(args, client)
        

def run_tests(args):
    client = socket.socket()
    try:
        client.connect(('localhost', args.port))
        run_simple_tests(args, client)
    finally:
        client.close()

def main():
    import argparse
    parser = argparse.ArgumentParser(description="FlatBuffers Person Client in Python")
    parser.add_argument('-a', '--auto', action='store_true',
        help="test automatically")
    parser.add_argument('-p', '--port', type=int, default=12307,
        help="port number; default to 12307")
    args = parser.parse_args()
    run_tests(args)

if __name__ == '__main__':
    main()

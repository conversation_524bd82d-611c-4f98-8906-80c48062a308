find_package(Boost REQUIRED COMPONENTS system thread)
find_package(Flatbuffers REQUIRED)

set(ns person)
execute_process(COMMAND mkdir -p  ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_request/${ns})
#message("flatbuffers out directory : "${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_request/${ns})
add_custom_target(flatbuffers_person_request_cpp ALL
    BYPRODUCTS ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_request/${ns}/person_request_generated.h
    COMMAND flatc --cpp --scoped-enums --reflect-names
            -o ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_request/${ns}/
            ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_request.fbs
    DEPENDS ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_request.fbs)

add_custom_target(flatbuffers_person_response_cpp ALL
    BYPRODUCTS ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_response/${ns}/person_response_generated.h
    COMMAND flatc --cpp --scoped-enums --reflect-names
            -o ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_response/${ns}/
            ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_response.fbs
    DEPENDS  ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_response.fbs)

add_custom_target(flatbuffers_person_request_py ALL
    BYPRODUCTS ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/adas/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/adas/rpc/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/adas/rpc/unittest/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/${ns}/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/${ns}/PersonRequest.py
    COMMAND flatc --python
            -o ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/
            ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_request.fbs
    DEPENDS  ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_request.fbs)

add_custom_target(flatbuffers_person_response_py ALL
    BYPRODUCTS ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/adas/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/adas/rpc/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/adas/rpc/unittest/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/${ns}/__init__.py
               ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/${ns}/PersonResponse.py
    COMMAND flatc --python
            -o ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/
            ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_response.fbs
    DEPENDS  ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/person_response.fbs)

add_executable(flatbuffers_person_server
    ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/flatbuffers_person_server.cpp
    ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_request/${ns}/person_request_generated.h
    ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_response/${ns}/person_response_generated.h
)
target_link_libraries(flatbuffers_person_server PRIVATE  flatbuffers pthread)
target_include_directories(flatbuffers_person_server PRIVATE ${Boost_INCLUDE_DIRS})
target_include_directories(flatbuffers_person_server PRIVATE ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_request)
target_include_directories(flatbuffers_person_server PRIVATE ${PROJECT_BINARY_DIR}/gen/flatbuffers/cpp/${ns}_response)
add_dependencies(flatbuffers_person_server flatbuffers_person_request_cpp)
add_dependencies(flatbuffers_person_server flatbuffers_person_response_cpp)

add_custom_target(flatbuffers_person_client ALL
    COMMAND ${CMAKE_COMMAND} -E copy
                ${PROJECT_SOURCE_DIR}/examples/flatbuffer_person/flatbuffers_person_client.py
            ${PROJECT_BINARY_DIR}/flatbuffers_person_client
    COMMAND chmod a+x ${PROJECT_BINARY_DIR}/flatbuffers_person_client
    COMMAND ${CMAKE_COMMAND} -E make_directory
            ${PROJECT_BINARY_DIR}/adas
    COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_request/adas
            ${PROJECT_BINARY_DIR}/adas
    COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PROJECT_BINARY_DIR}/gen/flatbuffers/py/${ns}_response/adas
            ${PROJECT_BINARY_DIR}/adas)
add_dependencies(flatbuffers_person_client flatbuffers_person_request_py)
add_dependencies(flatbuffers_person_client flatbuffers_person_response_py)

syntax = "proto3";

package apollo.cyber.examples.proto;


message TestImu{
   double timestamp = 1;
   double time = 2;
};

message SamplesTest1 {
   string class_name = 1;
   string case_name = 2;
};

message Chatter {
   uint64 timestamp = 1;
   uint64 lidar_timestamp = 2;
   uint64 seq = 3;
   bytes content = 4;
   double sys_timestamp = 5;

};

message Driver {
   string content = 1;
   uint64 msg_id = 2;
   uint64 timestamp = 3;
};

message Header {
  //Message passing time in seconds
   double timestamp = 1;
   string module_name = 2;
  //Sequence number maintained by each module from boot.
   uint32 sequence_num = 3;
  //Unique id from each frame. Can be generated by tools such as boost::uuids
   string frame_id = 4;
  //If not empty, it means the source data for current output frame
   string trigger_frame = 5;
   uint32 version = 6;
}

message Image {
   Header header=1;
   double measurement_time = 2; //time in seconds
   string encoding = 3;  //type of encoding. currently only "RGB8" and "RGBA8" supported.
   uint32 height = 4;
   uint32 width = 5;
   uint32 depth = 6; //bytes per pixel. It's redundant to encoding.
   bytes data = 7;
   string start = 8; //start from which corner. support "topleft" and "bottomleft"
}

message Chassis{
   Header header = 1;
   double velocity = 2;
   double lon_acceleration = 3;
   double lat_acceleration = 4;
   double yaw_rate = 5;
   double cur_torque = 6;
   bool brake_applied = 7;
   int32 epb_sys_st = 8;
   int32 gear_pos = 9;
   int32 takeover_stat = 10;
   uint64 measure_time = 11;

}

message PointXYZIT {
   float x = 1;
   float y = 2;
   float z = 3;
   uint32 intensity = 4;
   uint64 timestamp = 5;
}

message PointCloud {
   Header header = 1;
   string frame_id = 2;
   bool is_dense = 3;
  repeated PointXYZIT point = 4;
   double measurement_time = 5;
   uint32 width = 6;
   uint32 height = 7;
}

message Val{
   int32 value = 1;
}

syntax = "proto2";

package autoplt.com.examples.proto;

message Chatter {
    optional double timestamp = 1;
    optional double lidar_timestamp = 2;
    optional uint64 seq = 3;
    optional bytes content = 4;
    optional uint64 shm_block_index = 5;
    optional uint64 shm_size = 6;
};

message Driver {
    optional string content = 1;
    optional uint64 msg_id = 2;
    optional uint64 timestamp = 3;
};

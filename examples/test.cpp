/*************************************************************************
 > File Name: test.cpp
 > Author: yafeng.zhao 
 > Mail: <EMAIL>
 > Created Time: 2022年11月17日 星期四 10时42分27秒
 ************************************************************************/

#include <iostream>
#include <climits>
#include <cmath>

bool equal(float num, float num1){
    if(std::fabs(num - num1) < std::numeric_limits<double>::epsilon()){
        return true;
    }
    return false;
}

int main(){
    auto f1 = 1e-13;
    auto f2 = 1e-13;
    auto ret = equal(f1, f2);
    if(ret){
        printf("%f, %f equal\n",f1,f2);
    }else{
        printf("%f, %f not equal\n",f1,f2);
    }
    return 0;
}

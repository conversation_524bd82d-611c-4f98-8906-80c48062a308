/*************************************************************************
 > File Name: qnx_zip.cc
 > Author: yafeng.zhao
 > Mail: <EMAIL>
 > Created Time: 2023年08月29日 星期二 18时02分56秒
 ************************************************************************/

#include "cyber/cyber.h"
#include "cyber/time/rate.h"
#include "cyber/time/time.h"

#include <iostream>
#include <string>
#include <vector>
#include <zip.h>
#include <dirent.h>
#include <sys/stat.h>


bool addFileToZip(zip_t *archive, const std::string &filePath, const std::string &relativePath) {
    zip_source_t *source = zip_source_file(archive, filePath.c_str(), 0, 0);
    if (!source) {
        std::cerr << "Failed to create source for file: " << filePath << std::endl;
        return false;
    }

    if (zip_file_add(archive, relativePath.c_str(), source, ZIP_FL_ENC_UTF_8) < 0) {
        std::cerr << "Failed to add file to ZIP: " << filePath << std::endl;
        zip_source_free(source);
        return false;
    }

    return true;
}

bool compressDirectory(const std::string &inputDir, const std::string &outputZipPath) {
    zip_t *archive = zip_open(outputZipPath.c_str(), ZIP_CREATE | ZIP_TRUNCATE, nullptr);
    if (!archive) {
        std::cerr << "Failed to open output ZIP file." << std::endl;
        return false;
    }

    DIR *dir;
    struct dirent *entry;

    if ((dir = opendir(inputDir.c_str())) == NULL) {
        std::cerr << "Failed to open input directory: " << inputDir << std::endl;
        return false;
    }

    while ((entry = readdir(dir)) != NULL) {
        std::string entryName = entry->d_name;
        if (entryName == "." || entryName == "..") {
            continue;
        }

        std::string fullPath = inputDir + "/" + entryName;

        struct stat fileStat;
        if (stat(fullPath.c_str(), &fileStat) == 0) {
            if (S_ISDIR(fileStat.st_mode)) {
                // Do nothing for directories in this example
            } else {
                if (!addFileToZip(archive, fullPath, entryName)) {
                    closedir(dir);
                    zip_close(archive);
                    return false;
                }
            }
        }
    }

    closedir(dir);
    zip_close(archive);
    return true;
}
int main(int argc, char** argv) {
    std::string inputDirectory = "test";
    std::string outputZipPath = "compressed.zip";

    for(int i = 0;i < 100; i++){
        apollo::cyber::transport::Identity cyber_session_id(true);
        uint64_t session_id = cyber_session_id.HashValue();
        std::cout <<session_id<<"\t";
    }
    std::cout <<"\n";

    if(argc > 2){
        inputDirectory = argv[1];
        outputZipPath = argv[2];
    }else{
        if(argc != 1){
            std::cout <<"./qnx_zip -h"<< std::endl;
            std::cout <<"./qnx_zip input_dir output.zip"<< std::endl;
        }
    }

    std::cout <<"./qnx_zip "<<inputDirectory <<" "<<outputZipPath<< std::endl;
    if (compressDirectory(inputDirectory, outputZipPath)) {
        std::cout << "Compression complete." << std::endl;
    } else {
        std::cerr << "Compression failed." << std::endl;
    }

    return 0;
}


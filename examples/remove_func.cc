/*************************************************************************
 > File Name: qnx_zip.cc
 > Author: yafeng.zhao
 > Mail: <EMAIL>
 > Created Time: 2023年08月29日 星期二 18时02分56秒
 ************************************************************************/



#include <iostream>
#include <string>
#include <vector>
#include <dirent.h>
#include <sys/stat.h>

#include <cstdio>

int remove_dir(std::string dir_name){
  std::cout << " remove_dir " << dir_name << std::endl;
    DIR *dr;
    struct dirent *en;
    if ((dr = opendir(dir_name.c_str())) == NULL) {
        std::cout << "Failed to open input directory: " << dir_name ;
        return -1;
    }

    while ((en = readdir(dr)) != NULL) {
      std::string file_name = en->d_name;
      std::string fullPath = dir_name + "/" + file_name;
      std::cout <<"-------------------------- " <<std::endl;
      std::cout << file_name << std::endl;

      if (file_name =="." || file_name =="..")
      {
        std::cout <<"contian . or .. :" << fullPath << std::endl;
        continue;
      }
      

      struct stat fileStat;
      if (stat(fullPath.c_str(), &fileStat) == 0) {
          if (S_ISDIR(fileStat.st_mode)) {
              remove_dir(fullPath);
          } else {
           auto result =  remove(fullPath.c_str());
            std::cout  << "  remove(fullPath.c_str()); " << fullPath << std::endl;
          }
      }
    }
    closedir(dr); //close all directory
    auto result = remove(dir_name.c_str());
    std::cout  << "  remove(dir_name.c_str()); " << dir_name << std::endl;
    
    return 0;
}


int main(int argc, char** argv) {
 std::string  file = argv[1];
  int rmdir_result =  remove_dir(file);
  std::cout <<rmdir_result <<  "rmdir-------------------------------"  << file << std::endl;
}


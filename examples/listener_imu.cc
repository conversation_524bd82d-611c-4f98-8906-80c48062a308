/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/cyber.h"
#include "examples/proto/examples.pb.h"

#include "thirdparty/recommend_protocols/drivers/proto/imu.pb.h"

using apollo::cyber::examples::proto::TestImu;
using rainbowdash::drivers::Imu;

int main(int argc, char *argv[]) {
  // init cyber framework
  apollo::cyber::Init(argv[0]);
  // create listener node
  auto listener_node = apollo::cyber::CreateNode("listener_imu");
  // create listener
  auto talker =
      listener_node->CreateWriter<TestImu>("channel/imu_measurement_time");
  
  static int count = 0;
  static int mt = 0;
  static int lt = 0;
  auto listener = listener_node->CreateReader<Imu>(
      "channel/imu", [&talker](std::shared_ptr<Imu> msg) {
        auto GPS_UNIX_DIFF = int64_t(315964800) * 1000;
        auto LEAP_SECONDS = 18;
        auto gps_measurement_time =
            (msg->ins_gnns().week() * int64_t(7 * 24 * 3600 * 1000) + msg->ins_gps().time() + GPS_UNIX_DIFF) / (1000.0) - LEAP_SECONDS;
        // printf("measure_time : %f\t gps_cal_mesaure_time:%f\n", msg->measurement_time(), gps_measurement_time);
        auto imu = std::make_shared<TestImu>();
        imu->set_timestamp(gps_measurement_time);
        imu->set_time(msg->ins_gps().time());
        talker->Write(imu);
        if(msg->measurement_time() > msg->header().timestamp()){
            AINFO << "more than : "<<std::to_string(msg->measurement_time()) <<", "<< std::to_string(msg->header().timestamp()) <<", count : "<<mt++<<", all : "<<count++;
        }else if(msg->measurement_time() < msg->header().timestamp()){
            AINFO << "less than : "<<std::to_string(msg->measurement_time()) <<", "<< std::to_string(msg->header().timestamp()) <<", count : "<<lt++<<", all : "<<count++;
        }else{
            AINFO << "equal : "<<std::to_string(msg->measurement_time()) <<", "<< std::to_string(msg->header().timestamp()) << "all :  "<<count++;
        }
      });
  apollo::cyber::WaitForShutdown();
  return 0;
}

/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "examples/proto/examples.pb.h"

#include "cyber/cyber.h"

void MessageCallback(
    const std::shared_ptr<apollo::cyber::examples::proto::Chatter>& msg) {
    AINFO << "1Received message seq-> " << msg->seq();
    //AINFO << "1msgcontent->" << msg->content();

    static int i = 0;
    //std::cout <<"NO."<<i++<< " Received message seq-> " << msg->seq() << ",msgcontent->" << msg->content() <<"\n";
}

int main(int argc, char* argv[]) {
  // init cyber framework
  std::cout << "listener start!\n";
  apollo::cyber::Init(argv[0]);
  std::cout << "listener init success!\n";
  // create listener node
  auto listener_node = apollo::cyber::CreateNode("listener");
  std::cout << "listener node create success!\n";
  // create listener
  auto listener =
      listener_node->CreateReader<apollo::cyber::examples::proto::Chatter>(
          "channel/chatter", MessageCallback);
  auto imu_callback = [](const std::shared_ptr<apollo::cyber::examples::proto::Chatter>& msg){
    int count = 0;
    static int i = 0;
    AINFO << "Received message count: " << count;
    /*
    for (int j = 0; j < 10000000; j++){
        for(int i = 0; i < 1000; i++){
            count += msg->seq() + i + j;
        }
    }
    */
    AINFO << "2Received message count: " << count;
    //AINFO << "2msgcontent->" << msg->content();
    //std::cout <<"NO."<<i++<< " 2Received message seq-> " << msg->seq() << ",msgcontent->" << msg->content() <<"\n";
  };

  auto listener_imu = 
      listener_node->CreateReader<apollo::cyber::examples::proto::Chatter>(
          "channel/imu", imu_callback);
  std::cout << "listener node create success!!!!\n";
  apollo::cyber::WaitForShutdown();
  return 0;
}

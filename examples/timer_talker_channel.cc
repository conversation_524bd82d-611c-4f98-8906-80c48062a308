/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "examples/proto/examples.pb.h"

#include "cyber/cyber.h"
#include "cyber/time/rate.h"
#include "cyber/time/time.h"

#include <unistd.h>
#include <thread>
#include <chrono>
#include <random>
#include <string>

using apollo::cyber::Rate;
using apollo::cyber::Time;
using apollo::cyber::examples::proto::Chatter;

int main(int argc, char *argv[]) {
  std::cout << "talker start!\n";
  apollo::cyber::Init( "_test");
  std::cout << "talker init success!\n";
  auto talker_node = apollo::cyber::CreateNode("talkerxx");
  std::cout << "node create success!\n";
  // create talker
  auto talker = talker_node->CreateWriter<Chatter>("channel/chatter");
  auto imu_talker = talker_node->CreateWriter<Chatter>("channel/imu");
  std::cout << "talk node create success!\n";
  uint64_t seq = 0;
  std::random_device dev;
  std::mt19937 rng(dev());
  std::uniform_int_distribution<std::mt19937::result_type> dist6(1,100000000); // distribution in range [1, 6]

  auto start = Time::Now();
  std::string big_data;
  big_data.resize(3000000);
  auto writer_callback = [&](){
    auto end = Time::Now();

    auto elapsed_seconds = end - start;

    //std::cout << "[XXXXXXX] elapsed time: " << elapsed_seconds<< std::endl;
    std::cout  << "start talker, No. " << seq<< std::endl;

    auto msg = std::make_shared<Chatter>();
    msg->set_timestamp(Time::Now().ToNanosecond());
    msg->set_lidar_timestamp(Time::Now().ToNanosecond());
    msg->set_seq(seq);
    msg->set_content(big_data);
    talker->Write(msg);
    //std::cout  << "talker finish sent a message! No. " << seq<<"\n";
    seq++;
  };
  auto talk_timer = std::make_shared<apollo::cyber::Timer>(100, writer_callback, false);

  auto imu_writer_callback = [&](){
    auto end = Time::Now();
    auto elapsed_seconds = end - start;
    //std::cout << "[XXXXXXX] elapsed time: " << elapsed_seconds<< std::endl;
    std::cout  << "start talker, No. " << seq<< std::endl;
    auto msg = std::make_shared<Chatter>();
    msg->set_timestamp(Time::Now().ToNanosecond());
    msg->set_lidar_timestamp(Time::Now().ToNanosecond());
    msg->set_seq(seq);
    msg->set_content(big_data);
    //msg->set_content("Hello, apollo! random : " + std::to_string(dist6(rng)));
    imu_talker->Write(msg);
    seq++;
  };

  auto imu_talk_timer = std::make_shared<apollo::cyber::Timer>(10, imu_writer_callback, false);

  talk_timer->Start();
  imu_talk_timer->Start();

  std::cout << "finally talker finish sent a message! No. " << seq << "\n";
  apollo::cyber::WaitForShutdown();
  return 0;
}

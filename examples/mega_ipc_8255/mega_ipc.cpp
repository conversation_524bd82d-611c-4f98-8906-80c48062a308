#include "mega_ipc.h"
#include <functional>
#include <string>
#include <iostream>
#include <unistd.h>
#include "megaipc_api.h"

using std::string;

MegaIpcTest::MegaIpcTest(): m_context(new MegaIpcApi())
{
    using namespace std::placeholders;
    m_context->init();
    m_context->setMessageArrivalCallback(std::bind(&MegaIpcTest::messageArrival, this, _1, _2));
    m_context->setMessageDeliveredCallback(std::bind(&MegaIpcTest::messageDelivered, this, _1));
    m_context->setConnectstateChangedCallback(std::bind(&MegaIpcTest::connectStateChanged, this, _1, _2));
    m_context->subscribe("mega_ipc/override/test");
}

MegaIpcTest::~MegaIpcTest()
{
    if(m_context != nullptr)
    {
        m_context->destroy();
    }

    delete m_context;
}

void MegaIpcTest::messageArrival(const char* topic, const IpcMessage* msg)
{
    sleep(1);
    std::cout << "MegaIpcTest::onMessageArrival recive " << topic << msg->data << std::endl;
}

void MegaIpcTest::messageDelivered(const char* msgId)
{
    std::cout << "MegaIpcTest::onMessageDelivered " << msgId << std::endl;
}

void MegaIpcTest::connectStateChanged(const char* nodeId, const ConnectState* state)
{
    std::cout << "MegaIpcTest::onConnectStateChanged " << std::endl;
}

void MegaIpcTest::sendMsg()
{
    string data("123456");
    IpcMessage msg;
    msg.length = (uint32_t)data.size();
    msg.data = (uint8_t*)data.data();
    m_context->publish("mega_ipc/callback/test", msg);
}


MegaIpcListenerTest::MegaIpcListenerTest(): m_context(new MegaIpcApi())
{
    m_context->init();
    m_context->setListener(this);
    m_context->subscribe("mega_ipc/callback/test");
}

MegaIpcListenerTest::~MegaIpcListenerTest()
{
    if(m_context != nullptr)
    {
        m_context->destroy();
    }

    delete m_context;
}

void MegaIpcListenerTest::onMessageArrival(const string &topic, const IpcMessage &msg)
{
    std::cout << "MegaIpcListenerTest::onMessageArrival recive " << topic << msg.data << std::endl;
}

void MegaIpcListenerTest::onMessageDelivered(const string &msgId)
{
    std::cout << "MegaIpcListenerTest::onMessageDelivered " << msgId << std::endl;
}

void MegaIpcListenerTest::onConnectStateChanged(const string &nodeId, const ConnectState &state)
{
    std::cout << "MegaIpcListenerTest::onConnectStateChanged " << std::endl;
}

void MegaIpcListenerTest::onMessageArrival(const string &topic, const RequestMsg &req_msg, ResponseMsg **resp_msg)
{
    std::cout << "MegaIpcListenerTest request message arrival" << std::endl;
}

void MegaIpcListenerTest::sendMsg()
{
    string data("654321");
    IpcMessage msg;
    msg.length = (uint32_t)data.size();
    msg.data = (uint8_t*)data.data();
    m_context->publish("mega_ipc/override/test", msg);
}


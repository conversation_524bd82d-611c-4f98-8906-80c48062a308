ifndef QCONFIG
QCONFIG=qconfig.mk
endif
include $(QCONFIG)
include $(AMSS_ROOT)/amss_defs_bf.mk

#===== Macros for install directory and library naming
NAME=megaipc_cpptest

include $(MEGA_ROOT)/mega_defs.mk
INSTALLDIR := $(INSTALLDIR_VENDOR_COMMON_BIN)

#===== USEFILE - the file containing the usage message for the application.
USEFILE=
#===== PINFO - the file containing the packaging information for the application.
define PINFO
PINFO DESCRIPTION=megaipc_cpptest
endef

#===== CCFLAGS - add the flags to the C compiler command line.
CCFLAGS+=-Wno-sequence-point -Wno-extra -Wno-unused-function -Wno-error=unused-variable
CXXFLAGS+=-std=c++14 -Wno-error=attributes -D_QNX_SOURCE

#===== EXTRA_SRCVPATH - a space-separated list of directories to search for source files.

#===== EXTRA_INCVPATH - a space-separated list of directories to search for include files.
EXTRA_INCVPATH+=$(PROJECT_ROOT)/inc	\
		$(INSTALL_ROOT_nto)/usr/include/ \
                $(MEGA_ROOT)/prebuilts/sdk/mega_ipc/include

EXTRA_LIBVPATH+=$(AMSS_INSTALL_ROOT)install/armle-v7/lib \
                $(MEGA_ROOT)/prebuilts/sdk/mega_ipc/lib

ifeq ($(FEATURE_IO_SOCK), 1)
    EXTRA_LIBVPATH+=$(QNX_TARGET)/aarch64le/io-sock/lib
endif

LIBS+=mega_ipc
include $(MKFILES_ROOT)/qtargets.mk



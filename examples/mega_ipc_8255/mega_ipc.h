#include "ipc_listener_interface.h"
#include "megaipc_api.h"
#include <string>

using std::string;
using namespace megaipc;

class MegaIpcTest
{
public:
    MegaIpcTest();
    ~MegaIpcTest();
    void messageArrival(const char* topic, const IpcMessage* msg);
    void messageDelivered(const char* msgId);
    void connectStateChanged(const char* nodeId, const ConnectState* state);
    void sendMsg();

private:
    MegaIpcApi* m_context;
};


class MegaIpcListenerTest: public IPCListenerInterface
{
public:
    MegaIpcListenerTest();
    ~MegaIpcListenerTest();
    void onMessageArrival(const string &topic, const IpcMessage &msg);
    void onMessageDelivered(const string &msgId);
    void onConnectStateChanged(const string &nodeId, const ConnectState &state);
    void onMessageArrival(const string &topic, const RequestMsg &req_msg, ResponseMsg **resp_msg);
    void sendMsg();
private:
    MegaIpcApi* m_context;
};

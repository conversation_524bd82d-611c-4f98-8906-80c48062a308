
#include <stdlib.h>
#include <iostream>
#include <iomanip>
#include "autoplt/include/ADSNode.h"
#include "autoplt/include/ADSTime.h"
#include "examples/proto/BST_examples.pb.h"
#include <ads_log/logger.h>

using namespace std;
using namespace autoplt;
using autoplt::com::examples::proto::Chatter;

int main(int argc, char *argv[]) {

  // 初始化通信框架
  ADSNode::Init(argv[0]);
  // 创建Node
  std::shared_ptr<ADSNode> talker_node = std::make_shared<ADSNode>("auto_test_talker");

  std::string channel_name = "autoplt/test/chatter";
  // 创建Talker
  auto talker = talker_node->CreateWriter<Chatter>( channel_name );
  if(talker==nullptr)
  {
    AERROR << "[Example talker] talker create failed";
    return 0;
  }
  apollo::cyber::Rate rate(5.0);
  while (apollo::cyber::OK()) {
    // 构造消息
    static uint64_t seq = 0;
    auto msg = std::make_shared<Chatter>();
    msg->set_lidar_timestamp(ADSTime::Now().ToNanosecond());
    msg->set_seq(seq++);
    msg->set_content("Hello ADS-COM");
    msg->set_timestamp(ADSTime::Now().ToSecond());
    // 发送消息
    if(!talker->Write(msg))
    {
      AWARN  << "[Example intra com] Write msg failed.";
      continue;
    }
    std::cout << std::setprecision(20) << "talker sent a message!" << std::endl;
    rate.Sleep();
  }
  return 0;
}


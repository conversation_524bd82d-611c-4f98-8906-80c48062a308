#include <iostream>
#include <fstream> 
#include "autoplt/include/ADSNode.h"
#include "autoplt/include/ADSTime.h"
#include "examples/proto/BST_examples.pb.h"
#include <ads_log/logger.h>

using namespace std;
using namespace autoplt;
using autoplt::com::examples::proto::Chatter;

void MessageCallback( const std::shared_ptr<autoplt::com::examples::proto::Chatter>& msg) {
  std::cout << "You have received message from talker, which indicates that the inter-process communication is OK,  seq-> " << msg->seq()<< std::endl;
}

int main(int argc, char* argv[]) {

  // 初始化通信框架
  ADSNode::Init(argv[0]);
  // 创建Node
  std::shared_ptr<ADSNode> listener_node = std::make_shared<ADSNode>("auto_test_listener");

  // create listener
  auto listener =
      listener_node->CreateReader<autoplt::com::examples::proto::Chatter>(
          "autoplt/test/chatter", MessageCallback);
  if(listener==nullptr)
  {
    AERROR << "[Example listener] listner create failed.";
    return 0;
  }
  apollo::cyber::WaitForShutdown();
  return 0;
}
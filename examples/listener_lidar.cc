#include "cyber/cyber.h"
#include "examples/proto/examples.pb.h"

#include "thirdparty/recommend_protocols/drivers/proto/pointcloud.pb.h"

using apollo::cyber::examples::proto::TestImu;
using rainbowdash::drivers::PointCloud;

int main(int argc, char *argv[]) {
  // init cyber framework
  apollo::cyber::Init(argv[0]);
  // create listener node
  auto listener_node = apollo::cyber::CreateNode("listener_lidar");
  // create listener
  auto talker =
      listener_node->CreateWriter<TestImu>("channel/pointcloud_measurement_time");
  
  static int count = 0;
  static int mt = 0;
  static int lt = 0;
  auto listener = listener_node->CreateReader<PointCloud>(
      "channel/pointcloud", [&talker](std::shared_ptr<PointCloud> msg) {
        auto imu = std::make_shared<TestImu>();
        imu->set_timestamp(msg->measurement_time());
        talker->Write(imu);
        if(msg->measurement_time() > msg->header().timestamp()){
            AINFO << "more than : "<<std::to_string(msg->measurement_time()) <<", "<< std::to_string(msg->header().timestamp()) <<", count : "<<mt++<<", all : "<<count++;
        }else if(msg->measurement_time() < msg->header().timestamp()){
            AINFO << "less than : "<<std::to_string(msg->measurement_time()) <<", "<< std::to_string(msg->header().timestamp()) <<", count : "<<lt++<<", all : "<<count++;
        }else{
            AINFO << "equal : "<<std::to_string(msg->measurement_time()) <<", "<< std::to_string(msg->header().timestamp()) << "all :  "<<count++;
        }
      });
  apollo::cyber::WaitForShutdown();
  return 0;
}

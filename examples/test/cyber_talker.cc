#include "cyber/cyber.h"
#include "thirdparty/recommend_protocols/perception/proto/parking_slot.pb.h"

using rainbowdash::common::Header;
using rainbowdash::common::Point2D;

using apollo::cyber::Rate;
using apollo::cyber::Time;
using apollo::cyber::Node;

int main(int argc, char* argv[])
{
  apollo::cyber::Init(argv[0]);
  auto node = apollo::cyber::CreateNode("talker");
  //auto talker = createHeaderWriter(node, "channel/chatter");
  auto talker = node->CreateWriter<Point2D>("channel/chatter");
  Rate rate(1.0);
  uint64_t seq = 0;
  while (apollo::cyber::OK()) {
    auto msg = std::make_shared<Point2D>();
    //msg->set_timestamp(Time::Now().ToNanosecond());
    //msg->set_sequence_num(seq);
    //msg->set_frame_id("Hello, apollo!");
    msg->set_x(1.2);
    talker->Write(msg);
    AINFO << "talker sent a message! No. " << seq;
    seq++;
    rate.Sleep();
  }

  return 0;
}

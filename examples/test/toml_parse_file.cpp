//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2022/4/4.
//

#include <toml.hpp>
#include <iostream>
#include <chrono>

int main()
{
    const auto data  = toml::parse("../examples/test/example.toml");
    const auto title = toml::find<std::string>(data, "title");
    std::cout << "the title : " << title << std::endl;
    const auto answer    = toml::find<std::int64_t>(data, "answer");
    const auto pi        = toml::find<double>(data, "pi");
    const auto numbers   = toml::find<std::vector<int>>(data, "numbers");
    const auto timepoint = toml::find<std::chrono::system_clock::time_point>(data, "time");
    std::cout <<"the anwser : "<<answer<< std::endl;
    std::cout <<"the pi : "<<pi<< std::endl;
    std::cout <<"the numbders : ";
    for(unsigned i = 0; i < numbers.size(); i++){
        std::cout <<numbers.at(i)<<((i == numbers.size() - 1)?"\n":",");
    }

    std::cout << "the time : " <<&timepoint <<std::endl;
    /*
    answer  = 42
    pi      = 3.14
    numbers = [1,2,3]
    time    = 1979-05-27T07:32:00Z
    */

    return 0;
}

#!/usr/bin/env bash
apt-get install libgtk2.0-dev xterm libcanberra-gtk-module
git clone --recursive ***********************:adas/opencv-contrib.git /tmp/opencv-contrib  \
    && git clone --recursive ***********************:adas/opencv.git /tmp/opencv               \
    && cd /tmp/opencv-contrib && git checkout tags/4.5.4 && cd                                 \
    && cd /tmp/opencv && git checkout tags/4.5.4 && git apply /tmp/opencv.patch && cd          \
    && cmake -G "Unix Makefiles" -DCMAKE_BUILD_TYPE=Release                                    \
             -DBUILD_opencv_sfm=OFF                                                            \
             -DOPENCV_EXTRA_MODULES_PATH=/tmp/opencv-contrib/modules                           \
             -DBUILD_opencv_dnn_objdetect=OFF                                                  \
             -DBUILD_opencv_dnn_superres=OFF                                                   \
             -DBUILD_opencv_dnns_easily_fooled=OFF                                             \
             -DBUILD_opencv_wechat_qrcode=OFF                                                  \
             -DBUILD_opencv_xfeatures2d=OFF                                                    \
             -DBUILD_opencv_face=OFF                                                           \
             -H/tmp/opencv/ -B/tmp/opencv/build                                                \
    && cmake --build /tmp/opencv/build  --target install  -- -j8                               \
    && cd /tmp/opencv/build/python_loader                                                      \
    && python setup.py install                                                                 \
    && cd                                                                                      \
    && rm -rf /tmp/opencv-contrib && rm -rf /tmp/opencv


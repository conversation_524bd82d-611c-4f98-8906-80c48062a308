/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/cyber.h"
#include "examples/proto/examples.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/zlgcandbc_io.pb.h"
#include "thirdparty/recommend_protocols/perception/proto/parking_slot.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/camera.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/imu.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/route.pb.h"
#include "thirdparty/recommend_protocols/common/proto/basic.pb.h"
#include "thirdparty/recommend_protocols/control/proto/apa_control.pb.h"
#include "base/util/config_parser.hpp"
#include "thirdparty/recommend_protocols/location/proto/map.pb.h"
#include "thirdparty/recommend_protocols/location/proto/grid_2d.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"


using rainbowdash::drivers::WheelSpeed;
void MessageCallback(
    const std::shared_ptr<WheelSpeed>& msg) {
        std::cout << msg->header().timestamp()<<std::endl;
    
    AINFO << msg->DebugString();
    AINFO << msg->header().timestamp();
}

int main(int argc, char *argv[]) {
  // init cyber framework
  apollo::cyber::Init(argv[0]);
  // create listener node
  std::cout << "listener init success!\n";
  auto listener_node = apollo::cyber::CreateNode("listener_wheelspeed");
  
  auto listener = listener_node->CreateReader<WheelSpeed>(
      "channel/wheelspeed", MessageCallback);
      std::cout << "listener node create success!!!!\n";
  apollo::cyber::WaitForShutdown();
  return 0;
}

/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "examples/proto/examples.pb.h"

#include "cyber/cyber.h"
#include "cyber/time/rate.h"
#include "cyber/time/time.h"

#include <chrono>
#include <random>
#include <sys/time.h>
#include <thread>
#include <unistd.h>

using apollo::cyber::Rate;
using apollo::cyber::Time;
using apollo::cyber::examples::proto::Chatter;

static double get_timestamp(struct timeval *pt) {
  double N = 1000.0;
  struct timeval te;
  if (pt == nullptr)
    gettimeofday(&te, NULL); // get current time
  else
    te = *pt;
  double milliseconds =
      te.tv_sec * N + te.tv_usec / N; // calculate milliseconds
  return milliseconds / 1000;
}


int xprocess() {
    // 控制循环迭代次数来调整CPU负载
    int iterations = 1000000;

    // 计算密集型循环
    double result = 0.0;
    for (int i = 0; i < iterations; ++i) {
        for (int j = 0; j < 1000; ++j) {
            result += std::sqrt(i * j);
        }
    }
    return result;
}

int yprocess(){
    // 控制循环迭代次数来调整CPU负载
    int iterations = 1000000;

    // 计算密集型循环
    double result = 0.0;
    for (int i = 0; i < iterations; ++i) {
        for (int j = 0; j < 1000; ++j) {
            result += std::sqrt(i * j);
        }
    }
    return result;

}

int main(int argc, char *argv[]) {
  std::cout << "talker start!\n";
  for(int i = 0; i < 10; i++){
      std::cout <<i<< ", xprocess :" <<xprocess() << std::endl;
  }
  apollo::cyber::Init(argv[0]);
  std::cout << "talker init success!\n";
  auto talker_node = apollo::cyber::CreateNode("talker");
  std::cout << "node create success!\n";
  // create talker
  auto talker = talker_node->CreateWriter<Chatter>("channel/chatter");
  std::cout << "talk node create success!\n";
  uint64_t seq = 0;
  std::random_device dev;
  std::mt19937 rng(dev());
  std::uniform_int_distribution<std::mt19937::result_type> dist6(
      1, 100000000); // distribution in range [1, 6]

  auto start = Time::Now();
  auto writer_callback = [&]() {
    auto end = Time::Now();

    auto elapsed_seconds = end - start;

    // std::cout << "[XXXXXXX] elapsed time: " << elapsed_seconds<< std::endl;
    std::cout << "start talker, No. " << seq << std::endl;

    auto msg = std::make_shared<Chatter>();
    msg->set_timestamp(Time::Now().ToSecond());
    msg->set_lidar_timestamp(Time::Now().ToNanosecond());
    msg->set_sys_timestamp(get_timestamp(nullptr));
    msg->set_seq(seq);
    int result = yprocess();
    msg->set_content("Hello, apollo! random : " + std::to_string(dist6(rng)) + ", result : " +std::to_string(result));
    talker->Write(msg);
    // std::cout  << "talker finish sent a message! No. " << seq<<"\n";
    seq++;
  };
  auto talk_timer =
      std::make_shared<apollo::cyber::Timer>(10, writer_callback, false);
  talk_timer->Start();
  std::cout << "finally talker finish sent a message! No. " << seq << "\n";
  apollo::cyber::WaitForShutdown();
  return 0;
}

ADAS_BUILD_DOCKER_URL='harbor-local.i-tetris.com/adas'

# amd
ADAS_GPU_UBUNTU_18_VERSION="nvidia/cuda:11.1.1-devel-ubuntu18.04"
ADAS_CPU_UBUNTU_18_VERSION="ubuntu:18.04"

ADAS_GPU_UBUNTU_18_BUILD_DOCKER_NAME="cuda-ubuntu18"
ADAS_CPU_UBUNTU_18_BUILD_DOCKER_NAME="ubuntu18"
ADAS_BASE_BUILD_DOCKER_TAG='b.1.4'

ADAS_GPU_UBUNTU_18_THIRD_BUILD_DOCKER_NAME="adas-gpu"
ADAS_CPU_UBUNTU_18_THIRD_BUILD_DOCKER_NAME="adas-cpu"
ADAS_THIRD_BUILD_DOCKER_TAG='t.2.2'
CYBER_DOCKER_TAG='cyber.0.4'
ACC_ADAS_THIRD_BUILD_DOCKER_TAG='carla.0.1'
ACC_CYBER_DOCKER_TAG='bst_cyber.0.1'


ADAS_CORE_CPU_IMAGE="adas-cpu-release"
ADAS_CORE_GPU_IMAGE="adas-gpu-release"
ACC_ADAS_CORE_CPU_IMAGE="carla-adas-cpu-release"
ACC_ADAS_CORE_GPU_IMAGE="carla-adas-gpu-release"
ADAS_ROS_IMAGE="adas-ros-cyber"
DOCKER_VERSION='a2e1d70'
ACC_DOCKER_VERSION='carla-bst.0.6.9'



# arm

ADAS_ARM_GPU_UBUNTU_18_VERSION="harbor-local.i-tetris.com/adas/adas-gpu-aarch64:18.04"
ADAS_ARM_GPU_UBUNTU_18_VERSION="nvidia/cuda-arm64:11.4.0-runtime-ubuntu20.04"
ADAS_ARM_GPU_UBUNTU_18_VERSION="nvidia/cuda-arm64:11.4.0-runtime-ubuntu20.04"
ADAS_ARM_GPU_UBUNTU_18_VERSION="nvcr.io/nvidia/l4t-base:r32.4.4"
ADAS_ARM_GPU_UBUNTU_18_VERSION="nvcr.io/nvidia/l4t-cuda:11.4.14-runtime"
ADAS_ARM_GPU_UBUNTU_18_VERSION="nvcr.io/nvidia/l4t-ml:r32.6.1-py3"
ADAS_ARM_GPU_UBUNTU_18_VERSION="harbor-local.i-tetris.com/adas/nvidia/l4t-ml:r34.1.0-py3"
ADAS_ARM_GPU_UBUNTU_18_VERSION="nvcr.io/nvidia/l4t-ml:r34.1.0-py3"
ADAS_ARM_GPU_UBUNTU_18_BUILD_DOCKER_NAME="adas-gpu-aarch64"
ADAS_ARM_BASE_BUILD_DOCKER_TAG='a.0.8'

ADAS_ARM_GPU_UBUNTU_18_THIRD_BUILD_DOCKER_NAME="adas-gpu-aarch64"
ADAS_ARM_THIRD_BUILD_DOCKER_TAG='a.0.9'

ADAS_CORE_ARM_GPU_NAME="adas-gpu-aarch64"
ARM_DOCKER_VERSION='74c1be7'




# ekf

ADAS_EKF_SLAM_BUILD_DOCKER_NAME="ros-ekf-slam"
ADAS_EKF_SLAM_BUILD_DOCKER_TAG="1.0"

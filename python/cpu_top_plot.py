import re
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from itertools import cycle
import argparse
import sys
import os

log_file = "./20250709173948_top.log"  # 替换为你的文件
process_filter = "ais_acc_server"

# 设置参数解析器
parser = argparse.ArgumentParser(description="Process APS log file and filter by time range.")
parser.add_argument("logfile", type=str, help="Path to the APS log file")

args = parser.parse_args()

# 读取文件
try:
    with open(args.logfile, "r") as f:
        log_file = args.logfile
except FileNotFoundError:
    print(f"[Error] 无法打开文件: {args.logfile}")
    sys.exit(1)



# —— 1. 解析日志，生成 DataFrame —— #
timestamp_re = re.compile(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$")
thread_line_re = re.compile(
    r"^\s*\d+\s+\d+\s+\d+\s+\w+\s+\d+:\d+:\d+\s+([\d.]+)%\s+(\S+)\s+(.+)$"
)

data = []
current_time = None

with open(log_file) as f:
    for line in f:
        line = line.strip()
        if timestamp_re.match(line):
            current_time = datetime.strptime(line, "%Y-%m-%d %H:%M:%S")
        elif current_time:
            m = thread_line_re.match(line)
            if m:
                cpu = float(m.group(1))
                cmd, thr = m.group(2), m.group(3).strip()
                # —— 只统计 ais_acc_server 的线程 —— #
                if cmd != process_filter:
                    continue
                if cpu > 0.5:
                    data.append({"time": current_time,
                                 "thread": f"{cmd}:{thr}", 
                                 "cpu": cpu})

df = pd.DataFrame(data)
pivot = df.pivot_table(index="time", columns="thread", values="cpu", aggfunc="mean")

# —— 2. 选取前 N 条线程 —— #
# 计算每条线程的全局平均 CPU，然后取前 8 个
avg_cpu = pivot.mean().sort_values(ascending=False)
top_threads = avg_cpu.head(8).index.to_list()

# —— 3. 画图 —— #

plt.figure(figsize=(12, 6))
for thread in top_threads:
    plt.plot(pivot.index, pivot[thread], 
             label=thread, marker='o')

plt.title("Top 8 Threads by Average CPU Usage (>0.5%)")
plt.xlabel("Time")
plt.ylabel("CPU Usage (%)")
plt.xticks(rotation=45)
plt.grid(True)
plt.legend(loc='center left', bbox_to_anchor=(1, 0.5))
plt.tight_layout()
plt.show()


set(APA_CONTROL "modules/control/cpp")
if(compile_single)
    add_library(ais_apa_control SHARED ${APA_CONTROL}/control_server.cc)
    target_include_directories(ais_apa_control PRIVATE ${BOOST_INCLUDE_DIRS})
    target_compile_definitions(ais_apa_control PRIVATE APA_MODULE_NAME="${control_module_name}")
else()
    add_executable(apa_control_server ${APA_CONTROL}/control_server.cc ${PROTO_CC_FILES})
    target_include_directories(apa_control_server PRIVATE ${BOOST_INCLUDE_DIRS})
    target_compile_definitions(apa_control_server PRIVATE APA_MODULE_NAME="${control_module_name}")
endif()

if(CMAKE_SYSTEM_NAME MATCHES QNX)
    if(compile_single)
        target_link_libraries(ais_apa_control -Wl,--whole-archive control_maker -Wl,--no-whole-archive
            Boost::program_options
            ${LIBCYBER_LIBRARY}
            protobuf_
            ${Boost_FILESYSTEM_LIBRARY}
            ${Boost_SYSTEM_LIBRARY}
            ${LIBUUID_LIBRARY}
            glog::glog
            atomic
            fastrtps
            gflags
            fastcdr
            )
        #install(TARGETS apa_control_server DESTINATION control)
        target_compile_options(ais_apa_control PRIVATE -std=gnu++1z)
        if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
            target_compile_options(ais_apa_control PRIVATE -finstrument-functions)
            target_link_libraries(ais_apa_control profilingS)
        endif()
        add_custom_command(
            TARGET ais_apa_control
            POST_BUILD
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
            COMMAND mv ${PROJECT_BINARY_DIR}/libais_apa_control.so ${PROJECT_BINARY_DIR}/output/ais_lib
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/apa_control_server.json ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config -o ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/${PRJ_NAME} -o ${PROJECT_BINARY_DIR}/output/conf
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py ${PROJECT_BINARY_DIR}/output/ais_lib/libais_apa_control.so -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config -o ${PROJECT_BINARY_DIR}/output/conf
            )
    else()
        target_link_libraries(apa_control_server -Wl,--whole-archive control_maker -Wl,--no-whole-archive
            Boost::program_options
            ${LIBCYBER_LIBRARY}
            ${PROTOBUF_LIBRARY}
            ${Boost_FILESYSTEM_LIBRARY}
            ${Boost_SYSTEM_LIBRARY}
            ${LIBUUID_LIBRARY}
            glog::glog
            atomic
            fastrtps
            gflags
            fastcdr
            )
        #install(TARGETS apa_control_server DESTINATION control)
        target_compile_options(apa_control_server PRIVATE -std=gnu++1z)
        if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
            target_compile_options(apa_control_server PRIVATE -finstrument-functions)
            target_link_libraries(apa_control_server profilingS)
        endif()
        add_custom_command(
            TARGET apa_control_server
            POST_BUILD
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py ${PROJECT_BINARY_DIR}/apa_control_server -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mv ${PROJECT_BINARY_DIR}/apa_control_server ${PROJECT_BINARY_DIR}/output/bin
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/apa_control_server.json ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config -o ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/${PRJ_NAME} -o ${PROJECT_BINARY_DIR}/output/conf
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/output/conf
            # control to output
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control/config
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control/config/json
            #COMMAND mv apa_control_server ${PROJECT_BINARY_DIR}/apa_control
            #COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/apa_control/config
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/apa_control/config/json
            )
    endif()
else()
    if(compile_single)
        target_link_libraries(ais_apa_control -Wl,--whole-archive control_maker -Wl,--no-whole-archive cyber Boost::program_options protobuf_ atomic uuid fastrtps fastcdr pthread m z rt gflags glog ${BOOST_LIBRARIES})
        target_compile_options(ais_apa_control PRIVATE -std=gnu++1z)
        add_custom_command(
            TARGET ais_apa_control
            POST_BUILD
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
            COMMAND mv ${PROJECT_BINARY_DIR}/libais_apa_control.so ${PROJECT_BINARY_DIR}/output/ais_lib
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/apa_control_server.json ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config -o ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/${PRJ_NAME} -o ${PROJECT_BINARY_DIR}/output/conf
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/output/conf
            #COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/apa_control_server -o ${PROJECT_BINARY_DIR}/output/lib
            # control to output
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control/config
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control/config/json
            #COMMAND mv apa_control_server ${PROJECT_BINARY_DIR}/apa_control
            #COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/apa_control/config
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/apa_control/config/json
            )
    else()
        target_link_libraries(apa_control_server -Wl,--whole-archive control_maker -Wl,--no-whole-archive cyber Boost::program_options protobuf protobuf-lite atomic uuid fastrtps fastcdr pthread m z rt gflags glog ${BOOST_LIBRARIES})
        target_compile_options(apa_control_server PRIVATE -std=gnu++1z)
        add_custom_command(
            TARGET apa_control_server
            POST_BUILD
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
            COMMAND mv ${PROJECT_BINARY_DIR}/apa_control_server ${PROJECT_BINARY_DIR}/output/bin
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/apa_control_server.json ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config -o ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/${PRJ_NAME} -o ${PROJECT_BINARY_DIR}/output/conf
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/output/conf
            #COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/apa_control_server -o ${PROJECT_BINARY_DIR}/output/lib
            # control to output
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control/config
            #COMMAND mkdir -p ${PROJECT_BINARY_DIR}/apa_control/config/json
            #COMMAND mv apa_control_server ${PROJECT_BINARY_DIR}/apa_control
            #COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/conf/apa_control.ini ${PROJECT_BINARY_DIR}/apa_control/config
            #COMMAND cp ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/control/config/*.json  ${PROJECT_BINARY_DIR}/apa_control/config/json
            )
    endif()
endif()

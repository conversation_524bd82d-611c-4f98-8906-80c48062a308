#!/usr/bin/env python

#
# Copyright (c) 2019 Intel Corporation
#
# This work is licensed under the terms of the MIT license.
# For a copy, see <https://opensource.org/licenses/MIT>.
#
"""
Control Carla ego vehicle by using AckermannDrive messages
"""

import sys
import os
import time
import datetime
import logging
import numpy
from simple_pid import PID  # pylint: disable=import-error,wrong-import-order

import carla_control_physics as phys

#from modules.common.proto.ackermann_drive_pb2 import Ackermann<PERSON>riveCommand
from modules.control.proto.carla_ackermann_control_pb2 import AckermannDriveCommand
from modules.control.proto.carla_msgs_pb2 import CarlaEgoVehicleStatus
from modules.control.proto.carla_msgs_pb2 import CarlaEgoVehicleControl
from modules.control.proto.carla_msgs_pb2 import CarlaEgoVehicleInfo
from modules.control.proto.carla_ackermann_msgs_pb2 import EgoVehicleControlInfo

# creat file path
curFileDir = os.path.dirname(__file__) #获取当前类所在文件路径
fileDir = os.path.abspath(os.path.join(curFileDir,"../")) #相对路径转绝对路径
sys.path.append(curFileDir) #添加本文件夹路径到系统路径

# config parameters
log_name = fileDir + "/my_log.log"
log_level = logging.DEBUG
log_format = " [%(asctime)s - %(filename)s(%(lineno)s) -  %(levelname)s] - %(message)s"

# config console
console_handler = logging.StreamHandler(sys.stderr)
console_handler.setLevel(log_level)
console_handler.setFormatter(logging.Formatter(log_format))
# config log file
file_handler = logging.FileHandler(log_name)
file_handler.setLevel(log_level)
file_handler.setFormatter(logging.Formatter(log_format))
# create log object
log = logging.getLogger("mylogger")
log.setLevel(log_level)
log.addHandler(console_handler)
log.addHandler(file_handler)

class CarlaAckermannControl():
    """
    Convert ackermann_drive messages to carla VehicleCommand with a PID controller
    """

    def __init__(self, params):
        """
        Constructor

        """
        # PID controller
        # the controller has to run with the simulation time, not with real-time
        #
        # To prevent "float division by zero" within PID controller initialize it with
        # a previous point in time (the error happens because the time doesn't
        # change between initialization and first call, therefore dt is 0)
        sys.modules['simple_pid.PID']._current_time = (  # pylint: disable=protected-access
            lambda: self.get_time() - 0.1)

        # we might want to use a PID controller to reach the final target speed
        self.params = params
        self.speed_controller = PID(Kp=self.get_param("speed_Kp", alternative_value=0.05),
                                    Ki=self.get_param("speed_Ki", alternative_value=0.),
                                    Kd=self.get_param("speed_Kd", alternative_value=0.5),
                                    sample_time=0.02,
                                    output_limits=(-1., 1.)
                                    )
        self.accel_controller = PID(Kp=self.get_param("accel_Kp", alternative_value=0.05),
                                    Ki=self.get_param("accel_Ki", alternative_value=0),
                                    Kd=self.get_param("accel_Kd", alternative_value=0.0),
                                    sample_time=0.02,
                                    output_limits=(-1.0, 1.0)
                                    )

        # use the correct time for further calculations
        sys.modules['simple_pid.PID']._current_time = (  # pylint: disable=protected-access
            lambda: self.get_time())

        self.control_loop_rate = self.get_param("control_loop_rate", 0.05)
        self.last_ackermann_msg_received_sec = self.get_time()
        self.vehicle_status = CarlaEgoVehicleStatus()
        self.vehicle_info = CarlaEgoVehicleInfo()
        self.role_name = self.get_param('role_name', 'ego_vehicle')
        # control info
        self.info = EgoVehicleControlInfo()

        # set initial maximum values
        self.vehicle_info_update(self.vehicle_info, False)

        # target values
        self.info.target.steering_angle = 0.
        self.info.target.speed = 0.
        self.info.target.speed_abs = 0.
        self.info.target.accel = 0.
        self.info.target.jerk = 0.

        # current values
        self.info.current.time_sec = self.get_time()
        self.info.current.speed = 0.
        self.info.current.speed_abs = 0.
        self.info.current.accel = 0.

        # control values
        self.info.status.status = 'n/a'
        self.info.status.speed_control_activation_count = 0
        self.info.status.speed_control_accel_delta = 0.
        self.info.status.speed_control_accel_target = 0.
        self.info.status.accel_control_pedal_delta = 0.
        self.info.status.accel_control_pedal_target = 0.
        self.info.status.brake_upper_border = 0.
        self.info.status.throttle_lower_border = 0.

        # control output
        self.info.output.throttle = 0.
        self.info.output.brake = 1.0
        self.info.output.steer = 0.
        self.info.output.reverse = False
        self.info.output.hand_brake = True

        # c arla test
        self.flag = 0

    def loginfo(self, log):
        print(log)

    def get_time(self):
        return time.time()

    def get_param(self, name, alternative_value=None):
        if name in self.params:
            return self.params[name]
        else:
            return alternative_value

    def vehicle_status_update(self, vehicle_status):
        """
        Stores the ackermann drive message for the next controller calculation

        :param ros_ackermann_drive: the current ackermann control input
        :type ros_ackermann_drive: ackermann_msgs.AckermannDrive
        :return:
        """

        # set target values
        self.vehicle_status = vehicle_status

    def vehicle_info_update(self, vehicle_info, reverse):
        """
        Stores the ackermann drive message for the next controller calculation

        :param ros_ackermann_drive: the current ackermann control input
        :type ros_ackermann_drive: ackermann_msgs.AckermannDrive
        :return:
        """
        # set target values
        self.vehicle_info = vehicle_info

        # calculate restrictions
        self.info.restrictions.max_steering_angle = phys.get_vehicle_max_steering_angle(
            self.vehicle_info)
        self.info.restrictions.max_speed = phys.get_vehicle_max_speed(
            self.vehicle_info)
        self.info.restrictions.max_accel = phys.get_vehicle_max_acceleration(
            self.vehicle_info)
        self.info.restrictions.max_decel = phys.get_vehicle_max_deceleration(
            self.vehicle_info)
        self.info.restrictions.min_accel = self.get_param('min_accel', 1.0)
        # clipping the pedal in both directions to the same range using the usual lower
        # border: the max_accel to ensure the the pedal target is in symmetry to zero
        self.info.restrictions.max_pedal = min(
            self.info.restrictions.max_accel, self.info.restrictions.max_decel)
        self.info.output.reverse = reverse

    def ackermann_command_update(self, ros_ackermann_drive):
        """
        Stores the ackermann drive message for the next controller calculation

        :param ros_ackermann_drive: the current ackermann control input
        :type ros_ackermann_drive: ackermann_msgs.AckermannDrive
        :return:
        """
        self.last_ackermann_msg_received_sec = self.get_time()
        # set target values
        self.set_target_steering_angle(ros_ackermann_drive.steering_angle)
        self.set_target_speed(ros_ackermann_drive.speed)
        self.set_target_accel(ros_ackermann_drive.acceleration)
        self.set_target_jerk(ros_ackermann_drive.jerk)

    def set_target_steering_angle(self, target_steering_angle):
        """
        set target sterring angle
        """
        self.info.target.steering_angle = target_steering_angle
        if abs(self.info.target.steering_angle) > self.info.restrictions.max_steering_angle:
            self.logerr("Max steering angle reached, clipping value")
            self.info.target.steering_angle = numpy.clip(
                self.info.target.steering_angle,
                -self.info.restrictions.max_steering_angle,
                self.info.restrictions.max_steering_angle)

    def set_target_speed(self, target_speed):
        """
        set target speed
        """
        if abs(target_speed) > self.info.restrictions.max_speed:
            self.logerr("Max speed reached, clipping value")
            self.info.target.speed = numpy.clip(
                target_speed, -self.info.restrictions.max_speed, self.info.restrictions.max_speed)
        else:
            self.info.target.speed = target_speed
        self.info.target.speed_abs = abs(self.info.target.speed)

    def set_target_accel(self, target_accel):
        """
        set target accel
        """
        epsilon = 0.00001
        # if speed is set to zero, then use max decel value
        if self.info.target.speed_abs < epsilon:
            self.info.target.accel = -self.info.restrictions.max_decel
        else:
            self.info.target.accel = numpy.clip(
                target_accel, -self.info.restrictions.max_decel, self.info.restrictions.max_accel)
        if self.flag == 0:
            self.info.target.accel = numpy.clip(
                target_accel, -self.info.restrictions.max_decel, self.info.restrictions.max_accel)

    def set_target_jerk(self, target_jerk):
        """
        set target accel
        """
        self.info.target.jerk = target_jerk

    def vehicle_control_cycle(self):
        """
        Perform a vehicle control cycle and sends out CarlaEgoVehicleControl message
        """
        # perform actual control
        self.control_steering()
        self.control_stop_and_reverse()
        self.run_speed_control_loop()
        self.run_accel_control_loop()
        
         # Todo:
        log.debug("run_accel_control_loop: {}".format( self.info.status.accel_control_pedal_target))
        
        self.update_drive_vehicle_control_command()
        
        #Todo:
        log.debug(" update_drive_vehicle_control_command~~~~~~ throttle: {}, brake: {}, steer: {}, reverse: {}".format( 
                             self.info.output.throttle,  self.info.output.brake, self.info.output.steer, self.info.output.reverse))
        
        return self.info.output

    def control_steering(self):
        """
        Basic steering control
        """
        self.info.output.steer = self.info.target.steering_angle / \
                                 self.info.restrictions.max_steering_angle

    def control_stop_and_reverse(self):
        """
        Handle stop and switching to reverse gear
        """
        # from this velocity on it is allowed to switch to reverse gear
        standing_still_epsilon = 0.1
        # from this velocity on hand brake is turned on
        full_stop_epsilon = 0.00001

        # auto-control of hand-brake and reverse gear
        self.info.output.hand_brake = False
        if self.info.current.speed_abs < standing_still_epsilon:
            # standing still, change of driving direction allowed
            self.info.status.status = "standing"
            """
            if self.info.target.speed < 0:
                if not self.info.output.reverse:
                    self.loginfo(
                        "VehicleControl: Change of driving direction to reverse")
                    self.info.output.reverse = True
            elif self.info.target.speed > 0:
                if self.info.output.reverse:
                    self.loginfo(
                        "VehicleControl: Change of driving direction to forward")
                    self.info.output.reverse = False
            """
            if self.info.target.speed_abs < full_stop_epsilon:
                self.info.status.status = "full stop"
                self.info.status.speed_control_accel_target = 0.
                self.info.status.accel_control_pedal_target = 0.
                self.set_target_speed(0.)
                self.info.current.speed = 0.
                self.info.current.speed_abs = 0.
                self.info.current.accel = 0.
                self.info.output.hand_brake = True
                self.info.output.brake = 1.0
                self.info.output.throttle = 0.0

        elif numpy.sign(self.info.current.speed) * numpy.sign(self.info.target.speed) == -1:
            # requrest for change of driving direction
            # first we have to come to full stop before changing driving
            # direction
            self.loginfo("VehicleControl: Request change of driving direction."
                         " v_current={} v_desired={}"
                         " Set desired speed to 0".format(self.info.current.speed,
                                                          self.info.target.speed))
            self.set_target_speed(0.)

    def run_speed_control_loop(self):
        """
        Run the PID control loop for the speed

        The speed control is only activated if desired acceleration is moderate
        otherwhise we try to follow the desired acceleration values

        Reasoning behind:

        An autonomous vehicle calculates a trajectory including position and velocities.
        The ackermann drive is derived directly from that trajectory.
        The acceleration and jerk values provided by the ackermann drive command
        reflect already the speed profile of the trajectory.
        It makes no sense to try to mimick this a-priori knowledge by the speed PID
        controller.
        =>
        The speed controller is mainly responsible to keep the speed.
        On expected speed changes, the speed control loop is disabled
        """
        epsilon = 0.00001
        target_accel_abs = abs(self.info.target.accel)
        if target_accel_abs < self.info.restrictions.min_accel:
            if self.info.status.speed_control_activation_count < 5:
                self.info.status.speed_control_activation_count += 1
        else:
            if self.info.status.speed_control_activation_count > 0:
                self.info.status.speed_control_activation_count -= 1
        # set the auto_mode of the controller accordingly
        self.speed_controller.auto_mode = self.info.status.speed_control_activation_count >= 5

        if self.speed_controller.auto_mode:
            self.speed_controller.setpoint = self.info.target.speed_abs
            self.info.status.speed_control_accel_delta = float(self.speed_controller(
                self.info.current.speed_abs))
            # clipping borders
            clipping_lower_border = -target_accel_abs
            clipping_upper_border = target_accel_abs
            # per definition of ackermann drive: if zero, then use max value
            if target_accel_abs < epsilon:
                clipping_lower_border = -self.info.restrictions.max_decel
                clipping_upper_border = self.info.restrictions.max_accel
            self.info.status.speed_control_accel_target = numpy.clip(
                self.info.status.speed_control_accel_target +
                self.info.status.speed_control_accel_delta,
                clipping_lower_border, clipping_upper_border)
        else:
            self.info.status.speed_control_accel_delta = 0.
            self.info.status.speed_control_accel_target = self.info.target.accel
            
        self.info.status.speed_control_accel_delta = 0.
        self.info.status.speed_control_accel_target = self.info.target.accel

    def run_accel_control_loop(self):
        """
        Run the PID control loop for the acceleration
        """
        # setpoint of the acceleration controller is the output of the speed controller

        # carla test
        # 由于设置 self.info.restrictions.min_accel = 0.001，因此 run_speed_control_loop 几乎可以忽略
        self.accel_controller.setpoint = self.info.status.speed_control_accel_target

        # carla test tune pid
        self.accel_controller.output_limits = (-1.0, 1.0)
        self.accel_controller.tunings = (0.05, 0.01, 0.1)

        self.info.status.accel_control_pedal_delta = float(self.accel_controller(self.info.current.accel))
        # self.info.status.accel_control_pedal_delta = float(self.accel_controller(self.info.current.accel))

        # @todo: we might want to scale by making use of the the abs-jerk value
        # If the jerk input is big, then the trajectory input expects already quick changes
        # in the acceleration; to respect this we put an additional proportional factor on top

       # Todo: 0221
        if self.vehicle_status.acceleration > 3:
            self.accel_controller.output_limits = (None, 0.1)
        else:
            if self.vehicle_status.acceleration < -3:
                self.accel_controller.output_limits = (-0.1, None)
            else:
                self.accel_controller.output_limits = (-0.1, 0.1)

        self.accel_controller.tunings = (0.05, 0.1, 0)

        if self.flag == 0:
            if (abs(self.vehicle_status.acceleration) < 0.01) | (abs(self.vehicle_status.velocity) < 0.1):
                self.info.status.accel_control_pedal_target = self.accel_controller.setpoint
            else:
                self.flag = 1

        if self.info.status.accel_control_pedal_target > 3.0:
            self.info.status.accel_control_pedal_target = numpy.clip(
                self.info.status.accel_control_pedal_target +
                self.info.status.accel_control_pedal_delta,
                0, self.info.restrictions.max_pedal)
        else:
            if self.info.status.accel_control_pedal_target < -3.0:
                self.info.status.accel_control_pedal_target = numpy.clip(
                    self.info.status.accel_control_pedal_target +
                    self.info.status.accel_control_pedal_delta,
                    -self.info.restrictions.max_pedal, 0)
            else:
                self.info.status.accel_control_pedal_target = numpy.clip(
                    self.info.status.accel_control_pedal_target +
                    self.info.status.accel_control_pedal_delta,
                    -self.info.restrictions.max_pedal, self.info.restrictions.max_pedal)

    def update_drive_vehicle_control_command(self):
        """
        Apply the current speed_control_target value to throttle/brake commands
        """

        # the driving impedance moves the 'zero' acceleration border
        # Interpretation: To reach a zero acceleration the throttle has to pushed
        # down for a certain amount
        self.info.status.throttle_lower_border = phys.get_vehicle_driving_impedance_acceleration(
            self.vehicle_info, self.vehicle_status, self.info.output.reverse)

        # the engine lay off acceleration defines the size of the coasting area
        # Interpretation: The engine already prforms braking on its own;
        #  therefore pushing the brake is not required for small decelerations
        self.info.status.brake_upper_border = self.info.status.throttle_lower_border + \
                                              phys.get_vehicle_lay_off_engine_acceleration(self.vehicle_info)

        if self.info.status.accel_control_pedal_target > self.info.status.throttle_lower_border:
            self.info.status.status = "accelerating"
            self.info.output.brake = 0.0
            # the value has to be normed to max_pedal
            # be aware: is not required to take throttle_lower_border into the scaling factor,
            # because that border is in reality a shift of the coordinate system
            # the global maximum acceleration can practically not be reached anymore because of
            # driving impedance
            self.info.output.throttle = (
                    (self.info.status.accel_control_pedal_target -
                     self.info.status.throttle_lower_border) /
                    abs(self.info.restrictions.max_pedal))
        elif self.info.status.accel_control_pedal_target > self.info.status.brake_upper_border:
            self.info.status.status = "coasting"
            # no control required
            self.info.output.brake = 0.0
            self.info.output.throttle = 0.0
        else:
            self.info.status.status = "braking"
            # braking required
            self.info.output.brake = (
                    (self.info.status.brake_upper_border -
                     self.info.status.accel_control_pedal_target) /
                    abs(self.info.restrictions.max_pedal))
            self.info.output.throttle = 0.0

        # finally clip the final control output (should actually never happen)
        self.info.output.brake = numpy.clip(
            self.info.output.brake, 0., 1.)
        self.info.output.throttle = numpy.clip(
            self.info.output.throttle, 0., 1.)

    # from ego vehicle
    def send_ego_vehicle_control_info_msg(self):
        """
        Function to send carla_ackermann_control.msg.EgoVehicleControlInfo message.

        :return:
        """
        return self.info

    def update_current_values(self):
        """
        Function to update vehicle control current values.

        we calculate the acceleration on ourselves, because we are interested only in
        the acceleration in respect to the driving direction
        In addition a small average filter is applied

        :return:
        """
        current_time_sec = self.get_time()
        delta_time = current_time_sec - self.info.current.time_sec
        
        current_speed = self.vehicle_status.velocity
        """
        if delta_time >0:
            delta_speed = current_speed - self.info.current.speed
            current_accel = delta_speed / delta_time
            # average filter
            self.info.current.accel = ( self.info.current.accel *4 + current_accel)/5
        """
        current_acceleration = self.vehicle_status.acceleration
        self.info.current.accel = current_acceleration
        self.info.current.time_sec = current_time_sec
        self.info.current.speed = current_speed
        self.info.current.speed_abs = abs(current_speed)

    def run(self, ackermann_drive_command, vehicle_status, vehicle_info, reverse):
        """

        Control loop

        :return:
        """
        
         # Todo : log
        log.debug("input:~~~~~tar_v: {}, tar_a: {}, tar_steer: {}, tar_reverse: {}".format(
          ackermann_drive_command.speed, ackermann_drive_command.acceleration, 
          ackermann_drive_command.steering_angle, reverse))
        log.debug("state~~~~~~cur_v: {}, cur_a: {} ".format(vehicle_status.velocity, vehicle_status.acceleration ))
        
        self.ackermann_command_update(ackermann_drive_command)
        self.vehicle_status_update(vehicle_status)
        self.vehicle_info_update(vehicle_info, reverse)

        self.update_current_values()
        control_command = self.vehicle_control_cycle()
        control_command_pb2 = CarlaEgoVehicleControl(throttle=control_command.throttle,
                                                     brake=control_command.brake,
                                                     steer=control_command.steer,
                                                     reverse=control_command.reverse)
        report_info = self.send_ego_vehicle_control_info_msg()
        report_info_pb2 = EgoVehicleControlInfo()
        
        log.debug('tar_v: {}, tar_a: {}, act_v: {}, act_a: {}, reverse: {}'.format(self.info.target.speed, self.info.target.accel, self.info.current.speed, self.info.current.accel,  self.info.output.reverse))

        return control_command_pb2, report_info_pb2


def main(args=None):
    """

    main function

    :return:
    """

    params = {}
    try:
        controller = CarlaAckermannControl(params)
        controller.run()
    except KeyboardInterrupt:
        pass


if __name__ == "__main__":
    main()

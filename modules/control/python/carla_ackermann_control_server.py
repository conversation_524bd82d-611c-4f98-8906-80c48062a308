from concurrent import futures
import imp
import sys
import logging
import math
import time
import numpy as np

sys.path.append("thirdparty")

#from modules.control.proto.carla_msgs_pb2 import FloatValue
#from modules.control.proto.control import CarlaControl
#from modules.control.proto.carla_a<PERSON><PERSON>_control import CarlaAckermannControlInputs
from modules.control.proto.carla_ackermann_control_pb2 import CarlaAckermannControlInputs
from modules.control.proto.carla_msgs_pb2 import CarlaEgoVehicleControl
#from modules.control.proto.carla_ackermann_control_pb2 import AckermannDriveCommand
#from modules.control.proto.carla_msgs import CarlaEgoVehicleControl

from carla_a<PERSON>mann_control_node import CarlaAckermannControl
from cyber.python.cyber_py3 import cyber

class CarlaAckermannControlServicer():
  def __init__(self):
    config = {}
    self.controler = CarlaAckermannControl(config)

  def Update(self, request):
    
    command, report = self.controler.run(request.command, request.status, request.info, request.reverse)
    return command

  #def Observer(self, value, context):
    #target_acc = self.controler.ObserverTargetAcc()
    #target_acc_pb2 = FloatValue(ret=target_acc)
    #return target_acc_pb2
  
  def Start(self):
    pass
    self.node_name = 'carlaAckermannControl'
    self.node = cyber.Node(self.node_name)
    self.node.create_service("carla/calaAckemannControl", CarlaAckermannControlInputs, CarlaEgoVehicleControl, self.Update)
    self.node.spin()


if __name__ == '__main__':
  cyber.init()
  server = CarlaAckermannControlServicer()

  server.Start()
  print("Init done.")
  cyber.waitforshutdown()
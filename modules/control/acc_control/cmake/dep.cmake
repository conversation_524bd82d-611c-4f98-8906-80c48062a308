set(ACC_CONTROL_DIR "modules/control/acc_control/src")
set(ACC_CONTROL_SRC
    ${ACC_CONTROL_DIR}/control_component.cpp
    ${ACC_CONTROL_DIR}/main.cpp
    modules/common/base/base64.cc
)
include_directories(
    thirdparty/apa_splinemethod_routing/acc_control
    modules/common/base
)
if(CMAKE_SYSTEM_NAME MATCHES QNX)
    if(compile_single)
        add_library(ais_acc_control_server  SHARED  ${ACC_CONTROL_SRC})
        target_include_directories(ais_acc_control_server PUBLIC ${BOOST_INCLUDE_DIRS})
    else()
        add_executable(acc_control_server   ${ACC_CONTROL_SRC})
        target_include_directories(acc_control_server PUBLIC ${BOOST_INCLUDE_DIRS})
    endif()
else()
    if(compile_single)
        add_library(ais_acc_control_server  SHARED  ${ACC_CONTROL_SRC}  ${PROTO_CC_FILES})
        target_include_directories(ais_acc_control_server PUBLIC ${BOOST_INCLUDE_DIRS})
    else()
        add_executable(acc_control_server   ${ACC_CONTROL_SRC}  ${PROTO_CC_FILES})
        target_include_directories(acc_control_server PUBLIC ${BOOST_INCLUDE_DIRS})
    endif()
endif()



    

if(CMAKE_SYSTEM_NAME MATCHES QNX)
    if(compile_single)
        message("qnx single")
        target_link_libraries(ais_acc_control_server acc_control_lib 
        ${LIBCYBER_LIBRARY}
        protobuf_
        ${LIBUUID_LIBRARY}
        libzip::zip
        glog::glog
        Boost::program_options  atomic fastrtps fastcdr  m z  gflags 
        timer_common
        )
        target_compile_options(ais_acc_control_server PRIVATE -std=gnu++1z)
        if(${BUILD_8255})
            add_custom_command(
                TARGET ais_acc_control_server
                POST_BUILD
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx710_depends.py ${PROJECT_BINARY_DIR}/libais_acc_control_server.so -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib
                COMMAND mv ${PROJECT_BINARY_DIR}/libais_acc_control_server.so ${PROJECT_BINARY_DIR}/output/ais_lib
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/acc_control.ini ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/acc_control_server.json ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/python/my_test.py ${PROJECT_BINARY_DIR}/python
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_carla.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/lon_ctl_states.json ${PROJECT_BINARY_DIR}/output/conf
                )
        else()
            add_custom_command(
                TARGET ais_acc_control_server
                POST_BUILD
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py ${PROJECT_BINARY_DIR}/libais_acc_control_server.so -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib
                COMMAND mv ${PROJECT_BINARY_DIR}/libais_acc_control_server.so ${PROJECT_BINARY_DIR}/output/ais_lib
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/acc_control.ini ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/acc_control_server.json ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/python/my_test.py ${PROJECT_BINARY_DIR}/python
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_carla.pb.txt ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/lon_ctl_states.json ${PROJECT_BINARY_DIR}/output/conf
                )
        endif()
    else()
        message("qnx excutable")
    endif()
else()
    if(compile_single)
        target_link_libraries(ais_acc_control_server acc_control_lib 
            Boost::program_options cyber protobuf protobuf-lite atomic uuid fastrtps fastcdr pthread m z rt gflags	glog ${BOOST_LIBRARIES})
        target_compile_options(ais_acc_control_server PRIVATE -std=gnu++1z)
        add_custom_command(
            TARGET ais_acc_control_server
            POST_BUILD
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
            COMMAND mv ${PROJECT_BINARY_DIR}/libais_acc_control_server.so ${PROJECT_BINARY_DIR}/output/ais_lib
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/acc_control.ini ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/acc_control_server.json ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/python/my_test.py ${PROJECT_BINARY_DIR}/python
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_carla.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/output/ais_lib/libais_acc_control_server.so -o ${PROJECT_BINARY_DIR}/output/lib
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/lon_ctl_states.json ${PROJECT_BINARY_DIR}/output/conf
            )
    else()
        target_link_libraries(acc_control_server acc_control_lib 
        Boost::program_options cyber protobuf protobuf-lite atomic uuid fastrtps fastcdr pthread m z rt gflags	glog ${BOOST_LIBRARIES})
        target_compile_options(acc_control_server PRIVATE -std=gnu++1z)
        add_custom_command(
            TARGET acc_control_server
            POST_BUILD
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
            COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
            COMMAND mv acc_control_server ${PROJECT_BINARY_DIR}/output/bin
            COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/output/bin/acc_control_server -o ${PROJECT_BINARY_DIR}/output/lib
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/acc_control.ini ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/acc_control_server.json ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/vehicle_param_df.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/python/my_test.py ${PROJECT_BINARY_DIR}/python
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_carla.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/lon_ctl_states.json ${PROJECT_BINARY_DIR}/output/conf
            # COMMAND cp ${PROJECT_SOURCE_DIR}/modules/control/acc_control/conf/control_conf_carla_no_delay.pb.txt ${PROJECT_BINARY_DIR}/output/conf
            )
    endif()
endif()

# if(${BUILD_TEST})
# gtest
# find_package(GTest  REQUIRED)

# include_directories(${GTEST_INCLUDE_DIRS})
# link_libraries(${GTEST_BOTH_LIBRARIES})

# add_executable(acc_control_gtest
#                 ${PROTO_CC_FILES}
#                 # ${ACC_CONTROL_DIR}/control_component.cpp
#                 ${ACC_CONTROL_DIR}/acc_control_gtest.cpp
#                 # ${ACC_CONTROL_DIR}/control_component_gtest.cpp
# )

# target_include_directories(acc_control_gtest PUBLIC modules/acc_control/ )
# target_link_libraries(acc_control_gtest acc_control_lib pnc_math cyber protobuf protobuf-lite atomic uuid fastrtps fastcdr pthread m z rt gflags	glog)

# target_compile_options(acc_control_gtest PRIVATE -std=gnu++1z)
# add_custom_command(
#     TARGET acc_control_gtest
#     POST_BUILD
#     COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
#     COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
#     COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
#     COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
#     COMMAND mv acc_control_gtest ${PROJECT_BINARY_DIR}/output/bin
#     )
# endif()

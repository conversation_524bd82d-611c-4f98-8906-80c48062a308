{"LON_CTL_STATES": [{"StateName": "Start", "Parent": "*", "Transition": [{"Event": "go_stopped", "Priority": 0, "Key": "->Stopped", "Target": "Stopped"}, {"Event": "go_stopping", "Priority": 0, "Key": "->Stopping", "Target": "Stopping"}, {"Event": "go_driveoff", "Priority": 0, "Key": "-><PERSON><PERSON>ff", "Target": "<PERSON><PERSON>ff"}, {"Event": "go_emergency", "Priority": 0, "Key": "->Emergency", "Target": "Emergency"}, {"Event": "go_driving_followdistance", "Priority": 0, "Key": "->DrivingFollowDistance", "Target": "DrivingFollowDistance"}, {"Event": "go_driving_followspeed", "Priority": 0, "Key": "->DrivingFollowSpeed", "Target": "DrivingFollowSpeed"}]}, {"StateName": "Stopped", "Parent": "*", "Transition": [{"Event": "go_driveoff", "Priority": 0, "Key": "->Driveoff", "Target": "Driveoff"}]}, {"StateName": "Stopping", "Parent": "*", "Transition": [{"Event": "go_stopped", "Priority": 100, "Key": "->Stopped", "Target": "Stopped"}, {"Event": "go_emergency", "Priority": 0, "Key": "->Emergency", "Target": "Emergency"}, {"Event": "go_driving_followdistance", "Priority": 0, "Key": "->DrivingFollowDistance", "Target": "DrivingFollowDistance"}, {"Event": "go_driving_followspeed", "Priority": 0, "Key": "->DrivingFollowSpeed", "Target": "DrivingFollowSpeed"}]}, {"StateName": "<PERSON><PERSON>ff", "Parent": "*", "Transition": [{"Event": "go_emergency", "Priority": 0, "Key": "->Emergency", "Target": "Emergency"}, {"Event": "go_driving_followdistance", "Priority": 0, "Key": "->DrivingFollowDistance", "Target": "DrivingFollowDistance"}, {"Event": "go_driving_followspeed", "Priority": 0, "Key": "->DrivingFollowSpeed", "Target": "DrivingFollowSpeed"}]}, {"StateName": "Emergency", "Parent": "*", "Transition": [{"Event": "go_stopped", "Priority": 0, "Key": "->Stopped", "Target": "Stopped"}]}, {"StateName": "DrivingFollowDistance", "Parent": "*", "Transition": [{"Event": "go_stopping", "Priority": 0, "Key": "->Stopping", "Target": "Stopping"}, {"Event": "go_emergency", "Priority": 0, "Key": "->Emergency", "Target": "Emergency"}, {"Event": "go_driving_followspeed", "Priority": 0, "Key": "->DrivingFollowSpeed", "Target": "DrivingFollowSpeed"}]}, {"StateName": "DrivingFollowSpeed", "Parent": "Failed", "Transition": [{"Event": "go_stopping", "Priority": 0, "Key": "->Stopping", "Target": "Stopping"}, {"Event": "go_emergency", "Priority": 0, "Key": "->Emergency", "Target": "Emergency"}, {"Event": "go_driving_followdistance", "Priority": 0, "Key": "->DrivingFollowDistance", "Target": "DrivingFollowDistance"}]}]}
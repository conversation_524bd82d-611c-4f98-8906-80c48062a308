control_test_duration: -1
is_control_test_mode: false
max_localization_miss_num : 10
max_planning_miss_num: 20
max_chassis_miss_num:20

trajectory_period: 0.1
chassis_period: 0.02
localization_period: 0.02
control_period: 0.02

active_controllers: LON_CONTROLLER
active_controllers: LAT_LQRK2_CONTROLLER

max_acceleration_when_stopped: 0.03
minimum_speed_resolution: 0.2
soft_estop_brake: -1.0
publish_control_debug: true  
use_lat_control: true

use_debug_info_record: false
deque_size: 1000
save_rate: 50

lat_controller_conf {
  ts: 0.02
  preview_window: 10
  cf: 155494.663
  cr: 155494.663
  mass_fl: 520
  mass_fr: 520
  mass_rl: 520
  mass_rr: 520
  eps: 0.01
  matrix_q: 0.05
  matrix_q: 0.0
  matrix_q: 1.0
  matrix_q: 0.0
  reverse_matrix_q: 0.05
  reverse_matrix_q: 0.0
  reverse_matrix_q: 1.0
  reverse_matrix_q: 0.0
  cutoff_freq: 10
  mean_filter_window_size: 10
  max_iteration: 150
  max_lateral_acceleration: 5.0
  enable_reverse_leadlag_compensation: true
  enable_steer_mrac_control: false
  enable_look_ahead_back_control: true
  switch_speed: 10
  switch_speed_window: 2
  query_relative_time: 0.2
  lookahead_station: 1.4224
  lookback_station: 2.8448
  lookahead_station_high_speed: 1.4224
  lookback_station_high_speed: 2.8448
  minimum_speed_protection: 0.1
  lat_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.2
    }
    scheduler {
      speed: 20.0
      ratio: 0.1
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.4
    }
    scheduler {
      speed: 20.0
      ratio: 0.2
    }
    scheduler {
      speed: 25.0
      ratio: 0.1
    }
  }
  reverse_leadlag_conf {
    innerstate_saturation_level: 3000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }
}

lon_controller_conf {
  ts: 0.02
  brake_minimum_action: -0.05
  throttle_minimum_action: 10.0
  speed_controller_input_limit: 0.8
  station_error_limit: 6.0
  preview_window: 10.0
  standstill_acceleration: -1.5
  max_path_remain_when_stopped: 0.3

  state_update_params {
    drive_state_stop_dist: 0.5
    drive_state_offset_stop_dist: 1.0
    stopping_state_stop_dist: 0.5
    stopped_state_entry_vel: 0.03
    stopped_state_entry_acc: 0.1
    stopped_state_entry_duration_time: 0.1
    emergency_state_overshoot_stop_dist: 0.5
    driveoff_state_stop_dist: 1.5
    driving_state_entry_vel: 1.0
    emergency_state_traj_trans_dev: 3.0
    emergency_state_traj_rot_dev: 0.786  
    speed_error: 0.8
    station_error: 5.0
    use_keep_stoped_until_steer_convergence: false
    only_speed_follow: true
  }

  lon_state_params {
    driveoff_params {
      driveoff_min_acc: 0.4
      driveoff_max_jerk: 1.0
    }
    follow_distance_params {
      switch_speed: 3.0
      station_pid_conf {
        integrator_enable: false
        integrator_saturation_level: 0.3
        kp: 0.1
        ki: 0.0
        kd: 0.0
      }
      low_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 1.0
        ki: 0.25
        kd: 0.0
      }
      high_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 0.6
        ki: 0.15
        kd: 0.0
      }
    }
    follow_speed_params {
      switch_speed: 3.0
      low_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 1.0
        ki: 0.25
        kd: 0.0
      }
      high_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 0.6
        ki: 0.15
        kd: 0.0
      }
    }
    emergency_params {
      emergency_min_acc: -4.0
      emergency_min_jerk: -2.0
    }
    start_params {

    }
    stopped_params {
      stopped_acc: -1.0
    }
    stopping_params {
      min_fast_val: 0.5
      strong_stop_dist: -0.3
      weak_stop_dist: 0.1
      strong_stop_acc: -2.4
      weak_stop_acc: -0.8
      strong_acc: -0.8
      weak_acc: -0.4
      weak_stop_time: 0.8
      min_running_vel: 0.01
      min_running_acc: 0.01
    }
  }

  pitch_angle_filter_conf {
    cutoff_freq: 5
  }
  station_error_filter_conf {
    cutoff_freq: 1
  }
  speed_error_filter_conf {
    cutoff_freq: 1
  }
  acc_filter_conf {
    cutoff_freq: 2
  }
  mean_filter_window_size: 30
  acc_mean_filter_window_size: 15
  slope_mean_filter_window_size: 30

  station_leadlag_conf {
    innerstate_saturation_level: 1000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }
  speed_leadlag_conf {
    innerstate_saturation_level: 1000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }

  use_soft_switch: false
  soft_switch_speed: 3.0
  soft_sw_dece_threshold: -0.1
  soft_sw_acc_threshold: 0.05

  use_longitudinal_params_limited: true
  longitudinal_params {
    acceleration{
      x: 5.0
      y: 4.0
    }
    acceleration{
      x: 20.0
      y: 2.0
    }

    acceleration_rate{
      x: 5.0
      y: 4.0
    }
    acceleration_rate{
      x: 20.0
      y: 2.0
    }

    deceleration{
      x: 5.0
      y: 5.0
    }
    deceleration{
      x: 20.0
      y: 3.5
    }

    deceleration_rate{
      x: 5.0
      y: 5.0
    }
    deceleration_rate{
      x: 20.0
      y: 2.5
    }
  }

  use_pid_output_acceleration_limited: false
  pid_output_acceleration_limit: 0.8
  pid_output_acceleration_factor: 0.0

  use_calibration_value_rate_limited: false
  calibration_value_rate: 15.0

  enable_leadlag_compensation: false
  enable_speed_station_preview: true
  use_acceleration_limit: true
  use_acceleration_max_limit_follow_speed: false
  enable_slope_offset: false
  input_use_mean_filter: true
  input_use_digital_filter: false
  output_use_mean_filter: false
  output_use_digital_filter: false
  use_vehicle_mass_estimation: false

  calibration_table {
    calibration {
      speed: 0.2
    }
  }

  calibration_params{
    a: 0.666
    b: -2.066
    c: 197.458
    offset: 10.0
  }

}
mpc_controller_conf {
  ts: 0.02
  cf: 155494.663
  cr: 155494.663
  mass_fl: 520
  mass_fr: 520
  mass_rl: 520
  mass_rr: 520
  eps: 0.01
  matrix_q: 3.0
  matrix_q: 0.0
  matrix_q: 35
  matrix_q: 0.0
  matrix_q: 50.0
  matrix_q: 10.0
  matrix_r: 3.25
  matrix_r: 1.0
  cutoff_freq: 10
  mean_filter_window_size: 10
  max_iteration: 150
  max_lateral_acceleration: 5.0
  standstill_acceleration: -0.3
  brake_minimum_action: 0.0
  throttle_minimum_action: 0.0
  enable_mpc_feedforward_compensation: false
  lat_err_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 1.0
    }
    scheduler {
      speed: 5.0
      ratio: 1.0
    }
    scheduler {
      speed: 10.0
      ratio: 0.4
    }
    scheduler {
      speed: 15.0
      ratio: 0.3
    }
    scheduler {
      speed: 20.0
      ratio: 0.2
    }
    scheduler {
      speed: 25.0
      ratio: 0.1
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 1.0
    }
    scheduler {
      speed: 5.0
      ratio: 1.0
    }
    scheduler {
      speed: 10.0
      ratio: 0.5
    }
    scheduler {
      speed: 15.0
      ratio: 0.4
    }
    scheduler {
      speed: 20.0
      ratio: 0.35
    }
    scheduler {
      speed: 25.0
      ratio: 0.35
    }
  }
  feedforwardterm_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 0.7
    }
    scheduler {
      speed: 5.0
      ratio: 0.05
    }
    scheduler {
      speed: 10.0
      ratio: 0.0
    }
    scheduler {
      speed: 15.0
      ratio: 0.0
    }
    scheduler {
      speed: 20.0
      ratio: 0.0
    }
    scheduler {
      speed: 25.0
      ratio: 0.0
    }
  }
  steer_weight_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 1.0
    }
    scheduler {
      speed: 5.0
      ratio: 1.0
    }
    scheduler {
      speed: 10.0
      ratio: 1.1
    }
    scheduler {
      speed: 15.0
      ratio: 1.1
    }
    scheduler {
      speed: 20.0
      ratio: 1.35
    }
    scheduler {
      speed: 25.0
      ratio: 1.55
    }
  }
  calibration_table {
    calibration {
      speed: 0.0
      acceleration: -1.43
      command: -35.0
    }
    calibration {
      speed: 0.0
      acceleration: -1.28
      command: -27.0
    }
    calibration {
      speed: 0.0
      acceleration: -1.17
      command: -25.0
    }
    calibration {
      speed: 0.0
      acceleration: -1.02
      command: -30.0
    }
    calibration {
      speed: 0.0
      acceleration: -0.48
      command: -27.5
    }
    calibration {
      speed: 0.0
      acceleration: 0.09
      command: -20.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.32
      command: -15.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.35
      command: -17.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.37
      command: 17.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.39
      command: 15.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.41
      command: -13.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.72
      command: 20.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.97
      command: 22.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.4
      command: 25.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.43
      command: 27.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.53
      command: 30.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.65
      command: 35.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.79
      command: 40.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.89
      command: 45.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.94
      command: 50.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.03
      command: 55.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.08
      command: 65.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.11
      command: 60.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.3
      command: 80.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.33
      command: 70.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.34
      command: 75.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.87
      command: -35.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.41
      command: -27.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.25
      command: -30.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.22
      command: -25.0
    }
    calibration {
      speed: 0.2
      acceleration: -0.87
      command: -33.0
    }
    calibration {
      speed: 0.2
      acceleration: -0.48
      command: -22.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.09
      command: -20.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.32
      command: -15.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.36
      command: -17.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.38
      command: 17.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.39
      command: 15.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.41
      command: -13.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.74
      command: 20.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.98
      command: 22.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.43
      command: 25.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.48
      command: 27.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.59
      command: 30.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.72
      command: 35.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.87
      command: 40.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.98
      command: 45.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.03
      command: 50.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.15
      command: 55.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.22
      command: 65.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.23
      command: 60.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.44
      command: 80.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.45
      command: 70.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.47
      command: 75.0
    }
    calibration {
      speed: 0.4
      acceleration: -6.89
      command: -35.0
    }
    calibration {
      speed: 0.4
      acceleration: -5.78
      command: -33.0
    }
    calibration {
      speed: 0.4
      acceleration: -4.19
      command: -30.0
    }
    calibration {
      speed: 0.4
      acceleration: -2.88
      command: -27.0
    }
    calibration {
      speed: 0.4
      acceleration: -1.77
      command: -25.0
    }
    calibration {
      speed: 0.4
      acceleration: -0.48
      command: -22.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.01
      command: -20.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.31
      command: -15.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.35
      command: 15.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.38
      command: -17.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.4
      command: -13.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.43
      command: 17.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.82
      command: 20.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.07
      command: 22.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.57
      command: 25.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.66
      command: 27.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.78
      command: 30.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.94
      command: 35.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.12
      command: 40.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.22
      command: 45.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.24
      command: 50.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.43
      command: 55.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.47
      command: 60.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.54
      command: 65.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.67
      command: 75.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.68
      command: 70.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.71
      command: 80.0
    }
    calibration {
      speed: 0.6
      acceleration: -8.63
      command: -35.0
    }
    calibration {
      speed: 0.6
      acceleration: -7.54
      command: -33.0
    }
    calibration {
      speed: 0.6
      acceleration: -5.04
      command: -30.0
    }
    calibration {
      speed: 0.6
      acceleration: -2.98
      command: -27.0
    }
    calibration {
      speed: 0.6
      acceleration: -1.66
      command: -25.0
    }
    calibration {
      speed: 0.6
      acceleration: -0.5
      command: -22.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.02
      command: -20.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.31
      command: -15.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.35
      command: -13.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.36
      command: 15.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.38
      command: -17.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.43
      command: 17.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.85
      command: 20.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.18
      command: 22.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.71
      command: 25.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.85
      command: 27.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.96
      command: 30.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.16
      command: 35.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.34
      command: 40.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.37
      command: 50.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.4
      command: 45.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.57
      command: 60.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.61
      command: 55.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.72
      command: 65.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.77
      command: 72.5
    }
    calibration {
      speed: 0.6
      acceleration: 2.8
      command: 80.0
    }
    calibration {
      speed: 0.8
      acceleration: -9.18
      command: -35.0
    }
    calibration {
      speed: 0.8
      acceleration: -8.26
      command: -33.0
    }
    calibration {
      speed: 0.8
      acceleration: -5.13
      command: -30.0
    }
    calibration {
      speed: 0.8
      acceleration: -2.78
      command: -27.0
    }
    calibration {
      speed: 0.8
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 0.8
      acceleration: -0.48
      command: -22.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.01
      command: -20.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.29
      command: -15.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.31
      command: 1.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.35
      command: -17.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.37
      command: 17.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.87
      command: 20.0
    }
    calibration {
      speed: 0.8
      acceleration: 1.23
      command: 22.0
    }
    calibration {
      speed: 0.8
      acceleration: 1.82
      command: 25.0
    }
    calibration {
      speed: 0.8
      acceleration: 1.99
      command: 27.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.1
      command: 30.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.34
      command: 35.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.46
      command: 50.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.52
      command: 42.5
    }
    calibration {
      speed: 0.8
      acceleration: 2.61
      command: 60.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.7
      command: 55.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.73
      command: 70.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.74
      command: 75.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.76
      command: 65.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.78
      command: 80.0
    }
    calibration {
      speed: 1.0
      acceleration: -9.17
      command: -35.0
    }
    calibration {
      speed: 1.0
      acceleration: -8.44
      command: -33.0
    }
    calibration {
      speed: 1.0
      acceleration: -4.97
      command: -30.0
    }
    calibration {
      speed: 1.0
      acceleration: -2.72
      command: -27.0
    }
    calibration {
      speed: 1.0
      acceleration: -1.74
      command: -25.0
    }
    calibration {
      speed: 1.0
      acceleration: -0.5
      command: -22.0
    }
    calibration {
      speed: 1.0
      acceleration: -0.07
      command: -20.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.21
      command: -15.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.27
      command: -13.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.28
      command: -1.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.32
      command: 17.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.89
      command: 20.0
    }
    calibration {
      speed: 1.0
      acceleration: 1.25
      command: 22.0
    }
    calibration {
      speed: 1.0
      acceleration: 1.89
      command: 25.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.09
      command: 27.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.2
      command: 30.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.49
      command: 35.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.56
      command: 50.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.62
      command: 45.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.66
      command: 40.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.67
      command: 60.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.7
      command: 70.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.73
      command: 75.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.76
      command: 66.6666666667
    }
    calibration {
      speed: 1.2
      acceleration: -9.07
      command: -35.0
    }
    calibration {
      speed: 1.2
      acceleration: -8.35
      command: -33.0
    }
    calibration {
      speed: 1.2
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 1.2
      acceleration: -2.78
      command: -27.0
    }
    calibration {
      speed: 1.2
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 1.2
      acceleration: -0.53
      command: -22.0
    }
    calibration {
      speed: 1.2
      acceleration: -0.09
      command: -20.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.14
      command: -15.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.19
      command: 15.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.21
      command: -13.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.22
      command: -17.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.23
      command: 17.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.87
      command: 20.0
    }
    calibration {
      speed: 1.2
      acceleration: 1.27
      command: 22.0
    }
    calibration {
      speed: 1.2
      acceleration: 1.95
      command: 25.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.16
      command: 27.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.27
      command: 30.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.59
      command: 35.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.68
      command: 50.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.71
      command: 45.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.74
      command: 70.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.77
      command: 40.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.78
      command: 60.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.8
      command: 73.3333333333
    }
    calibration {
      speed: 1.2
      acceleration: 2.82
      command: 55.0
    }
    calibration {
      speed: 1.4
      acceleration: -8.98
      command: -35.0
    }
    calibration {
      speed: 1.4
      acceleration: -8.12
      command: -33.0
    }
    calibration {
      speed: 1.4
      acceleration: -4.67
      command: -30.0
    }
    calibration {
      speed: 1.4
      acceleration: -2.82
      command: -27.0
    }
    calibration {
      speed: 1.4
      acceleration: -1.69
      command: -25.0
    }
    calibration {
      speed: 1.4
      acceleration: -0.54
      command: -22.0
    }
    calibration {
      speed: 1.4
      acceleration: -0.14
      command: -20.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.11
      command: -15.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.14
      command: -17.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.15
      command: 1.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.18
      command: 17.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.87
      command: 20.0
    }
    calibration {
      speed: 1.4
      acceleration: 1.3
      command: 22.0
    }
    calibration {
      speed: 1.4
      acceleration: 1.99
      command: 25.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.23
      command: 27.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.35
      command: 30.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.68
      command: 35.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.77
      command: 50.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.8
      command: 45.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.82
      command: 70.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.84
      command: 40.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.87
      command: 60.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.88
      command: 72.5
    }
    calibration {
      speed: 1.4
      acceleration: 2.89
      command: 65.0
    }
    calibration {
      speed: 1.6
      acceleration: -8.91
      command: -35.0
    }
    calibration {
      speed: 1.6
      acceleration: -7.88
      command: -33.0
    }
    calibration {
      speed: 1.6
      acceleration: -4.66
      command: -30.0
    }
    calibration {
      speed: 1.6
      acceleration: -2.79
      command: -27.0
    }
    calibration {
      speed: 1.6
      acceleration: -1.69
      command: -25.0
    }
    calibration {
      speed: 1.6
      acceleration: -0.56
      command: -22.0
    }
    calibration {
      speed: 1.6
      acceleration: -0.2
      command: -20.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.04
      command: -15.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.05
      command: 15.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.08
      command: -17.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.09
      command: -13.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.13
      command: 17.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.8
      command: 20.0
    }
    calibration {
      speed: 1.6
      acceleration: 1.29
      command: 22.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.03
      command: 25.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.29
      command: 27.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.41
      command: 30.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.74
      command: 35.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.85
      command: 47.5
    }
    calibration {
      speed: 1.6
      acceleration: 2.89
      command: 70.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.9
      command: 40.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.94
      command: 60.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.95
      command: 70.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.96
      command: 75.0
    }
    calibration {
      speed: 1.8
      acceleration: -8.81
      command: -35.0
    }
    calibration {
      speed: 1.8
      acceleration: -7.64
      command: -33.0
    }
    calibration {
      speed: 1.8
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 1.8
      acceleration: -2.72
      command: -27.0
    }
    calibration {
      speed: 1.8
      acceleration: -1.68
      command: -25.0
    }
    calibration {
      speed: 1.8
      acceleration: -0.58
      command: -22.0
    }
    calibration {
      speed: 1.8
      acceleration: -0.24
      command: -20.0
    }
    calibration {
      speed: 1.8
      acceleration: -0.02
      command: -15.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.02
      command: -1.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.03
      command: -13.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.08
      command: 17.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.78
      command: 20.0
    }
    calibration {
      speed: 1.8
      acceleration: 1.25
      command: 22.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.05
      command: 25.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.34
      command: 27.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.48
      command: 30.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.8
      command: 35.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.9
      command: 47.5
    }
    calibration {
      speed: 1.8
      acceleration: 2.93
      command: 70.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.95
      command: 40.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.96
      command: 80.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.97
      command: 60.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.98
      command: 67.5
    }
    calibration {
      speed: 2.0
      acceleration: -8.72
      command: -35.0
    }
    calibration {
      speed: 2.0
      acceleration: -7.47
      command: -33.0
    }
    calibration {
      speed: 2.0
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 2.0
      acceleration: -2.66
      command: -27.0
    }
    calibration {
      speed: 2.0
      acceleration: -1.67
      command: -25.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.62
      command: -22.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.07
      command: 15.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.05
      command: -17.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.03
      command: -13.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.02
      command: -15.0
    }
    calibration {
      speed: 2.0
      acceleration: 0.05
      command: 17.0
    }
    calibration {
      speed: 2.0
      acceleration: 0.7
      command: 20.0
    }
    calibration {
      speed: 2.0
      acceleration: 1.22
      command: 22.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.05
      command: 25.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.36
      command: 27.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.51
      command: 30.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.85
      command: 35.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.92
      command: 70.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.95
      command: 58.3333333333
    }
    calibration {
      speed: 2.0
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.98
      command: 55.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.99
      command: 40.0
    }
    calibration {
      speed: 2.0
      acceleration: 3.01
      command: 60.0
    }
    calibration {
      speed: 2.2
      acceleration: -8.65
      command: -35.0
    }
    calibration {
      speed: 2.2
      acceleration: -7.37
      command: -33.0
    }
    calibration {
      speed: 2.2
      acceleration: -4.84
      command: -30.0
    }
    calibration {
      speed: 2.2
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 2.2
      acceleration: -1.66
      command: -25.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.13
      command: 1.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.1
      command: -15.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.09
      command: -17.0
    }
    calibration {
      speed: 2.2
      acceleration: 0.08
      command: 17.0
    }
    calibration {
      speed: 2.2
      acceleration: 0.59
      command: 20.0
    }
    calibration {
      speed: 2.2
      acceleration: 1.13
      command: 22.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.02
      command: 25.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.35
      command: 27.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.53
      command: 30.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.89
      command: 35.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.9
      command: 70.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.93
      command: 65.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.94
      command: 80.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.95
      command: 75.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.98
      command: 55.0
    }
    calibration {
      speed: 2.2
      acceleration: 3.0
      command: 50.0
    }
    calibration {
      speed: 2.2
      acceleration: 3.01
      command: 45.0
    }
    calibration {
      speed: 2.2
      acceleration: 3.03
      command: 50.0
    }
    calibration {
      speed: 2.4
      acceleration: -8.64
      command: -35.0
    }
    calibration {
      speed: 2.4
      acceleration: -7.35
      command: -33.0
    }
    calibration {
      speed: 2.4
      acceleration: -4.87
      command: -30.0
    }
    calibration {
      speed: 2.4
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 2.4
      acceleration: -1.66
      command: -25.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.14
      command: -13.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.13
      command: 15.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.1
      command: -15.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.09
      command: -17.0
    }
    calibration {
      speed: 2.4
      acceleration: 0.08
      command: 17.0
    }
    calibration {
      speed: 2.4
      acceleration: 0.5
      command: 20.0
    }
    calibration {
      speed: 2.4
      acceleration: 1.03
      command: 22.0
    }
    calibration {
      speed: 2.4
      acceleration: 1.96
      command: 25.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.32
      command: 27.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.54
      command: 30.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.89
      command: 70.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.91
      command: 35.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.93
      command: 65.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.95
      command: 80.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.97
      command: 75.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.0
      command: 55.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.05
      command: 50.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.06
      command: 60.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.07
      command: 45.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.08
      command: 40.0
    }
    calibration {
      speed: 2.6
      acceleration: -8.68
      command: -35.0
    }
    calibration {
      speed: 2.6
      acceleration: -7.36
      command: -33.0
    }
    calibration {
      speed: 2.6
      acceleration: -4.86
      command: -30.0
    }
    calibration {
      speed: 2.6
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 2.6
      acceleration: -1.68
      command: -25.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.64
      command: -22.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.13
      command: 15.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.12
      command: -14.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.11
      command: -17.0
    }
    calibration {
      speed: 2.6
      acceleration: 0.06
      command: 17.0
    }
    calibration {
      speed: 2.6
      acceleration: 0.4
      command: 20.0
    }
    calibration {
      speed: 2.6
      acceleration: 0.92
      command: 22.0
    }
    calibration {
      speed: 2.6
      acceleration: 1.9
      command: 25.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.28
      command: 27.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.55
      command: 30.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.91
      command: 70.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.92
      command: 35.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.96
      command: 65.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.99
      command: 80.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.01
      command: 75.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.03
      command: 55.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.09
      command: 60.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.11
      command: 50.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.12
      command: 42.5
    }
    calibration {
      speed: 2.8
      acceleration: -8.76
      command: -35.0
    }
    calibration {
      speed: 2.8
      acceleration: -7.41
      command: -33.0
    }
    calibration {
      speed: 2.8
      acceleration: -4.84
      command: -30.0
    }
    calibration {
      speed: 2.8
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 2.8
      acceleration: -1.7
      command: -25.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.13
      command: -5.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.11
      command: -15.0
    }
    calibration {
      speed: 2.8
      acceleration: 0.06
      command: 17.0
    }
    calibration {
      speed: 2.8
      acceleration: 0.35
      command: 20.0
    }
    calibration {
      speed: 2.8
      acceleration: 0.84
      command: 22.0
    }
    calibration {
      speed: 2.8
      acceleration: 1.82
      command: 25.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.24
      command: 27.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.57
      command: 30.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.93
      command: 35.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.94
      command: 70.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.02
      command: 65.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.03
      command: 80.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.04
      command: 75.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.06
      command: 55.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.13
      command: 60.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.15
      command: 45.0
    }
    calibration {
      speed: 3.0
      acceleration: -8.84
      command: -35.0
    }
    calibration {
      speed: 3.0
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 3.0
      acceleration: -4.82
      command: -30.0
    }
    calibration {
      speed: 3.0
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 3.0
      acceleration: -1.73
      command: -25.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.13
      command: 0.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.12
      command: -13.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.1
      command: -17.0
    }
    calibration {
      speed: 3.0
      acceleration: 0.03
      command: 17.0
    }
    calibration {
      speed: 3.0
      acceleration: 0.31
      command: 20.0
    }
    calibration {
      speed: 3.0
      acceleration: 0.76
      command: 22.0
    }
    calibration {
      speed: 3.0
      acceleration: 1.72
      command: 25.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.2
      command: 27.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.58
      command: 30.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.94
      command: 35.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.04
      command: 72.5
    }
    calibration {
      speed: 3.0
      acceleration: 3.05
      command: 75.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.07
      command: 55.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.14
      command: 60.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.15
      command: 45.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.16
      command: 40.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.17
      command: 50.0
    }
    calibration {
      speed: 3.2
      acceleration: -8.93
      command: -35.0
    }
    calibration {
      speed: 3.2
      acceleration: -7.55
      command: -33.0
    }
    calibration {
      speed: 3.2
      acceleration: -4.8
      command: -30.0
    }
    calibration {
      speed: 3.2
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 3.2
      acceleration: -1.74
      command: -25.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.29
      command: -20.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.15
      command: 15.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.13
      command: -14.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.12
      command: -17.0
    }
    calibration {
      speed: 3.2
      acceleration: 0.04
      command: 17.0
    }
    calibration {
      speed: 3.2
      acceleration: 0.27
      command: 20.0
    }
    calibration {
      speed: 3.2
      acceleration: 0.7
      command: 22.0
    }
    calibration {
      speed: 3.2
      acceleration: 1.65
      command: 25.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.12
      command: 27.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.57
      command: 30.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.93
      command: 35.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.03
      command: 80.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.04
      command: 70.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.07
      command: 55.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.13
      command: 45.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.15
      command: 60.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.17
      command: 45.0
    }
    calibration {
      speed: 3.4
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 3.4
      acceleration: -7.6
      command: -33.0
    }
    calibration {
      speed: 3.4
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 3.4
      acceleration: -2.61
      command: -27.0
    }
    calibration {
      speed: 3.4
      acceleration: -1.74
      command: -25.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.16
      command: 15.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.14
      command: -13.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.13
      command: -17.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.11
      command: -15.0
    }
    calibration {
      speed: 3.4
      acceleration: 0.06
      command: 17.0
    }
    calibration {
      speed: 3.4
      acceleration: 0.27
      command: 20.0
    }
    calibration {
      speed: 3.4
      acceleration: 0.63
      command: 22.0
    }
    calibration {
      speed: 3.4
      acceleration: 1.58
      command: 25.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.04
      command: 27.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.54
      command: 30.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.92
      command: 35.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.02
      command: 80.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.03
      command: 70.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.05
      command: 55.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.12
      command: 45.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.14
      command: 60.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.15
      command: 50.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.16
      command: 40.0
    }
    calibration {
      speed: 3.6
      acceleration: -9.02
      command: -35.0
    }
    calibration {
      speed: 3.6
      acceleration: -7.61
      command: -33.0
    }
    calibration {
      speed: 3.6
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 3.6
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 3.6
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.29
      command: -20.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.16
      command: -13.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.15
      command: 15.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.13
      command: -15.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.11
      command: -17.0
    }
    calibration {
      speed: 3.6
      acceleration: 0.29
      command: 20.0
    }
    calibration {
      speed: 3.6
      acceleration: 0.58
      command: 22.0
    }
    calibration {
      speed: 3.6
      acceleration: 1.51
      command: 25.0
    }
    calibration {
      speed: 3.6
      acceleration: 1.99
      command: 27.0
    }
    calibration {
      speed: 3.6
      acceleration: 2.5
      command: 30.0
    }
    calibration {
      speed: 3.6
      acceleration: 2.89
      command: 35.0
    }
    calibration {
      speed: 3.6
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.01
      command: 80.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.02
      command: 65.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.03
      command: 55.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.04
      command: 75.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.11
      command: 45.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.12
      command: 55.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.14
      command: 40.0
    }
    calibration {
      speed: 3.8
      acceleration: -9.01
      command: -35.0
    }
    calibration {
      speed: 3.8
      acceleration: -7.6
      command: -33.0
    }
    calibration {
      speed: 3.8
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 3.8
      acceleration: -2.61
      command: -27.0
    }
    calibration {
      speed: 3.8
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.15
      command: -1.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.14
      command: -13.0
    }
    calibration {
      speed: 3.8
      acceleration: 0.26
      command: 20.0
    }
    calibration {
      speed: 3.8
      acceleration: 0.52
      command: 22.0
    }
    calibration {
      speed: 3.8
      acceleration: 1.42
      command: 25.0
    }
    calibration {
      speed: 3.8
      acceleration: 1.94
      command: 27.0
    }
    calibration {
      speed: 3.8
      acceleration: 2.43
      command: 30.0
    }
    calibration {
      speed: 3.8
      acceleration: 2.85
      command: 35.0
    }
    calibration {
      speed: 3.8
      acceleration: 2.99
      command: 70.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.0
      command: 55.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.04
      command: 72.5
    }
    calibration {
      speed: 3.8
      acceleration: 3.07
      command: 75.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.1
      command: 50.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.11
      command: 52.5
    }
    calibration {
      speed: 3.8
      acceleration: 3.13
      command: 40.0
    }
    calibration {
      speed: 4.0
      acceleration: -8.99
      command: -35.0
    }
    calibration {
      speed: 4.0
      acceleration: -7.58
      command: -33.0
    }
    calibration {
      speed: 4.0
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 4.0
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 4.0
      acceleration: -1.76
      command: -25.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.64
      command: -22.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.18
      command: 15.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.15
      command: -15.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.02
      command: 17.0
    }
    calibration {
      speed: 4.0
      acceleration: 0.24
      command: 20.0
    }
    calibration {
      speed: 4.0
      acceleration: 0.49
      command: 22.0
    }
    calibration {
      speed: 4.0
      acceleration: 1.33
      command: 25.0
    }
    calibration {
      speed: 4.0
      acceleration: 1.84
      command: 27.0
    }
    calibration {
      speed: 4.0
      acceleration: 2.35
      command: 30.0
    }
    calibration {
      speed: 4.0
      acceleration: 2.8
      command: 35.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.01
      command: 55.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.02
      command: 70.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.08
      command: 65.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.09
      command: 50.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.1
      command: 60.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.11
      command: 75.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.12
      command: 52.5
    }
    calibration {
      speed: 4.2
      acceleration: -8.98
      command: -35.0
    }
    calibration {
      speed: 4.2
      acceleration: -7.53
      command: -33.0
    }
    calibration {
      speed: 4.2
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 4.2
      acceleration: -2.67
      command: -27.0
    }
    calibration {
      speed: 4.2
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.6
      command: -22.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.28
      command: -20.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.17
      command: 15.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.16
      command: -13.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.12
      command: -17.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.04
      command: 17.0
    }
    calibration {
      speed: 4.2
      acceleration: 0.22
      command: 20.0
    }
    calibration {
      speed: 4.2
      acceleration: 0.47
      command: 22.0
    }
    calibration {
      speed: 4.2
      acceleration: 1.22
      command: 25.0
    }
    calibration {
      speed: 4.2
      acceleration: 1.71
      command: 27.0
    }
    calibration {
      speed: 4.2
      acceleration: 2.25
      command: 30.0
    }
    calibration {
      speed: 4.2
      acceleration: 2.75
      command: 35.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.04
      command: 55.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.06
      command: 55.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.1
      command: 50.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.12
      command: 65.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.13
      command: 52.5
    }
    calibration {
      speed: 4.2
      acceleration: 3.16
      command: 77.5
    }
    calibration {
      speed: 4.4
      acceleration: -8.88
      command: -35.0
    }
    calibration {
      speed: 4.4
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 4.4
      acceleration: -4.77
      command: -30.0
    }
    calibration {
      speed: 4.4
      acceleration: -2.67
      command: -27.0
    }
    calibration {
      speed: 4.4
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.27
      command: -20.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.16
      command: 15.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.15
      command: -13.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.03
      command: 17.0
    }
    calibration {
      speed: 4.4
      acceleration: 0.2
      command: 20.0
    }
    calibration {
      speed: 4.4
      acceleration: 0.43
      command: 22.0
    }
    calibration {
      speed: 4.4
      acceleration: 1.14
      command: 25.0
    }
    calibration {
      speed: 4.4
      acceleration: 1.59
      command: 27.0
    }
    calibration {
      speed: 4.4
      acceleration: 2.15
      command: 30.0
    }
    calibration {
      speed: 4.4
      acceleration: 2.68
      command: 35.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.02
      command: 40.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.07
      command: 55.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.08
      command: 70.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.11
      command: 50.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.12
      command: 45.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.16
      command: 62.5
    }
    calibration {
      speed: 4.4
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 4.6
      acceleration: -8.79
      command: -35.0
    }
    calibration {
      speed: 4.6
      acceleration: -7.43
      command: -33.0
    }
    calibration {
      speed: 4.6
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 4.6
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 4.6
      acceleration: -1.77
      command: -25.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.62
      command: -22.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.26
      command: -20.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.18
      command: 1.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.17
      command: -15.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.15
      command: -17.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.01
      command: 17.0
    }
    calibration {
      speed: 4.6
      acceleration: 0.15
      command: 20.0
    }
    calibration {
      speed: 4.6
      acceleration: 0.39
      command: 22.0
    }
    calibration {
      speed: 4.6
      acceleration: 1.04
      command: 25.0
    }
    calibration {
      speed: 4.6
      acceleration: 1.49
      command: 27.0
    }
    calibration {
      speed: 4.6
      acceleration: 2.04
      command: 30.0
    }
    calibration {
      speed: 4.6
      acceleration: 2.62
      command: 35.0
    }
    calibration {
      speed: 4.6
      acceleration: 2.97
      command: 40.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.08
      command: 45.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.1
      command: 55.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.12
      command: 60.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.17
      command: 65.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.19
      command: 60.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.21
      command: 80.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.23
      command: 75.0
    }
    calibration {
      speed: 4.8
      acceleration: -8.56
      command: -35.0
    }
    calibration {
      speed: 4.8
      acceleration: -7.39
      command: -33.0
    }
    calibration {
      speed: 4.8
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 4.8
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 4.8
      acceleration: -1.78
      command: -25.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.16
      command: 0.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 4.8
      acceleration: 0.11
      command: 20.0
    }
    calibration {
      speed: 4.8
      acceleration: 0.34
      command: 22.0
    }
    calibration {
      speed: 4.8
      acceleration: 0.94
      command: 25.0
    }
    calibration {
      speed: 4.8
      acceleration: 1.38
      command: 27.0
    }
    calibration {
      speed: 4.8
      acceleration: 1.92
      command: 30.0
    }
    calibration {
      speed: 4.8
      acceleration: 2.55
      command: 35.0
    }
    calibration {
      speed: 4.8
      acceleration: 2.92
      command: 40.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.04
      command: 45.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.12
      command: 52.5
    }
    calibration {
      speed: 4.8
      acceleration: 3.14
      command: 70.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.17
      command: 65.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.22
      command: 67.5
    }
    calibration {
      speed: 5.0
      acceleration: -8.57
      command: -35.0
    }
    calibration {
      speed: 5.0
      acceleration: -7.36
      command: -33.0
    }
    calibration {
      speed: 5.0
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 5.0
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 5.0
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.2
      command: 15.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.18
      command: -14.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.16
      command: -17.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 5.0
      acceleration: 0.11
      command: 20.0
    }
    calibration {
      speed: 5.0
      acceleration: 0.34
      command: 22.0
    }
    calibration {
      speed: 5.0
      acceleration: 0.86
      command: 25.0
    }
    calibration {
      speed: 5.0
      acceleration: 1.27
      command: 27.0
    }
    calibration {
      speed: 5.0
      acceleration: 1.8
      command: 30.0
    }
    calibration {
      speed: 5.0
      acceleration: 2.46
      command: 35.0
    }
    calibration {
      speed: 5.0
      acceleration: 2.87
      command: 40.0
    }
    calibration {
      speed: 5.0
      acceleration: 2.97
      command: 45.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.1
      command: 50.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.14
      command: 55.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.15
      command: 70.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.16
      command: 80.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.2
      command: 75.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.24
      command: 60.0
    }
    calibration {
      speed: 5.2
      acceleration: -8.6
      command: -35.0
    }
    calibration {
      speed: 5.2
      acceleration: -7.35
      command: -33.0
    }
    calibration {
      speed: 5.2
      acceleration: -4.77
      command: -30.0
    }
    calibration {
      speed: 5.2
      acceleration: -2.62
      command: -27.0
    }
    calibration {
      speed: 5.2
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.19
      command: 15.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.09
      command: 17.0
    }
    calibration {
      speed: 5.2
      acceleration: 0.1
      command: 20.0
    }
    calibration {
      speed: 5.2
      acceleration: 0.3
      command: 22.0
    }
    calibration {
      speed: 5.2
      acceleration: 0.79
      command: 25.0
    }
    calibration {
      speed: 5.2
      acceleration: 1.19
      command: 27.0
    }
    calibration {
      speed: 5.2
      acceleration: 1.71
      command: 30.0
    }
    calibration {
      speed: 5.2
      acceleration: 2.38
      command: 35.0
    }
    calibration {
      speed: 5.2
      acceleration: 2.83
      command: 40.0
    }
    calibration {
      speed: 5.2
      acceleration: 2.92
      command: 45.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.07
      command: 50.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.15
      command: 67.5
    }
    calibration {
      speed: 5.2
      acceleration: 3.16
      command: 70.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.19
      command: 70.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.25
      command: 60.0
    }
    calibration {
      speed: 5.4
      acceleration: -8.6
      command: -35.0
    }
    calibration {
      speed: 5.4
      acceleration: -7.35
      command: -33.0
    }
    calibration {
      speed: 5.4
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 5.4
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 5.4
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.2
      command: 15.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.17
      command: -15.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 5.4
      acceleration: 0.13
      command: 20.0
    }
    calibration {
      speed: 5.4
      acceleration: 0.29
      command: 22.0
    }
    calibration {
      speed: 5.4
      acceleration: 0.76
      command: 25.0
    }
    calibration {
      speed: 5.4
      acceleration: 1.12
      command: 27.0
    }
    calibration {
      speed: 5.4
      acceleration: 1.62
      command: 30.0
    }
    calibration {
      speed: 5.4
      acceleration: 2.31
      command: 35.0
    }
    calibration {
      speed: 5.4
      acceleration: 2.78
      command: 40.0
    }
    calibration {
      speed: 5.4
      acceleration: 2.89
      command: 45.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.04
      command: 50.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.16
      command: 68.3333333333
    }
    calibration {
      speed: 5.4
      acceleration: 3.19
      command: 75.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.26
      command: 60.0
    }
    calibration {
      speed: 5.6
      acceleration: -8.65
      command: -35.0
    }
    calibration {
      speed: 5.6
      acceleration: -7.36
      command: -33.0
    }
    calibration {
      speed: 5.6
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 5.6
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 5.6
      acceleration: -1.81
      command: -25.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.18
      command: 0.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.15
      command: -13.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.07
      command: 17.0
    }
    calibration {
      speed: 5.6
      acceleration: 0.09
      command: 20.0
    }
    calibration {
      speed: 5.6
      acceleration: 0.29
      command: 22.0
    }
    calibration {
      speed: 5.6
      acceleration: 0.7
      command: 25.0
    }
    calibration {
      speed: 5.6
      acceleration: 1.06
      command: 27.0
    }
    calibration {
      speed: 5.6
      acceleration: 1.54
      command: 30.0
    }
    calibration {
      speed: 5.6
      acceleration: 2.24
      command: 35.0
    }
    calibration {
      speed: 5.6
      acceleration: 2.72
      command: 40.0
    }
    calibration {
      speed: 5.6
      acceleration: 2.86
      command: 45.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.01
      command: 50.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.16
      command: 70.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.17
      command: 55.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.18
      command: 80.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.2
      command: 75.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.27
      command: 60.0
    }
    calibration {
      speed: 5.8
      acceleration: -8.68
      command: -35.0
    }
    calibration {
      speed: 5.8
      acceleration: -7.38
      command: -33.0
    }
    calibration {
      speed: 5.8
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 5.8
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 5.8
      acceleration: -1.82
      command: -25.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.21
      command: 15.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.2
      command: -13.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.07
      command: 17.0
    }
    calibration {
      speed: 5.8
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 5.8
      acceleration: 0.29
      command: 22.0
    }
    calibration {
      speed: 5.8
      acceleration: 0.69
      command: 25.0
    }
    calibration {
      speed: 5.8
      acceleration: 1.01
      command: 27.0
    }
    calibration {
      speed: 5.8
      acceleration: 1.47
      command: 30.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.18
      command: 35.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.66
      command: 40.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.83
      command: 45.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.99
      command: 50.0
    }
    calibration {
      speed: 5.8
      acceleration: 3.16
      command: 62.5
    }
    calibration {
      speed: 5.8
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 5.8
      acceleration: 3.2
      command: 77.5
    }
    calibration {
      speed: 5.8
      acceleration: 3.27
      command: 60.0
    }
    calibration {
      speed: 6.0
      acceleration: -8.86
      command: -35.0
    }
    calibration {
      speed: 6.0
      acceleration: -7.41
      command: -33.0
    }
    calibration {
      speed: 6.0
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 6.0
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 6.0
      acceleration: -1.81
      command: -25.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.66
      command: -22.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.23
      command: -13.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.2
      command: 0.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.18
      command: -17.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.1
      command: 17.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.09
      command: 20.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.27
      command: 22.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.7
      command: 25.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.98
      command: 27.0
    }
    calibration {
      speed: 6.0
      acceleration: 1.41
      command: 30.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.13
      command: 35.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.6
      command: 40.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.78
      command: 45.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.95
      command: 50.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.14
      command: 55.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.2
      command: 80.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.28
      command: 60.0
    }
    calibration {
      speed: 6.2
      acceleration: -8.91
      command: -35.0
    }
    calibration {
      speed: 6.2
      acceleration: -7.42
      command: -33.0
    }
    calibration {
      speed: 6.2
      acceleration: -4.74
      command: -30.0
    }
    calibration {
      speed: 6.2
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 6.2
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.34
      command: -20.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.19
      command: 0.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.06
      command: 20.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.28
      command: 22.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.63
      command: 25.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.94
      command: 27.0
    }
    calibration {
      speed: 6.2
      acceleration: 1.37
      command: 30.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.08
      command: 35.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.54
      command: 40.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.7
      command: 45.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.91
      command: 50.0
    }
    calibration {
      speed: 6.2
      acceleration: 3.11
      command: 55.0
    }
    calibration {
      speed: 6.2
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.2
      acceleration: 3.2
      command: 73.3333333333
    }
    calibration {
      speed: 6.2
      acceleration: 3.27
      command: 60.0
    }
    calibration {
      speed: 6.4
      acceleration: -8.96
      command: -35.0
    }
    calibration {
      speed: 6.4
      acceleration: -7.45
      command: -33.0
    }
    calibration {
      speed: 6.4
      acceleration: -4.72
      command: -30.0
    }
    calibration {
      speed: 6.4
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 6.4
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.34
      command: -20.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.2
      command: 15.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.19
      command: -15.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.07
      command: 17.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.25
      command: 22.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.63
      command: 25.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.91
      command: 27.0
    }
    calibration {
      speed: 6.4
      acceleration: 1.34
      command: 30.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.02
      command: 35.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.49
      command: 40.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.64
      command: 45.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.87
      command: 50.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.08
      command: 55.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.2
      command: 77.5
    }
    calibration {
      speed: 6.4
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.26
      command: 60.0
    }
    calibration {
      speed: 6.6
      acceleration: -8.98
      command: -35.0
    }
    calibration {
      speed: 6.6
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 6.6
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 6.6
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 6.6
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.66
      command: -22.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.23
      command: 15.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.21
      command: -15.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.2
      command: -13.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.23
      command: 22.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.62
      command: 25.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.89
      command: 27.0
    }
    calibration {
      speed: 6.6
      acceleration: 1.29
      command: 30.0
    }
    calibration {
      speed: 6.6
      acceleration: 1.97
      command: 35.0
    }
    calibration {
      speed: 6.6
      acceleration: 2.43
      command: 40.0
    }
    calibration {
      speed: 6.6
      acceleration: 2.6
      command: 45.0
    }
    calibration {
      speed: 6.6
      acceleration: 2.83
      command: 50.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.04
      command: 55.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.25
      command: 60.0
    }
    calibration {
      speed: 6.8
      acceleration: -9.02
      command: -35.0
    }
    calibration {
      speed: 6.8
      acceleration: -7.49
      command: -33.0
    }
    calibration {
      speed: 6.8
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 6.8
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 6.8
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.22
      command: 1.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.1
      command: 17.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.03
      command: 20.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.21
      command: 22.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.59
      command: 25.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.85
      command: 27.0
    }
    calibration {
      speed: 6.8
      acceleration: 1.25
      command: 30.0
    }
    calibration {
      speed: 6.8
      acceleration: 1.92
      command: 35.0
    }
    calibration {
      speed: 6.8
      acceleration: 2.38
      command: 40.0
    }
    calibration {
      speed: 6.8
      acceleration: 2.56
      command: 45.0
    }
    calibration {
      speed: 6.8
      acceleration: 2.79
      command: 50.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.01
      command: 55.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.23
      command: 70.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.24
      command: 60.0
    }
    calibration {
      speed: 7.0
      acceleration: -9.01
      command: -35.0
    }
    calibration {
      speed: 7.0
      acceleration: -7.51
      command: -33.0
    }
    calibration {
      speed: 7.0
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 7.0
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 7.0
      acceleration: -1.78
      command: -25.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.25
      command: -17.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.22
      command: 0.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.1
      command: 17.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.06
      command: 20.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.22
      command: 22.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.55
      command: 25.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.83
      command: 27.0
    }
    calibration {
      speed: 7.0
      acceleration: 1.21
      command: 30.0
    }
    calibration {
      speed: 7.0
      acceleration: 1.88
      command: 35.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.34
      command: 40.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.51
      command: 45.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.75
      command: 50.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.98
      command: 55.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.2
      command: 80.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.23
      command: 60.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.24
      command: 75.0
    }
    calibration {
      speed: 7.2
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 7.2
      acceleration: -7.51
      command: -33.0
    }
    calibration {
      speed: 7.2
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 7.2
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 7.2
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.7
      command: -22.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.23
      command: 15.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.22
      command: -17.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.2
      command: -15.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.17
      command: -13.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.11
      command: 17.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.19
      command: 22.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.55
      command: 25.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.78
      command: 27.0
    }
    calibration {
      speed: 7.2
      acceleration: 1.16
      command: 30.0
    }
    calibration {
      speed: 7.2
      acceleration: 1.83
      command: 35.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.3
      command: 40.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.45
      command: 45.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.7
      command: 50.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.95
      command: 55.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.21
      command: 70.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.25
      command: 75.0
    }
    calibration {
      speed: 7.4
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 7.4
      acceleration: -7.51
      command: -33.0
    }
    calibration {
      speed: 7.4
      acceleration: -4.72
      command: -30.0
    }
    calibration {
      speed: 7.4
      acceleration: -2.62
      command: -27.0
    }
    calibration {
      speed: 7.4
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.22
      command: -15.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.21
      command: -17.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.2
      command: -13.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.13
      command: 17.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.02
      command: 20.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.21
      command: 22.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.53
      command: 25.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.78
      command: 27.0
    }
    calibration {
      speed: 7.4
      acceleration: 1.15
      command: 30.0
    }
    calibration {
      speed: 7.4
      acceleration: 1.79
      command: 35.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.26
      command: 40.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.38
      command: 45.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.64
      command: 50.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.92
      command: 55.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.22
      command: 80.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.23
      command: 75.0
    }
    calibration {
      speed: 7.6
      acceleration: -8.99
      command: -35.0
    }
    calibration {
      speed: 7.6
      acceleration: -7.5
      command: -33.0
    }
    calibration {
      speed: 7.6
      acceleration: -4.72
      command: -30.0
    }
    calibration {
      speed: 7.6
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 7.6
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.36
      command: -20.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.26
      command: -15.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.2
      command: -17.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.16
      command: -13.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.14
      command: 17.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.02
      command: 20.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.16
      command: 22.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.52
      command: 25.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.75
      command: 27.0
    }
    calibration {
      speed: 7.6
      acceleration: 1.12
      command: 30.0
    }
    calibration {
      speed: 7.6
      acceleration: 1.76
      command: 35.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.2
      command: 40.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.32
      command: 45.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.58
      command: 50.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.88
      command: 55.0
    }
    calibration {
      speed: 7.6
      acceleration: 3.16
      command: 60.0
    }
    calibration {
      speed: 7.6
      acceleration: 3.19
      command: 67.5
    }
    calibration {
      speed: 7.6
      acceleration: 3.22
      command: 75.0
    }
    calibration {
      speed: 7.6
      acceleration: 3.23
      command: 80.0
    }
    calibration {
      speed: 7.8
      acceleration: -8.99
      command: -35.0
    }
    calibration {
      speed: 7.8
      acceleration: -7.49
      command: -33.0
    }
    calibration {
      speed: 7.8
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 7.8
      acceleration: -2.66
      command: -27.0
    }
    calibration {
      speed: 7.8
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.72
      command: -22.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.38
      command: -20.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.23
      command: -15.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.2
      command: -15.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.13
      command: 17.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.03
      command: 20.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.16
      command: 22.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.51
      command: 25.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.71
      command: 27.0
    }
    calibration {
      speed: 7.8
      acceleration: 1.08
      command: 30.0
    }
    calibration {
      speed: 7.8
      acceleration: 1.73
      command: 35.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.15
      command: 40.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.28
      command: 45.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.52
      command: 50.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.83
      command: 55.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.14
      command: 60.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.24
      command: 80.0
    }
    calibration {
      speed: 8.0
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 8.0
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 8.0
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 8.0
      acceleration: -2.68
      command: -27.0
    }
    calibration {
      speed: 8.0
      acceleration: -1.81
      command: -25.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.35
      command: -20.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.26
      command: 15.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.23
      command: -14.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.21
      command: -17.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.13
      command: 17.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.02
      command: 20.0
    }
    calibration {
      speed: 8.0
      acceleration: 0.16
      command: 22.0
    }
    calibration {
      speed: 8.0
      acceleration: 0.48
      command: 25.0
    }
    calibration {
      speed: 8.0
      acceleration: 0.69
      command: 27.0
    }
    calibration {
      speed: 8.0
      acceleration: 1.05
      command: 30.0
    }
    calibration {
      speed: 8.0
      acceleration: 1.69
      command: 35.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.11
      command: 40.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.25
      command: 45.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.48
      command: 50.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.79
      command: 55.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.11
      command: 60.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.22
      command: 75.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.23
      command: 80.0
    }
    calibration {
      speed: 8.2
      acceleration: -9.03
      command: -35.0
    }
    calibration {
      speed: 8.2
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 8.2
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 8.2
      acceleration: -2.68
      command: -27.0
    }
    calibration {
      speed: 8.2
      acceleration: -1.84
      command: -25.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.68
      command: -22.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.37
      command: -20.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.3
      command: -15.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.26
      command: -13.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.23
      command: -17.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.17
      command: 17.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 8.2
      acceleration: 0.14
      command: 22.0
    }
    calibration {
      speed: 8.2
      acceleration: 0.47
      command: 25.0
    }
    calibration {
      speed: 8.2
      acceleration: 0.67
      command: 27.0
    }
    calibration {
      speed: 8.2
      acceleration: 1.01
      command: 30.0
    }
    calibration {
      speed: 8.2
      acceleration: 1.66
      command: 35.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.07
      command: 40.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.23
      command: 45.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.46
      command: 50.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.77
      command: 55.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.09
      command: 60.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.15
      command: 70.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.21
      command: 80.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.24
      command: 75.0
    }
    calibration {
      speed: 8.4
      acceleration: -9.08
      command: -35.0
    }
    calibration {
      speed: 8.4
      acceleration: -7.49
      command: -33.0
    }
    calibration {
      speed: 8.4
      acceleration: -4.68
      command: -30.0
    }
    calibration {
      speed: 8.4
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 8.4
      acceleration: -1.83
      command: -25.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.69
      command: -22.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.37
      command: -20.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.3
      command: -15.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.27
      command: -17.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.25
      command: 1.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.19
      command: 17.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.15
      command: 22.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.43
      command: 25.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.63
      command: 27.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.99
      command: 30.0
    }
    calibration {
      speed: 8.4
      acceleration: 1.61
      command: 35.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.03
      command: 40.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.19
      command: 45.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.43
      command: 50.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.77
      command: 55.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.07
      command: 60.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.14
      command: 70.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.18
      command: 80.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.23
      command: 65.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.26
      command: 75.0
    }
    calibration {
      speed: 8.6
      acceleration: -9.15
      command: -35.0
    }
    calibration {
      speed: 8.6
      acceleration: -7.5
      command: -33.0
    }
    calibration {
      speed: 8.6
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 8.6
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 8.6
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.68
      command: -22.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.39
      command: -20.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.3
      command: 15.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.24
      command: -17.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.23
      command: -15.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.18
      command: 17.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.06
      command: 20.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.1
      command: 22.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.42
      command: 25.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.62
      command: 27.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.96
      command: 30.0
    }
    calibration {
      speed: 8.6
      acceleration: 1.56
      command: 35.0
    }
    calibration {
      speed: 8.6
      acceleration: 1.99
      command: 40.0
    }
    calibration {
      speed: 8.6
      acceleration: 2.15
      command: 45.0
    }
    calibration {
      speed: 8.6
      acceleration: 2.4
      command: 50.0
    }
    calibration {
      speed: 8.6
      acceleration: 2.77
      command: 55.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.06
      command: 60.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.14
      command: 70.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.15
      command: 80.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.23
      command: 65.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.27
      command: 75.0
    }
    calibration {
      speed: 8.8
      acceleration: -9.22
      command: -35.0
    }
    calibration {
      speed: 8.8
      acceleration: -7.55
      command: -33.0
    }
    calibration {
      speed: 8.8
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 8.8
      acceleration: -2.57
      command: -27.0
    }
    calibration {
      speed: 8.8
      acceleration: -1.77
      command: -25.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.34
      command: -20.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.3
      command: -13.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.24
      command: -17.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.21
      command: -15.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.16
      command: 17.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.05
      command: 20.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.12
      command: 22.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.4
      command: 25.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.61
      command: 27.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.93
      command: 30.0
    }
    calibration {
      speed: 8.8
      acceleration: 1.51
      command: 35.0
    }
    calibration {
      speed: 8.8
      acceleration: 1.95
      command: 40.0
    }
    calibration {
      speed: 8.8
      acceleration: 2.1
      command: 45.0
    }
    calibration {
      speed: 8.8
      acceleration: 2.37
      command: 50.0
    }
    calibration {
      speed: 8.8
      acceleration: 2.75
      command: 55.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.04
      command: 60.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.12
      command: 80.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.15
      command: 70.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.23
      command: 65.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.25
      command: 75.0
    }
    calibration {
      speed: 9.0
      acceleration: -9.27
      command: -35.0
    }
    calibration {
      speed: 9.0
      acceleration: -7.6
      command: -33.0
    }
    calibration {
      speed: 9.0
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 9.0
      acceleration: -2.57
      command: -27.0
    }
    calibration {
      speed: 9.0
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.74
      command: -22.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.41
      command: -20.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.29
      command: -15.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.2
      command: 17.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.15
      command: 20.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.12
      command: 22.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.38
      command: 25.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.57
      command: 27.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.88
      command: 30.0
    }
    calibration {
      speed: 9.0
      acceleration: 1.47
      command: 35.0
    }
    calibration {
      speed: 9.0
      acceleration: 1.91
      command: 40.0
    }
    calibration {
      speed: 9.0
      acceleration: 2.07
      command: 45.0
    }
    calibration {
      speed: 9.0
      acceleration: 2.33
      command: 50.0
    }
    calibration {
      speed: 9.0
      acceleration: 2.71
      command: 55.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.03
      command: 60.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.09
      command: 80.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.16
      command: 70.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.21
      command: 70.0
    }
    calibration {
      speed: 9.2
      acceleration: -9.34
      command: -35.0
    }
    calibration {
      speed: 9.2
      acceleration: -7.67
      command: -33.0
    }
    calibration {
      speed: 9.2
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 9.2
      acceleration: -2.61
      command: -27.0
    }
    calibration {
      speed: 9.2
      acceleration: -1.85
      command: -25.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.39
      command: -20.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.28
      command: -15.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.27
      command: 15.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.26
      command: -17.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.21
      command: -13.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.17
      command: 17.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.1
      command: 22.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.34
      command: 25.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.56
      command: 27.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.87
      command: 30.0
    }
    calibration {
      speed: 9.2
      acceleration: 1.44
      command: 35.0
    }
    calibration {
      speed: 9.2
      acceleration: 1.86
      command: 40.0
    }
    calibration {
      speed: 9.2
      acceleration: 2.05
      command: 45.0
    }
    calibration {
      speed: 9.2
      acceleration: 2.3
      command: 50.0
    }
    calibration {
      speed: 9.2
      acceleration: 2.65
      command: 55.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.02
      command: 60.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.05
      command: 80.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.17
      command: 75.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 9.4
      acceleration: -9.37
      command: -35.0
    }
    calibration {
      speed: 9.4
      acceleration: -7.76
      command: -33.0
    }
    calibration {
      speed: 9.4
      acceleration: -4.65
      command: -30.0
    }
    calibration {
      speed: 9.4
      acceleration: -2.66
      command: -27.0
    }
    calibration {
      speed: 9.4
      acceleration: -1.87
      command: -25.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.73
      command: -22.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.37
      command: -20.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.31
      command: 15.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.26
      command: -15.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.24
      command: -15.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.16
      command: 17.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.1
      command: 20.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.1
      command: 22.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.38
      command: 25.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.53
      command: 27.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.8
      command: 30.0
    }
    calibration {
      speed: 9.4
      acceleration: 1.38
      command: 35.0
    }
    calibration {
      speed: 9.4
      acceleration: 1.82
      command: 40.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.04
      command: 45.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.28
      command: 50.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.6
      command: 55.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.99
      command: 60.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.02
      command: 80.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.15
      command: 75.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.19
      command: 70.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 9.6
      acceleration: -9.36
      command: -35.0
    }
    calibration {
      speed: 9.6
      acceleration: -7.87
      command: -33.0
    }
    calibration {
      speed: 9.6
      acceleration: -4.56
      command: -30.0
    }
    calibration {
      speed: 9.6
      acceleration: -2.68
      command: -27.0
    }
    calibration {
      speed: 9.6
      acceleration: -1.88
      command: -25.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.73
      command: -22.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.38
      command: -20.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.29
      command: 15.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.28
      command: -15.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.26
      command: -17.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.2
      command: 17.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.07
      command: 20.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.09
      command: 22.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.34
      command: 25.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.53
      command: 27.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.8
      command: 30.0
    }
    calibration {
      speed: 9.6
      acceleration: 1.36
      command: 35.0
    }
    calibration {
      speed: 9.6
      acceleration: 1.79
      command: 40.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.01
      command: 45.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.25
      command: 50.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.57
      command: 55.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.98
      command: 60.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.99
      command: 80.0
    }
    calibration {
      speed: 9.6
      acceleration: 3.15
      command: 75.0
    }
    calibration {
      speed: 9.6
      acceleration: 3.2
      command: 70.0
    }
    calibration {
      speed: 9.6
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 9.8
      acceleration: -9.31
      command: -35.0
    }
    calibration {
      speed: 9.8
      acceleration: -7.97
      command: -33.0
    }
    calibration {
      speed: 9.8
      acceleration: -4.51
      command: -30.0
    }
    calibration {
      speed: 9.8
      acceleration: -2.67
      command: -27.0
    }
    calibration {
      speed: 9.8
      acceleration: -1.9
      command: -25.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.73
      command: -22.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.39
      command: -20.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.28
      command: -14.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.27
      command: -17.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.22
      command: 17.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.13
      command: 20.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.08
      command: 22.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.34
      command: 25.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.5
      command: 27.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.78
      command: 30.0
    }
    calibration {
      speed: 9.8
      acceleration: 1.35
      command: 35.0
    }
    calibration {
      speed: 9.8
      acceleration: 1.76
      command: 40.0
    }
    calibration {
      speed: 9.8
      acceleration: 1.97
      command: 45.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.21
      command: 50.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.55
      command: 55.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.96
      command: 60.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.98
      command: 80.0
    }
    calibration {
      speed: 9.8
      acceleration: 3.15
      command: 75.0
    }
    calibration {
      speed: 9.8
      acceleration: 3.2
      command: 70.0
    }
    calibration {
      speed: 9.8
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 10.0
      acceleration: -9.18
      command: -35.0
    }
    calibration {
      speed: 10.0
      acceleration: -8.04
      command: -33.0
    }
    calibration {
      speed: 10.0
      acceleration: -4.75
      command: -30.0
    }
    calibration {
      speed: 10.0
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 10.0
      acceleration: -1.92
      command: -25.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.75
      command: -22.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.36
      command: -20.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.29
      command: -13.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.27
      command: -15.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.24
      command: -17.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.21
      command: 17.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.08
      command: 22.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.32
      command: 25.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.49
      command: 27.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.75
      command: 30.0
    }
    calibration {
      speed: 10.0
      acceleration: 1.3
      command: 35.0
    }
    calibration {
      speed: 10.0
      acceleration: 1.71
      command: 40.0
    }
    calibration {
      speed: 10.0
      acceleration: 1.92
      command: 45.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.17
      command: 50.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.53
      command: 55.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.94
      command: 60.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.99
      command: 80.0
    }
    calibration {
      speed: 10.0
      acceleration: 3.14
      command: 75.0
    }
    calibration {
      speed: 10.0
      acceleration: 3.19
      command: 70.0
    }
    calibration {
      speed: 10.0
      acceleration: 3.21
      command: 65.0
    }
  }
}

lat_pid_controller_conf {
  ts: 0.02
  lateral_error_limit: 0.5
  heading_error_limit: 0.5

  lat_error_pid_conf {
    integrator_enable: false
    integrator_saturation_level: 0.02
    kp: 0.1
    ki: 0.0
    kd: 0.0
  }
  heading_error_pid_conf {
    integrator_enable: true
    integrator_saturation_level: 0.02
    kp: 0.6
    ki: 0.08
    kd: 0.0
  }

  cutoff_freq: 3.0
  mean_filter_window_size: 10
  query_relative_time: 0.2
  query_time_nearest_point_only: true
  enable_error_filter: true
  use_heading_error: true
  fix_trajectory_follow_location: true
  enable_wheel_steer_limit: true
  enable_maximum_steer_rate_limit: true
  use_output_wheel_steer_filter: false

  lat_calibration_table{
    calibration {
      steer_angle: -495.8
      steer_wheel_angle: -32.2865
    }
    calibration {
      steer_angle: -400.021
      steer_wheel_angle: -24.9346
    }
    calibration {
      steer_angle: -299.38
      steer_wheel_angle: -18.464
    }
    calibration {
      steer_angle: -199.363
      steer_wheel_angle: -11.6462
    }
    calibration {
      steer_angle: -100.066
      steer_wheel_angle: -5.65623
    }
    calibration {
      steer_angle: 100.623
      steer_wheel_angle: 6.01519
    }
    calibration {
      steer_angle: 200.145
      steer_wheel_angle: 11.9209
    }
    calibration {
      steer_angle: 299.529
      steer_wheel_angle: 17.1562
    }
    calibration {
      steer_angle: 401.0
      steer_wheel_angle: 25.5566
    }
    calibration {
      steer_angle: 500.682
      steer_wheel_angle: 33.7488
    }
  }
}

lat_lqrk1_controller_conf {
  ts: 0.02
  preview_window: 10.0
  x_error_limit: 10.0
  y_error_limit: 10.0
  heading_error_limit: 10.0
  eps: 0.01
  matrix_q: 0.0
  matrix_q: 1.0
  matrix_q: 1.0
  cutoff_freq: 10
  mean_filter_window_size: 10
  max_iteration: 150
  max_lateral_acceleration: 5.0

  x_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.2
    }
    scheduler {
      speed: 20.0
      ratio: 0.1
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  y_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.2
    }
    scheduler {
      speed: 20.0
      ratio: 0.1
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.4
    }
    scheduler {
      speed: 20.0
      ratio: 0.2
    }
    scheduler {
      speed: 25.0
      ratio: 0.1
    }
  }
  reverse_leadlag_conf {
    innerstate_saturation_level: 3000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }
  enable_reverse_leadlag_compensation: false
  enable_look_ahead_back_control: false
  lookahead_station: 1.4224
  enable_steer_mrac_control: false
  lookahead_station_high_speed: 1.4224
  query_relative_time: 0.2
  minimum_speed_protection: 0.1
}

lat_lqrk2_controller_conf {
  ts: 0.02
  state_size: 2
  preview_window: 0
  max_iteration: 240
  eps: 0.01
  matrix_q: 0.04
  matrix_q: 0.084
  matrix_r: 20.0
  cutoff_freq: 3.0
  mean_filter_window_size: 10
  lat_error_limit: 9.0
  heading_error_limit: 9.0
  max_lateral_acceleration: 5.0

  lat_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 0.15
    }
    scheduler {
      speed: 8.0
      ratio: 0.05
    }
    scheduler {
      speed: 12.0
      ratio: 0.04
    }
    scheduler {
      speed: 20.0
      ratio: 0.032
    }
    scheduler {
      speed: 25.0
      ratio: 0.025
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 0.2
    }
    scheduler {
      speed: 8.0
      ratio: 0.1
    }
    scheduler {
      speed: 12.0
      ratio: 0.085
    }
    scheduler {
      speed: 20.0
      ratio: 0.07
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  reverse_leadlag_conf {
    innerstate_saturation_level: 3000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }

  use_mean_filter: true
  use_low_pass_filter: true
  use_output_wheel_steer_filter: false
  use_curvature_filter: false
  enable_gain_scheduler: false
  query_time_nearest_point_only: true
  use_delay_prediction_compensation: false
  query_relative_time: 1.0
  use_delay_compensation: false
  delay_steps: 0
  set_steer_limit: true
  enable_reverse_leadlag_compensation: false
  enable_look_ahead_back_control: false
  lookahead_station: 1.4224
  enable_steer_mrac_control: false
  lookahead_station_high_speed: 1.4224
  minimum_speed_protection: 0.1
  curvature_cutoff_freq: 1.8
  feedback_ratio: 1.0
  feedforward_ratio: 1.0
  use_output_deadzone: false
  output_deadzone_threshold: 1.8
}


lat_mpck1_controller_conf
{
  ts: 0.02
  steer_tau: 0.2
  dim_x: 3
  dim_u: 1
  dim_y: 2
  min_prediction_length: 5.0
  preview_window: 25
  input_delay: 0.08
  max_iteration: 4000
  eps: 0.001
  polish: true
  verbose: false
  low_curvature_thresh: 0.0
  nominal_weight{
    lat_error: 1.0
    heading_error: 0.0
    heading_error_squared_vel: 0.3
    terminal_lat_error: 1.0
    terminal_heading_error: 0.1
    steering_input: 1.0
    steering_input_squared_vel: 0.25
    lat_jerk: 0.1
    steer_rate: 0.0
    steer_acc: 0.000001
  }
  low_curvature_weight{
    lat_error: 0.1
    heading_error: 0.0
    heading_error_squared_vel: 0.3
    terminal_lat_error: 1.0
    terminal_heading_error: 0.1
    steering_input: 1.0
    steering_input_squared_vel: 0.25
    lat_jerk: 0.0
    steer_rate: 0.0
    steer_acc: 0.000001
  }
  steer_rate_lim_map_by_curvature:{
    points:{
      x: 0.001
      y: 0.6978
    }
    points:{
      x: 0.002
      y: 0.8722
    }
    points:{
      x: 0.01
      y: 1.0467
    }
  }
  steer_rate_lim_map_by_velocity:{
    points:{
      x: 10.0
      y: 1.0467
    }
    points:{
      x: 15.0
      y: 0.8722
    }
    points:{
      x: 20.0
      y: 0.6978
    }
  }
  mean_filter_window_size: 10
  max_lateral_acceleration: 3.0
  use_mean_filter: true
  set_steer_limit: true
  minimum_speed_protection: 0.01
  admissible_position_error: 5.0
  admissible_yaw_error_rad: 1.57
  zero_steer_rad: 0.01
  prediction_dt: 0.1
  traj_resample_dist: 0.1
}

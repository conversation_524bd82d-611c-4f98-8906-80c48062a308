control_test_duration: -1
is_control_test_mode: false
max_localization_miss_num : 10
max_planning_miss_num: 20
max_chassis_miss_num:20

trajectory_period: 0.1
chassis_period: 0.02
localization_period: 0.02
control_period: 0.02

active_controllers: LON_CONTROLLER
active_controllers: LAT_PID_CONTROLLER

max_acceleration_when_stopped: 0.03
minimum_speed_resolution: 0.2
soft_estop_brake: -1.0
publish_control_debug: false
use_lat_control: false

use_debug_info_record: false
deque_size: 1000
save_rate: 50

lat_controller_conf {
  ts: 0.02
  preview_window: 10
  cf: 311405.828
  cr: 313160.041
  mass_fl: 452
  mass_fr: 452
  mass_rl: 710
  mass_rr: 710
  eps: 0.01
  matrix_q: 1.0
  matrix_q: 0.0
  matrix_q: 1.0
  matrix_q: 0.0
  reverse_matrix_q: 0.05
  reverse_matrix_q: 0.0
  reverse_matrix_q: 1.0
  reverse_matrix_q: 0.0
  cutoff_freq: 10
  mean_filter_window_size: 10
  max_iteration: 150
  max_lateral_acceleration: 5.0
  enable_reverse_leadlag_compensation: true
  enable_steer_mrac_control: false
  enable_look_ahead_back_control: true
  switch_speed: 10
  switch_speed_window: 2
  query_relative_time: 0.2
  lookahead_station: 1.4224
  lookback_station: 2.8448
  lookahead_station_high_speed: 1.4224
  lookback_station_high_speed: 2.8448
  minimum_speed_protection: 0.1
  lat_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.2
    }
    scheduler {
      speed: 20.0
      ratio: 0.1
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.4
    }
    scheduler {
      speed: 20.0
      ratio: 0.2
    }
    scheduler {
      speed: 25.0
      ratio: 0.1
    }
  }
  reverse_leadlag_conf {
    innerstate_saturation_level: 3000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }
}
lon_controller_conf {
  ts: 0.02
  brake_minimum_action: 0.0
  throttle_minimum_action: 0
  speed_controller_input_limit: 0.8
  station_error_limit: 6.0
  preview_window: 10.0
  standstill_acceleration: -1.5
  max_path_remain_when_stopped: 0.3

  state_update_params {
    drive_state_stop_dist: 0.5
    drive_state_offset_stop_dist: 1.0
    stopping_state_stop_dist: 0.5
    stopped_state_entry_vel: 0.01
    stopped_state_entry_acc: 0.1
    stopped_state_entry_duration_time: 0.1
    emergency_state_overshoot_stop_dist: 0.5
    driveoff_state_stop_dist: 1.5
    driving_state_entry_vel: 2.0
    emergency_state_traj_trans_dev: 3.0
    emergency_state_traj_rot_dev: 0.786  
    speed_error: 0.8
    station_error: 5.0
    use_keep_stoped_until_steer_convergence: false
    only_speed_follow: false
  }

  lon_state_params {
    driveoff_params {
      driveoff_min_acc: 0.6
      driveoff_max_jerk: 1.0
    }
    follow_distance_params {
      switch_speed: 3.0
      station_pid_conf {
        integrator_enable: false
        integrator_saturation_level: 0.3
        kp: 0.2
        ki: 0.0
        kd: 0.0
      }
      low_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 2.0
        ki: 0.3
        kd: 0.0
      }
      high_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 1.0
        ki: 0.3
        kd: 0.0
      }
    }
    follow_speed_params {
      switch_speed: 3.0
      low_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 2.0
        ki: 0.3
        kd: 0.0
      }
      high_speed_pid_conf {
        integrator_enable: true
        integrator_saturation_level: 0.3
        kp: 1.0
        ki: 0.3
        kd: 0.0
      }
    }
    emergency_params {
      emergency_min_acc: -4.0
      emergency_min_jerk: -2.0
    }
    start_params {

    }
    stopped_params {
      stopped_acc: -1.0
    }
    stopping_params {
      min_fast_val: 0.5
      strong_stop_dist: -0.3
      weak_stop_dist: 0.1
      strong_stop_acc: -2.4
      weak_stop_acc: -0.8
      strong_acc: -0.8
      weak_acc: -0.4
      weak_stop_time: 0.8
      min_running_vel: 0.01
      min_running_acc: 0.01
    }
  }

  pitch_angle_filter_conf {
    cutoff_freq: 5
  }
  station_error_filter_conf {
    cutoff_freq: 9
  }
  speed_error_filter_conf {
    cutoff_freq: 9
  }
  acc_filter_conf {
    cutoff_freq: 9
  }
  mean_filter_window_size: 10
  acc_mean_filter_window_size: 10

  station_leadlag_conf {
    innerstate_saturation_level: 1000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }
  speed_leadlag_conf {
    innerstate_saturation_level: 1000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }

  use_soft_switch: false
  soft_switch_speed: 3.5
  soft_sw_dece_threshold: -0.1
  soft_sw_acc_threshold: 0.05

  use_longitudinal_params_limited: true
  longitudinal_params {
    acceleration{
      x: 5.0
      y: 4.0
    }
    acceleration{
      x: 20.0
      y: 2.0
    }

    acceleration_rate{
      x: 5.0
      y: 4.0
    }
    acceleration_rate{
      x: 20.0
      y: 2.0
    }

    deceleration{
      x: 5.0
      y: 5.0
    }
    deceleration{
      x: 20.0
      y: 3.5
    }

    deceleration_rate{
      x: 5.0
      y: 5.0
    }
    deceleration_rate{
      x: 20.0
      y: 2.5
    }
  }

  use_pid_output_acceleration_limited: false
  pid_output_acceleration_limit: 0.8
  pid_output_acceleration_factor: 0.0

  use_calibration_value_rate_limited: false
  calibration_value_rate: 15.0

  enable_leadlag_compensation: false
  enable_speed_station_preview: true
  use_acceleration_limit: true
  use_acceleration_max_limit_follow_speed: true
  enable_slope_offset: false
  input_use_mean_filter: false
  input_use_digital_filter: false
  output_use_mean_filter: false
  output_use_digital_filter: false

  calibration_table{
    calibration {
      acceleration: -0.008
      command: 0.05
    }
    calibration {
      acceleration: 0.01
      command: 0.3
    }
    calibration {
      acceleration: 0.02
      command: 0.35
    }
    calibration {
      acceleration: 0.03
      command: 0.4
    }
    calibration {
      acceleration: 0.04
      command: 0.45
    }
    calibration {
      acceleration: 0.05
      command: 0.5
    }
    calibration {
      acceleration: 0.06
      command: 0.7
    }
    calibration {
      speed: 0.2
      acceleration: -0.075
      command: 0.025
    }
    calibration {
      speed: 0.2
      acceleration: -0.049
      command: 0.1
    }
    calibration {
      speed: 0.2
      acceleration: 0.182
      command: 0.65
    }
    calibration {
      speed: 0.2
      acceleration: 0.212
      command: 0.6
    }
    calibration {
      speed: 0.2
      acceleration: 0.241
      command: 0.3
    }
    calibration {
      speed: 0.2
      acceleration: 0.281
      command: 0.35
    }
    calibration {
      speed: 0.2
      acceleration: 0.309
      command: 0.4
    }
    calibration {
      speed: 0.2
      acceleration: 0.311
      command: 0.75
    }
    calibration {
      speed: 0.2
      acceleration: 0.325
      command: 0.85
    }
    calibration {
      speed: 0.2
      acceleration: 0.329
      command: 0.45
    }
    calibration {
      speed: 0.2
      acceleration: 0.387
      command: 0.5
    }
    calibration {
      speed: 0.2
      acceleration: 0.45
      command: 0.55
    }
    calibration {
      speed: 0.4
      acceleration: -0.152
      command: 0.025
    }
    calibration {
      speed: 0.4
      acceleration: -0.072
      command: 0.1
    }
    calibration {
      speed: 0.4
      acceleration: 0.665
      command: 0.6
    }
    calibration {
      speed: 0.4
      acceleration: 0.718
      command: 0.35
    }
    calibration {
      speed: 0.4
      acceleration: 0.721
      command: 0.3
    }
    calibration {
      speed: 0.4
      acceleration: 0.728
      command: 0.4
    }
    calibration {
      speed: 0.4
      acceleration: 0.738
      command: 0.45
    }
    calibration {
      speed: 0.4
      acceleration: 0.828
      command: 0.65
    }
    calibration {
      speed: 0.4
      acceleration: 0.853
      command: 0.75
    }
    calibration {
      speed: 0.4
      acceleration: 0.882
      command: 0.5
    }
    calibration {
      speed: 0.4
      acceleration: 0.896
      command: 0.85
    }
    calibration {
      speed: 0.4
      acceleration: 0.922
      command: 0.55
    }
    calibration {
      speed: 0.6
      acceleration: -0.222
      command: 0.025
    }
    calibration {
      speed: 0.6
      acceleration: -0.215
      command: 0.1
    }
    calibration {
      speed: 0.6
      acceleration: 0.801
      command: 0.3
    }
    calibration {
      speed: 0.6
      acceleration: 0.802
      command: 0.35
    }
    calibration {
      speed: 0.6
      acceleration: 0.9
      command: 0.4
    }
    calibration {
      speed: 0.6
      acceleration: 0.931
      command: 0.45
    }
    calibration {
      speed: 0.6
      acceleration: 1.014
      command: 0.65
    }
    calibration {
      speed: 0.6
      acceleration: 1.044
      command: 0.7
    }
    calibration {
      speed: 0.6
      acceleration: 1.055
      command: 0.8
    }
    calibration {
      speed: 0.6
      acceleration: 1.196
      command: 0.6
    }
    calibration {
      speed: 0.6
      acceleration: 1.266
      command: 0.5
    }
    calibration {
      speed: 0.6
      acceleration: 1.461
      command: 0.55
    }
    calibration {
      speed: 0.8
      acceleration: -0.314
      command: 0.025
    }
    calibration {
      speed: 0.8
      acceleration: -0.305
      command: 0.1
    }
    calibration {
      speed: 0.8
      acceleration: -0.008
      command: 0.15
    }
    calibration {
      speed: 0.8
      acceleration: 0.738
      command: 0.3
    }
    calibration {
      speed: 0.8
      acceleration: 0.896
      command: 0.85
    }
    calibration {
      speed: 0.8
      acceleration: 0.975
      command: 0.8
    }
    calibration {
      speed: 0.8
      acceleration: 1.03
      command: 0.35
    }
    calibration {
      speed: 0.8
      acceleration: 1.045
      command: 0.75
    }
    calibration {
      speed: 0.8
      acceleration: 1.213
      command: 0.4
    }
    calibration {
      speed: 0.8
      acceleration: 1.23
      command: 0.45
    }
    calibration {
      speed: 0.8
      acceleration: 1.375
      command: 0.6
    }
    calibration {
      speed: 0.8
      acceleration: 1.441
      command: 0.7
    }
    calibration {
      speed: 0.8
      acceleration: 1.548
      command: 0.5
    }
    calibration {
      speed: 0.8
      acceleration: 1.57
      command: 0.65
    }
    calibration {
      speed: 0.8
      acceleration: 1.693
      command: 0.55
    }
    calibration {
      speed: 1.0
      acceleration: -0.381
      command: 0.025
    }
    calibration {
      speed: 1.0
      acceleration: -0.314
      command: 0.1
    }
    calibration {
      speed: 1.0
      acceleration: -0.045
      command: 0.15
    }
    calibration {
      speed: 1.0
      acceleration: 0.462
      command: 0.3
    }
    calibration {
      speed: 1.0
      acceleration: 0.821
      command: 0.35
    }
    calibration {
      speed: 1.0
      acceleration: 1.045
      command: 0.775
    }
    calibration {
      speed: 1.0
      acceleration: 1.102
      command: 0.4
    }
    calibration {
      speed: 1.0
      acceleration: 1.373
      command: 0.45
    }
    calibration {
      speed: 1.0
      acceleration: 1.663
      command: 0.5
    }
    calibration {
      speed: 1.0
      acceleration: 1.753
      command: 0.55
    }
    calibration {
      speed: 1.0
      acceleration: 1.838
      command: 0.7
    }
    calibration {
      speed: 1.0
      acceleration: 1.84
      command: 0.6
    }
    calibration {
      speed: 1.0
      acceleration: 1.886
      command: 0.65
    }
    calibration {
      speed: 1.0
      acceleration: 2.292
      command: 0.85
    }
    calibration {
      speed: 1.2
      acceleration: -0.456
      command: 0.025
    }
    calibration {
      speed: 1.2
      acceleration: -0.348
      command: 0.1
    }
    calibration {
      speed: 1.2
      acceleration: -0.107
      command: 0.15
    }
    calibration {
      speed: 1.2
      acceleration: 0.44
      command: 0.3
    }
    calibration {
      speed: 1.2
      acceleration: 0.754
      command: 0.35
    }
    calibration {
      speed: 1.2
      acceleration: 1.019
      command: 0.4
    }
    calibration {
      speed: 1.2
      acceleration: 1.365
      command: 0.45
    }
    calibration {
      speed: 1.2
      acceleration: 1.594
      command: 0.75
    }
    calibration {
      speed: 1.2
      acceleration: 1.598
      command: 0.8
    }
    calibration {
      speed: 1.2
      acceleration: 1.634
      command: 0.5
    }
    calibration {
      speed: 1.2
      acceleration: 1.759
      command: 0.55
    }
    calibration {
      speed: 1.2
      acceleration: 1.958
      command: 0.6
    }
    calibration {
      speed: 1.2
      acceleration: 2.012
      command: 0.65
    }
    calibration {
      speed: 1.2
      acceleration: 2.027
      command: 0.7
    }
    calibration {
      speed: 1.2
      acceleration: 2.292
      command: 0.85
    }
    calibration {
      speed: 1.4
      acceleration: -0.539
      command: 0.025
    }
    calibration {
      speed: 1.4
      acceleration: -0.46
      command: 0.1
    }
    calibration {
      speed: 1.4
      acceleration: -0.185
      command: 0.15
    }
    calibration {
      speed: 1.4
      acceleration: 0.731
      command: 0.3
    }
    calibration {
      speed: 1.4
      acceleration: 0.79
      command: 0.35
    }
    calibration {
      speed: 1.4
      acceleration: 1.131
      command: 0.4
    }
    calibration {
      speed: 1.4
      acceleration: 1.339
      command: 0.45
    }
    calibration {
      speed: 1.4
      acceleration: 1.574
      command: 0.5
    }
    calibration {
      speed: 1.4
      acceleration: 1.594
      command: 0.75
    }
    calibration {
      speed: 1.4
      acceleration: 1.598
      command: 0.8
    }
    calibration {
      speed: 1.4
      acceleration: 1.734
      command: 0.55
    }
    calibration {
      speed: 1.4
      acceleration: 2.126
      command: 0.6
    }
    calibration {
      speed: 1.4
      acceleration: 2.24
      command: 0.65
    }
    calibration {
      speed: 1.4
      acceleration: 2.505
      command: 0.7
    }
    calibration {
      speed: 1.4
      acceleration: 3.824
      command: 0.85
    }
    calibration {
      speed: 1.6
      acceleration: -0.607
      command: 0.025
    }
    calibration {
      speed: 1.6
      acceleration: -0.489
      command: 0.1
    }
    calibration {
      speed: 1.6
      acceleration: -0.248
      command: 0.15
    }
    calibration {
      speed: 1.6
      acceleration: 0.017
      command: 0.2
    }
    calibration {
      speed: 1.6
      acceleration: 0.553
      command: 0.3
    }
    calibration {
      speed: 1.6
      acceleration: 0.742
      command: 0.35
    }
    calibration {
      speed: 1.6
      acceleration: 1.253
      command: 0.4
    }
    calibration {
      speed: 1.6
      acceleration: 1.354
      command: 0.45
    }
    calibration {
      speed: 1.6
      acceleration: 1.48
      command: 0.5
    }
    calibration {
      speed: 1.6
      acceleration: 1.76
      command: 0.55
    }
    calibration {
      speed: 1.6
      acceleration: 2.226
      command: 0.6
    }
    calibration {
      speed: 1.6
      acceleration: 2.27
      command: 0.65
    }
    calibration {
      speed: 1.6
      acceleration: 2.624
      command: 0.7
    }
    calibration {
      speed: 1.6
      acceleration: 2.775
      command: 0.75
    }
    calibration {
      speed: 1.6
      acceleration: 2.78
      command: 0.8
    }
    calibration {
      speed: 1.6
      acceleration: 3.824
      command: 0.85
    }
    calibration {
      speed: 1.8
      acceleration: -0.747
      command: 0.025
    }
    calibration {
      speed: 1.8
      acceleration: -0.514
      command: 0.1
    }
    calibration {
      speed: 1.8
      acceleration: -0.271
      command: 0.15
    }
    calibration {
      speed: 1.8
      acceleration: -0.024
      command: 0.2
    }
    calibration {
      speed: 1.8
      acceleration: 0.44
      command: 0.3
    }
    calibration {
      speed: 1.8
      acceleration: 0.742
      command: 0.35
    }
    calibration {
      speed: 1.8
      acceleration: 1.424
      command: 0.45
    }
    calibration {
      speed: 1.8
      acceleration: 1.444
      command: 0.4
    }
    calibration {
      speed: 1.8
      acceleration: 1.505
      command: 0.5
    }
    calibration {
      speed: 1.8
      acceleration: 1.876
      command: 0.55
    }
    calibration {
      speed: 1.8
      acceleration: 2.2
      command: 0.6
    }
    calibration {
      speed: 1.8
      acceleration: 2.201
      command: 0.65
    }
    calibration {
      speed: 1.8
      acceleration: 2.791
      command: 0.7
    }
    calibration {
      speed: 1.8
      acceleration: 3.209
      command: 0.75
    }
    calibration {
      speed: 1.8
      acceleration: 3.271
      command: 0.8
    }
    calibration {
      speed: 1.8
      acceleration: 4.549
      command: 0.85
    }
    calibration {
      speed: 2.0
      acceleration: -0.796
      command: 0.025
    }
    calibration {
      speed: 2.0
      acceleration: -0.55
      command: 0.1
    }
    calibration {
      speed: 2.0
      acceleration: -0.307
      command: 0.15
    }
    calibration {
      speed: 2.0
      acceleration: -0.069
      command: 0.2
    }
    calibration {
      speed: 2.0
      acceleration: 0.499
      command: 0.3
    }
    calibration {
      speed: 2.0
      acceleration: 0.782
      command: 0.35
    }
    calibration {
      speed: 2.0
      acceleration: 1.478
      command: 0.4
    }
    calibration {
      speed: 2.0
      acceleration: 1.488
      command: 0.45
    }
    calibration {
      speed: 2.0
      acceleration: 1.536
      command: 0.5
    }
    calibration {
      speed: 2.0
      acceleration: 2.028
      command: 0.55
    }
    calibration {
      speed: 2.0
      acceleration: 2.101
      command: 0.6
    }
    calibration {
      speed: 2.0
      acceleration: 2.137
      command: 0.65
    }
    calibration {
      speed: 2.0
      acceleration: 2.839
      command: 0.7
    }
    calibration {
      speed: 2.0
      acceleration: 3.875
      command: 0.75
    }
    calibration {
      speed: 2.0
      acceleration: 3.884
      command: 0.8
    }
    calibration {
      speed: 2.0
      acceleration: 5.197
      command: 0.85
    }
    calibration {
      speed: 2.2
      acceleration: -0.834
      command: 0.025
    }
    calibration {
      speed: 2.2
      acceleration: -0.588
      command: 0.1
    }
    calibration {
      speed: 2.2
      acceleration: -0.344
      command: 0.15
    }
    calibration {
      speed: 2.2
      acceleration: -0.104
      command: 0.2
    }
    calibration {
      speed: 2.2
      acceleration: 0.515
      command: 0.3
    }
    calibration {
      speed: 2.2
      acceleration: 0.715
      command: 0.35
    }
    calibration {
      speed: 2.2
      acceleration: 1.465
      command: 0.4
    }
    calibration {
      speed: 2.2
      acceleration: 1.473
      command: 0.45
    }
    calibration {
      speed: 2.2
      acceleration: 1.534
      command: 0.5
    }
    calibration {
      speed: 2.2
      acceleration: 2.117
      command: 0.55
    }
    calibration {
      speed: 2.2
      acceleration: 2.177
      command: 0.6
    }
    calibration {
      speed: 2.2
      acceleration: 2.194
      command: 0.65
    }
    calibration {
      speed: 2.2
      acceleration: 2.864
      command: 0.7
    }
    calibration {
      speed: 2.2
      acceleration: 4.326
      command: 0.75
    }
    calibration {
      speed: 2.2
      acceleration: 4.365
      command: 0.8
    }
    calibration {
      speed: 2.2
      acceleration: 6.141
      command: 0.85
    }
    calibration {
      speed: 2.4
      acceleration: -0.896
    }
    calibration {
      speed: 2.4
      acceleration: -0.868
      command: 0.05
    }
    calibration {
      speed: 2.4
      acceleration: -0.628
      command: 0.1
    }
    calibration {
      speed: 2.4
      acceleration: -0.379
      command: 0.15
    }
    calibration {
      speed: 2.4
      acceleration: -0.113
      command: 0.2
    }
    calibration {
      speed: 2.4
      acceleration: 0.409
      command: 0.3
    }
    calibration {
      speed: 2.4
      acceleration: 0.659
      command: 0.35
    }
    calibration {
      speed: 2.4
      acceleration: 1.3
      command: 0.4
    }
    calibration {
      speed: 2.4
      acceleration: 1.303
      command: 0.45
    }
    calibration {
      speed: 2.4
      acceleration: 1.468
      command: 0.5
    }
    calibration {
      speed: 2.4
      acceleration: 2.063
      command: 0.55
    }
    calibration {
      speed: 2.4
      acceleration: 2.087
      command: 0.65
    }
    calibration {
      speed: 2.4
      acceleration: 2.143
      command: 0.6
    }
    calibration {
      speed: 2.4
      acceleration: 2.869
      command: 0.7
    }
    calibration {
      speed: 2.4
      acceleration: 4.75
      command: 0.75
    }
    calibration {
      speed: 2.4
      acceleration: 4.833
      command: 0.8
    }
    calibration {
      speed: 2.4
      acceleration: 6.396
      command: 0.85
    }
    calibration {
      speed: 2.6
      acceleration: -0.992
    }
    calibration {
      speed: 2.6
      acceleration: -0.9
      command: 0.05
    }
    calibration {
      speed: 2.6
      acceleration: -0.671
      command: 0.1
    }
    calibration {
      speed: 2.6
      acceleration: -0.42
      command: 0.15
    }
    calibration {
      speed: 2.6
      acceleration: -0.16
      command: 0.2
    }
    calibration {
      speed: 2.6
      acceleration: 0.431
      command: 0.3
    }
    calibration {
      speed: 2.6
      acceleration: 0.663
      command: 0.35
    }
    calibration {
      speed: 2.6
      acceleration: 1.137
      command: 0.4
    }
    calibration {
      speed: 2.6
      acceleration: 1.146
      command: 0.45
    }
    calibration {
      speed: 2.6
      acceleration: 1.404
      command: 0.5
    }
    calibration {
      speed: 2.6
      acceleration: 1.874
      command: 0.55
    }
    calibration {
      speed: 2.6
      acceleration: 2.097
      command: 0.6
    }
    calibration {
      speed: 2.6
      acceleration: 2.12
      command: 0.65
    }
    calibration {
      speed: 2.6
      acceleration: 2.785
      command: 0.7
    }
    calibration {
      speed: 2.6
      acceleration: 5.006
      command: 0.75
    }
    calibration {
      speed: 2.6
      acceleration: 5.012
      command: 0.8
    }
    calibration {
      speed: 2.6
      acceleration: 6.447
      command: 0.85
    }
    calibration {
      speed: 2.8
      acceleration: -1.059
    }
    calibration {
      speed: 2.8
      acceleration: -0.936
      command: 0.05
    }
    calibration {
      speed: 2.8
      acceleration: -0.709
      command: 0.1
    }
    calibration {
      speed: 2.8
      acceleration: -0.461
      command: 0.15
    }
    calibration {
      speed: 2.8
      acceleration: -0.215
      command: 0.2
    }
    calibration {
      speed: 2.8
      acceleration: 0.348
      command: 0.3
    }
    calibration {
      speed: 2.8
      acceleration: 0.614
      command: 0.35
    }
    calibration {
      speed: 2.8
      acceleration: 0.79
      command: 0.4
    }
    calibration {
      speed: 2.8
      acceleration: 1.031
      command: 0.45
    }
    calibration {
      speed: 2.8
      acceleration: 1.316
      command: 0.5
    }
    calibration {
      speed: 2.8
      acceleration: 1.557
      command: 0.55
    }
    calibration {
      speed: 2.8
      acceleration: 1.942
      command: 0.6
    }
    calibration {
      speed: 2.8
      acceleration: 2.172
      command: 0.65
    }
    calibration {
      speed: 2.8
      acceleration: 2.676
      command: 0.7
    }
    calibration {
      speed: 2.8
      acceleration: 4.953
      command: 0.75
    }
    calibration {
      speed: 2.8
      acceleration: 5.063
      command: 0.8
    }
    calibration {
      speed: 2.8
      acceleration: 6.263
      command: 0.85
    }
    calibration {
      speed: 3.0
      acceleration: -1.119
    }
    calibration {
      speed: 3.0
      acceleration: -1.014
      command: 0.05
    }
    calibration {
      speed: 3.0
      acceleration: -0.748
      command: 0.1
    }
    calibration {
      speed: 3.0
      acceleration: -0.498
      command: 0.15
    }
    calibration {
      speed: 3.0
      acceleration: -0.237
      command: 0.2
    }
    calibration {
      speed: 3.0
      command: 0.25
    }
    calibration {
      speed: 3.0
      acceleration: 0.301
      command: 0.3
    }
    calibration {
      speed: 3.0
      acceleration: 0.597
      command: 0.35
    }
    calibration {
      speed: 3.0
      acceleration: 0.68
      command: 0.4
    }
    calibration {
      speed: 3.0
      acceleration: 0.981
      command: 0.45
    }
    calibration {
      speed: 3.0
      acceleration: 1.317
      command: 0.5
    }
    calibration {
      speed: 3.0
      acceleration: 1.439
      command: 0.55
    }
    calibration {
      speed: 3.0
      acceleration: 1.817
      command: 0.6
    }
    calibration {
      speed: 3.0
      acceleration: 2.24
      command: 0.65
    }
    calibration {
      speed: 3.0
      acceleration: 2.55
      command: 0.7
    }
    calibration {
      speed: 3.0
      acceleration: 4.153
      command: 0.75
    }
    calibration {
      speed: 3.0
      acceleration: 4.812
      command: 0.8
    }
    calibration {
      speed: 3.0
      acceleration: 5.969
      command: 0.85
    }
    calibration {
      speed: 3.2
      acceleration: -1.231
    }
    calibration {
      speed: 3.2
      acceleration: -1.039
      command: 0.05
    }
    calibration {
      speed: 3.2
      acceleration: -0.809
      command: 0.1
    }
    calibration {
      speed: 3.2
      acceleration: -0.549
      command: 0.15
    }
    calibration {
      speed: 3.2
      acceleration: -0.28
      command: 0.2
    }
    calibration {
      speed: 3.2
      acceleration: -0.035
      command: 0.25
    }
    calibration {
      speed: 3.2
      acceleration: 0.28
      command: 0.3
    }
    calibration {
      speed: 3.2
      acceleration: 0.521
      command: 0.35
    }
    calibration {
      speed: 3.2
      acceleration: 0.723
      command: 0.4
    }
    calibration {
      speed: 3.2
      acceleration: 0.972
      command: 0.45
    }
    calibration {
      speed: 3.2
      acceleration: 1.336
      command: 0.5
    }
    calibration {
      speed: 3.2
      acceleration: 1.363
      command: 0.55
    }
    calibration {
      speed: 3.2
      acceleration: 1.737
      command: 0.6
    }
    calibration {
      speed: 3.2
      acceleration: 2.253
      command: 0.65
    }
    calibration {
      speed: 3.2
      acceleration: 2.487
      command: 0.7
    }
    calibration {
      speed: 3.2
      acceleration: 3.8
      command: 0.75
    }
    calibration {
      speed: 3.2
      acceleration: 4.698
      command: 0.8
    }
    calibration {
      speed: 3.2
      acceleration: 5.169
      command: 0.85
    }
    calibration {
      speed: 3.4
      acceleration: -1.379
    }
    calibration {
      speed: 3.4
      acceleration: -1.083
      command: 0.05
    }
    calibration {
      speed: 3.4
      acceleration: -0.871
      command: 0.1
    }
    calibration {
      speed: 3.4
      acceleration: -0.595
      command: 0.15
    }
    calibration {
      speed: 3.4
      acceleration: -0.349
      command: 0.2
    }
    calibration {
      speed: 3.4
      acceleration: -0.08
      command: 0.25
    }
    calibration {
      speed: 3.4
      acceleration: 0.204
      command: 0.3
    }
    calibration {
      speed: 3.4
      acceleration: 0.445
      command: 0.35
    }
    calibration {
      speed: 3.4
      acceleration: 0.762
      command: 0.4
    }
    calibration {
      speed: 3.4
      acceleration: 0.977
      command: 0.45
    }
    calibration {
      speed: 3.4
      acceleration: 1.328
      command: 0.5
    }
    calibration {
      speed: 3.4
      acceleration: 1.417
      command: 0.55
    }
    calibration {
      speed: 3.4
      acceleration: 1.724
      command: 0.6
    }
    calibration {
      speed: 3.4
      acceleration: 2.231
      command: 0.65
    }
    calibration {
      speed: 3.4
      acceleration: 2.374
      command: 0.7
    }
    calibration {
      speed: 3.4
      acceleration: 2.817
      command: 0.75
    }
    calibration {
      speed: 3.4
      acceleration: 4.126
      command: 0.8
    }
    calibration {
      speed: 3.4
      acceleration: 4.729
      command: 0.85
    }
    calibration {
      speed: 3.6
      acceleration: -1.472
    }
    calibration {
      speed: 3.6
      acceleration: -1.185
      command: 0.05
    }
    calibration {
      speed: 3.6
      acceleration: -0.889
      command: 0.1
    }
    calibration {
      speed: 3.6
      acceleration: -0.674
      command: 0.15
    }
    calibration {
      speed: 3.6
      acceleration: -0.379
      command: 0.2
    }
    calibration {
      speed: 3.6
      acceleration: -0.125
      command: 0.25
    }
    calibration {
      speed: 3.6
      acceleration: 0.152
      command: 0.3
    }
    calibration {
      speed: 3.6
      acceleration: 0.434
      command: 0.35
    }
    calibration {
      speed: 3.6
      acceleration: 0.668
      command: 0.4
    }
    calibration {
      speed: 3.6
      acceleration: 0.985
      command: 0.45
    }
    calibration {
      speed: 3.6
      acceleration: 1.277
      command: 0.5
    }
    calibration {
      speed: 3.6
      acceleration: 1.498
      command: 0.55
    }
    calibration {
      speed: 3.6
      acceleration: 1.758
      command: 0.6
    }
    calibration {
      speed: 3.6
      acceleration: 2.15
      command: 0.65
    }
    calibration {
      speed: 3.6
      acceleration: 2.291
      command: 0.7
    }
    calibration {
      speed: 3.6
      acceleration: 2.397
      command: 0.75
    }
    calibration {
      speed: 3.6
      acceleration: 3.516
      command: 0.8
    }
    calibration {
      speed: 3.6
      acceleration: 3.919
      command: 0.85
    }
    calibration {
      speed: 3.8
      acceleration: -1.459
    }
    calibration {
      speed: 3.8
      acceleration: -1.285
      command: 0.05
    }
    calibration {
      speed: 3.8
      acceleration: -0.941
      command: 0.1
    }
    calibration {
      speed: 3.8
      acceleration: -0.705
      command: 0.15
    }
    calibration {
      speed: 3.8
      acceleration: -0.441
      command: 0.2
    }
    calibration {
      speed: 3.8
      acceleration: -0.176
      command: 0.25
    }
    calibration {
      speed: 3.8
      acceleration: 0.088
      command: 0.3
    }
    calibration {
      speed: 3.8
      acceleration: 0.36
      command: 0.35
    }
    calibration {
      speed: 3.8
      acceleration: 0.664
      command: 0.4
    }
    calibration {
      speed: 3.8
      acceleration: 0.964
      command: 0.45
    }
    calibration {
      speed: 3.8
      acceleration: 1.224
      command: 0.5
    }
    calibration {
      speed: 3.8
      acceleration: 1.545
      command: 0.55
    }
    calibration {
      speed: 3.8
      acceleration: 1.769
      command: 0.6
    }
    calibration {
      speed: 3.8
      acceleration: 2.088
      command: 0.65
    }
    calibration {
      speed: 3.8
      acceleration: 2.232
      command: 0.7
    }
    calibration {
      speed: 3.8
      acceleration: 2.233
      command: 0.75
    }
    calibration {
      speed: 3.8
      acceleration: 3.252
      command: 0.8
    }
    calibration {
      speed: 3.8
      acceleration: 3.592
      command: 0.85
    }
    calibration {
      speed: 4.0
      acceleration: -1.372
    }
    calibration {
      speed: 4.0
      acceleration: -1.344
      command: 0.05
    }
    calibration {
      speed: 4.0
      acceleration: -1.083
      command: 0.1
    }
    calibration {
      speed: 4.0
      acceleration: -0.736
      command: 0.15
    }
    calibration {
      speed: 4.0
      acceleration: -0.477
      command: 0.2
    }
    calibration {
      speed: 4.0
      acceleration: -0.223
      command: 0.25
    }
    calibration {
      speed: 4.0
      acceleration: 0.047
      command: 0.3
    }
    calibration {
      speed: 4.0
      acceleration: 0.322
      command: 0.35
    }
    calibration {
      speed: 4.0
      acceleration: 0.655
      command: 0.4
    }
    calibration {
      speed: 4.0
      acceleration: 0.899
      command: 0.45
    }
    calibration {
      speed: 4.0
      acceleration: 1.164
      command: 0.5
    }
    calibration {
      speed: 4.0
      acceleration: 1.531
      command: 0.55
    }
    calibration {
      speed: 4.0
      acceleration: 1.764
      command: 0.6
    }
    calibration {
      speed: 4.0
      acceleration: 2.044
      command: 0.65
    }
    calibration {
      speed: 4.0
      acceleration: 2.25
      command: 0.7
    }
    calibration {
      speed: 4.0
      acceleration: 2.252
      command: 0.75
    }
    calibration {
      speed: 4.0
      acceleration: 2.881
      command: 0.8
    }
    calibration {
      speed: 4.0
      acceleration: 3.334
      command: 0.85
    }
    calibration {
      speed: 4.2
      acceleration: -1.353
    }
    calibration {
      speed: 4.2
      acceleration: -1.25
      command: 0.05
    }
    calibration {
      speed: 4.2
      acceleration: -1.149
      command: 0.1
    }
    calibration {
      speed: 4.2
      acceleration: -0.869
      command: 0.15
    }
    calibration {
      speed: 4.2
      acceleration: -0.536
      command: 0.2
    }
    calibration {
      speed: 4.2
      acceleration: -0.278
      command: 0.25
    }
    calibration {
      speed: 4.2
      acceleration: -0.003
      command: 0.3
    }
    calibration {
      speed: 4.2
      acceleration: 0.278
      command: 0.35
    }
    calibration {
      speed: 4.2
      acceleration: 0.55
      command: 0.4
    }
    calibration {
      speed: 4.2
      acceleration: 0.849
      command: 0.45
    }
    calibration {
      speed: 4.2
      acceleration: 1.123
      command: 0.5
    }
    calibration {
      speed: 4.2
      acceleration: 1.419
      command: 0.55
    }
    calibration {
      speed: 4.2
      acceleration: 1.727
      command: 0.6
    }
    calibration {
      speed: 4.2
      acceleration: 2.029
      command: 0.65
    }
    calibration {
      speed: 4.2
      acceleration: 2.28
      command: 0.7
    }
    calibration {
      speed: 4.2
      acceleration: 2.508
      command: 0.75
    }
    calibration {
      speed: 4.2
      acceleration: 2.783
      command: 0.8
    }
    calibration {
      speed: 4.2
      acceleration: 3.029
      command: 0.85
    }
    calibration {
      speed: 4.4
      acceleration: -1.459
    }
    calibration {
      speed: 4.4
      acceleration: -1.099
      command: 0.05
    }
    calibration {
      speed: 4.4
      acceleration: -0.941
      command: 0.1
    }
    calibration {
      speed: 4.4
      acceleration: -0.897
      command: 0.15
    }
    calibration {
      speed: 4.4
      acceleration: -0.595
      command: 0.2
    }
    calibration {
      speed: 4.4
      acceleration: -0.34
      command: 0.25
    }
    calibration {
      speed: 4.4
      acceleration: -0.055
      command: 0.3
    }
    calibration {
      speed: 4.4
      acceleration: 0.221
      command: 0.35
    }
    calibration {
      speed: 4.4
      acceleration: 0.502
      command: 0.4
    }
    calibration {
      speed: 4.4
      acceleration: 0.773
      command: 0.45
    }
    calibration {
      speed: 4.4
      acceleration: 1.058
      command: 0.5
    }
    calibration {
      speed: 4.4
      acceleration: 1.358
      command: 0.55
    }
    calibration {
      speed: 4.4
      acceleration: 1.713
      command: 0.6
    }
    calibration {
      speed: 4.4
      acceleration: 2.043
      command: 0.65
    }
    calibration {
      speed: 4.4
      acceleration: 2.384
      command: 0.7
    }
    calibration {
      speed: 4.4
      acceleration: 2.633
      command: 0.75
    }
    calibration {
      speed: 4.4
      acceleration: 2.741
      command: 0.8
    }
    calibration {
      speed: 4.4
      acceleration: 2.967
      command: 0.85
    }
    calibration {
      speed: 4.6
      acceleration: -1.717
    }
    calibration {
      speed: 4.6
      acceleration: -1.006
      command: 0.05
    }
    calibration {
      speed: 4.6
      acceleration: -0.747
      command: 0.1
    }
    calibration {
      speed: 4.6
      acceleration: -0.676
      command: 0.15
    }
    calibration {
      speed: 4.6
      acceleration: -0.654
      command: 0.2
    }
    calibration {
      speed: 4.6
      acceleration: -0.394
      command: 0.25
    }
    calibration {
      speed: 4.6
      acceleration: -0.11
      command: 0.3
    }
    calibration {
      speed: 4.6
      acceleration: 0.176
      command: 0.35
    }
    calibration {
      speed: 4.6
      acceleration: 0.459
      command: 0.4
    }
    calibration {
      speed: 4.6
      acceleration: 0.721
      command: 0.45
    }
    calibration {
      speed: 4.6
      acceleration: 1.018
      command: 0.5
    }
    calibration {
      speed: 4.6
      acceleration: 1.334
      command: 0.55
    }
    calibration {
      speed: 4.6
      acceleration: 1.714
      command: 0.6
    }
    calibration {
      speed: 4.6
      acceleration: 2.07
      command: 0.65
    }
    calibration {
      speed: 4.6
      acceleration: 2.47
      command: 0.7
    }
    calibration {
      speed: 4.6
      acceleration: 2.752
      command: 0.75
    }
    calibration {
      speed: 4.6
      acceleration: 2.782
      command: 0.8
    }
    calibration {
      speed: 4.6
      acceleration: 2.947
      command: 0.85
    }
    calibration {
      speed: 4.8
      acceleration: -1.947
    }
    calibration {
      speed: 4.8
      acceleration: -1.247
      command: 0.05
    }
    calibration {
      speed: 4.8
      acceleration: -0.831
      command: 0.1
    }
    calibration {
      speed: 4.8
      acceleration: -0.598
      command: 0.15
    }
    calibration {
      speed: 4.8
      acceleration: -0.305
      command: 0.25
    }
    calibration {
      speed: 4.8
      acceleration: -0.303
      command: 0.2
    }
    calibration {
      speed: 4.8
      acceleration: -0.162
      command: 0.3
    }
    calibration {
      speed: 4.8
      acceleration: 0.119
      command: 0.35
    }
    calibration {
      speed: 4.8
      acceleration: 0.378
      command: 0.4
    }
    calibration {
      speed: 4.8
      acceleration: 0.737
      command: 0.45
    }
    calibration {
      speed: 4.8
      acceleration: 1.023
      command: 0.5
    }
    calibration {
      speed: 4.8
      acceleration: 1.345
      command: 0.55
    }
    calibration {
      speed: 4.8
      acceleration: 1.716
      command: 0.6
    }
    calibration {
      speed: 4.8
      acceleration: 2.068
      command: 0.65
    }
    calibration {
      speed: 4.8
      acceleration: 2.451
      command: 0.7
    }
    calibration {
      speed: 4.8
      acceleration: 2.743
      command: 0.75
    }
    calibration {
      speed: 4.8
      acceleration: 2.836
      command: 0.8
    }
    calibration {
      speed: 4.8
      acceleration: 2.961
      command: 0.85
    }
    calibration {
      speed: 5.0
      acceleration: -2.483
    }
    calibration {
      speed: 5.0
      acceleration: -2.145
      command: 0.05
    }
    calibration {
      speed: 5.0
      acceleration: -1.603
      command: 0.1
    }
    calibration {
      speed: 5.0
      acceleration: -1.421
      command: 0.15
    }
    calibration {
      speed: 5.0
      acceleration: -1.115
      command: 0.2
    }
    calibration {
      speed: 5.0
      acceleration: -0.45
      command: 0.25
    }
    calibration {
      speed: 5.0
      acceleration: -0.193
      command: 0.3
    }
    calibration {
      speed: 5.0
      acceleration: 0.03
      command: 0.35
    }
    calibration {
      speed: 5.0
      acceleration: 0.176
      command: 0.4
    }
    calibration {
      speed: 5.0
      acceleration: 0.34
      command: 0.45
    }
    calibration {
      speed: 5.0
      acceleration: 0.992
      command: 0.5
    }
    calibration {
      speed: 5.0
      acceleration: 1.309
      command: 0.55
    }
    calibration {
      speed: 5.0
      acceleration: 1.625
      command: 0.6
    }
    calibration {
      speed: 5.0
      acceleration: 1.987
      command: 0.65
    }
    calibration {
      speed: 5.0
      acceleration: 2.221
      command: 0.7
    }
    calibration {
      speed: 5.0
      acceleration: 2.775
      command: 0.75
    }
    calibration {
      speed: 5.0
      acceleration: 2.927
      command: 0.8
    }
    calibration {
      speed: 5.0
      acceleration: 2.96
      command: 0.85
    }
    calibration {
      speed: 5.2
      acceleration: -2.555
    }
    calibration {
      speed: 5.2
      acceleration: -2.427
      command: 0.05
    }
    calibration {
      speed: 5.2
      acceleration: -2.14
      command: 0.1
    }
    calibration {
      speed: 5.2
      acceleration: -1.904
      command: 0.15
    }
    calibration {
      speed: 5.2
      acceleration: -1.638
      command: 0.2
    }
    calibration {
      speed: 5.2
      acceleration: -1.342
      command: 0.25
    }
    calibration {
      speed: 5.2
      acceleration: -1.052
      command: 0.3
    }
    calibration {
      speed: 5.2
      acceleration: -0.758
      command: 0.35
    }
    calibration {
      speed: 5.2
      acceleration: -0.343
      command: 0.4
    }
    calibration {
      speed: 5.2
      acceleration: 0.039
      command: 0.45
    }
    calibration {
      speed: 5.2
      acceleration: 0.146
      command: 0.5
    }
    calibration {
      speed: 5.2
      acceleration: 0.345
      command: 0.55
    }
    calibration {
      speed: 5.2
      acceleration: 0.348
      command: 0.6
    }
    calibration {
      speed: 5.2
      acceleration: 0.519
      command: 0.65
    }
    calibration {
      speed: 5.2
      acceleration: 0.522
      command: 0.7
    }
    calibration {
      speed: 5.2
      acceleration: 0.795
      command: 0.75
    }
    calibration {
      speed: 5.2
      acceleration: 1.152
      command: 0.8
    }
    calibration {
      speed: 5.2
      acceleration: 2.471
      command: 0.85
    }
    calibration {
      speed: 5.4
      acceleration: -2.68
    }
    calibration {
      speed: 5.4
      acceleration: -2.452
      command: 0.05
    }
    calibration {
      speed: 5.4
      acceleration: -2.219
      command: 0.1
    }
    calibration {
      speed: 5.4
      acceleration: -1.914
      command: 0.15
    }
    calibration {
      speed: 5.4
      acceleration: -1.602
      command: 0.2
    }
    calibration {
      speed: 5.4
      acceleration: -1.338
      command: 0.25
    }
    calibration {
      speed: 5.4
      acceleration: -0.998
      command: 0.3
    }
    calibration {
      speed: 5.4
      acceleration: -0.659
      command: 0.35
    }
    calibration {
      speed: 5.4
      acceleration: -0.394
      command: 0.4
    }
    calibration {
      speed: 5.4
      acceleration: -0.057
      command: 0.45
    }
    calibration {
      speed: 5.4
      acceleration: 0.162
      command: 0.5
    }
    calibration {
      speed: 5.4
      acceleration: 0.422
      command: 0.6
    }
    calibration {
      speed: 5.4
      acceleration: 0.426
      command: 0.65
    }
    calibration {
      speed: 5.4
      acceleration: 0.519
      command: 0.7
    }
    calibration {
      speed: 5.4
      acceleration: 0.554
      command: 0.75
    }
    calibration {
      speed: 5.4
      acceleration: 0.682
      command: 0.55
    }
    calibration {
      speed: 5.4
      acceleration: 0.77
      command: 0.8
    }
    calibration {
      speed: 5.4
      acceleration: 1.893
      command: 0.85
    }
    calibration {
      speed: 5.6
      acceleration: -2.702
    }
    calibration {
      speed: 5.6
      acceleration: -2.463
      command: 0.05
    }
    calibration {
      speed: 5.6
      acceleration: -2.231
      command: 0.1
    }
    calibration {
      speed: 5.6
      acceleration: -1.875
      command: 0.15
    }
    calibration {
      speed: 5.6
      acceleration: -1.569
      command: 0.2
    }
    calibration {
      speed: 5.6
      acceleration: -1.313
      command: 0.25
    }
    calibration {
      speed: 5.6
      acceleration: -0.994
      command: 0.3
    }
    calibration {
      speed: 5.6
      acceleration: -0.733
      command: 0.35
    }
    calibration {
      speed: 5.6
      acceleration: -0.402
      command: 0.4
    }
    calibration {
      speed: 5.6
      acceleration: -0.074
      command: 0.45
    }
    calibration {
      speed: 5.6
      acceleration: 0.13
      command: 0.5
    }
    calibration {
      speed: 5.6
      acceleration: 0.691
      command: 0.55
    }
    calibration {
      speed: 5.6
      acceleration: 0.724
      command: 0.65
    }
    calibration {
      speed: 5.6
      acceleration: 0.788
      command: 0.775
    }
    calibration {
      speed: 5.6
      acceleration: 0.937
      command: 0.7
    }
    calibration {
      speed: 5.6
      acceleration: 0.97
      command: 0.6
    }
    calibration {
      speed: 5.6
      acceleration: 1.583
      command: 0.85
    }
    calibration {
      speed: 5.8
      acceleration: -2.695
    }
    calibration {
      speed: 5.8
      acceleration: -2.434
      command: 0.05
    }
    calibration {
      speed: 5.8
      acceleration: -2.186
      command: 0.1
    }
    calibration {
      speed: 5.8
      acceleration: -1.851
      command: 0.15
    }
    calibration {
      speed: 5.8
      acceleration: -1.565
      command: 0.2
    }
    calibration {
      speed: 5.8
      acceleration: -1.317
      command: 0.25
    }
    calibration {
      speed: 5.8
      acceleration: -1.049
      command: 0.3
    }
    calibration {
      speed: 5.8
      acceleration: -0.747
      command: 0.35
    }
    calibration {
      speed: 5.8
      acceleration: -0.422
      command: 0.4
    }
    calibration {
      speed: 5.8
      acceleration: -0.092
      command: 0.45
    }
    calibration {
      speed: 5.8
      acceleration: 0.169
      command: 0.5
    }
    calibration {
      speed: 5.8
      acceleration: 0.632
      command: 0.55
    }
    calibration {
      speed: 5.8
      acceleration: 1.034
      command: 0.75
    }
    calibration {
      speed: 5.8
      acceleration: 1.076
      command: 0.65
    }
    calibration {
      speed: 5.8
      acceleration: 1.156
      command: 0.6
    }
    calibration {
      speed: 5.8
      acceleration: 1.168
      command: 0.8
    }
    calibration {
      speed: 5.8
      acceleration: 1.268
      command: 0.7
    }
    calibration {
      speed: 5.8
      acceleration: 1.449
      command: 0.85
    }
    calibration {
      speed: 6.0
      acceleration: -2.693
    }
    calibration {
      speed: 6.0
      acceleration: -2.422
      command: 0.05
    }
    calibration {
      speed: 6.0
      acceleration: -2.146
      command: 0.1
    }
    calibration {
      speed: 6.0
      acceleration: -1.879
      command: 0.15
    }
    calibration {
      speed: 6.0
      acceleration: -1.602
      command: 0.2
    }
    calibration {
      speed: 6.0
      acceleration: -1.36
      command: 0.25
    }
    calibration {
      speed: 6.0
      acceleration: -1.076
      command: 0.3
    }
    calibration {
      speed: 6.0
      acceleration: -0.752
      command: 0.35
    }
    calibration {
      speed: 6.0
      acceleration: -0.446
      command: 0.4
    }
    calibration {
      speed: 6.0
      acceleration: -0.111
      command: 0.45
    }
    calibration {
      speed: 6.0
      acceleration: 0.188
      command: 0.5
    }
    calibration {
      speed: 6.0
      acceleration: 0.652
      command: 0.55
    }
    calibration {
      speed: 6.0
      acceleration: 1.005
      command: 0.6
    }
    calibration {
      speed: 6.0
      acceleration: 1.341
      command: 0.65
    }
    calibration {
      speed: 6.0
      acceleration: 1.508
      command: 0.85
    }
    calibration {
      speed: 6.0
      acceleration: 1.535
      command: 0.75
    }
    calibration {
      speed: 6.0
      acceleration: 1.582
      command: 0.8
    }
    calibration {
      speed: 6.0
      acceleration: 1.73
      command: 0.7
    }
    calibration {
      speed: 6.2
      acceleration: -2.697
    }
    calibration {
      speed: 6.2
      acceleration: -2.43
      command: 0.05
    }
    calibration {
      speed: 6.2
      acceleration: -2.161
      command: 0.1
    }
    calibration {
      speed: 6.2
      acceleration: -1.919
      command: 0.15
    }
    calibration {
      speed: 6.2
      acceleration: -1.632
      command: 0.2
    }
    calibration {
      speed: 6.2
      acceleration: -1.401
      command: 0.25
    }
    calibration {
      speed: 6.2
      acceleration: -1.096
      command: 0.3
    }
    calibration {
      speed: 6.2
      acceleration: -0.79
      command: 0.35
    }
    calibration {
      speed: 6.2
      acceleration: -0.466
      command: 0.4
    }
    calibration {
      speed: 6.2
      acceleration: -0.131
      command: 0.45
    }
    calibration {
      speed: 6.2
      acceleration: 0.184
      command: 0.5
    }
    calibration {
      speed: 6.2
      acceleration: 0.551
      command: 0.55
    }
    calibration {
      speed: 6.2
      acceleration: 0.893
      command: 0.6
    }
    calibration {
      speed: 6.2
      acceleration: 1.373
      command: 0.65
    }
    calibration {
      speed: 6.2
      acceleration: 1.726
      command: 0.75
    }
    calibration {
      speed: 6.2
      acceleration: 1.81
      command: 0.7
    }
    calibration {
      speed: 6.2
      acceleration: 1.91
      command: 0.8
    }
    calibration {
      speed: 6.2
      acceleration: 1.928
      command: 0.85
    }
    calibration {
      speed: 6.4
      acceleration: -2.74
    }
    calibration {
      speed: 6.4
      acceleration: -2.491
      command: 0.05
    }
    calibration {
      speed: 6.4
      acceleration: -2.226
      command: 0.1
    }
    calibration {
      speed: 6.4
      acceleration: -2.012
      command: 0.15
    }
    calibration {
      speed: 6.4
      acceleration: -1.649
      command: 0.2
    }
    calibration {
      speed: 6.4
      acceleration: -1.429
      command: 0.25
    }
    calibration {
      speed: 6.4
      acceleration: -1.121
      command: 0.3
    }
    calibration {
      speed: 6.4
      acceleration: -0.808
      command: 0.35
    }
    calibration {
      speed: 6.4
      acceleration: -0.485
      command: 0.4
    }
    calibration {
      speed: 6.4
      acceleration: -0.151
      command: 0.45
    }
    calibration {
      speed: 6.4
      acceleration: 0.167
      command: 0.5
    }
    calibration {
      speed: 6.4
      acceleration: 0.6
      command: 0.55
    }
    calibration {
      speed: 6.4
      acceleration: 0.92
      command: 0.6
    }
    calibration {
      speed: 6.4
      acceleration: 1.326
      command: 0.65
    }
    calibration {
      speed: 6.4
      acceleration: 1.701
      command: 0.7
    }
    calibration {
      speed: 6.4
      acceleration: 1.899
      command: 0.75
    }
    calibration {
      speed: 6.4
      acceleration: 2.306
      command: 0.85
    }
    calibration {
      speed: 6.4
      acceleration: 2.392
      command: 0.8
    }
    calibration {
      speed: 6.6
      acceleration: -2.789
    }
    calibration {
      speed: 6.6
      acceleration: -2.553
      command: 0.05
    }
    calibration {
      speed: 6.6
      acceleration: -2.283
      command: 0.1
    }
    calibration {
      speed: 6.6
      acceleration: -2.049
      command: 0.15
    }
    calibration {
      speed: 6.6
      acceleration: -1.682
      command: 0.2
    }
    calibration {
      speed: 6.6
      acceleration: -1.458
      command: 0.25
    }
    calibration {
      speed: 6.6
      acceleration: -1.142
      command: 0.3
    }
    calibration {
      speed: 6.6
      acceleration: -0.83
      command: 0.35
    }
    calibration {
      speed: 6.6
      acceleration: -0.507
      command: 0.4
    }
    calibration {
      speed: 6.6
      acceleration: -0.174
      command: 0.45
    }
    calibration {
      speed: 6.6
      acceleration: 0.112
      command: 0.5
    }
    calibration {
      speed: 6.6
      acceleration: 0.526
      command: 0.55
    }
    calibration {
      speed: 6.6
      acceleration: 0.929
      command: 0.6
    }
    calibration {
      speed: 6.6
      acceleration: 1.306
      command: 0.65
    }
    calibration {
      speed: 6.6
      acceleration: 1.627
      command: 0.7
    }
    calibration {
      speed: 6.6
      acceleration: 2.242
      command: 0.75
    }
    calibration {
      speed: 6.6
      acceleration: 2.392
      command: 0.8
    }
    calibration {
      speed: 6.6
      acceleration: 2.491
      command: 0.85
    }
    calibration {
      speed: 6.8
      acceleration: -2.846
    }
    calibration {
      speed: 6.8
      acceleration: -2.587
      command: 0.05
    }
    calibration {
      speed: 6.8
      acceleration: -2.338
      command: 0.1
    }
    calibration {
      speed: 6.8
      acceleration: -2.077
      command: 0.15
    }
    calibration {
      speed: 6.8
      acceleration: -1.73
      command: 0.2
    }
    calibration {
      speed: 6.8
      acceleration: -1.485
      command: 0.25
    }
    calibration {
      speed: 6.8
      acceleration: -1.174
      command: 0.3
    }
    calibration {
      speed: 6.8
      acceleration: -0.857
      command: 0.35
    }
    calibration {
      speed: 6.8
      acceleration: -0.53
      command: 0.4
    }
    calibration {
      speed: 6.8
      acceleration: -0.195
      command: 0.45
    }
    calibration {
      speed: 6.8
      acceleration: 0.118
      command: 0.5
    }
    calibration {
      speed: 6.8
      acceleration: 0.465
      command: 0.55
    }
    calibration {
      speed: 6.8
      acceleration: 0.905
      command: 0.6
    }
    calibration {
      speed: 6.8
      acceleration: 1.314
      command: 0.65
    }
    calibration {
      speed: 6.8
      acceleration: 1.579
      command: 0.7
    }
    calibration {
      speed: 6.8
      acceleration: 2.302
      command: 0.75
    }
    calibration {
      speed: 6.8
      acceleration: 2.734
      command: 0.8
    }
    calibration {
      speed: 6.8
      acceleration: 2.918
      command: 0.85
    }
    calibration {
      speed: 7.0
      acceleration: -2.876
    }
    calibration {
      speed: 7.0
      acceleration: -2.653
      command: 0.05
    }
    calibration {
      speed: 7.0
      acceleration: -2.387
      command: 0.1
    }
    calibration {
      speed: 7.0
      acceleration: -2.096
      command: 0.15
    }
    calibration {
      speed: 7.0
      acceleration: -1.806
      command: 0.2
    }
    calibration {
      speed: 7.0
      acceleration: -1.511
      command: 0.25
    }
    calibration {
      speed: 7.0
      acceleration: -1.19
      command: 0.3
    }
    calibration {
      speed: 7.0
      acceleration: -0.875
      command: 0.35
    }
    calibration {
      speed: 7.0
      acceleration: -0.555
      command: 0.4
    }
    calibration {
      speed: 7.0
      acceleration: -0.217
      command: 0.45
    }
    calibration {
      speed: 7.0
      acceleration: 0.141
      command: 0.5
    }
    calibration {
      speed: 7.0
      acceleration: 0.54
      command: 0.55
    }
    calibration {
      speed: 7.0
      acceleration: 0.873
      command: 0.6
    }
    calibration {
      speed: 7.0
      acceleration: 1.281
      command: 0.65
    }
    calibration {
      speed: 7.0
      acceleration: 1.585
      command: 0.7
    }
    calibration {
      speed: 7.0
      acceleration: 2.252
      command: 0.75
    }
    calibration {
      speed: 7.0
      acceleration: 2.869
      command: 0.8
    }
    calibration {
      speed: 7.0
      acceleration: 3.002
      command: 0.85
    }
    calibration {
      speed: 7.2
      acceleration: -2.963
    }
    calibration {
      speed: 7.2
      acceleration: -2.712
      command: 0.05
    }
    calibration {
      speed: 7.2
      acceleration: -2.43
      command: 0.1
    }
    calibration {
      speed: 7.2
      acceleration: -2.112
      command: 0.15
    }
    calibration {
      speed: 7.2
      acceleration: -1.85
      command: 0.2
    }
    calibration {
      speed: 7.2
      acceleration: -1.531
      command: 0.25
    }
    calibration {
      speed: 7.2
      acceleration: -1.225
      command: 0.3
    }
    calibration {
      speed: 7.2
      acceleration: -0.905
      command: 0.35
    }
    calibration {
      speed: 7.2
      acceleration: -0.579
      command: 0.4
    }
    calibration {
      speed: 7.2
      acceleration: -0.24
      command: 0.45
    }
    calibration {
      speed: 7.2
      acceleration: 0.042
      command: 0.5
    }
    calibration {
      speed: 7.2
      acceleration: 0.519
      command: 0.55
    }
    calibration {
      speed: 7.2
      acceleration: 0.847
      command: 0.6
    }
    calibration {
      speed: 7.2
      acceleration: 1.224
      command: 0.65
    }
    calibration {
      speed: 7.2
      acceleration: 1.642
      command: 0.7
    }
    calibration {
      speed: 7.2
      acceleration: 2.139
      command: 0.75
    }
    calibration {
      speed: 7.2
      acceleration: 2.804
      command: 0.8
    }
    calibration {
      speed: 7.2
      acceleration: 3.081
      command: 0.85
    }
    calibration {
      speed: 7.4
      acceleration: -2.99
    }
    calibration {
      speed: 7.4
      acceleration: -2.738
      command: 0.05
    }
    calibration {
      speed: 7.4
      acceleration: -2.468
      command: 0.1
    }
    calibration {
      speed: 7.4
      acceleration: -2.139
      command: 0.15
    }
    calibration {
      speed: 7.4
      acceleration: -1.87
      command: 0.2
    }
    calibration {
      speed: 7.4
      acceleration: -1.563
      command: 0.25
    }
    calibration {
      speed: 7.4
      acceleration: -1.249
      command: 0.3
    }
    calibration {
      speed: 7.4
      acceleration: -0.936
      command: 0.35
    }
    calibration {
      speed: 7.4
      acceleration: -0.602
      command: 0.4
    }
    calibration {
      speed: 7.4
      acceleration: -0.267
      command: 0.45
    }
    calibration {
      speed: 7.4
      acceleration: 0.063
      command: 0.5
    }
    calibration {
      speed: 7.4
      acceleration: 0.465
      command: 0.55
    }
    calibration {
      speed: 7.4
      acceleration: 0.839
      command: 0.6
    }
    calibration {
      speed: 7.4
      acceleration: 1.171
      command: 0.65
    }
    calibration {
      speed: 7.4
      acceleration: 1.675
      command: 0.7
    }
    calibration {
      speed: 7.4
      acceleration: 1.964
      command: 0.75
    }
    calibration {
      speed: 7.4
      acceleration: 2.614
      command: 0.8
    }
    calibration {
      speed: 7.4
      acceleration: 3.062
      command: 0.85
    }
    calibration {
      speed: 7.6
      acceleration: -3.065
    }
    calibration {
      speed: 7.6
      acceleration: -2.805
      command: 0.05
    }
    calibration {
      speed: 7.6
      acceleration: -2.487
      command: 0.1
    }
    calibration {
      speed: 7.6
      acceleration: -2.177
      command: 0.15
    }
    calibration {
      speed: 7.6
      acceleration: -1.874
      command: 0.2
    }
    calibration {
      speed: 7.6
      acceleration: -1.592
      command: 0.25
    }
    calibration {
      speed: 7.6
      acceleration: -1.277
      command: 0.3
    }
    calibration {
      speed: 7.6
      acceleration: -0.964
      command: 0.35
    }
    calibration {
      speed: 7.6
      acceleration: -0.634
      command: 0.4
    }
    calibration {
      speed: 7.6
      acceleration: -0.29
      command: 0.45
    }
    calibration {
      speed: 7.6
      acceleration: 0.021
      command: 0.5
    }
    calibration {
      speed: 7.6
      acceleration: 0.436
      command: 0.55
    }
    calibration {
      speed: 7.6
      acceleration: 0.827
      command: 0.6
    }
    calibration {
      speed: 7.6
      acceleration: 1.159
      command: 0.65
    }
    calibration {
      speed: 7.6
      acceleration: 1.69
      command: 0.7
    }
    calibration {
      speed: 7.6
      acceleration: 1.897
      command: 0.75
    }
    calibration {
      speed: 7.6
      acceleration: 2.404
      command: 0.8
    }
    calibration {
      speed: 7.6
      acceleration: 3.029
      command: 0.85
    }
    calibration {
      speed: 7.8
      acceleration: -3.114
    }
    calibration {
      speed: 7.8
      acceleration: -2.815
      command: 0.05
    }
    calibration {
      speed: 7.8
      acceleration: -2.547
      command: 0.1
    }
    calibration {
      speed: 7.8
      acceleration: -2.229
      command: 0.15
    }
    calibration {
      speed: 7.8
      acceleration: -1.874
      command: 0.2
    }
    calibration {
      speed: 7.8
      acceleration: -1.634
      command: 0.25
    }
    calibration {
      speed: 7.8
      acceleration: -1.32
      command: 0.3
    }
    calibration {
      speed: 7.8
      acceleration: -0.987
      command: 0.35
    }
    calibration {
      speed: 7.8
      acceleration: -0.661
      command: 0.4
    }
    calibration {
      speed: 7.8
      acceleration: -0.314
      command: 0.45
    }
    calibration {
      speed: 7.8
      acceleration: 0.077
      command: 0.5
    }
    calibration {
      speed: 7.8
      acceleration: 0.396
      command: 0.55
    }
    calibration {
      speed: 7.8
      acceleration: 0.819
      command: 0.6
    }
    calibration {
      speed: 7.8
      acceleration: 1.161
      command: 0.65
    }
    calibration {
      speed: 7.8
      acceleration: 1.659
      command: 0.7
    }
    calibration {
      speed: 7.8
      acceleration: 1.881
      command: 0.75
    }
    calibration {
      speed: 7.8
      acceleration: 2.179
      command: 0.8
    }
    calibration {
      speed: 7.8
      acceleration: 2.896
      command: 0.85
    }
    calibration {
      speed: 8.0
      acceleration: -3.138
    }
    calibration {
      speed: 8.0
      acceleration: -2.865
      command: 0.05
    }
    calibration {
      speed: 8.0
      acceleration: -2.591
      command: 0.1
    }
    calibration {
      speed: 8.0
      acceleration: -2.259
      command: 0.15
    }
    calibration {
      speed: 8.0
      acceleration: -1.893
      command: 0.2
    }
    calibration {
      speed: 8.0
      acceleration: -1.678
      command: 0.25
    }
    calibration {
      speed: 8.0
      acceleration: -1.358
      command: 0.3
    }
    calibration {
      speed: 8.0
      acceleration: -1.016
      command: 0.35
    }
    calibration {
      speed: 8.0
      acceleration: -0.69
      command: 0.4
    }
    calibration {
      speed: 8.0
      acceleration: -0.34
      command: 0.45
    }
    calibration {
      speed: 8.0
      acceleration: 0.038
      command: 0.5
    }
    calibration {
      speed: 8.0
      acceleration: 0.441
      command: 0.55
    }
    calibration {
      speed: 8.0
      acceleration: 0.833
      command: 0.6
    }
    calibration {
      speed: 8.0
      acceleration: 1.158
      command: 0.65
    }
    calibration {
      speed: 8.0
      acceleration: 1.6
      command: 0.7
    }
    calibration {
      speed: 8.0
      acceleration: 1.904
      command: 0.75
    }
    calibration {
      speed: 8.0
      acceleration: 2.143
      command: 0.8
    }
    calibration {
      speed: 8.0
      acceleration: 2.855
      command: 0.85
    }
    calibration {
      speed: 8.2
      acceleration: -3.188
    }
    calibration {
      speed: 8.2
      acceleration: -2.886
      command: 0.05
    }
    calibration {
      speed: 8.2
      acceleration: -2.615
      command: 0.1
    }
    calibration {
      speed: 8.2
      acceleration: -2.29
      command: 0.15
    }
    calibration {
      speed: 8.2
      acceleration: -1.918
      command: 0.2
    }
    calibration {
      speed: 8.2
      acceleration: -1.705
      command: 0.25
    }
    calibration {
      speed: 8.2
      acceleration: -1.387
      command: 0.3
    }
    calibration {
      speed: 8.2
      acceleration: -1.046
      command: 0.35
    }
    calibration {
      speed: 8.2
      acceleration: -0.719
      command: 0.4
    }
    calibration {
      speed: 8.2
      acceleration: -0.368
      command: 0.45
    }
    calibration {
      speed: 8.2
      acceleration: -0.007
      command: 0.5
    }
    calibration {
      speed: 8.2
      acceleration: 0.365
      command: 0.55
    }
    calibration {
      speed: 8.2
      acceleration: 0.783
      command: 0.6
    }
    calibration {
      speed: 8.2
      acceleration: 1.141
      command: 0.65
    }
    calibration {
      speed: 8.2
      acceleration: 1.56
      command: 0.7
    }
    calibration {
      speed: 8.2
      acceleration: 1.969
      command: 0.75
    }
    calibration {
      speed: 8.2
      acceleration: 2.151
      command: 0.8
    }
    calibration {
      speed: 8.2
      acceleration: 2.768
      command: 0.85
    }
    calibration {
      speed: 8.4
      acceleration: -3.241
    }
    calibration {
      speed: 8.4
      acceleration: -2.945
      command: 0.05
    }
    calibration {
      speed: 8.4
      acceleration: -2.65
      command: 0.1
    }
    calibration {
      speed: 8.4
      acceleration: -2.367
      command: 0.15
    }
    calibration {
      speed: 8.4
      acceleration: -1.955
      command: 0.2
    }
    calibration {
      speed: 8.4
      acceleration: -1.747
      command: 0.25
    }
    calibration {
      speed: 8.4
      acceleration: -1.411
      command: 0.3
    }
    calibration {
      speed: 8.4
      acceleration: -1.07
      command: 0.35
    }
    calibration {
      speed: 8.4
      acceleration: -0.742
      command: 0.4
    }
    calibration {
      speed: 8.4
      acceleration: -0.391
      command: 0.45
    }
    calibration {
      speed: 8.4
      acceleration: -0.033
      command: 0.5
    }
    calibration {
      speed: 8.4
      acceleration: 0.385
      command: 0.55
    }
    calibration {
      speed: 8.4
      acceleration: 0.683
      command: 0.6
    }
    calibration {
      speed: 8.4
      acceleration: 1.136
      command: 0.65
    }
    calibration {
      speed: 8.4
      acceleration: 1.506
      command: 0.7
    }
    calibration {
      speed: 8.4
      acceleration: 1.958
      command: 0.75
    }
    calibration {
      speed: 8.4
      acceleration: 2.217
      command: 0.8
    }
    calibration {
      speed: 8.4
      acceleration: 2.752
      command: 0.85
    }
    calibration {
      speed: 8.6
      acceleration: -3.269
    }
    calibration {
      speed: 8.6
      acceleration: -2.971
      command: 0.05
    }
    calibration {
      speed: 8.6
      acceleration: -2.697
      command: 0.1
    }
    calibration {
      speed: 8.6
      acceleration: -2.399
      command: 0.15
    }
    calibration {
      speed: 8.6
      acceleration: -2.027
      command: 0.2
    }
    calibration {
      speed: 8.6
      acceleration: -1.776
      command: 0.25
    }
    calibration {
      speed: 8.6
      acceleration: -1.44
      command: 0.3
    }
    calibration {
      speed: 8.6
      acceleration: -1.11
      command: 0.35
    }
    calibration {
      speed: 8.6
      acceleration: -0.774
      command: 0.4
    }
    calibration {
      speed: 8.6
      acceleration: -0.418
      command: 0.45
    }
    calibration {
      speed: 8.6
      acceleration: -0.059
      command: 0.5
    }
    calibration {
      speed: 8.6
      acceleration: 0.375
      command: 0.55
    }
    calibration {
      speed: 8.6
      acceleration: 0.673
      command: 0.6
    }
    calibration {
      speed: 8.6
      acceleration: 1.129
      command: 0.65
    }
    calibration {
      speed: 8.6
      acceleration: 1.49
      command: 0.7
    }
    calibration {
      speed: 8.6
      acceleration: 1.902
      command: 0.75
    }
    calibration {
      speed: 8.6
      acceleration: 2.256
      command: 0.8
    }
    calibration {
      speed: 8.6
      acceleration: 2.731
      command: 0.85
    }
    calibration {
      speed: 8.8
      acceleration: -3.329
    }
    calibration {
      speed: 8.8
      acceleration: -3.0
      command: 0.05
    }
    calibration {
      speed: 8.8
      acceleration: -2.719
      command: 0.1
    }
    calibration {
      speed: 8.8
      acceleration: -2.462
      command: 0.15
    }
    calibration {
      speed: 8.8
      acceleration: -2.111
      command: 0.2
    }
    calibration {
      speed: 8.8
      acceleration: -1.806
      command: 0.25
    }
    calibration {
      speed: 8.8
      acceleration: -1.483
      command: 0.3
    }
    calibration {
      speed: 8.8
      acceleration: -1.148
      command: 0.35
    }
    calibration {
      speed: 8.8
      acceleration: -0.796
      command: 0.4
    }
    calibration {
      speed: 8.8
      acceleration: -0.45
      command: 0.45
    }
    calibration {
      speed: 8.8
      acceleration: -0.085
      command: 0.5
    }
    calibration {
      speed: 8.8
      acceleration: 0.331
      command: 0.55
    }
    calibration {
      speed: 8.8
      acceleration: 0.758
      command: 0.6
    }
    calibration {
      speed: 8.8
      acceleration: 1.096
      command: 0.65
    }
    calibration {
      speed: 8.8
      acceleration: 1.485
      command: 0.7
    }
    calibration {
      speed: 8.8
      acceleration: 1.818
      command: 0.75
    }
    calibration {
      speed: 8.8
      acceleration: 2.326
      command: 0.8
    }
    calibration {
      speed: 8.8
      acceleration: 2.727
      command: 0.85
    }
    calibration {
      speed: 9.0
      acceleration: -3.361
    }
    calibration {
      speed: 9.0
      acceleration: -3.062
      command: 0.05
    }
    calibration {
      speed: 9.0
      acceleration: -2.763
      command: 0.1
    }
    calibration {
      speed: 9.0
      acceleration: -2.483
      command: 0.15
    }
    calibration {
      speed: 9.0
      acceleration: -2.195
      command: 0.2
    }
    calibration {
      speed: 9.0
      acceleration: -1.853
      command: 0.25
    }
    calibration {
      speed: 9.0
      acceleration: -1.513
      command: 0.3
    }
    calibration {
      speed: 9.0
      acceleration: -1.173
      command: 0.35
    }
    calibration {
      speed: 9.0
      acceleration: -0.822
      command: 0.4
    }
    calibration {
      speed: 9.0
      acceleration: -0.479
      command: 0.45
    }
    calibration {
      speed: 9.0
      acceleration: -0.112
      command: 0.5
    }
    calibration {
      speed: 9.0
      acceleration: 0.219
      command: 0.55
    }
    calibration {
      speed: 9.0
      acceleration: 0.696
      command: 0.6
    }
    calibration {
      speed: 9.0
      acceleration: 1.045
      command: 0.65
    }
    calibration {
      speed: 9.0
      acceleration: 1.472
      command: 0.7
    }
    calibration {
      speed: 9.0
      acceleration: 1.777
      command: 0.75
    }
    calibration {
      speed: 9.0
      acceleration: 2.367
      command: 0.8
    }
    calibration {
      speed: 9.0
      acceleration: 2.719
      command: 0.85
    }
    calibration {
      speed: 9.2
      acceleration: -3.394
    }
    calibration {
      speed: 9.2
      acceleration: -3.132
      command: 0.05
    }
    calibration {
      speed: 9.2
      acceleration: -2.784
      command: 0.1
    }
    calibration {
      speed: 9.2
      acceleration: -2.522
      command: 0.15
    }
    calibration {
      speed: 9.2
      acceleration: -2.245
      command: 0.2
    }
    calibration {
      speed: 9.2
      acceleration: -1.886
      command: 0.25
    }
    calibration {
      speed: 9.2
      acceleration: -1.552
      command: 0.3
    }
    calibration {
      speed: 9.2
      acceleration: -1.209
      command: 0.35
    }
    calibration {
      speed: 9.2
      acceleration: -0.863
      command: 0.4
    }
    calibration {
      speed: 9.2
      acceleration: -0.506
      command: 0.45
    }
    calibration {
      speed: 9.2
      acceleration: -0.141
      command: 0.5
    }
    calibration {
      speed: 9.2
      acceleration: 0.244
      command: 0.55
    }
    calibration {
      speed: 9.2
      acceleration: 0.55
      command: 0.6
    }
    calibration {
      speed: 9.2
      acceleration: 1.002
      command: 0.65
    }
    calibration {
      speed: 9.2
      acceleration: 1.436
      command: 0.7
    }
    calibration {
      speed: 9.2
      acceleration: 1.759
      command: 0.75
    }
    calibration {
      speed: 9.2
      acceleration: 2.362
      command: 0.8
    }
    calibration {
      speed: 9.2
      acceleration: 2.713
      command: 0.85
    }
    calibration {
      speed: 9.4
      acceleration: -3.428
    }
    calibration {
      speed: 9.4
      acceleration: -3.209
      command: 0.05
    }
    calibration {
      speed: 9.4
      acceleration: -2.847
      command: 0.1
    }
    calibration {
      speed: 9.4
      acceleration: -2.562
      command: 0.15
    }
    calibration {
      speed: 9.4
      acceleration: -2.309
      command: 0.2
    }
    calibration {
      speed: 9.4
      acceleration: -1.919
      command: 0.25
    }
    calibration {
      speed: 9.4
      acceleration: -1.607
      command: 0.3
    }
    calibration {
      speed: 9.4
      acceleration: -1.244
      command: 0.35
    }
    calibration {
      speed: 9.4
      acceleration: -0.895
      command: 0.4
    }
    calibration {
      speed: 9.4
      acceleration: -0.535
      command: 0.45
    }
    calibration {
      speed: 9.4
      acceleration: -0.169
      command: 0.5
    }
    calibration {
      speed: 9.4
      acceleration: 0.255
      command: 0.55
    }
    calibration {
      speed: 9.4
      acceleration: 0.548
      command: 0.6
    }
    calibration {
      speed: 9.4
      acceleration: 0.979
      command: 0.65
    }
    calibration {
      speed: 9.4
      acceleration: 1.408
      command: 0.7
    }
    calibration {
      speed: 9.4
      acceleration: 1.763
      command: 0.75
    }
    calibration {
      speed: 9.4
      acceleration: 2.325
      command: 0.8
    }
    calibration {
      speed: 9.4
      acceleration: 2.687
      command: 0.85
    }
    calibration {
      speed: 9.6
      acceleration: -3.542
    }
    calibration {
      speed: 9.6
      acceleration: -3.249
      command: 0.05
    }
    calibration {
      speed: 9.6
      acceleration: -2.869
      command: 0.1
    }
    calibration {
      speed: 9.6
      acceleration: -2.582
      command: 0.15
    }
    calibration {
      speed: 9.6
      acceleration: -2.341
      command: 0.2
    }
    calibration {
      speed: 9.6
      acceleration: -1.953
      command: 0.25
    }
    calibration {
      speed: 9.6
      acceleration: -1.628
      command: 0.3
    }
    calibration {
      speed: 9.6
      acceleration: -1.288
      command: 0.35
    }
    calibration {
      speed: 9.6
      acceleration: -0.923
      command: 0.4
    }
    calibration {
      speed: 9.6
      acceleration: -0.573
      command: 0.45
    }
    calibration {
      speed: 9.6
      acceleration: -0.197
      command: 0.5
    }
    calibration {
      speed: 9.6
      acceleration: 0.187
      command: 0.55
    }
    calibration {
      speed: 9.6
      acceleration: 0.539
      command: 0.6
    }
    calibration {
      speed: 9.6
      acceleration: 0.979
      command: 0.65
    }
    calibration {
      speed: 9.6
      acceleration: 1.39
      command: 0.7
    }
    calibration {
      speed: 9.6
      acceleration: 1.808
      command: 0.75
    }
    calibration {
      speed: 9.6
      acceleration: 2.301
      command: 0.8
    }
    calibration {
      speed: 9.6
      acceleration: 2.677
      command: 0.85
    }
    calibration {
      speed: 9.8
      acceleration: -3.583
    }
    calibration {
      speed: 9.8
      acceleration: -3.291
      command: 0.05
    }
    calibration {
      speed: 9.8
      acceleration: -2.944
      command: 0.1
    }
    calibration {
      speed: 9.8
      acceleration: -2.624
      command: 0.15
    }
    calibration {
      speed: 9.8
      acceleration: -2.355
      command: 0.2
    }
    calibration {
      speed: 9.8
      acceleration: -2.023
      command: 0.25
    }
    calibration {
      speed: 9.8
      acceleration: -1.667
      command: 0.3
    }
    calibration {
      speed: 9.8
      acceleration: -1.311
      command: 0.35
    }
    calibration {
      speed: 9.8
      acceleration: -0.952
      command: 0.4
    }
    calibration {
      speed: 9.8
      acceleration: -0.599
      command: 0.45
    }
    calibration {
      speed: 9.8
      acceleration: -0.225
      command: 0.5
    }
    calibration {
      speed: 9.8
      acceleration: 0.148
      command: 0.55
    }
    calibration {
      speed: 9.8
      acceleration: 0.497
      command: 0.6
    }
    calibration {
      speed: 9.8
      acceleration: 0.983
      command: 0.65
    }
    calibration {
      speed: 9.8
      acceleration: 1.384
      command: 0.7
    }
    calibration {
      speed: 9.8
      acceleration: 1.825
      command: 0.75
    }
    calibration {
      speed: 9.8
      acceleration: 2.232
      command: 0.8
    }
    calibration {
      speed: 9.8
      acceleration: 2.658
      command: 0.85
    }
    calibration {
      speed: 10.0
      acceleration: -3.626
    }
    calibration {
      speed: 10.0
      acceleration: -3.379
      command: 0.05
    }
    calibration {
      speed: 10.0
      acceleration: -3.003
      command: 0.1
    }
    calibration {
      speed: 10.0
      acceleration: -2.693
      command: 0.15
    }
    calibration {
      speed: 10.0
      acceleration: -2.38
      command: 0.2
    }
    calibration {
      speed: 10.0
      acceleration: -2.059
      command: 0.25
    }
    calibration {
      speed: 10.0
      acceleration: -1.705
      command: 0.3
    }
    calibration {
      speed: 10.0
      acceleration: -1.349
      command: 0.35
    }
    calibration {
      speed: 10.0
      acceleration: -0.984
      command: 0.4
    }
    calibration {
      speed: 10.0
      acceleration: -0.63
      command: 0.45
    }
    calibration {
      speed: 10.0
      acceleration: -0.256
      command: 0.5
    }
    calibration {
      speed: 10.0
      acceleration: 0.15
      command: 0.55
    }
    calibration {
      speed: 10.0
      acceleration: 0.543
      command: 0.6
    }
    calibration {
      speed: 10.0
      acceleration: 0.974
      command: 0.65
    }
    calibration {
      speed: 10.0
      acceleration: 1.378
      command: 0.7
    }
    calibration {
      speed: 10.0
      acceleration: 1.828
      command: 0.75
    }
    calibration {
      speed: 10.0
      acceleration: 2.199
      command: 0.8
    }
    calibration {
      speed: 10.0
      acceleration: 2.649
      command: 0.85
    }
    calibration {
      speed: 10.2
      acceleration: -3.67
    }
    calibration {
      speed: 10.2
      acceleration: -3.425
      command: 0.05
    }
    calibration {
      speed: 10.2
      acceleration: -3.108
      command: 0.1
    }
    calibration {
      speed: 10.2
      acceleration: -2.719
      command: 0.15
    }
    calibration {
      speed: 10.2
      acceleration: -2.402
      command: 0.2
    }
    calibration {
      speed: 10.2
      acceleration: -2.096
      command: 0.25
    }
    calibration {
      speed: 10.2
      acceleration: -1.731
      command: 0.3
    }
    calibration {
      speed: 10.2
      acceleration: -1.38
      command: 0.35
    }
    calibration {
      speed: 10.2
      acceleration: -1.019
      command: 0.4
    }
    calibration {
      speed: 10.2
      acceleration: -0.666
      command: 0.45
    }
    calibration {
      speed: 10.2
      acceleration: -0.287
      command: 0.5
    }
    calibration {
      speed: 10.2
      acceleration: 0.083
      command: 0.55
    }
    calibration {
      speed: 10.2
      acceleration: 0.527
      command: 0.6
    }
    calibration {
      speed: 10.2
      acceleration: 0.948
      command: 0.65
    }
    calibration {
      speed: 10.2
      acceleration: 1.358
      command: 0.7
    }
    calibration {
      speed: 10.2
      acceleration: 1.805
      command: 0.75
    }
    calibration {
      speed: 10.2
      acceleration: 2.178
      command: 0.8
    }
    calibration {
      speed: 10.2
      acceleration: 2.632
      command: 0.85
    }
    calibration {
      speed: 10.4
      acceleration: -3.763
    }
    calibration {
      speed: 10.4
      acceleration: -3.472
      command: 0.05
    }
    calibration {
      speed: 10.4
      acceleration: -3.147
      command: 0.1
    }
    calibration {
      speed: 10.4
      acceleration: -2.805
      command: 0.15
    }
    calibration {
      speed: 10.4
      acceleration: -2.441
      command: 0.2
    }
    calibration {
      speed: 10.4
      acceleration: -2.134
      command: 0.25
    }
    calibration {
      speed: 10.4
      acceleration: -1.773
      command: 0.3
    }
    calibration {
      speed: 10.4
      acceleration: -1.425
      command: 0.35
    }
    calibration {
      speed: 10.4
      acceleration: -1.068
      command: 0.4
    }
    calibration {
      speed: 10.4
      acceleration: -0.701
      command: 0.45
    }
    calibration {
      speed: 10.4
      acceleration: -0.317
      command: 0.5
    }
    calibration {
      speed: 10.4
      acceleration: 0.107
      command: 0.55
    }
    calibration {
      speed: 10.4
      acceleration: 0.456
      command: 0.6
    }
    calibration {
      speed: 10.4
      acceleration: 0.913
      command: 0.65
    }
    calibration {
      speed: 10.4
      acceleration: 1.31
      command: 0.7
    }
    calibration {
      speed: 10.4
      acceleration: 1.777
      command: 0.75
    }
    calibration {
      speed: 10.4
      acceleration: 2.168
      command: 0.8
    }
    calibration {
      speed: 10.4
      acceleration: 2.628
      command: 0.85
    }
    calibration {
      speed: 10.6
      acceleration: -3.811
    }
    calibration {
      speed: 10.6
      acceleration: -3.52
      command: 0.05
    }
    calibration {
      speed: 10.6
      acceleration: -3.188
      command: 0.1
    }
    calibration {
      speed: 10.6
      acceleration: -2.836
      command: 0.15
    }
    calibration {
      speed: 10.6
      acceleration: -2.494
      command: 0.2
    }
    calibration {
      speed: 10.6
      acceleration: -2.174
      command: 0.25
    }
    calibration {
      speed: 10.6
      acceleration: -1.832
      command: 0.3
    }
    calibration {
      speed: 10.6
      acceleration: -1.472
      command: 0.35
    }
    calibration {
      speed: 10.6
      acceleration: -1.101
      command: 0.4
    }
    calibration {
      speed: 10.6
      acceleration: -0.728
      command: 0.45
    }
    calibration {
      speed: 10.6
      acceleration: -0.348
      command: 0.5
    }
    calibration {
      speed: 10.6
      acceleration: 0.027
      command: 0.55
    }
    calibration {
      speed: 10.6
      acceleration: 0.401
      command: 0.6
    }
    calibration {
      speed: 10.6
      acceleration: 0.896
      command: 0.65
    }
    calibration {
      speed: 10.6
      acceleration: 1.271
      command: 0.7
    }
    calibration {
      speed: 10.6
      acceleration: 1.746
      command: 0.75
    }
    calibration {
      speed: 10.6
      acceleration: 2.16
      command: 0.8
    }
    calibration {
      speed: 10.6
      acceleration: 2.62
      command: 0.85
    }
    calibration {
      speed: 10.8
      acceleration: -3.961
    }
    calibration {
      speed: 10.8
      acceleration: -3.67
      command: 0.05
    }
    calibration {
      speed: 10.8
      acceleration: -3.274
      command: 0.1
    }
    calibration {
      speed: 10.8
      acceleration: -2.938
      command: 0.15
    }
    calibration {
      speed: 10.8
      acceleration: -2.516
      command: 0.2
    }
    calibration {
      speed: 10.8
      acceleration: -2.238
      command: 0.25
    }
    calibration {
      speed: 10.8
      acceleration: -1.862
      command: 0.3
    }
    calibration {
      speed: 10.8
      acceleration: -1.507
      command: 0.35
    }
    calibration {
      speed: 10.8
      acceleration: -1.135
      command: 0.4
    }
    calibration {
      speed: 10.8
      acceleration: -0.766
      command: 0.45
    }
    calibration {
      speed: 10.8
      acceleration: -0.383
      command: 0.5
    }
    calibration {
      speed: 10.8
      acceleration: -0.01
      command: 0.55
    }
    calibration {
      speed: 10.8
      acceleration: 0.388
      command: 0.6
    }
    calibration {
      speed: 10.8
      acceleration: 0.872
      command: 0.65
    }
    calibration {
      speed: 10.8
      acceleration: 1.242
      command: 0.7
    }
    calibration {
      speed: 10.8
      acceleration: 1.706
      command: 0.75
    }
    calibration {
      speed: 10.8
      acceleration: 2.153
      command: 0.8
    }
    calibration {
      speed: 10.8
      acceleration: 2.616
      command: 0.85
    }
    calibration {
      speed: 11.0
      acceleration: -4.065
    }
    calibration {
      speed: 11.0
      acceleration: -3.721
      command: 0.05
    }
    calibration {
      speed: 11.0
      acceleration: -3.319
      command: 0.1
    }
    calibration {
      speed: 11.0
      acceleration: -2.975
      command: 0.15
    }
    calibration {
      speed: 11.0
      acceleration: -2.593
      command: 0.2
    }
    calibration {
      speed: 11.0
      acceleration: -2.285
      command: 0.25
    }
    calibration {
      speed: 11.0
      acceleration: -1.905
      command: 0.3
    }
    calibration {
      speed: 11.0
      acceleration: -1.541
      command: 0.35
    }
    calibration {
      speed: 11.0
      acceleration: -1.184
      command: 0.4
    }
    calibration {
      speed: 11.0
      acceleration: -0.802
      command: 0.45
    }
    calibration {
      speed: 11.0
      acceleration: -0.413
      command: 0.5
    }
    calibration {
      speed: 11.0
      acceleration: -0.018
      command: 0.55
    }
    calibration {
      speed: 11.0
      acceleration: 0.419
      command: 0.6
    }
    calibration {
      speed: 11.0
      acceleration: 0.82
      command: 0.65
    }
    calibration {
      speed: 11.0
      acceleration: 1.219
      command: 0.7
    }
    calibration {
      speed: 11.0
      acceleration: 1.677
      command: 0.75
    }
    calibration {
      speed: 11.0
      acceleration: 2.147
      command: 0.8
    }
    calibration {
      speed: 11.0
      acceleration: 2.597
      command: 0.85
    }
    calibration {
      speed: 11.2
      acceleration: -4.172
    }
    calibration {
      speed: 11.2
      acceleration: -3.774
      command: 0.05
    }
    calibration {
      speed: 11.2
      acceleration: -3.365
      command: 0.1
    }
    calibration {
      speed: 11.2
      acceleration: -3.05
      command: 0.15
    }
    calibration {
      speed: 11.2
      acceleration: -2.622
      command: 0.2
    }
    calibration {
      speed: 11.2
      acceleration: -2.309
      command: 0.25
    }
    calibration {
      speed: 11.2
      acceleration: -1.947
      command: 0.3
    }
    calibration {
      speed: 11.2
      acceleration: -1.576
      command: 0.35
    }
    calibration {
      speed: 11.2
      acceleration: -1.2
      command: 0.4
    }
    calibration {
      speed: 11.2
      acceleration: -0.836
      command: 0.45
    }
    calibration {
      speed: 11.2
      acceleration: -0.446
      command: 0.5
    }
    calibration {
      speed: 11.2
      acceleration: -0.049
      command: 0.55
    }
    calibration {
      speed: 11.2
      acceleration: 0.326
      command: 0.6
    }
    calibration {
      speed: 11.2
      acceleration: 0.758
      command: 0.65
    }
    calibration {
      speed: 11.2
      acceleration: 1.206
      command: 0.7
    }
    calibration {
      speed: 11.2
      acceleration: 1.642
      command: 0.75
    }
    calibration {
      speed: 11.2
      acceleration: 2.133
      command: 0.8
    }
    calibration {
      speed: 11.2
      acceleration: 2.588
      command: 0.85
    }
    calibration {
      speed: 11.4
      acceleration: -4.226
    }
    calibration {
      speed: 11.4
      acceleration: -3.881
      command: 0.05
    }
    calibration {
      speed: 11.4
      acceleration: -3.51
      command: 0.1
    }
    calibration {
      speed: 11.4
      acceleration: -3.089
      command: 0.15
    }
    calibration {
      speed: 11.4
      acceleration: -2.711
      command: 0.2
    }
    calibration {
      speed: 11.4
      acceleration: -2.36
      command: 0.25
    }
    calibration {
      speed: 11.4
      acceleration: -1.974
      command: 0.3
    }
    calibration {
      speed: 11.4
      acceleration: -1.624
      command: 0.35
    }
    calibration {
      speed: 11.4
      acceleration: -1.253
      command: 0.4
    }
    calibration {
      speed: 11.4
      acceleration: -0.872
      command: 0.45
    }
    calibration {
      speed: 11.4
      acceleration: -0.478
      command: 0.5
    }
    calibration {
      speed: 11.4
      acceleration: -0.08
      command: 0.55
    }
    calibration {
      speed: 11.4
      acceleration: 0.305
      command: 0.6
    }
    calibration {
      speed: 11.4
      acceleration: 0.743
      command: 0.65
    }
    calibration {
      speed: 11.4
      acceleration: 1.176
      command: 0.7
    }
    calibration {
      speed: 11.4
      acceleration: 1.609
      command: 0.75
    }
    calibration {
      speed: 11.4
      acceleration: 2.105
      command: 0.8
    }
    calibration {
      speed: 11.4
      acceleration: 2.556
      command: 0.85
    }
    calibration {
      speed: 11.6
      acceleration: -4.282
    }
    calibration {
      speed: 11.6
      acceleration: -3.936
      command: 0.05
    }
    calibration {
      speed: 11.6
      acceleration: -3.56
      command: 0.1
    }
    calibration {
      speed: 11.6
      acceleration: -3.128
      command: 0.15
    }
    calibration {
      speed: 11.6
      acceleration: -2.771
      command: 0.2
    }
    calibration {
      speed: 11.6
      acceleration: -2.413
      command: 0.25
    }
    calibration {
      speed: 11.6
      acceleration: -2.018
      command: 0.3
    }
    calibration {
      speed: 11.6
      acceleration: -1.679
      command: 0.35
    }
    calibration {
      speed: 11.6
      acceleration: -1.293
      command: 0.4
    }
    calibration {
      speed: 11.6
      acceleration: -0.9
      command: 0.45
    }
    calibration {
      speed: 11.6
      acceleration: -0.514
      command: 0.5
    }
    calibration {
      speed: 11.6
      acceleration: -0.112
      command: 0.55
    }
    calibration {
      speed: 11.6
      acceleration: 0.301
      command: 0.6
    }
    calibration {
      speed: 11.6
      acceleration: 0.736
      command: 0.65
    }
    calibration {
      speed: 11.6
      acceleration: 1.128
      command: 0.7
    }
    calibration {
      speed: 11.6
      acceleration: 1.567
      command: 0.75
    }
    calibration {
      speed: 11.6
      acceleration: 2.075
      command: 0.8
    }
    calibration {
      speed: 11.6
      acceleration: 2.532
      command: 0.85
    }
    calibration {
      speed: 11.8
      acceleration: -4.338
    }
    calibration {
      speed: 11.8
      acceleration: -3.99
      command: 0.05
    }
    calibration {
      speed: 11.8
      acceleration: -3.664
      command: 0.1
    }
    calibration {
      speed: 11.8
      acceleration: -3.249
      command: 0.15
    }
    calibration {
      speed: 11.8
      acceleration: -2.801
      command: 0.2
    }
    calibration {
      speed: 11.8
      acceleration: -2.469
      command: 0.25
    }
    calibration {
      speed: 11.8
      acceleration: -2.068
      command: 0.3
    }
    calibration {
      speed: 11.8
      acceleration: -1.709
      command: 0.35
    }
    calibration {
      speed: 11.8
      acceleration: -1.332
      command: 0.4
    }
    calibration {
      speed: 11.8
      acceleration: -0.942
      command: 0.45
    }
    calibration {
      speed: 11.8
      acceleration: -0.543
      command: 0.5
    }
    calibration {
      speed: 11.8
      acceleration: -0.144
      command: 0.55
    }
    calibration {
      speed: 11.8
      acceleration: 0.235
      command: 0.6
    }
    calibration {
      speed: 11.8
      acceleration: 0.693
      command: 0.65
    }
    calibration {
      speed: 11.8
      acceleration: 1.084
      command: 0.7
    }
    calibration {
      speed: 11.8
      acceleration: 1.551
      command: 0.75
    }
    calibration {
      speed: 11.8
      acceleration: 2.058
      command: 0.8
    }
    calibration {
      speed: 11.8
      acceleration: 2.52
      command: 0.85
    }
    calibration {
      speed: 12.0
      acceleration: -4.395
    }
    calibration {
      speed: 12.0
      acceleration: -4.045
      command: 0.05
    }
    calibration {
      speed: 12.0
      acceleration: -3.717
      command: 0.1
    }
    calibration {
      speed: 12.0
      acceleration: -3.33
      command: 0.15
    }
    calibration {
      speed: 12.0
      acceleration: -2.891
      command: 0.2
    }
    calibration {
      speed: 12.0
      acceleration: -2.555
      command: 0.25
    }
    calibration {
      speed: 12.0
      acceleration: -2.128
      command: 0.3
    }
    calibration {
      speed: 12.0
      acceleration: -1.76
      command: 0.35
    }
    calibration {
      speed: 12.0
      acceleration: -1.369
      command: 0.4
    }
    calibration {
      speed: 12.0
      acceleration: -0.997
      command: 0.45
    }
    calibration {
      speed: 12.0
      acceleration: -0.585
      command: 0.5
    }
    calibration {
      speed: 12.0
      acceleration: -0.176
      command: 0.55
    }
    calibration {
      speed: 12.0
      acceleration: 0.272
      command: 0.6
    }
    calibration {
      speed: 12.0
      acceleration: 0.656
      command: 0.65
    }
    calibration {
      speed: 12.0
      acceleration: 1.018
      command: 0.7
    }
    calibration {
      speed: 12.0
      acceleration: 1.551
      command: 0.75
    }
    calibration {
      speed: 12.0
      acceleration: 2.034
      command: 0.8
    }
    calibration {
      speed: 12.0
      acceleration: 2.497
      command: 0.85
    }
    calibration {
      speed: 12.2
      acceleration: -4.513
    }
    calibration {
      speed: 12.2
      acceleration: -4.1
      command: 0.05
    }
    calibration {
      speed: 12.2
      acceleration: -3.771
      command: 0.1
    }
    calibration {
      speed: 12.2
      acceleration: -3.371
      command: 0.15
    }
    calibration {
      speed: 12.2
      acceleration: -2.95
      command: 0.2
    }
    calibration {
      speed: 12.2
      acceleration: -2.585
      command: 0.25
    }
    calibration {
      speed: 12.2
      acceleration: -2.197
      command: 0.3
    }
    calibration {
      speed: 12.2
      acceleration: -1.796
      command: 0.35
    }
    calibration {
      speed: 12.2
      acceleration: -1.397
      command: 0.4
    }
    calibration {
      speed: 12.2
      acceleration: -1.022
      command: 0.45
    }
    calibration {
      speed: 12.2
      acceleration: -0.616
      command: 0.5
    }
    calibration {
      speed: 12.2
      acceleration: -0.208
      command: 0.55
    }
    calibration {
      speed: 12.2
      acceleration: 0.217
      command: 0.6
    }
    calibration {
      speed: 12.2
      acceleration: 0.635
      command: 0.65
    }
    calibration {
      speed: 12.2
      acceleration: 1.01
      command: 0.7
    }
    calibration {
      speed: 12.2
      acceleration: 1.553
      command: 0.75
    }
    calibration {
      speed: 12.2
      acceleration: 2.018
      command: 0.8
    }
    calibration {
      speed: 12.2
      acceleration: 2.462
      command: 0.85
    }
    calibration {
      speed: 12.4
      acceleration: -4.573
    }
    calibration {
      speed: 12.4
      acceleration: -4.155
      command: 0.05
    }
    calibration {
      speed: 12.4
      acceleration: -3.882
      command: 0.1
    }
    calibration {
      speed: 12.4
      acceleration: -3.452
      command: 0.15
    }
    calibration {
      speed: 12.4
      acceleration: -3.042
      command: 0.2
    }
    calibration {
      speed: 12.4
      acceleration: -2.645
      command: 0.25
    }
    calibration {
      speed: 12.4
      acceleration: -2.246
      command: 0.3
    }
    calibration {
      speed: 12.4
      acceleration: -1.855
      command: 0.35
    }
    calibration {
      speed: 12.4
      acceleration: -1.432
      command: 0.4
    }
    calibration {
      speed: 12.4
      acceleration: -1.059
      command: 0.45
    }
    calibration {
      speed: 12.4
      acceleration: -0.654
      command: 0.5
    }
    calibration {
      speed: 12.4
      acceleration: -0.241
      command: 0.55
    }
    calibration {
      speed: 12.4
      acceleration: 0.21
      command: 0.6
    }
    calibration {
      speed: 12.4
      acceleration: 0.607
      command: 0.65
    }
    calibration {
      speed: 12.4
      acceleration: 1.026
      command: 0.7
    }
    calibration {
      speed: 12.4
      acceleration: 1.538
      command: 0.75
    }
    calibration {
      speed: 12.4
      acceleration: 2.0
      command: 0.8
    }
    calibration {
      speed: 12.4
      acceleration: 2.439
      command: 0.85
    }
    calibration {
      speed: 12.6
      acceleration: -4.634
    }
    calibration {
      speed: 12.6
      acceleration: -4.317
      command: 0.05
    }
    calibration {
      speed: 12.6
      acceleration: -3.939
      command: 0.1
    }
    calibration {
      speed: 12.6
      acceleration: -3.493
      command: 0.15
    }
    calibration {
      speed: 12.6
      acceleration: -3.073
      command: 0.2
    }
    calibration {
      speed: 12.6
      acceleration: -2.676
      command: 0.25
    }
    calibration {
      speed: 12.6
      acceleration: -2.322
      command: 0.3
    }
    calibration {
      speed: 12.6
      acceleration: -1.917
      command: 0.35
    }
    calibration {
      speed: 12.6
      acceleration: -1.507
      command: 0.4
    }
    calibration {
      speed: 12.6
      acceleration: -1.087
      command: 0.45
    }
    calibration {
      speed: 12.6
      acceleration: -0.693
      command: 0.5
    }
    calibration {
      speed: 12.6
      acceleration: -0.274
      command: 0.55
    }
    calibration {
      speed: 12.6
      acceleration: 0.143
      command: 0.6
    }
    calibration {
      speed: 12.6
      acceleration: 0.587
      command: 0.65
    }
    calibration {
      speed: 12.6
      acceleration: 1.03
      command: 0.7
    }
    calibration {
      speed: 12.6
      acceleration: 1.486
      command: 0.75
    }
    calibration {
      speed: 12.6
      acceleration: 1.956
      command: 0.8
    }
    calibration {
      speed: 12.6
      acceleration: 2.401
      command: 0.85
    }
    calibration {
      speed: 12.8
      acceleration: -4.758
    }
    calibration {
      speed: 12.8
      acceleration: -4.37
      command: 0.05
    }
    calibration {
      speed: 12.8
      acceleration: -3.995
      command: 0.1
    }
    calibration {
      speed: 12.8
      acceleration: -3.533
      command: 0.15
    }
    calibration {
      speed: 12.8
      acceleration: -3.135
      command: 0.2
    }
    calibration {
      speed: 12.8
      acceleration: -2.769
      command: 0.25
    }
    calibration {
      speed: 12.8
      acceleration: -2.373
      command: 0.3
    }
    calibration {
      speed: 12.8
      acceleration: -1.96
      command: 0.35
    }
    calibration {
      speed: 12.8
      acceleration: -1.557
      command: 0.4
    }
    calibration {
      speed: 12.8
      acceleration: -1.127
      command: 0.45
    }
    calibration {
      speed: 12.8
      acceleration: -0.735
      command: 0.5
    }
    calibration {
      speed: 12.8
      acceleration: -0.309
      command: 0.55
    }
    calibration {
      speed: 12.8
      acceleration: 0.113
      command: 0.6
    }
    calibration {
      speed: 12.8
      acceleration: 0.548
      command: 0.65
    }
    calibration {
      speed: 12.8
      acceleration: 1.004
      command: 0.7
    }
    calibration {
      speed: 12.8
      acceleration: 1.422
      command: 0.75
    }
    calibration {
      speed: 12.8
      acceleration: 1.931
      command: 0.8
    }
    calibration {
      speed: 12.8
      acceleration: 2.373
      command: 0.85
    }
    calibration {
      speed: 13.0
      acceleration: -4.821
    }
    calibration {
      speed: 13.0
      acceleration: -4.423
      command: 0.05
    }
    calibration {
      speed: 13.0
      acceleration: -4.052
      command: 0.1
    }
    calibration {
      speed: 13.0
      acceleration: -3.573
      command: 0.15
    }
    calibration {
      speed: 13.0
      acceleration: -3.199
      command: 0.2
    }
    calibration {
      speed: 13.0
      acceleration: -2.833
      command: 0.25
    }
    calibration {
      speed: 13.0
      acceleration: -2.398
      command: 0.3
    }
    calibration {
      speed: 13.0
      acceleration: -2.005
      command: 0.35
    }
    calibration {
      speed: 13.0
      acceleration: -1.598
      command: 0.4
    }
    calibration {
      speed: 13.0
      acceleration: -1.168
      command: 0.45
    }
    calibration {
      speed: 13.0
      acceleration: -0.761
      command: 0.5
    }
    calibration {
      speed: 13.0
      acceleration: -0.344
      command: 0.55
    }
    calibration {
      speed: 13.0
      acceleration: 0.091
      command: 0.6
    }
    calibration {
      speed: 13.0
      acceleration: 0.518
      command: 0.65
    }
    calibration {
      speed: 13.0
      acceleration: 0.94
      command: 0.7
    }
    calibration {
      speed: 13.0
      acceleration: 1.378
      command: 0.75
    }
    calibration {
      speed: 13.0
      acceleration: 1.903
      command: 0.8
    }
    calibration {
      speed: 13.0
      acceleration: 2.358
      command: 0.85
    }
    calibration {
      speed: 13.2
      acceleration: -4.821
    }
    calibration {
      speed: 13.2
      acceleration: -4.475
      command: 0.05
    }
    calibration {
      speed: 13.2
      acceleration: -4.11
      command: 0.1
    }
    calibration {
      speed: 13.2
      acceleration: -3.654
      command: 0.15
    }
    calibration {
      speed: 13.2
      acceleration: -3.231
      command: 0.2
    }
    calibration {
      speed: 13.2
      acceleration: -2.865
      command: 0.25
    }
    calibration {
      speed: 13.2
      acceleration: -2.447
      command: 0.3
    }
    calibration {
      speed: 13.2
      acceleration: -2.051
      command: 0.35
    }
    calibration {
      speed: 13.2
      acceleration: -1.629
      command: 0.4
    }
    calibration {
      speed: 13.2
      acceleration: -1.212
      command: 0.45
    }
    calibration {
      speed: 13.2
      acceleration: -0.795
      command: 0.5
    }
    calibration {
      speed: 13.2
      acceleration: -0.376
      command: 0.55
    }
    calibration {
      speed: 13.2
      acceleration: 0.051
      command: 0.6
    }
    calibration {
      speed: 13.2
      acceleration: 0.496
      command: 0.65
    }
    calibration {
      speed: 13.2
      acceleration: 0.911
      command: 0.7
    }
    calibration {
      speed: 13.2
      acceleration: 1.362
      command: 0.75
    }
    calibration {
      speed: 13.2
      acceleration: 1.875
      command: 0.8
    }
    calibration {
      speed: 13.2
      acceleration: 2.329
      command: 0.85
    }
    calibration {
      speed: 13.4
      acceleration: -4.884
    }
    calibration {
      speed: 13.4
      acceleration: -4.527
      command: 0.05
    }
    calibration {
      speed: 13.4
      acceleration: -4.167
      command: 0.1
    }
    calibration {
      speed: 13.4
      acceleration: -3.736
      command: 0.15
    }
    calibration {
      speed: 13.4
      acceleration: -3.263
      command: 0.2
    }
    calibration {
      speed: 13.4
      acceleration: -2.929
      command: 0.25
    }
    calibration {
      speed: 13.4
      acceleration: -2.518
      command: 0.3
    }
    calibration {
      speed: 13.4
      acceleration: -2.12
      command: 0.35
    }
    calibration {
      speed: 13.4
      acceleration: -1.671
      command: 0.4
    }
    calibration {
      speed: 13.4
      acceleration: -1.258
      command: 0.45
    }
    calibration {
      speed: 13.4
      acceleration: -0.837
      command: 0.5
    }
    calibration {
      speed: 13.4
      acceleration: -0.421
      command: 0.55
    }
    calibration {
      speed: 13.4
      acceleration: 0.001
      command: 0.6
    }
    calibration {
      speed: 13.4
      acceleration: 0.453
      command: 0.65
    }
    calibration {
      speed: 13.4
      acceleration: 0.889
      command: 0.7
    }
    calibration {
      speed: 13.4
      acceleration: 1.356
      command: 0.75
    }
    calibration {
      speed: 13.4
      acceleration: 1.834
      command: 0.8
    }
    calibration {
      speed: 13.4
      acceleration: 2.299
      command: 0.85
    }
    calibration {
      speed: 13.6
      acceleration: -4.947
    }
    calibration {
      speed: 13.6
      acceleration: -4.527
      command: 0.05
    }
    calibration {
      speed: 13.6
      acceleration: -4.225
      command: 0.1
    }
    calibration {
      speed: 13.6
      acceleration: -3.777
      command: 0.15
    }
    calibration {
      speed: 13.6
      acceleration: -3.36
      command: 0.2
    }
    calibration {
      speed: 13.6
      acceleration: -2.962
      command: 0.25
    }
    calibration {
      speed: 13.6
      acceleration: -2.566
      command: 0.3
    }
    calibration {
      speed: 13.6
      acceleration: -2.165
      command: 0.35
    }
    calibration {
      speed: 13.6
      acceleration: -1.712
      command: 0.4
    }
    calibration {
      speed: 13.6
      acceleration: -1.321
      command: 0.45
    }
    calibration {
      speed: 13.6
      acceleration: -0.876
      command: 0.5
    }
    calibration {
      speed: 13.6
      acceleration: -0.44
      command: 0.55
    }
    calibration {
      speed: 13.6
      acceleration: -0.013
      command: 0.6
    }
    calibration {
      speed: 13.6
      acceleration: 0.428
      command: 0.65
    }
    calibration {
      speed: 13.6
      acceleration: 0.873
      command: 0.7
    }
    calibration {
      speed: 13.6
      acceleration: 1.345
      command: 0.75
    }
    calibration {
      speed: 13.6
      acceleration: 1.801
      command: 0.8
    }
    calibration {
      speed: 13.6
      acceleration: 2.269
      command: 0.85
    }
    calibration {
      speed: 13.8
      acceleration: -5.01
    }
    calibration {
      speed: 13.8
      acceleration: -4.578
      command: 0.05
    }
    calibration {
      speed: 13.8
      acceleration: -4.282
      command: 0.1
    }
    calibration {
      speed: 13.8
      acceleration: -3.819
      command: 0.15
    }
    calibration {
      speed: 13.8
      acceleration: -3.393
      command: 0.2
    }
    calibration {
      speed: 13.8
      acceleration: -3.061
      command: 0.25
    }
    calibration {
      speed: 13.8
      acceleration: -2.589
      command: 0.3
    }
    calibration {
      speed: 13.8
      acceleration: -2.208
      command: 0.35
    }
    calibration {
      speed: 13.8
      acceleration: -1.771
      command: 0.4
    }
    calibration {
      speed: 13.8
      acceleration: -1.363
      command: 0.45
    }
    calibration {
      speed: 13.8
      acceleration: -0.925
      command: 0.5
    }
    calibration {
      speed: 13.8
      acceleration: -0.475
      command: 0.55
    }
    calibration {
      speed: 13.8
      acceleration: -0.046
      command: 0.6
    }
    calibration {
      speed: 13.8
      acceleration: 0.405
      command: 0.65
    }
    calibration {
      speed: 13.8
      acceleration: 0.853
      command: 0.7
    }
    calibration {
      speed: 13.8
      acceleration: 1.328
      command: 0.75
    }
    calibration {
      speed: 13.8
      acceleration: 1.788
      command: 0.8
    }
    calibration {
      speed: 13.8
      acceleration: 2.239
      command: 0.85
    }
    calibration {
      speed: 14.0
      acceleration: -5.073
    }
    calibration {
      speed: 14.0
      acceleration: -4.629
      command: 0.05
    }
    calibration {
      speed: 14.0
      acceleration: -4.34
      command: 0.1
    }
    calibration {
      speed: 14.0
      acceleration: -3.862
      command: 0.15
    }
    calibration {
      speed: 14.0
      acceleration: -3.461
      command: 0.2
    }
    calibration {
      speed: 14.0
      acceleration: -3.094
      command: 0.25
    }
    calibration {
      speed: 14.0
      acceleration: -2.663
      command: 0.3
    }
    calibration {
      speed: 14.0
      acceleration: -2.249
      command: 0.35
    }
    calibration {
      speed: 14.0
      acceleration: -1.803
      command: 0.4
    }
    calibration {
      speed: 14.0
      acceleration: -1.405
      command: 0.45
    }
    calibration {
      speed: 14.0
      acceleration: -0.959
      command: 0.5
    }
    calibration {
      speed: 14.0
      acceleration: -0.515
      command: 0.55
    }
    calibration {
      speed: 14.0
      acceleration: -0.08
      command: 0.6
    }
    calibration {
      speed: 14.0
      acceleration: 0.377
      command: 0.65
    }
    calibration {
      speed: 14.0
      acceleration: 0.827
      command: 0.7
    }
    calibration {
      speed: 14.0
      acceleration: 1.302
      command: 0.75
    }
    calibration {
      speed: 14.0
      acceleration: 1.751
      command: 0.8
    }
    calibration {
      speed: 14.0
      acceleration: 2.21
      command: 0.85
    }
    calibration {
      speed: 14.2
      acceleration: -5.136
    }
    calibration {
      speed: 14.2
      acceleration: -4.681
      command: 0.05
    }
    calibration {
      speed: 14.2
      acceleration: -4.398
      command: 0.1
    }
    calibration {
      speed: 14.2
      acceleration: -3.905
      command: 0.15
    }
    calibration {
      speed: 14.2
      acceleration: -3.496
      command: 0.2
    }
    calibration {
      speed: 14.2
      acceleration: -3.127
      command: 0.25
    }
    calibration {
      speed: 14.2
      acceleration: -2.688
      command: 0.3
    }
    calibration {
      speed: 14.2
      acceleration: -2.288
      command: 0.35
    }
    calibration {
      speed: 14.2
      acceleration: -1.882
      command: 0.4
    }
    calibration {
      speed: 14.2
      acceleration: -1.445
      command: 0.45
    }
    calibration {
      speed: 14.2
      acceleration: -1.008
      command: 0.5
    }
    calibration {
      speed: 14.2
      acceleration: -0.557
      command: 0.55
    }
    calibration {
      speed: 14.2
      acceleration: -0.112
      command: 0.6
    }
    calibration {
      speed: 14.2
      acceleration: 0.341
      command: 0.65
    }
    calibration {
      speed: 14.2
      acceleration: 0.79
      command: 0.7
    }
    calibration {
      speed: 14.2
      acceleration: 1.263
      command: 0.75
    }
    calibration {
      speed: 14.2
      acceleration: 1.707
      command: 0.8
    }
    calibration {
      speed: 14.2
      acceleration: 2.182
      command: 0.85
    }
    calibration {
      speed: 14.4
      acceleration: -5.263
    }
    calibration {
      speed: 14.4
      acceleration: -4.786
      command: 0.05
    }
    calibration {
      speed: 14.4
      acceleration: -4.456
      command: 0.1
    }
    calibration {
      speed: 14.4
      acceleration: -3.993
      command: 0.15
    }
    calibration {
      speed: 14.4
      acceleration: -3.608
      command: 0.2
    }
    calibration {
      speed: 14.4
      acceleration: -3.194
      command: 0.25
    }
    calibration {
      speed: 14.4
      acceleration: -2.767
      command: 0.3
    }
    calibration {
      speed: 14.4
      acceleration: -2.326
      command: 0.35
    }
    calibration {
      speed: 14.4
      acceleration: -1.918
      command: 0.4
    }
    calibration {
      speed: 14.4
      acceleration: -1.483
      command: 0.45
    }
    calibration {
      speed: 14.4
      acceleration: -1.045
      command: 0.5
    }
    calibration {
      speed: 14.4
      acceleration: -0.596
      command: 0.55
    }
    calibration {
      speed: 14.4
      acceleration: -0.146
      command: 0.6
    }
    calibration {
      speed: 14.4
      acceleration: 0.306
      command: 0.65
    }
    calibration {
      speed: 14.4
      acceleration: 0.764
      command: 0.7
    }
    calibration {
      speed: 14.4
      acceleration: 1.226
      command: 0.75
    }
    calibration {
      speed: 14.4
      acceleration: 1.687
      command: 0.8
    }
    calibration {
      speed: 14.4
      acceleration: 2.157
      command: 0.85
    }
    calibration {
      speed: 14.6
      acceleration: -5.326
    }
    calibration {
      speed: 14.6
      acceleration: -4.84
      command: 0.05
    }
    calibration {
      speed: 14.6
      acceleration: -4.515
      command: 0.1
    }
    calibration {
      speed: 14.6
      acceleration: -4.037
      command: 0.15
    }
    calibration {
      speed: 14.6
      acceleration: -3.647
      command: 0.2
    }
    calibration {
      speed: 14.6
      acceleration: -3.261
      command: 0.25
    }
    calibration {
      speed: 14.6
      acceleration: -2.794
      command: 0.3
    }
    calibration {
      speed: 14.6
      acceleration: -2.385
      command: 0.35
    }
    calibration {
      speed: 14.6
      acceleration: -1.953
      command: 0.4
    }
    calibration {
      speed: 14.6
      acceleration: -1.519
      command: 0.45
    }
    calibration {
      speed: 14.6
      acceleration: -1.091
      command: 0.5
    }
    calibration {
      speed: 14.6
      acceleration: -0.636
      command: 0.55
    }
    calibration {
      speed: 14.6
      acceleration: -0.181
      command: 0.6
    }
    calibration {
      speed: 14.6
      acceleration: 0.274
      command: 0.65
    }
    calibration {
      speed: 14.6
      acceleration: 0.737
      command: 0.7
    }
    calibration {
      speed: 14.6
      acceleration: 1.194
      command: 0.75
    }
    calibration {
      speed: 14.6
      acceleration: 1.652
      command: 0.8
    }
    calibration {
      speed: 14.6
      acceleration: 2.133
      command: 0.85
    }
    calibration {
      speed: 14.8
      acceleration: -5.326
    }
    calibration {
      speed: 14.8
      acceleration: -4.953
      command: 0.05
    }
    calibration {
      speed: 14.8
      acceleration: -4.574
      command: 0.1
    }
    calibration {
      speed: 14.8
      acceleration: -4.126
      command: 0.15
    }
    calibration {
      speed: 14.8
      acceleration: -3.73
      command: 0.2
    }
    calibration {
      speed: 14.8
      acceleration: -3.295
      command: 0.25
    }
    calibration {
      speed: 14.8
      acceleration: -2.877
      command: 0.3
    }
    calibration {
      speed: 14.8
      acceleration: -2.406
      command: 0.35
    }
    calibration {
      speed: 14.8
      acceleration: -2.004
      command: 0.4
    }
    calibration {
      speed: 14.8
      acceleration: -1.554
      command: 0.45
    }
    calibration {
      speed: 14.8
      acceleration: -1.129
      command: 0.5
    }
    calibration {
      speed: 14.8
      acceleration: -0.667
      command: 0.55
    }
    calibration {
      speed: 14.8
      acceleration: -0.218
      command: 0.6
    }
    calibration {
      speed: 14.8
      acceleration: 0.243
      command: 0.65
    }
    calibration {
      speed: 14.8
      acceleration: 0.701
      command: 0.7
    }
    calibration {
      speed: 14.8
      acceleration: 1.166
      command: 0.75
    }
    calibration {
      speed: 14.8
      acceleration: 1.629
      command: 0.8
    }
    calibration {
      speed: 14.8
      acceleration: 2.103
      command: 0.85
    }
    calibration {
      speed: 15.0
      acceleration: -5.39
    }
    calibration {
      speed: 15.0
      acceleration: -5.012
      command: 0.05
    }
    calibration {
      speed: 15.0
      acceleration: -4.634
      command: 0.1
    }
    calibration {
      speed: 15.0
      acceleration: -4.215
      command: 0.15
    }
    calibration {
      speed: 15.0
      acceleration: -3.772
      command: 0.2
    }
    calibration {
      speed: 15.0
      acceleration: -3.364
      command: 0.25
    }
    calibration {
      speed: 15.0
      acceleration: -2.904
      command: 0.3
    }
    calibration {
      speed: 15.0
      acceleration: -2.472
      command: 0.35
    }
    calibration {
      speed: 15.0
      acceleration: -2.051
      command: 0.4
    }
    calibration {
      speed: 15.0
      acceleration: -1.605
      command: 0.45
    }
    calibration {
      speed: 15.0
      acceleration: -1.16
      command: 0.5
    }
    calibration {
      speed: 15.0
      acceleration: -0.709
      command: 0.55
    }
    calibration {
      speed: 15.0
      acceleration: -0.252
      command: 0.6
    }
    calibration {
      speed: 15.0
      acceleration: 0.209
      command: 0.65
    }
    calibration {
      speed: 15.0
      acceleration: 0.672
      command: 0.7
    }
    calibration {
      speed: 15.0
      acceleration: 1.143
      command: 0.75
    }
    calibration {
      speed: 15.0
      acceleration: 1.603
      command: 0.8
    }
    calibration {
      speed: 15.0
      acceleration: 2.079
      command: 0.85
    }
    calibration {
      speed: 15.2
      acceleration: -5.453
    }
    calibration {
      speed: 15.2
      acceleration: -5.074
      command: 0.05
    }
    calibration {
      speed: 15.2
      acceleration: -4.694
      command: 0.1
    }
    calibration {
      speed: 15.2
      acceleration: -4.259
      command: 0.15
    }
    calibration {
      speed: 15.2
      acceleration: -3.816
      command: 0.2
    }
    calibration {
      speed: 15.2
      acceleration: -3.4
      command: 0.25
    }
    calibration {
      speed: 15.2
      acceleration: -2.987
      command: 0.3
    }
    calibration {
      speed: 15.2
      acceleration: -2.547
      command: 0.35
    }
    calibration {
      speed: 15.2
      acceleration: -2.075
      command: 0.4
    }
    calibration {
      speed: 15.2
      acceleration: -1.661
      command: 0.45
    }
    calibration {
      speed: 15.2
      acceleration: -1.213
      command: 0.5
    }
    calibration {
      speed: 15.2
      acceleration: -0.752
      command: 0.55
    }
    calibration {
      speed: 15.2
      acceleration: -0.285
      command: 0.6
    }
    calibration {
      speed: 15.2
      acceleration: 0.177
      command: 0.65
    }
    calibration {
      speed: 15.2
      acceleration: 0.642
      command: 0.7
    }
    calibration {
      speed: 15.2
      acceleration: 1.115
      command: 0.75
    }
    calibration {
      speed: 15.2
      acceleration: 1.591
      command: 0.8
    }
    calibration {
      speed: 15.2
      acceleration: 2.06
      command: 0.85
    }
    calibration {
      speed: 15.4
      acceleration: -5.517
    }
    calibration {
      speed: 15.4
      acceleration: -5.138
      command: 0.05
    }
    calibration {
      speed: 15.4
      acceleration: -4.755
      command: 0.1
    }
    calibration {
      speed: 15.4
      acceleration: -4.302
      command: 0.15
    }
    calibration {
      speed: 15.4
      acceleration: -3.86
      command: 0.2
    }
    calibration {
      speed: 15.4
      acceleration: -3.435
      command: 0.25
    }
    calibration {
      speed: 15.4
      acceleration: -3.015
      command: 0.3
    }
    calibration {
      speed: 15.4
      acceleration: -2.6
      command: 0.35
    }
    calibration {
      speed: 15.4
      acceleration: -2.106
      command: 0.4
    }
    calibration {
      speed: 15.4
      acceleration: -1.691
      command: 0.45
    }
    calibration {
      speed: 15.4
      acceleration: -1.247
      command: 0.5
    }
    calibration {
      speed: 15.4
      acceleration: -0.782
      command: 0.55
    }
    calibration {
      speed: 15.4
      acceleration: -0.32
      command: 0.6
    }
    calibration {
      speed: 15.4
      acceleration: 0.147
      command: 0.65
    }
    calibration {
      speed: 15.4
      acceleration: 0.614
      command: 0.7
    }
    calibration {
      speed: 15.4
      acceleration: 1.089
      command: 0.75
    }
    calibration {
      speed: 15.4
      acceleration: 1.566
      command: 0.8
    }
    calibration {
      speed: 15.4
      acceleration: 2.042
      command: 0.85
    }
    calibration {
      speed: 15.6
      acceleration: -5.645
    }
    calibration {
      speed: 15.6
      acceleration: -5.138
      command: 0.05
    }
    calibration {
      speed: 15.6
      acceleration: -4.816
      command: 0.1
    }
    calibration {
      speed: 15.6
      acceleration: -4.388
      command: 0.15
    }
    calibration {
      speed: 15.6
      acceleration: -3.994
      command: 0.2
    }
    calibration {
      speed: 15.6
      acceleration: -3.508
      command: 0.25
    }
    calibration {
      speed: 15.6
      acceleration: -3.071
      command: 0.3
    }
    calibration {
      speed: 15.6
      acceleration: -2.653
      command: 0.35
    }
    calibration {
      speed: 15.6
      acceleration: -2.17
      command: 0.4
    }
    calibration {
      speed: 15.6
      acceleration: -1.752
      command: 0.45
    }
    calibration {
      speed: 15.6
      acceleration: -1.29
      command: 0.5
    }
    calibration {
      speed: 15.6
      acceleration: -0.818
      command: 0.55
    }
    calibration {
      speed: 15.6
      acceleration: -0.359
      command: 0.6
    }
    calibration {
      speed: 15.6
      acceleration: 0.114
      command: 0.65
    }
    calibration {
      speed: 15.6
      acceleration: 0.587
      command: 0.7
    }
    calibration {
      speed: 15.6
      acceleration: 1.067
      command: 0.75
    }
    calibration {
      speed: 15.6
      acceleration: 1.546
      command: 0.8
    }
    calibration {
      speed: 15.6
      acceleration: 2.025
      command: 0.85
    }
    calibration {
      speed: 15.8
      acceleration: -5.645
    }
    calibration {
      speed: 15.8
      acceleration: -5.273
      command: 0.05
    }
    calibration {
      speed: 15.8
      acceleration: -4.94
      command: 0.1
    }
    calibration {
      speed: 15.8
      acceleration: -4.431
      command: 0.15
    }
    calibration {
      speed: 15.8
      acceleration: -4.038
      command: 0.2
    }
    calibration {
      speed: 15.8
      acceleration: -3.583
      command: 0.25
    }
    calibration {
      speed: 15.8
      acceleration: -3.1
      command: 0.3
    }
    calibration {
      speed: 15.8
      acceleration: -2.708
      command: 0.35
    }
    calibration {
      speed: 15.8
      acceleration: -2.219
      command: 0.4
    }
    calibration {
      speed: 15.8
      acceleration: -1.783
      command: 0.45
    }
    calibration {
      speed: 15.8
      acceleration: -1.333
      command: 0.5
    }
    calibration {
      speed: 15.8
      acceleration: -0.861
      command: 0.55
    }
    calibration {
      speed: 15.8
      acceleration: -0.387
      command: 0.6
    }
    calibration {
      speed: 15.8
      acceleration: 0.083
      command: 0.65
    }
    calibration {
      speed: 15.8
      acceleration: 0.562
      command: 0.7
    }
    calibration {
      speed: 15.8
      acceleration: 1.039
      command: 0.75
    }
    calibration {
      speed: 15.8
      acceleration: 1.532
      command: 0.8
    }
    calibration {
      speed: 15.8
      acceleration: 2.007
      command: 0.85
    }
    calibration {
      speed: 16.0
      acceleration: -5.709
    }
    calibration {
      speed: 16.0
      acceleration: -5.343
      command: 0.05
    }
    calibration {
      speed: 16.0
      acceleration: -5.002
      command: 0.1
    }
    calibration {
      speed: 16.0
      acceleration: -4.473
      command: 0.15
    }
    calibration {
      speed: 16.0
      acceleration: -4.082
      command: 0.2
    }
    calibration {
      speed: 16.0
      acceleration: -3.66
      command: 0.25
    }
    calibration {
      speed: 16.0
      acceleration: -3.129
      command: 0.3
    }
    calibration {
      speed: 16.0
      acceleration: -2.735
      command: 0.35
    }
    calibration {
      speed: 16.0
      acceleration: -2.261
      command: 0.4
    }
    calibration {
      speed: 16.0
      acceleration: -1.814
      command: 0.45
    }
    calibration {
      speed: 16.0
      acceleration: -1.365
      command: 0.5
    }
    calibration {
      speed: 16.0
      acceleration: -0.902
      command: 0.55
    }
    calibration {
      speed: 16.0
      acceleration: -0.424
      command: 0.6
    }
    calibration {
      speed: 16.0
      acceleration: 0.052
      command: 0.65
    }
    calibration {
      speed: 16.0
      acceleration: 0.534
      command: 0.7
    }
    calibration {
      speed: 16.0
      acceleration: 1.012
      command: 0.75
    }
    calibration {
      speed: 16.0
      acceleration: 1.511
      command: 0.8
    }
    calibration {
      speed: 16.0
      acceleration: 1.994
      command: 0.85
    }
    calibration {
      speed: 16.2
      acceleration: -5.774
    }
    calibration {
      speed: 16.2
      acceleration: -5.415
      command: 0.05
    }
    calibration {
      speed: 16.2
      acceleration: -5.002
      command: 0.1
    }
    calibration {
      speed: 16.2
      acceleration: -4.473
      command: 0.15
    }
    calibration {
      speed: 16.2
      acceleration: -4.126
      command: 0.2
    }
    calibration {
      speed: 16.2
      acceleration: -3.699
      command: 0.25
    }
    calibration {
      speed: 16.2
      acceleration: -3.221
      command: 0.3
    }
    calibration {
      speed: 16.2
      acceleration: -2.788
      command: 0.35
    }
    calibration {
      speed: 16.2
      acceleration: -2.311
      command: 0.4
    }
    calibration {
      speed: 16.2
      acceleration: -1.877
      command: 0.45
    }
    calibration {
      speed: 16.2
      acceleration: -1.404
      command: 0.5
    }
    calibration {
      speed: 16.2
      acceleration: -0.93
      command: 0.55
    }
    calibration {
      speed: 16.2
      acceleration: -0.458
      command: 0.6
    }
    calibration {
      speed: 16.2
      acceleration: 0.02
      command: 0.65
    }
    calibration {
      speed: 16.2
      acceleration: 0.507
      command: 0.7
    }
    calibration {
      speed: 16.2
      acceleration: 0.988
      command: 0.75
    }
    calibration {
      speed: 16.2
      acceleration: 1.488
      command: 0.8
    }
    calibration {
      speed: 16.2
      acceleration: 1.975
      command: 0.85
    }
    calibration {
      speed: 16.4
      acceleration: -5.902
    }
    calibration {
      speed: 16.4
      acceleration: -5.489
      command: 0.05
    }
    calibration {
      speed: 16.4
      acceleration: -5.064
      command: 0.1
    }
    calibration {
      speed: 16.4
      acceleration: -4.516
      command: 0.15
    }
    calibration {
      speed: 16.4
      acceleration: -4.169
      command: 0.2
    }
    calibration {
      speed: 16.4
      acceleration: -3.739
      command: 0.25
    }
    calibration {
      speed: 16.4
      acceleration: -3.285
      command: 0.3
    }
    calibration {
      speed: 16.4
      acceleration: -2.815
      command: 0.35
    }
    calibration {
      speed: 16.4
      acceleration: -2.362
      command: 0.4
    }
    calibration {
      speed: 16.4
      acceleration: -1.91
      command: 0.45
    }
    calibration {
      speed: 16.4
      acceleration: -1.451
      command: 0.5
    }
    calibration {
      speed: 16.4
      acceleration: -0.981
      command: 0.55
    }
    calibration {
      speed: 16.4
      acceleration: -0.497
      command: 0.6
    }
    calibration {
      speed: 16.4
      acceleration: -0.011
      command: 0.65
    }
    calibration {
      speed: 16.4
      acceleration: 0.474
      command: 0.7
    }
    calibration {
      speed: 16.4
      acceleration: 0.969
      command: 0.75
    }
    calibration {
      speed: 16.4
      acceleration: 1.466
      command: 0.8
    }
    calibration {
      speed: 16.4
      acceleration: 1.961
      command: 0.85
    }
    calibration {
      speed: 16.6
      acceleration: -5.902
    }
    calibration {
      speed: 16.6
      acceleration: -5.489
      command: 0.05
    }
    calibration {
      speed: 16.6
      acceleration: -5.126
      command: 0.1
    }
    calibration {
      speed: 16.6
      acceleration: -4.603
      command: 0.15
    }
    calibration {
      speed: 16.6
      acceleration: -4.252
      command: 0.2
    }
    calibration {
      speed: 16.6
      acceleration: -3.779
      command: 0.25
    }
    calibration {
      speed: 16.6
      acceleration: -3.319
      command: 0.3
    }
    calibration {
      speed: 16.6
      acceleration: -2.897
      command: 0.35
    }
    calibration {
      speed: 16.6
      acceleration: -2.415
      command: 0.4
    }
    calibration {
      speed: 16.6
      acceleration: -1.958
      command: 0.45
    }
    calibration {
      speed: 16.6
      acceleration: -1.503
      command: 0.5
    }
    calibration {
      speed: 16.6
      acceleration: -1.011
      command: 0.55
    }
    calibration {
      speed: 16.6
      acceleration: -0.532
      command: 0.6
    }
    calibration {
      speed: 16.6
      acceleration: -0.043
      command: 0.65
    }
    calibration {
      speed: 16.6
      acceleration: 0.45
      command: 0.7
    }
    calibration {
      speed: 16.6
      acceleration: 0.944
      command: 0.75
    }
    calibration {
      speed: 16.6
      acceleration: 1.444
      command: 0.8
    }
    calibration {
      speed: 16.6
      acceleration: 1.935
      command: 0.85
    }
    calibration {
      speed: 16.8
      acceleration: -5.966
    }
    calibration {
      speed: 16.8
      acceleration: -5.639
      command: 0.05
    }
    calibration {
      speed: 16.8
      acceleration: -5.188
      command: 0.1
    }
    calibration {
      speed: 16.8
      acceleration: -4.648
      command: 0.15
    }
    calibration {
      speed: 16.8
      acceleration: -4.292
      command: 0.2
    }
    calibration {
      speed: 16.8
      acceleration: -3.819
      command: 0.25
    }
    calibration {
      speed: 16.8
      acceleration: -3.424
      command: 0.3
    }
    calibration {
      speed: 16.8
      acceleration: -2.926
      command: 0.35
    }
    calibration {
      speed: 16.8
      acceleration: -2.434
      command: 0.4
    }
    calibration {
      speed: 16.8
      acceleration: -1.989
      command: 0.45
    }
    calibration {
      speed: 16.8
      acceleration: -1.544
      command: 0.5
    }
    calibration {
      speed: 16.8
      acceleration: -1.05
      command: 0.55
    }
    calibration {
      speed: 16.8
      acceleration: -0.562
      command: 0.6
    }
    calibration {
      speed: 16.8
      acceleration: -0.075
      command: 0.65
    }
    calibration {
      speed: 16.8
      acceleration: 0.421
      command: 0.7
    }
    calibration {
      speed: 16.8
      acceleration: 0.92
      command: 0.75
    }
    calibration {
      speed: 16.8
      acceleration: 1.418
      command: 0.8
    }
    calibration {
      speed: 16.8
      acceleration: 1.922
      command: 0.85
    }
    calibration {
      speed: 17.0
      acceleration: -6.03
    }
    calibration {
      speed: 17.0
      acceleration: -5.714
      command: 0.05
    }
    calibration {
      speed: 17.0
      acceleration: -5.25
      command: 0.1
    }
    calibration {
      speed: 17.0
      acceleration: -4.742
      command: 0.15
    }
    calibration {
      speed: 17.0
      acceleration: -4.33
      command: 0.2
    }
    calibration {
      speed: 17.0
      acceleration: -3.859
      command: 0.25
    }
    calibration {
      speed: 17.0
      acceleration: -3.46
      command: 0.3
    }
    calibration {
      speed: 17.0
      acceleration: -3.015
      command: 0.35
    }
    calibration {
      speed: 17.0
      acceleration: -2.492
      command: 0.4
    }
    calibration {
      speed: 17.0
      acceleration: -2.034
      command: 0.45
    }
    calibration {
      speed: 17.0
      acceleration: -1.572
      command: 0.5
    }
    calibration {
      speed: 17.0
      acceleration: -1.093
      command: 0.55
    }
    calibration {
      speed: 17.0
      acceleration: -0.6
      command: 0.6
    }
    calibration {
      speed: 17.0
      acceleration: -0.106
      command: 0.65
    }
    calibration {
      speed: 17.0
      acceleration: 0.393
      command: 0.7
    }
    calibration {
      speed: 17.0
      acceleration: 0.891
      command: 0.75
    }
    calibration {
      speed: 17.0
      acceleration: 1.399
      command: 0.8
    }
    calibration {
      speed: 17.0
      acceleration: 1.902
      command: 0.85
    }
    calibration {
      speed: 17.2
      acceleration: -6.03
    }
    calibration {
      speed: 17.2
      acceleration: -5.789
      command: 0.05
    }
    calibration {
      speed: 17.2
      acceleration: -5.311
      command: 0.1
    }
    calibration {
      speed: 17.2
      acceleration: -4.791
      command: 0.15
    }
    calibration {
      speed: 17.2
      acceleration: -4.368
      command: 0.2
    }
    calibration {
      speed: 17.2
      acceleration: -3.939
      command: 0.25
    }
    calibration {
      speed: 17.2
      acceleration: -3.496
      command: 0.3
    }
    calibration {
      speed: 17.2
      acceleration: -3.046
      command: 0.35
    }
    calibration {
      speed: 17.2
      acceleration: -2.554
      command: 0.4
    }
    calibration {
      speed: 17.2
      acceleration: -2.063
      command: 0.45
    }
    calibration {
      speed: 17.2
      acceleration: -1.614
      command: 0.5
    }
    calibration {
      speed: 17.2
      acceleration: -1.131
      command: 0.55
    }
    calibration {
      speed: 17.2
      acceleration: -0.636
      command: 0.6
    }
    calibration {
      speed: 17.2
      acceleration: -0.137
      command: 0.65
    }
    calibration {
      speed: 17.2
      acceleration: 0.363
      command: 0.7
    }
    calibration {
      speed: 17.2
      acceleration: 0.869
      command: 0.75
    }
    calibration {
      speed: 17.2
      acceleration: 1.38
      command: 0.8
    }
    calibration {
      speed: 17.2
      acceleration: 1.888
      command: 0.85
    }
    calibration {
      speed: 17.4
      acceleration: -6.16
    }
    calibration {
      speed: 17.4
      acceleration: -5.864
      command: 0.05
    }
    calibration {
      speed: 17.4
      acceleration: -5.373
      command: 0.1
    }
    calibration {
      speed: 17.4
      acceleration: -4.842
      command: 0.15
    }
    calibration {
      speed: 17.4
      acceleration: -4.442
      command: 0.2
    }
    calibration {
      speed: 17.4
      acceleration: -4.019
      command: 0.25
    }
    calibration {
      speed: 17.4
      acceleration: -3.532
      command: 0.3
    }
    calibration {
      speed: 17.4
      acceleration: -3.111
      command: 0.35
    }
    calibration {
      speed: 17.4
      acceleration: -2.587
      command: 0.4
    }
    calibration {
      speed: 17.4
      acceleration: -2.093
      command: 0.45
    }
    calibration {
      speed: 17.4
      acceleration: -1.658
      command: 0.5
    }
    calibration {
      speed: 17.4
      acceleration: -1.179
      command: 0.55
    }
    calibration {
      speed: 17.4
      acceleration: -0.668
      command: 0.6
    }
    calibration {
      speed: 17.4
      acceleration: -0.169
      command: 0.65
    }
    calibration {
      speed: 17.4
      acceleration: 0.336
      command: 0.7
    }
    calibration {
      speed: 17.4
      acceleration: 0.84
      command: 0.75
    }
    calibration {
      speed: 17.4
      acceleration: 1.356
      command: 0.8
    }
    calibration {
      speed: 17.4
      acceleration: 1.874
      command: 0.85
    }
    calibration {
      speed: 17.6
      acceleration: -6.226
    }
    calibration {
      speed: 17.6
      acceleration: -5.864
      command: 0.05
    }
    calibration {
      speed: 17.6
      acceleration: -5.373
      command: 0.1
    }
    calibration {
      speed: 17.6
      acceleration: -4.947
      command: 0.15
    }
    calibration {
      speed: 17.6
      acceleration: -4.478
      command: 0.2
    }
    calibration {
      speed: 17.6
      acceleration: -4.058
      command: 0.25
    }
    calibration {
      speed: 17.6
      acceleration: -3.604
      command: 0.3
    }
    calibration {
      speed: 17.6
      acceleration: -3.144
      command: 0.35
    }
    calibration {
      speed: 17.6
      acceleration: -2.654
      command: 0.4
    }
    calibration {
      speed: 17.6
      acceleration: -2.155
      command: 0.45
    }
    calibration {
      speed: 17.6
      acceleration: -1.702
      command: 0.5
    }
    calibration {
      speed: 17.6
      acceleration: -1.199
      command: 0.55
    }
    calibration {
      speed: 17.6
      acceleration: -0.7
      command: 0.6
    }
    calibration {
      speed: 17.6
      acceleration: -0.201
      command: 0.65
    }
    calibration {
      speed: 17.6
      acceleration: 0.305
      command: 0.7
    }
    calibration {
      speed: 17.6
      acceleration: 0.813
      command: 0.75
    }
    calibration {
      speed: 17.6
      acceleration: 1.332
      command: 0.8
    }
    calibration {
      speed: 17.6
      acceleration: 1.859
      command: 0.85
    }
    calibration {
      speed: 17.8
      acceleration: -6.294
    }
    calibration {
      speed: 17.8
      acceleration: -5.937
      command: 0.05
    }
    calibration {
      speed: 17.8
      acceleration: -5.434
      command: 0.1
    }
    calibration {
      speed: 17.8
      acceleration: -5.058
      command: 0.15
    }
    calibration {
      speed: 17.8
      acceleration: -4.514
      command: 0.2
    }
    calibration {
      speed: 17.8
      acceleration: -4.137
      command: 0.25
    }
    calibration {
      speed: 17.8
      acceleration: -3.64
      command: 0.3
    }
    calibration {
      speed: 17.8
      acceleration: -3.178
      command: 0.35
    }
    calibration {
      speed: 17.8
      acceleration: -2.723
      command: 0.4
    }
    calibration {
      speed: 17.8
      acceleration: -2.208
      command: 0.45
    }
    calibration {
      speed: 17.8
      acceleration: -1.746
      command: 0.5
    }
    calibration {
      speed: 17.8
      acceleration: -1.249
      command: 0.55
    }
    calibration {
      speed: 17.8
      acceleration: -0.733
      command: 0.6
    }
    calibration {
      speed: 17.8
      acceleration: -0.232
      command: 0.65
    }
    calibration {
      speed: 17.8
      acceleration: 0.277
      command: 0.7
    }
    calibration {
      speed: 17.8
      acceleration: 0.792
      command: 0.75
    }
    calibration {
      speed: 17.8
      acceleration: 1.31
      command: 0.8
    }
    calibration {
      speed: 17.8
      acceleration: 1.83
      command: 0.85
    }
    calibration {
      speed: 18.0
      acceleration: -6.294
    }
    calibration {
      speed: 18.0
      acceleration: -6.01
      command: 0.05
    }
    calibration {
      speed: 18.0
      acceleration: -5.495
      command: 0.1
    }
    calibration {
      speed: 18.0
      acceleration: -5.116
      command: 0.15
    }
    calibration {
      speed: 18.0
      acceleration: -4.551
      command: 0.2
    }
    calibration {
      speed: 18.0
      acceleration: -4.177
      command: 0.25
    }
    calibration {
      speed: 18.0
      acceleration: -3.711
      command: 0.3
    }
    calibration {
      speed: 18.0
      acceleration: -3.247
      command: 0.35
    }
    calibration {
      speed: 18.0
      acceleration: -2.723
      command: 0.4
    }
    calibration {
      speed: 18.0
      acceleration: -2.247
      command: 0.45
    }
    calibration {
      speed: 18.0
      acceleration: -1.768
      command: 0.5
    }
    calibration {
      speed: 18.0
      acceleration: -1.279
      command: 0.55
    }
    calibration {
      speed: 18.0
      acceleration: -0.768
      command: 0.6
    }
    calibration {
      speed: 18.0
      acceleration: -0.262
      command: 0.65
    }
    calibration {
      speed: 18.0
      acceleration: 0.25
      command: 0.7
    }
    calibration {
      speed: 18.0
      acceleration: 0.768
      command: 0.75
    }
    calibration {
      speed: 18.0
      acceleration: 1.284
      command: 0.8
    }
    calibration {
      speed: 18.0
      acceleration: 1.808
      command: 0.85
    }
    calibration {
      speed: 18.2
      acceleration: -6.363
    }
    calibration {
      speed: 18.2
      acceleration: -6.01
      command: 0.05
    }
    calibration {
      speed: 18.2
      acceleration: -5.62
      command: 0.1
    }
    calibration {
      speed: 18.2
      acceleration: -5.116
      command: 0.15
    }
    calibration {
      speed: 18.2
      acceleration: -4.625
      command: 0.2
    }
    calibration {
      speed: 18.2
      acceleration: -4.216
      command: 0.25
    }
    calibration {
      speed: 18.2
      acceleration: -3.745
      command: 0.3
    }
    calibration {
      speed: 18.2
      acceleration: -3.317
      command: 0.35
    }
    calibration {
      speed: 18.2
      acceleration: -2.807
      command: 0.4
    }
    calibration {
      speed: 18.2
      acceleration: -2.31
      command: 0.45
    }
    calibration {
      speed: 18.2
      acceleration: -1.812
      command: 0.5
    }
    calibration {
      speed: 18.2
      acceleration: -1.31
      command: 0.55
    }
    calibration {
      speed: 18.2
      acceleration: -0.809
      command: 0.6
    }
    calibration {
      speed: 18.2
      acceleration: -0.292
      command: 0.65
    }
    calibration {
      speed: 18.2
      acceleration: 0.223
      command: 0.7
    }
    calibration {
      speed: 18.2
      acceleration: 0.742
      command: 0.75
    }
    calibration {
      speed: 18.2
      acceleration: 1.268
      command: 0.8
    }
    calibration {
      speed: 18.2
      acceleration: 1.801
      command: 0.85
    }
    calibration {
      speed: 18.4
      acceleration: -6.508
    }
    calibration {
      speed: 18.4
      acceleration: -6.082
      command: 0.05
    }
    calibration {
      speed: 18.4
      acceleration: -5.62
      command: 0.1
    }
    calibration {
      speed: 18.4
      acceleration: -5.174
      command: 0.15
    }
    calibration {
      speed: 18.4
      acceleration: -4.625
      command: 0.2
    }
    calibration {
      speed: 18.4
      acceleration: -4.255
      command: 0.25
    }
    calibration {
      speed: 18.4
      acceleration: -3.78
      command: 0.3
    }
    calibration {
      speed: 18.4
      acceleration: -3.351
      command: 0.35
    }
    calibration {
      speed: 18.4
      acceleration: -2.832
      command: 0.4
    }
    calibration {
      speed: 18.4
      acceleration: -2.354
      command: 0.45
    }
    calibration {
      speed: 18.4
      acceleration: -1.855
      command: 0.5
    }
    calibration {
      speed: 18.4
      acceleration: -1.351
      command: 0.55
    }
    calibration {
      speed: 18.4
      acceleration: -0.848
      command: 0.6
    }
    calibration {
      speed: 18.4
      acceleration: -0.327
      command: 0.65
    }
    calibration {
      speed: 18.4
      acceleration: 0.196
      command: 0.7
    }
    calibration {
      speed: 18.4
      acceleration: 0.718
      command: 0.75
    }
    calibration {
      speed: 18.4
      acceleration: 1.251
      command: 0.8
    }
    calibration {
      speed: 18.4
      acceleration: 1.781
      command: 0.85
    }
    calibration {
      speed: 18.6
      acceleration: -6.508
    }
    calibration {
      speed: 18.6
      acceleration: -6.152
      command: 0.05
    }
    calibration {
      speed: 18.6
      acceleration: -5.684
      command: 0.1
    }
    calibration {
      speed: 18.6
      acceleration: -5.233
      command: 0.15
    }
    calibration {
      speed: 18.6
      acceleration: -4.703
      command: 0.2
    }
    calibration {
      speed: 18.6
      acceleration: -4.295
      command: 0.25
    }
    calibration {
      speed: 18.6
      acceleration: -3.813
      command: 0.3
    }
    calibration {
      speed: 18.6
      acceleration: -3.385
      command: 0.35
    }
    calibration {
      speed: 18.6
      acceleration: -2.883
      command: 0.4
    }
    calibration {
      speed: 18.6
      acceleration: -2.42
      command: 0.45
    }
    calibration {
      speed: 18.6
      acceleration: -1.885
      command: 0.5
    }
    calibration {
      speed: 18.6
      acceleration: -1.382
      command: 0.55
    }
    calibration {
      speed: 18.6
      acceleration: -0.879
      command: 0.6
    }
    calibration {
      speed: 18.6
      acceleration: -0.359
      command: 0.65
    }
    calibration {
      speed: 18.6
      acceleration: 0.166
      command: 0.7
    }
    calibration {
      speed: 18.6
      acceleration: 0.696
      command: 0.75
    }
    calibration {
      speed: 18.6
      acceleration: 1.228
      command: 0.8
    }
    calibration {
      speed: 18.6
      acceleration: 1.756
      command: 0.85
    }
    calibration {
      speed: 18.8
      acceleration: -6.584
    }
    calibration {
      speed: 18.8
      acceleration: -6.222
      command: 0.05
    }
    calibration {
      speed: 18.8
      acceleration: -5.748
      command: 0.1
    }
    calibration {
      speed: 18.8
      acceleration: -5.293
      command: 0.15
    }
    calibration {
      speed: 18.8
      acceleration: -4.744
      command: 0.2
    }
    calibration {
      speed: 18.8
      acceleration: -4.336
      command: 0.25
    }
    calibration {
      speed: 18.8
      acceleration: -3.879
      command: 0.3
    }
    calibration {
      speed: 18.8
      acceleration: -3.452
      command: 0.35
    }
    calibration {
      speed: 18.8
      acceleration: -2.909
      command: 0.4
    }
    calibration {
      speed: 18.8
      acceleration: -2.442
      command: 0.45
    }
    calibration {
      speed: 18.8
      acceleration: -1.931
      command: 0.5
    }
    calibration {
      speed: 18.8
      acceleration: -1.422
      command: 0.55
    }
    calibration {
      speed: 18.8
      acceleration: -0.904
      command: 0.6
    }
    calibration {
      speed: 18.8
      acceleration: -0.387
      command: 0.65
    }
    calibration {
      speed: 18.8
      acceleration: 0.138
      command: 0.7
    }
    calibration {
      speed: 18.8
      acceleration: 0.672
      command: 0.75
    }
    calibration {
      speed: 18.8
      acceleration: 1.201
      command: 0.8
    }
    calibration {
      speed: 18.8
      acceleration: 1.743
      command: 0.85
    }
    calibration {
      speed: 19.0
      acceleration: -6.744
    }
    calibration {
      speed: 19.0
      acceleration: -6.222
      command: 0.05
    }
    calibration {
      speed: 19.0
      acceleration: -5.849
      command: 0.1
    }
    calibration {
      speed: 19.0
      acceleration: -5.354
      command: 0.15
    }
    calibration {
      speed: 19.0
      acceleration: -4.829
      command: 0.2
    }
    calibration {
      speed: 19.0
      acceleration: -4.418
      command: 0.25
    }
    calibration {
      speed: 19.0
      acceleration: -3.943
      command: 0.3
    }
    calibration {
      speed: 19.0
      acceleration: -3.485
      command: 0.35
    }
    calibration {
      speed: 19.0
      acceleration: -2.992
      command: 0.4
    }
    calibration {
      speed: 19.0
      acceleration: -2.484
      command: 0.45
    }
    calibration {
      speed: 19.0
      acceleration: -1.979
      command: 0.5
    }
    calibration {
      speed: 19.0
      acceleration: -1.454
      command: 0.55
    }
    calibration {
      speed: 19.0
      acceleration: -0.947
      command: 0.6
    }
    calibration {
      speed: 19.0
      acceleration: -0.421
      command: 0.65
    }
    calibration {
      speed: 19.0
      acceleration: 0.11
      command: 0.7
    }
    calibration {
      speed: 19.0
      acceleration: 0.643
      command: 0.75
    }
    calibration {
      speed: 19.0
      acceleration: 1.181
      command: 0.8
    }
    calibration {
      speed: 19.0
      acceleration: 1.725
      command: 0.85
    }
    calibration {
      speed: 19.2
      acceleration: -6.744
    }
    calibration {
      speed: 19.2
      acceleration: -6.291
      command: 0.05
    }
    calibration {
      speed: 19.2
      acceleration: -5.849
      command: 0.1
    }
    calibration {
      speed: 19.2
      acceleration: -5.354
      command: 0.15
    }
    calibration {
      speed: 19.2
      acceleration: -4.92
      command: 0.2
    }
    calibration {
      speed: 19.2
      acceleration: -4.461
      command: 0.25
    }
    calibration {
      speed: 19.2
      acceleration: -3.975
      command: 0.3
    }
    calibration {
      speed: 19.2
      acceleration: -3.518
      command: 0.35
    }
    calibration {
      speed: 19.2
      acceleration: -3.049
      command: 0.4
    }
    calibration {
      speed: 19.2
      acceleration: -2.525
      command: 0.45
    }
    calibration {
      speed: 19.2
      acceleration: -2.012
      command: 0.5
    }
    calibration {
      speed: 19.2
      acceleration: -1.501
      command: 0.55
    }
    calibration {
      speed: 19.2
      acceleration: -0.972
      command: 0.6
    }
    calibration {
      speed: 19.2
      acceleration: -0.451
      command: 0.65
    }
    calibration {
      speed: 19.2
      acceleration: 0.082
      command: 0.7
    }
    calibration {
      speed: 19.2
      acceleration: 0.62
      command: 0.75
    }
    calibration {
      speed: 19.2
      acceleration: 1.161
      command: 0.8
    }
    calibration {
      speed: 19.2
      acceleration: 1.706
      command: 0.85
    }
    calibration {
      speed: 19.4
      acceleration: -6.828
    }
    calibration {
      speed: 19.4
      acceleration: -6.429
      command: 0.05
    }
    calibration {
      speed: 19.4
      acceleration: -5.918
      command: 0.1
    }
    calibration {
      speed: 19.4
      acceleration: -5.477
      command: 0.15
    }
    calibration {
      speed: 19.4
      acceleration: -4.966
      command: 0.2
    }
    calibration {
      speed: 19.4
      acceleration: -4.55
      command: 0.25
    }
    calibration {
      speed: 19.4
      acceleration: -4.038
      command: 0.3
    }
    calibration {
      speed: 19.4
      acceleration: -3.55
      command: 0.35
    }
    calibration {
      speed: 19.4
      acceleration: -3.078
      command: 0.4
    }
    calibration {
      speed: 19.4
      acceleration: -2.565
      command: 0.45
    }
    calibration {
      speed: 19.4
      acceleration: -2.06
      command: 0.5
    }
    calibration {
      speed: 19.4
      acceleration: -1.539
      command: 0.55
    }
    calibration {
      speed: 19.4
      acceleration: -1.017
      command: 0.6
    }
    calibration {
      speed: 19.4
      acceleration: -0.48
      command: 0.65
    }
    calibration {
      speed: 19.4
      acceleration: 0.054
      command: 0.7
    }
    calibration {
      speed: 19.4
      acceleration: 0.596
      command: 0.75
    }
    calibration {
      speed: 19.4
      acceleration: 1.146
      command: 0.8
    }
    calibration {
      speed: 19.4
      acceleration: 1.694
      command: 0.85
    }
    calibration {
      speed: 19.6
      acceleration: -6.828
    }
    calibration {
      speed: 19.6
      acceleration: -6.429
      command: 0.05
    }
    calibration {
      speed: 19.6
      acceleration: -5.989
      command: 0.1
    }
    calibration {
      speed: 19.6
      acceleration: -5.54
      command: 0.15
    }
    calibration {
      speed: 19.6
      acceleration: -5.013
      command: 0.2
    }
    calibration {
      speed: 19.6
      acceleration: -4.597
      command: 0.25
    }
    calibration {
      speed: 19.6
      acceleration: -4.069
      command: 0.3
    }
    calibration {
      speed: 19.6
      acceleration: -3.614
      command: 0.35
    }
    calibration {
      speed: 19.6
      acceleration: -3.137
      command: 0.4
    }
    calibration {
      speed: 19.6
      acceleration: -2.584
      command: 0.45
    }
    calibration {
      speed: 19.6
      acceleration: -2.091
      command: 0.5
    }
    calibration {
      speed: 19.6
      acceleration: -1.591
      command: 0.55
    }
    calibration {
      speed: 19.6
      acceleration: -1.044
      command: 0.6
    }
    calibration {
      speed: 19.6
      acceleration: -0.517
      command: 0.65
    }
    calibration {
      speed: 19.6
      acceleration: 0.026
      command: 0.7
    }
    calibration {
      speed: 19.6
      acceleration: 0.569
      command: 0.75
    }
    calibration {
      speed: 19.6
      acceleration: 1.119
      command: 0.8
    }
    calibration {
      speed: 19.6
      acceleration: 1.669
      command: 0.85
    }
    calibration {
      speed: 19.8
      acceleration: -6.913
    }
    calibration {
      speed: 19.8
      acceleration: -6.499
      command: 0.05
    }
    calibration {
      speed: 19.8
      acceleration: -6.062
      command: 0.1
    }
    calibration {
      speed: 19.8
      acceleration: -5.603
      command: 0.15
    }
    calibration {
      speed: 19.8
      acceleration: -5.108
      command: 0.2
    }
    calibration {
      speed: 19.8
      acceleration: -4.644
      command: 0.25
    }
    calibration {
      speed: 19.8
      acceleration: -4.102
      command: 0.3
    }
    calibration {
      speed: 19.8
      acceleration: -3.646
      command: 0.35
    }
    calibration {
      speed: 19.8
      acceleration: -3.166
      command: 0.4
    }
    calibration {
      speed: 19.8
      acceleration: -2.644
      command: 0.45
    }
    calibration {
      speed: 19.8
      acceleration: -2.137
      command: 0.5
    }
    calibration {
      speed: 19.8
      acceleration: -1.617
      command: 0.55
    }
    calibration {
      speed: 19.8
      acceleration: -1.088
      command: 0.6
    }
    calibration {
      speed: 19.8
      acceleration: -0.547
      command: 0.65
    }
    calibration {
      speed: 19.8
      acceleration: -0.008
      command: 0.7
    }
    calibration {
      speed: 19.8
      acceleration: 0.545
      command: 0.75
    }
    calibration {
      speed: 19.8
      acceleration: 1.091
      command: 0.8
    }
    calibration {
      speed: 19.8
      acceleration: 1.651
      command: 0.85
    }
    calibration {
      speed: 20.0
      acceleration: -7.0
    }
    calibration {
      speed: 20.0
      acceleration: -6.57
      command: 0.05
    }
    calibration {
      speed: 20.0
      acceleration: -6.062
      command: 0.1
    }
    calibration {
      speed: 20.0
      acceleration: -5.668
      command: 0.15
    }
    calibration {
      speed: 20.0
      acceleration: -5.108
      command: 0.2
    }
    calibration {
      speed: 20.0
      acceleration: -4.694
      command: 0.25
    }
    calibration {
      speed: 20.0
      acceleration: -4.17
      command: 0.3
    }
    calibration {
      speed: 20.0
      acceleration: -3.679
      command: 0.35
    }
    calibration {
      speed: 20.0
      acceleration: -3.196
      command: 0.4
    }
    calibration {
      speed: 20.0
      acceleration: -2.664
      command: 0.45
    }
    calibration {
      speed: 20.0
      acceleration: -2.167
      command: 0.5
    }
    calibration {
      speed: 20.0
      acceleration: -1.655
      command: 0.55
    }
    calibration {
      speed: 20.0
      acceleration: -1.115
      command: 0.6
    }
    calibration {
      speed: 20.0
      acceleration: -0.578
      command: 0.65
    }
    calibration {
      speed: 20.0
      acceleration: -0.03
      command: 0.7
    }
    calibration {
      speed: 20.0
      acceleration: 0.522
      command: 0.75
    }
    calibration {
      speed: 20.0
      acceleration: 1.078
      command: 0.8
    }
    calibration {
      speed: 20.0
      acceleration: 1.629
      command: 0.85
    }
    calibration {
      speed: 20.2
      acceleration: -7.0
    }
    calibration {
      speed: 20.2
      acceleration: -6.57
      command: 0.05
    }
    calibration {
      speed: 20.2
      acceleration: -6.214
      command: 0.1
    }
    calibration {
      speed: 20.2
      acceleration: -5.668
      command: 0.15
    }
    calibration {
      speed: 20.2
      acceleration: -5.156
      command: 0.2
    }
    calibration {
      speed: 20.2
      acceleration: -4.694
      command: 0.25
    }
    calibration {
      speed: 20.2
      acceleration: -4.205
      command: 0.3
    }
    calibration {
      speed: 20.2
      acceleration: -3.745
      command: 0.35
    }
    calibration {
      speed: 20.2
      acceleration: -3.255
      command: 0.4
    }
    calibration {
      speed: 20.2
      acceleration: -2.728
      command: 0.45
    }
    calibration {
      speed: 20.2
      acceleration: -2.198
      command: 0.5
    }
    calibration {
      speed: 20.2
      acceleration: -1.68
      command: 0.55
    }
    calibration {
      speed: 20.2
      acceleration: -1.147
      command: 0.6
    }
    calibration {
      speed: 20.2
      acceleration: -0.609
      command: 0.65
    }
    calibration {
      speed: 20.2
      acceleration: -0.057
      command: 0.7
    }
    calibration {
      speed: 20.2
      acceleration: 0.495
      command: 0.75
    }
    calibration {
      speed: 20.2
      acceleration: 1.06
      command: 0.8
    }
    calibration {
      speed: 20.2
      acceleration: 1.613
      command: 0.85
    }
    calibration {
      speed: 20.4
      acceleration: -7.172
    }
    calibration {
      speed: 20.4
      acceleration: -6.643
      command: 0.05
    }
    calibration {
      speed: 20.4
      acceleration: -6.292
      command: 0.1
    }
    calibration {
      speed: 20.4
      acceleration: -5.8
      command: 0.15
    }
    calibration {
      speed: 20.4
      acceleration: -5.25
      command: 0.2
    }
    calibration {
      speed: 20.4
      acceleration: -4.795
      command: 0.25
    }
    calibration {
      speed: 20.4
      acceleration: -4.281
      command: 0.3
    }
    calibration {
      speed: 20.4
      acceleration: -3.813
      command: 0.35
    }
    calibration {
      speed: 20.4
      acceleration: -3.285
      command: 0.4
    }
    calibration {
      speed: 20.4
      acceleration: -2.797
      command: 0.45
    }
    calibration {
      speed: 20.4
      acceleration: -2.23
      command: 0.5
    }
    calibration {
      speed: 20.4
      acceleration: -1.731
      command: 0.55
    }
    calibration {
      speed: 20.4
      acceleration: -1.187
      command: 0.6
    }
    calibration {
      speed: 20.4
      acceleration: -0.64
      command: 0.65
    }
    calibration {
      speed: 20.4
      acceleration: -0.084
      command: 0.7
    }
    calibration {
      speed: 20.4
      acceleration: 0.474
      command: 0.75
    }
    calibration {
      speed: 20.4
      acceleration: 1.04
      command: 0.8
    }
    calibration {
      speed: 20.4
      acceleration: 1.592
      command: 0.85
    }
    calibration {
      speed: 20.6
      acceleration: -7.256
    }
    calibration {
      speed: 20.6
      acceleration: -6.717
      command: 0.05
    }
    calibration {
      speed: 20.6
      acceleration: -6.292
      command: 0.1
    }
    calibration {
      speed: 20.6
      acceleration: -5.868
      command: 0.15
    }
    calibration {
      speed: 20.6
      acceleration: -5.297
      command: 0.2
    }
    calibration {
      speed: 20.6
      acceleration: -4.848
      command: 0.25
    }
    calibration {
      speed: 20.6
      acceleration: -4.321
      command: 0.3
    }
    calibration {
      speed: 20.6
      acceleration: -3.848
      command: 0.35
    }
    calibration {
      speed: 20.6
      acceleration: -3.344
      command: 0.4
    }
    calibration {
      speed: 20.6
      acceleration: -2.821
      command: 0.45
    }
    calibration {
      speed: 20.6
      acceleration: -2.281
      command: 0.5
    }
    calibration {
      speed: 20.6
      acceleration: -1.758
      command: 0.55
    }
    calibration {
      speed: 20.6
      acceleration: -1.221
      command: 0.6
    }
    calibration {
      speed: 20.6
      acceleration: -0.667
      command: 0.65
    }
    calibration {
      speed: 20.6
      acceleration: -0.112
      command: 0.7
    }
    calibration {
      speed: 20.6
      acceleration: 0.445
      command: 0.75
    }
    calibration {
      speed: 20.6
      acceleration: 1.019
      command: 0.8
    }
    calibration {
      speed: 20.6
      acceleration: 1.582
      command: 0.85
    }
    calibration {
      speed: 20.8
      acceleration: -7.256
    }
    calibration {
      speed: 20.8
      acceleration: -6.717
      command: 0.05
    }
    calibration {
      speed: 20.8
      acceleration: -6.371
      command: 0.1
    }
    calibration {
      speed: 20.8
      acceleration: -5.938
      command: 0.15
    }
    calibration {
      speed: 20.8
      acceleration: -5.39
      command: 0.2
    }
    calibration {
      speed: 20.8
      acceleration: -4.901
      command: 0.25
    }
    calibration {
      speed: 20.8
      acceleration: -4.406
      command: 0.3
    }
    calibration {
      speed: 20.8
      acceleration: -3.884
      command: 0.35
    }
    calibration {
      speed: 20.8
      acceleration: -3.404
      command: 0.4
    }
    calibration {
      speed: 20.8
      acceleration: -2.87
      command: 0.45
    }
    calibration {
      speed: 20.8
      acceleration: -2.317
      command: 0.5
    }
    calibration {
      speed: 20.8
      acceleration: -1.817
      command: 0.55
    }
    calibration {
      speed: 20.8
      acceleration: -1.257
      command: 0.6
    }
    calibration {
      speed: 20.8
      acceleration: -0.692
      command: 0.65
    }
    calibration {
      speed: 20.8
      acceleration: -0.14
      command: 0.7
    }
    calibration {
      speed: 20.8
      acceleration: 0.424
      command: 0.75
    }
    calibration {
      speed: 20.8
      acceleration: 0.993
      command: 0.8
    }
    calibration {
      speed: 20.8
      acceleration: 1.561
      command: 0.85
    }
    calibration {
      speed: 21.0
      acceleration: -7.297
    }
    calibration {
      speed: 21.0
      acceleration: -6.872
      command: 0.05
    }
    calibration {
      speed: 21.0
      acceleration: -6.45
      command: 0.1
    }
    calibration {
      speed: 21.0
      acceleration: -5.938
      command: 0.15
    }
    calibration {
      speed: 21.0
      acceleration: -5.39
      command: 0.2
    }
    calibration {
      speed: 21.0
      acceleration: -4.955
      command: 0.25
    }
    calibration {
      speed: 21.0
      acceleration: -4.45
      command: 0.3
    }
    calibration {
      speed: 21.0
      acceleration: -3.958
      command: 0.35
    }
    calibration {
      speed: 21.0
      acceleration: -3.435
      command: 0.4
    }
    calibration {
      speed: 21.0
      acceleration: -2.895
      command: 0.45
    }
    calibration {
      speed: 21.0
      acceleration: -2.375
      command: 0.5
    }
    calibration {
      speed: 21.0
      acceleration: -1.849
      command: 0.55
    }
    calibration {
      speed: 21.0
      acceleration: -1.285
      command: 0.6
    }
    calibration {
      speed: 21.0
      acceleration: -0.724
      command: 0.65
    }
    calibration {
      speed: 21.0
      acceleration: -0.167
      command: 0.7
    }
    calibration {
      speed: 21.0
      acceleration: 0.394
      command: 0.75
    }
    calibration {
      speed: 21.0
      acceleration: 0.967
      command: 0.8
    }
    calibration {
      speed: 21.0
      acceleration: 1.54
      command: 0.85
    }
    calibration {
      speed: 21.2
      acceleration: -7.297
    }
    calibration {
      speed: 21.2
      acceleration: -6.952
      command: 0.05
    }
    calibration {
      speed: 21.2
      acceleration: -6.45
      command: 0.1
    }
    calibration {
      speed: 21.2
      acceleration: -6.01
      command: 0.15
    }
    calibration {
      speed: 21.2
      acceleration: -5.437
      command: 0.2
    }
    calibration {
      speed: 21.2
      acceleration: -5.064
      command: 0.25
    }
    calibration {
      speed: 21.2
      acceleration: -4.519
      command: 0.3
    }
    calibration {
      speed: 21.2
      acceleration: -3.997
      command: 0.35
    }
    calibration {
      speed: 21.2
      acceleration: -3.465
      command: 0.4
    }
    calibration {
      speed: 21.2
      acceleration: -2.945
      command: 0.45
    }
    calibration {
      speed: 21.2
      acceleration: -2.436
      command: 0.5
    }
    calibration {
      speed: 21.2
      acceleration: -1.882
      command: 0.55
    }
    calibration {
      speed: 21.2
      acceleration: -1.322
      command: 0.6
    }
    calibration {
      speed: 21.2
      acceleration: -0.77
      command: 0.65
    }
    calibration {
      speed: 21.2
      acceleration: -0.195
      command: 0.7
    }
    calibration {
      speed: 21.2
      acceleration: 0.373
      command: 0.75
    }
    calibration {
      speed: 21.2
      acceleration: 0.95
      command: 0.8
    }
    calibration {
      speed: 21.2
      acceleration: 1.525
      command: 0.85
    }
    calibration {
      speed: 21.4
      acceleration: -7.414
    }
    calibration {
      speed: 21.4
      acceleration: -6.952
      command: 0.05
    }
    calibration {
      speed: 21.4
      acceleration: -6.529
      command: 0.1
    }
    calibration {
      speed: 21.4
      acceleration: -6.083
      command: 0.15
    }
    calibration {
      speed: 21.4
      acceleration: -5.485
      command: 0.2
    }
    calibration {
      speed: 21.4
      acceleration: -5.092
      command: 0.25
    }
    calibration {
      speed: 21.4
      acceleration: -4.566
      command: 0.3
    }
    calibration {
      speed: 21.4
      acceleration: -4.036
      command: 0.35
    }
    calibration {
      speed: 21.4
      acceleration: -3.527
      command: 0.4
    }
    calibration {
      speed: 21.4
      acceleration: -2.996
      command: 0.45
    }
    calibration {
      speed: 21.4
      acceleration: -2.457
      command: 0.5
    }
    calibration {
      speed: 21.4
      acceleration: -1.915
      command: 0.55
    }
    calibration {
      speed: 21.4
      acceleration: -1.349
      command: 0.6
    }
    calibration {
      speed: 21.4
      acceleration: -0.792
      command: 0.65
    }
    calibration {
      speed: 21.4
      acceleration: -0.223
      command: 0.7
    }
    calibration {
      speed: 21.4
      acceleration: 0.347
      command: 0.75
    }
    calibration {
      speed: 21.4
      acceleration: 0.932
      command: 0.8
    }
    calibration {
      speed: 21.4
      acceleration: 1.509
      command: 0.85
    }
    calibration {
      speed: 21.6
      acceleration: -7.558
    }
    calibration {
      speed: 21.6
      acceleration: -7.033
      command: 0.05
    }
    calibration {
      speed: 21.6
      acceleration: -6.607
      command: 0.1
    }
    calibration {
      speed: 21.6
      acceleration: -6.083
      command: 0.15
    }
    calibration {
      speed: 21.6
      acceleration: -5.534
      command: 0.2
    }
    calibration {
      speed: 21.6
      acceleration: -5.174
      command: 0.25
    }
    calibration {
      speed: 21.6
      acceleration: -4.659
      command: 0.3
    }
    calibration {
      speed: 21.6
      acceleration: -4.115
      command: 0.35
    }
    calibration {
      speed: 21.6
      acceleration: -3.543
      command: 0.4
    }
    calibration {
      speed: 21.6
      acceleration: -3.022
      command: 0.45
    }
    calibration {
      speed: 21.6
      acceleration: -2.5
      command: 0.5
    }
    calibration {
      speed: 21.6
      acceleration: -1.963
      command: 0.55
    }
    calibration {
      speed: 21.6
      acceleration: -1.383
      command: 0.6
    }
    calibration {
      speed: 21.6
      acceleration: -0.818
      command: 0.65
    }
    calibration {
      speed: 21.6
      acceleration: -0.25
      command: 0.7
    }
    calibration {
      speed: 21.6
      acceleration: 0.323
      command: 0.75
    }
    calibration {
      speed: 21.6
      acceleration: 0.905
      command: 0.8
    }
    calibration {
      speed: 21.6
      acceleration: 1.487
      command: 0.85
    }
    calibration {
      speed: 21.8
      acceleration: -7.558
    }
    calibration {
      speed: 21.8
      acceleration: -7.033
      command: 0.05
    }
    calibration {
      speed: 21.8
      acceleration: -6.607
      command: 0.1
    }
    calibration {
      speed: 21.8
      acceleration: -6.158
      command: 0.15
    }
    calibration {
      speed: 21.8
      acceleration: -5.638
      command: 0.2
    }
    calibration {
      speed: 21.8
      acceleration: -5.174
      command: 0.25
    }
    calibration {
      speed: 21.8
      acceleration: -4.706
      command: 0.3
    }
    calibration {
      speed: 21.8
      acceleration: -4.156
      command: 0.35
    }
    calibration {
      speed: 21.8
      acceleration: -3.621
      command: 0.4
    }
    calibration {
      speed: 21.8
      acceleration: -3.098
      command: 0.45
    }
    calibration {
      speed: 21.8
      acceleration: -2.544
      command: 0.5
    }
    calibration {
      speed: 21.8
      acceleration: -1.995
      command: 0.55
    }
    calibration {
      speed: 21.8
      acceleration: -1.407
      command: 0.6
    }
    calibration {
      speed: 21.8
      acceleration: -0.859
      command: 0.65
    }
    calibration {
      speed: 21.8
      acceleration: -0.278
      command: 0.7
    }
    calibration {
      speed: 21.8
      acceleration: 0.298
      command: 0.75
    }
    calibration {
      speed: 21.8
      acceleration: 0.888
      command: 0.8
    }
    calibration {
      speed: 21.8
      acceleration: 1.476
      command: 0.85
    }
    calibration {
      speed: 22.0
      acceleration: -7.624
    }
    calibration {
      speed: 22.0
      acceleration: -7.116
      command: 0.05
    }
    calibration {
      speed: 22.0
      acceleration: -6.683
      command: 0.1
    }
    calibration {
      speed: 22.0
      acceleration: -6.234
      command: 0.15
    }
    calibration {
      speed: 22.0
      acceleration: -5.638
      command: 0.2
    }
    calibration {
      speed: 22.0
      acceleration: -5.23
      command: 0.25
    }
    calibration {
      speed: 22.0
      acceleration: -4.706
      command: 0.3
    }
    calibration {
      speed: 22.0
      acceleration: -4.196
      command: 0.35
    }
    calibration {
      speed: 22.0
      acceleration: -3.653
      command: 0.4
    }
    calibration {
      speed: 22.0
      acceleration: -3.124
      command: 0.45
    }
    calibration {
      speed: 22.0
      acceleration: -2.567
      command: 0.5
    }
    calibration {
      speed: 22.0
      acceleration: -2.025
      command: 0.55
    }
    calibration {
      speed: 22.0
      acceleration: -1.441
      command: 0.6
    }
    calibration {
      speed: 22.0
      acceleration: -0.89
      command: 0.65
    }
    calibration {
      speed: 22.0
      acceleration: -0.306
      command: 0.7
    }
    calibration {
      speed: 22.0
      acceleration: 0.273
      command: 0.75
    }
    calibration {
      speed: 22.0
      acceleration: 0.867
      command: 0.8
    }
    calibration {
      speed: 22.0
      acceleration: 1.455
      command: 0.85
    }
    calibration {
      speed: 22.2
      acceleration: -7.624
    }
    calibration {
      speed: 22.2
      acceleration: -7.198
      command: 0.05
    }
    calibration {
      speed: 22.2
      acceleration: -6.758
      command: 0.1
    }
    calibration {
      speed: 22.2
      acceleration: -6.311
      command: 0.15
    }
    calibration {
      speed: 22.2
      acceleration: -5.694
      command: 0.2
    }
    calibration {
      speed: 22.2
      acceleration: -5.286
      command: 0.25
    }
    calibration {
      speed: 22.2
      acceleration: -4.752
      command: 0.3
    }
    calibration {
      speed: 22.2
      acceleration: -4.236
      command: 0.35
    }
    calibration {
      speed: 22.2
      acceleration: -3.717
      command: 0.4
    }
    calibration {
      speed: 22.2
      acceleration: -3.149
      command: 0.45
    }
    calibration {
      speed: 22.2
      acceleration: -2.612
      command: 0.5
    }
    calibration {
      speed: 22.2
      acceleration: -2.056
      command: 0.55
    }
    calibration {
      speed: 22.2
      acceleration: -1.48
      command: 0.6
    }
    calibration {
      speed: 22.2
      acceleration: -0.918
      command: 0.65
    }
    calibration {
      speed: 22.2
      acceleration: -0.331
      command: 0.7
    }
    calibration {
      speed: 22.2
      acceleration: 0.249
      command: 0.75
    }
    calibration {
      speed: 22.2
      acceleration: 0.842
      command: 0.8
    }
    calibration {
      speed: 22.2
      acceleration: 1.439
      command: 0.85
    }
    calibration {
      speed: 22.4
      acceleration: -7.688
    }
    calibration {
      speed: 22.4
      acceleration: -7.198
      command: 0.05
    }
    calibration {
      speed: 22.4
      acceleration: -6.758
      command: 0.1
    }
    calibration {
      speed: 22.4
      acceleration: -6.311
      command: 0.15
    }
    calibration {
      speed: 22.4
      acceleration: -5.811
      command: 0.2
    }
    calibration {
      speed: 22.4
      acceleration: -5.342
      command: 0.25
    }
    calibration {
      speed: 22.4
      acceleration: -4.796
      command: 0.3
    }
    calibration {
      speed: 22.4
      acceleration: -4.315
      command: 0.35
    }
    calibration {
      speed: 22.4
      acceleration: -3.749
      command: 0.4
    }
    calibration {
      speed: 22.4
      acceleration: -3.2
      command: 0.45
    }
    calibration {
      speed: 22.4
      acceleration: -2.682
      command: 0.5
    }
    calibration {
      speed: 22.4
      acceleration: -2.086
      command: 0.55
    }
    calibration {
      speed: 22.4
      acceleration: -1.538
      command: 0.6
    }
    calibration {
      speed: 22.4
      acceleration: -0.947
      command: 0.65
    }
    calibration {
      speed: 22.4
      acceleration: -0.359
      command: 0.7
    }
    calibration {
      speed: 22.4
      acceleration: 0.225
      command: 0.75
    }
    calibration {
      speed: 22.4
      acceleration: 0.818
      command: 0.8
    }
    calibration {
      speed: 22.4
      acceleration: 1.419
      command: 0.85
    }
    calibration {
      speed: 22.6
      acceleration: -7.688
    }
    calibration {
      speed: 22.6
      acceleration: -7.361
      command: 0.05
    }
    calibration {
      speed: 22.6
      acceleration: -6.899
      command: 0.1
    }
    calibration {
      speed: 22.6
      acceleration: -6.387
      command: 0.15
    }
    calibration {
      speed: 22.6
      acceleration: -5.874
      command: 0.2
    }
    calibration {
      speed: 22.6
      acceleration: -5.399
      command: 0.25
    }
    calibration {
      speed: 22.6
      acceleration: -4.84
      command: 0.3
    }
    calibration {
      speed: 22.6
      acceleration: -4.354
      command: 0.35
    }
    calibration {
      speed: 22.6
      acceleration: -3.781
      command: 0.4
    }
    calibration {
      speed: 22.6
      acceleration: -3.226
      command: 0.45
    }
    calibration {
      speed: 22.6
      acceleration: -2.706
      command: 0.5
    }
    calibration {
      speed: 22.6
      acceleration: -2.132
      command: 0.55
    }
    calibration {
      speed: 22.6
      acceleration: -1.574
      command: 0.6
    }
    calibration {
      speed: 22.6
      acceleration: -0.979
      command: 0.65
    }
    calibration {
      speed: 22.6
      acceleration: -0.385
      command: 0.7
    }
    calibration {
      speed: 22.6
      acceleration: 0.2
      command: 0.75
    }
    calibration {
      speed: 22.6
      acceleration: 0.797
      command: 0.8
    }
    calibration {
      speed: 22.6
      acceleration: 1.403
      command: 0.85
    }
    calibration {
      speed: 22.8
      acceleration: -7.751
    }
    calibration {
      speed: 22.8
      acceleration: -7.361
      command: 0.05
    }
    calibration {
      speed: 22.8
      acceleration: -6.966
      command: 0.1
    }
    calibration {
      speed: 22.8
      acceleration: -6.462
      command: 0.15
    }
    calibration {
      speed: 22.8
      acceleration: -5.874
      command: 0.2
    }
    calibration {
      speed: 22.8
      acceleration: -5.399
      command: 0.25
    }
    calibration {
      speed: 22.8
      acceleration: -4.883
      command: 0.3
    }
    calibration {
      speed: 22.8
      acceleration: -4.373
      command: 0.35
    }
    calibration {
      speed: 22.8
      acceleration: -3.814
      command: 0.4
    }
    calibration {
      speed: 22.8
      acceleration: -3.252
      command: 0.45
    }
    calibration {
      speed: 22.8
      acceleration: -2.753
      command: 0.5
    }
    calibration {
      speed: 22.8
      acceleration: -2.163
      command: 0.55
    }
    calibration {
      speed: 22.8
      acceleration: -1.597
      command: 0.6
    }
    calibration {
      speed: 22.8
      acceleration: -1.009
      command: 0.65
    }
    calibration {
      speed: 22.8
      acceleration: -0.413
      command: 0.7
    }
    calibration {
      speed: 22.8
      acceleration: 0.175
      command: 0.75
    }
    calibration {
      speed: 22.8
      acceleration: 0.777
      command: 0.8
    }
    calibration {
      speed: 22.8
      acceleration: 1.386
      command: 0.85
    }
    calibration {
      speed: 23.0
      acceleration: -7.751
    }
    calibration {
      speed: 23.0
      acceleration: -7.44
      command: 0.05
    }
    calibration {
      speed: 23.0
      acceleration: -6.966
      command: 0.1
    }
    calibration {
      speed: 23.0
      acceleration: -6.462
      command: 0.15
    }
    calibration {
      speed: 23.0
      acceleration: -5.937
      command: 0.2
    }
    calibration {
      speed: 23.0
      acceleration: -5.457
      command: 0.25
    }
    calibration {
      speed: 23.0
      acceleration: -4.924
      command: 0.3
    }
    calibration {
      speed: 23.0
      acceleration: -4.411
      command: 0.35
    }
    calibration {
      speed: 23.0
      acceleration: -3.846
      command: 0.4
    }
    calibration {
      speed: 23.0
      acceleration: -3.331
      command: 0.45
    }
    calibration {
      speed: 23.0
      acceleration: -2.777
      command: 0.5
    }
    calibration {
      speed: 23.0
      acceleration: -2.213
      command: 0.55
    }
    calibration {
      speed: 23.0
      acceleration: -1.631
      command: 0.6
    }
    calibration {
      speed: 23.0
      acceleration: -1.042
      command: 0.65
    }
    calibration {
      speed: 23.0
      acceleration: -0.443
      command: 0.7
    }
    calibration {
      speed: 23.0
      acceleration: 0.15
      command: 0.75
    }
    calibration {
      speed: 23.0
      acceleration: 0.753
      command: 0.8
    }
    calibration {
      speed: 23.0
      acceleration: 1.364
      command: 0.85
    }
    calibration {
      speed: 23.2
      acceleration: -7.883
    }
    calibration {
      speed: 23.2
      acceleration: -7.517
      command: 0.05
    }
    calibration {
      speed: 23.2
      acceleration: -7.031
      command: 0.1
    }
    calibration {
      speed: 23.2
      acceleration: -6.536
      command: 0.15
    }
    calibration {
      speed: 23.2
      acceleration: -6.067
      command: 0.2
    }
    calibration {
      speed: 23.2
      acceleration: -5.577
      command: 0.25
    }
    calibration {
      speed: 23.2
      acceleration: -4.964
      command: 0.3
    }
    calibration {
      speed: 23.2
      acceleration: -4.485
      command: 0.35
    }
    calibration {
      speed: 23.2
      acceleration: -3.912
      command: 0.4
    }
    calibration {
      speed: 23.2
      acceleration: -3.385
      command: 0.45
    }
    calibration {
      speed: 23.2
      acceleration: -2.825
      command: 0.5
    }
    calibration {
      speed: 23.2
      acceleration: -2.247
      command: 0.55
    }
    calibration {
      speed: 23.2
      acceleration: -1.653
      command: 0.6
    }
    calibration {
      speed: 23.2
      acceleration: -1.08
      command: 0.65
    }
    calibration {
      speed: 23.2
      acceleration: -0.47
      command: 0.7
    }
    calibration {
      speed: 23.2
      acceleration: 0.126
      command: 0.75
    }
    calibration {
      speed: 23.2
      acceleration: 0.737
      command: 0.8
    }
    calibration {
      speed: 23.2
      acceleration: 1.344
      command: 0.85
    }
    calibration {
      speed: 23.4
      acceleration: -7.956
    }
    calibration {
      speed: 23.4
      acceleration: -7.517
      command: 0.05
    }
    calibration {
      speed: 23.4
      acceleration: -7.031
      command: 0.1
    }
    calibration {
      speed: 23.4
      acceleration: -6.676
      command: 0.15
    }
    calibration {
      speed: 23.4
      acceleration: -6.067
      command: 0.2
    }
    calibration {
      speed: 23.4
      acceleration: -5.577
      command: 0.25
    }
    calibration {
      speed: 23.4
      acceleration: -5.041
      command: 0.3
    }
    calibration {
      speed: 23.4
      acceleration: -4.522
      command: 0.35
    }
    calibration {
      speed: 23.4
      acceleration: -3.961
      command: 0.4
    }
    calibration {
      speed: 23.4
      acceleration: -3.413
      command: 0.45
    }
    calibration {
      speed: 23.4
      acceleration: -2.848
      command: 0.5
    }
    calibration {
      speed: 23.4
      acceleration: -2.282
      command: 0.55
    }
    calibration {
      speed: 23.4
      acceleration: -1.685
      command: 0.6
    }
    calibration {
      speed: 23.4
      acceleration: -1.107
      command: 0.65
    }
    calibration {
      speed: 23.4
      acceleration: -0.494
      command: 0.7
    }
    calibration {
      speed: 23.4
      acceleration: 0.101
      command: 0.75
    }
    calibration {
      speed: 23.4
      acceleration: 0.709
      command: 0.8
    }
    calibration {
      speed: 23.4
      acceleration: 1.326
      command: 0.85
    }
    calibration {
      speed: 23.6
      acceleration: -7.956
    }
    calibration {
      speed: 23.6
      acceleration: -7.661
      command: 0.05
    }
    calibration {
      speed: 23.6
      acceleration: -7.094
      command: 0.1
    }
    calibration {
      speed: 23.6
      acceleration: -6.676
      command: 0.15
    }
    calibration {
      speed: 23.6
      acceleration: -6.132
      command: 0.2
    }
    calibration {
      speed: 23.6
      acceleration: -5.701
      command: 0.25
    }
    calibration {
      speed: 23.6
      acceleration: -5.041
      command: 0.3
    }
    calibration {
      speed: 23.6
      acceleration: -4.596
      command: 0.35
    }
    calibration {
      speed: 23.6
      acceleration: -4.01
      command: 0.4
    }
    calibration {
      speed: 23.6
      acceleration: -3.441
      command: 0.45
    }
    calibration {
      speed: 23.6
      acceleration: -2.907
      command: 0.5
    }
    calibration {
      speed: 23.6
      acceleration: -2.3
      command: 0.55
    }
    calibration {
      speed: 23.6
      acceleration: -1.718
      command: 0.6
    }
    calibration {
      speed: 23.6
      acceleration: -1.125
      command: 0.65
    }
    calibration {
      speed: 23.6
      acceleration: -0.523
      command: 0.7
    }
    calibration {
      speed: 23.6
      acceleration: 0.076
      command: 0.75
    }
    calibration {
      speed: 23.6
      acceleration: 0.689
      command: 0.8
    }
    calibration {
      speed: 23.6
      acceleration: 1.312
      command: 0.85
    }
    calibration {
      speed: 23.8
      acceleration: -8.038
    }
    calibration {
      speed: 23.8
      acceleration: -7.661
      command: 0.05
    }
    calibration {
      speed: 23.8
      acceleration: -7.157
      command: 0.1
    }
    calibration {
      speed: 23.8
      acceleration: -6.74
      command: 0.15
    }
    calibration {
      speed: 23.8
      acceleration: -6.195
      command: 0.2
    }
    calibration {
      speed: 23.8
      acceleration: -5.701
      command: 0.25
    }
    calibration {
      speed: 23.8
      acceleration: -5.078
      command: 0.3
    }
    calibration {
      speed: 23.8
      acceleration: -4.596
      command: 0.35
    }
    calibration {
      speed: 23.8
      acceleration: -4.074
      command: 0.4
    }
    calibration {
      speed: 23.8
      acceleration: -3.497
      command: 0.45
    }
    calibration {
      speed: 23.8
      acceleration: -2.931
      command: 0.5
    }
    calibration {
      speed: 23.8
      acceleration: -2.337
      command: 0.55
    }
    calibration {
      speed: 23.8
      acceleration: -1.764
      command: 0.6
    }
    calibration {
      speed: 23.8
      acceleration: -1.16
      command: 0.65
    }
    calibration {
      speed: 23.8
      acceleration: -0.55
      command: 0.7
    }
    calibration {
      speed: 23.8
      acceleration: 0.051
      command: 0.75
    }
    calibration {
      speed: 23.8
      acceleration: 0.667
      command: 0.8
    }
    calibration {
      speed: 23.8
      acceleration: 1.298
      command: 0.85
    }
    calibration {
      speed: 24.0
      acceleration: -8.038
    }
    calibration {
      speed: 24.0
      acceleration: -7.73
      command: 0.05
    }
    calibration {
      speed: 24.0
      acceleration: -7.157
      command: 0.1
    }
    calibration {
      speed: 24.0
      acceleration: -6.801
      command: 0.15
    }
    calibration {
      speed: 24.0
      acceleration: -6.195
      command: 0.2
    }
    calibration {
      speed: 24.0
      acceleration: -5.701
      command: 0.25
    }
    calibration {
      speed: 24.0
      acceleration: -5.115
      command: 0.3
    }
    calibration {
      speed: 24.0
      acceleration: -4.596
      command: 0.35
    }
    calibration {
      speed: 24.0
      acceleration: -4.107
      command: 0.4
    }
    calibration {
      speed: 24.0
      acceleration: -3.526
      command: 0.45
    }
    calibration {
      speed: 24.0
      acceleration: -2.979
      command: 0.5
    }
    calibration {
      speed: 24.0
      acceleration: -2.395
      command: 0.55
    }
    calibration {
      speed: 24.0
      acceleration: -1.787
      command: 0.6
    }
    calibration {
      speed: 24.0
      acceleration: -1.186
      command: 0.65
    }
    calibration {
      speed: 24.0
      acceleration: -0.578
      command: 0.7
    }
    calibration {
      speed: 24.0
      acceleration: 0.027
      command: 0.75
    }
    calibration {
      speed: 24.0
      acceleration: 0.649
      command: 0.8
    }
    calibration {
      speed: 24.0
      acceleration: 1.278
      command: 0.85
    }
    calibration {
      speed: 24.2
      acceleration: -8.232
    }
    calibration {
      speed: 24.2
      acceleration: -7.73
      command: 0.05
    }
    calibration {
      speed: 24.2
      acceleration: -7.22
      command: 0.1
    }
    calibration {
      speed: 24.2
      acceleration: -6.801
      command: 0.15
    }
    calibration {
      speed: 24.2
      acceleration: -6.256
      command: 0.2
    }
    calibration {
      speed: 24.2
      acceleration: -5.829
      command: 0.25
    }
    calibration {
      speed: 24.2
      acceleration: -5.19
      command: 0.3
    }
    calibration {
      speed: 24.2
      acceleration: -4.634
      command: 0.35
    }
    calibration {
      speed: 24.2
      acceleration: -4.139
      command: 0.4
    }
    calibration {
      speed: 24.2
      acceleration: -3.555
      command: 0.45
    }
    calibration {
      speed: 24.2
      acceleration: -3.004
      command: 0.5
    }
    calibration {
      speed: 24.2
      acceleration: -2.452
      command: 0.55
    }
    calibration {
      speed: 24.2
      acceleration: -1.821
      command: 0.6
    }
    calibration {
      speed: 24.2
      acceleration: -1.222
      command: 0.65
    }
    calibration {
      speed: 24.2
      acceleration: -0.604
      command: 0.7
    }
    calibration {
      speed: 24.2
      acceleration: -0.001
      command: 0.75
    }
    calibration {
      speed: 24.2
      acceleration: 0.625
      command: 0.8
    }
    calibration {
      speed: 24.2
      acceleration: 1.253
      command: 0.85
    }
    calibration {
      speed: 24.4
      acceleration: -8.232
    }
    calibration {
      speed: 24.4
      acceleration: -7.796
      command: 0.05
    }
    calibration {
      speed: 24.4
      acceleration: -7.22
      command: 0.1
    }
    calibration {
      speed: 24.4
      acceleration: -6.857
      command: 0.15
    }
    calibration {
      speed: 24.4
      acceleration: -6.37
      command: 0.2
    }
    calibration {
      speed: 24.4
      acceleration: -5.893
      command: 0.25
    }
    calibration {
      speed: 24.4
      acceleration: -5.228
      command: 0.3
    }
    calibration {
      speed: 24.4
      acceleration: -4.674
      command: 0.35
    }
    calibration {
      speed: 24.4
      acceleration: -4.172
      command: 0.4
    }
    calibration {
      speed: 24.4
      acceleration: -3.613
      command: 0.45
    }
    calibration {
      speed: 24.4
      acceleration: -3.029
      command: 0.5
    }
    calibration {
      speed: 24.4
      acceleration: -2.49
      command: 0.55
    }
    calibration {
      speed: 24.4
      acceleration: -1.854
      command: 0.6
    }
    calibration {
      speed: 24.4
      acceleration: -1.253
      command: 0.65
    }
    calibration {
      speed: 24.4
      acceleration: -0.636
      command: 0.7
    }
    calibration {
      speed: 24.4
      acceleration: -0.023
      command: 0.75
    }
    calibration {
      speed: 24.4
      acceleration: 0.607
      command: 0.8
    }
    calibration {
      speed: 24.4
      acceleration: 1.236
      command: 0.85
    }
    calibration {
      speed: 24.6
      acceleration: -8.347
    }
    calibration {
      speed: 24.6
      acceleration: -7.796
      command: 0.05
    }
    calibration {
      speed: 24.6
      acceleration: -7.285
      command: 0.1
    }
    calibration {
      speed: 24.6
      acceleration: -6.857
      command: 0.15
    }
    calibration {
      speed: 24.6
      acceleration: -6.423
      command: 0.2
    }
    calibration {
      speed: 24.6
      acceleration: -5.893
      command: 0.25
    }
    calibration {
      speed: 24.6
      acceleration: -5.307
      command: 0.3
    }
    calibration {
      speed: 24.6
      acceleration: -4.756
      command: 0.35
    }
    calibration {
      speed: 24.6
      acceleration: -4.205
      command: 0.4
    }
    calibration {
      speed: 24.6
      acceleration: -3.643
      command: 0.45
    }
    calibration {
      speed: 24.6
      acceleration: -3.105
      command: 0.5
    }
    calibration {
      speed: 24.6
      acceleration: -2.49
      command: 0.55
    }
    calibration {
      speed: 24.6
      acceleration: -1.888
      command: 0.6
    }
    calibration {
      speed: 24.6
      acceleration: -1.294
      command: 0.65
    }
    calibration {
      speed: 24.6
      acceleration: -0.658
      command: 0.7
    }
    calibration {
      speed: 24.6
      acceleration: -0.047
      command: 0.75
    }
    calibration {
      speed: 24.6
      acceleration: 0.588
      command: 0.8
    }
    calibration {
      speed: 24.6
      acceleration: 1.224
      command: 0.85
    }
    calibration {
      speed: 24.8
      acceleration: -8.347
    }
    calibration {
      speed: 24.8
      acceleration: -7.862
      command: 0.05
    }
    calibration {
      speed: 24.8
      acceleration: -7.425
      command: 0.1
    }
    calibration {
      speed: 24.8
      acceleration: -6.911
      command: 0.15
    }
    calibration {
      speed: 24.8
      acceleration: -6.423
      command: 0.2
    }
    calibration {
      speed: 24.8
      acceleration: -5.957
      command: 0.25
    }
    calibration {
      speed: 24.8
      acceleration: -5.307
      command: 0.3
    }
    calibration {
      speed: 24.8
      acceleration: -4.8
      command: 0.35
    }
    calibration {
      speed: 24.8
      acceleration: -4.274
      command: 0.4
    }
    calibration {
      speed: 24.8
      acceleration: -3.672
      command: 0.45
    }
    calibration {
      speed: 24.8
      acceleration: -3.131
      command: 0.5
    }
    calibration {
      speed: 24.8
      acceleration: -2.528
      command: 0.55
    }
    calibration {
      speed: 24.8
      acceleration: -1.924
      command: 0.6
    }
    calibration {
      speed: 24.8
      acceleration: -1.321
      command: 0.65
    }
    calibration {
      speed: 24.8
      acceleration: -0.688
      command: 0.7
    }
    calibration {
      speed: 24.8
      acceleration: -0.073
      command: 0.75
    }
    calibration {
      speed: 24.8
      acceleration: 0.565
      command: 0.8
    }
    calibration {
      speed: 24.8
      acceleration: 1.203
      command: 0.85
    }
    calibration {
      speed: 25.0
      acceleration: -8.472
    }
    calibration {
      speed: 25.0
      acceleration: -7.927
      command: 0.05
    }
    calibration {
      speed: 25.0
      acceleration: -7.425
      command: 0.1
    }
    calibration {
      speed: 25.0
      acceleration: -7.013
      command: 0.15
    }
    calibration {
      speed: 25.0
      acceleration: -6.473
      command: 0.2
    }
    calibration {
      speed: 25.0
      acceleration: -6.02
      command: 0.25
    }
    calibration {
      speed: 25.0
      acceleration: -5.349
      command: 0.3
    }
    calibration {
      speed: 25.0
      acceleration: -4.845
      command: 0.35
    }
    calibration {
      speed: 25.0
      acceleration: -4.31
      command: 0.4
    }
    calibration {
      speed: 25.0
      acceleration: -3.759
      command: 0.45
    }
    calibration {
      speed: 25.0
      acceleration: -3.183
      command: 0.5
    }
    calibration {
      speed: 25.0
      acceleration: -2.564
      command: 0.55
    }
    calibration {
      speed: 25.0
      acceleration: -1.949
      command: 0.6
    }
    calibration {
      speed: 25.0
      acceleration: -1.357
      command: 0.65
    }
    calibration {
      speed: 25.0
      acceleration: -0.715
      command: 0.7
    }
    calibration {
      speed: 25.0
      acceleration: -0.096
      command: 0.75
    }
    calibration {
      speed: 25.0
      acceleration: 0.541
      command: 0.8
    }
    calibration {
      speed: 25.0
      acceleration: 1.192
      command: 0.85
    }
    calibration {
      speed: 25.2
      acceleration: -8.472
    }
    calibration {
      speed: 25.2
      acceleration: -7.927
      command: 0.05
    }
    calibration {
      speed: 25.2
      acceleration: -7.502
      command: 0.1
    }
    calibration {
      speed: 25.2
      acceleration: -7.013
      command: 0.15
    }
    calibration {
      speed: 25.2
      acceleration: -6.571
      command: 0.2
    }
    calibration {
      speed: 25.2
      acceleration: -6.02
      command: 0.25
    }
    calibration {
      speed: 25.2
      acceleration: -5.436
      command: 0.3
    }
    calibration {
      speed: 25.2
      acceleration: -4.938
      command: 0.35
    }
    calibration {
      speed: 25.2
      acceleration: -4.347
      command: 0.4
    }
    calibration {
      speed: 25.2
      acceleration: -3.788
      command: 0.45
    }
    calibration {
      speed: 25.2
      acceleration: -3.209
      command: 0.5
    }
    calibration {
      speed: 25.2
      acceleration: -2.6
      command: 0.55
    }
    calibration {
      speed: 25.2
      acceleration: -1.99
      command: 0.6
    }
    calibration {
      speed: 25.2
      acceleration: -1.385
      command: 0.65
    }
    calibration {
      speed: 25.2
      acceleration: -0.744
      command: 0.7
    }
    calibration {
      speed: 25.2
      acceleration: -0.121
      command: 0.75
    }
    calibration {
      speed: 25.2
      acceleration: 0.517
      command: 0.8
    }
    calibration {
      speed: 25.2
      acceleration: 1.177
      command: 0.85
    }
    calibration {
      speed: 25.4
      acceleration: -8.604
    }
    calibration {
      speed: 25.4
      acceleration: -8.064
      command: 0.05
    }
    calibration {
      speed: 25.4
      acceleration: -7.502
      command: 0.1
    }
    calibration {
      speed: 25.4
      acceleration: -7.065
      command: 0.15
    }
    calibration {
      speed: 25.4
      acceleration: -6.571
      command: 0.2
    }
    calibration {
      speed: 25.4
      acceleration: -6.082
      command: 0.25
    }
    calibration {
      speed: 25.4
      acceleration: -5.481
      command: 0.3
    }
    calibration {
      speed: 25.4
      acceleration: -4.984
      command: 0.35
    }
    calibration {
      speed: 25.4
      acceleration: -4.386
      command: 0.4
    }
    calibration {
      speed: 25.4
      acceleration: -3.817
      command: 0.45
    }
    calibration {
      speed: 25.4
      acceleration: -3.234
      command: 0.5
    }
    calibration {
      speed: 25.4
      acceleration: -2.636
      command: 0.55
    }
    calibration {
      speed: 25.4
      acceleration: -2.017
      command: 0.6
    }
    calibration {
      speed: 25.4
      acceleration: -1.414
      command: 0.65
    }
    calibration {
      speed: 25.4
      acceleration: -0.769
      command: 0.7
    }
    calibration {
      speed: 25.4
      acceleration: -0.146
      command: 0.75
    }
    calibration {
      speed: 25.4
      acceleration: 0.497
      command: 0.8
    }
    calibration {
      speed: 25.4
      acceleration: 1.151
      command: 0.85
    }
    calibration {
      speed: 25.6
      acceleration: -8.604
    }
    calibration {
      speed: 25.6
      acceleration: -8.064
      command: 0.05
    }
    calibration {
      speed: 25.6
      acceleration: -7.673
      command: 0.1
    }
    calibration {
      speed: 25.6
      acceleration: -7.118
      command: 0.15
    }
    calibration {
      speed: 25.6
      acceleration: -6.621
      command: 0.2
    }
    calibration {
      speed: 25.6
      acceleration: -6.141
      command: 0.25
    }
    calibration {
      speed: 25.6
      acceleration: -5.481
      command: 0.3
    }
    calibration {
      speed: 25.6
      acceleration: -4.984
      command: 0.35
    }
    calibration {
      speed: 25.6
      acceleration: -4.47
      command: 0.4
    }
    calibration {
      speed: 25.6
      acceleration: -3.875
      command: 0.45
    }
    calibration {
      speed: 25.6
      acceleration: -3.26
      command: 0.5
    }
    calibration {
      speed: 25.6
      acceleration: -2.67
      command: 0.55
    }
    calibration {
      speed: 25.6
      acceleration: -2.075
      command: 0.6
    }
    calibration {
      speed: 25.6
      acceleration: -1.452
      command: 0.65
    }
    calibration {
      speed: 25.6
      acceleration: -0.792
      command: 0.7
    }
    calibration {
      speed: 25.6
      acceleration: -0.17
      command: 0.75
    }
    calibration {
      speed: 25.6
      acceleration: 0.479
      command: 0.8
    }
    calibration {
      speed: 25.6
      acceleration: 1.132
      command: 0.85
    }
    calibration {
      speed: 25.8
      acceleration: -8.741
    }
    calibration {
      speed: 25.8
      acceleration: -8.137
      command: 0.05
    }
    calibration {
      speed: 25.8
      acceleration: -7.673
      command: 0.1
    }
    calibration {
      speed: 25.8
      acceleration: -7.118
      command: 0.15
    }
    calibration {
      speed: 25.8
      acceleration: -6.621
      command: 0.2
    }
    calibration {
      speed: 25.8
      acceleration: -6.197
      command: 0.25
    }
    calibration {
      speed: 25.8
      acceleration: -5.527
      command: 0.3
    }
    calibration {
      speed: 25.8
      acceleration: -5.076
      command: 0.35
    }
    calibration {
      speed: 25.8
      acceleration: -4.515
      command: 0.4
    }
    calibration {
      speed: 25.8
      acceleration: -3.904
      command: 0.45
    }
    calibration {
      speed: 25.8
      acceleration: -3.334
      command: 0.5
    }
    calibration {
      speed: 25.8
      acceleration: -2.705
      command: 0.55
    }
    calibration {
      speed: 25.8
      acceleration: -2.103
      command: 0.6
    }
    calibration {
      speed: 25.8
      acceleration: -1.482
      command: 0.65
    }
    calibration {
      speed: 25.8
      acceleration: -0.822
      command: 0.7
    }
    calibration {
      speed: 25.8
      acceleration: -0.195
      command: 0.75
    }
    calibration {
      speed: 25.8
      acceleration: 0.456
      command: 0.8
    }
    calibration {
      speed: 25.8
      acceleration: 1.118
      command: 0.85
    }
    calibration {
      speed: 26.0
      acceleration: -8.741
    }
    calibration {
      speed: 26.0
      acceleration: -8.137
      command: 0.05
    }
    calibration {
      speed: 26.0
      acceleration: -7.765
      command: 0.1
    }
    calibration {
      speed: 26.0
      acceleration: -7.177
      command: 0.15
    }
    calibration {
      speed: 26.0
      acceleration: -6.674
      command: 0.2
    }
    calibration {
      speed: 26.0
      acceleration: -6.197
      command: 0.25
    }
    calibration {
      speed: 26.0
      acceleration: -5.621
      command: 0.3
    }
    calibration {
      speed: 26.0
      acceleration: -5.12
      command: 0.35
    }
    calibration {
      speed: 26.0
      acceleration: -4.56
      command: 0.4
    }
    calibration {
      speed: 26.0
      acceleration: -3.934
      command: 0.45
    }
    calibration {
      speed: 26.0
      acceleration: -3.359
      command: 0.5
    }
    calibration {
      speed: 26.0
      acceleration: -2.722
      command: 0.55
    }
    calibration {
      speed: 26.0
      acceleration: -2.132
      command: 0.6
    }
    calibration {
      speed: 26.0
      acceleration: -1.513
      command: 0.65
    }
    calibration {
      speed: 26.0
      acceleration: -0.849
      command: 0.7
    }
    calibration {
      speed: 26.0
      acceleration: -0.219
      command: 0.75
    }
    calibration {
      speed: 26.0
      acceleration: 0.438
      command: 0.8
    }
    calibration {
      speed: 26.0
      acceleration: 1.097
      command: 0.85
    }
    calibration {
      speed: 26.2
      acceleration: -8.874
    }
    calibration {
      speed: 26.2
      acceleration: -8.213
      command: 0.05
    }
    calibration {
      speed: 26.2
      acceleration: -7.86
      command: 0.1
    }
    calibration {
      speed: 26.2
      acceleration: -7.177
      command: 0.15
    }
    calibration {
      speed: 26.2
      acceleration: -6.732
      command: 0.2
    }
    calibration {
      speed: 26.2
      acceleration: -6.249
      command: 0.25
    }
    calibration {
      speed: 26.2
      acceleration: -5.667
      command: 0.3
    }
    calibration {
      speed: 26.2
      acceleration: -5.161
      command: 0.35
    }
    calibration {
      speed: 26.2
      acceleration: -4.607
      command: 0.4
    }
    calibration {
      speed: 26.2
      acceleration: -3.964
      command: 0.45
    }
    calibration {
      speed: 26.2
      acceleration: -3.384
      command: 0.5
    }
    calibration {
      speed: 26.2
      acceleration: -2.775
      command: 0.55
    }
    calibration {
      speed: 26.2
      acceleration: -2.16
      command: 0.6
    }
    calibration {
      speed: 26.2
      acceleration: -1.534
      command: 0.65
    }
    calibration {
      speed: 26.2
      acceleration: -0.878
      command: 0.7
    }
    calibration {
      speed: 26.2
      acceleration: -0.243
      command: 0.75
    }
    calibration {
      speed: 26.2
      acceleration: 0.414
      command: 0.8
    }
    calibration {
      speed: 26.2
      acceleration: 1.079
      command: 0.85
    }
    calibration {
      speed: 26.4
      acceleration: -8.874
    }
    calibration {
      speed: 26.4
      acceleration: -8.213
      command: 0.05
    }
    calibration {
      speed: 26.4
      acceleration: -7.86
      command: 0.1
    }
    calibration {
      speed: 26.4
      acceleration: -7.241
      command: 0.15
    }
    calibration {
      speed: 26.4
      acceleration: -6.732
      command: 0.2
    }
    calibration {
      speed: 26.4
      acceleration: -6.342
      command: 0.25
    }
    calibration {
      speed: 26.4
      acceleration: -5.667
      command: 0.3
    }
    calibration {
      speed: 26.4
      acceleration: -5.201
      command: 0.35
    }
    calibration {
      speed: 26.4
      acceleration: -4.607
      command: 0.4
    }
    calibration {
      speed: 26.4
      acceleration: -3.98
      command: 0.45
    }
    calibration {
      speed: 26.4
      acceleration: -3.435
      command: 0.5
    }
    calibration {
      speed: 26.4
      acceleration: -2.793
      command: 0.55
    }
    calibration {
      speed: 26.4
      acceleration: -2.189
      command: 0.6
    }
    calibration {
      speed: 26.4
      acceleration: -1.565
      command: 0.65
    }
    calibration {
      speed: 26.4
      acceleration: -0.908
      command: 0.7
    }
    calibration {
      speed: 26.4
      acceleration: -0.268
      command: 0.75
    }
    calibration {
      speed: 26.4
      acceleration: 0.394
      command: 0.8
    }
    calibration {
      speed: 26.4
      acceleration: 1.062
      command: 0.85
    }
    calibration {
      speed: 26.6
      acceleration: -9.1
    }
    calibration {
      speed: 26.6
      acceleration: -8.291
      command: 0.05
    }
    calibration {
      speed: 26.6
      acceleration: -7.954
      command: 0.1
    }
    calibration {
      speed: 26.6
      acceleration: -7.241
      command: 0.15
    }
    calibration {
      speed: 26.6
      acceleration: -6.795
      command: 0.2
    }
    calibration {
      speed: 26.6
      acceleration: -6.342
      command: 0.25
    }
    calibration {
      speed: 26.6
      acceleration: -5.755
      command: 0.3
    }
    calibration {
      speed: 26.6
      acceleration: -5.238
      command: 0.35
    }
    calibration {
      speed: 26.6
      acceleration: -4.653
      command: 0.4
    }
    calibration {
      speed: 26.6
      acceleration: -4.028
      command: 0.45
    }
    calibration {
      speed: 26.6
      acceleration: -3.461
      command: 0.5
    }
    calibration {
      speed: 26.6
      acceleration: -2.831
      command: 0.55
    }
    calibration {
      speed: 26.6
      acceleration: -2.231
      command: 0.6
    }
    calibration {
      speed: 26.6
      acceleration: -1.607
      command: 0.65
    }
    calibration {
      speed: 26.6
      acceleration: -0.936
      command: 0.7
    }
    calibration {
      speed: 26.6
      acceleration: -0.294
      command: 0.75
    }
    calibration {
      speed: 26.6
      acceleration: 0.371
      command: 0.8
    }
    calibration {
      speed: 26.6
      acceleration: 1.045
      command: 0.85
    }
    calibration {
      speed: 26.8
      acceleration: -9.1
    }
    calibration {
      speed: 26.8
      acceleration: -8.291
      command: 0.05
    }
    calibration {
      speed: 26.8
      acceleration: -7.954
      command: 0.1
    }
    calibration {
      speed: 26.8
      acceleration: -7.311
      command: 0.15
    }
    calibration {
      speed: 26.8
      acceleration: -6.865
      command: 0.2
    }
    calibration {
      speed: 26.8
      acceleration: -6.384
      command: 0.25
    }
    calibration {
      speed: 26.8
      acceleration: -5.797
      command: 0.3
    }
    calibration {
      speed: 26.8
      acceleration: -5.238
      command: 0.35
    }
    calibration {
      speed: 26.8
      acceleration: -4.7
      command: 0.4
    }
    calibration {
      speed: 26.8
      acceleration: -4.096
      command: 0.45
    }
    calibration {
      speed: 26.8
      acceleration: -3.489
      command: 0.5
    }
    calibration {
      speed: 26.8
      acceleration: -2.869
      command: 0.55
    }
    calibration {
      speed: 26.8
      acceleration: -2.258
      command: 0.6
    }
    calibration {
      speed: 26.8
      acceleration: -1.627
      command: 0.65
    }
    calibration {
      speed: 26.8
      acceleration: -0.96
      command: 0.7
    }
    calibration {
      speed: 26.8
      acceleration: -0.32
      command: 0.75
    }
    calibration {
      speed: 26.8
      acceleration: 0.35
      command: 0.8
    }
    calibration {
      speed: 26.8
      acceleration: 1.028
      command: 0.85
    }
    calibration {
      speed: 27.0
      acceleration: -9.174
    }
    calibration {
      speed: 27.0
      acceleration: -8.371
      command: 0.05
    }
    calibration {
      speed: 27.0
      acceleration: -8.044
      command: 0.1
    }
    calibration {
      speed: 27.0
      acceleration: -7.389
      command: 0.15
    }
    calibration {
      speed: 27.0
      acceleration: -6.865
      command: 0.2
    }
    calibration {
      speed: 27.0
      acceleration: -6.422
      command: 0.25
    }
    calibration {
      speed: 27.0
      acceleration: -5.873
      command: 0.3
    }
    calibration {
      speed: 27.0
      acceleration: -5.272
      command: 0.35
    }
    calibration {
      speed: 27.0
      acceleration: -4.787
      command: 0.4
    }
    calibration {
      speed: 27.0
      acceleration: -4.131
      command: 0.45
    }
    calibration {
      speed: 27.0
      acceleration: -3.548
      command: 0.5
    }
    calibration {
      speed: 27.0
      acceleration: -2.929
      command: 0.55
    }
    calibration {
      speed: 27.0
      acceleration: -2.286
      command: 0.6
    }
    calibration {
      speed: 27.0
      acceleration: -1.654
      command: 0.65
    }
    calibration {
      speed: 27.0
      acceleration: -0.986
      command: 0.7
    }
    calibration {
      speed: 27.0
      acceleration: -0.341
      command: 0.75
    }
    calibration {
      speed: 27.0
      acceleration: 0.328
      command: 0.8
    }
    calibration {
      speed: 27.0
      acceleration: 1.008
      command: 0.85
    }
    calibration {
      speed: 27.2
      acceleration: -9.174
    }
    calibration {
      speed: 27.2
      acceleration: -8.371
      command: 0.05
    }
    calibration {
      speed: 27.2
      acceleration: -8.044
      command: 0.1
    }
    calibration {
      speed: 27.2
      acceleration: -7.389
      command: 0.15
    }
    calibration {
      speed: 27.2
      acceleration: -6.941
      command: 0.2
    }
    calibration {
      speed: 27.2
      acceleration: -6.422
      command: 0.25
    }
    calibration {
      speed: 27.2
      acceleration: -5.873
      command: 0.3
    }
    calibration {
      speed: 27.2
      acceleration: -5.338
      command: 0.35
    }
    calibration {
      speed: 27.2
      acceleration: -4.827
      command: 0.4
    }
    calibration {
      speed: 27.2
      acceleration: -4.202
      command: 0.45
    }
    calibration {
      speed: 27.2
      acceleration: -3.58
      command: 0.5
    }
    calibration {
      speed: 27.2
      acceleration: -2.95
      command: 0.55
    }
    calibration {
      speed: 27.2
      acceleration: -2.329
      command: 0.6
    }
    calibration {
      speed: 27.2
      acceleration: -1.672
      command: 0.65
    }
    calibration {
      speed: 27.2
      acceleration: -1.016
      command: 0.7
    }
    calibration {
      speed: 27.2
      acceleration: -0.367
      command: 0.75
    }
    calibration {
      speed: 27.2
      acceleration: 0.306
      command: 0.8
    }
    calibration {
      speed: 27.2
      acceleration: 0.986
      command: 0.85
    }
    calibration {
      speed: 27.4
      acceleration: -9.174
    }
    calibration {
      speed: 27.4
      acceleration: -8.449
      command: 0.05
    }
    calibration {
      speed: 27.4
      acceleration: -8.123
      command: 0.1
    }
    calibration {
      speed: 27.4
      acceleration: -7.559
      command: 0.15
    }
    calibration {
      speed: 27.4
      acceleration: -6.941
      command: 0.2
    }
    calibration {
      speed: 27.4
      acceleration: -6.457
      command: 0.25
    }
    calibration {
      speed: 27.4
      acceleration: -5.909
      command: 0.3
    }
    calibration {
      speed: 27.4
      acceleration: -5.371
      command: 0.35
    }
    calibration {
      speed: 27.4
      acceleration: -4.863
      command: 0.4
    }
    calibration {
      speed: 27.4
      acceleration: -4.238
      command: 0.45
    }
    calibration {
      speed: 27.4
      acceleration: -3.613
      command: 0.5
    }
    calibration {
      speed: 27.4
      acceleration: -3.011
      command: 0.55
    }
    calibration {
      speed: 27.4
      acceleration: -2.358
      command: 0.6
    }
    calibration {
      speed: 27.4
      acceleration: -1.706
      command: 0.65
    }
    calibration {
      speed: 27.4
      acceleration: -1.046
      command: 0.7
    }
    calibration {
      speed: 27.4
      acceleration: -0.389
      command: 0.75
    }
    calibration {
      speed: 27.4
      acceleration: 0.288
      command: 0.8
    }
    calibration {
      speed: 27.4
      acceleration: 0.971
      command: 0.85
    }
    calibration {
      speed: 27.6
      acceleration: -9.174
    }
    calibration {
      speed: 27.6
      acceleration: -8.449
      command: 0.05
    }
    calibration {
      speed: 27.6
      acceleration: -8.123
      command: 0.1
    }
    calibration {
      speed: 27.6
      acceleration: -7.559
      command: 0.15
    }
    calibration {
      speed: 27.6
      acceleration: -7.022
      command: 0.2
    }
    calibration {
      speed: 27.6
      acceleration: -6.492
      command: 0.25
    }
    calibration {
      speed: 27.6
      acceleration: -5.975
      command: 0.3
    }
    calibration {
      speed: 27.6
      acceleration: -5.405
      command: 0.35
    }
    calibration {
      speed: 27.6
      acceleration: -4.895
      command: 0.4
    }
    calibration {
      speed: 27.6
      acceleration: -4.273
      command: 0.45
    }
    calibration {
      speed: 27.6
      acceleration: -3.717
      command: 0.5
    }
    calibration {
      speed: 27.6
      acceleration: -3.032
      command: 0.55
    }
    calibration {
      speed: 27.6
      acceleration: -2.388
      command: 0.6
    }
    calibration {
      speed: 27.6
      acceleration: -1.733
      command: 0.65
    }
    calibration {
      speed: 27.6
      acceleration: -1.066
      command: 0.7
    }
    calibration {
      speed: 27.6
      acceleration: -0.415
      command: 0.75
    }
    calibration {
      speed: 27.6
      acceleration: 0.265
      command: 0.8
    }
    calibration {
      speed: 27.6
      acceleration: 0.956
      command: 0.85
    }
    calibration {
      speed: 27.8
      acceleration: -9.207
    }
    calibration {
      speed: 27.8
      acceleration: -8.583
      command: 0.05
    }
    calibration {
      speed: 27.8
      acceleration: -8.186
      command: 0.1
    }
    calibration {
      speed: 27.8
      acceleration: -7.647
      command: 0.15
    }
    calibration {
      speed: 27.8
      acceleration: -7.19
      command: 0.2
    }
    calibration {
      speed: 27.8
      acceleration: -6.492
      command: 0.25
    }
    calibration {
      speed: 27.8
      acceleration: -5.975
      command: 0.3
    }
    calibration {
      speed: 27.8
      acceleration: -5.405
      command: 0.35
    }
    calibration {
      speed: 27.8
      acceleration: -4.924
      command: 0.4
    }
    calibration {
      speed: 27.8
      acceleration: -4.306
      command: 0.45
    }
    calibration {
      speed: 27.8
      acceleration: -3.751
      command: 0.5
    }
    calibration {
      speed: 27.8
      acceleration: -3.053
      command: 0.55
    }
    calibration {
      speed: 27.8
      acceleration: -2.418
      command: 0.6
    }
    calibration {
      speed: 27.8
      acceleration: -1.762
      command: 0.65
    }
    calibration {
      speed: 27.8
      acceleration: -1.097
      command: 0.7
    }
    calibration {
      speed: 27.8
      acceleration: -0.441
      command: 0.75
    }
    calibration {
      speed: 27.8
      acceleration: 0.243
      command: 0.8
    }
    calibration {
      speed: 27.8
      acceleration: 0.937
      command: 0.85
    }
    calibration {
      speed: 28.0
      acceleration: 0.222
      command: 0.8
    }
    calibration {
      speed: 28.0
      acceleration: 0.916
      command: 0.85
    }
    calibration {
      speed: 28.2
      acceleration: 0.2
      command: 0.8
    }
    calibration {
      speed: 28.2
      acceleration: 0.902
      command: 0.85
    }
    calibration {
      speed: 28.4
      acceleration: 0.179
      command: 0.8
    }
    calibration {
      speed: 28.4
      acceleration: 0.884
      command: 0.85
    }
    calibration {
      speed: 28.6
      acceleration: 0.158
      command: 0.8
    }
    calibration {
      speed: 28.6
      acceleration: 0.859
      command: 0.85
    }
    calibration {
      speed: 28.8
      acceleration: 0.137
      command: 0.8
    }
    calibration {
      speed: 28.8
      acceleration: 0.844
      command: 0.85
    }
    calibration {
      speed: 29.0
      acceleration: 0.116
      command: 0.8
    }
    calibration {
      speed: 29.0
      acceleration: 0.825
      command: 0.85
    }
    calibration {
      speed: 29.2
      acceleration: 0.094
      command: 0.8
    }
    calibration {
      speed: 29.2
      acceleration: 0.813
      command: 0.85
    }
    calibration {
      speed: 29.4
      acceleration: 0.073
      command: 0.8
    }
    calibration {
      speed: 29.4
      acceleration: 0.791
      command: 0.85
    }
    calibration {
      speed: 29.6
      acceleration: 0.051
      command: 0.8
    }
    calibration {
      speed: 29.6
      acceleration: 0.771
      command: 0.85
    }
    calibration {
      speed: 29.8
      acceleration: 0.03
      command: 0.8
    }
    calibration {
      speed: 29.8
      acceleration: 0.755
      command: 0.85
    }
    calibration {
      speed: 30.0
      acceleration: 0.009
      command: 0.8
    }
    calibration {
      speed: 30.0
      acceleration: 0.734
      command: 0.85
    }
    calibration {
      speed: 30.2
      acceleration: 0.72
      command: 0.85
    }
    calibration {
      speed: 30.4
      acceleration: 0.701
      command: 0.85
    }
    calibration {
      speed: 30.6
      acceleration: 0.682
      command: 0.85
    }
    calibration {
      speed: 30.8
      acceleration: 0.663
      command: 0.85
    }
    calibration {
      speed: 31.0
      acceleration: 0.647
      command: 0.85
    }
    calibration {
      speed: 31.2
      acceleration: 0.628
      command: 0.85
    }
    calibration {
      speed: 31.4
      acceleration: 0.614
      command: 0.85
    }
    calibration {
      speed: 31.6
      acceleration: 0.593
      command: 0.85
    }
    calibration {
      speed: 31.8
      acceleration: 0.577
      command: 0.85
    }
    calibration {
      speed: 32.0
      acceleration: 0.559
      command: 0.85
    }
    calibration {
      speed: 32.2
      acceleration: 0.54
      command: 0.85
    }
    calibration {
      speed: 32.4
      acceleration: 0.522
      command: 0.85
    }
    calibration {
      speed: 32.6
      acceleration: 0.503
      command: 0.85
    }
    calibration {
      speed: 32.8
      acceleration: 0.485
      command: 0.85
    }
    calibration {
      speed: 33.0
      acceleration: 0.465
      command: 0.85
    }
    calibration {
      speed: 33.2
      acceleration: 0.45
      command: 0.85
    }
    calibration {
      speed: 33.4
      acceleration: 0.431
      command: 0.85
    }
    calibration {
      speed: 33.6
      acceleration: 0.414
      command: 0.85
    }
    calibration {
      speed: 33.8
      acceleration: 0.395
      command: 0.85
    }
    calibration {
      speed: 34.0
      acceleration: 0.378
      command: 0.85
    }
    calibration {
      speed: 34.2
      acceleration: 0.358
      command: 0.85
    }
    calibration {
      speed: 34.4
      acceleration: 0.342
      command: 0.85
    }
    calibration {
      speed: 34.6
      acceleration: 0.322
      command: 0.85
    }
    calibration {
      speed: 34.8
      acceleration: 0.306
      command: 0.85
    }
  }
}
mpc_controller_conf {
  ts: 0.02
  cf: 155494.663
  cr: 155494.663
  mass_fl: 520
  mass_fr: 520
  mass_rl: 520
  mass_rr: 520
  eps: 0.01
  matrix_q: 3.0
  matrix_q: 0.0
  matrix_q: 35
  matrix_q: 0.0
  matrix_q: 50.0
  matrix_q: 10.0
  matrix_r: 3.25
  matrix_r: 1.0
  cutoff_freq: 10
  mean_filter_window_size: 10
  max_iteration: 150
  max_lateral_acceleration: 5.0
  standstill_acceleration: -0.3
  brake_minimum_action: 0.0
  throttle_minimum_action: 0.0
  enable_mpc_feedforward_compensation: false
  lat_err_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 1.0
    }
    scheduler {
      speed: 5.0
      ratio: 1.0
    }
    scheduler {
      speed: 10.0
      ratio: 0.4
    }
    scheduler {
      speed: 15.0
      ratio: 0.3
    }
    scheduler {
      speed: 20.0
      ratio: 0.2
    }
    scheduler {
      speed: 25.0
      ratio: 0.1
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 1.0
    }
    scheduler {
      speed: 5.0
      ratio: 1.0
    }
    scheduler {
      speed: 10.0
      ratio: 0.5
    }
    scheduler {
      speed: 15.0
      ratio: 0.4
    }
    scheduler {
      speed: 20.0
      ratio: 0.35
    }
    scheduler {
      speed: 25.0
      ratio: 0.35
    }
  }
  feedforwardterm_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 0.7
    }
    scheduler {
      speed: 5.0
      ratio: 0.05
    }
    scheduler {
      speed: 10.0
      ratio: 0.0
    }
    scheduler {
      speed: 15.0
      ratio: 0.0
    }
    scheduler {
      speed: 20.0
      ratio: 0.0
    }
    scheduler {
      speed: 25.0
      ratio: 0.0
    }
  }
  steer_weight_gain_scheduler {
    scheduler {
      speed: 2.5
      ratio: 1.0
    }
    scheduler {
      speed: 5.0
      ratio: 1.0
    }
    scheduler {
      speed: 10.0
      ratio: 1.1
    }
    scheduler {
      speed: 15.0
      ratio: 1.1
    }
    scheduler {
      speed: 20.0
      ratio: 1.35
    }
    scheduler {
      speed: 25.0
      ratio: 1.55
    }
  }
  calibration_table {
    calibration {
      speed: 0.0
      acceleration: -1.43
      command: -35.0
    }
    calibration {
      speed: 0.0
      acceleration: -1.28
      command: -27.0
    }
    calibration {
      speed: 0.0
      acceleration: -1.17
      command: -25.0
    }
    calibration {
      speed: 0.0
      acceleration: -1.02
      command: -30.0
    }
    calibration {
      speed: 0.0
      acceleration: -0.48
      command: -27.5
    }
    calibration {
      speed: 0.0
      acceleration: 0.09
      command: -20.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.32
      command: -15.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.35
      command: -17.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.37
      command: 17.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.39
      command: 15.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.41
      command: -13.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.72
      command: 20.0
    }
    calibration {
      speed: 0.0
      acceleration: 0.97
      command: 22.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.4
      command: 25.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.43
      command: 27.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.53
      command: 30.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.65
      command: 35.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.79
      command: 40.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.89
      command: 45.0
    }
    calibration {
      speed: 0.0
      acceleration: 1.94
      command: 50.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.03
      command: 55.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.08
      command: 65.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.11
      command: 60.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.3
      command: 80.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.33
      command: 70.0
    }
    calibration {
      speed: 0.0
      acceleration: 2.34
      command: 75.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.87
      command: -35.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.41
      command: -27.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.25
      command: -30.0
    }
    calibration {
      speed: 0.2
      acceleration: -1.22
      command: -25.0
    }
    calibration {
      speed: 0.2
      acceleration: -0.87
      command: -33.0
    }
    calibration {
      speed: 0.2
      acceleration: -0.48
      command: -22.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.09
      command: -20.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.32
      command: -15.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.36
      command: -17.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.38
      command: 17.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.39
      command: 15.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.41
      command: -13.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.74
      command: 20.0
    }
    calibration {
      speed: 0.2
      acceleration: 0.98
      command: 22.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.43
      command: 25.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.48
      command: 27.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.59
      command: 30.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.72
      command: 35.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.87
      command: 40.0
    }
    calibration {
      speed: 0.2
      acceleration: 1.98
      command: 45.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.03
      command: 50.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.15
      command: 55.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.22
      command: 65.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.23
      command: 60.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.44
      command: 80.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.45
      command: 70.0
    }
    calibration {
      speed: 0.2
      acceleration: 2.47
      command: 75.0
    }
    calibration {
      speed: 0.4
      acceleration: -6.89
      command: -35.0
    }
    calibration {
      speed: 0.4
      acceleration: -5.78
      command: -33.0
    }
    calibration {
      speed: 0.4
      acceleration: -4.19
      command: -30.0
    }
    calibration {
      speed: 0.4
      acceleration: -2.88
      command: -27.0
    }
    calibration {
      speed: 0.4
      acceleration: -1.77
      command: -25.0
    }
    calibration {
      speed: 0.4
      acceleration: -0.48
      command: -22.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.01
      command: -20.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.31
      command: -15.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.35
      command: 15.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.38
      command: -17.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.4
      command: -13.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.43
      command: 17.0
    }
    calibration {
      speed: 0.4
      acceleration: 0.82
      command: 20.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.07
      command: 22.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.57
      command: 25.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.66
      command: 27.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.78
      command: 30.0
    }
    calibration {
      speed: 0.4
      acceleration: 1.94
      command: 35.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.12
      command: 40.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.22
      command: 45.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.24
      command: 50.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.43
      command: 55.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.47
      command: 60.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.54
      command: 65.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.67
      command: 75.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.68
      command: 70.0
    }
    calibration {
      speed: 0.4
      acceleration: 2.71
      command: 80.0
    }
    calibration {
      speed: 0.6
      acceleration: -8.63
      command: -35.0
    }
    calibration {
      speed: 0.6
      acceleration: -7.54
      command: -33.0
    }
    calibration {
      speed: 0.6
      acceleration: -5.04
      command: -30.0
    }
    calibration {
      speed: 0.6
      acceleration: -2.98
      command: -27.0
    }
    calibration {
      speed: 0.6
      acceleration: -1.66
      command: -25.0
    }
    calibration {
      speed: 0.6
      acceleration: -0.5
      command: -22.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.02
      command: -20.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.31
      command: -15.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.35
      command: -13.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.36
      command: 15.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.38
      command: -17.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.43
      command: 17.0
    }
    calibration {
      speed: 0.6
      acceleration: 0.85
      command: 20.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.18
      command: 22.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.71
      command: 25.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.85
      command: 27.0
    }
    calibration {
      speed: 0.6
      acceleration: 1.96
      command: 30.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.16
      command: 35.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.34
      command: 40.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.37
      command: 50.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.4
      command: 45.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.57
      command: 60.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.61
      command: 55.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.72
      command: 65.0
    }
    calibration {
      speed: 0.6
      acceleration: 2.77
      command: 72.5
    }
    calibration {
      speed: 0.6
      acceleration: 2.8
      command: 80.0
    }
    calibration {
      speed: 0.8
      acceleration: -9.18
      command: -35.0
    }
    calibration {
      speed: 0.8
      acceleration: -8.26
      command: -33.0
    }
    calibration {
      speed: 0.8
      acceleration: -5.13
      command: -30.0
    }
    calibration {
      speed: 0.8
      acceleration: -2.78
      command: -27.0
    }
    calibration {
      speed: 0.8
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 0.8
      acceleration: -0.48
      command: -22.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.01
      command: -20.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.29
      command: -15.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.31
      command: 1.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.35
      command: -17.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.37
      command: 17.0
    }
    calibration {
      speed: 0.8
      acceleration: 0.87
      command: 20.0
    }
    calibration {
      speed: 0.8
      acceleration: 1.23
      command: 22.0
    }
    calibration {
      speed: 0.8
      acceleration: 1.82
      command: 25.0
    }
    calibration {
      speed: 0.8
      acceleration: 1.99
      command: 27.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.1
      command: 30.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.34
      command: 35.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.46
      command: 50.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.52
      command: 42.5
    }
    calibration {
      speed: 0.8
      acceleration: 2.61
      command: 60.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.7
      command: 55.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.73
      command: 70.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.74
      command: 75.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.76
      command: 65.0
    }
    calibration {
      speed: 0.8
      acceleration: 2.78
      command: 80.0
    }
    calibration {
      speed: 1.0
      acceleration: -9.17
      command: -35.0
    }
    calibration {
      speed: 1.0
      acceleration: -8.44
      command: -33.0
    }
    calibration {
      speed: 1.0
      acceleration: -4.97
      command: -30.0
    }
    calibration {
      speed: 1.0
      acceleration: -2.72
      command: -27.0
    }
    calibration {
      speed: 1.0
      acceleration: -1.74
      command: -25.0
    }
    calibration {
      speed: 1.0
      acceleration: -0.5
      command: -22.0
    }
    calibration {
      speed: 1.0
      acceleration: -0.07
      command: -20.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.21
      command: -15.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.27
      command: -13.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.28
      command: -1.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.32
      command: 17.0
    }
    calibration {
      speed: 1.0
      acceleration: 0.89
      command: 20.0
    }
    calibration {
      speed: 1.0
      acceleration: 1.25
      command: 22.0
    }
    calibration {
      speed: 1.0
      acceleration: 1.89
      command: 25.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.09
      command: 27.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.2
      command: 30.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.49
      command: 35.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.56
      command: 50.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.62
      command: 45.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.66
      command: 40.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.67
      command: 60.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.7
      command: 70.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.73
      command: 75.0
    }
    calibration {
      speed: 1.0
      acceleration: 2.76
      command: 66.6666666667
    }
    calibration {
      speed: 1.2
      acceleration: -9.07
      command: -35.0
    }
    calibration {
      speed: 1.2
      acceleration: -8.35
      command: -33.0
    }
    calibration {
      speed: 1.2
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 1.2
      acceleration: -2.78
      command: -27.0
    }
    calibration {
      speed: 1.2
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 1.2
      acceleration: -0.53
      command: -22.0
    }
    calibration {
      speed: 1.2
      acceleration: -0.09
      command: -20.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.14
      command: -15.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.19
      command: 15.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.21
      command: -13.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.22
      command: -17.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.23
      command: 17.0
    }
    calibration {
      speed: 1.2
      acceleration: 0.87
      command: 20.0
    }
    calibration {
      speed: 1.2
      acceleration: 1.27
      command: 22.0
    }
    calibration {
      speed: 1.2
      acceleration: 1.95
      command: 25.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.16
      command: 27.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.27
      command: 30.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.59
      command: 35.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.68
      command: 50.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.71
      command: 45.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.74
      command: 70.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.77
      command: 40.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.78
      command: 60.0
    }
    calibration {
      speed: 1.2
      acceleration: 2.8
      command: 73.3333333333
    }
    calibration {
      speed: 1.2
      acceleration: 2.82
      command: 55.0
    }
    calibration {
      speed: 1.4
      acceleration: -8.98
      command: -35.0
    }
    calibration {
      speed: 1.4
      acceleration: -8.12
      command: -33.0
    }
    calibration {
      speed: 1.4
      acceleration: -4.67
      command: -30.0
    }
    calibration {
      speed: 1.4
      acceleration: -2.82
      command: -27.0
    }
    calibration {
      speed: 1.4
      acceleration: -1.69
      command: -25.0
    }
    calibration {
      speed: 1.4
      acceleration: -0.54
      command: -22.0
    }
    calibration {
      speed: 1.4
      acceleration: -0.14
      command: -20.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.11
      command: -15.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.14
      command: -17.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.15
      command: 1.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.18
      command: 17.0
    }
    calibration {
      speed: 1.4
      acceleration: 0.87
      command: 20.0
    }
    calibration {
      speed: 1.4
      acceleration: 1.3
      command: 22.0
    }
    calibration {
      speed: 1.4
      acceleration: 1.99
      command: 25.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.23
      command: 27.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.35
      command: 30.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.68
      command: 35.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.77
      command: 50.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.8
      command: 45.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.82
      command: 70.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.84
      command: 40.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.87
      command: 60.0
    }
    calibration {
      speed: 1.4
      acceleration: 2.88
      command: 72.5
    }
    calibration {
      speed: 1.4
      acceleration: 2.89
      command: 65.0
    }
    calibration {
      speed: 1.6
      acceleration: -8.91
      command: -35.0
    }
    calibration {
      speed: 1.6
      acceleration: -7.88
      command: -33.0
    }
    calibration {
      speed: 1.6
      acceleration: -4.66
      command: -30.0
    }
    calibration {
      speed: 1.6
      acceleration: -2.79
      command: -27.0
    }
    calibration {
      speed: 1.6
      acceleration: -1.69
      command: -25.0
    }
    calibration {
      speed: 1.6
      acceleration: -0.56
      command: -22.0
    }
    calibration {
      speed: 1.6
      acceleration: -0.2
      command: -20.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.04
      command: -15.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.05
      command: 15.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.08
      command: -17.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.09
      command: -13.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.13
      command: 17.0
    }
    calibration {
      speed: 1.6
      acceleration: 0.8
      command: 20.0
    }
    calibration {
      speed: 1.6
      acceleration: 1.29
      command: 22.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.03
      command: 25.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.29
      command: 27.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.41
      command: 30.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.74
      command: 35.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.85
      command: 47.5
    }
    calibration {
      speed: 1.6
      acceleration: 2.89
      command: 70.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.9
      command: 40.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.94
      command: 60.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.95
      command: 70.0
    }
    calibration {
      speed: 1.6
      acceleration: 2.96
      command: 75.0
    }
    calibration {
      speed: 1.8
      acceleration: -8.81
      command: -35.0
    }
    calibration {
      speed: 1.8
      acceleration: -7.64
      command: -33.0
    }
    calibration {
      speed: 1.8
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 1.8
      acceleration: -2.72
      command: -27.0
    }
    calibration {
      speed: 1.8
      acceleration: -1.68
      command: -25.0
    }
    calibration {
      speed: 1.8
      acceleration: -0.58
      command: -22.0
    }
    calibration {
      speed: 1.8
      acceleration: -0.24
      command: -20.0
    }
    calibration {
      speed: 1.8
      acceleration: -0.02
      command: -15.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.02
      command: -1.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.03
      command: -13.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.08
      command: 17.0
    }
    calibration {
      speed: 1.8
      acceleration: 0.78
      command: 20.0
    }
    calibration {
      speed: 1.8
      acceleration: 1.25
      command: 22.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.05
      command: 25.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.34
      command: 27.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.48
      command: 30.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.8
      command: 35.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.9
      command: 47.5
    }
    calibration {
      speed: 1.8
      acceleration: 2.93
      command: 70.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.95
      command: 40.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.96
      command: 80.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.97
      command: 60.0
    }
    calibration {
      speed: 1.8
      acceleration: 2.98
      command: 67.5
    }
    calibration {
      speed: 2.0
      acceleration: -8.72
      command: -35.0
    }
    calibration {
      speed: 2.0
      acceleration: -7.47
      command: -33.0
    }
    calibration {
      speed: 2.0
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 2.0
      acceleration: -2.66
      command: -27.0
    }
    calibration {
      speed: 2.0
      acceleration: -1.67
      command: -25.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.62
      command: -22.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.07
      command: 15.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.05
      command: -17.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.03
      command: -13.0
    }
    calibration {
      speed: 2.0
      acceleration: -0.02
      command: -15.0
    }
    calibration {
      speed: 2.0
      acceleration: 0.05
      command: 17.0
    }
    calibration {
      speed: 2.0
      acceleration: 0.7
      command: 20.0
    }
    calibration {
      speed: 2.0
      acceleration: 1.22
      command: 22.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.05
      command: 25.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.36
      command: 27.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.51
      command: 30.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.85
      command: 35.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.92
      command: 70.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.95
      command: 58.3333333333
    }
    calibration {
      speed: 2.0
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.98
      command: 55.0
    }
    calibration {
      speed: 2.0
      acceleration: 2.99
      command: 40.0
    }
    calibration {
      speed: 2.0
      acceleration: 3.01
      command: 60.0
    }
    calibration {
      speed: 2.2
      acceleration: -8.65
      command: -35.0
    }
    calibration {
      speed: 2.2
      acceleration: -7.37
      command: -33.0
    }
    calibration {
      speed: 2.2
      acceleration: -4.84
      command: -30.0
    }
    calibration {
      speed: 2.2
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 2.2
      acceleration: -1.66
      command: -25.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.13
      command: 1.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.1
      command: -15.0
    }
    calibration {
      speed: 2.2
      acceleration: -0.09
      command: -17.0
    }
    calibration {
      speed: 2.2
      acceleration: 0.08
      command: 17.0
    }
    calibration {
      speed: 2.2
      acceleration: 0.59
      command: 20.0
    }
    calibration {
      speed: 2.2
      acceleration: 1.13
      command: 22.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.02
      command: 25.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.35
      command: 27.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.53
      command: 30.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.89
      command: 35.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.9
      command: 70.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.93
      command: 65.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.94
      command: 80.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.95
      command: 75.0
    }
    calibration {
      speed: 2.2
      acceleration: 2.98
      command: 55.0
    }
    calibration {
      speed: 2.2
      acceleration: 3.0
      command: 50.0
    }
    calibration {
      speed: 2.2
      acceleration: 3.01
      command: 45.0
    }
    calibration {
      speed: 2.2
      acceleration: 3.03
      command: 50.0
    }
    calibration {
      speed: 2.4
      acceleration: -8.64
      command: -35.0
    }
    calibration {
      speed: 2.4
      acceleration: -7.35
      command: -33.0
    }
    calibration {
      speed: 2.4
      acceleration: -4.87
      command: -30.0
    }
    calibration {
      speed: 2.4
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 2.4
      acceleration: -1.66
      command: -25.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.14
      command: -13.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.13
      command: 15.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.1
      command: -15.0
    }
    calibration {
      speed: 2.4
      acceleration: -0.09
      command: -17.0
    }
    calibration {
      speed: 2.4
      acceleration: 0.08
      command: 17.0
    }
    calibration {
      speed: 2.4
      acceleration: 0.5
      command: 20.0
    }
    calibration {
      speed: 2.4
      acceleration: 1.03
      command: 22.0
    }
    calibration {
      speed: 2.4
      acceleration: 1.96
      command: 25.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.32
      command: 27.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.54
      command: 30.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.89
      command: 70.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.91
      command: 35.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.93
      command: 65.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.95
      command: 80.0
    }
    calibration {
      speed: 2.4
      acceleration: 2.97
      command: 75.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.0
      command: 55.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.05
      command: 50.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.06
      command: 60.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.07
      command: 45.0
    }
    calibration {
      speed: 2.4
      acceleration: 3.08
      command: 40.0
    }
    calibration {
      speed: 2.6
      acceleration: -8.68
      command: -35.0
    }
    calibration {
      speed: 2.6
      acceleration: -7.36
      command: -33.0
    }
    calibration {
      speed: 2.6
      acceleration: -4.86
      command: -30.0
    }
    calibration {
      speed: 2.6
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 2.6
      acceleration: -1.68
      command: -25.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.64
      command: -22.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.13
      command: 15.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.12
      command: -14.0
    }
    calibration {
      speed: 2.6
      acceleration: -0.11
      command: -17.0
    }
    calibration {
      speed: 2.6
      acceleration: 0.06
      command: 17.0
    }
    calibration {
      speed: 2.6
      acceleration: 0.4
      command: 20.0
    }
    calibration {
      speed: 2.6
      acceleration: 0.92
      command: 22.0
    }
    calibration {
      speed: 2.6
      acceleration: 1.9
      command: 25.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.28
      command: 27.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.55
      command: 30.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.91
      command: 70.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.92
      command: 35.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.96
      command: 65.0
    }
    calibration {
      speed: 2.6
      acceleration: 2.99
      command: 80.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.01
      command: 75.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.03
      command: 55.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.09
      command: 60.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.11
      command: 50.0
    }
    calibration {
      speed: 2.6
      acceleration: 3.12
      command: 42.5
    }
    calibration {
      speed: 2.8
      acceleration: -8.76
      command: -35.0
    }
    calibration {
      speed: 2.8
      acceleration: -7.41
      command: -33.0
    }
    calibration {
      speed: 2.8
      acceleration: -4.84
      command: -30.0
    }
    calibration {
      speed: 2.8
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 2.8
      acceleration: -1.7
      command: -25.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.13
      command: -5.0
    }
    calibration {
      speed: 2.8
      acceleration: -0.11
      command: -15.0
    }
    calibration {
      speed: 2.8
      acceleration: 0.06
      command: 17.0
    }
    calibration {
      speed: 2.8
      acceleration: 0.35
      command: 20.0
    }
    calibration {
      speed: 2.8
      acceleration: 0.84
      command: 22.0
    }
    calibration {
      speed: 2.8
      acceleration: 1.82
      command: 25.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.24
      command: 27.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.57
      command: 30.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.93
      command: 35.0
    }
    calibration {
      speed: 2.8
      acceleration: 2.94
      command: 70.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.02
      command: 65.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.03
      command: 80.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.04
      command: 75.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.06
      command: 55.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.13
      command: 60.0
    }
    calibration {
      speed: 2.8
      acceleration: 3.15
      command: 45.0
    }
    calibration {
      speed: 3.0
      acceleration: -8.84
      command: -35.0
    }
    calibration {
      speed: 3.0
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 3.0
      acceleration: -4.82
      command: -30.0
    }
    calibration {
      speed: 3.0
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 3.0
      acceleration: -1.73
      command: -25.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.13
      command: 0.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.12
      command: -13.0
    }
    calibration {
      speed: 3.0
      acceleration: -0.1
      command: -17.0
    }
    calibration {
      speed: 3.0
      acceleration: 0.03
      command: 17.0
    }
    calibration {
      speed: 3.0
      acceleration: 0.31
      command: 20.0
    }
    calibration {
      speed: 3.0
      acceleration: 0.76
      command: 22.0
    }
    calibration {
      speed: 3.0
      acceleration: 1.72
      command: 25.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.2
      command: 27.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.58
      command: 30.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.94
      command: 35.0
    }
    calibration {
      speed: 3.0
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.04
      command: 72.5
    }
    calibration {
      speed: 3.0
      acceleration: 3.05
      command: 75.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.07
      command: 55.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.14
      command: 60.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.15
      command: 45.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.16
      command: 40.0
    }
    calibration {
      speed: 3.0
      acceleration: 3.17
      command: 50.0
    }
    calibration {
      speed: 3.2
      acceleration: -8.93
      command: -35.0
    }
    calibration {
      speed: 3.2
      acceleration: -7.55
      command: -33.0
    }
    calibration {
      speed: 3.2
      acceleration: -4.8
      command: -30.0
    }
    calibration {
      speed: 3.2
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 3.2
      acceleration: -1.74
      command: -25.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.29
      command: -20.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.15
      command: 15.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.13
      command: -14.0
    }
    calibration {
      speed: 3.2
      acceleration: -0.12
      command: -17.0
    }
    calibration {
      speed: 3.2
      acceleration: 0.04
      command: 17.0
    }
    calibration {
      speed: 3.2
      acceleration: 0.27
      command: 20.0
    }
    calibration {
      speed: 3.2
      acceleration: 0.7
      command: 22.0
    }
    calibration {
      speed: 3.2
      acceleration: 1.65
      command: 25.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.12
      command: 27.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.57
      command: 30.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.93
      command: 35.0
    }
    calibration {
      speed: 3.2
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.03
      command: 80.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.04
      command: 70.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.07
      command: 55.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.13
      command: 45.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.15
      command: 60.0
    }
    calibration {
      speed: 3.2
      acceleration: 3.17
      command: 45.0
    }
    calibration {
      speed: 3.4
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 3.4
      acceleration: -7.6
      command: -33.0
    }
    calibration {
      speed: 3.4
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 3.4
      acceleration: -2.61
      command: -27.0
    }
    calibration {
      speed: 3.4
      acceleration: -1.74
      command: -25.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.16
      command: 15.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.14
      command: -13.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.13
      command: -17.0
    }
    calibration {
      speed: 3.4
      acceleration: -0.11
      command: -15.0
    }
    calibration {
      speed: 3.4
      acceleration: 0.06
      command: 17.0
    }
    calibration {
      speed: 3.4
      acceleration: 0.27
      command: 20.0
    }
    calibration {
      speed: 3.4
      acceleration: 0.63
      command: 22.0
    }
    calibration {
      speed: 3.4
      acceleration: 1.58
      command: 25.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.04
      command: 27.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.54
      command: 30.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.92
      command: 35.0
    }
    calibration {
      speed: 3.4
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.02
      command: 80.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.03
      command: 70.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.05
      command: 55.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.12
      command: 45.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.14
      command: 60.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.15
      command: 50.0
    }
    calibration {
      speed: 3.4
      acceleration: 3.16
      command: 40.0
    }
    calibration {
      speed: 3.6
      acceleration: -9.02
      command: -35.0
    }
    calibration {
      speed: 3.6
      acceleration: -7.61
      command: -33.0
    }
    calibration {
      speed: 3.6
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 3.6
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 3.6
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.29
      command: -20.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.16
      command: -13.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.15
      command: 15.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.13
      command: -15.0
    }
    calibration {
      speed: 3.6
      acceleration: -0.11
      command: -17.0
    }
    calibration {
      speed: 3.6
      acceleration: 0.29
      command: 20.0
    }
    calibration {
      speed: 3.6
      acceleration: 0.58
      command: 22.0
    }
    calibration {
      speed: 3.6
      acceleration: 1.51
      command: 25.0
    }
    calibration {
      speed: 3.6
      acceleration: 1.99
      command: 27.0
    }
    calibration {
      speed: 3.6
      acceleration: 2.5
      command: 30.0
    }
    calibration {
      speed: 3.6
      acceleration: 2.89
      command: 35.0
    }
    calibration {
      speed: 3.6
      acceleration: 2.96
      command: 70.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.01
      command: 80.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.02
      command: 65.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.03
      command: 55.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.04
      command: 75.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.11
      command: 45.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.12
      command: 55.0
    }
    calibration {
      speed: 3.6
      acceleration: 3.14
      command: 40.0
    }
    calibration {
      speed: 3.8
      acceleration: -9.01
      command: -35.0
    }
    calibration {
      speed: 3.8
      acceleration: -7.6
      command: -33.0
    }
    calibration {
      speed: 3.8
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 3.8
      acceleration: -2.61
      command: -27.0
    }
    calibration {
      speed: 3.8
      acceleration: -1.71
      command: -25.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.15
      command: -1.0
    }
    calibration {
      speed: 3.8
      acceleration: -0.14
      command: -13.0
    }
    calibration {
      speed: 3.8
      acceleration: 0.26
      command: 20.0
    }
    calibration {
      speed: 3.8
      acceleration: 0.52
      command: 22.0
    }
    calibration {
      speed: 3.8
      acceleration: 1.42
      command: 25.0
    }
    calibration {
      speed: 3.8
      acceleration: 1.94
      command: 27.0
    }
    calibration {
      speed: 3.8
      acceleration: 2.43
      command: 30.0
    }
    calibration {
      speed: 3.8
      acceleration: 2.85
      command: 35.0
    }
    calibration {
      speed: 3.8
      acceleration: 2.99
      command: 70.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.0
      command: 55.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.04
      command: 72.5
    }
    calibration {
      speed: 3.8
      acceleration: 3.07
      command: 75.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.1
      command: 50.0
    }
    calibration {
      speed: 3.8
      acceleration: 3.11
      command: 52.5
    }
    calibration {
      speed: 3.8
      acceleration: 3.13
      command: 40.0
    }
    calibration {
      speed: 4.0
      acceleration: -8.99
      command: -35.0
    }
    calibration {
      speed: 4.0
      acceleration: -7.58
      command: -33.0
    }
    calibration {
      speed: 4.0
      acceleration: -4.79
      command: -30.0
    }
    calibration {
      speed: 4.0
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 4.0
      acceleration: -1.76
      command: -25.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.64
      command: -22.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.18
      command: 15.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.15
      command: -15.0
    }
    calibration {
      speed: 4.0
      acceleration: -0.02
      command: 17.0
    }
    calibration {
      speed: 4.0
      acceleration: 0.24
      command: 20.0
    }
    calibration {
      speed: 4.0
      acceleration: 0.49
      command: 22.0
    }
    calibration {
      speed: 4.0
      acceleration: 1.33
      command: 25.0
    }
    calibration {
      speed: 4.0
      acceleration: 1.84
      command: 27.0
    }
    calibration {
      speed: 4.0
      acceleration: 2.35
      command: 30.0
    }
    calibration {
      speed: 4.0
      acceleration: 2.8
      command: 35.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.01
      command: 55.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.02
      command: 70.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.08
      command: 65.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.09
      command: 50.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.1
      command: 60.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.11
      command: 75.0
    }
    calibration {
      speed: 4.0
      acceleration: 3.12
      command: 52.5
    }
    calibration {
      speed: 4.2
      acceleration: -8.98
      command: -35.0
    }
    calibration {
      speed: 4.2
      acceleration: -7.53
      command: -33.0
    }
    calibration {
      speed: 4.2
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 4.2
      acceleration: -2.67
      command: -27.0
    }
    calibration {
      speed: 4.2
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.6
      command: -22.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.28
      command: -20.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.17
      command: 15.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.16
      command: -13.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.12
      command: -17.0
    }
    calibration {
      speed: 4.2
      acceleration: -0.04
      command: 17.0
    }
    calibration {
      speed: 4.2
      acceleration: 0.22
      command: 20.0
    }
    calibration {
      speed: 4.2
      acceleration: 0.47
      command: 22.0
    }
    calibration {
      speed: 4.2
      acceleration: 1.22
      command: 25.0
    }
    calibration {
      speed: 4.2
      acceleration: 1.71
      command: 27.0
    }
    calibration {
      speed: 4.2
      acceleration: 2.25
      command: 30.0
    }
    calibration {
      speed: 4.2
      acceleration: 2.75
      command: 35.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.04
      command: 55.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.06
      command: 55.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.1
      command: 50.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.12
      command: 65.0
    }
    calibration {
      speed: 4.2
      acceleration: 3.13
      command: 52.5
    }
    calibration {
      speed: 4.2
      acceleration: 3.16
      command: 77.5
    }
    calibration {
      speed: 4.4
      acceleration: -8.88
      command: -35.0
    }
    calibration {
      speed: 4.4
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 4.4
      acceleration: -4.77
      command: -30.0
    }
    calibration {
      speed: 4.4
      acceleration: -2.67
      command: -27.0
    }
    calibration {
      speed: 4.4
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.27
      command: -20.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.16
      command: 15.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.15
      command: -13.0
    }
    calibration {
      speed: 4.4
      acceleration: -0.03
      command: 17.0
    }
    calibration {
      speed: 4.4
      acceleration: 0.2
      command: 20.0
    }
    calibration {
      speed: 4.4
      acceleration: 0.43
      command: 22.0
    }
    calibration {
      speed: 4.4
      acceleration: 1.14
      command: 25.0
    }
    calibration {
      speed: 4.4
      acceleration: 1.59
      command: 27.0
    }
    calibration {
      speed: 4.4
      acceleration: 2.15
      command: 30.0
    }
    calibration {
      speed: 4.4
      acceleration: 2.68
      command: 35.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.02
      command: 40.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.07
      command: 55.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.08
      command: 70.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.11
      command: 50.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.12
      command: 45.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.16
      command: 62.5
    }
    calibration {
      speed: 4.4
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 4.4
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 4.6
      acceleration: -8.79
      command: -35.0
    }
    calibration {
      speed: 4.6
      acceleration: -7.43
      command: -33.0
    }
    calibration {
      speed: 4.6
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 4.6
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 4.6
      acceleration: -1.77
      command: -25.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.62
      command: -22.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.26
      command: -20.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.18
      command: 1.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.17
      command: -15.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.15
      command: -17.0
    }
    calibration {
      speed: 4.6
      acceleration: -0.01
      command: 17.0
    }
    calibration {
      speed: 4.6
      acceleration: 0.15
      command: 20.0
    }
    calibration {
      speed: 4.6
      acceleration: 0.39
      command: 22.0
    }
    calibration {
      speed: 4.6
      acceleration: 1.04
      command: 25.0
    }
    calibration {
      speed: 4.6
      acceleration: 1.49
      command: 27.0
    }
    calibration {
      speed: 4.6
      acceleration: 2.04
      command: 30.0
    }
    calibration {
      speed: 4.6
      acceleration: 2.62
      command: 35.0
    }
    calibration {
      speed: 4.6
      acceleration: 2.97
      command: 40.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.08
      command: 45.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.1
      command: 55.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.12
      command: 60.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.17
      command: 65.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.19
      command: 60.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.21
      command: 80.0
    }
    calibration {
      speed: 4.6
      acceleration: 3.23
      command: 75.0
    }
    calibration {
      speed: 4.8
      acceleration: -8.56
      command: -35.0
    }
    calibration {
      speed: 4.8
      acceleration: -7.39
      command: -33.0
    }
    calibration {
      speed: 4.8
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 4.8
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 4.8
      acceleration: -1.78
      command: -25.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.16
      command: 0.0
    }
    calibration {
      speed: 4.8
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 4.8
      acceleration: 0.11
      command: 20.0
    }
    calibration {
      speed: 4.8
      acceleration: 0.34
      command: 22.0
    }
    calibration {
      speed: 4.8
      acceleration: 0.94
      command: 25.0
    }
    calibration {
      speed: 4.8
      acceleration: 1.38
      command: 27.0
    }
    calibration {
      speed: 4.8
      acceleration: 1.92
      command: 30.0
    }
    calibration {
      speed: 4.8
      acceleration: 2.55
      command: 35.0
    }
    calibration {
      speed: 4.8
      acceleration: 2.92
      command: 40.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.04
      command: 45.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.12
      command: 52.5
    }
    calibration {
      speed: 4.8
      acceleration: 3.14
      command: 70.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.17
      command: 65.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 4.8
      acceleration: 3.22
      command: 67.5
    }
    calibration {
      speed: 5.0
      acceleration: -8.57
      command: -35.0
    }
    calibration {
      speed: 5.0
      acceleration: -7.36
      command: -33.0
    }
    calibration {
      speed: 5.0
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 5.0
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 5.0
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.63
      command: -22.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.2
      command: 15.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.18
      command: -14.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.16
      command: -17.0
    }
    calibration {
      speed: 5.0
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 5.0
      acceleration: 0.11
      command: 20.0
    }
    calibration {
      speed: 5.0
      acceleration: 0.34
      command: 22.0
    }
    calibration {
      speed: 5.0
      acceleration: 0.86
      command: 25.0
    }
    calibration {
      speed: 5.0
      acceleration: 1.27
      command: 27.0
    }
    calibration {
      speed: 5.0
      acceleration: 1.8
      command: 30.0
    }
    calibration {
      speed: 5.0
      acceleration: 2.46
      command: 35.0
    }
    calibration {
      speed: 5.0
      acceleration: 2.87
      command: 40.0
    }
    calibration {
      speed: 5.0
      acceleration: 2.97
      command: 45.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.1
      command: 50.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.14
      command: 55.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.15
      command: 70.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.16
      command: 80.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.2
      command: 75.0
    }
    calibration {
      speed: 5.0
      acceleration: 3.24
      command: 60.0
    }
    calibration {
      speed: 5.2
      acceleration: -8.6
      command: -35.0
    }
    calibration {
      speed: 5.2
      acceleration: -7.35
      command: -33.0
    }
    calibration {
      speed: 5.2
      acceleration: -4.77
      command: -30.0
    }
    calibration {
      speed: 5.2
      acceleration: -2.62
      command: -27.0
    }
    calibration {
      speed: 5.2
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.3
      command: -20.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.19
      command: 15.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 5.2
      acceleration: -0.09
      command: 17.0
    }
    calibration {
      speed: 5.2
      acceleration: 0.1
      command: 20.0
    }
    calibration {
      speed: 5.2
      acceleration: 0.3
      command: 22.0
    }
    calibration {
      speed: 5.2
      acceleration: 0.79
      command: 25.0
    }
    calibration {
      speed: 5.2
      acceleration: 1.19
      command: 27.0
    }
    calibration {
      speed: 5.2
      acceleration: 1.71
      command: 30.0
    }
    calibration {
      speed: 5.2
      acceleration: 2.38
      command: 35.0
    }
    calibration {
      speed: 5.2
      acceleration: 2.83
      command: 40.0
    }
    calibration {
      speed: 5.2
      acceleration: 2.92
      command: 45.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.07
      command: 50.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.15
      command: 67.5
    }
    calibration {
      speed: 5.2
      acceleration: 3.16
      command: 70.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.19
      command: 70.0
    }
    calibration {
      speed: 5.2
      acceleration: 3.25
      command: 60.0
    }
    calibration {
      speed: 5.4
      acceleration: -8.6
      command: -35.0
    }
    calibration {
      speed: 5.4
      acceleration: -7.35
      command: -33.0
    }
    calibration {
      speed: 5.4
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 5.4
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 5.4
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.2
      command: 15.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.17
      command: -15.0
    }
    calibration {
      speed: 5.4
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 5.4
      acceleration: 0.13
      command: 20.0
    }
    calibration {
      speed: 5.4
      acceleration: 0.29
      command: 22.0
    }
    calibration {
      speed: 5.4
      acceleration: 0.76
      command: 25.0
    }
    calibration {
      speed: 5.4
      acceleration: 1.12
      command: 27.0
    }
    calibration {
      speed: 5.4
      acceleration: 1.62
      command: 30.0
    }
    calibration {
      speed: 5.4
      acceleration: 2.31
      command: 35.0
    }
    calibration {
      speed: 5.4
      acceleration: 2.78
      command: 40.0
    }
    calibration {
      speed: 5.4
      acceleration: 2.89
      command: 45.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.04
      command: 50.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.16
      command: 68.3333333333
    }
    calibration {
      speed: 5.4
      acceleration: 3.19
      command: 75.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 5.4
      acceleration: 3.26
      command: 60.0
    }
    calibration {
      speed: 5.6
      acceleration: -8.65
      command: -35.0
    }
    calibration {
      speed: 5.6
      acceleration: -7.36
      command: -33.0
    }
    calibration {
      speed: 5.6
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 5.6
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 5.6
      acceleration: -1.81
      command: -25.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.18
      command: 0.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.15
      command: -13.0
    }
    calibration {
      speed: 5.6
      acceleration: -0.07
      command: 17.0
    }
    calibration {
      speed: 5.6
      acceleration: 0.09
      command: 20.0
    }
    calibration {
      speed: 5.6
      acceleration: 0.29
      command: 22.0
    }
    calibration {
      speed: 5.6
      acceleration: 0.7
      command: 25.0
    }
    calibration {
      speed: 5.6
      acceleration: 1.06
      command: 27.0
    }
    calibration {
      speed: 5.6
      acceleration: 1.54
      command: 30.0
    }
    calibration {
      speed: 5.6
      acceleration: 2.24
      command: 35.0
    }
    calibration {
      speed: 5.6
      acceleration: 2.72
      command: 40.0
    }
    calibration {
      speed: 5.6
      acceleration: 2.86
      command: 45.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.01
      command: 50.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.16
      command: 70.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.17
      command: 55.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.18
      command: 80.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.2
      command: 75.0
    }
    calibration {
      speed: 5.6
      acceleration: 3.27
      command: 60.0
    }
    calibration {
      speed: 5.8
      acceleration: -8.68
      command: -35.0
    }
    calibration {
      speed: 5.8
      acceleration: -7.38
      command: -33.0
    }
    calibration {
      speed: 5.8
      acceleration: -4.78
      command: -30.0
    }
    calibration {
      speed: 5.8
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 5.8
      acceleration: -1.82
      command: -25.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.21
      command: 15.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.2
      command: -13.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.18
      command: -15.0
    }
    calibration {
      speed: 5.8
      acceleration: -0.07
      command: 17.0
    }
    calibration {
      speed: 5.8
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 5.8
      acceleration: 0.29
      command: 22.0
    }
    calibration {
      speed: 5.8
      acceleration: 0.69
      command: 25.0
    }
    calibration {
      speed: 5.8
      acceleration: 1.01
      command: 27.0
    }
    calibration {
      speed: 5.8
      acceleration: 1.47
      command: 30.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.18
      command: 35.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.66
      command: 40.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.83
      command: 45.0
    }
    calibration {
      speed: 5.8
      acceleration: 2.99
      command: 50.0
    }
    calibration {
      speed: 5.8
      acceleration: 3.16
      command: 62.5
    }
    calibration {
      speed: 5.8
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 5.8
      acceleration: 3.2
      command: 77.5
    }
    calibration {
      speed: 5.8
      acceleration: 3.27
      command: 60.0
    }
    calibration {
      speed: 6.0
      acceleration: -8.86
      command: -35.0
    }
    calibration {
      speed: 6.0
      acceleration: -7.41
      command: -33.0
    }
    calibration {
      speed: 6.0
      acceleration: -4.76
      command: -30.0
    }
    calibration {
      speed: 6.0
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 6.0
      acceleration: -1.81
      command: -25.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.66
      command: -22.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.23
      command: -13.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.2
      command: 0.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.18
      command: -17.0
    }
    calibration {
      speed: 6.0
      acceleration: -0.1
      command: 17.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.09
      command: 20.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.27
      command: 22.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.7
      command: 25.0
    }
    calibration {
      speed: 6.0
      acceleration: 0.98
      command: 27.0
    }
    calibration {
      speed: 6.0
      acceleration: 1.41
      command: 30.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.13
      command: 35.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.6
      command: 40.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.78
      command: 45.0
    }
    calibration {
      speed: 6.0
      acceleration: 2.95
      command: 50.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.14
      command: 55.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.2
      command: 80.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 6.0
      acceleration: 3.28
      command: 60.0
    }
    calibration {
      speed: 6.2
      acceleration: -8.91
      command: -35.0
    }
    calibration {
      speed: 6.2
      acceleration: -7.42
      command: -33.0
    }
    calibration {
      speed: 6.2
      acceleration: -4.74
      command: -30.0
    }
    calibration {
      speed: 6.2
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 6.2
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.34
      command: -20.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.19
      command: 0.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 6.2
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.06
      command: 20.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.28
      command: 22.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.63
      command: 25.0
    }
    calibration {
      speed: 6.2
      acceleration: 0.94
      command: 27.0
    }
    calibration {
      speed: 6.2
      acceleration: 1.37
      command: 30.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.08
      command: 35.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.54
      command: 40.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.7
      command: 45.0
    }
    calibration {
      speed: 6.2
      acceleration: 2.91
      command: 50.0
    }
    calibration {
      speed: 6.2
      acceleration: 3.11
      command: 55.0
    }
    calibration {
      speed: 6.2
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.2
      acceleration: 3.2
      command: 73.3333333333
    }
    calibration {
      speed: 6.2
      acceleration: 3.27
      command: 60.0
    }
    calibration {
      speed: 6.4
      acceleration: -8.96
      command: -35.0
    }
    calibration {
      speed: 6.4
      acceleration: -7.45
      command: -33.0
    }
    calibration {
      speed: 6.4
      acceleration: -4.72
      command: -30.0
    }
    calibration {
      speed: 6.4
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 6.4
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.67
      command: -22.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.34
      command: -20.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.2
      command: 15.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.19
      command: -15.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.18
      command: -13.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 6.4
      acceleration: -0.07
      command: 17.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.25
      command: 22.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.63
      command: 25.0
    }
    calibration {
      speed: 6.4
      acceleration: 0.91
      command: 27.0
    }
    calibration {
      speed: 6.4
      acceleration: 1.34
      command: 30.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.02
      command: 35.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.49
      command: 40.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.64
      command: 45.0
    }
    calibration {
      speed: 6.4
      acceleration: 2.87
      command: 50.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.08
      command: 55.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.2
      command: 77.5
    }
    calibration {
      speed: 6.4
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 6.4
      acceleration: 3.26
      command: 60.0
    }
    calibration {
      speed: 6.6
      acceleration: -8.98
      command: -35.0
    }
    calibration {
      speed: 6.6
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 6.6
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 6.6
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 6.6
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.66
      command: -22.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.23
      command: 15.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.21
      command: -15.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.2
      command: -13.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.17
      command: -17.0
    }
    calibration {
      speed: 6.6
      acceleration: -0.08
      command: 17.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.23
      command: 22.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.62
      command: 25.0
    }
    calibration {
      speed: 6.6
      acceleration: 0.89
      command: 27.0
    }
    calibration {
      speed: 6.6
      acceleration: 1.29
      command: 30.0
    }
    calibration {
      speed: 6.6
      acceleration: 1.97
      command: 35.0
    }
    calibration {
      speed: 6.6
      acceleration: 2.43
      command: 40.0
    }
    calibration {
      speed: 6.6
      acceleration: 2.6
      command: 45.0
    }
    calibration {
      speed: 6.6
      acceleration: 2.83
      command: 50.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.04
      command: 55.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 6.6
      acceleration: 3.25
      command: 60.0
    }
    calibration {
      speed: 6.8
      acceleration: -9.02
      command: -35.0
    }
    calibration {
      speed: 6.8
      acceleration: -7.49
      command: -33.0
    }
    calibration {
      speed: 6.8
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 6.8
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 6.8
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.22
      command: 1.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.16
      command: -15.0
    }
    calibration {
      speed: 6.8
      acceleration: -0.1
      command: 17.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.03
      command: 20.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.21
      command: 22.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.59
      command: 25.0
    }
    calibration {
      speed: 6.8
      acceleration: 0.85
      command: 27.0
    }
    calibration {
      speed: 6.8
      acceleration: 1.25
      command: 30.0
    }
    calibration {
      speed: 6.8
      acceleration: 1.92
      command: 35.0
    }
    calibration {
      speed: 6.8
      acceleration: 2.38
      command: 40.0
    }
    calibration {
      speed: 6.8
      acceleration: 2.56
      command: 45.0
    }
    calibration {
      speed: 6.8
      acceleration: 2.79
      command: 50.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.01
      command: 55.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.19
      command: 80.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.23
      command: 70.0
    }
    calibration {
      speed: 6.8
      acceleration: 3.24
      command: 60.0
    }
    calibration {
      speed: 7.0
      acceleration: -9.01
      command: -35.0
    }
    calibration {
      speed: 7.0
      acceleration: -7.51
      command: -33.0
    }
    calibration {
      speed: 7.0
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 7.0
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 7.0
      acceleration: -1.78
      command: -25.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.33
      command: -20.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.25
      command: -17.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.22
      command: 0.0
    }
    calibration {
      speed: 7.0
      acceleration: -0.1
      command: 17.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.06
      command: 20.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.22
      command: 22.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.55
      command: 25.0
    }
    calibration {
      speed: 7.0
      acceleration: 0.83
      command: 27.0
    }
    calibration {
      speed: 7.0
      acceleration: 1.21
      command: 30.0
    }
    calibration {
      speed: 7.0
      acceleration: 1.88
      command: 35.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.34
      command: 40.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.51
      command: 45.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.75
      command: 50.0
    }
    calibration {
      speed: 7.0
      acceleration: 2.98
      command: 55.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.2
      command: 80.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.23
      command: 60.0
    }
    calibration {
      speed: 7.0
      acceleration: 3.24
      command: 75.0
    }
    calibration {
      speed: 7.2
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 7.2
      acceleration: -7.51
      command: -33.0
    }
    calibration {
      speed: 7.2
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 7.2
      acceleration: -2.63
      command: -27.0
    }
    calibration {
      speed: 7.2
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.7
      command: -22.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.31
      command: -20.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.23
      command: 15.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.22
      command: -17.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.2
      command: -15.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.17
      command: -13.0
    }
    calibration {
      speed: 7.2
      acceleration: -0.11
      command: 17.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.07
      command: 20.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.19
      command: 22.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.55
      command: 25.0
    }
    calibration {
      speed: 7.2
      acceleration: 0.78
      command: 27.0
    }
    calibration {
      speed: 7.2
      acceleration: 1.16
      command: 30.0
    }
    calibration {
      speed: 7.2
      acceleration: 1.83
      command: 35.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.3
      command: 40.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.45
      command: 45.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.7
      command: 50.0
    }
    calibration {
      speed: 7.2
      acceleration: 2.95
      command: 55.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.21
      command: 70.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 7.2
      acceleration: 3.25
      command: 75.0
    }
    calibration {
      speed: 7.4
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 7.4
      acceleration: -7.51
      command: -33.0
    }
    calibration {
      speed: 7.4
      acceleration: -4.72
      command: -30.0
    }
    calibration {
      speed: 7.4
      acceleration: -2.62
      command: -27.0
    }
    calibration {
      speed: 7.4
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.65
      command: -22.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.32
      command: -20.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.22
      command: -15.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.21
      command: -17.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.2
      command: -13.0
    }
    calibration {
      speed: 7.4
      acceleration: -0.13
      command: 17.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.02
      command: 20.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.21
      command: 22.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.53
      command: 25.0
    }
    calibration {
      speed: 7.4
      acceleration: 0.78
      command: 27.0
    }
    calibration {
      speed: 7.4
      acceleration: 1.15
      command: 30.0
    }
    calibration {
      speed: 7.4
      acceleration: 1.79
      command: 35.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.26
      command: 40.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.38
      command: 45.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.64
      command: 50.0
    }
    calibration {
      speed: 7.4
      acceleration: 2.92
      command: 55.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.19
      command: 65.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.22
      command: 80.0
    }
    calibration {
      speed: 7.4
      acceleration: 3.23
      command: 75.0
    }
    calibration {
      speed: 7.6
      acceleration: -8.99
      command: -35.0
    }
    calibration {
      speed: 7.6
      acceleration: -7.5
      command: -33.0
    }
    calibration {
      speed: 7.6
      acceleration: -4.72
      command: -30.0
    }
    calibration {
      speed: 7.6
      acceleration: -2.64
      command: -27.0
    }
    calibration {
      speed: 7.6
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.36
      command: -20.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.26
      command: -15.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.2
      command: -17.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.16
      command: -13.0
    }
    calibration {
      speed: 7.6
      acceleration: -0.14
      command: 17.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.02
      command: 20.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.16
      command: 22.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.52
      command: 25.0
    }
    calibration {
      speed: 7.6
      acceleration: 0.75
      command: 27.0
    }
    calibration {
      speed: 7.6
      acceleration: 1.12
      command: 30.0
    }
    calibration {
      speed: 7.6
      acceleration: 1.76
      command: 35.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.2
      command: 40.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.32
      command: 45.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.58
      command: 50.0
    }
    calibration {
      speed: 7.6
      acceleration: 2.88
      command: 55.0
    }
    calibration {
      speed: 7.6
      acceleration: 3.16
      command: 60.0
    }
    calibration {
      speed: 7.6
      acceleration: 3.19
      command: 67.5
    }
    calibration {
      speed: 7.6
      acceleration: 3.22
      command: 75.0
    }
    calibration {
      speed: 7.6
      acceleration: 3.23
      command: 80.0
    }
    calibration {
      speed: 7.8
      acceleration: -8.99
      command: -35.0
    }
    calibration {
      speed: 7.8
      acceleration: -7.49
      command: -33.0
    }
    calibration {
      speed: 7.8
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 7.8
      acceleration: -2.66
      command: -27.0
    }
    calibration {
      speed: 7.8
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.72
      command: -22.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.38
      command: -20.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.23
      command: -15.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.2
      command: -15.0
    }
    calibration {
      speed: 7.8
      acceleration: -0.13
      command: 17.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.03
      command: 20.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.16
      command: 22.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.51
      command: 25.0
    }
    calibration {
      speed: 7.8
      acceleration: 0.71
      command: 27.0
    }
    calibration {
      speed: 7.8
      acceleration: 1.08
      command: 30.0
    }
    calibration {
      speed: 7.8
      acceleration: 1.73
      command: 35.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.15
      command: 40.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.28
      command: 45.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.52
      command: 50.0
    }
    calibration {
      speed: 7.8
      acceleration: 2.83
      command: 55.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.14
      command: 60.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.21
      command: 75.0
    }
    calibration {
      speed: 7.8
      acceleration: 3.24
      command: 80.0
    }
    calibration {
      speed: 8.0
      acceleration: -9.0
      command: -35.0
    }
    calibration {
      speed: 8.0
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 8.0
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 8.0
      acceleration: -2.68
      command: -27.0
    }
    calibration {
      speed: 8.0
      acceleration: -1.81
      command: -25.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.35
      command: -20.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.26
      command: 15.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.23
      command: -14.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.21
      command: -17.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.13
      command: 17.0
    }
    calibration {
      speed: 8.0
      acceleration: -0.02
      command: 20.0
    }
    calibration {
      speed: 8.0
      acceleration: 0.16
      command: 22.0
    }
    calibration {
      speed: 8.0
      acceleration: 0.48
      command: 25.0
    }
    calibration {
      speed: 8.0
      acceleration: 0.69
      command: 27.0
    }
    calibration {
      speed: 8.0
      acceleration: 1.05
      command: 30.0
    }
    calibration {
      speed: 8.0
      acceleration: 1.69
      command: 35.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.11
      command: 40.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.25
      command: 45.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.48
      command: 50.0
    }
    calibration {
      speed: 8.0
      acceleration: 2.79
      command: 55.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.11
      command: 60.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.17
      command: 70.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.22
      command: 75.0
    }
    calibration {
      speed: 8.0
      acceleration: 3.23
      command: 80.0
    }
    calibration {
      speed: 8.2
      acceleration: -9.03
      command: -35.0
    }
    calibration {
      speed: 8.2
      acceleration: -7.48
      command: -33.0
    }
    calibration {
      speed: 8.2
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 8.2
      acceleration: -2.68
      command: -27.0
    }
    calibration {
      speed: 8.2
      acceleration: -1.84
      command: -25.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.68
      command: -22.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.37
      command: -20.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.3
      command: -15.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.26
      command: -13.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.23
      command: -17.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.17
      command: 17.0
    }
    calibration {
      speed: 8.2
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 8.2
      acceleration: 0.14
      command: 22.0
    }
    calibration {
      speed: 8.2
      acceleration: 0.47
      command: 25.0
    }
    calibration {
      speed: 8.2
      acceleration: 0.67
      command: 27.0
    }
    calibration {
      speed: 8.2
      acceleration: 1.01
      command: 30.0
    }
    calibration {
      speed: 8.2
      acceleration: 1.66
      command: 35.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.07
      command: 40.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.23
      command: 45.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.46
      command: 50.0
    }
    calibration {
      speed: 8.2
      acceleration: 2.77
      command: 55.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.09
      command: 60.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.15
      command: 70.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.21
      command: 80.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.22
      command: 65.0
    }
    calibration {
      speed: 8.2
      acceleration: 3.24
      command: 75.0
    }
    calibration {
      speed: 8.4
      acceleration: -9.08
      command: -35.0
    }
    calibration {
      speed: 8.4
      acceleration: -7.49
      command: -33.0
    }
    calibration {
      speed: 8.4
      acceleration: -4.68
      command: -30.0
    }
    calibration {
      speed: 8.4
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 8.4
      acceleration: -1.83
      command: -25.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.69
      command: -22.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.37
      command: -20.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.3
      command: -15.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.27
      command: -17.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.25
      command: 1.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.19
      command: 17.0
    }
    calibration {
      speed: 8.4
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.15
      command: 22.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.43
      command: 25.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.63
      command: 27.0
    }
    calibration {
      speed: 8.4
      acceleration: 0.99
      command: 30.0
    }
    calibration {
      speed: 8.4
      acceleration: 1.61
      command: 35.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.03
      command: 40.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.19
      command: 45.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.43
      command: 50.0
    }
    calibration {
      speed: 8.4
      acceleration: 2.77
      command: 55.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.07
      command: 60.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.14
      command: 70.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.18
      command: 80.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.23
      command: 65.0
    }
    calibration {
      speed: 8.4
      acceleration: 3.26
      command: 75.0
    }
    calibration {
      speed: 8.6
      acceleration: -9.15
      command: -35.0
    }
    calibration {
      speed: 8.6
      acceleration: -7.5
      command: -33.0
    }
    calibration {
      speed: 8.6
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 8.6
      acceleration: -2.6
      command: -27.0
    }
    calibration {
      speed: 8.6
      acceleration: -1.79
      command: -25.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.68
      command: -22.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.39
      command: -20.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.3
      command: 15.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.24
      command: -17.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.23
      command: -15.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.18
      command: 17.0
    }
    calibration {
      speed: 8.6
      acceleration: -0.06
      command: 20.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.1
      command: 22.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.42
      command: 25.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.62
      command: 27.0
    }
    calibration {
      speed: 8.6
      acceleration: 0.96
      command: 30.0
    }
    calibration {
      speed: 8.6
      acceleration: 1.56
      command: 35.0
    }
    calibration {
      speed: 8.6
      acceleration: 1.99
      command: 40.0
    }
    calibration {
      speed: 8.6
      acceleration: 2.15
      command: 45.0
    }
    calibration {
      speed: 8.6
      acceleration: 2.4
      command: 50.0
    }
    calibration {
      speed: 8.6
      acceleration: 2.77
      command: 55.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.06
      command: 60.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.14
      command: 70.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.15
      command: 80.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.23
      command: 65.0
    }
    calibration {
      speed: 8.6
      acceleration: 3.27
      command: 75.0
    }
    calibration {
      speed: 8.8
      acceleration: -9.22
      command: -35.0
    }
    calibration {
      speed: 8.8
      acceleration: -7.55
      command: -33.0
    }
    calibration {
      speed: 8.8
      acceleration: -4.7
      command: -30.0
    }
    calibration {
      speed: 8.8
      acceleration: -2.57
      command: -27.0
    }
    calibration {
      speed: 8.8
      acceleration: -1.77
      command: -25.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.34
      command: -20.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.3
      command: -13.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.24
      command: -17.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.21
      command: -15.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.16
      command: 17.0
    }
    calibration {
      speed: 8.8
      acceleration: -0.05
      command: 20.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.12
      command: 22.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.4
      command: 25.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.61
      command: 27.0
    }
    calibration {
      speed: 8.8
      acceleration: 0.93
      command: 30.0
    }
    calibration {
      speed: 8.8
      acceleration: 1.51
      command: 35.0
    }
    calibration {
      speed: 8.8
      acceleration: 1.95
      command: 40.0
    }
    calibration {
      speed: 8.8
      acceleration: 2.1
      command: 45.0
    }
    calibration {
      speed: 8.8
      acceleration: 2.37
      command: 50.0
    }
    calibration {
      speed: 8.8
      acceleration: 2.75
      command: 55.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.04
      command: 60.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.12
      command: 80.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.15
      command: 70.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.23
      command: 65.0
    }
    calibration {
      speed: 8.8
      acceleration: 3.25
      command: 75.0
    }
    calibration {
      speed: 9.0
      acceleration: -9.27
      command: -35.0
    }
    calibration {
      speed: 9.0
      acceleration: -7.6
      command: -33.0
    }
    calibration {
      speed: 9.0
      acceleration: -4.71
      command: -30.0
    }
    calibration {
      speed: 9.0
      acceleration: -2.57
      command: -27.0
    }
    calibration {
      speed: 9.0
      acceleration: -1.8
      command: -25.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.74
      command: -22.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.41
      command: -20.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.29
      command: -15.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.2
      command: 17.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.19
      command: -17.0
    }
    calibration {
      speed: 9.0
      acceleration: -0.15
      command: 20.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.12
      command: 22.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.38
      command: 25.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.57
      command: 27.0
    }
    calibration {
      speed: 9.0
      acceleration: 0.88
      command: 30.0
    }
    calibration {
      speed: 9.0
      acceleration: 1.47
      command: 35.0
    }
    calibration {
      speed: 9.0
      acceleration: 1.91
      command: 40.0
    }
    calibration {
      speed: 9.0
      acceleration: 2.07
      command: 45.0
    }
    calibration {
      speed: 9.0
      acceleration: 2.33
      command: 50.0
    }
    calibration {
      speed: 9.0
      acceleration: 2.71
      command: 55.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.03
      command: 60.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.09
      command: 80.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.16
      command: 70.0
    }
    calibration {
      speed: 9.0
      acceleration: 3.21
      command: 70.0
    }
    calibration {
      speed: 9.2
      acceleration: -9.34
      command: -35.0
    }
    calibration {
      speed: 9.2
      acceleration: -7.67
      command: -33.0
    }
    calibration {
      speed: 9.2
      acceleration: -4.69
      command: -30.0
    }
    calibration {
      speed: 9.2
      acceleration: -2.61
      command: -27.0
    }
    calibration {
      speed: 9.2
      acceleration: -1.85
      command: -25.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.71
      command: -22.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.39
      command: -20.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.28
      command: -15.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.27
      command: 15.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.26
      command: -17.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.21
      command: -13.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.17
      command: 17.0
    }
    calibration {
      speed: 9.2
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.1
      command: 22.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.34
      command: 25.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.56
      command: 27.0
    }
    calibration {
      speed: 9.2
      acceleration: 0.87
      command: 30.0
    }
    calibration {
      speed: 9.2
      acceleration: 1.44
      command: 35.0
    }
    calibration {
      speed: 9.2
      acceleration: 1.86
      command: 40.0
    }
    calibration {
      speed: 9.2
      acceleration: 2.05
      command: 45.0
    }
    calibration {
      speed: 9.2
      acceleration: 2.3
      command: 50.0
    }
    calibration {
      speed: 9.2
      acceleration: 2.65
      command: 55.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.02
      command: 60.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.05
      command: 80.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.17
      command: 75.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.18
      command: 70.0
    }
    calibration {
      speed: 9.2
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 9.4
      acceleration: -9.37
      command: -35.0
    }
    calibration {
      speed: 9.4
      acceleration: -7.76
      command: -33.0
    }
    calibration {
      speed: 9.4
      acceleration: -4.65
      command: -30.0
    }
    calibration {
      speed: 9.4
      acceleration: -2.66
      command: -27.0
    }
    calibration {
      speed: 9.4
      acceleration: -1.87
      command: -25.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.73
      command: -22.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.37
      command: -20.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.31
      command: 15.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.26
      command: -15.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.24
      command: -15.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.16
      command: 17.0
    }
    calibration {
      speed: 9.4
      acceleration: -0.1
      command: 20.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.1
      command: 22.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.38
      command: 25.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.53
      command: 27.0
    }
    calibration {
      speed: 9.4
      acceleration: 0.8
      command: 30.0
    }
    calibration {
      speed: 9.4
      acceleration: 1.38
      command: 35.0
    }
    calibration {
      speed: 9.4
      acceleration: 1.82
      command: 40.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.04
      command: 45.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.28
      command: 50.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.6
      command: 55.0
    }
    calibration {
      speed: 9.4
      acceleration: 2.99
      command: 60.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.02
      command: 80.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.15
      command: 75.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.19
      command: 70.0
    }
    calibration {
      speed: 9.4
      acceleration: 3.2
      command: 65.0
    }
    calibration {
      speed: 9.6
      acceleration: -9.36
      command: -35.0
    }
    calibration {
      speed: 9.6
      acceleration: -7.87
      command: -33.0
    }
    calibration {
      speed: 9.6
      acceleration: -4.56
      command: -30.0
    }
    calibration {
      speed: 9.6
      acceleration: -2.68
      command: -27.0
    }
    calibration {
      speed: 9.6
      acceleration: -1.88
      command: -25.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.73
      command: -22.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.38
      command: -20.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.29
      command: 15.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.28
      command: -15.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.27
      command: -13.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.26
      command: -17.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.2
      command: 17.0
    }
    calibration {
      speed: 9.6
      acceleration: -0.07
      command: 20.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.09
      command: 22.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.34
      command: 25.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.53
      command: 27.0
    }
    calibration {
      speed: 9.6
      acceleration: 0.8
      command: 30.0
    }
    calibration {
      speed: 9.6
      acceleration: 1.36
      command: 35.0
    }
    calibration {
      speed: 9.6
      acceleration: 1.79
      command: 40.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.01
      command: 45.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.25
      command: 50.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.57
      command: 55.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.98
      command: 60.0
    }
    calibration {
      speed: 9.6
      acceleration: 2.99
      command: 80.0
    }
    calibration {
      speed: 9.6
      acceleration: 3.15
      command: 75.0
    }
    calibration {
      speed: 9.6
      acceleration: 3.2
      command: 70.0
    }
    calibration {
      speed: 9.6
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 9.8
      acceleration: -9.31
      command: -35.0
    }
    calibration {
      speed: 9.8
      acceleration: -7.97
      command: -33.0
    }
    calibration {
      speed: 9.8
      acceleration: -4.51
      command: -30.0
    }
    calibration {
      speed: 9.8
      acceleration: -2.67
      command: -27.0
    }
    calibration {
      speed: 9.8
      acceleration: -1.9
      command: -25.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.73
      command: -22.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.39
      command: -20.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.28
      command: -14.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.27
      command: -17.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.24
      command: 15.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.22
      command: 17.0
    }
    calibration {
      speed: 9.8
      acceleration: -0.13
      command: 20.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.08
      command: 22.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.34
      command: 25.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.5
      command: 27.0
    }
    calibration {
      speed: 9.8
      acceleration: 0.78
      command: 30.0
    }
    calibration {
      speed: 9.8
      acceleration: 1.35
      command: 35.0
    }
    calibration {
      speed: 9.8
      acceleration: 1.76
      command: 40.0
    }
    calibration {
      speed: 9.8
      acceleration: 1.97
      command: 45.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.21
      command: 50.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.55
      command: 55.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.96
      command: 60.0
    }
    calibration {
      speed: 9.8
      acceleration: 2.98
      command: 80.0
    }
    calibration {
      speed: 9.8
      acceleration: 3.15
      command: 75.0
    }
    calibration {
      speed: 9.8
      acceleration: 3.2
      command: 70.0
    }
    calibration {
      speed: 9.8
      acceleration: 3.21
      command: 65.0
    }
    calibration {
      speed: 10.0
      acceleration: -9.18
      command: -35.0
    }
    calibration {
      speed: 10.0
      acceleration: -8.04
      command: -33.0
    }
    calibration {
      speed: 10.0
      acceleration: -4.75
      command: -30.0
    }
    calibration {
      speed: 10.0
      acceleration: -2.65
      command: -27.0
    }
    calibration {
      speed: 10.0
      acceleration: -1.92
      command: -25.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.75
      command: -22.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.36
      command: -20.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.29
      command: -13.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.27
      command: -15.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.25
      command: 15.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.24
      command: -17.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.21
      command: 17.0
    }
    calibration {
      speed: 10.0
      acceleration: -0.08
      command: 20.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.08
      command: 22.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.32
      command: 25.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.49
      command: 27.0
    }
    calibration {
      speed: 10.0
      acceleration: 0.75
      command: 30.0
    }
    calibration {
      speed: 10.0
      acceleration: 1.3
      command: 35.0
    }
    calibration {
      speed: 10.0
      acceleration: 1.71
      command: 40.0
    }
    calibration {
      speed: 10.0
      acceleration: 1.92
      command: 45.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.17
      command: 50.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.53
      command: 55.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.94
      command: 60.0
    }
    calibration {
      speed: 10.0
      acceleration: 2.99
      command: 80.0
    }
    calibration {
      speed: 10.0
      acceleration: 3.14
      command: 75.0
    }
    calibration {
      speed: 10.0
      acceleration: 3.19
      command: 70.0
    }
    calibration {
      speed: 10.0
      acceleration: 3.21
      command: 65.0
    }
  }
}
lat_pid_controller_conf {
  ts: 0.02
  lateral_error_limit: 0.5
  heading_error_limit: 0.5

  lat_error_pid_conf {
    integrator_enable: false
    integrator_saturation_level: 0.3
    kp: 0.2
    ki: 0.0
    kd: 0.0
  }
  heading_error_pid_conf {
    integrator_enable: true
    integrator_saturation_level: 0.3
    kp: 2.0
    ki: 0.3
    kd: 0.0
  }

  cutoff_freq: 10
  mean_filter_window_size: 10
  query_relative_time: 0.2
  query_time_nearest_point_only: true
  enable_error_filter: true
  use_heading_error: false
  fix_trajectory_follow_location: true
  enable_wheel_steer_limit: true
  enable_maximum_steer_rate_limit: true
  use_output_wheel_steer_filter: false

  lat_calibration_table{
    calibration {
      steer_angle: -1.0
      steer_wheel_angle: -57.33
    }
    calibration {
      steer_angle: -0.95
      steer_wheel_angle: -54.54
    }
    calibration {
      steer_angle: -0.9
      steer_wheel_angle: -51.8
    }
    calibration {
      steer_angle: -0.85
      steer_wheel_angle: -49.09
    }
    calibration {
      steer_angle: -0.8
      steer_wheel_angle: -46.41
    }
    calibration {
      steer_angle: -0.75
      steer_wheel_angle: -43.75
    }
    calibration {
      steer_angle: -0.7
      steer_wheel_angle: -41.09
    }
    calibration {
      steer_angle: -0.65
      steer_wheel_angle: -38.44
    }
    calibration {
      steer_angle: -0.6
      steer_wheel_angle: -35.77
    }
    calibration {
      steer_angle: -0.55
      steer_wheel_angle: -33.09
    }
    calibration {
      steer_angle: -0.5
      steer_wheel_angle: -30.38
    }
    calibration {
      steer_angle: -0.45
      steer_wheel_angle: -27.64
    }
    calibration {
      steer_angle: -0.4
      steer_wheel_angle: -24.86
    }
    calibration {
      steer_angle: -0.35
      steer_wheel_angle: -22.03
    }
    calibration {
      steer_angle: -0.3
      steer_wheel_angle: -19.13
    }
    calibration {
      steer_angle: -0.25
      steer_wheel_angle: -16.17
    }
    calibration {
      steer_angle: -0.2
      steer_wheel_angle: -13.13
    }
    calibration {
      steer_angle: -0.15
      steer_wheel_angle: -10.0
    }
    calibration {
      steer_angle: -0.1
      steer_wheel_angle: -6.77
    }
    calibration {
      steer_angle: -0.05
      steer_wheel_angle: -3.44
    }
    calibration {
    }
    calibration {
      steer_angle: 0.05
      steer_wheel_angle: 3.44
    }
    calibration {
      steer_angle: 0.1
      steer_wheel_angle: 6.77
    }
    calibration {
      steer_angle: 0.15
      steer_wheel_angle: 10.0
    }
    calibration {
      steer_angle: 0.2
      steer_wheel_angle: 13.13
    }
    calibration {
      steer_angle: 0.25
      steer_wheel_angle: 16.17
    }
    calibration {
      steer_angle: 0.3
      steer_wheel_angle: 19.13
    }
    calibration {
      steer_angle: 0.35
      steer_wheel_angle: 22.03
    }
    calibration {
      steer_angle: 0.4
      steer_wheel_angle: 24.86
    }
    calibration {
      steer_angle: 0.45
      steer_wheel_angle: 27.64
    }
    calibration {
      steer_angle: 0.5
      steer_wheel_angle: 30.38
    }
    calibration {
      steer_angle: 0.55
      steer_wheel_angle: 33.09
    }
    calibration {
      steer_angle: 0.6
      steer_wheel_angle: 35.77
    }
    calibration {
      steer_angle: 0.65
      steer_wheel_angle: 38.44
    }
    calibration {
      steer_angle: 0.7
      steer_wheel_angle: 41.09
    }
    calibration {
      steer_angle: 0.75
      steer_wheel_angle: 43.75
    }
    calibration {
      steer_angle: 0.8
      steer_wheel_angle: 46.41
    }
    calibration {
      steer_angle: 0.85
      steer_wheel_angle: 49.09
    }
    calibration {
      steer_angle: 0.9
      steer_wheel_angle: 51.8
    }
    calibration {
      steer_angle: 0.95
      steer_wheel_angle: 54.54
    }
    calibration {
      steer_angle: 1.0
      steer_wheel_angle: 57.33
    }
  }
}

lat_lqrk1_controller_conf {
  ts: 0.02
  preview_window: 10.0
  x_error_limit: 10.0
  y_error_limit: 10.0
  heading_error_limit: 10.0
  eps: 0.01
  matrix_q: 0.0
  matrix_q: 1.0
  matrix_q: 1.0
  cutoff_freq: 10
  mean_filter_window_size: 10
  max_iteration: 150
  max_lateral_acceleration: 5.0

  x_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.2
    }
    scheduler {
      speed: 20.0
      ratio: 0.1
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  y_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.2
    }
    scheduler {
      speed: 20.0
      ratio: 0.1
    }
    scheduler {
      speed: 25.0
      ratio: 0.05
    }
  }
  heading_err_gain_scheduler {
    scheduler {
      speed: 4.0
      ratio: 1.0
    }
    scheduler {
      speed: 8.0
      ratio: 0.6
    }
    scheduler {
      speed: 12.0
      ratio: 0.4
    }
    scheduler {
      speed: 20.0
      ratio: 0.2
    }
    scheduler {
      speed: 25.0
      ratio: 0.1
    }
  }
  reverse_leadlag_conf {
    innerstate_saturation_level: 3000
    alpha: 1.0
    beta: 1.0
    tau: 0.0
  }
  enable_reverse_leadlag_compensation: false
  enable_look_ahead_back_control: false
  lookahead_station: 1.4224
  enable_steer_mrac_control: false
  lookahead_station_high_speed: 1.4224
  query_relative_time: 0.2
  minimum_speed_protection: 0.1
}

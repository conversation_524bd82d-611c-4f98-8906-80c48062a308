# 进行消息测试或者其他小功能的测试

import time
import math
import random

# import numpy as np
import sys
from random import *
import logging as log
from cyber.python.cyber_py3 import cyber
from modules.control.acc_control.proto.chassis_pb2 import Chassis
from modules.control.acc_control.proto.control_cmd_pb2 import ControlCommand


class MyTest():

  def __init__(self):
    print("start my test.")
    self.my_test_node = cyber.Node("my_test")

    self.chassis_writer = self.my_test_node.create_writer("channel/chassis",<PERSON><PERSON><PERSON>,6)
    self.control_writer = self.my_test_node.create_writer("channel/control",ControlCommand,6)
    pass


  def pub_chassis(self):
    cmd = Chassis()
    cmd.header.timestamp = time.time()
    cmd.speed_mps = 50.0
    cmd.driving_mode = 2
    cmd.cur_acc = 0.23
    cmd.cur_steering_angle = 0.1
    cmd.yaw_rate = 0.0
    self.chassis_writer.write(cmd)
    pass

  def pub_control(self):
    cmd = ControlCommand()
    cmd.header.timestamp = time.time()
    cmd.is_driving = 1
    cmd.tar_acc = 0.5
    self.control_writer.write(cmd)
    pass 

  def auto_run(self):
    while True:
      k = input('[Prompt] continue test or not?  [q] for Quit:  ')
      if (k == 'q'):
        return
      elif k == 'w':
        self.pub_chassis()
        print(k)
      elif k == 'e':
        self.pub_control()
        print(k)
      else:
        print("other key")




# ==============================================================================
# -- main() --------------------------------------------------------------------
# ==============================================================================


def main():
  """
  Main function
  """
  cyber.init()
 
  my_test = MyTest()
  try:
    my_test.auto_run()
    cyber.shutdown()
    
  finally:
    if my_test is not None:
      pass


if __name__ == '__main__':

    main()

    

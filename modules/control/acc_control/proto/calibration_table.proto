syntax = "proto3";

package mega.control.calibrationtable;

message ControlCalibrationTable {
  repeated ControlCalibrationInfo calibration = 1;
}

message ControlCalibrationInfo {
  double speed = 1;
  double acceleration = 2;
  double command = 3;
}

message LatControlCalibrationTable {
  repeated LatControlCalibrationInfo calibration = 1;
}

message LatControlCalibrationInfo {
  double steer_angle = 1;
  double steer_wheel_angle = 2;
}

//y = 0.6661x^2 + -2.0657x + 197.4583
message ControlCalibrationParams {
  double a = 1;
  double b = 2;
  double c = 3;
  double offset = 4;
}

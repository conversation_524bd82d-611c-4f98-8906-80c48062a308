syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/leadlag_conf.proto";
import "modules/control/acc_control/proto/gain_scheduler_conf.proto";

// controller param
message LatK2ControllerConf {
  double ts = 1;  // longitudinal controller sampling time
  int32 state_size = 2;
  int32 preview_window = 3;
  int32 max_iteration = 4;  // maximum iteration for lqr solve
  double eps = 5;        // converge threshold for lqr solver
  repeated double matrix_q = 6;  // matrix_q size = 2 
  repeated double matrix_r = 7;  // matrix_r size = 1
  double cutoff_freq = 8;              // cutoff frequency
  int32 mean_filter_window_size = 9;  // window size of mean filter
  double lat_error_limit = 10;
  double heading_error_limit = 11;
  double max_lateral_acceleration = 12;  // limit aggressive steering
  mega.control.GainScheduler lat_err_gain_scheduler = 13;
  mega.control.GainScheduler heading_err_gain_scheduler = 14;
  LeadlagConf reverse_leadlag_conf = 15;
  bool use_mean_filter = 16;
  bool use_low_pass_filter = 17;
  bool use_output_wheel_steer_filter = 18;
  bool use_curvature_filter = 19;
  bool enable_gain_scheduler = 20;
  bool query_time_nearest_point_only = 21;
  bool use_delay_prediction_compensation = 22;
  double query_relative_time = 23;
  bool use_delay_compensation = 24;
  int32 delay_steps = 25;
  bool set_steer_limit = 26;
  bool enable_reverse_leadlag_compensation = 27;
  bool enable_look_ahead_back_control = 28;
  double lookahead_station = 29 ;
  bool enable_steer_mrac_control = 30;
  double lookahead_station_high_speed = 31 ;
  double minimum_speed_protection = 32;
  double curvature_cutoff_freq = 33;              // cutoff frequency
  double feedback_ratio = 34;
  double feedforward_ratio = 35;
  bool use_output_deadzone = 36;
  double output_deadzone_threshold = 37;
}

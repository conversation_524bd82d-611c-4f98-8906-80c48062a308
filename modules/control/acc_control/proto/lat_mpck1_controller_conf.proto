syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/leadlag_conf.proto";
import "modules/control/acc_control/proto/gain_scheduler_conf.proto";

message MPCK1Weight {
  // Weight for lateral tracking error. A larger weight leads to less lateral tracking error.
  double lat_error = 1;

  // Weight for heading tracking error. A larger weight reduces heading tracking error.
  double heading_error = 2;

  // Weight combining heading error and velocity. Adjusts the influence of heading error based on
  // velocity.
  double heading_error_squared_vel = 3;

  // Weight for lateral tracking error at the terminal state of the trajectory. This improves the
  // stability of MPC.
  double terminal_lat_error = 4;

  // Weight for heading tracking error at the terminal state of the trajectory. This improves the
  // stability of MPC.
  double terminal_heading_error = 5;

  // Weight for the steering input. This surpress the deviation between the steering command and
  // reference steering angle calculated from curvature.
  double steering_input = 6;

  // Adjusts the influence of steering_input weight based on velocity.
  double steering_input_squared_vel = 77;

  // Weight for lateral jerk. Penalizes sudden changes in lateral acceleration.
  double lat_jerk = 8;

  // Weight for steering rate. Penalizes the speed of steering angle change.
  double steer_rate = 9;

  // Weight for steering angle acceleration. Regulates the rate of change of steering rate.
  double steer_acc = 10;
}


message InterpPoints {
  repeated InterpPoint points = 1;
}

message InterpPoint {
  double x = 1;
  double y = 2;
}

// controller param
message MPCK1ControllerConf {
  double ts = 1;  // longitudinal controller sampling time
  double steer_tau = 2;
  int32 dim_x = 3;
  int32 dim_u = 4;
  int32 dim_y = 5;
  double min_prediction_length = 6;
  int32 preview_window = 7;
  double input_delay = 8;
  int32 max_iteration = 9;  // maximum iteration for lqr solve
  double eps = 10;        // converge threshold for lqr solver
  bool polish = 11;
  bool verbose = 12;
  double low_curvature_thresh = 13;
  MPCK1Weight nominal_weight = 14;
  MPCK1Weight low_curvature_weight = 15;
  InterpPoints steer_rate_lim_map_by_curvature = 16;
  InterpPoints steer_rate_lim_map_by_velocity = 17;
  int32 mean_filter_window_size = 18;  // window size of mean filter
  double max_lateral_acceleration = 19;  // limit aggressive steering
  bool use_mean_filter = 20;
  bool set_steer_limit = 21;
  double minimum_speed_protection = 22;
  double admissible_position_error = 23;
  double admissible_yaw_error_rad = 24;
  double zero_steer_rad = 25;
  double prediction_dt = 26;
  double traj_resample_dist = 27;
}

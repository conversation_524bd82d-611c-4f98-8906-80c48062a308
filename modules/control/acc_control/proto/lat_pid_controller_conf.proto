syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/pid_conf.proto";
import "modules/control/acc_control/proto/gain_scheduler_conf.proto";
import "modules/control/acc_control/proto/calibration_table.proto";

// controller param
message LatPIDControllerConf {
  double ts = 1;  // longitudinal controller sampling time

  double lateral_error_limit = 2;
  double heading_error_limit = 3;

  PidConf lat_error_pid_conf = 4;
  PidConf heading_error_pid_conf = 5;

  double cutoff_freq = 6;              // cutoff frequency
  int32 mean_filter_window_size = 7;  // window size of mean filter
  double query_relative_time = 8;

  bool query_time_nearest_point_only = 9;
  bool enable_error_filter = 10;
  bool use_heading_error = 11;
  bool fix_trajectory_follow_location = 12;
  bool enable_wheel_steer_limit = 13;
  bool enable_maximum_steer_rate_limit = 14;
  bool use_output_wheel_steer_filter = 15;

  calibrationtable.LatControlCalibrationTable lat_calibration_table = 16;
  
}

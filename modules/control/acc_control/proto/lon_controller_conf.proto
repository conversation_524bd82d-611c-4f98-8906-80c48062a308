syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/calibration_table.proto";
import "modules/control/acc_control/proto/leadlag_conf.proto";
import "modules/control/acc_control/proto/pid_conf.proto";

message FilterConf {
  int32 cutoff_freq = 1;
}

message LongitudinalVec2{
  double x = 1;
  double y = 2;
}
message LongitudinalParams{
  repeated LongitudinalVec2 acceleration = 1;
  repeated LongitudinalVec2 acceleration_rate = 2;
  repeated LongitudinalVec2 deceleration = 3;
  repeated LongitudinalVec2 deceleration_rate = 4;
}

message StateUpdateParams {
  double drive_state_stop_dist = 1;
  double drive_state_offset_stop_dist = 2;
  double stopping_state_stop_dist = 3;
  double stopped_state_entry_vel = 4;
  double stopped_state_entry_acc = 5;
  double stopped_state_entry_duration_time = 6; 
  double emergency_state_overshoot_stop_dist = 7;
  double driveoff_state_stop_dist = 8;
  double driving_state_entry_vel = 9;
  double emergency_state_traj_trans_dev = 10;
  double emergency_state_traj_rot_dev = 11;  //rad
  double speed_error = 12; //m/s
  double station_error = 13;
  bool use_keep_stoped_until_steer_convergence = 14;
  bool only_speed_follow = 15;
}

message DriveOffParams {
  double driveoff_min_acc = 1;
  double driveoff_max_jerk = 2; 
}

//后续根据需求确定是否分离距离控制和速度控制的PID参数
message FollowDistanceParams { 
    double switch_speed = 1;  // low/high speed controller switch speed
    PidConf station_pid_conf = 2;
    PidConf low_speed_pid_conf = 3;
    PidConf high_speed_pid_conf = 4;
}

message FollowSpeedParams { 
  double switch_speed = 1;  // low/high speed controller switch speed
  PidConf low_speed_pid_conf = 2;
  PidConf high_speed_pid_conf = 3;
}

message EmergencyParams { 
  double emergency_min_acc = 1;
  double emergency_min_jerk = 2;
}

message StartParams { 
}

message SoppedParams { 
  double stopped_acc = 1;
}

message StoppingParams { 
  double min_fast_val = 1; //m/s^s
  double strong_stop_dist = 2;
  double weak_stop_dist = 3;
  double strong_stop_acc = 4;
  double weak_stop_acc = 5;
  double strong_acc = 6;
  double weak_acc = 7;
  double weak_stop_time = 8; //s
  double min_running_vel = 9;
  double min_running_acc = 10;
}

message LonStateParams {
  DriveOffParams driveoff_params = 1;
  FollowDistanceParams follow_distance_params = 2;
  FollowSpeedParams follow_speed_params = 3;
  EmergencyParams emergency_params = 4;
  StartParams start_params = 5;
  SoppedParams stopped_params = 6;
  StoppingParams stopping_params = 7;
}

// controller param
message LonControllerConf {
  double ts = 1;  // longitudinal controller sampling time
  double brake_minimum_action = 2;
  double throttle_minimum_action = 3;
  double speed_controller_input_limit = 4;
  double station_error_limit = 5;
  double preview_window = 6;
  double standstill_acceleration = 7;
  double max_path_remain_when_stopped = 8 ;//stop when path_remain lower 

  StateUpdateParams state_update_params = 9;
  LonStateParams lon_state_params = 10;

  FilterConf pitch_angle_filter_conf = 11;
  FilterConf station_error_filter_conf = 12;  //保持一致，不放入具体状态
  FilterConf speed_error_filter_conf = 13;  //保持一致，不放入具体状态
  FilterConf acc_filter_conf = 14;
  int32 mean_filter_window_size = 15;  // window size of mean filter
  int32 acc_mean_filter_window_size = 16;  // window size of mean filter
  int32 slope_mean_filter_window_size = 17;  // window size of mean filter
  LeadlagConf station_leadlag_conf = 18;
  LeadlagConf speed_leadlag_conf = 19;

 // soft switch
  bool use_soft_switch = 20;
  double soft_switch_speed = 21; //超过该速度才能用soft_switch功能
  double soft_sw_dece_threshold = 22;
  double soft_sw_acc_threshold = 23;

  //longitudinal_params limited
  bool use_longitudinal_params_limited = 24;
  LongitudinalParams longitudinal_params = 25;

  //pid output limited
  bool use_pid_output_acceleration_limited = 26;
  double pid_output_acceleration_limit = 27;
  double pid_output_acceleration_factor = 28;

  //torque limited
  bool use_calibration_value_rate_limited = 29;
  double calibration_value_rate = 30;

  //flag
  bool enable_leadlag_compensation = 31;
  bool enable_speed_station_preview = 32;
  bool use_acceleration_limit = 33; 
  bool use_acceleration_max_limit_follow_speed = 34;
  bool enable_slope_offset = 35;
  bool input_use_mean_filter = 36;
  bool input_use_digital_filter = 37;
  bool output_use_mean_filter = 38;
  bool output_use_digital_filter = 39;
  bool use_vehicle_mass_estimation = 40;

  calibrationtable.ControlCalibrationTable calibration_table = 41;
  calibrationtable.ControlCalibrationParams calibration_params = 42;

}

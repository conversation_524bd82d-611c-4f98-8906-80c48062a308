syntax = "proto3";

package mega.control;

//import "modules/control/acc_control/proto/chassis.proto";
//import "thirdparty/recommend_protocols/common/proto/header.proto";
import "thirdparty/recommend_protocols/location/proto/pose.proto";
import "thirdparty/recommend_protocols/planning/proto/acc_planning_output.proto";
import "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.proto";

message LocalView {
  //mega.common.Header header = 1;
  rainbowdash.control_by_wire.Chassis chassis = 1;
  rainbowdash.planning.ADCTrajectory trajectory = 2;
  rainbowdash.location.Pose localization = 3;
}

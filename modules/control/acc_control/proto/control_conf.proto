syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/lat_controller_conf.proto";
import "modules/control/acc_control/proto/lon_controller_conf.proto";
import "modules/control/acc_control/proto/mpc_controller_conf.proto";
import "modules/control/acc_control/proto/lat_pid_controller_conf.proto";
import "modules/control/acc_control/proto/lat_lqrk1_controller_conf.proto";
import "modules/control/acc_control/proto/lat_lqrk2_controller_conf.proto";
import "modules/control/acc_control/proto/lat_mpck1_controller_conf.proto";

message ControlConf {
  enum ControllerType {
    LAT_PID_CONTROLLER = 0;
    LON_CONTROLLER = 1;
    MPC_CONTROLLER = 2;
    LAT_LQRK1_CONTROLLER = 3;
    LAT_LQRK2_CONTROLLER = 4;
    LAT_MPCK1_CONTROLLER = 5;
  };

  // configs from gflags
  double control_test_duration = 1 ;
  bool is_control_test_mode = 2;
  int32 max_localization_miss_num = 3;
  int32 max_chassis_miss_num = 4;
  int32 max_planning_miss_num = 5;
  double trajectory_period = 6;
  double chassis_period = 7;
  double localization_period = 8;
  double control_period = 9;

  repeated ControllerType active_controllers = 10;
  //change trajectory_point v and a
  double max_acceleration_when_stopped = 11;
  double minimum_speed_resolution = 12;
  double soft_estop_brake = 13;//stop when timeout msg  
  bool publish_control_debug = 14;
  bool use_lat_control = 15;
  //recoed config
  bool use_debug_info_record = 16;
  int32 deque_size = 17;
  int32 save_rate = 18;

  mega.control.LatControllerConf lat_controller_conf = 19;
  mega.control.LonControllerConf lon_controller_conf = 20;
  mega.control.MPCControllerConf mpc_controller_conf = 21;
  mega.control.LatPIDControllerConf lat_pid_controller_conf = 22;
  mega.control.LatK1ControllerConf lat_lqrk1_controller_conf = 23;
  mega.control.LatK2ControllerConf lat_lqrk2_controller_conf = 24;
  mega.control.MPCK1ControllerConf lat_mpck1_controller_conf = 25;
}

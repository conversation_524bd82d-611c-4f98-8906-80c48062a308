syntax = "proto3";

package apollo.common;

import "thirdparty/recommend_protocols/common/proto/header.proto";

enum VehicleBrand {
  LINCOLN_MKZ = 0;
  GEM = 1;
  LEXUS = 2;
  TRANSIT = 3;
  GE3 = 4;
  WEY = 5;
  ZHONGYUN = 6;
  CH = 7;
  DKIT = 8;
  NEOLIX = 9;
  AION = 10;
  DONGFENG = 11;
}

message LatencyParam {
  // latency parameters, in second (s)
  double dead_time = 1;
  double rise_time = 2;
  double peak_time = 3;
  double settling_time = 4;
}

message VehicleParam {
  VehicleBrand brand = 1;
  // Car center point is car reference point, i.e., center of rear axle.
  uint32 vehicle_id = 2;
  double front_edge_to_center = 3;
  double back_edge_to_center = 4;
  double left_edge_to_center = 5;
  double right_edge_to_center = 6;

  double length = 7;
  double width = 8;
  double height = 9;

  double min_turn_radius = 10;
  double max_acceleration = 11;
  double min_deceleration = 12;

  // The following items are used to compute trajectory constraints in
  // planning/control/canbus,
  // vehicle max steer angle deg
  double max_steer_angle = 13;
  // vehicle max steer rate; how fast can the steering wheel turn. deg/s
  double max_steer_angle_rate = 14;
  // vehicle min steer rate;
  double min_steer_angle_rate = 15;
  // ratio between the turn of steering wheel and the turn of wheels
  double steer_ratio = 16;
  // the distance between the front and back wheels
  double wheel_base = 17;
  // Tire effective rolling radius (vertical distance between the wheel center
  // and the ground).
  double wheel_rolling_radius = 18;

  // minimum differentiable vehicle speed, in m/s
  float max_abs_speed_when_stopped = 19;

  // minimum value get from chassis.brake, in percentage
  double brake_deadzone = 20;
  // minimum value get from chassis.throttle, in percentage
  double throttle_deadzone = 21;

  double vehicle_mass = 22;

  // vehicle latency parameters
  LatencyParam steering_latency_param = 23;
  LatencyParam throttle_latency_param = 24;
  LatencyParam brake_latency_param = 25;
}

message VehicleConfig {
  rainbowdash.common.Header header = 1;
  VehicleParam vehicle_param = 2;
//  Extrinsics extrinsics = 3;
}

syntax = "proto3";

package mega.common;

//import "thirdparty/recommend_protocols/location/proto/pose.proto";
import "thirdparty/recommend_protocols/common/proto/geometry.proto";

message VehicleState {
  double x = 1;
  double y = 2;
  double z = 3;
  double timestamp = 4;
  double roll = 5;
  double pitch = 6;
  double yaw = 7;
  double heading = 8;
  double kappa = 9;
  double linear_velocity = 10;
  double angular_velocity = 11;
  double linear_acceleration = 12;
  rainbowdash.common.Quaternion orientation = 13;
  //apollo.localization.Pose pose = 15;
  //double steering_percentage = 16;
}



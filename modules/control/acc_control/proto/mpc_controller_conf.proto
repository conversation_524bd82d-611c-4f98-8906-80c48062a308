syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/calibration_table.proto";

import "modules/control/acc_control/proto/gain_scheduler_conf.proto";

// model predictive control param
message MPCControllerConf {
  double ts = 1;      // sample time (dt) 0.01 sec, configurable
  double cf = 2;      // corner stiffness front: N/rad
  double cr = 3;      // corner stiffness rear: N/rad
  int32 mass_fl = 4;  // mass forward left
  int32 mass_fr = 5;  // mass forward right
  int32 mass_rl = 6;  // mass rear left
  int32 mass_rr = 7;  // mass rear right
  double eps = 8;     // converge threshold

  // output variable (control state) weight matrix
  // (lateral_error, lateral_error_rate, heading_error, heading_error_rate,
  //  station_error, speed_error)
  repeated double matrix_q = 9;

  // manipulated variable weight matrix
  // (steer, acceleration)
  repeated double matrix_r = 10;

  int32 cutoff_freq = 11;              // cutoff frequency
  int32 mean_filter_window_size = 12;  // window size of mean filter
  // for a normal car, it should be in range[16, 18]
  int32 max_iteration = 13;  // maximum iteration for lqr solve
  double max_lateral_acceleration = 14;  // limit aggressive steering
  double standstill_acceleration = 15;
  double brake_minimum_action = 16;
  double throttle_minimum_action = 17;
  mega.control.GainScheduler lat_err_gain_scheduler = 18;
  mega.control.GainScheduler heading_err_gain_scheduler = 19;
  mega.control.GainScheduler steer_weight_gain_scheduler = 20;
  mega.control.GainScheduler feedforwardterm_gain_scheduler = 21;
  calibrationtable.ControlCalibrationTable calibration_table = 22;
  bool enable_mpc_feedforward_compensation = 23 ;
  double unconstrained_control_diff_limit = 24;
}

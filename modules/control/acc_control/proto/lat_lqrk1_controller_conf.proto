syntax = "proto3";

package mega.control;

import "modules/control/acc_control/proto/leadlag_conf.proto";
import "modules/control/acc_control/proto/gain_scheduler_conf.proto";

// controller param
message LatK1ControllerConf {
  double ts = 1;  // longitudinal controller sampling time

  double preview_window = 2;
  double x_error_limit = 3;
  double y_error_limit = 4;
  double heading_error_limit = 5;
  double eps = 6;        // converge threshold for lqr solver
  repeated double matrix_q = 7;  // matrix_q size = 3 + preview_window
  int32 cutoff_freq = 8;              // cutoff frequency
  int32 mean_filter_window_size = 9;  // window size of mean filter
  int32 max_iteration = 10;  // maximum iteration for lqr solve
  double max_lateral_acceleration = 11;  // limit aggressive steering
  mega.control.GainScheduler x_err_gain_scheduler = 12;
  mega.control.GainScheduler y_err_gain_scheduler = 13;
  mega.control.GainScheduler heading_err_gain_scheduler = 14;
  LeadlagConf reverse_leadlag_conf = 15;
  bool enable_reverse_leadlag_compensation = 16;
  bool enable_look_ahead_back_control = 17;
  double lookahead_station = 18 ;
  bool enable_steer_mrac_control = 19;
  double lookahead_station_high_speed = 20 ;
  double query_relative_time = 21;
  double minimum_speed_protection = 22;

}

syntax = "proto3";
package mega.control;

import "thirdparty/recommend_protocols/planning/proto/pnc_point.proto";
import "thirdparty/recommend_protocols/common/proto/header.proto";
import "modules/control/acc_control/proto/pid_conf.proto";

message SimpleLongitudinalDebug {
  double station_reference = 1;
  double current_station = 2;
  double station_error = 3;
  double preview_station_error = 4;
  double speed_reference = 5;
  double speed_error = 6;
  double preview_station_reference = 7;
  double preview_speed_reference = 8;
  double preview_speed_error = 9;
  double preview_acceleration_reference = 10;
  double heading_error = 11;
  double current_speed = 12;
  double lon_speed = 13;
  double lon_acceleration = 14;
  double acceleration_reference = 15;
  double acceleration_error = 16;
  double jerk_reference = 17;
  double current_jerk = 18;
  double jerk_error = 19;

  rainbowdash.planning.TrajectoryPoint current_matched_point = 20;
  rainbowdash.planning.TrajectoryPoint current_reference_point = 21;
  rainbowdash.planning.TrajectoryPoint preview_reference_point = 22;

  int32 is_replan = 23;
  PidConf  station_pid_conf = 24;
  double mean_filter_station_error = 25;
  double digital_filter_station_error = 26;
  double filter_station_error = 27;
  double lon_delay_time = 28;
  double station_error_limited = 29;
  int32 s_pid_saturation_status = 30;
  double speed_error_ori = 31;
  double mean_filter_speed_error = 32;
  double digital_filter_speed_error = 33;
  double filter_speed_error = 34;
  double speed_offset = 35;
  PidConf  speed_pid_conf = 36;
  double speed_controller_input_limited = 37;
  int32 v_pid_saturation_status = 38;
  double v_pid_integrator = 39;
  double acceleration_cmd_closeloop = 40;
  double pid_output_acceleration_limited = 41;
  double pid_output_acceleration_limit = 42;
  double acceleration_cmd = 43;

  bool is_full_stop = 44;
  double path_remain = 45;

  double calibration_compensation_acc = 46;
  double slope_offset_compensation = 47;
  double slope_compensation_acc = 48;
  double mean_filter_acc = 49;
  double digital_filter_acc = 50;
  double filter_acc = 51;
  double jerk_limited_acc =52;
  double max_acceleration_limited = 53;
  double min_deceleration_limited = 54;
  double acceleration_limited_acc = 55; 
  double soft_sw_acc = 56;
  double final_acc = 57;
  double calibration_value = 58;
  double calibration_value_rate_limited = 59;
  double preview_time = 60;
  int32 input_use_mean_filter = 61;
  int32 input_use_digital_filter = 62;
  int32 output_use_mean_filter = 63;
  int32 output_use_digital_filter = 64;
  int32 use_pid_output_acceleration_limited = 65;
  int32 use_calibration_value_rate_limited = 66;
  int32 use_soft_switch = 67;
  int32 use_jerk_limited = 68;
  int32 use_acc_limited = 69;
  double soft_switch_speed = 70;
  int32 drive_mode = 71; //用于查看握手状态 0 默认 1 ACC 2 LCC 3 MANUAL
  double maximum_user_speed = 72; //接收来自hmi的
  double cruise_interval = 73; // 接收来自hmi的
  int32 lon_control_state = 74;
  double pre_final_acc = 75;
  double  control_dt = 76;
  double cur_time = 77;
  double v_error_p_contribution = 78;
  double v_error_i_contribution = 79;

  uint64 chassis_seq = 80;
  uint64 localization_seq = 81;
  uint64 trajectory_seq = 82;
}

message SimpleLateralDebug {
// current planning target point
  rainbowdash.planning.TrajectoryPoint current_target_point = 1;

  double ori_lateral_error = 2;
  double lateral_error = 3;
  double lateral_error_rate = 4;
  // time derivative of lateral error rate, in m/s^2
  double lateral_acceleration = 5;
  // second time derivative of lateral error rate, in m/s^3
  double lateral_jerk = 6;

  double ref_heading = 7;
  double heading = 8;
  double ori_heading_error = 9;
  double filter_heading_error = 10;
  double heading_error = 11;
  double heading_error_rate = 12;
  double ref_heading_rate = 13;
  double heading_rate = 14;
    // heading_acceleration, as known as yaw acceleration, is the time derivative
  // of heading rate,  in rad/s^2
  double ref_heading_acceleration = 15;
  double heading_acceleration = 16;
  double heading_error_acceleration = 17;

  // heading_jerk, as known as yaw jerk, is the second time derivative of
  // heading rate, in rad/s^3
  double ref_heading_jerk = 18;
  double heading_jerk = 19;
  double heading_error_jerk = 20;
  
  double curvature = 21;

  double lateral_error_limit = 22;
  double lateral_error_limited = 23;
  double lat_err_integrator = 24;
  double lat_err_wheel_steer_closecmd = 25;
  double heading_error_limit = 26;
  double heading_error_limited = 27;
  double heading_err_integrator = 28;
  double heading_err_wheel_steer_closecmd = 29;
  double wheel_steer_angle_feedback = 30;
  double wheel_steer_angle_feedforward = 31;
  double wheel_steer_angle_rad_cmd = 32;
  double wheel_steer_angle_cmd = 33;
  double wheel_steer_angle_limit = 34;
  double cur_speed = 35;
  double wheel_steer_angle_limited = 36;
  double filter_wheel_steer_angle = 37;

  double tar_steer_wheel_angle = 38;
  double final_steer_wheel_angle = 39;
  double cur_steer_wheel_angle = 40;
  double lat_error_p_contribution = 41;
  double lat_error_i_contribution = 42;
  double heading_error_p_contribution = 43;
  double heading_error_i_contribution = 44;
  double steer_wheel_max_rate = 45;
  double cur_time = 46;
}



message SimpleLateralLQRD1Debug {
  double lateral_error = 1;
  double ref_heading = 2;
  double heading = 3;
  double heading_error = 4;
  double heading_error_rate = 5;
  double lateral_error_rate = 6;
  double curvature = 7;
  double steer_angle = 8;
  double steer_angle_feedforward = 9;
  double steer_angle_lateral_contribution = 10;
  double steer_angle_lateral_rate_contribution = 11;
  double steer_angle_heading_contribution = 12;
  double steer_angle_heading_rate_contribution = 13;
  double steer_angle_feedback = 14;
  double steering_position = 15;
  double ref_speed = 16;
  double steer_angle_limited = 17;

  // time derivative of lateral error rate, in m/s^2
  double lateral_acceleration = 18;
  // second time derivative of lateral error rate, in m/s^3
  double lateral_jerk = 19;

  double ref_heading_rate = 20;
  double heading_rate = 21;

  // heading_acceleration, as known as yaw acceleration, is the time derivative
  // of heading rate,  in rad/s^2
  double ref_heading_acceleration = 22;
  double heading_acceleration = 23;
  double heading_error_acceleration = 24;

  // heading_jerk, as known as yaw jerk, is the second time derivative of
  // heading rate, in rad/s^3
  double ref_heading_jerk = 25;
  double heading_jerk = 26;
  double heading_error_jerk = 27;

  // modified lateral_error and heading_error with look-ahead or look-back
  // station, as the feedback term for control usage
  double lateral_error_feedback = 28;
  double heading_error_feedback = 29;

  // current planning target point
  rainbowdash.planning.TrajectoryPoint current_target_point = 30;

  // Augmented feedback control term in addition to LQR control
  double steer_angle_feedback_augment = 31;

}

// * derivative_x = v * cos(pha)
// * derivative_y = v * sin(pha)
// * derivative_pha = v / R = v * tan(beta) / wheel_base
message SimpleLateralLQRK1Debug {
  double cur_x = 1;
  double cur_y = 2;
  double cur_heading = 3;
  double tar_x = 4;
  double tar_y = 5;
  double tar_heading = 6;
  double delta_x = 7;
  double delta_y = 8;
  double delta_heading = 9;
  double use_mean_filter = 10;
  double cur_v = 11;
  double curvature = 12;
  double feed_forward = 13;
  double pre_steer_angle = 14;
  double cur_steer_angle = 15;
  double tar_steer_angle = 16;
  double steer_angle_feedforward = 17;
  double steer_angle_feedback = 18;
  double steer_angle_limit = 19;
  double x_error_contribution = 20;
  double y_error_contribution = 21;
  double heading_error_contribution = 22;
}

// * derivative_lateral_err = vx * sin(theta_x - thrta_r) 
// * derivative_heading_err = v * tan(beta) / wheel_base 
message SimpleLateralLQRK2Debug {
  // current planning target point
  rainbowdash.planning.TrajectoryPoint current_target_point = 1;

  double ori_lateral_error = 2;
  double lateral_error = 3;
  double lateral_error_rate = 4;
  // time derivative of lateral error rate, in m/s^2
  double lateral_acceleration = 5;
  // second time derivative of lateral error rate, in m/s^3
  double lateral_jerk = 6;

  double ref_heading = 7;
  double heading = 8;
  double ori_heading_error = 9;
  double filter_heading_error = 10;
  double heading_error = 11;
  double heading_error_rate = 12;
  double ref_heading_rate = 13;
  double heading_rate = 14;
  // heading_acceleration, as known as yaw acceleration, is the time derivative
  // of heading rate,  in rad/s^2
  double ref_heading_acceleration = 15;
  double heading_acceleration = 16;
  double heading_error_acceleration = 17;

  // heading_jerk, as known as yaw jerk, is the second time derivative of
  // heading rate, in rad/s^3
  double ref_heading_jerk = 18;
  double heading_jerk = 19;
  double heading_error_jerk = 20;

  double cur_speed = 21;
  double ori_curvature = 22;
  double curvature = 23;

  double wheel_steer_angle_feedforward = 24;
  double wheel_steer_angle_feedback = 25;
  double wheel_steer_angle_cmd = 26;
  double wheel_steer_angle_rad_cmd = 27;
  double wheel_steer_angle_limit = 28;
  double wheel_steer_angle_limited = 29;
  double filter_wheel_steer_angle = 30;

  double tar_steer_wheel_angle = 31;
  double final_steer_wheel_angle = 32;
  double lat_error_contribution = 33;
  double heading_error_contribution = 34;
  double cur_steer_wheel_angle = 35;
  double steer_wheel_max_rate = 36;
  double cur_time = 37;
}

message SimpleLateralMPCK1Debug {
  double lateral_error = 1;
  double ref_heading = 2;
  double heading = 3;
  double heading_error = 4;
  double curvature = 5;
  double cur_steer_angle = 6;
  double cur_speed = 7;

  double delay_lateral_error = 8;
  double delay_heading_error = 9;
  double delay_steer_angle = 10;

  double prediction_dt = 11;
  double wheel_steer_angle_cmd = 12;
  double wheel_steer_angle_limit = 13;
  double wheel_steer_angle_limited = 14;
  double filter_wheel_steer_angle = 15;
  double tar_steer_wheel_angle = 16;
  double steer_wheel_max_rate = 17;
  double final_steer_wheel_angle = 18;
  double cur_steer_wheel_angle = 19;
}

message ControlDebug {
  SimpleLongitudinalDebug simple_longitudinal_debug = 1;
  SimpleLateralDebug simple_lateral_debug = 2;
  SimpleLateralLQRK1Debug simple_lateral_lqrk1_debug = 3;
  // SimpleMPCDebug simple_mpc_debug = 4;
  // SimpleLateralLQRD1Debug simple_lateral_lqrd1_debug = 5;
  SimpleLateralLQRK2Debug simple_lateral_lqrk2_debug = 4;
  SimpleLateralMPCK1Debug simple_lateral_mpck1_debug = 5;
}

message ControlCommand {
  rainbowdash.common.Header header = 1;
  uint32 is_driving =2;
  double tar_acc = 3;
  uint64 tar_torque = 4;
  uint32 is_steering = 5;
  double steering_target = 6;
  uint32 is_braking = 7;
  double tar_deacc = 8;
  uint32 is_gear = 9;
  uint32 tar_gear = 10;
}


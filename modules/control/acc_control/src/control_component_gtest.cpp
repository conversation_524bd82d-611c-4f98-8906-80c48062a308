
#include "control_component.h"
#include <thread>
#include <string>

#include "cyber/common/file.h"
#include "cyber/common/log.h"
#include "cyber/cyber.h"
#include "gtest/gtest.h"

#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"

#include "modules/control/acc_control/proto/chassis.pb.h"
#include "thirdparty/recommend_protocols/location/proto/pose.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/acc_planning_output.pb.h"
#include "modules/control/acc_control/proto/control_cmd.pb.h"
#include "modules/control/acc_control/proto/control_conf.pb.h"
#include "modules/control/acc_control/proto/local_view.pb.h"
#include "controller/lon_pid_control.h"

using apollo::cyber::Node;
using apollo::cyber::Reader;
using apollo::cyber::Writer;

using mega::control::LocalView;
using mega::chassis::Chassis;
using mega::control::ControlCommand;
using rainbowdash::location::Pose;
using rainbowdash::planning::ADCTrajectory;
using mega::control::SimpleLongitudinalDebug;

using mega::control::ControlConf;

std::string FLAGS_test_data_dir;
std::string FLAGS_test_localization_file;
std::string FLAGS_test_planning_file;
std::string FLAGS_test_chassis_file;

std::string FLAGS_control_conf_file;
std::string FLAGS_test_local_view_file;
std::string FLAGS_test_debug_info_file;
bool FLAGS_is_control_test_mode;

using namespace mega::control;

class ControlComponentTest : public ::testing::Test
{
public:
    virtual void SetUp()
    {
        FLAGS_control_conf_file =
            "/workdir/drive-core/build/output/conf/control_conf_carla.pb.txt";
        FLAGS_is_control_test_mode = true;

        SetupCyber();
    }

protected:
    bool FeedTestData();
    void SetupCyber();
    bool RunControl(const std::string &test_case_name);
    bool RunSingleControl(const std::string &test_case_name);

protected:
    bool is_cyber_initialized_ = false;
    std::mutex mutex_;
    cyber::TimerComponentConfig component_config_;

    // cyber readers/writers
    std::shared_ptr<Writer<Chassis>> chassis_writer_;
    std::shared_ptr<Writer<Pose>> localization_writer_;
    std::shared_ptr<Writer<ADCTrajectory>> planning_writer_;
    std::shared_ptr<Reader<ControlCommand>> control_reader_;

    std::shared_ptr<ControlComponent> control_component_ = nullptr;
    ControlCommand control_command_;
    Chassis chassis_;
    ADCTrajectory trajectory_;
    Pose localization_;

    std::shared_ptr<LonPIDController> lon_pid_control_;
};

void ControlComponentTest::SetupCyber()
{
    if (is_cyber_initialized_)
    {
        return;
    }

    // init cyber framework
    apollo::cyber::Init("control_test");

    // Clock::SetMode(apollo::cyber::proto::MODE_CYBER);

    std::shared_ptr<apollo::cyber::Node> node(
        apollo::cyber::CreateNode("control_test"));

    chassis_writer_ = node->CreateWriter<Chassis>("channel/chassis");
    localization_writer_ =
        node->CreateWriter<Pose>("location/vehicleinfo");
    planning_writer_ =
        node->CreateWriter<ADCTrajectory>("accplanning/out/trajectory");

    control_reader_ = node->CreateReader<ControlCommand>(
        "channel/control",
        [this](const std::shared_ptr<ControlCommand> &control_command)
        {
            ADEBUG << "Received planning data: run planning callback.";
            std::lock_guard<std::mutex> lock(mutex_);
            control_command_.CopyFrom(*control_command);
        });

    is_cyber_initialized_ = true;
}

bool ControlComponentTest::FeedTestData()
{
    // Localization
    if (!FLAGS_test_localization_file.empty())
    {
        if (!cyber::common::GetProtoFromFile(
                FLAGS_test_data_dir + FLAGS_test_localization_file,
                &localization_))
        {
            AERROR << "Failed to load localization file " << FLAGS_test_data_dir
                   << FLAGS_test_localization_file;
            return false;
        }
    }

    // Planning
    if (!FLAGS_test_planning_file.empty())
    {
        if (!cyber::common::GetProtoFromFile(
                FLAGS_test_data_dir + FLAGS_test_planning_file, &trajectory_))
        {
            AERROR << "Failed to load planning file " << FLAGS_test_data_dir
                   << FLAGS_test_planning_file;
            return false;
        }
    }

    // Chassis
    if (!FLAGS_test_chassis_file.empty())
    {
        if (!cyber::common::GetProtoFromFile(
                FLAGS_test_data_dir + FLAGS_test_chassis_file, &chassis_))
        {
            AERROR << "Failed to load chassis file " << FLAGS_test_data_dir
                   << FLAGS_test_chassis_file;
            return false;
        }
    }

    AINFO << "Successfully feed proto files.";
    return true;
}

bool ControlComponentTest::RunControl(const std::string &test_case_name)
{
    ACHECK(FeedTestData()) << "Failed to feed test data";

    std::shared_ptr <Node> node(apollo::cyber::CreateNode("control_node"));
    auto node_cfg = "conf/acc_control_server.json";
    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        return 1;
    }
    auto cfg_path = "conf/control_conf_carla.pb.txt";
    control_component_.reset(new ControlComponent(node,nodeCfg,cfg_path));

    std::this_thread::sleep_for(std::chrono::milliseconds(1));

    // feed topics
    planning_writer_->Write(trajectory_);
    chassis_writer_->Write(chassis_);
    localization_writer_->Write(localization_);
    AERROR<<"trajectory: "<<trajectory_.ShortDebugString();
    AERROR<<"chassis: "<<chassis_.ShortDebugString();
    AERROR<<"localization: "<<localization_.ShortDebugString();
    

    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    control_command_.Clear();
    AERROR<<control_command_.ShortDebugString();

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));

    AERROR<<control_command_.ShortDebugString();

    return true;
}

// TEST_F(ControlComponentTest, simple_test)
// {
//     FLAGS_test_data_dir = "/workdir/drive-core/build/output/conf/";
//     FLAGS_test_localization_file = "1_localization.pb.txt";
//     FLAGS_test_planning_file = "1_planning.pb.txt";
//     FLAGS_test_chassis_file = "1_chassis.pb.txt";
//     bool run_control_success = RunControl("simple_test_0");
//     EXPECT_TRUE(run_control_success);
// }

bool ControlComponentTest::RunSingleControl(const std::string &test_case_name)
{
    ControlConf control_conf;
    auto cfg_path = "conf/control_conf_carla.pb.txt";
    if(false == cyber::common::GetProtoFromFile(cfg_path, &control_conf)){
        AERROR << __func__<< ": Unable to load control conf file: " <<cfg_path;
        return false;
    }

    std::string file_path = FLAGS_test_data_dir + FLAGS_test_local_view_file;
    std::ifstream ifs(file_path);
    if (!ifs.is_open()) {
        AERROR << "Can not find config file: " << file_path;
        return false;
    }
    nlohmann::json jcfg;
    ifs >> jcfg;
    std::string json_str = jcfg.dump();
    AERROR<< " json_str info: " << json_str;
    LocalView local_view;
    google::protobuf::util::JsonStringToMessage(json_str, &local_view);
    AERROR<< " local_view info: " << local_view.ShortDebugString();

    std::string file_path1 = FLAGS_test_data_dir + FLAGS_test_debug_info_file;
    std::ifstream ifs1(file_path1);
    if (!ifs1.is_open()) {
        AERROR << "Can not find config file: " << file_path1;
        return false;
    }
    nlohmann::json jcfg1;
    ifs1 >> jcfg1;
    std::string json_str1 = jcfg1.dump();
    AERROR<< " json_str_debug_info: " << json_str1;
    SimpleLongitudinalDebug debug_info;
    google::protobuf::util::JsonStringToMessage(json_str1, &debug_info);
    AERROR<< " debug_info info: " << debug_info.ShortDebugString();

    local_view.mutable_localization()->mutable_header()->set_timestamp(debug_info.cur_time());
    std::shared_ptr<DependencyInjector> injector = std::make_shared<DependencyInjector>();
    injector->vehicle_state()->Update(local_view.localization(),
                                     local_view.chassis());
    lon_pid_control_.reset(new LonPIDController());
    lon_pid_control_->Init(injector,&control_conf);

    ControlCmd cmd;
    lon_pid_control_->ComputeControlCommand(&local_view.localization(), 
                                        &local_view.chassis(),
                                        &local_view.trajectory(), &cmd);

    return true;
}

TEST_F(ControlComponentTest, single_test)
{
    FLAGS_test_data_dir = "/workdir/drive-core/modules/control/acc_control/conf/";
    FLAGS_test_local_view_file = "1_local_view.json";
    FLAGS_test_debug_info_file = "1_debug_info.json";
    bool run_success = RunSingleControl("single_test_0");
    EXPECT_TRUE(run_success);
}

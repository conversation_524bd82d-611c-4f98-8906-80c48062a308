#pragma once

#include <memory>
#include <string>
#include <cstdlib>
#include <atomic>
#include "common/file.h"
#include "base/util/config_parser.hpp"
#include <google/protobuf/util/json_util.h>
#include "exception_type.h"

#ifndef CYBER_TIMER_EN
#include "timer_common.h"
#endif
#include "cyber/time/time.h"
#include "controller/controller_agent.h"
#include "cyber/cyber.h"
#include "cyber/time/clock.h"
#include "cyber/node/node_channel_impl.h"

#include "thirdparty/recommend_protocols/location/proto/pose.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/acc_planning_output.pb.h"
#include "modules/control/acc_control/proto/local_view.pb.h"
#include "modules/control/acc_control/proto/control_cmd.pb.h"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_control.pb.h"
#include "modules/control/acc_control/proto/control_conf.pb.h"
#include "common/configs/vehicle_config_helper.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"
#include "thirdparty/recommend_protocols/common/proto/geometry.pb.h"
#include "thirdparty/recommend_protocols/interactive/proto/acchmi.pb.h"
#include "common/debug/debug_info.h"
#include "thirdparty/recommend_protocols/common/proto/exception.pb.h"

using rainbowdash::common::CommonBool;
using rainbowdash::common::CommonGearPosition;
using rainbowdash::control_by_wire::Chassis;
using rainbowdash::planning::ADCTrajectory;
using rainbowdash::location::Pose;
using mega::control::ControlCommand;
using rainbowdash::control_by_wire::LatAndLongControlCmd;
using rainbowdash::control_by_wire::TorqueControlData;
using rainbowdash::control_by_wire::AccelerationControlData;
using mega::control::LocalView;
using mega::control::ControlDebug;
using apollo::cyber::Time;
using apollo::cyber::Node;
using apollo::cyber::Writer;
using apollo::cyber::Reader;
using namespace apollo::cyber;
using namespace mega::common;
using namespace apollo::cyber::common;
using namespace apollo::common;
using mega::control::ControlConf;

using rainbowdash::planning::ACCState;
using rainbowdash::planning::LCCState;
using rainbowdash::interactive::Velocity;
using rainbowdash::interactive::CruiseInterval;
using rainbowdash::planning::HwpDecisionToPlanningUpdateMsg;
using rainbowdash::planning::HwpDecisionToPlanningTriggerMsg;
using rainbowdash::common::ExceptionPtr;

namespace mega {
namespace control {

    using apollo::cyber::Clock;
    
    class ControlComponent
    {
    public:
        ControlComponent(std::shared_ptr <Node> node, NodeCfg node_cfg, 
                                                std::string file_path, std::string vehicle_param_path);
        ~ControlComponent();

        bool Init();

        bool Proc();

        bool DebugProc();

    private:
        /* data */
        void OnChassis(const std::shared_ptr<Chassis> &chassis);

        void OnPlanning(const std::shared_ptr<ADCTrajectory> &trajectory);

        void OnLocalization(const std::shared_ptr<Pose> &localization);
        
        void OnDecisionUpdate(const std::shared_ptr<HwpDecisionToPlanningUpdateMsg> &dec_update);

        void HmiVelocityCallback(const std::shared_ptr <Velocity>& msg);
        void HmiCruiseIntervalCallback(const std::shared_ptr <CruiseInterval>& msg);
        void DecisionACCStateCallback(const std::shared_ptr <ACCState>& msg);
        void DecisionLCCStateCallback(const std::shared_ptr <LCCState>& msg);
        void HwpDecisionToPlanningUpdateMsgCallback(const std::shared_ptr <HwpDecisionToPlanningUpdateMsg>& msg);
        void HwpDecisionToPlanningTriggerMsgCallback(const std::shared_ptr <HwpDecisionToPlanningTriggerMsg>& msg);

        bool ProduceControlCommand(ControlCmd *control_cmd, ControlDebug *debug);
        bool CheckInput(LocalView *local_view);
        bool CheckTimestamp(const LocalView &local_view);

        void ResetAndProduceZeroControlCommand(ControlCmd *control_cmd);

        void TransferControlCmdType(
            LatAndLongControlCmd *control_command, ControlCmd *control_cmd);
        void PubException(exception::ExceptionType err_type, const std::string& name);

        std::string PrintDebugInfo(const ControlConf *control_conf, ControlDebug *debug);
        std::string PrintInputInfo(LocalView *local_view, uint32_t type);
    private:
        double init_time_;

        ControlConf control_conf_;

        ControllerAgent controller_agent_;
        std::shared_ptr<DependencyInjector> injector_;
        bool FLAGS_use_control_submodules = false;

        std::shared_ptr<Reader<Chassis>> chassis_reader_;
        std::shared_ptr<Reader<ADCTrajectory>> trajectory_reader_;
        std::shared_ptr<Reader<Pose>> localization_reader_;
        std::shared_ptr<Reader<HwpDecisionToPlanningUpdateMsg>> decision_update_reader_;
        std::shared_ptr<Reader<HwpDecisionToPlanningTriggerMsg>> decision_trigger_reader_;

        std::shared_ptr<Writer<LatAndLongControlCmd>> control_cmd_writer_;
        std::shared_ptr<Writer<LatAndLongControlCmd>> aion_control_cmd_writer_;
        std::shared_ptr<Writer<ControlDebug>> control_debug_writer_;
        std::shared_ptr<Writer<ExceptionPtr>> pExceptionCaptureInfo = nullptr;

#ifdef CYBER_TIMER_EN
        std::shared_ptr <apollo::cyber::Timer> pControlProc = nullptr;
        std::shared_ptr <apollo::cyber::Timer> pDebugProc = nullptr;
#else
        std::unique_ptr <TimerCommon::Timer> pControlProc = nullptr;
        std::unique_ptr <TimerCommon::Timer> pDebugProc = nullptr;
#endif

        Pose latest_localization_;
        Chassis latest_chassis_;
        ADCTrajectory latest_trajectory_;
        LocalView local_view_;
        ControlDebug control_debug_;
        HwpDecisionToPlanningUpdateMsg decison_update_;

        std::mutex mutex_;
        bool estop_ = false;
        bool publish_control_debug_ = false;
        bool use_lat_control_ = false;

        std::shared_ptr <Node> node_ = nullptr;
        std::string file_path_;
        NodeCfg node_cfg_;
        std::string vehicle_param_path_;

        //debug info
        std::unique_ptr<RecordDebugInfo> record_debug_info_;
        bool use_debug_info_record_ = false;
        // std::string l2_enter_time_;
        // std::string l2_quit_time_;
        std::atomic<bool> l2_enter_quit_flag_{false}; //true:enter false:quit
        bool enter_quit_flag_ = false;

        std::map<uint32_t, std::string> l2_control_exception_map_={
            {0x0d000001, "HWP_CONTROL_CHASSIS_LOSS"},
            {0x0d000002, "HWP_CONTROL_LOCATION_LOSS"},
            {0x0d000003, "HWP_CONTROL_PLAN_LOSS"}
        };
    };
    
}
}


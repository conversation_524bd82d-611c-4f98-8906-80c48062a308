#include "control_component.h"
#include "common/common_util.h"
#include "modules/common/base/logproto.h"

namespace mega {
namespace control {
    ControlComponent::ControlComponent(std::shared_ptr <Node> node, 
            NodeCfg node_cfg, std::string file_path, std::string vehicle_param_path):
            node_(node),file_path_(file_path),node_cfg_(node_cfg),
            vehicle_param_path_(vehicle_param_path){
        Init();
        std::cout << "ACC ControlComponent init done. Enjoy." << std::endl;
    }
    ControlComponent::~ControlComponent(){
        // record_debug_info_->WriteJsonFile(12345672,"./");
        // std::cout << "ACC ControlComponent finish done. Enjoy." << std::endl;
    }

    bool ControlComponent::Init(){
        injector_ = std::make_shared<DependencyInjector>();
        init_time_ = GetTimeS(nullptr);

        AINFO << "Control init, starting ...";

        if(false == cyber::common::GetProtoFromFile(file_path_, &control_conf_)){
            AERROR << __func__<< ": Unable to load control conf file: " <<file_path_;
            std::exit(0);
        }

        AINFO << __func__<< "Conf file: " << file_path_ << " is loaded.";

        auto vehicle_config_helper = VehicleConfigHelper::Instance();
        vehicle_config_helper->Init(vehicle_param_path_);

        if(!FLAGS_use_control_submodules &&
            !controller_agent_.Init(injector_, &control_conf_)){
                AERROR << __func__<<"Control init controller failed! Stopping... ";
                return false;
        }
        publish_control_debug_ = control_conf_.publish_control_debug();
        use_lat_control_ = control_conf_.use_lat_control();

        ReaderConfig chassis_reader_config;
        auto acc_chassis = node_cfg_.getSubChannel("acc_chassis");
        chassis_reader_config.channel_name = acc_chassis.name;
        chassis_reader_config.pending_queue_size = acc_chassis.pendingQueueSize;

        chassis_reader_ = node_->CreateReader<Chassis>(chassis_reader_config, nullptr);

        cyber::ReaderConfig planning_reader_config;
        auto acc_trajectory = node_cfg_.getSubChannel("acc_trajectory");
        planning_reader_config.channel_name = acc_trajectory.name;
        planning_reader_config.pending_queue_size = acc_trajectory.pendingQueueSize;

        trajectory_reader_ =
            node_->CreateReader<ADCTrajectory>(planning_reader_config, nullptr);

        cyber::ReaderConfig localization_reader_config;
        auto acc_localization = node_cfg_.getSubChannel("acc_localization");
        localization_reader_config.channel_name = acc_localization.name;
        localization_reader_config.pending_queue_size = acc_localization.pendingQueueSize;

        localization_reader_ = node_->CreateReader<Pose>(
            localization_reader_config, nullptr);

        auto hmi_velocity = node_cfg_.getSubChannel("hmi_velocity"); 
        auto pReaderHmiVelocity = node_->CreateReader<Velocity>(hmi_velocity.name, 
                                                                                std::bind(&ControlComponent::HmiVelocityCallback,this,std::placeholders::_1));
        auto hmi_cruise_interval = node_cfg_.getSubChannel("hmi_cruise_interval"); 
        auto pReaderHmiCruiseInterval = node_->CreateReader<CruiseInterval>(hmi_cruise_interval.name, 
                                                                                std::bind(&ControlComponent::HmiCruiseIntervalCallback,this,std::placeholders::_1));
        auto decision_out_acc_state = node_cfg_.getSubChannel("decision_out_acc_state"); 
        auto pReaderDecisionACCState = node_->CreateReader<ACCState>(decision_out_acc_state.name, 
                                                                                std::bind(&ControlComponent::DecisionACCStateCallback,this,std::placeholders::_1));
        auto decision_out_lcc_state = node_cfg_.getSubChannel("decision_out_lcc_state"); 
        auto pReaderDecisionLCCState = node_->CreateReader<LCCState>(decision_out_lcc_state.name, 
                                                                                std::bind(&ControlComponent::DecisionLCCStateCallback,this,std::placeholders::_1));

        auto decision_to_planning_update = node_cfg_.getSubChannel("decision_to_planning_update"); 
        apollo::cyber::ReaderConfig decision_to_planning_update_cfg;
        decision_to_planning_update_cfg.channel_name = decision_to_planning_update.name;
        decision_to_planning_update_cfg.pending_queue_size = decision_to_planning_update.pendingQueueSize;
        decision_update_reader_ = 
            node_->CreateReader<HwpDecisionToPlanningUpdateMsg>(decision_to_planning_update_cfg, nullptr);

        auto decision_to_planning_trigger = node_cfg_.getSubChannel("decision_to_planning_trigger"); 
        apollo::cyber::ReaderConfig decision_to_planning_trigger_cfg;
        decision_to_planning_trigger_cfg.channel_name = decision_to_planning_trigger.name;
        decision_to_planning_trigger_cfg.pending_queue_size = decision_to_planning_trigger.pendingQueueSize;
        decision_trigger_reader_ = 
            node_->CreateReader<HwpDecisionToPlanningTriggerMsg>(decision_to_planning_trigger_cfg, 
            std::bind(&ControlComponent::HwpDecisionToPlanningTriggerMsgCallback,this,std::placeholders::_1));

        if(!FLAGS_use_control_submodules){
            auto acc_control = node_cfg_.getPubChannel("acc_control");
            control_cmd_writer_ =
                node_->CreateWriter<LatAndLongControlCmd>(acc_control.name);
            auto aion_acc_control = node_cfg_.getPubChannel("aion_acc_control");
            aion_control_cmd_writer_ =
                node_->CreateWriter<LatAndLongControlCmd>(aion_acc_control.name);
            auto control_debug = node_cfg_.getPubChannel("control_debug");
            control_debug_writer_ =
                node_->CreateWriter<ControlDebug>(control_debug.name);
        }

        if(pExceptionCaptureInfo == nullptr){
            auto exception = node_cfg_.getPubChannel("exception");
            pExceptionCaptureInfo = node_->CreateWriter<ExceptionPtr>(exception.name);
       }

        // record info 
        use_debug_info_record_ = control_conf_.use_debug_info_record();
        uint64_t deque_size = control_conf_.deque_size();
        uint32_t save_rate = control_conf_.save_rate();
        uint32_t ctl_rate = static_cast<uint32_t>(std::round(1.0 / control_conf_.control_period()));
        AINFO << "deque_size=" << deque_size << ", save_rate=" << save_rate << 
                        ", ctl_rate=" << ctl_rate;
        record_debug_info_.reset(new RecordDebugInfo(deque_size, save_rate, ctl_rate));

#ifdef CYBER_TIMER_EN
        pDebugProc = std::make_shared<apollo::cyber::Timer>(20,std::bind(&ControlComponent::DebugProc,this),false, "l2_control_debug");
        pDebugProc->Start();

        pControlProc = std::make_shared<apollo::cyber::Timer>(20,std::bind(&ControlComponent::Proc,this),false, "l2_control");
        pControlProc->Start();
#else
        pDebugProc = std::make_unique<TimerCommon::Timer>(20, std::bind(&ControlComponent::DebugProc,this), "l2_control_debug");
        pDebugProc->Start();

        pControlProc = std::make_unique<TimerCommon::Timer>(20, std::bind(&ControlComponent::Proc,this), "l2_control");
        pControlProc->Start();
#endif

        return true;
    }

    void ControlComponent::OnChassis(
                                                        const std::shared_ptr<Chassis> &chassis) {
        AINFO << "Received chassis data: run chassis callback.";
        std::lock_guard<std::mutex> lock(mutex_);
        latest_chassis_.CopyFrom(*chassis);
    }

    void ControlComponent::OnPlanning(
                                                        const std::shared_ptr<ADCTrajectory> &trajectory) {
        AINFO << "Received plan data: run trajectory callback.";
        std::lock_guard<std::mutex> lock(mutex_);
        latest_trajectory_.CopyFrom(*trajectory);
    }

    void ControlComponent::OnLocalization(
                                            const std::shared_ptr<Pose> &localization) {
        AINFO << "Received location data: run localization message callback.";
        std::lock_guard<std::mutex> lock(mutex_);
        latest_localization_.CopyFrom(*localization);
    }

    void ControlComponent::OnDecisionUpdate(
                                            const std::shared_ptr<HwpDecisionToPlanningUpdateMsg> &dec_update) {
        static uint32_t pre_update_seq = 0;
        static double pre_update_timestamp = 0;

        if(dec_update->header().sequence_num() != pre_update_seq || 
            fabs(dec_update->header().timestamp() - pre_update_timestamp) > 0.01){
            pre_update_seq = dec_update->header().sequence_num();
            pre_update_timestamp = dec_update->header().timestamp();
            std::string log_msg = "Received HwpDecisionToPlanningUpdateMsg";
            LogProto(__FILE__,__LINE__,log_msg,dec_update);
            switch (dec_update->payload_case()) {
                    case rainbowdash::planning::HwpDecisionToPlanningUpdateMsg::kDriverSetting :
                        //  chassis_.maximum_user_speed = dec_update->driver_setting().custom_cruise_velocity();
                        // chassis_.time_gap = dec_update->driver_setting().custom_cruise_interval();
                        break;           
                case rainbowdash::planning::HMIMsg::PAYLOAD_NOT_SET :
                    default :
                        AERROR << "dec_update payload_case is not set.";
                        break;
            }
            
            std::lock_guard<std::mutex> lock(mutex_);
            decison_update_.CopyFrom(*dec_update);
        }
    }

    bool ControlComponent::CheckInput(LocalView *local_view){
        static uint32_t pre_localization_seq = 0;
        static uint32_t pre_chassis_seq = 0;
        static uint32_t pre_trajectory_seq = 0;
        if(local_view->localization().header().sequence_num() != pre_localization_seq){
            pre_localization_seq = local_view->localization().header().sequence_num();
            AINFO << "Received localization sequence_num: l_seq=" << pre_localization_seq;
            // AINFO << PrintInputInfo(local_view, 1);
        }
        
        if(local_view->chassis().header().sequence_num() != pre_chassis_seq){
            pre_chassis_seq = local_view->chassis().header().sequence_num();
            AINFO << "Received chassis sequence_num: c_seq=" << pre_chassis_seq;
            // AINFO << PrintInputInfo(local_view, 2);
        }

        if(local_view->trajectory().header().sequence_num() != pre_trajectory_seq){
            pre_trajectory_seq = local_view->trajectory().header().sequence_num();
            AINFO << "Received trajectory sequence_num: t_seq=" << pre_trajectory_seq;
            // AINFO << PrintInputInfo(local_view, 3);
        }

        if (local_view->trajectory().trajectory_point().empty()) {
            AERROR_EVERY(100) << "planning has no trajectory point. ";
            return false;
        }

        for (auto &trajectory_point :
            *local_view->mutable_trajectory()->mutable_trajectory_point()) {
            if (std::abs(trajectory_point.v()) <
                    control_conf_.minimum_speed_resolution() &&
                std::abs(trajectory_point.a()) <
                    control_conf_.max_acceleration_when_stopped()) {
            trajectory_point.set_v(0.0);
            trajectory_point.set_a(0.0);
            }
        }

// #if SIMULATION
//         auto file_path =  "conf/1_localization.pb.txt";
//         if(false == SetProtoToASCIIFile(local_view->localization(), file_path)){
//             AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
//         }
//         file_path =  "conf/1_chassis.pb.txt";
//         if(false == SetProtoToASCIIFile(local_view->chassis(), file_path)){
//             AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
//         }
//         file_path =  "conf/1_planning.pb.txt";
//         if(false == SetProtoToASCIIFile(local_view->trajectory(), file_path)){
//             AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
//         }
// #endif

        injector_->vehicle_state()->Update(local_view->localization(),
                                     local_view->chassis());

        return true;
    }

    bool ControlComponent::CheckTimestamp(const LocalView &local_view){
        if (control_conf_.is_control_test_mode()) {
            ADEBUG << "Skip input timestamp check by gflags.";
            return true;
        }
        double current_timestamp = GetTimeS(nullptr);
        double localization_diff =
            current_timestamp - local_view.localization().header().timestamp();
        
        //TODO: 单位s，定位的频率是多少
        if(localization_diff > (20.0 * control_conf_.localization_period())){
            AINFO << "Localization msg lost for " << std::setprecision(6)
                << localization_diff << "s";
        }

        if (localization_diff > (control_conf_.max_localization_miss_num() *
                                control_conf_.localization_period())) {
            AERROR << "Localization msg lost for " << std::setprecision(6)
                << localization_diff << "s";
            if(true == l2_enter_quit_flag_){
                PubException(static_cast<exception::ExceptionType>(0x0d000002),"HWP_CONTROL_LOCATION_LOSS");
            }
            return false;
        }

        double chassis_diff =
            current_timestamp - local_view.chassis().header().timestamp();
        if (chassis_diff >
            (control_conf_.max_chassis_miss_num() * control_conf_.chassis_period())) {
            AERROR << "Chassis msg lost for " << std::setprecision(6) << chassis_diff
                << "s";
            if(true == l2_enter_quit_flag_){
                PubException(static_cast<exception::ExceptionType>(0x0d000001),"HWP_CONTROL_CHASSIS_LOSS");
            }
            return false;
        }

        double trajectory_diff =
            current_timestamp - local_view.trajectory().header().timestamp();
        if (trajectory_diff > (control_conf_.max_planning_miss_num() *
                                control_conf_.trajectory_period())) {
            AERROR << "ADCTrajectory msg lost for " << std::setprecision(6)
                << trajectory_diff << "s";
            if(true == l2_enter_quit_flag_){
                PubException(static_cast<exception::ExceptionType>(0x0d000003),"HWP_CONTROL_PLAN_LOSS");
            }
            return false;
        }
        return true;
    }

    bool ControlComponent::ProduceControlCommand(
                                                ControlCmd *control_cmd, ControlDebug *debug){
        bool status = CheckInput(&local_view_);

        if(false == status){
            AERROR_EVERY(100) << "Control input data failed...";
            estop_ = true;
        }else{
            bool status_ts = CheckTimestamp(local_view_);
            if(false == status_ts){
                AERROR << "Input messages timeout";
                trajectory_reader_->ClearData();
                status = status_ts;
                estop_ = true;
            }
        }

        //根据CheckInput，导致estop_的原因只有路径为空，所以在这里清除恢复estop_
        if (local_view_.trajectory().trajectory_point().empty()) {
            AERROR_EVERY(100) << "planning has no trajectory point. ";
            estop_ = true;
        }
        else{
            estop_ = false;
        }

        if (!estop_) {
            bool status_compute = controller_agent_.ComputeControlCommand(
                &local_view_.localization(), &local_view_.chassis(),
                &local_view_.trajectory(), control_cmd, debug);

            if(!status_compute){
                AERROR << "Control main function failed"
                                << " with localization: "
                                << local_view_.localization().SerializeAsString()
                                << " with chassis: " 
                                << " type=" << local_view_.chassis().GetDescriptor()->full_name()
                                << " data=" << local_view_.chassis().SerializeAsString()
                                << " with trajectory: "
                                << " type=" << local_view_.trajectory().GetDescriptor()->full_name()
                                << " data=" << local_view_.trajectory().SerializeAsString()
                                << " with cmd: " << control_cmd->ShortDebugString();
                estop_ = true;
                status = status_compute;
            }
        }

        // if planning set estop, then no control process triggered
        if (estop_) {
            AWARN_EVERY(100) << "Estop triggered! No control core method executed!";
            // set Estop command
            control_cmd->is_driving = 0;
            control_cmd->tar_acc = 0.0;
            control_cmd->tar_torque = 0;
            control_cmd->is_steering = 0;
            control_cmd->steering_target = 0.0;
            control_cmd->is_braking = 1;
            control_cmd->tar_deacc = control_conf_.soft_estop_brake();
            control_cmd->is_gear = 0;
            control_cmd->tar_gear = 0;
        }

        return status;
    }

    bool ControlComponent::Proc(){
        const auto start_time = Clock::NowInSeconds();

        chassis_reader_->Observe();
        const auto &chassis_msg = chassis_reader_->GetLatestObserved();
        if (chassis_msg == nullptr) {
            AINFO << "Chassis msg is not ready!";
            return false;
        }
        OnChassis(chassis_msg);

        trajectory_reader_->Observe();
        const auto &trajectory_msg = trajectory_reader_->GetLatestObserved();
        if (trajectory_msg == nullptr) {
            AINFO << "planning msg is not ready!";
            return false;
        }
        OnPlanning(trajectory_msg);

        localization_reader_->Observe();
        const auto &localization_msg = localization_reader_->GetLatestObserved();
        if (localization_msg == nullptr) {
            AINFO << "localization msg is not ready!";
            return false;
        }
        OnLocalization(localization_msg);

        decision_update_reader_->Observe();
        const auto &decision_update_msg = decision_update_reader_->GetLatestObserved();
        if (decision_update_msg != nullptr) {
            OnDecisionUpdate(decision_update_msg);
        }
        

        if (false){//根据定位时间和当前时间更新定位信息，暂时设置为false
            double current_timestamp = GetTimeS(nullptr);
            double diff_time = current_timestamp - latest_localization_.header().timestamp();
            if(diff_time > 1.0e-6 ){
                double curr_vehicle_heading = latest_localization_.heading();
                double curr_vehicle_x = latest_localization_.position().x();
                double curr_vehicle_y = latest_localization_.position().y();
                double cur_speed = latest_localization_.linear_velocity().x();
                double cur_status_x = curr_vehicle_x + cur_speed * diff_time * cos(curr_vehicle_heading);
                double cur_status_y = curr_vehicle_x + cur_speed * diff_time * sin(curr_vehicle_heading);
                latest_localization_.mutable_position()->set_x(cur_status_x);
                latest_localization_.mutable_position()->set_y(cur_status_y);
            }
        }

        {
            // TODO(SHU): to avoid redundent copy
            std::lock_guard<std::mutex> lock(mutex_);
            local_view_.mutable_chassis()->CopyFrom(latest_chassis_);
            local_view_.mutable_trajectory()->CopyFrom(latest_trajectory_);
            local_view_.mutable_localization()->CopyFrom(latest_localization_);
        }

        if (FLAGS_use_control_submodules) {
            // local_view_writer_->Write(local_view_);
            return true;
        }

        if (control_conf_.is_control_test_mode() &&
            control_conf_.control_test_duration() > 0 &&
            (start_time - init_time_) >
                control_conf_.control_test_duration()) {
            AERROR << "Control finished testing. exit";
            return false;
        }
        
        ControlCmd control_cmd;
        //目前都需要生成控制命令，置为true，后续根据drive_mode制定逻辑
        if (true == l2_enter_quit_flag_) {
            ProduceControlCommand(&control_cmd, &control_debug_);
            AINFO << PrintDebugInfo(&control_conf_, &control_debug_);
        } else {
            ProduceControlCommand(&control_cmd, &control_debug_);
            ResetAndProduceZeroControlCommand(&control_cmd);
        }
        // record info 
        if(use_debug_info_record_ ){
            if(true == l2_enter_quit_flag_){
                if(false == enter_quit_flag_){
                    enter_quit_flag_ = true;
                    record_debug_info_->SetEnterL2TimeRecordInfo();
                }
                // 用户设置速度、目标速度、底盘车速、定位车速、目标加速度、底盘（IMU）加速度、定位加速度、速度误差，位置误差
                std::array<double ,9> tmp = {
                        latest_chassis_.maximum_user_speed(),                                                                              //用户设置速度
                        control_debug_.simple_longitudinal_debug().speed_reference(),                         //目标速度
                        latest_chassis_.vehicle_speed().vehicle_speed(),                                                              //底盘车速
                        // control_debug_.simple_longitudinal_debug().current_speed(),                          //底盘车速
                        latest_localization_.linear_velocity().x(),                                                                                 //定位车速
                        control_debug_.simple_longitudinal_debug().final_acc(),                                          //目标加速度
                        latest_chassis_.acceleration().longitudinal(),                                                                          //底盘（IMU）加速度
                        latest_localization_.linear_acceleration_vrf().x(),                                                                 //定位加速度
                        control_debug_.simple_longitudinal_debug().speed_error(),                                      //速度误差
                        control_debug_.simple_longitudinal_debug().station_error()                                     //位置误差
                    };
                double time = std::stod(record_debug_info_->GetLogTimeString());
                record_debug_info_->FillAllDebugInfo(time, tmp);
            }
            else{
                if(true == enter_quit_flag_){
                    enter_quit_flag_ = false;
                    record_debug_info_->SetQuitL2TimeRecordInfo();
                    // record_debug_info_->WriteJsonFile(12345672,"./");
                }
            }
        }
#if SIMULATION
        LatAndLongControlCmd control_command;
        TransferControlCmdType(&control_command, &control_cmd);
#else
        LatAndLongControlCmd control_command;
        TransferControlCmdType(&control_command, &control_cmd);
#endif

        const auto end_time = Clock::NowInSeconds();
        const double time_diff_ms = (end_time - start_time) * 1000;
        AINFO << "total control time spend: " << time_diff_ms << " ms.";

        if (control_conf_.is_control_test_mode()) {
            AINFO << "Skip publish control command in test mode";
            return true;
        }
#if SIMULATION
        if(control_cmd_writer_ != nullptr){
            control_cmd_writer_->Write(control_command);
        }
#else
        if(aion_control_cmd_writer_ != nullptr){
            aion_control_cmd_writer_->Write(control_command);
            AINFO << "Control cmd pub: is_driving=" << control_cmd.is_driving
                        << ", tar_acc=" << control_cmd.tar_acc << ", tar_torque=" << control_cmd.tar_torque
                        << ", is_braking=" << control_cmd.is_braking << ", tar_deacc=" << control_cmd.tar_deacc
                        << ", is_steering=" << control_cmd.is_steering 
                        << ", steering_target=" << control_cmd.steering_target;
        }
#endif
        if(control_debug_writer_ != nullptr && publish_control_debug_){
            control_debug_writer_->Write(control_debug_);
        }
        return true;
    }

    void ControlComponent::TransferControlCmdType(
        LatAndLongControlCmd *control_command, ControlCmd *control_cmd){
        static int64_t seq=0;
        auto header = control_command->mutable_header();
        header->set_timestamp(GetTimeS(nullptr));
        header->set_module_name(node_cfg_.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        auto ctrl_msg = control_command->mutable_data();
#if SIMULATION
        AccelerationControlData acc_ctrl_data;
        acc_ctrl_data.set_tar_acceleration(control_cmd->tar_acc);
        if(use_lat_control_){
            acc_ctrl_data.set_is_steering(control_cmd->is_steering == 1 ? CommonBool::TRUE : CommonBool::FALSE);
        }
        else{
            acc_ctrl_data.set_is_steering(CommonBool::FALSE);
        }
        acc_ctrl_data.set_tar_steer_angle(control_cmd->steering_target);
        acc_ctrl_data.set_put_gear(CommonBool::NONE);
        acc_ctrl_data.set_tar_gear(CommonGearPosition::PARKING);
        ctrl_msg->PackFrom(acc_ctrl_data);
#else
        TorqueControlData trq_ctrl_data;
        trq_ctrl_data.set_is_driving(control_cmd->is_driving == 1 ? CommonBool::TRUE : CommonBool::FALSE);
        trq_ctrl_data.set_tar_acc(control_cmd->tar_acc);
        trq_ctrl_data.set_tar_torque(control_cmd->tar_torque);
        trq_ctrl_data.set_is_braking(control_cmd->is_braking == 1 ? CommonBool::TRUE : CommonBool::FALSE);
        trq_ctrl_data.set_tar_deceleration(control_cmd->tar_deacc);
        if(use_lat_control_){
            trq_ctrl_data.set_is_steering(control_cmd->is_steering == 1 ? CommonBool::TRUE : CommonBool::FALSE);
        }
        else{
            trq_ctrl_data.set_is_steering(CommonBool::FALSE);
        }
        trq_ctrl_data.set_tar_steer_angle(control_cmd->steering_target);
        trq_ctrl_data.set_put_gear(CommonBool::NONE);
        trq_ctrl_data.set_tar_gear(CommonGearPosition::PARKING);
        // trq_ctrl_data.set_lamp_ctl(0);
        ctrl_msg->PackFrom(trq_ctrl_data);
#endif
        
    }

    void ControlComponent::ResetAndProduceZeroControlCommand(
            ControlCmd *control_cmd) {
        control_cmd->is_driving = 0;
        control_cmd->tar_acc = 0.0;
        control_cmd->tar_torque = 0;
        control_cmd->is_steering = 0;
        control_cmd->steering_target = 0.0;
        control_cmd->is_braking = 0;
        control_cmd->tar_deacc = 0.0;
        control_cmd->is_gear = 0;
        control_cmd->tar_gear = 0;
        controller_agent_.Reset();
        latest_trajectory_.mutable_trajectory_point()->Clear();
        // latest_trajectory_.mutable_path_point()->Clear();
        trajectory_reader_->ClearData();
    }

    void ControlComponent::DecisionACCStateCallback(const std::shared_ptr <ACCState>& msg){
      LogProto(__FILE__,__LINE__,__FUNCTION__,msg);
    }

    void ControlComponent::DecisionLCCStateCallback(const std::shared_ptr <LCCState>& msg){
      LogProto(__FILE__,__LINE__,__FUNCTION__,msg);
    }

    void ControlComponent::HmiVelocityCallback(const std::shared_ptr <Velocity>& msg){
      LogProto(__FILE__,__LINE__,__FUNCTION__,msg);
    //   chassis_.maximum_user_speed = msg->velocity();
    }

    void ControlComponent::HmiCruiseIntervalCallback(const std::shared_ptr <CruiseInterval>& msg){
      LogProto(__FILE__,__LINE__,__FUNCTION__,msg);
    //   chassis_.time_gap = msg->cruise_interval();
    }
    
    void ControlComponent::HwpDecisionToPlanningTriggerMsgCallback(const std::shared_ptr <HwpDecisionToPlanningTriggerMsg>& msg){
        LogProto(__FILE__,__LINE__,__FUNCTION__,msg);
        switch (msg->payload_case()) {
            case rainbowdash::planning::HwpDecisionToPlanningTriggerMsg::kStartPlan :
                AINFO << __FUNCTION__<< ", DecisionToPlanningUpdateMsg: " << ", start="<< static_cast<int>(msg->start_plan().start())
                                << ", driving_mode="<< static_cast<int>(msg->start_plan().driving_mode())
                                << ", driver_action="<< static_cast<int>(msg->start_plan().driver_action())
                                << ", custom_cruise_velocity="<< msg->start_plan().driver_setting().custom_cruise_velocity()
                                << ", custom_cruise_interval="<< msg->start_plan().driver_setting().custom_cruise_interval();
                if(static_cast<int>(CommonBool::TRUE) == static_cast<int>(msg->start_plan().start())){
                    l2_enter_quit_flag_ = true;
                }
                else if(static_cast<int>(CommonBool::FALSE) == static_cast<int>(msg->start_plan().start())){
                    l2_enter_quit_flag_ = false;
                }
                break;           
          case rainbowdash::planning::HMIMsg::PAYLOAD_NOT_SET :
            default :
                AERROR << "msg payload_case is not set.";
                break;
      }
    }

    bool ControlComponent::DebugProc(){
        const auto start_time = Clock::NowInSeconds();
        LocalView local_view_tmp;
        {
            std::lock_guard<std::mutex> lock(mutex_);
            local_view_tmp.CopyFrom(local_view_);
        }
        std::string str;
        static uint32_t pre_localization_seq = 0;
        static uint32_t pre_chassis_seq = 0;
        static uint32_t pre_trajectory_seq = 0;
        if(local_view_tmp.localization().header().sequence_num() != pre_localization_seq){
            pre_localization_seq = local_view_tmp.localization().header().sequence_num();
            AINFO << PrintInputInfo(&local_view_tmp, 1);
        }
        
        if(local_view_tmp.chassis().header().sequence_num() != pre_chassis_seq){
            pre_chassis_seq = local_view_tmp.chassis().header().sequence_num();
            AINFO << PrintInputInfo(&local_view_tmp, 2);
        }

        if(local_view_tmp.trajectory().header().sequence_num() != pre_trajectory_seq){
            pre_trajectory_seq = local_view_tmp.trajectory().header().sequence_num();
            AINFO << PrintInputInfo(&local_view_tmp, 3);
        }
        const auto end_time = Clock::NowInSeconds();
        const double time_diff_ms = (end_time - start_time) * 1000;
        AINFO << "total debugproc time spend: " << time_diff_ms << " ms.";
        return true;
    }

    std::string ControlComponent::PrintInputInfo(LocalView *local_view, uint32_t type){
        std::string str;
        if(1 == type){
            str.append("Received localization:");
            str.append(" timestamp:").append(std::to_string(local_view->localization().header().timestamp())
                        ).append(", sequence_num:").append(std::to_string(local_view->localization().header().sequence_num())
                        ).append(", heading:").append(std::to_string(local_view->localization().heading())
                        ).append(", position_x:").append(std::to_string(local_view->localization().position().x())
                        ).append(", position_y:").append(std::to_string(local_view->localization().position().y())
                        ).append(", linear_velocity_x:").append(std::to_string(local_view->localization().linear_velocity().x())
                        ).append(", linear_acceleration_vrf_x:").append(std::to_string(local_view->localization().linear_acceleration_vrf().x()));
        }
        if(2 == type){
            str.append("Received chassis:");
            str.append(" timestamp:").append(std::to_string(local_view->chassis().header().timestamp())
                        ).append(", sequence_num:").append(std::to_string(local_view->chassis().header().sequence_num())
                        ).append(", vehicle_speed:").append(std::to_string(local_view->chassis().vehicle_speed().vehicle_speed())
                        ).append(", lon_acc:").append(std::to_string(local_view->chassis().acceleration().longitudinal())
                        ).append(", lat_acc:").append(std::to_string(local_view->chassis().acceleration().lateral())
                        ).append(", steering_wheel_angle:").append(std::to_string(local_view->chassis().steering_system().steering_wheel_angle())
                        ).append(", maximum_user_speed:").append(std::to_string(local_view->chassis().maximum_user_speed())
                        ).append(", brake_percentage:").append(std::to_string(local_view->chassis().brake_percentage())
                        ).append(", throttle_perceptage:").append(std::to_string(local_view->chassis().throttle_perceptage())
                        ).append(", driving_mode:").append(std::to_string(static_cast<int>(local_view->chassis().driving_mode()))
                        ).append(", yaw_rate:").append(std::to_string(local_view->chassis().yaw_rate())
                        ).append(", cur_torque:").append(std::to_string(local_view->chassis().cur_torque()));
        }
        if(3 == type){
            str.append("Received trajectory:");
            str.append( " timestamp:" +std::to_string( local_view->trajectory().header().timestamp())
                        ).append(", sequence_num:").append(std::to_string(local_view->trajectory().header().sequence_num())
                        ).append(", total_path_length:").append(std::to_string(local_view->trajectory().total_path_length())
                        ).append(", total_path_time:").append(std::to_string(local_view->trajectory().total_path_time())
                        ).append(", is_replan:").append(std::to_string(static_cast<int>(local_view->trajectory().is_replan()))
                        ).append(", plan_type:").append(local_view->trajectory().plan_type());
                        auto traj = local_view->trajectory().trajectory_point();
                        int trajectory_point_size = local_view->trajectory().trajectory_point_size();
                        for(int i = 0; i < trajectory_point_size; i++){
                            str.append(", v:").append(std::to_string(local_view->trajectory().trajectory_point(i).v()) 
                                    ).append(", a:").append(std::to_string(local_view->trajectory().trajectory_point(i).a())
                                    ).append(", rt:").append(std::to_string(local_view->trajectory().trajectory_point(i).relative_time())
                                    ).append(", x:").append(std::to_string(local_view->trajectory().trajectory_point(i).path_point().x())
                                    ).append(", y:").append(std::to_string(local_view->trajectory().trajectory_point(i).path_point().y())
                                    ).append(", th:").append(std::to_string(local_view->trajectory().trajectory_point(i).path_point().theta())
                                    ).append(", k:").append(std::to_string(local_view->trajectory().trajectory_point(i).path_point().kappa())
                                    ).append(", s:").append(std::to_string(local_view->trajectory().trajectory_point(i).path_point().s()));
                        }
        }
        return str;
    }

    std::string ControlComponent::PrintDebugInfo(const ControlConf *control_conf, ControlDebug *debug){
        std:: string str;
        auto simple_longitudinal_debug = debug->mutable_simple_longitudinal_debug();
        auto simple_lateral_lqrk2_debug = debug->mutable_simple_lateral_lqrk2_debug();
        auto simple_lateral_debug = debug->mutable_simple_lateral_debug();
        auto simple_lateral_mpck1_debug = debug->mutable_simple_lateral_mpck1_debug();
        static uint64_t control_seq = 0;
        for (auto active_controller : control_conf->active_controllers()) {
            switch (active_controller) {
            case static_cast<int>(StruControllerType::LON_CONTROLLER):
                str.append(" LON PID debug info: ");
                str.append(",cmp_v:").append(std::to_string(simple_longitudinal_debug->current_matched_point().v()) 
                            ).append(",cmp_a:").append(std::to_string(simple_longitudinal_debug->current_matched_point().a()) 
                            ).append(",cmp_px:").append(std::to_string(simple_longitudinal_debug->current_matched_point().path_point().x()) 
                            ).append(",cmp_py:").append(std::to_string(simple_longitudinal_debug->current_matched_point().path_point().y()) 
                            ).append(",cmp_ps:").append(std::to_string(simple_longitudinal_debug->current_matched_point().path_point().s()) 
                            ).append(",crp_v:").append(std::to_string(simple_longitudinal_debug->current_reference_point().v()) 
                            ).append(",crp_a:").append(std::to_string(simple_longitudinal_debug->current_reference_point().a()) 
                            ).append(",crp_px:").append(std::to_string(simple_longitudinal_debug->current_reference_point().path_point().x()) 
                            ).append(",crp_py:").append(std::to_string(simple_longitudinal_debug->current_reference_point().path_point().y()) 
                            ).append(",crp_ps:").append(std::to_string(simple_longitudinal_debug->current_reference_point().path_point().s()) 
                            ).append(",prp_v:").append(std::to_string(simple_longitudinal_debug->preview_reference_point().v()) 
                            ).append(",prp_a:").append(std::to_string(simple_longitudinal_debug->preview_reference_point().a()) 
                            ).append(",prp_px:").append(std::to_string(simple_longitudinal_debug->preview_reference_point().path_point().x()) 
                            ).append(",prp_py:").append(std::to_string(simple_longitudinal_debug->preview_reference_point().path_point().y()) 
                            ).append(",prp_ps:").append(std::to_string(simple_longitudinal_debug->preview_reference_point().path_point().s()) 
                            ).append(",lon_cur_t:").append(std::to_string(simple_longitudinal_debug->cur_time()) 
                            ).append(",is_replan:").append(std::to_string(simple_longitudinal_debug->is_replan()) 
                            ).append(",cur_speed:").append(std::to_string(simple_longitudinal_debug->current_speed()) 
                            //    ).append(",lateral_error:").append(std::to_string(simple_longitudinal_debug->lateral_error()) 
                            //    ).append(",heading_error:").append(std::to_string(simple_longitudinal_debug->heading_error()) 
                            ).append(",s_err_ori:").append(std::to_string(simple_longitudinal_debug->station_error()) 
                            ).append(",flt_s_err:").append(std::to_string(simple_longitudinal_debug->filter_station_error()) 
                            ).append(",lon_delay_t:").append(std::to_string(simple_longitudinal_debug->lon_delay_time()) 
                            ).append(",v_err_ori:").append(std::to_string(simple_longitudinal_debug->speed_error_ori()) 
                            ).append(",flt_v_err:").append(std::to_string(simple_longitudinal_debug->filter_speed_error()) 
                            ).append(",a_cmd_closeloop:").append(std::to_string(simple_longitudinal_debug->acceleration_cmd_closeloop()) 
                            ).append(",ctl_input_limited:").append(std::to_string(simple_longitudinal_debug->speed_controller_input_limited()) 
                            ).append(",a_cmd:").append(std::to_string(simple_longitudinal_debug->acceleration_cmd()) 
                            ).append(",path_remain:").append(std::to_string(simple_longitudinal_debug->path_remain()) 
                            ).append(",final_acc:").append(std::to_string(simple_longitudinal_debug->final_acc()) 
                            ).append(",calibration_value:").append(std::to_string(simple_longitudinal_debug->calibration_value()) 
                            ).append(",drive_mode:").append(std::to_string(simple_longitudinal_debug->drive_mode()) 
                            ).append(",lon_ctl_state:").append(std::to_string(simple_longitudinal_debug->lon_control_state()) 
                            ).append(",v_err_p_con:").append(std::to_string(simple_longitudinal_debug->v_error_p_contribution()) 
                            ).append(",v_err_i_con:").append(std::to_string(simple_longitudinal_debug->v_error_i_contribution())
                            ).append(",slope_compensation:").append(std::to_string(simple_longitudinal_debug->slope_offset_compensation())
                            ).append(",chassis_seq:").append(std::to_string(simple_longitudinal_debug->chassis_seq())
                            ).append(",localization_seq:").append(std::to_string(simple_longitudinal_debug->localization_seq())
                            ).append(",trajectory_seq:").append(std::to_string(simple_longitudinal_debug->trajectory_seq())
                            ).append(",debug_seq:").append(std::to_string(control_seq++));
                break;
            case static_cast<int>(StruControllerType::LAT_LQRK2_CONTROLLER):
                str.append(" LAT LQR debug info: ");
                str.append(",ctp_v:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().v()) 
                            ).append(",ctp_a:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().a()) 
                            ).append(",ctp_px:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().path_point().x()) 
                            ).append(",ctp_py:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().path_point().y()) 
                            ).append(",ctp_ps:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().path_point().s()) 
                            ).append(",ctp_p_th:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().path_point().theta()) 
                            ).append(",ctp_pk:").append(std::to_string(simple_lateral_lqrk2_debug->current_target_point().path_point().kappa()) 
                            ).append(",lat_lqr_cur_time:").append(std::to_string(simple_lateral_lqrk2_debug->cur_time()) 
                            ).append(",ori_lat_err:").append(std::to_string(simple_lateral_lqrk2_debug->ori_lateral_error()) 
                            ).append(",lat_err:").append(std::to_string(simple_lateral_lqrk2_debug->lateral_error()) 
                            ).append(",ori_heading_err:").append(std::to_string(simple_lateral_lqrk2_debug->ori_heading_error()) 
                            ).append(",heading_error:").append(std::to_string(simple_lateral_lqrk2_debug->heading_error()) 
                            ).append(",ori_curvature:").append(std::to_string(simple_lateral_lqrk2_debug->ori_curvature()) 
                            ).append(",curvature:").append(std::to_string(simple_lateral_lqrk2_debug->curvature()) 
                            ).append(",ws_feedforward:").append(std::to_string(simple_lateral_lqrk2_debug->wheel_steer_angle_feedforward()) 
                            ).append(",ws_feedback:").append(std::to_string(simple_lateral_lqrk2_debug->wheel_steer_angle_feedback()) 
                            ).append(",ws_rad_cmd:").append(std::to_string(simple_lateral_lqrk2_debug->wheel_steer_angle_rad_cmd()) 
                            ).append(",ws_agl_cmd:").append(std::to_string(simple_lateral_lqrk2_debug->wheel_steer_angle_cmd()) 
                            ).append(",tar_sw_angle:").append(std::to_string(simple_lateral_lqrk2_debug->tar_steer_wheel_angle()) 
                            ).append(",filter_sw_angle:").append(std::to_string(simple_lateral_lqrk2_debug->filter_wheel_steer_angle()) 
                            ).append(",final_sw_angle:").append(std::to_string(simple_lateral_lqrk2_debug->final_steer_wheel_angle()) 
                            ).append(",lat_err_con:").append(std::to_string(simple_lateral_lqrk2_debug->lat_error_contribution()) 
                            ).append(",heading_err_con:").append(std::to_string(simple_lateral_lqrk2_debug->heading_error_contribution()) 
                            ).append(",cur_sw_angle:").append(std::to_string(simple_lateral_lqrk2_debug->cur_steer_wheel_angle()));
                break;
            case static_cast<int>(StruControllerType::LAT_PID_CONTROLLER):
                str.append(" LAT PID debug info: ");
                str.append(",ctp_v:").append(std::to_string(simple_lateral_debug->current_target_point().v()) 
                            ).append(",ctp_a:").append(std::to_string(simple_lateral_debug->current_target_point().a()) 
                            ).append(",ctp_px:").append(std::to_string(simple_lateral_debug->current_target_point().path_point().x()) 
                            ).append(",ctp_py:").append(std::to_string(simple_lateral_debug->current_target_point().path_point().y()) 
                            ).append(",ctp_ps:").append(std::to_string(simple_lateral_debug->current_target_point().path_point().s()) 
                            ).append(",ctp_p_th:").append(std::to_string(simple_lateral_debug->current_target_point().path_point().theta()) 
                            ).append(",ctp_pk:").append(std::to_string(simple_lateral_debug->current_target_point().path_point().kappa()) 
                            ).append(",lat_pid_cur_time:").append(std::to_string(simple_lateral_debug->cur_time()) 
                            ).append(",ori_lat_err:").append(std::to_string(simple_lateral_debug->ori_lateral_error()) 
                            ).append(",lat_err:").append(std::to_string(simple_lateral_debug->lateral_error()) 
                            ).append(",ori_heading_err:").append(std::to_string(simple_lateral_debug->ori_heading_error()) 
                            ).append(",heading_error:").append(std::to_string(simple_lateral_debug->heading_error()) 
                            ).append(",curvature:").append(std::to_string(simple_lateral_debug->curvature()) 
                            ).append(",lat_err_ws_closecmd:").append(std::to_string(simple_lateral_debug->lat_err_wheel_steer_closecmd()) 
                            ).append(",heading_err_ws_closecmd:").append(std::to_string(simple_lateral_debug->heading_err_wheel_steer_closecmd()) 
                            ).append(",ws_feedback:").append(std::to_string(simple_lateral_debug->wheel_steer_angle_feedback()) 
                            ).append(",ws_feedforward:").append(std::to_string(simple_lateral_debug->wheel_steer_angle_feedforward()) 
                            ).append(",ws_rad_cmd:").append(std::to_string(simple_lateral_debug->wheel_steer_angle_rad_cmd()) 
                            ).append(",ws_agl_cmd:").append(std::to_string(simple_lateral_debug->wheel_steer_angle_cmd()) 
                            ).append(",tar_sw_angle:").append(std::to_string(simple_lateral_debug->tar_steer_wheel_angle()) 
                            ).append(",filter_sw_angle:").append(std::to_string(simple_lateral_debug->filter_wheel_steer_angle()) 
                            ).append(",final_sw_angle:").append(std::to_string(simple_lateral_debug->final_steer_wheel_angle()) 
                            ).append(",lat_err_p_con:").append(std::to_string(simple_lateral_debug->lat_error_p_contribution()) 
                            ).append(",lat_err_i_con:").append(std::to_string(simple_lateral_debug->lat_error_i_contribution()) 
                            ).append(",heading_err_p_con:").append(std::to_string(simple_lateral_debug->heading_error_p_contribution()) 
                            ).append(",heading_err_i_con:").append(std::to_string(simple_lateral_debug->heading_error_i_contribution())
                            ).append(",cur_sw_angle:").append(std::to_string(simple_lateral_debug->cur_steer_wheel_angle()));
                break;
            case static_cast<int>(StruControllerType::LAT_MPCK1_CONTROLLER):
                str.append(" LAT MPCK1 debug info: ");
                str.append(",lat_err:").append(std::to_string(simple_lateral_mpck1_debug->lateral_error()) 
                               ).append(",ref_heading:").append(std::to_string(simple_lateral_mpck1_debug->ref_heading()) 
                               ).append(",heading:").append(std::to_string(simple_lateral_mpck1_debug->heading())
                               ).append(",heading_err:").append(std::to_string(simple_lateral_mpck1_debug->heading_error()) 
                               ).append(",curvature:").append(std::to_string(simple_lateral_mpck1_debug->curvature()) 
                               ).append(",cur_steer_angle:").append(std::to_string(simple_lateral_mpck1_debug->cur_steer_angle()) 
                               ).append(",cur_v:").append(std::to_string(simple_lateral_mpck1_debug->cur_speed()) 
                               ).append(",delay_lat_err:").append(std::to_string(simple_lateral_mpck1_debug->delay_lateral_error()) 
                               ).append(",delay_heading_err:").append(std::to_string(simple_lateral_mpck1_debug->delay_heading_error()) 
                               ).append(",delay_steer_angle:").append(std::to_string(simple_lateral_mpck1_debug->delay_steer_angle()) 
                               ).append(",p_dt:").append(std::to_string(simple_lateral_mpck1_debug->prediction_dt()) 
                               ).append(",ws_cmd:").append(std::to_string(simple_lateral_mpck1_debug->wheel_steer_angle_cmd()) 
                               ).append(",final_sw_angle:").append(std::to_string(simple_lateral_mpck1_debug->final_steer_wheel_angle()) 
                               ).append(",cur_sw_angle:").append(std::to_string(simple_lateral_mpck1_debug->cur_steer_wheel_angle())
                               ).append("! \n");
                break;
            }
        }
        return str;
    }


    void ControlComponent::PubException(exception::ExceptionType err_type, const std::string& name) {

      ExceptionPtr msg_ptr{};
      msg_ptr.set_timestamp(GetTimeS(nullptr));
      msg_ptr.set_code((uint64_t) err_type);
      msg_ptr.set_name(name);
        AINFO<<__func__ <<" L2 control pub exception name : "<< name <<  " code :0x " << std::hex <<  (uint32_t)err_type;
      pExceptionCaptureInfo->Write(msg_ptr);
  }
}
}

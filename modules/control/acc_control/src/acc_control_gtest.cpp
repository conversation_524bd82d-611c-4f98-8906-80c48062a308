// gtest_sum.cpp
#include <iostream>
#include <memory>
#include <string>
#include <gtest/gtest.h>

#include "cyber/cyber.h"

#include "common/file.h"
#include "common/interpolation_2d.h"
#include "common/interpolation_1d.h"
#include "modules/control/acc_control/proto/lon_controller_conf.pb.h"
#include "modules/control/acc_control/proto/control_cmd.pb.h"
#include "modules/control/acc_control/proto/control_conf.pb.h"
#include "thirdparty/common-algorithm/pnc_math/basic_math/math_utils.h"
#include "thirdparty/math/linear_quadratic_regulator.h"
#include "common/debug/debug_info.h"
#include "common/mpc_k1_helper.h"
 
using namespace apollo::cyber::common;
using namespace mega::control;
using mega::control::ControlConf;
using mega::control::Interpolation2D;

using apollo::cyber::Node;
using apollo::cyber::Writer;
using apollo::cyber::Reader;

ControlConf control_conf_;
std::unique_ptr<Interpolation2D> control_interpolation_;

std::unique_ptr<Interpolation1D> acceleration_interpolation_;
std::unique_ptr<Interpolation1D> acceleration_rate_interpolation_;
std::unique_ptr<Interpolation1D> deceleration_interpolation_;
std::unique_ptr<Interpolation1D> deceleration_rate_interpolation_;
std::unique_ptr<Interpolation1D> steer_angle_interpolation_;
std::vector<std::pair<double, double>> steer_angle_linear_interpolation_;
std::vector<std::pair<double, double>> linear_interpolation_test_;

std::unique_ptr<RecordDebugInfo> record_debug_info_;
std::unique_ptr<QPSolverOSQP> osqp_solver_ = nullptr;

// std::shared_ptr <Node> node_( apollo::cyber::CreateNode("acc_carla_test") );

bool Init(){


    std::string file_path = "conf/control_conf.pb.txt";
    if(false == GetProtoFromFile(file_path, &control_conf_)){
        AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
    }

    file_path = "conf/control_conf_ascll.pb.txt";
    if(false == SetProtoToASCIIFile(control_conf_, file_path)){
        AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
    }
    file_path = "conf/control_conf_binary.pb.txt";
    if(false == SetProtoToBinaryFile(control_conf_, file_path)){
        AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
    }

    const LonControllerConf &lon_controller_conf =
                control_conf_.lon_controller_conf();

    std::cout<<"control_period="<<control_conf_.control_period()
                        <<", trajectory_period="<<control_conf_.trajectory_period()<<std::endl;

    const auto &control_table = lon_controller_conf.calibration_table();
    AINFO << "Control calibration table loaded";
    std::cout << "Control calibration table size is "
            << control_table.calibration_size()<<std::endl;
    Interpolation2D::DataType xyz;
    for (const auto &calibration : control_table.calibration()) {
        xyz.push_back(std::make_tuple(calibration.speed(),
                                    calibration.acceleration(),
                                    calibration.command()));
    }
    control_interpolation_.reset(new Interpolation2D);
    // ACHECK(control_interpolation_->Init(xyz))
    //     << "Fail to load control calibration table";
    if(!control_interpolation_->Init(xyz)){
        std::cout<<"Fail to load control calibration table"<<std::endl;
    }

    auto LoadLongitudinalParams = [&](const LongitudinalParams& longitudinal_params){
            AINFO << "longitudinal_params acceleration table size is "
                    << longitudinal_params.acceleration_size();
            Interpolation1D::DataType xy1;
            for (const auto &longitudinal : longitudinal_params.acceleration()) {
                xy1.push_back(std::make_pair(longitudinal.x(),
                                            longitudinal.y()));
            }
            acceleration_interpolation_.reset(new Interpolation1D);
            ACHECK(acceleration_interpolation_->Init(xy1))
                << "Fail to load acceleration interpolation table";

            AINFO << "longitudinal_params acceleration_rate table size is "
                    << longitudinal_params.acceleration_rate_size();
            Interpolation1D::DataType xy2;
            for (const auto &longitudinal : longitudinal_params.acceleration_rate()) {
                xy2.push_back(std::make_pair(longitudinal.x(),
                                            longitudinal.y()));
            }
            acceleration_rate_interpolation_.reset(new Interpolation1D);
            ACHECK(acceleration_rate_interpolation_->Init(xy2))
                << "Fail to load acceleration interpolation table";

            AINFO << "longitudinal_params deceleration table size is "
                    << longitudinal_params.deceleration_size();
            Interpolation1D::DataType xy3;
            for (const auto &longitudinal : longitudinal_params.deceleration()) {
                xy3.push_back(std::make_pair(longitudinal.x(),
                                            longitudinal.y()));
            }
            deceleration_interpolation_.reset(new Interpolation1D);
            ACHECK(deceleration_interpolation_->Init(xy3))
                << "Fail to load deceleration interpolation table";

            AINFO << "longitudinal_params deceleration_rate table size is "
                    << longitudinal_params.deceleration_rate_size();
            Interpolation1D::DataType xy4;
            for (const auto &longitudinal : longitudinal_params.deceleration_rate()) {
                xy4.push_back(std::make_pair(longitudinal.x(),
                                            longitudinal.y()));
            }
            deceleration_rate_interpolation_.reset(new Interpolation1D);
            ACHECK(deceleration_rate_interpolation_->Init(xy4))
                << "Fail to load deceleration_rate interpolation table";
        };
        LoadLongitudinalParams(lon_controller_conf.longitudinal_params());

        
        auto lat_control_interpolation = control_conf_.lat_pid_controller_conf().lat_calibration_table();
        AINFO << "lat_control_interpolation calibration_size is "
                    << lat_control_interpolation.calibration_size();
        Interpolation1D::DataType xy5;
        for (const auto &calibration : lat_control_interpolation.calibration()) {
            xy5.push_back(std::make_pair(calibration.steer_wheel_angle(),
                                        calibration.steer_angle()));
            steer_angle_linear_interpolation_.emplace_back(std::make_pair(calibration.steer_wheel_angle(),
                                        calibration.steer_angle()));
        }
        steer_angle_interpolation_.reset(new Interpolation1D);
        ACHECK(steer_angle_interpolation_->Init(xy5))
            << "Fail to load steer_angle_interpolation table";

        // linear_interpolation_test_.emplace_back(std::make_pair(0.0, 4.0));
        // linear_interpolation_test_.emplace_back(std::make_pair(5.0, 4.0));
        // linear_interpolation_test_.emplace_back(std::make_pair(20.0, 2.0));

    return true;
}

bool LoadControlCalibrationTable() {
    Init();

    double calibration_value = 0.0;
    double cur_v = 0.0;
    double cur_a = 0.0;
    calibration_value = control_interpolation_->Interpolate(
                std::make_pair(cur_v, cur_a));
    std::cout<<"calibration_value = "<<calibration_value<<std::endl;

    for(int i=0;i<100;i++){
        cur_v = 100.0*static_cast<double>(i)/100.0/3.6;
        cur_a = 0.5;
        calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
        std::cout<<"calibration_value"<<i<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;
    }

    for(int i=0;i<100;i++){
        cur_v = 100.0*static_cast<double>(i)/100.0/3.6;
        cur_a = -0.5;
        calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
        std::cout<<"calibration_value"<<i<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;
    }

    for(int i=0;i<100;i++){
        cur_a = 0.2*static_cast<double>(i)/100.0;
        cur_v = 20.0;
        calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
        std::cout<<"calibration_value"<<i<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;
    }

    cur_v = 0.0;
    cur_a = 0.0;
    calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
    std::cout<<"calibration_value"<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;

    cur_v = 1.0;
    cur_a = 0.0;
    calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
    std::cout<<"calibration_value"<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;

    cur_v = 10.0;
    cur_a = 0.01;
    calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
    std::cout<<"calibration_value"<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;

    cur_v = 3.3;
    cur_a = 0.01;
    calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
    std::cout<<"calibration_value"<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;

    std::vector<double> cur_v_vec = {19.992, 19.992, 19.992, 19.984, 19.984, 19.984, 19.945, 19.945, 19.969,19.969,
                                                                            19.961,19.961, 19.992,19.992, 20.008,20.008, 20.031, 20.031,20.016,20.016,
                                                                            19.984,19.984, 20.031,20.031,20.016,20.016};
    std::vector<double> cur_a_vec = {0.120,0.1223,0.1256,0.1284,0.1357,0.1398,0.144,0.1517,0.157,0.1601,
                                                                            0.1638,0.1672,0.1672,0.1662,0.1642,0.1606,0.1521,0.1426,0.1353,0.129,
                                                                            0.1267,0.1267,0.1212,0.1172,0.1174,0.1172};
    for(int i = 0; i < static_cast<int>(cur_v_vec.size()); i++){
        cur_v = cur_v_vec[i];
        cur_a = cur_a_vec[i];
        calibration_value = control_interpolation_->Interpolate(
                    std::make_pair(cur_v, cur_a));
        std::cout<<"calibration_value"<<": {"<<cur_v<<","<< cur_a<<"} = "<<calibration_value<<std::endl;
    }

    double interpolation_value = 0.0;
    for(int i=0;i<100;i++){
        double current_v = 30.0*static_cast<double>(i)/100.0;
        interpolation_value = acceleration_interpolation_->Interpolate(current_v);
        std::cout<<"interpolation_value"<<i<<": {"<<current_v<<"} = "<<interpolation_value<<std::endl;
    }

    for(int i=-100;i<100;i++){
        double tar_steer_tire_angle= 27.0*static_cast<double>(i)/100.0;
        calibration_value = steer_angle_interpolation_->Interpolate(tar_steer_tire_angle);
        std::cout<<"interpolation: "<<"{tar_steer_tire_angle, steer_angle}"<<": {"<<tar_steer_tire_angle<<","<< calibration_value<<"} " <<std::endl;
        calibration_value = linearSearch(tar_steer_tire_angle, steer_angle_linear_interpolation_);
        std::cout<<"linear_interpolation: "<<"{tar_steer_tire_angle, steer_angle}"<<": {"<<tar_steer_tire_angle<<","<< calibration_value<<"} " <<std::endl;
    }

    for(int i=0;i<100;i++){
        double current_tmp = 30.0*static_cast<double>(i)/100.0;
        calibration_value = linearSearch(current_tmp, steer_angle_linear_interpolation_);
        std::cout<<"value"<<i<<": {"<<current_tmp<<"} = "<<calibration_value<<std::endl;
    }

    return true;
}

int sum(int a, int b) {
    return a+b;
}

// bool ACCTEST() {
//     Init();

//     return true;
// }
 
// TEST(sum, testSum) {
//     EXPECT_EQ(5, sum(2, 3));	// 求合2+3=5
//     EXPECT_NE(3, sum(3, 4));	// 求合3+4 != 3
// }

// TEST(LoadControlCalibrationTable, load_calibration_table) {
//   EXPECT_TRUE(LoadControlCalibrationTable());
// }

bool SolveLQRProblemTest(){
    int basic_state_size_ = 2;
    int lqr_max_iteration_ = 240;
    double lqr_eps_ = 0.01;
    Eigen::MatrixXd matrix_adc_ = Eigen::MatrixXd::Zero(basic_state_size_, basic_state_size_);
    matrix_adc_(0,0) = 1.0; matrix_adc_(0,1) = 0.0626459;
    matrix_adc_(1,0) = 0.0; matrix_adc_(1,1) = 1.0; 
    Eigen::MatrixXd matrix_bdc_ = Eigen::MatrixXd::Zero(basic_state_size_, 1);
    matrix_bdc_(0,0) = 0.0; 
    matrix_bdc_(1,0) = 0.0218875; 
    Eigen::MatrixXd matrix_q_ = Eigen::MatrixXd::Zero(basic_state_size_, basic_state_size_);
    matrix_q_(0,0) = 0.0; matrix_q_(0,1) = 0.0;
    matrix_q_(1,0) = 0.0; matrix_q_(1,1) = 0.0; 
    Eigen::MatrixXd matrix_r_ = Eigen::MatrixXd::Identity(1,1);
    matrix_r_(0,0) = 1.0; 
    Eigen::MatrixXd matrix_k_ = Eigen::MatrixXd::Zero(1,basic_state_size_);
    //matrix_k_=[0.970447 2.57842]
    Eigen::MatrixXd matrix_state_ = Eigen::MatrixXd::Zero(basic_state_size_, 1);
    matrix_state_(0,0) = 0.1; 
    matrix_state_(1,0) = 0.1; 
    apollo::common::math::SolveLQRProblem(matrix_adc_,matrix_bdc_,matrix_q_,
                                                                                    matrix_r_,lqr_eps_, lqr_max_iteration_,
                                                                                    &matrix_k_);
    
    double wheel_steer_angle_feedback = -(matrix_k_ * matrix_state_)(0, 0);//前轮转角
    //wheel_steer_angle_feedback=-0.539088;
    std::cout   << "[test] LQRK2: matrix_adc_=["<< matrix_adc_(0,0) << " "<< matrix_adc_(0,1) << " "<< matrix_adc_(1,0) << " "<< matrix_adc_(1,1)
                        << "], matrix_bdc_=["<< matrix_bdc_(0,0) << " "<< matrix_bdc_(1,0)
                        << "], matrix_q_=["<< matrix_q_(0,0) << " "<< matrix_q_(0,1)  << " "<< matrix_q_(1,0) << " "<< matrix_q_(1,1)
                        << "], matrix_r_=["<< matrix_r_(0,0)
                        << "], matrix_k_=["<< matrix_k_(0,0)  << " "<< matrix_k_(0,1)
                        << "], matrix_state_=["<< matrix_state_(0,0) << " "<< matrix_state_(1,0) << "]"
                        <<", wheel_steer_angle_feedback="<<wheel_steer_angle_feedback 
                        << ", contribution_lat="<< (-matrix_k_(0,0) * matrix_state_(0,0))
                        << ", contribution_heading="<< (-matrix_k_(0,1) * matrix_state_(1,0))
                        << ", contribution_lat_rate="<< (-matrix_k_(0,0) * matrix_state_(0,0)) / wheel_steer_angle_feedback
                        << ", contribution_heading_rate="<< (-matrix_k_(0,1) * matrix_state_(1,0)) / wheel_steer_angle_feedback
                        << std::endl;

    std::cout   <<"-----------------------------------------------------------------------------------------------"<< std::endl;

    for(int i=0;i<100;i++){
        double current_tmp = 30.0*static_cast<double>(i)/100.0;
        matrix_r_(0,0) = current_tmp; 
        apollo::common::math::SolveLQRProblem(matrix_adc_,matrix_bdc_,matrix_q_,
                                                                                    matrix_r_,lqr_eps_, lqr_max_iteration_,
                                                                                    &matrix_k_);
    
        double wheel_steer_angle_feedback = -(matrix_k_ * matrix_state_)(0, 0);//前轮转角
        //wheel_steer_angle_feedback=-0.539088;
        std::cout   << "[test] LQRK2: matrix_adc_=["<< matrix_adc_(0,0) << " "<< matrix_adc_(0,1) << " "<< matrix_adc_(1,0) << " "<< matrix_adc_(1,1)
                            << "], matrix_bdc_=["<< matrix_bdc_(0,0) << " "<< matrix_bdc_(1,0)
                            << "], matrix_q_=["<< matrix_q_(0,0) << " "<< matrix_q_(0,1)  << " "<< matrix_q_(1,0) << " "<< matrix_q_(1,1)
                            << "], matrix_r_=["<< matrix_r_(0,0)
                            << "], matrix_k_=["<< matrix_k_(0,0)  << " "<< matrix_k_(0,1)
                            << "], matrix_state_=["<< matrix_state_(0,0) << " "<< matrix_state_(1,0) << "]"
                            <<", wheel_steer_angle_feedback="<<wheel_steer_angle_feedback 
                            << ", contribution_lat="<< (-matrix_k_(0,0) * matrix_state_(0,0))
                            << ", contribution_heading="<< (-matrix_k_(0,1) * matrix_state_(1,0))
                            << ", contribution_lat_rate="<< (-matrix_k_(0,0) * matrix_state_(0,0)) / wheel_steer_angle_feedback
                            << ", contribution_heading_rate="<< (-matrix_k_(0,1) * matrix_state_(1,0)) / wheel_steer_angle_feedback
                            << std::endl;
    }

    std::cout   <<"-----------------------------------------------------------------------------------------------"<< std::endl;
    matrix_r_(0,0) = 1.0; 
    matrix_q_(0,0) = 1.0; matrix_q_(0,1) = 0.0;
    matrix_q_(1,0) = 0.0; matrix_q_(1,1) = 1.0; 
    for(int i=0;i<100;i++){
        double current_tmp = 30.0*static_cast<double>(i)/100.0;
        matrix_q_(0,0) = current_tmp;
        apollo::common::math::SolveLQRProblem(matrix_adc_,matrix_bdc_,matrix_q_,
                                                                                    matrix_r_,lqr_eps_, lqr_max_iteration_,
                                                                                    &matrix_k_);
    
        double wheel_steer_angle_feedback = -(matrix_k_ * matrix_state_)(0, 0);//前轮转角
        //wheel_steer_angle_feedback=-0.539088;
        std::cout   << "[test] LQRK2: matrix_adc_=["<< matrix_adc_(0,0) << " "<< matrix_adc_(0,1) << " "<< matrix_adc_(1,0) << " "<< matrix_adc_(1,1)
                            << "], matrix_bdc_=["<< matrix_bdc_(0,0) << " "<< matrix_bdc_(1,0)
                            << "], matrix_q_=["<< matrix_q_(0,0) << " "<< matrix_q_(0,1)  << " "<< matrix_q_(1,0) << " "<< matrix_q_(1,1)
                            << "], matrix_r_=["<< matrix_r_(0,0)
                            << "], matrix_k_=["<< matrix_k_(0,0)  << " "<< matrix_k_(0,1)
                            << "], matrix_state_=["<< matrix_state_(0,0) << " "<< matrix_state_(1,0) << "]"
                            <<", wheel_steer_angle_feedback="<<wheel_steer_angle_feedback 
                            << ", contribution_lat="<< (-matrix_k_(0,0) * matrix_state_(0,0))
                            << ", contribution_heading="<< (-matrix_k_(0,1) * matrix_state_(1,0))
                            << ", contribution_lat_rate="<< (-matrix_k_(0,0) * matrix_state_(0,0)) / wheel_steer_angle_feedback
                            << ", contribution_heading_rate="<< (-matrix_k_(0,1) * matrix_state_(1,0)) / wheel_steer_angle_feedback
                            << std::endl;
    }

    std::cout   <<"-----------------------------------------------------------------------------------------------"<< std::endl;
    matrix_r_(0,0) = 1.0; 
    matrix_q_(0,0) = 1.0; matrix_q_(0,1) = 0.0;
    matrix_q_(1,0) = 0.0; matrix_q_(1,1) = 1.0; 
    for(int i=0;i<100;i++){
        double current_tmp = 30.0*static_cast<double>(i)/100.0;
        matrix_q_(1,1) = current_tmp; 
        apollo::common::math::SolveLQRProblem(matrix_adc_,matrix_bdc_,matrix_q_,
                                                                                    matrix_r_,lqr_eps_, lqr_max_iteration_,
                                                                                    &matrix_k_);
    
        double wheel_steer_angle_feedback = -(matrix_k_ * matrix_state_)(0, 0);//前轮转角
        //wheel_steer_angle_feedback=-0.539088;
        std::cout   << "[test] LQRK2: matrix_adc_=["<< matrix_adc_(0,0) << " "<< matrix_adc_(0,1) << " "<< matrix_adc_(1,0) << " "<< matrix_adc_(1,1)
                            << "], matrix_bdc_=["<< matrix_bdc_(0,0) << " "<< matrix_bdc_(1,0)
                            << "], matrix_q_=["<< matrix_q_(0,0) << " "<< matrix_q_(0,1)  << " "<< matrix_q_(1,0) << " "<< matrix_q_(1,1)
                            << "], matrix_r_=["<< matrix_r_(0,0)
                            << "], matrix_k_=["<< matrix_k_(0,0)  << " "<< matrix_k_(0,1)
                            << "], matrix_state_=["<< matrix_state_(0,0) << " "<< matrix_state_(1,0) << "]"
                            <<", wheel_steer_angle_feedback="<<wheel_steer_angle_feedback 
                            << ", contribution_lat="<< (-matrix_k_(0,0) * matrix_state_(0,0))
                            << ", contribution_heading="<< (-matrix_k_(0,1) * matrix_state_(1,0))
                            << ", contribution_lat_rate="<< (-matrix_k_(0,0) * matrix_state_(0,0)) / wheel_steer_angle_feedback
                            << ", contribution_heading_rate="<< (-matrix_k_(0,1) * matrix_state_(1,0)) / wheel_steer_angle_feedback
                            << std::endl;
    }

    return true;

}


bool RecordDebugInfoTest(){
    record_debug_info_.reset(new RecordDebugInfo(10, 1, 1));
    std::cout << "-----------------GetTimeString=" << record_debug_info_->GetTimeString();
    std::cout << ",GetLogTimeString=" << record_debug_info_->GetLogTimeString();
    std::cout << ",TimestamptoString="  << record_debug_info_->TimestamptoString(1739438263066) << std::endl;
    std::cout << ",LogTimetoString="  << record_debug_info_->LogTimetoString(61027.003) << std::endl;
    // record_debug_info_->SetEnterL2TimeRecordInfo();
    std::array<double ,9> tmp = {1,2,3,4,5,6,7,8,9};
    record_debug_info_->FillAllDebugInfo(1, tmp);
    tmp[1]=0;
    record_debug_info_->FillAllDebugInfo(2, tmp);
    tmp[1]=10;
    record_debug_info_->FillAllDebugInfo(3, tmp);
    record_debug_info_->WriteJsonFile(1234567,"./");
    return true;
}

TEST(SolveLQRProblem, load_calibration_table) {
  EXPECT_TRUE(SolveLQRProblemTest());
}

bool MyOSQPTest(){
    double eps_abs = 0.01;
    bool polish = true;
    int max_iteration = 4000;
    osqp_solver_.reset(new QPSolverOSQP(static_cast<c_float>(eps_abs), 
                                                    polish, max_iteration));

    // Eigen::MatrixXd P(2,2);
    // P <<2,0,0,2;
    // Eigen::MatrixXd A(2,2);
    // A<< 1,0,0,1;
    // std::vector<double> q = {-2,-2};
    // std::vector<double> l={0.0,0.0}; 
    // std::vector<double> u={1.5,1.5};

    Eigen::MatrixXd P(2,2);
    P <<0.5,0,0,0;
    Eigen::MatrixXd A(3,2);
    A<< 1,1,0,1,1,0;
    std::vector<double> q = {0,2};
    std::vector<double> l={-OSQP_INFTY,0.0,0.0}; 
    std::vector<double> u={3,OSQP_INFTY,OSQP_INFTY};
    
    auto t_start = std::chrono::system_clock::now();

    auto result = osqp_solver_->optimize(P, A, q, l, u);

    std::vector<double> U_osqp = std::get<0>(result);
    Eigen::VectorXd r_u = Eigen::Map<Eigen::Matrix<double, Eigen::Dynamic, 1>>(
        &U_osqp[0], static_cast<Eigen::Index>(U_osqp.size()), 1);

    const int solver_result = std::get<3>(result);
    // if (solver_result != 1)
    // {
    // AERROR << "optimization failed.";
    // return false;
    // }
    // bool solver_result = osqp_solver_->solve(H, f.transpose(), A, lb, ub, lbA, ubA, Uex);

    auto t_end = std::chrono::system_clock::now();

    {
        auto t = std::chrono::duration_cast<std::chrono::milliseconds>(t_end - t_start).count();
        AINFO << "r_u:"<<r_u;
        AINFO << "mpc solver time:" << t;
    }

    if(!solver_result){
        AERROR << "qp solver error";
        return false;
    }
    
    return true;
}

// TEST(SolveLQRProblem, load_calibration_table) {
//   EXPECT_TRUE(SolveLQRProblemTest());
// }

// TEST(RecordDebugInfo, record_debug_info) {
//   EXPECT_TRUE(RecordDebugInfoTest());
// }

TEST(OSQPTest, osqp_test) {
  EXPECT_TRUE(MyOSQPTest());
}

// TEST(RecordDebugInfo, record_debug_info) {
//   EXPECT_TRUE(RecordDebugInfoTest());
// }

// TEST(ACCTEST, acc_test) {
//   EXPECT_TRUE(ACCTEST());
// }

// 如果在此处不写main函数，那么在链接库的时候还需要链接-lgtest_main, 否则只需链接-lgtest即可。
#if 1
int main(int argc, char **argv)
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
#endif
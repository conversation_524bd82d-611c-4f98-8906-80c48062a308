#include "control_component.h"

#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"

using namespace mega::control;
std::shared_ptr <Node> pNode = nullptr;
std::shared_ptr<ControlComponent> control_component = nullptr;

#if (USE_SINGLE)
extern "C" int stop() {
    return 0;
}
extern "C" int start() {
    return 0;
}
extern "C" int run_acc_control_server(int argc, char *argv[]) {
#else
int main (int argc, char *argv[]) {
    apollo::cyber::Init(argv[0]);
#endif

    std::cout<<"Enter ACC Control main function..."<<std::endl;

    adas::app_template app;
    namespace bpo = boost::program_options;
    // clang-off
    app.add_options()
        ("cyber_path",bpo::value<std::string>()->default_value("config"),"specify CYBER_PATH for adas")
        ("cyber_ip",bpo::value<std::string>()->default_value("***********"),"specify CYBER_IP for adas")
        ("cyber_log",bpo::value<std::string>()->default_value("logs"),"specify CYBER_LOG for adas")
        ("deubg_enabled", bpo::value<bool>()->default_value(false), "debug default tracking")
        ("log", bpo::value<bool>()->default_value(false), "debug default tracking")
        ("discard", bpo::value<int>()->default_value(800), "discard limit of request")
        ("timeout", bpo::value<int>()->default_value(20), "timeout of request")
        ("update_prefix", bpo::value<std::string>()->default_value(""), "update prefix")
        ("node_name",bpo::value<std::string>()->default_value("acc_control"),"apa tracking node name")
        ("conf", bpo::value<std::string>()->default_value("conf/acc_control.ini"), "template")
        ("node_cfg",bpo::value<std::string>()->default_value("conf/acc_control_server.json"),"node config json file")
        ("root_path",bpo::value<std::string>()->default_value("conf/control_conf.pb.txt"),"root path of config json data")
        ("vehicle_param_path",bpo::value<std::string>()->default_value("conf/vehicle_param.pb.txt"),"vehicle_param path of config json data");
    // clang-on
    auto ret = app.run(argc,argv,"conf") ;
    if(ret != 0){
        if(ret == 1){
        std::cout <<"show help!"<< std::endl;
        return 0;
        }
        std::cout <<"command_line or conf_file parse failed !"<< std::endl;
        return -1;
    }
    auto &&config = app.configuration();
    auto node_cfg = config["node_cfg"].as<std::string>();
    auto node_name = config["node_name"].as<std::string>();
#if SIMULATION
    auto cfg_path = "conf/control_conf_carla.pb.txt";
    AERROR<<"SIMULATION: cfg_path="<<cfg_path;
#else
    auto cfg_path = config["root_path"].as<std::string>();
    AERROR<<"DEFAULT: cfg_path="<<cfg_path;
#endif


    // std::string vehicle_param_path = "conf/vehicle_param.pb.txt";
    auto vehicle_param_path = config["vehicle_param_path"].as<std::string>();
    AERROR<<"DEFAULT: vehicle_param_path="<<vehicle_param_path;
    
    bpo::notify(config);
    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        return 1;
    }
    if (nullptr == pNode) {
        std::shared_ptr <Node> node(apollo::cyber::CreateNode(nodeCfg.getName()));
        pNode = node;
    }

    if (control_component == nullptr)
    {
        control_component = std::make_shared<ControlComponent>(pNode, nodeCfg, cfg_path, vehicle_param_path);
    }

    std::cout << "ACC Control init done. Enjoy." << std::endl;
  #if (!USE_SINGLE)
      apollo::cyber::WaitForShutdown();
  #endif
  return 0;
}
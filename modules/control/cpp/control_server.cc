#include <string>
#include <iostream>
#include <fstream>
#include <vector>
#include <complex>
#include <functional>
#include <math.h>
#include <glog/logging.h>
#include <google/protobuf/any.pb.h>
#include "cyber/cyber.h"
#include "cyber/node/writer.h"
#include "cyber/timer/timer.h"
#include <google/protobuf/util/json_util.h>
#include "thirdparty/recommend_protocols/common/proto/basic.pb.h"
#include "base/util/config_parser.hpp"
#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"
#include "base/exception_type.h"
#include "thirdparty/recommend_protocols/perception/proto/parking_slot.pb.h"
#include "thirdparty/recommend_protocols/location/proto/map.pb.h"
#include "thirdparty/recommend_protocols/location/proto/location.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/route.pb.h"
#include "thirdparty/recommend_protocols/control/proto/control_input.pb.h"
#include "thirdparty/recommend_protocols/control/proto/control_output.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/common/proto/exception.pb.h"
#if USE_SIMULATION
#include "thirdparty/recommend_protocols/simulation/proto/sim_vehicle.pb.h"
#endif
#include "control_maker.hpp"
#if USE_SINGLE
namespace apa_control_server{
#endif
using apollo::cyber::Node;
using apollo::cyber::Writer;
using rainbowdash::common::Basic;
using rainbowdash::common::StateOfModule;
using rainbowdash::common::Header;
using rainbowdash::common::CommonBool;
using rainbowdash::common::SlotType;
using rainbowdash::common::CommonGearPosition;
using rainbowdash::common::Point2D;
using rainbowdash::common::HMISession;
using rainbowdash::location::SlamFrame;
using rainbowdash::location::ParkingGarage;
using rainbowdash::planning::SplineRoute;
using rainbowdash::planning::ObstacleAvoid;
using rainbowdash::control::VehicleState;
using rainbowdash::control_by_wire::ChassisLongInfo;
using rainbowdash::control_by_wire::ChassisLatInfo;
using rainbowdash::control_by_wire::LatAndLongControlCmd;
using rainbowdash::control_by_wire::TorqueControlData;
using rainbowdash::control_by_wire::SVControlData;
using rainbowdash::control_by_wire::AccelerationControlData;
using rainbowdash::control_by_wire::ChassisShiftGearInfo;
// using rainbowdash::control::ControlMonitorInfo;
using rainbowdash::control::ParkingFinishState;
using rainbowdash::control::DecCtlStart;
using rainbowdash::control::DecCtlPause;
using rainbowdash::control::DecCtlStop;
using rainbowdash::control::DecCtlStopSafe;
using rainbowdash::control::CtlStatemachineName;
using rainbowdash::control::DecCtlTerminateReq;
using rainbowdash::control::CtlTerminateRet;
using rainbowdash::common::ExceptionPtr;
#if USE_SIMULATION
using sim_vehicle::control_io::SimVehicleContorlInput;
#endif

class ApaControl: public ModuleBase{
private:
    std::size_t seq;
    control_maker::ControlMaker* pmaker = nullptr;
    uint64_t session_id_{0};
    char* dump_dir_ = getenv("GLOG_log_dir");
    std::string session_path_{};
    double init_time_ = 0.0;

public:
    std::shared_ptr <Writer<ParkingFinishState>> pWriterFinishState = nullptr;
    std::shared_ptr<Writer<ExceptionPtr>> pWriterMonitorState = nullptr;
    std::shared_ptr<Writer<VehicleState>> pWriterVehicleState = nullptr;
    std::shared_ptr<Writer<CtlStatemachineName>> pWriterCtlStatemachineName = nullptr;
    std::shared_ptr <Writer<LatAndLongControlCmd>> pWriterLatAndLongControlCmd = nullptr;
    std::shared_ptr<Writer<CtlTerminateRet>> pWriterCtlTerminateRet = nullptr;
#if USE_SIMULATION
    std::shared_ptr <Writer<SimVehicleContorlInput>> pWriterSimChassis = nullptr;
#endif

public:
    ApaControl(const std::string& ctrlFiles) : seq(0) {
        pmaker = &control_maker::ControlMaker::getInstance();
        pmaker->Init(ctrlFiles);
        pmaker->setMsgSendCallback(DATAO_CTRL_STATEMACHINE_NAME, std::bind(&ApaControl::statemachineMsgSend, this, std::placeholders::_1));
        pmaker->setMsgSendCallback(DATAO_VEHICLE_STATE, std::bind(&ApaControl::vehiclestateMsgSend, this, std::placeholders::_1));
        pmaker->setMsgSendCallback(DATAO_CTRL_FINISH, std::bind(&ApaControl::finishctrlMsgSend, this, std::placeholders::_1));
        pmaker->setMsgSendCallback(DATAO_CTRL_MONITOR, std::bind(&ApaControl::monitorMsgSend, this, std::placeholders::_1));
        pmaker->setMsgSendCallback(DATAO_PRJ_CTRL, std::bind(&ApaControl::prjctrlMsgSend, this, std::placeholders::_1));
        pmaker->setMsgSendCallback(UPLOADER_PARKEND_MSG, std::bind(&ApaControl::uploaderParkingEndState, this, std::placeholders::_1));
    #if(USE_SIMULATION)
        pmaker->setMsgSendCallback(DATAO_SIM_CTRL, std::bind(&ApaControl::simctrlMsgSend, this, std::placeholders::_1));
    #endif
    }
    ~ApaControl() { }

    //input data
    void update_chassis_loninfo(const std::shared_ptr<ChassisLongInfo>& msg){
        auto bool_translation = [](CommonBool::Enum _bool_data){
            return _bool_data == CommonBool::TRUE ? true : false;
        };

        control_maker::ChassisLonInfo chassis_loninfo;
        auto chassis_londata = msg->data();
        chassis_loninfo.lon_velocity = chassis_londata.speed();
        chassis_loninfo.lon_acceleration = chassis_londata.lon_acceleration();
        chassis_loninfo.cur_torque =chassis_londata.cur_torque();
        chassis_loninfo.min_torque = chassis_londata.min_torque();
        chassis_loninfo.max_torque = chassis_londata.max_torque();
        chassis_loninfo.gear_pos = static_cast<pnc::CommonGearPosition>((int)chassis_londata.gear_pos());
        chassis_loninfo.esp_standstill = bool_translation(chassis_londata.stand_still());
        chassis_loninfo.ibcu_pressure = chassis_londata.ibcu_pressure();
        chassis_loninfo.slope = chassis_londata.slope();
        chassis_loninfo.mcu_brake = (chassis_londata.sv_result() > 0);
        auto chassis_lonstate = msg->state();
        chassis_loninfo.esp_valid = bool_translation(chassis_lonstate.esp_valid());
        chassis_loninfo.epb_valid = bool_translation(chassis_lonstate.epb_valid());
        chassis_loninfo.vcu_valid = bool_translation(chassis_lonstate.vcu_valid());

        pmaker->setColData(DATAI_CTRL_CHASSISLON, &chassis_loninfo);

        return;
    }

    void update_chassis_latinfo(const std::shared_ptr<ChassisLatInfo>& msg){
        auto bool_translation = [](CommonBool::Enum _bool_data){
            return _bool_data == CommonBool::TRUE ? true : false;
        };

        control_maker::ChassisLatInfo chassis_latinfo;
        auto chassis_latdata = msg->data();
        chassis_latinfo.steer_angle = chassis_latdata.steering_angle();
        chassis_latinfo.steer_velocity = chassis_latdata.steering_velocity();
        chassis_latinfo. steer_torque = chassis_latdata.steering_torque();
        auto chassis_latstate = msg->state();
        chassis_latinfo.eps_valid = bool_translation(chassis_latstate.eps_valid());

        pmaker->setColData(DATAI_CTRL_CHASSISLAT, &chassis_latinfo);
        return;
    }

    void update_ins_info(const std::shared_ptr<SlamFrame>& msg){
        control_maker::INSInfo ins_info;
        ins_info.lon_acceleration = msg->accelerate().lon();
        ins_info.lat_acceleration = msg->accelerate().lat();
        // ins_info.slope

        pmaker->setColData(DATAI_CTRL_INS, &ins_info);
        return;
    }

    void update_slam_info(const std::shared_ptr <SlamFrame> &msg){
        auto header = msg->mutable_header();
        // _vehicleMsgTimeStamp = header->timestamp();

        control_maker::SlamInfo slam_info;
        auto position = msg->location().position();
        slam_info.x = position.x();
        slam_info.y = position.y();
        slam_info.yaw = msg->location().theta();
        slam_info.ori_x = msg->ori_location().position().x();
        slam_info.ori_y = msg->ori_location().position().y();
        slam_info.ori_yaw = msg->ori_location().theta();
        slam_info.yaw_rate = msg->yaw_rate();
        pmaker->setColData(DATAI_CTRL_SLAM, &slam_info);

        update_ins_info(msg);

        return;
    }

    void update_slot_info (const std::shared_ptr <ParkingGarage> &msg){
        control_maker::SlotInfo slot_info;
        control_maker::SlotBoundary slot_boundary;
        control_maker::CarLimiter car_limiter;
        std::vector<control_maker::Bump> bumps;
        std::vector<control_maker::CarLimiter> limiters;
        slot_boundary.top_left(msg->top_left().x(),msg->top_left().y());
        slot_boundary.top_right(msg->top_right().x(),msg->top_right().y());
        slot_boundary.bottom_left(msg->bottom_left().x(),msg->bottom_left().y());
        slot_boundary.bottom_right(msg->bottom_right().x(),msg->bottom_right().y());

        const auto& limiter = msg->limiter();
        car_limiter.limiter_exist(limiter.limiter_exist());
        if(car_limiter.limiter_exist()){
            car_limiter.limiter_left(limiter.e_left().x(),limiter.e_left().y());
            car_limiter.limiter_right(limiter.e_right().x(),limiter.e_right().y());
        }

        const auto& proto_bumps = msg->bumps();
        for(const auto& proto_bump : proto_bumps){
            bumps.emplace_back();
            bumps.back().top_left(proto_bump.top_left().x(),proto_bump.top_left().y());
            bumps.back().top_right(proto_bump.top_right().x(),proto_bump.top_right().y());
            bumps.back().bottom_left(proto_bump.bottom_left().x(),proto_bump.bottom_left().y());
            bumps.back().bottom_right(proto_bump.bottom_right().x(),proto_bump.bottom_right().y());
        }

        const auto& proto_limiters = msg-> limiters();
        for(const auto& proto_limiter : proto_limiters){
            limiters.emplace_back();
            limiters.back().limiter_left(proto_limiter.e_left().x(),proto_limiter.e_left().y());
            limiters.back().limiter_right(proto_limiter.e_right().x(),proto_limiter.e_right().y());
        }

        slot_info.slot_boundary(slot_boundary);
        slot_info.car_limiter(car_limiter);
        slot_info.bumps(bumps);
        slot_info.limiters(limiters);

        pmaker->setColData(DATAI_CTRL_SLOT, &slot_info);
        return;
    }

    void update_routing_info(const std::shared_ptr <SplineRoute> &msg){
        //spline receive
        //spline
        control_maker::SplineRoutingInfo spline_routing_info;

        //route type
        spline_routing_info.slot_type(static_cast<pnc::SlotType>((int)msg->slot_type()));

        auto &splines_read = msg->spline();
        if(splines_read.size() == 0){
            AERROR << "Failed routing message!";
            return;
        }
        std::vector<Spline2d> splines;
        for (const auto &spline_read: splines_read){
            Spline2d spline;
            std::vector<double> t_knots;
            std::vector < std::pair < std::vector < double > , std::vector < double>>> params;
            if (spline_read.t_knots_size() - 1 != spline_read.spline_seg_size()) {
                AERROR << "spline read failed!";
                return;
            }
            uint32_t spline_order = spline_read.spline_order();
            t_knots.clear();
            for (const auto& t_knot: spline_read.t_knots()) {
                t_knots.push_back(t_knot);
            }
            params.clear();
            for (const auto& splineSeg_read: spline_read.spline_seg()) {
                std::vector<double> x_param;
                std::vector<double> y_param;
                if (splineSeg_read.x_param_size() != splineSeg_read.y_param_size()) {
                    AERROR << "spline read failed!";
                    return;
                }
                for (auto i = 0; i < splineSeg_read.x_param_size(); i++) {
                    x_param.push_back(splineSeg_read.x_param(i));
                    y_param.push_back(splineSeg_read.y_param(i));
                }
                params.emplace_back(std::make_pair(x_param, y_param));
            }
            spline.create_splines(t_knots, params, spline_order);
            splines.emplace_back(spline);
        }
        spline_routing_info.splines(splines);

        //gear
        std::vector<pnc::CommonGearPosition> tar_gears;
        for (auto& is_forward: msg->isforward()) {
            tar_gears.push_back(is_forward ? pnc::CommonGearPosition::DRIVING : pnc::CommonGearPosition::REVERSE);
        }
        spline_routing_info.tar_gears(tar_gears);

        if(splines.size() != tar_gears.size()){
            AERROR << "spline read failed!";
            return;
        }

        //max step
        spline_routing_info.max_step(splines.size());

        //related slot
        if(msg->slot_type() >= SlotType::VERTICAL && msg->slot_type() <= SlotType::OBLIQUE){
            Node3d slot_cen;
            auto msg_slot_cen = msg->slot_cen();
            slot_cen.SetX(msg_slot_cen.position().x());
            slot_cen.SetY(msg_slot_cen.position().y());
            slot_cen.SetPhi(msg_slot_cen.theta());
            spline_routing_info.slot_cen(slot_cen);

            auto last_spline = splines.back();
            auto routing_end_point = last_spline(last_spline.t_knots().back());
            spline_routing_info.end_point(routing_end_point.first, routing_end_point.second);
        }

        pmaker->setColData(DATAI_SPLINE_ROUTING, &spline_routing_info);
        PRINT_INTERFACE(*msg);
        return;
    }

    void update_uls_info(const std::shared_ptr <ObstacleAvoid> &msg){
        control_maker::UltraSoundObsAvoid uls_info;
        uls_info.is_trigger = !msg->exit_status();
        uls_info.decelerate = msg->decelerate();
        uls_info.v_max = msg->v_max();
        uls_info.tar_distance = msg->tar_distance();
        uls_info.tar_velocity = msg->tar_velocity();

        pmaker->setColData(DATAI_CTRL_ULS, &uls_info);
        return;
    }

    void update_start_info(const std::shared_ptr <DecCtlStart> &msg){
        init_time_ = getTimeS(nullptr);
        AERROR << "CON: init_time=" << init_time_;

        control_maker::CtrlStartInfo external_apa_start;
        external_apa_start.ctrl_start(msg->start());

        pmaker->setColData(DATAI_EXTERNAL_APA_START, &external_apa_start);
        PRINT_INTERFACE(*msg);
        return;
    }

    void update_external_pause_info(const std::shared_ptr<DecCtlPause>& msg){
        control_maker::ExternalApaPauseInfo external_apa_pause;
        external_apa_pause.pause_parking(msg->pause());
        external_apa_pause.again_parking(msg->again());

        pmaker->setColData(DATAI_EXTERNAL_APA_PAUSE, &external_apa_pause);
        PRINT_INTERFACE(*msg);
        return;
    }

    void update_external_stop_info(const std::shared_ptr<DecCtlStop> & msg){
        control_maker::ExternalApaStopInfo extern_apa_stop;
        extern_apa_stop.stop_parking(msg->stop());

        pmaker->setColData(DATAI_EXTERNAL_APA_STOP, &extern_apa_stop);
        PRINT_INTERFACE(*msg);
        return;
    }

    void update_safestop_info(const std::shared_ptr<DecCtlStopSafe>& msg){
        control_maker::ExternalSafestopInfo extern_safestop_info;
        extern_safestop_info.safe_stop(msg->stop());
        extern_safestop_info.enable_pgear(msg->enable_p_gear());
        extern_safestop_info.save_path(msg->hold_last_path());

        pmaker->setColData(DATAI_EXTERNAL_APA_SAFESTOP, &extern_safestop_info);
        PRINT_INTERFACE(*msg);
        return;
    }

    void update_iofeedback_info(const std::shared_ptr<ChassisShiftGearInfo>& msg) {
        control_maker::IoFeedbackInfo io_feedback_info;
        io_feedback_info.gear_shifted((int)msg->gear_shifted() == (int)pnc::CommonBool::TRUE_);

        pmaker->setColData(DATAI_IO_FEEDBACK, &io_feedback_info);
        return;
    }

    void update_hmisession_info(const std::shared_ptr<HMISession>& msg){
        session_id_ = msg->session_id();
        session_path_ = msg->session_path();
        return;
    }

    void update_decctl_terminate_req(const std::shared_ptr<DecCtlTerminateReq>& msg){
        if(nullptr == pWriterCtlTerminateRet){
            return;
        }
        if(msg->req_terminate()){
            bool ret_terminate = ((pmaker->getRootState()->state_name()).compare(std::string{"Waiting"}) == 0);
            CtlTerminateRet cmd;
            auto header = cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            cmd.set_ret_terminate(ret_terminate);

            pWriterCtlTerminateRet->Write(cmd);
            PRINT_INTERFACE(cmd);
        }
        return;
    }

    //output
    void statemachineMsgSend(void* pmsg){
        if(nullptr == pWriterCtlStatemachineName){
            return;
        }
        auto p = (std::string*)pmsg;
        CtlStatemachineName cmd;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        cmd.set_name(*p);

        pWriterCtlStatemachineName->Write(cmd);
        return;
    }

    void monitorMsgSend(void* pmsg){
        if(nullptr == pWriterMonitorState){
            return;
        }
        auto p = (control_maker::CtlMonitorInfo*)pmsg;

        uint64_t err_code = 0x0600ffff;
        if(SPLIT_LEVEL(err_code) != exception::ExceptionLevel::LEVEL0){
            return;
        }

        switch(p->except_id()){
            #define X_MACRO(a, b) case b:
               CTLEXCEPT_TYPE
            #undef X_MACRO
                err_code = p->except_id();
                break;
            default:
                AERROR << "CON: EXCEPTMATCHERROR! err_code=" << p->except_id();
                break;
        }
        ExceptionPtr cmd;
        cmd.set_code(err_code);
        std::string name = p->except_name();
        std::vector<std::string> vStr;
        boost::split(vStr, name, boost::is_any_of("/"), boost::token_compress_on);
        cmd.set_name(std::string("CTL_" + vStr.back()));
        pWriterMonitorState->Write(cmd);
        PRINT_INTERFACE(cmd);
        return;
    }

    void finishctrlMsgSend(void* pmsg){
        if(nullptr == pWriterFinishState){
            return;
        }
        auto translation_bool = [](bool _bool_data) {
            return (_bool_data == true? CommonBool::TRUE : CommonBool::FALSE);
        };
        auto p = (control_maker::CtrlFinishInfo*)pmsg;
        ParkingFinishState cmd;

        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));

        cmd.set_parking_achieved(translation_bool(p->ctrl_finish));
        cmd.set_except_stop(translation_bool(p->ctrl_except_stop));

        pWriterFinishState->Write(cmd);
        PRINT_INTERFACE(cmd);
        return;
    }


    void vehiclestateMsgSend(void*pmsg){
        if(nullptr == pWriterVehicleState){
            return;
        }

        auto bool_proto_trans = [](bool _bool_value){
            return (_bool_value ? CommonBool::TRUE : CommonBool::FALSE);
        };

        auto p = (control_maker::CtlVehiclestateInfo*)pmsg;
        VehicleState cmd;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));

        auto pose_err = cmd.mutable_pose_err();
        pose_err->set_lat_err(p->lat_err());
        pose_err->set_heading_err(p->heading_err());
        pose_err->set_long_err(p->lon_err());

        cmd.set_standstill(bool_proto_trans(p->one_end()));
        cmd.set_last_step(bool_proto_trans(p->last_step()));
        cmd.set_tar_gear(static_cast<CommonGearPosition::Enum>((int)p->tar_gear()));
        cmd.set_remains(p->remaining_s());

        pWriterVehicleState->Write(cmd);
        //need keep
        return;

    }

    void prjctrlMsgSend(void* pmsg){
    #if(CAR_CHANGAN_385|| CAR_WEIMA)
        prjctrltaMsgSend(pmsg);
    #endif
    #if(CAR_DONGFENG_S59)
        prjctrlsvMsgSend(pmsg);
    #endif
    #if(0)
        prjctrloaMsgSend(pmsg);
    #endif
        return;
    }
    void prjctrltaMsgSend(void* pmsg){
        if(nullptr == pWriterLatAndLongControlCmd){
            return;
        }
        auto translation_bool = [](bool _bool_data) {
            return (_bool_data == true? CommonBool::TRUE : CommonBool::FALSE);
        };
        auto p = (control_maker::PrjCtrlInfo*) pmsg;
        LatAndLongControlCmd cmd;

        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));

        auto ctrl_msg = cmd.mutable_data();

        TorqueControlData trq_ctrl_data;
        trq_ctrl_data.set_is_driving(translation_bool(p->is_driving));
        trq_ctrl_data.set_is_braking(translation_bool(p->is_braking));
        trq_ctrl_data.set_tar_torque(p->tar_torque);
        trq_ctrl_data.set_tar_deceleration(p->tar_dec);
        trq_ctrl_data.set_is_steering(translation_bool(p->is_steering));
        trq_ctrl_data.set_tar_steer_angle(p->tar_steer);
        trq_ctrl_data.set_put_gear(translation_bool(p->put_gear));
        trq_ctrl_data.set_tar_gear(static_cast<CommonGearPosition::Enum>((int)p->tar_gear));

        ctrl_msg->PackFrom(trq_ctrl_data);

#if(CAR_CHANGAN_385)
        cmd.set_hold((rainbowdash::common::CommonBool::Enum)p->hold_pressure);
        cmd.set_release((rainbowdash::common::CommonBool::Enum)p->release_pressure);
#endif

        pWriterLatAndLongControlCmd->Write(cmd);
        AINFO << "CON: out_time=" << (header->timestamp() - init_time_);
        return;
    }
    void prjctrlsvMsgSend(void* pmsg){
        if(nullptr == pWriterLatAndLongControlCmd){
            return;
        }
        auto translation_bool = [](bool _bool_data) {
            return (_bool_data == true? CommonBool::TRUE : CommonBool::FALSE);
        };
        auto p = (control_maker::PrjCtrlInfo*) pmsg;
        LatAndLongControlCmd cmd;

        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));

        auto ctrl_msg = cmd.mutable_data();

        SVControlData sv_ctrl_data;
        sv_ctrl_data.set_tar_distance(p->tar_s);
        sv_ctrl_data.set_tar_velocity(p->tar_v);
        sv_ctrl_data.set_is_steering(translation_bool(p->is_steering));
        sv_ctrl_data.set_tar_steer_angle(p->tar_steer);
        sv_ctrl_data.set_put_gear(translation_bool(p->put_gear));
        sv_ctrl_data.set_tar_gear(static_cast<CommonGearPosition::Enum>((int)p->tar_gear));

        ctrl_msg->PackFrom(sv_ctrl_data);

        pWriterLatAndLongControlCmd->Write(cmd);
        AINFO << "CON: out_time=" << (header->timestamp() - init_time_);
        return;
    }
    void prjctrloaMsgSend(void* pmsg){
        if(nullptr == pWriterLatAndLongControlCmd){
            return;
        }
        auto translation_bool = [](bool _bool_data) {
            return (_bool_data == true? CommonBool::TRUE : CommonBool::FALSE);
        };
        auto p = (control_maker::PrjCtrlInfo*) pmsg;
        LatAndLongControlCmd cmd;

        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));

        auto ctrl_msg = cmd.mutable_data();

        AccelerationControlData acc_ctrl_data;
        acc_ctrl_data.set_tar_acceleration(p->tar_acc);
        acc_ctrl_data.set_is_steering(translation_bool(p->is_steering));
        acc_ctrl_data.set_tar_steer_angle(p->tar_steer);
        acc_ctrl_data.set_put_gear(translation_bool(p->put_gear));
        acc_ctrl_data.set_tar_gear(static_cast<CommonGearPosition::Enum>((int)p->tar_gear));

        ctrl_msg->PackFrom(acc_ctrl_data);

        pWriterLatAndLongControlCmd->Write(cmd);
        AINFO << "CON: out_time=" << (header->timestamp() - init_time_);
        return;
    }

#if USE_SIMULATION
    void simctrlMsgSend(void* pmsg){
        if(nullptr == pWriterSimChassis){
            return;
        }
        auto p = (control_maker::SIMCtrl*) pmsg;
        SimVehicleContorlInput cmd;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        cmd.set_acc_i(p->tar_acc());
        cmd.set_is_lat_ctrl(p->is_steering());
        cmd.set_wheel_angle_i(p->tar_wheel());
        cmd.set_gear_i(static_cast<int32_t>(p->tar_gear()));
        pWriterSimChassis->Write(cmd);
        return;
    }
#endif
    void uploaderParkingEndState(void* pmsg){
        auto p = (std::string*) pmsg;
        std::string base_dir;
        if(!session_path_.empty()){
            base_dir = session_path_;
        }else if(dump_dir_ != nullptr){
            base_dir = (std::string)(dump_dir_);
        }else{
            AERROR << "dir not exist";
            return;
        }
        std::string file_path = base_dir + "/parking_end_status.json";
        AERROR << file_path;
        if(!save_map_json(file_path, *p)){
            AERROR << "Control save json failed!";
        }
        return;
    }

    //run
    void run(){
        pmaker->run();
        return;
    }

};

std::mutex chModuleStateMutx;
std::shared_ptr <Node> pNode = nullptr;
std::shared_ptr <apollo::cyber::Timer> pTimer = nullptr;
std::shared_ptr <ApaControl> pControl = nullptr;

extern "C" int start() {
    AINFO << "control start";
    control_maker::SystemInstruct sys_instruct;
    sys_instruct.system_stop(false);
    control_maker::ControlMaker::getInstance().setColData(DATAS_SYSTEM_INSTRUCT, &sys_instruct);

    if(pTimer != nullptr){
        pTimer->Start();
    }
  return 0;
}

#if (USE_SINGLE)
extern "C" int stop() {
    AINFO << "control shutdown";
    control_maker::SystemInstruct sys_instruct;
    sys_instruct.system_stop(true);
    control_maker::ControlMaker::getInstance().setColData(DATAS_SYSTEM_INSTRUCT, &sys_instruct);

    while(control_maker::ControlMaker::getInstance().getRootState()->state_name().compare(std::string("Start")) != 0){
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }

    if(pTimer != nullptr){
        pTimer->Stop();
    }
    return 0;
}

extern "C" int run_apa_control_server(int argc, char *argv[]) {
#else
int main (int argc, char *argv[]) {
    apollo::cyber::Init(argv[0]);
#endif
    adas::app_template app;
    namespace bpo = boost::program_options;
    std::cout <<"cplusplus : "<<__cplusplus<< std::endl;
    AINFO <<"control, APA_MODULE_NAME : "<<APA_MODULE_NAME;
    // clang-off
    app.add_options()
     ("cyber_path",bpo::value<std::string>()->default_value("config"),"specify CYBER_PATH for adas")
     ("cyber_ip",bpo::value<std::string>()->default_value("***********"),"specify CYBER_IP for adas")
     ("cyber_log",bpo::value<std::string>()->default_value("logs"),"specify CYBER_LOG for adas")
     ("deubg_enabled", bpo::value<bool>()->default_value(false), "debug default control")
     ("log", bpo::value<bool>()->default_value(false), "debug default control")
     ("discard", bpo::value<int>()->default_value(800), "discard limit of request")
     ("timeout", bpo::value<int>()->default_value(20), "timeout of request")
     ("update_prefix", bpo::value<std::string>()->default_value(""), "update prefix")
     ("monitor_toml",bpo::value<std::string>()->default_value(""),"monitor config toml file")
     ("other_yaml",bpo::value<std::string>()->default_value(""),"other config yaml file")
     ("ctrl_ctrls",bpo::value<std::string>()->default_value("conf/"),"ctrl control json files")
     ("node_name",bpo::value<std::string>()->default_value(""),"apa control node name")
     ("node_cfg",bpo::value<std::string>()->default_value("conf/apa_control_server.json"),"node config json file")
     ("conf", bpo::value<std::string>()->default_value("conf/apa_control.ini"), "template");
    // clang-on
    auto ret = app.run(argc,argv,"conf") ;
    if(ret != 0){
        if(ret == 1){
            std::cout <<"show help!"<< std::endl;
            return 0;
        }
        std::cout <<"command_line or conf_file parse failed !"<< std::endl;
        return -1;
    }
    auto &&config = app.configuration();
    auto cyber_path = config["cyber_path"].as<std::string>();
    auto cyber_ip = config["cyber_ip"].as<std::string>();
    auto cyber_log = config["cyber_log"].as<std::string>();
    auto ctrl_ctrls = config["ctrl_ctrls"].as<std::string>();
    auto node_cfg = config["node_cfg"].as<std::string>();
    auto monitor_yaml = config["monitor_toml"].as<std::string>();
    auto other_yaml = config["other_yaml"].as<std::string>();
    auto node_name = config["node_name"].as<std::string>();
    bpo::notify(config);

    std::cout <<"cyber_path : "<<cyber_path<< std::endl;
    std::cout <<"cyber_log : "<<cyber_log<< std::endl;
    std::cout <<"cyber_ip : "<<cyber_ip<< std::endl;
    std::cout <<"ctrl_ctrls : "<<ctrl_ctrls<< std::endl;
    std::cout <<"node_cfg : "<<node_cfg<< std::endl;
    std::cout <<"monitor_yaml : "<<monitor_yaml<< std::endl;
    std::cout <<"other_yaml : "<<other_yaml<< std::endl;

    AINFO << __FUNCTION__ << "argc " << argc;

    //apollo::cyber::Init(argv[0]);

    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        return 1;
    }
    if(node_name.empty()){
        node_name = nodeCfg.getName();
    }
    if (nullptr == pNode) {
        std::shared_ptr <Node> node(apollo::cyber::CreateNode(node_name));
        pNode = node;
    }

    if (pControl == nullptr) {
        pControl = std::make_shared<ApaControl>(ctrl_ctrls);
    }
    if (pTimer == nullptr) {
        auto control_callback = std::bind(&ApaControl::run, pControl);
        pTimer = std::make_shared<apollo::cyber::Timer>(20, control_callback, false, "apa_control_qnx");
    }

    control_maker::SystemInstruct sys_instruct;
    sys_instruct.system_stop(false);
    control_maker::ControlMaker::getInstance().setColData(DATAS_SYSTEM_INSTRUCT, &sys_instruct);
    if(pTimer != nullptr){
        pTimer->Start();
    }
    AINFO << "control start";

    if(nullptr == pControl->pWriterCtlStatemachineName){
        pControl->pWriterCtlStatemachineName = pNode->CreateWriter<CtlStatemachineName>(nodeCfg.getPubChannel("control_out_statemachine_name").name);
    }
    if (nullptr == pControl->pWriterLatAndLongControlCmd) {
        pControl->pWriterLatAndLongControlCmd = pNode->CreateWriter<LatAndLongControlCmd>(nodeCfg.getPubChannel("io_in_chassisctl").name);
    }
    if (nullptr == pControl->pWriterFinishState) {
        pControl->pWriterFinishState = pNode->CreateWriter<ParkingFinishState>(nodeCfg.getPubChannel("control_out_finish_state").name);
    }
    if(nullptr == pControl->pWriterVehicleState){
        pControl->pWriterVehicleState = pNode->CreateWriter<VehicleState>(nodeCfg.getPubChannel("control_vehicle_state").name);
    }
    if(nullptr == pControl->pWriterCtlTerminateRet){
        pControl->pWriterCtlTerminateRet = pNode->CreateWriter<CtlTerminateRet>(nodeCfg.getPubChannel("control_out_terminate_ret").name);
    }
    if(nullptr == pControl->pWriterMonitorState){
        pControl->pWriterMonitorState = pNode->CreateWriter<ExceptionPtr>(nodeCfg.getPubChannel("channel_exception").name);
    }
#if USE_SIMULATION
    if (nullptr == pControl->pWriterSimChassis) {
        pControl->pWriterSimChassis = pNode->CreateWriter<SimVehicleContorlInput>(nodeCfg.getPubChannel("sim_vehicle_control").name);
    }
#endif

    auto controlStart = nodeCfg.getSubChannel("control_in_start");
    auto start_listener = pNode->CreateReader<DecCtlStart>(controlStart.name, std::bind(&ApaControl::update_start_info, pControl, std::placeholders::_1));

    auto controlPause = nodeCfg.getSubChannel("control_in_pause");
    auto pause_listener = pNode->CreateReader<DecCtlPause>(controlPause.name, std::bind(&ApaControl::update_external_pause_info, pControl, std::placeholders::_1));

    auto controlStop = nodeCfg.getSubChannel("control_in_stop");
    auto stop_listener = pNode->CreateReader<DecCtlStop>(controlStop.name, std::bind(&ApaControl::update_external_stop_info, pControl, std::placeholders::_1));

    auto ioFeedbacks = nodeCfg.getSubChannel("io_out_chassis_gearshift");
    auto iofeedback_listener = pNode->CreateReader<ChassisShiftGearInfo>(ioFeedbacks.name, std::bind(&ApaControl::update_iofeedback_info, pControl, std::placeholders::_1));

    auto controlSafestop = nodeCfg.getSubChannel("control_in_stop_safe");
    auto decsafestop_listener = pNode->CreateReader<DecCtlStopSafe>(controlSafestop.name, std::bind(&ApaControl::update_safestop_info, pControl, std::placeholders::_1));

    auto planningPath = nodeCfg.getSubChannel("planning_path");
    apollo::cyber::ReaderConfig trajectoryConfig;
    trajectoryConfig.channel_name = planningPath.name;
    trajectoryConfig.pending_queue_size = planningPath.pendingQueueSize;
    auto trajectory_listener = pNode->CreateReader<SplineRoute>(trajectoryConfig, std::bind(&ApaControl::update_routing_info, pControl, std::placeholders::_1));

    auto locationVehicleinfo = nodeCfg.getSubChannel("location_vehicleinfo");
    apollo::cyber::ReaderConfig vehicleConfig;
    vehicleConfig.channel_name = locationVehicleinfo.name;
    vehicleConfig.pending_queue_size = locationVehicleinfo.pendingQueueSize;
    auto vehicle_listener = pNode->CreateReader<SlamFrame>(vehicleConfig, std::bind(&ApaControl::update_slam_info, pControl, std::placeholders::_1));

    auto ioChassisLoninfo = nodeCfg.getSubChannel("io_out_chassisinfo_lon");
    apollo::cyber::ReaderConfig chassislonConfig;
    chassislonConfig.channel_name = ioChassisLoninfo.name;
    chassislonConfig.pending_queue_size = ioChassisLoninfo.pendingQueueSize;
    auto chassislon_listener = pNode->CreateReader<ChassisLongInfo>(chassislonConfig, std::bind(&ApaControl::update_chassis_loninfo, pControl, std::placeholders::_1));

    auto ioChassisLatinfo = nodeCfg.getSubChannel("io_out_chassisinfo_lat");
    apollo::cyber::ReaderConfig chassislatConfig;
    chassislatConfig.channel_name = ioChassisLatinfo.name;
    chassislatConfig.pending_queue_size = ioChassisLatinfo.pendingQueueSize;
    auto chassislat_listener = pNode->CreateReader<ChassisLatInfo>(chassislatConfig, std::bind(&ApaControl::update_chassis_latinfo, pControl, std::placeholders::_1));

    auto locationSlotinfo = nodeCfg.getSubChannel("location_parkinfo");
    apollo::cyber::ReaderConfig slotConfig;
    slotConfig.channel_name = locationSlotinfo.name;
    slotConfig.pending_queue_size = locationSlotinfo.pendingQueueSize;
    auto slot_listener = pNode->CreateReader<ParkingGarage>(slotConfig, std::bind(&ApaControl::update_slot_info, pControl, std::placeholders::_1));

    auto decisionObstacleinfo = nodeCfg.getSubChannel("decision_obstacle_avoid");
    apollo::cyber::ReaderConfig obstacleConfig;
    obstacleConfig.channel_name = decisionObstacleinfo.name;
    obstacleConfig.pending_queue_size = decisionObstacleinfo.pendingQueueSize;
    auto obstacle_listener = pNode->CreateReader<ObstacleAvoid>(obstacleConfig, std::bind(&ApaControl::update_uls_info, pControl, std::placeholders::_1));

    auto hmi_session_id = nodeCfg.getSubChannel("hmi_out_session_id");
    pNode->CreateReader<HMISession>(hmi_session_id.name,std::bind(&ApaControl::update_hmisession_info, pControl, std::placeholders::_1));

    auto dec_ctl_terminate_req = nodeCfg.getSubChannel("control_in_terminate_req");
    pNode->CreateReader<DecCtlTerminateReq>(dec_ctl_terminate_req.name, std::bind(&ApaControl::update_decctl_terminate_req,
            pControl, std::placeholders::_1));

    std::cout << "Init done. Enjoy." << std::endl;
#if (!USE_SINGLE)
    apollo::cyber::WaitForShutdown();
#endif
    return 0;
}
#if USE_SINGLE
}//namespace apa_control_server
#endif

#include <glog/logging.h>
#include <google/protobuf/util/json_util.h>

#include <functional>
#include <memory>
#include <string>

#ifndef CYBER_TIMER_EN
#include "timer_common.h"
#endif
#include "accplanning_maker.hpp"
#include "base/util/app_template.hpp"
#include "base/util/config_parser.hpp"
#include "base/util/module_base.hpp"
#include "modules/common/base/logproto.h"
#include "cyber/cyber.h"
#include "cyber/node/writer.h"
#include "cyber/timer/timer.h"
#include "json/json.hpp"
#include "thirdparty/recommend_protocols/common/proto/exception.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/location/proto/obstacle_in_map.pb.h"
#include "thirdparty/recommend_protocols/location/proto/pose.pb.h"
#include "thirdparty/recommend_protocols/perception/proto/obstacle_drive.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/acc_planning_output.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/pnc_point.pb.h"

#include "thirdparty/recommend_protocols/noa/proto/LaneNet.pb.h"
#include "thirdparty/recommend_protocols/noa/proto/Lane.pb.h"

#if USE_SINGLE
namespace acc_planning_server {
#endif

using apollo::cyber::Node;
using apollo::cyber::Writer;
using rainbowdash::common::ExceptionPtr;
using rainbowdash::control_by_wire::Chassis;
using rainbowdash::location::FusionObstacles;
using rainbowdash::location::LanesInMap;
using rainbowdash::location::Pose;
using rainbowdash::planning::ACCPlanningStatus;
using rainbowdash::planning::ADCTrajectory;
using rainbowdash::planning::HwpDecisionToPlanningTriggerMsg;
using rainbowdash::planning::HwpDecisionToPlanningUpdateMsg;
using rainbowdash::planning::HwpStartPlan;
using rainbowdash::planning::VisualPlanMsg;
using rainbowdash::planning::ADCTrajectorys;

std::shared_ptr<Node> pNode = nullptr;

class ACCPlanningServer
    : public ModuleBase,
      public std::enable_shared_from_this<ACCPlanningServer> {
 private:
  acc_planning::ACCPlanningMaker* pmaker = nullptr;
  std::weak_ptr<ACCPlanningServer> weak_this_;
  double start_time_ = 0.0, end_time_ = 0.0;

 public:
  // writer
  std::shared_ptr<Writer<ADCTrajectory>> p_writer_planning_route_ = nullptr;
  std::shared_ptr<Writer<ACCPlanningStatus>>
      p_writer_debug_out_planning_status_ = nullptr;
  std::shared_ptr<Writer<VisualPlanMsg>> p_writer_visual_plan_msg_ = nullptr;
  std::shared_ptr<Writer<ExceptionPtr>> p_writer_exception_ = nullptr;

  std::shared_ptr<Writer<ADCTrajectorys>> p_writer_planning_lat_lattice_ = nullptr;

  // reader
  std::shared_ptr<apollo::cyber::Reader<Pose>> p_reader_location_pose_;
  std::shared_ptr<apollo::cyber::Reader<Chassis>> p_reader_chassis_;
  std::shared_ptr<apollo::cyber::Reader<LanesInMap>> p_reader_lanes_;
  std::shared_ptr<apollo::cyber::Reader<FusionObstacles>> p_reader_obs_;
  std::shared_ptr<apollo::cyber::Reader<HwpDecisionToPlanningTriggerMsg>>
      p_reader_acc_decision_trigger_;
  std::shared_ptr<apollo::cyber::Reader<HwpDecisionToPlanningUpdateMsg>>
      p_reader_acc_decision_update_;
  std::shared_ptr<apollo::cyber::Reader<ACCPlanningStatus>>
      p_reader_debug_in_planning_status_;

  std::shared_ptr<apollo::cyber::Reader<HwpStartPlan>> p_reader_simulationLC_;

  //Noa Lane and obs
  std::shared_ptr<apollo::cyber::Reader<rainbowdash::noa_planning::LaneNet>> p_reader_NOA_lanes_;

 public:
  ACCPlanningServer() = default;
  virtual ~ACCPlanningServer() = default;

  void init(const std::string& root_path) {
    weak_this_ = shared_from_this();
    pmaker = &acc_planning::ACCPlanningMaker::getInstance();
    pmaker->init(root_path);
    pmaker->setMsgSendCallback(MSGO_TRAJECTORY, [this](void* pmsg) {
      if (weak_this_.lock() != nullptr) {
        planningMsgSend(pmsg);
      } else {
        AINFO << __FUNCTION__
              << " planningMsgSend failed ,weak_this_ is nullptr !";
      }
    });

    pmaker->setMsgSendCallback(MSGO_VISUAL_PLAN_MSG, [this](void* pmsg) {
      if (weak_this_.lock() != nullptr) {
        planningVisualMsgSend(pmsg);
      } else {
        AINFO << __FUNCTION__
              << " planningVisualMsgSend failed, weak_this_ is nullptr!";
      }
    });
    pmaker->setMsgSendCallback(MSGO_PLANNING_STATUS, [this](void* pmsg) {
      if (weak_this_.lock() != nullptr) {
        planningStatusMsgSend(pmsg);
      } else {
        AINFO << __FUNCTION__
              << " planningStatusMsgSend failed, weak_this_ is nullptr!";
      }
    });
    pmaker->setMsgSendCallback(MSGO_EXCEPTION, [this](void* pmsg) {
      if (weak_this_.lock() != nullptr) {
        planningExceptionMsgSend(pmsg);
      } else {
        AINFO << __FUNCTION__
              << " planningExceptionMsgSend failed, weak_this_ is nullptr!";
      }
    });

    pmaker->setMsgSendCallback(MSGO_LAT_LATTICE, [this](void* pmsg) {
      if (weak_this_.lock() != nullptr) {
        planningLatLatticeMsgSend(pmsg);
      } else {
        AINFO << __FUNCTION__
              << " planningLatLatticeSend failed ,weak_this_ is nullptr !";
      }
    });

    std::cout << "ACC_PLANNING_INIT_PATH: " << root_path << std::endl;
    return;
  }

  bool createCyberReader(NodeCfg* const node_cfg) {
    bool b_result{false};
    do {
      AINFO << "Begin to create reader of planning.";
      if (nullptr == pNode) {
        AERROR << " Failed to get pNode.";
        break;
      }
      if (nullptr == node_cfg) {
        AERROR << "Failed to get nodeCfg.";
        break;
      }

      // reader
#ifndef ACCPLANDEBUG
      auto locpose_info = node_cfg->getSubChannel("location_pose");
      auto locpose_callback = [this](const std::shared_ptr<Pose>& msg) {
        if (weak_this_.lock() != nullptr) {
          updateLocInfo(msg);
        }
      };
      if (p_reader_location_pose_ == nullptr) {
        p_reader_location_pose_ =
            pNode->CreateReader<Pose>(locpose_info.name, locpose_callback);
        if (p_reader_location_pose_ == nullptr) {
          AERROR << " Failed to create reader of locpose.";
          break;
        }
      }

      auto chassis_info = node_cfg->getSubChannel("chassis_info");
      auto chassis_callback = [this](const std::shared_ptr<Chassis>& msg) {
        if (weak_this_.lock() != nullptr) {
          updateChassisInfo(msg);
        }
      };
      if (p_reader_chassis_ == nullptr) {
        p_reader_chassis_ =
            pNode->CreateReader<Chassis>(chassis_info.name, chassis_callback);
        if (p_reader_chassis_ == nullptr) {
          AERROR << " Failed to create reader of chassis.";
          break;
        }
      }

      auto simulationLC_callback =
          [this](const std::shared_ptr<HwpStartPlan>& msg) {
            if (weak_this_.lock() != nullptr) {
              updateSimulationLCRequest(*msg);
            }
          };
      if (p_reader_simulationLC_ == nullptr) {
        p_reader_simulationLC_ = pNode->CreateReader<HwpStartPlan>(
            "planning/simulation_joy", simulationLC_callback);
        if (p_reader_simulationLC_ == nullptr) {
          AERROR << " Failed to create reader of simulation LC.";
          break;
        }
      }

      apollo::cyber::ReaderConfig acc_decision_trigger_cfg;
      auto acc_decision_trigger_info =
          node_cfg->getSubChannel("decision_trigger_planning");
      acc_decision_trigger_cfg.channel_name = acc_decision_trigger_info.name;
      acc_decision_trigger_cfg.pending_queue_size =
          acc_decision_trigger_info.pendingQueueSize;
      auto acc_decision_trigger_callback =
          [this](const std::shared_ptr<HwpDecisionToPlanningTriggerMsg>& msg) {
            if (weak_this_.lock() != nullptr) {
              updateDecisionTriggerCallback(msg);
            }
          };
      if (p_reader_acc_decision_trigger_ == nullptr) {
        p_reader_acc_decision_trigger_ =
            pNode->CreateReader<HwpDecisionToPlanningTriggerMsg>(
                acc_decision_trigger_cfg, acc_decision_trigger_callback);
        if (p_reader_acc_decision_trigger_ == nullptr) {
          AERROR << " Failed to create reader of acc_decision_trigger.";
          break;
        }
      }

      apollo::cyber::ReaderConfig acc_decision_update_cfg;
      auto acc_decision_update_info =
          node_cfg->getSubChannel("decision_update_planning");
      acc_decision_update_cfg.channel_name = acc_decision_update_info.name;
      acc_decision_update_cfg.pending_queue_size =
          acc_decision_update_info.pendingQueueSize;
      auto acc_decision_update_callback =
          [this](const std::shared_ptr<HwpDecisionToPlanningUpdateMsg>& msg) {
            if (weak_this_.lock() != nullptr) {
              updateDecisionUpdateCallback(msg);
            }
          };
      if (p_reader_acc_decision_update_ == nullptr) {
        p_reader_acc_decision_update_ =
            pNode->CreateReader<HwpDecisionToPlanningUpdateMsg>(
                acc_decision_update_cfg, acc_decision_update_callback);
        if (p_reader_acc_decision_update_ == nullptr) {
          AERROR << " Failed to create reader of acc_decision_update.";
          break;
        }
      }

      auto lanes_info = node_cfg->getSubChannel("fusion_lanes");
      auto lanes_callback = [this](const std::shared_ptr<LanesInMap>& msg) {
        if (weak_this_.lock() != nullptr) {
          updateLanesInfo(msg);
        }
      };
      if (p_reader_lanes_ == nullptr) {
        p_reader_lanes_ =
            pNode->CreateReader<LanesInMap>(lanes_info.name, lanes_callback);
        if (p_reader_lanes_ == nullptr) {
          AERROR << " Failed to create reader of p_reader_lanes.";
          break;
        }
      }

      auto obs_info = node_cfg->getSubChannel("fusion_obstacles");
      auto obs_callback = [this](const std::shared_ptr<FusionObstacles>& msg) {
        if (weak_this_.lock() != nullptr) {
          updateObstacleInfo(msg);
        }
      };
      if (p_reader_obs_ == nullptr) {
        p_reader_obs_ =
            pNode->CreateReader<FusionObstacles>(obs_info.name, obs_callback);
        // test: simulation obs callback
        if (0) {
          p_reader_obs_ = pNode->CreateReader<FusionObstacles>(
              "planning/simulation_obstacles", obs_callback);
        }

        if (p_reader_obs_ == nullptr) {
          AERROR << " Failed to create reader of p_reader_obs.";
          break;
        }
      }

      auto NOA_lanes_info = node_cfg->getSubChannel("NOA_fusion_lanes");
      auto NOA_lanes_callback = [this](const std::shared_ptr<rainbowdash::noa_planning::LaneNet>& msg) {
        if (weak_this_.lock() != nullptr) {
          updateNOALanesInfo(msg);
        }
      };
      if (p_reader_NOA_lanes_ == nullptr) {
        p_reader_NOA_lanes_ =
            pNode->CreateReader<rainbowdash::noa_planning::LaneNet>(NOA_lanes_info.name, NOA_lanes_callback);
        if (p_reader_NOA_lanes_ == nullptr) {
          AERROR << " Failed to create reader of p_reader_NOA_lanes_.";
          break;
        }
      }
#else
      auto debug_in_planning_status =
          node_cfg->getSubChannel("debug_in_planning_status");
      auto debug_in_planning_status_callback =
          [this](const std::shared_ptr<ACCPlanningStatus>& msg) {
            if (weak_this_.lock() != nullptr) {
              updateDebugPlanningStatus(msg);
            }
          };
      if (p_reader_debug_in_planning_status_ == nullptr) {
        p_reader_debug_in_planning_status_ =
            pNode->CreateReader<ACCPlanningStatus>(
                debug_in_planning_status.name,
                debug_in_planning_status_callback);
        if (p_reader_debug_in_planning_status_ == nullptr) {
          AERROR << " Failed to create reader of "
                    "p_reader_debug_in_planning_status_.";
          break;
        }
      }
#endif

      AINFO << "End Reader of planning creation.";
      b_result = true;
    } while (false);
    return b_result;
  }

  bool createCyberWriter(NodeCfg* const node_cfg) {
    bool b_result{false};
    do {
      AINFO << "Begin to create writer of planning.";
      if (nullptr == pNode) {
        AERROR << "Failed to get pNode.";
        break;
      }
      if (nullptr == node_cfg) {
        AERROR << "Failed to get nodeCfg.";
        break;
      }

      if (nullptr == p_writer_planning_route_) {
        p_writer_planning_route_ = pNode->CreateWriter<ADCTrajectory>(
            node_cfg->getPubChannel("acc_planing_out_trajectory").name);
        if (p_writer_planning_route_ == nullptr) {
          AERROR << "Failed to create writer of PlanningRoute.";
          break;
        }
      }

      if (nullptr == p_writer_planning_lat_lattice_) {
        p_writer_planning_lat_lattice_ = pNode->CreateWriter<ADCTrajectorys>(
            node_cfg->getPubChannel("acc_lat_lattice").name);
        if (p_writer_planning_lat_lattice_ == nullptr) {
          AERROR << "Failed to create writer of Planning Lat lattice.";
          break;
        }
      }

      if (nullptr == p_writer_visual_plan_msg_) {
        p_writer_visual_plan_msg_ = pNode->CreateWriter<VisualPlanMsg>(
            node_cfg->getPubChannel("acc_visual_plan").name);
        if (p_writer_visual_plan_msg_ == nullptr) {
          AERROR << "Failed to create writer of PlanningVisual.";
        }
      }

      if (nullptr == p_writer_exception_) {
        p_writer_exception_ = pNode->CreateWriter<ExceptionPtr>(
            node_cfg->getPubChannel("acc_plan_exception").name);
        if (p_writer_exception_ == nullptr) {
          AERROR << "Failed to create writer of Exception.";
          break;
        }
      }

#ifndef ACCPLANDEBUG
      if (nullptr == p_writer_debug_out_planning_status_) {
        p_writer_debug_out_planning_status_ =
            pNode->CreateWriter<ACCPlanningStatus>(
                node_cfg->getPubChannel("debug_out_planing_status").name);
        if (p_writer_debug_out_planning_status_ == nullptr) {
          AERROR << "Failed to create writer of PlanningStatus.";
          break;
        }
      }
#endif
      b_result = true;
    } while (false);
    AINFO << "End Writer of planning creation.";
    return b_result;
  }

  bool subscribe() {
    bool b_result{false};
    do {
#ifndef ACCPLANDEBUG
      if (p_reader_location_pose_ != nullptr) {
        b_result = p_reader_location_pose_->Init();
        if (!b_result) {
          AERROR << "Failed to init p_reader_location_pose_.";
          break;
        }
      } else {
        AERROR << "Failed to get p_reader_location_pose_.";
        break;
      }

      if (p_reader_chassis_ != nullptr) {
        b_result = p_reader_chassis_->Init();
        if (!b_result) {
          AERROR << "Failed to init p_reader_chassis_.";
          break;
        }
      } else {
        AERROR << "Failed to get p_reader_chassis_.";
        break;
      }

      if (p_reader_lanes_ != nullptr) {
        b_result = p_reader_lanes_->Init();
        if (!b_result) {
          AERROR << "Failed to init p_reader_obstacles_lines_.";
          break;
        }
      } else {
        AERROR << "Failed to get p_reader_obstacles_lines_.";
        break;
      }

      if (p_reader_acc_decision_trigger_ != nullptr) {
        b_result = p_reader_acc_decision_trigger_->Init();
        if (!b_result) {
          AERROR << "Failed to init p_reader_acc_decision_trigger_.";
          break;
        }
      } else {
        AERROR << "Failed to get p_reader_acc_decision_trigger_.";
        break;
      }

      if (p_reader_acc_decision_update_ != nullptr) {
        b_result = p_reader_acc_decision_update_->Init();
        if (!b_result) {
          AERROR << "Failed to init p_reader_acc_decision_update_.";
          break;
        }
      } else {
        AERROR << "Failed to get p_reader_acc_decision_update_.";
        break;
      }

#else
      if (p_reader_debug_in_planning_status_ != nullptr) {
        b_result = p_reader_debug_in_planning_status_->Init();
        if (!b_result) {
          AERROR << "Failed to init p_reader_debug_in_planning_status_.";
          break;
        }
      } else {
        AERROR << "Failed to get p_reader_debug_in_planning_status_.";
        break;
      }
#endif
    } while (false);
    return b_result;
  }

  void unsubscribe() {
#ifndef ACCPLANDEBUG
    if (p_reader_location_pose_ != nullptr) {
      p_reader_location_pose_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_location_pose_.";
    }

    if (p_reader_chassis_ != nullptr) {
      p_reader_chassis_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_chassis_.";
    }

    if (p_reader_lanes_ != nullptr) {
      p_reader_lanes_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_obstacles_lines_.";
    }

    if (p_reader_NOA_lanes_ != nullptr) {
      p_reader_NOA_lanes_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_NOA_lanes_.";
    }

    if (p_reader_acc_decision_trigger_ != nullptr) {
      p_reader_acc_decision_trigger_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_acc_decision_trigger_.";
    }

    if (p_reader_acc_decision_update_ != nullptr) {
      p_reader_acc_decision_update_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_acc_decision_update_.";
    }
#else
    if (p_reader_debug_in_planning_status_ != nullptr) {
      p_reader_debug_in_planning_status_->Shutdown();
    } else {
      AERROR << "Failed to get p_reader_debug_in_planning_status_.";
    }
#endif
    return;
  }

  void start() {
    subscribe();
    return;
  }

  void stop() {
    unsubscribe();
    return;
  }
  // input data
  void updateLocInfo(const std::shared_ptr<Pose>& msg) {
    Pose loc_info = *msg;
    pmaker->setColData(MSGI_LOCATION, &loc_info);
    return;
  }

  void updateChassisInfo(const std::shared_ptr<Chassis>& msg) {
    Chassis chassis_info = *msg;
    pmaker->setColData(MSGI_CHASSIS, &chassis_info);
    return;
  }

  void updateDecisionTriggerCallback(
      const std::shared_ptr<HwpDecisionToPlanningTriggerMsg>& msg) {
    LogProto(__FILE__,__LINE__,__FUNCTION__,msg);

    if (!msg->valid()) {
      AERROR << "DecisionTriggerInfo is invalid";
      return;
    }

    switch (msg->payload_case()) {
      case HwpDecisionToPlanningTriggerMsg::kStartPlan:
        updateDecisionTriggerStartPlan(msg->start_plan());
        break;
      case HwpDecisionToPlanningTriggerMsg::kStopPlan:
        // updateDecisionTriggerStopPlan(msg->stop_plan());
        // break;
      case HwpDecisionToPlanningTriggerMsg::PAYLOAD_NOT_SET:
      default:
        AERROR << __func__ << "msg payload_case is not set.";
        break;
    }

    // pmaker->setColData(MSGI_DECISION_TRIGGER_INFO, msg.get());
  }

  void updateDecisionUpdateCallback(
      const std::shared_ptr<HwpDecisionToPlanningUpdateMsg>& msg) {
    LogProto(__FILE__,__LINE__,__FUNCTION__,msg);

    if (!msg->valid()) {
      AERROR << "DecisionUpdateInfo is invalid";
      return;
    }
    auto msg_driving_mode = msg->driving_mode();
    auto msg_driver_action = msg->driver_action();
    auto msg_driver_setting = msg->driver_setting();
    switch (msg->payload_case()) {
      case HwpDecisionToPlanningUpdateMsg::kDrivingMode:
        pmaker->setColData(MSGI_DECISION_DRIVING_MODE, &msg_driving_mode);
        break;
      case HwpDecisionToPlanningUpdateMsg::kDriverAction:
        pmaker->setColData(MSGI_DECISION_DRIVER_ACTION, &msg_driver_action);
        break;
      case HwpDecisionToPlanningUpdateMsg::kDriverSetting:
        pmaker->setColData(MSGI_DECISION_DRIVER_SETTING, &msg_driver_setting);
        break;
      case HwpDecisionToPlanningUpdateMsg::PAYLOAD_NOT_SET:
      default:
        AERROR << __func__ << "msg payload_case is not set.";
        break;
    }
  }

  void updateDecisionTriggerStartPlan(
      const rainbowdash::planning::HwpStartPlan& msg) {
    auto msg_start = msg.start();
    auto msg_driving_mode = msg.driving_mode();
    auto msg_driver_action = msg.driver_action();
    auto msg_driver_setting = msg.driver_setting();
    pmaker->setColData(MSGI_DECISION_TRIGGER_START_PLAN, &msg_start);
    pmaker->setColData(MSGI_DECISION_DRIVING_MODE, &msg_driving_mode);
    pmaker->setColData(MSGI_DECISION_DRIVER_ACTION, &msg_driver_action);
    pmaker->setColData(MSGI_DECISION_DRIVER_SETTING, &msg_driver_setting);
  }

  void updateSimulationLCRequest(
      const rainbowdash::planning::HwpStartPlan& msg) {
    auto msg_driver_action = msg.driver_action();
    pmaker->setColData(MSGI_SIMULATION_DRIVER_ACTION, &msg_driver_action);
  }

  void updateLanesInfo(const std::shared_ptr<LanesInMap>& msg) {
    std::bitset<7> binary(0000000);  // r1 r0 l1 l0 cr cl c
    acc_planning::MapInfo map_info;
    if (msg.get() == nullptr) {
      AINFO << __FUNCTION__ << " NoLaneInfo.";
      return;
    }
    map_info.lanes_.reserve(3);
    /// @note timestamp
    map_info.timestamp = msg->header().timestamp();

    /// @note 接收车道中心线
    if (msg->has_center_lines()) {
      if (msg->center_lines().has_current_center_line()) {
        map_info.lanes_.emplace_back();
        acc_planning::LaneInfo& center_lane = map_info.lanes_.back();
        if (updateDiscreteLine(msg->center_lines().current_center_line(),
                               center_lane.curve_center_)) {
          binary.set(0, true);
          center_lane.tag_ = std::string("center");
          center_lane.line_type_ = pnc::LineType::CENTER;
          center_lane.left_lane_bound_id_ = "l0";
          center_lane.right_lane_bound_id_ = "r0";
          center_lane.id_ = msg->center_lines().current_center_line().id();
          AINFO << "===center id " << center_lane.id_;
          if (msg->center_lines().has_left_center_line()) {
            center_lane.left_lane_id_ = msg->center_lines().left_center_line().id();
          }
          if (msg->center_lines().has_right_center_line()) {
            center_lane.right_lane_id_ = msg->center_lines().right_center_line().id();
          }
        } else {
          AERROR << __func__ << " ref_line c0 suspend";
          map_info.lanes_.pop_back();
        }
      }
      if (msg->center_lines().has_left_center_line()) {
        map_info.lanes_.emplace_back();
        acc_planning::LaneInfo& center_lane = map_info.lanes_.back();
        if (updateDiscreteLine(msg->center_lines().left_center_line(),
                               center_lane.curve_center_)) {
          binary.set(1, true);
          center_lane.tag_ = std::string("left");
          center_lane.line_type_ = pnc::LineType::LEFT;
          center_lane.left_lane_bound_id_ = "l1";
          center_lane.right_lane_bound_id_ = "l0";
          center_lane.id_ = msg->center_lines().left_center_line().id();
          AINFO << "===left id " << center_lane.id_;
        } else {
          map_info.lanes_.pop_back();
          AERROR << __func__ << " ref_line cl suspend";
        }
      }
      if (msg->center_lines().has_right_center_line()) {
        map_info.lanes_.emplace_back();
        acc_planning::LaneInfo& center_lane = map_info.lanes_.back();
        if (updateDiscreteLine(msg->center_lines().right_center_line(),
                               center_lane.curve_center_)) {
          binary.set(2, true);
          center_lane.tag_ = std::string("right");
          center_lane.line_type_ = pnc::LineType::RIGHT;
          center_lane.left_lane_bound_id_ = "r0";
          center_lane.right_lane_bound_id_ = "r1";
          center_lane.id_ = msg->center_lines().right_center_line().id();
          AINFO << "===right id " << center_lane.id_;
        } else {
          map_info.lanes_.pop_back();
          AERROR << __func__ << " ref_line cr suspend";
        }
      }
    }

    /// @note 接收车道线
    if (msg->has_lane_point_marker()) {
      if (msg->lane_point_marker().has_left_lane_marker()) {
        map_info.lane_bounds_["l0"] = pnc::LaneBound();
        auto& lane = map_info.lane_bounds_["l0"];
        if (updateDiscreteLine(msg->lane_point_marker().left_lane_marker(),
                               lane.curve_)) {
          binary.set(3, true);
          lane.id_ = std::string("l0");
        } else {
          map_info.lane_bounds_.erase("l0");
          AERROR << __func__ << " lane_bound l0 suspend";
        }
      }
      if (msg->lane_point_marker().has_next_left_lane_marker()) {
        map_info.lane_bounds_["l1"] = pnc::LaneBound();
        auto& lane = map_info.lane_bounds_["l1"];
        if (updateDiscreteLine(msg->lane_point_marker().next_left_lane_marker(),
                               lane.curve_)) {
          binary.set(4, true);
          lane.id_ = std::string("l1");
        } else {
          map_info.lane_bounds_.erase("l1");
          AERROR << __func__ << " lane_bound l0 suspend";
        }
      }
      if (msg->lane_point_marker().has_right_lane_marker()) {
        map_info.lane_bounds_["r0"] = pnc::LaneBound();
        auto& lane = map_info.lane_bounds_["r0"];
        lane.id_ = std::string("r0");
        if (updateDiscreteLine(msg->lane_point_marker().right_lane_marker(),
                               lane.curve_)) {
          binary.set(5, true);
          lane.id_ = std::string("r0");
        } else {
          map_info.lane_bounds_.erase("r0");
          AERROR << __func__ << " lane_bound l0 suspend";
        }
      }
      if (msg->lane_point_marker().has_next_right_lane_marker()) {
        map_info.lane_bounds_["r1"] = pnc::LaneBound();
        auto& lane = map_info.lane_bounds_["r1"];
        lane.id_ = std::string("r1");
        if (updateDiscreteLine(
                msg->lane_point_marker().next_right_lane_marker(),
                lane.curve_)) {
          binary.set(6, true);
          lane.id_ = std::string("r1");
        } else {
          map_info.lane_bounds_.erase("r1");
          AERROR << __func__ << " lane_bound l0 suspend";
        }
      }
    }

    map_info.lanes_in_map_ = *msg;
    pmaker->setColData(MSGI_MAP, &map_info);
    AINFO << __func__ << " lane state: " << binary;
    if (msg->has_center_lines()) {
      if (msg->center_lines().has_current_center_line()) {
        std::string log_msg =  std::string(__func__) + " cur_cen_line ";
        LogProto(__FILE__,__LINE__,log_msg,msg->center_lines().current_center_line());
      }
      if (msg->center_lines().has_left_center_line()) {
        std::string log_msg =  std::string(__func__) + " left_center_line ";
        LogProto(__FILE__,__LINE__,log_msg,msg->center_lines().left_center_line());
      }
      if (msg->center_lines().has_right_center_line()) {
        std::string log_msg =  std::string(__func__) + " right_cen_line ";
        LogProto(__FILE__,__LINE__,log_msg,msg->center_lines().right_center_line());
      }
    }
    if (msg->has_lane_point_marker()) {
      if (msg->lane_point_marker().has_left_lane_marker()) {
        std::string log_msg =  std::string(__func__) + " l0_lane ";
        LogProto(__FILE__,__LINE__,log_msg,msg->lane_point_marker().left_lane_marker());
      }
      if (msg->lane_point_marker().has_next_left_lane_marker()) {
        std::string log_msg =  std::string(__func__) + " l1_lane ";
        LogProto(__FILE__,__LINE__,log_msg,msg->lane_point_marker().next_left_lane_marker());
      }
      if (msg->lane_point_marker().has_right_lane_marker()) {
        std::string log_msg =  std::string(__func__) + " r0_lane ";
        LogProto(__FILE__,__LINE__,log_msg,msg->lane_point_marker().right_lane_marker());
      }
      if (msg->lane_point_marker().has_next_right_lane_marker()) {
        std::string log_msg =  std::string(__func__) + " r1_lane ";
        LogProto(__FILE__,__LINE__,log_msg,msg->lane_point_marker().next_left_lane_marker());
      }
    }
    return;
  }

  void updateNOALanesInfo(const std::shared_ptr<rainbowdash::noa_planning::LaneNet>& lane_net_msg) {
    acc_planning::MapInfo map_info;
    if (lane_net_msg.get() == nullptr) {
      AINFO << __FUNCTION__ << " NoLaneInfo.";
      return;
    }    
    
    map_info.lanes_.reserve(lane_net_msg->lanes().size());
    map_info.timestamp = lane_net_msg->header().timestamp();
    for (const auto& lane_msg : lane_net_msg->lanes()) {
      map_info.lanes_.emplace_back();
      acc_planning::LaneInfo& lane = map_info.lanes_.back();
      if (GetLaneRawFromCyberLane(lane_msg, &lane)) {
        AINFO << "======update NOA lane done=======";
      } else {
        map_info.lanes_.pop_back();
      }
    }

    pmaker->setColData(MSGI_MAP, &map_info);
    return;
  }

  bool GetLaneRawFromCyberLane(const rainbowdash::noa_planning::Lane &msg,
                                         acc_planning::LaneInfo *p_lane) {
    p_lane->id_ = msg.id();                     //id

    p_lane->child_id.reserve(msg.child_id().size());
    for (const auto& id : msg.child_id()) {
      p_lane->child_id.emplace_back(id);
    }
    p_lane->father_id.reserve(msg.father_id().size());
    for (const auto& id : msg.father_id()) {
      p_lane->father_id.emplace_back(id);
    }
    p_lane->left_lane_id_ = msg.l_lane_id();
    p_lane->right_lane_id_ = msg.r_lane_id();

    p_lane->l_change_avbl = msg.l_change_avbl();
    p_lane->r_change_avbl = msg.r_change_avbl();
    
    // p_lane->dir = msg.dir();
    // p_lane->behavior = msg.behavior();
    // p_lane->length = msg.length();

    // p_lane->start_point(0) = msg.start_point().x();
    // p_lane->start_point(1) = msg.start_point().y();
    // p_lane->final_point(0) = msg.final_point().x();
    // p_lane->final_point(1) = msg.final_point().y();

    if (updateNOADiscreteLine(msg, p_lane->curve_center_)) {
      AINFO << "update NOA lane points suc";
    }

    return true;
  }

  void updateObstacleInfo(const std::shared_ptr<FusionObstacles>& msg) {
    if (msg.get() == nullptr) {
      AINFO << "ObsNull";
    }
    double time = getTimeS(nullptr);
    AINFO << __FUNCTION__ << " time " << std::fixed << std::setprecision(3)
          << time;
    pnc::ObstaclesInfo obs_pred;
    obs_pred.timestamp = msg->mutable_header()->timestamp();

    obs_pred.em_loc_.pnt_ << 0.0, 0.0, 0.0;

    std::vector<pnc::ObstaclePredInfo>& obs = obs_pred.pred_obses_;
    obs.reserve(msg->obstacle_raws().size());
    for (const auto& tmp_obs : msg->obstacle_raws()) {
      pnc::ObstaclePredInfo s_obs;
      s_obs.obs_info_.timestamp_ = obs_pred.timestamp;
      s_obs.obs_info_.id = std::to_string(tmp_obs.id());
      s_obs.obs_info_.pnt_ << tmp_obs.position().x(), tmp_obs.position().y(),
          tmp_obs.orientation_rad();
      s_obs.obs_info_.velocity_ << tmp_obs.velocity().x(),
          tmp_obs.velocity().y();
      s_obs.obs_info_.accelerate_ << tmp_obs.accelerate().x(),
          tmp_obs.accelerate().y();
      s_obs.obs_info_.length_ = tmp_obs.length();
      s_obs.obs_info_.width_ = tmp_obs.width();

      std::vector<pnc::TrajectoryPoint>& trjs = s_obs.pred_trj_.trj_points;
      trjs.reserve(tmp_obs.trajectory().trajectory_point().size());
      for (const auto& msg_trj : tmp_obs.trajectory().trajectory_point()) {
        pnc::TrajectoryPoint& trj = trjs.emplace_back();
        updateTrj(msg_trj, trj);
      }
      obs.emplace_back(std::move(s_obs));
    }

    obs_pred.obs_in_map_ = *msg;
    pmaker->setColData(MSGI_PREDICTIONOBSTACLES, &obs_pred);
    return;
  }

  // output data
  void planningMsgSend(void* pmsg) {
    if (nullptr == p_writer_planning_route_) {
      AINFO << __FUNCTION__ << "  p_writer_planning_route_ is nullptr.";
      return;
    }
    auto p = (acc_planning::ADCTrajectory*)pmsg;
    ADCTrajectory cmd;
    cmd.mutable_trajectory_point()->Reserve(p->trajectory_point_.size());

    auto header = cmd.mutable_header();
    header->set_timestamp(p->timestamp_sec_);
    header->set_sequence_num(p->trj_id_);
    cmd.set_total_path_length(p->total_path_length_);
    cmd.set_total_path_time(p->total_path_time_);
    cmd.set_is_replan(p->re_plan_);
    cmd.set_plan_type(
        acc_planning::MotionPlanHandle::nuToString(p->plan_type_nu_));

    for (const auto& point : p->trajectory_point_) {
      auto trajectory_point = cmd.add_trajectory_point();
      auto path_point = trajectory_point->mutable_path_point();
      path_point->set_x(point.path_point.pnt_.x());
      path_point->set_y(point.path_point.pnt_.y());
      path_point->set_theta(point.path_point.pnt_.z());
      path_point->set_s(point.path_point.s_);
      path_point->set_kappa(point.path_point.kappa_);
      path_point->set_dkappa(point.path_point.dkappa_);
      trajectory_point->set_v(point.v);
      trajectory_point->set_a(point.a);
      trajectory_point->set_relative_time(point.rel_time_);
    }
    p_writer_planning_route_->Write(cmd);
    return;
  }

  void planningLatLatticeMsgSend(void* pmsg) {
    if (nullptr == p_writer_planning_lat_lattice_) {
      AINFO << __FUNCTION__ << "  p_writer_planning_lat_lattice_ is nullptr.";
      return;
    }
    auto p = (std::vector<acc_planning::ADCTrajectory>*)pmsg;
    auto lattice_trajs = *p;
    rainbowdash::planning::ADCTrajectorys cmd;
    cmd.mutable_trajectory()->Reserve(lattice_trajs.size());

    for (const auto& traj : lattice_trajs) {
      auto lattice_traj = cmd.add_trajectory();
      lattice_traj->mutable_trajectory_point()->Reserve(traj.trajectory_point_.size());
      for (const auto& point : traj.trajectory_point_) {
        auto trajectory_point = lattice_traj->add_trajectory_point();
        auto path_point = trajectory_point->mutable_path_point();
        path_point->set_x(point.path_point.pnt_.x());
        path_point->set_y(point.path_point.pnt_.y());
        path_point->set_theta(point.path_point.pnt_.z());
        path_point->set_s(point.path_point.s_);
        path_point->set_kappa(point.path_point.kappa_);
        path_point->set_dkappa(point.path_point.dkappa_);
        trajectory_point->set_v(point.v);
        trajectory_point->set_a(point.a);
        trajectory_point->set_relative_time(point.rel_time_);
      }
    }
    
    p_writer_planning_lat_lattice_->Write(cmd);
    return;
  }

  void planningVisualMsgSend(void* pmsg) {
    if (nullptr == p_writer_visual_plan_msg_) {
      AINFO << __FUNCTION__ << " p_writer_visual_plan_msg_ is nullptr.";
      return;
    }
    VisualPlanMsg cmd = *(VisualPlanMsg*)(pmsg);
    p_writer_visual_plan_msg_->Write(cmd);
    return;
  }

  void planningStatusMsgSend(void* pmsg) {
    if (nullptr == p_writer_debug_out_planning_status_) {
      AINFO << __FUNCTION__ << "  p_writer_planning_status_ is nullptr.";
      return;
    }
    ACCPlanningStatus cmd = *(ACCPlanningStatus*)(pmsg);
    p_writer_debug_out_planning_status_->Write(cmd);
    return;
  }

  void planningExceptionMsgSend(void* pmsg) {
    if (nullptr == p_writer_exception_) {
      AINFO << __FUNCTION__ << " p_writer_exception_ is nullptr.";
      return;
    }
    auto cmd = std::make_shared<ExceptionPtr>();
    *cmd = *((ExceptionPtr*)(pmsg));
    p_writer_exception_->Write(cmd);
    LogProto(__FILE__,__LINE__,__func__,cmd);
    return;
  }

#ifdef ACCPLANDEBUG
  void updateDebugPlanningStatus(
      const std::shared_ptr<ACCPlanningStatus>& msg) {
    acc_planning::MsgPlanningStatus planning_status_info;
    planning_status_info.time_stamp_ = msg->header().timestamp();
    planning_status_info.scene_ = msg->scene();
    planning_status_info.id_ = msg->id();

    AINFO << "ACC: trajectory_id=" << planning_status_info.id_;

    planning_status_info.stitch_trajectory_.reserve(msg->stitch_trajectory_points().size());
    for (const auto& trj : msg->stitch_trajectory_points()) {
      pnc::TrajectoryPoint& stitch_trj =
          planning_status_info.stitch_trajectory_.emplace_back();
      updateTrj(trj, stitch_trj);
    }
    pmaker->setCalData(MSGDEBUG_PLAN_STATUS, &planning_status_info);

    auto pose = msg->pose();
    pmaker->setColData(MSGI_LOCATION, &pose);
    auto chassis = msg->chassis();
    pmaker->setColData(MSGI_CHASSIS, &chassis);

    // update decision msg
    auto msg_start = msg->start_plan();
    auto msg_driving_mode = msg->driving_mode();
    auto msg_driver_action = msg->driver_action();
    auto msg_driver_setting = msg->driver_setting();
    pmaker->setColData(MSGI_DECISION_TRIGGER_START_PLAN, &msg_start);
    pmaker->setColData(MSGI_DECISION_DRIVING_MODE, &msg_driving_mode);
    pmaker->setColData(MSGI_DECISION_DRIVER_ACTION, &msg_driver_action);
    pmaker->setColData(MSGI_DECISION_DRIVER_SETTING, &msg_driver_setting);

    updateLanesInfo(std::make_shared<LanesInMap>(msg->lanes_in_map()));
    updateObstacleInfo(std::make_shared<FusionObstacles>(msg->obs_in_map()));

    run();
    return;
  }
#endif

  // run
  void run() {
    start_time_ = getTimeS(nullptr);
    AINFO << "ACC: start_time=" << std::fixed << std::setprecision(3)
          << start_time_;
    pmaker->process(start_time_);
    end_time_ = getTimeS(nullptr);
    AINFO << "HWP: time=" << std::fixed << std::setprecision(3)
          << end_time_ - start_time_;
    return;
  }

 protected:
  bool updateDiscreteLine(const rainbowdash::location::LaneLine& in_line,
                          pnc::LineUnit& out_line) {
    const auto& pnts = in_line.pts();
    if (pnts.size() < 3) {
      AERROR << __func__ << "InvalidLineSize.";
      return false;
    }

    out_line.tag_ = pnc::LinePointType::DISCRETE;
    out_line.em_loc_.pnt_ << 0.0, 0.0, 0.0;
    out_line.discrete_curve_.pnts_.reserve(pnts.size());
    for (int i = 0; i < pnts.size(); i++) {
      pnc::PathPoint path_point;
      path_point.pnt_ << pnts[i].x(), pnts[i].y(), 0.0;
      out_line.discrete_curve_.pnts_.emplace_back(std::move(path_point));
    }
    return true;
  }

  bool updateNOADiscreteLine(const rainbowdash::noa_planning::Lane &msg,
                             pnc::LineUnit& out_line) {
    if (msg.points().size() < 3) {
      AERROR << __func__ << "InvalidLineSize.";
      return false;
    }

    out_line.tag_ = pnc::LinePointType::DISCRETE;
    out_line.em_loc_.pnt_ << 0.0, 0.0, 0.0;
    out_line.discrete_curve_.pnts_.reserve(msg.points().size());
    for (int i = 0; i < msg.points().size(); ++i) {
      pnc::PathPoint path_point;
      auto msg_pt = msg.points(i);
      path_point.pnt_ << msg_pt.x(), msg_pt.y(), 0.0;
      out_line.discrete_curve_.pnts_.emplace_back(std::move(path_point));
    }

    return true;
  }

  bool updateContinueLine(const rainbowdash::drivers::LaneMarker& in_line,
                          pnc::LineUnit& out_line) {
    out_line.tag_ = pnc::LinePointType::CONTINUOUS;
    out_line.em_loc_.pnt_ << 0.0, 0.0, 0.0;
    pnc::SplineCurve<4> spline_curve;
    spline_curve.cx_ = std::array<double, 4>{0, 1, 0, 0};
    spline_curve.cy_ = std::array<double, 4>{
        in_line.c0_position(), in_line.c1_heading_angle(),
        in_line.c2_curvature(), in_line.c3_curvature_derivative()};
    spline_curve.start_ = in_line.longitude_start();
    spline_curve.end_ = in_line.longitude_end();
    out_line.continuous_curve_.curves_.emplace_back(spline_curve);
    return true;
  }

  void updateTrj(const rainbowdash::planning::TrajectoryPoint& input,
                 pnc::TrajectoryPoint& output) {
    output.rel_time_ = input.relative_time();
    output.v_.x() = input.vx();
    output.v_.y() = input.vy();
    output.a_.x() = input.ax();
    output.a_.y() = input.ay();
    output.v = input.v();
    output.a = input.a();
    output.path_point.pnt_ << input.path_point().x(), input.path_point().y(),
        input.path_point().theta();
    output.path_point.kappa_ = input.path_point().kappa();
    output.path_point.dkappa_ = input.path_point().dkappa();
    output.path_point.s_ = input.path_point().s();
    return;
  }
};

#ifdef CYBER_TIMER_EN
std::shared_ptr<apollo::cyber::Timer> pTimer = nullptr;
#else
std::unique_ptr<TimerCommon::Timer> pTimer = nullptr;
#endif
std::shared_ptr<ACCPlanningServer> pACCPlanning = nullptr;

extern "C" int start() {
  AINFO << "Planning start!";
  if (pACCPlanning) {
    pACCPlanning->start();
  }
  pTimer->Start();
  return 0;
}

extern "C" int stop() {
  AINFO << "Planning stop!";
  if (pACCPlanning) {
    pACCPlanning->stop();
  }
  pTimer->Stop();
  return 0;
}

#if (USE_SINGLE)
extern "C" int run_acc_planning_server(int argc, char* argv[]) {
#else
int main(int argc, char* argv[]) {
  apollo::cyber::Init(argv[0]);
#endif
  adas::app_template app;
  namespace bpo = boost::program_options;
  // clang-off
  app.add_options()("cyber_path",
                    bpo::value<std::string>()->default_value("config"),
                    "specify CYBER_PATH for adas")(
      "cyber_ip", bpo::value<std::string>()->default_value("***********"),
      "specify CYBER_IP for adas")(
      "cyber_log", bpo::value<std::string>()->default_value("logs"),
      "specify CYBER_LOG for adas")("log",
                                    bpo::value<bool>()->default_value(false),
                                    "debug default tracking")(
      "root_path", bpo::value<std::string>()->default_value("conf/"),
      "root path of config json data")(
      "node_name", bpo::value<std::string>()->default_value(""),
      "acc planning node name")(
      "node_cfg",
      bpo::value<std::string>()->default_value("conf/acc_planning_server.json"),
      "node config json file")(
      "conf", bpo::value<std::string>()->default_value("conf/acc_planning.ini"),
      "template");
  // clang-on
  auto ret = app.run(argc, argv, "conf");
  if (ret != 0) {
    if (ret == 1) {
      AINFO << "show help!" << std::endl;
      return 0;
    }
    AINFO << "command_line or conf_file parse failed !" << std::endl;
    return -1;
  }
  auto&& config = app.configuration();
  auto cyber_path = config["cyber_path"].as<std::string>();
  auto cyber_ip = config["cyber_ip"].as<std::string>();
  auto cyber_log = config["cyber_log"].as<std::string>();
  auto root_path = config["root_path"].as<std::string>();
  auto node_cfg = config["node_cfg"].as<std::string>();
  auto node_name = config["node_name"].as<std::string>();
  bpo::notify(config);
  FLAGS_stderrthreshold = google::WARNING;
  NodeCfg nodeCfg(node_cfg);
  if (!nodeCfg.isValid()) {
    return 1;
  }
  if (node_name.empty()) {
    node_name = nodeCfg.getName();
  }
  if (nullptr == pNode) {
    std::shared_ptr<Node> node(apollo::cyber::CreateNode(node_name));
    pNode = node;
  }
  if (nullptr == pACCPlanning) {
    pACCPlanning = std::make_shared<ACCPlanningServer>();
  }
  if (pACCPlanning != nullptr) {
    AINFO << "Begin to init acc planning.";
    pACCPlanning->init(root_path);
    AINFO << "End of initialization acc planning.";
  }
  if (!pACCPlanning->createCyberWriter(&nodeCfg)) {
    AERROR << "Failed to create cyber writer.";
    return -1;
  }
  if (!pACCPlanning->createCyberReader(&nodeCfg)) {
    AERROR << "Failed to create cyber reader.";
    return -1;
  }
  if (pACCPlanning) {
    pACCPlanning->start();
  }

#ifndef ACCPLANDEBUG
  std::weak_ptr<ACCPlanningServer> weak_planning =
      pACCPlanning->shared_from_this();
  if (nullptr == pTimer) {
    auto accplanning_callback = [weak_planning]() {
      if (weak_planning.lock() != nullptr) {
        weak_planning.lock()->run();
      }
    };
#ifdef CYBER_TIMER_EN
    pTimer = std::make_shared<apollo::cyber::Timer>(
        100, accplanning_callback, false, "acc_planning_server");
#else
    pTimer = std::make_unique<TimerCommon::Timer>(
        100, accplanning_callback, "acc_planning_server");
#endif
    pTimer->Start();
    AINFO << "Planning start!";
  }
#endif

#if (!USE_SINGLE)
  apollo::cyber::WaitForShutdown();
#endif
  return 0;
}
#if USE_SINGLE
}  // namespace apa_control_server
#endif

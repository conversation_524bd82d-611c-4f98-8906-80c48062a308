find_package(Eigen3 REQUIRED)

set(AIS_AEB_DIR "modules/aeb/src")
set(AIS_AEB_SRC
    ${AIS_AEB_DIR}/aeb_component.cpp
    ${AIS_AEB_DIR}/aeb_main.cpp
    ${AIS_AEB_DIR}/aeb_fusion_obstacles_info.cpp
    ${AIS_AEB_DIR}/aeb.cc
    ${AIS_AEB_DIR}/AEBDecisionLogic/AEBDecisionLogic.cpp
    ${AIS_AEB_DIR}/AEBControl/AEBControl.cpp
    modules/common/base/base64.cc
)

if(compile_single)
    set(TARGET_NAME "ais_aeb_server")
else()
    set(TARGET_NAME "aeb_server")
endif()

if(CMAKE_SYSTEM_NAME MATCHES QNX)
    # do nothing
else()
    set(AIS_AEB_SRC ${AIS_AEB_SRC} ${PROTO_CC_FILES})
endif()

if(compile_single)
    add_library(${TARGET_NAME} SHARED ${AIS_AEB_SRC})
else()
    add_executable(${TARGET_NAME} ${AIS_AEB_SRC})
endif()

target_include_directories(${TARGET_NAME} PRIVATE ${BOOST_INCLUDE_DIRS} ${AIS_AEB_DIR} ${EIGEN3_INCLUDE_DIR} modules/common/base)

set(AIS_AEB_LIB protobuf_ glog::glog Boost::program_options atomic fastrtps fastcdr m z gflags pnc_math)
if(CMAKE_SYSTEM_NAME MATCHES QNX)
    set(AIS_AEB_LIB ${AIS_AEB_LIB} ${LIBCYBER_LIBRARY} ${LIBUUID_LIBRARY} timer_common)
else()
    set(AIS_AEB_LIB ${AIS_AEB_LIB} cyber uuid pthread rt)
endif()

target_link_libraries(${TARGET_NAME} ${AIS_AEB_LIB})
target_compile_options(${TARGET_NAME} PRIVATE -std=gnu++1z)

if(CMAKE_SYSTEM_NAME MATCHES QNX)
    if(compile_single)
        message("qnx single")
        if(${BUILD_8255})
            set(CUSTOM_COMMAND_DEPENDS COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx710_depends.py ${PROJECT_BINARY_DIR}/libais_aeb_server.so -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib)
        else()
            set(CUSTOM_COMMAND_DEPENDS COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py ${PROJECT_BINARY_DIR}/libais_aeb_server.so -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.0.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib)
        endif()
        set(CUSTOM_COMMAND_MV COMMAND mv ${PROJECT_BINARY_DIR}/libais_aeb_server.so ${PROJECT_BINARY_DIR}/output/ais_lib)
    else()
        message("qnx excutable")
        if(${BUILD_8255})
            set(CUSTOM_COMMAND_DEPENDS COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx710_depends.py ${PROJECT_BINARY_DIR}/${TARGET_NAME} -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib)
        else()
            set(CUSTOM_COMMAND_DEPENDS COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py ${PROJECT_BINARY_DIR}/${TARGET_NAME} -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.0.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib)
        endif()
        set(CUSTOM_COMMAND_MV COMMAND mv ${PROJECT_BINARY_DIR}/${TARGET_NAME} ${PROJECT_BINARY_DIR}/output/bin)
    endif()
    add_custom_command(
        TARGET ${TARGET_NAME}
        POST_BUILD
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
        ${CUSTOM_COMMAND_DEPENDS}
        ${CUSTOM_COMMAND_MV}
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/aeb/conf/aeb.ini ${PROJECT_BINARY_DIR}/output/conf
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/aeb/conf/aeb_conf.json ${PROJECT_BINARY_DIR}/output/conf
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/aeb_server.json ${PROJECT_BINARY_DIR}/output/conf
        COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/common/config/config/${PRJ_NAME} -o ${PROJECT_BINARY_DIR}/output/conf
    )
else()
    if(compile_single)
        set(CUSTOM_COMMAND_MV COMMAND mv ${PROJECT_BINARY_DIR}/libais_aeb_server.so ${PROJECT_BINARY_DIR}/output/ais_lib)
        set(CUSTOM_COMMAND_DEPENDS COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/output/ais_lib/libais_aeb_server.so -o ${PROJECT_BINARY_DIR}/output/lib)
    else()
        set(CUSTOM_COMMAND_MV COMMAND mv ${PROJECT_BINARY_DIR}/${TARGET_NAME} ${PROJECT_BINARY_DIR}/output/bin)
        set(CUSTOM_COMMAND_DEPENDS COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/output/bin/${TARGET_NAME} -o ${PROJECT_BINARY_DIR}/output/lib)
    endif()
    add_custom_command(
        TARGET ${TARGET_NAME}
        POST_BUILD
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
        ${CUSTOM_COMMAND_MV}
        ${CUSTOM_COMMAND_DEPENDS}
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/aeb/conf/aeb.ini ${PROJECT_BINARY_DIR}/output/conf
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/aeb/conf/aeb_conf.json ${PROJECT_BINARY_DIR}/output/conf
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/aeb_server.json ${PROJECT_BINARY_DIR}/output/conf
        COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/thirdparty/apa_splinemethod_routing/common/config/config/${PRJ_NAME} -o ${PROJECT_BINARY_DIR}/output/conf
    )
    if(${AEB_UNIT_TEST})
        include(${CMAKE_CURRENT_SOURCE_DIR}/modules/aeb/tests/cmake/dep.cmake)
    endif()
endif()

#include "aeb_component.h"
#include "base/util/util_def.h"
#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"

using namespace mega::aeb;
std::shared_ptr<AebComponent> g_aeb_component{nullptr};
std::shared_ptr<apollo::cyber::Node> g_node{nullptr};
#if (USE_SINGLE)
extern "C" int stop() {
    AINFO << "stop aeb server";
    return 0;
}

extern "C" int start(base::util::HScene scene = base::util::HScene::UNDEFINE) {
    AINFO << "start aeb server";
    return 0;
}

extern "C" int eco() {
    AINFO << "aeb server eco";
    return 0;
}

extern "C" int stop_all() {
    AINFO << "aeb server stop_all";
    return 0;
}

extern "C" int run_aeb_server(int argc, char *argv[]) {
#else
int main(int argc, char *argv[]) {
    apollo::cyber::Init(argv[0]);
#endif

    AINFO << "Enter AEB main function...";

    adas::app_template app;
    namespace bpo = boost::program_options;
    // clang-off
    app.add_options()
            ("cyber_path", bpo::value<std::string>()->default_value("config"), "specify CYBER_PATH for adas")
            ("cyber_ip", bpo::value<std::string>()->default_value("***********"), "specify CYBER_IP for adas")
            ("cyber_log", bpo::value<std::string>()->default_value("logs"), "specify CYBER_LOG for adas")
            ("debug_enabled", bpo::value<bool>()->default_value(false), "debug default tracking")
            ("log", bpo::value<bool>()->default_value(false), "debug default tracking")
            ("discard", bpo::value<int>()->default_value(800), "discard limit of request")
            ("timeout", bpo::value<int>()->default_value(20), "timeout of request")
            ("update_prefix", bpo::value<std::string>()->default_value(""), "update prefix")
            ("node_name", bpo::value<std::string>()->default_value("aeb"), "apa tracking node name")
            ("conf", bpo::value<std::string>()->default_value("conf/aeb.ini"), "template")
            ("node_cfg", bpo::value<std::string>()->default_value("conf/aeb_server.json"), "node config json file")
            ("root_path", bpo::value<std::string>()->default_value("conf/aeb_conf.pb.txt"), "root path of config json data");
    // clang-on
    auto ret = app.run(argc, argv, "conf");
    if (ret != 0) {
        if (ret == 1) {
            AERROR << "show help!";
            return 0;
        }
        AERROR << "command_line or conf_file parse failed !";
        return -1;
    }
    auto &&config = app.configuration();
    auto node_cfg = config["node_cfg"].as<std::string>();
    auto node_name = config["node_name"].as<std::string>();
#if USE_CARLA_TEST
    auto cfg_path = "conf/aeb_conf_carla.pb.txt";
    AERROR << "USE_CARLA_TEST: cfg_path=" << cfg_path;
#elif SIMULATION
    auto cfg_path = "conf/aeb_conf_carla.pb.txt";
    AERROR << "SIMULATION: cfg_path=" << cfg_path;
#else
    auto cfg_path = config["root_path"].as<std::string>();
    AINFO << "DEFAULT: cfg_path=" << cfg_path;
#endif
    std::string vehicle_param_path = "conf/vehicle_param.pb.txt";

    bpo::notify(config);
    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        return 1;
    }
    if (nullptr == g_node) {
        std::shared_ptr<apollo::cyber::Node> node(apollo::cyber::CreateNode(nodeCfg.getName()));
        g_node = node;
    }
    if (g_aeb_component == nullptr){
        g_aeb_component = std::make_shared<AebComponent>(g_node, nodeCfg, cfg_path, vehicle_param_path);
        if (g_aeb_component != nullptr) {
            g_aeb_component->init();
        }
    }

    AINFO << "AEB init done. Enjoy.";
#if (!USE_SINGLE)
    apollo::cyber::WaitForShutdown();
#endif
    return 0;
}
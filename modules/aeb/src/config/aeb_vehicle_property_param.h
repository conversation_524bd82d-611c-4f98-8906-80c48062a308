#pragma once

#include "config_param_base.hpp"

namespace mega::aeb {
struct VehiclePropertyParam final : public pnc::ConfigParamBase {
    double length_ = 0;
    double width_ = 0;
    double tire_radius_ = 0.35;

    //轴距
    double front_edge_to_frontCenter_ = 0;
    double back_edge_to_rearCenter_ = 0;
    double wheel_base_ = 0;

    //横向运动属性
    double min_turn_radius_ = 0;

    double plan_min_turn_radius_ = 5.6;
    double door_front_dis_ = 3.0;      //车的位置往前多少是门
    double min_free_space_out_ = 0.6;  //允许留给乘客的最小下车空间

    double rearview_mirror_front_ = 0.7;
    double rearview_mirror_back_ = 0.9;
    double rearview_mirror_size_ = 0.1;

    double lat_safe_dis_ = 0.3;
    double long_safe_dis_ = 0.3;
    double parallel_stall_length_delta_ = 2.6;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(VehiclePropertyParam, length_, width_, front_edge_to_frontCenter_,
                                   back_edge_to_rearCenter_, wheel_base_, min_turn_radius_,
                                   plan_min_turn_radius_,
                                   door_front_dis_, min_free_space_out_, rearview_mirror_front_,
                                   rearview_mirror_back_,
                                   rearview_mirror_size_, lat_safe_dis_, long_safe_dis_,
                                   parallel_stall_length_delta_);

    VehiclePropertyParam() = default;
    ~VehiclePropertyParam() override = default;

    explicit VehiclePropertyParam(const std::string &file_path) { ReadConf(file_path); }

    void ReadConf(const std::string &file_path) override { *this = Init(file_path, "VehicleProperty"); }

    double length() const { return length_; }

    double width() const { return width_; }

    double front_edge_to_frontCenter() const { return front_edge_to_frontCenter_; }

    double back_edge_to_rearCenter() const { return back_edge_to_rearCenter_; }

    double wheel_base() const { return wheel_base_; }

    double min_turn_radius() const { return min_turn_radius_; }

    double plan_min_turn_radius() const { return plan_min_turn_radius_; }
};
}

//
// Created by wq on 25-4-21.
//

#ifndef ADAS_CORE_AEB_CONFIG_HPP
#define ADAS_CORE_AEB_CONFIG_HPP

#include "config_param_base.hpp"

namespace mega::aeb {
struct AEBConf final : public pnc::ConfigParamBase {
    uint16_t time_period{50};                   // The period of the timer, unit is ms.
    double vehicle_width_offset{0.3};           // 自车宽度外扩0.3, unit is m.
    double MinLatentWarningVelocity{18.0};      // 安全距离报警使能的最低车速，单位 m/s
    double MinQuitLatentWarningVelocity{16.67}; // 退出安全距离报警在车速低于此速度时，单位 m/s
    double WiredControlDelay{0.4};              // 线控延迟, 单位:s。默认值是0.4秒。
    double EnableLatentWarningVelocityRel{2.0}; // FCW LatentWarning, 与前车的相对速度的绝对值小于2m/s，单位 m/s
    double EnableLatentWarningHeadWay{0.9};     // FCW LatentWarning, 与前车的车间时距（相对距离/自车速度）小于0.9s, 单位:s
    double MinFCWWorkVelocity{8.333333};        // FCW 最小工作速度，30km/h, 单位：m/s
    double SteeringAngleInhibitionFCW{2.1};     // FCW抑制的转向角角度大于2.1 rad，单位：rad
    double SteeringRotSpdInhibitionFCW{1.75};   // FCW抑制的转向角角速率大于1.75rad/s, 单位：rad/s
    double AccelPedalStInhibitionFCW{0.85};     // FCW抑制的油门踏板开度大于 0.85
    double BrakePercentage{6.0};                // 制动踏板信号置真, 大于6
    double MinEBAWorkVelocity{1.111111};        // EBA启动的最小速度，单位 m/s
    double AccelPedalStInhibitionEBA{0.1};      // EBA抑制，油门开度大于 0.1
    double SteeringAngleEvasiveEBA{6.28};       // 驾驶员闪避转向,转向角角度大于 6.28 rad,中止EBA功能，单位：rad
    double SteeringRotSpdEvasiveEBA{3.32};      // 驾驶员闪避转向,转向角角速率大于 3.32 rad/s,中止EBA功能，单位：rad/s
    double CrossCollisionInterval{0.5};         // 横穿碰撞的时间间隔，0.5s,单位:s
    double DecelerationEBALevel2{4.0};          // EBA Level2 部分制动减速度，4.0m/s2, 单位：m/s2
    double DecelerationEBALevel3{7.0};          // EBA Level3 全力制动减速度，7.0m/s2, 单位：m/s2
    double StaticTTCHigh{2.5};                  // 前车静止，自车30km/h转弯, 高灵敏度，TTC最晚报警时间，单位：s
    double StaticTTCMedium{2.4};                // 前车静止，自车30km/h转弯, 中灵敏度，TTC最晚报警时间，单位：s
    double StaticTTCLow{2.3};                   // 前车静止，自车30km/h转弯, 低灵敏度，TTC最晚报警时间，单位：s
    double DynamicTTCHigh{2.4};                 // 前车同向运动，自车30km/h转弯, 高灵敏度，TTC最晚报警时间，单位：s
    double DynamicTTCMedium{2.3};               // 前车同向运动，自车30km/h转弯, 中灵敏度，TTC最晚报警时间，单位：s
    double DynamicTTCLow{2.2};                  // 前车同向运动，自车30km/h转弯, 低灵敏度，TTC最晚报警时间，单位：s
    uint32_t TTCSampleStep{50};                 // timestep used in the AEB loop. unit is ms.
    bool EnableAEB{false};                      // 是否enable AEB功能. Default value is false.
    bool EnableFCW{false};                      // 是否enable FCW功能. Default value is false.
    int32_t FCWSensitivity{1};                  // 设置AEB灵敏度：低 : 1; 中 : 2; 高 : 3. Default value is 1.
    double TracksCloseTTC{5.0};                 // 制动预填充激活的TTC时间，单位：s。默认值是5秒。
    double TimeToReact{1.2};                    // 司机刹车反应时间, d，单位：s。默认值是1.2秒。
    double NotProcessedDeceleration{2.0};       // 不予处理的减速度。默认值是2.0米/秒2。
    double PartialDeceleration{4.0};            // 部分减速的减速度。默认值是4.0米/秒2。
    double FullForceDeceleration{6.0};          // 全力减速的减速度。默认值是6.0米/秒2。
    double TurnSteeringAngle{1.0472};           // 判断自车在转弯，方向盘转向角度。
    double TurnVelocity{9.0};                   // 判断自车在转弯，速度上限。
    double ChangeLaneSteeringAngle{0.0873};     // 判断自车在变道，方向盘转向角度。5度。
    double SafetyDistance{1.5};                 // 避障的安全距离，单位：m。
    bool UseFusion{false};                      // 是否使用视觉融合。
    bool RadarTransform{false};                 // 毫米波雷达数据是否进行坐标转换。
    double judge_static_v_limit_for_car{1.0};   // 障碍物是车辆时，判断障碍物是否静止的最新速度，默认是1m/s。即速度小于1m/s时，认为障碍物静止。
    double static_obs_dis_min_delta_for_car{0.4};   // 障碍物是车辆时，障碍物帧间位移如果小于这个值，则认为障碍物静止。
    double FCW_driver_decel{4.0};               // 司机刹车的减速度
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(AEBConf, time_period, vehicle_width_offset, MinLatentWarningVelocity,
                                   MinQuitLatentWarningVelocity, WiredControlDelay, EnableLatentWarningVelocityRel,
                                   EnableLatentWarningHeadWay, MinFCWWorkVelocity, SteeringAngleInhibitionFCW,
                                   SteeringRotSpdInhibitionFCW, AccelPedalStInhibitionFCW, BrakePercentage, MinEBAWorkVelocity,
                                   AccelPedalStInhibitionEBA, SteeringAngleEvasiveEBA, SteeringRotSpdEvasiveEBA, CrossCollisionInterval,
                                   DecelerationEBALevel2, DecelerationEBALevel3, StaticTTCHigh, StaticTTCMedium, StaticTTCLow,
                                   DynamicTTCHigh, DynamicTTCMedium, DynamicTTCLow, TTCSampleStep, EnableAEB, EnableFCW,
                                   FCWSensitivity, TracksCloseTTC, TimeToReact, NotProcessedDeceleration, PartialDeceleration,
                                   FullForceDeceleration, TurnSteeringAngle, TurnVelocity, ChangeLaneSteeringAngle, SafetyDistance,
                                   UseFusion, RadarTransform, judge_static_v_limit_for_car, static_obs_dis_min_delta_for_car,
                                   FCW_driver_decel);

    AEBConf() = default;
    ~AEBConf() override = default;
    explicit AEBConf(const std::string &file_path) { ReadConf(file_path); }

    void ReadConf(const std::string &file_path) override {
        *this = Init(file_path, "AEBConf");
    }
};
}

#endif //ADAS_CORE_AEB_CONFIG_HPP

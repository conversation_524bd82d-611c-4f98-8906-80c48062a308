#pragma once

#include <memory>
#include <string>
#include <cstdlib>
#include <cyber/cyber.h>
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/common/proto/exception.pb.h"
#include "thirdparty/recommend_protocols/aeb/proto/aeb.pb.h"
#include "thirdparty/recommend_protocols/interactive/proto/acchmi.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/acc_planning_input.pb.h"
#include "base/util/config_parser.hpp"
#include "base/util/module_base.hpp"
#include "config/aeb_conf.hpp"
#include "config/aeb_vehicle_property_param.h"
#include "aeb.h"
#ifndef CYBER_TIMER_EN
#include "timer_common.h"
#endif //CYBER_TIMER_EN

namespace mega::aeb {
class AebComponent : public ModuleBase, public std::enable_shared_from_this<AebComponent>{
public:
    AebComponent(std::shared_ptr<apollo::cyber::Node> node, NodeCfg node_cfg,
                 std::string file_path, std::string vehicle_param_path);

    virtual ~AebComponent() = default;

    void init();

    void proc();

    AEBConf get_aeb_conf();

    VehiclePropertyParam get_vehicle_param();
protected:
    void exception_pub(uint32_T code, const std::string &name);
    static double get_timestamp(struct timeval * pt);
    void update_hmi_aeb_msg(const std::shared_ptr<rainbowdash::interactive::HMIAEBMsg>& msg);
    void update_hmi_aeb_enabled(const rainbowdash::interactive::AEBEnabled& msg);
    void update_hmi_aeb_fcw_sensitivity(const rainbowdash::interactive::FCWSensitivity& msg);
    void update_hmi_fcw_enabled(const rainbowdash::interactive::FCWEnabled& msg);
    void update_decision_to_planning_msg(const std::shared_ptr<rainbowdash::planning::HwpDecisionToPlanningTriggerMsg> &msg);
private:
#ifdef CYBER_TIMER_EN
    std::shared_ptr<apollo::cyber::Timer> control_proc_timer_ = nullptr;
#else
    std::unique_ptr<TimerCommon::Timer> control_proc_timer_ = nullptr;
#endif
    std::shared_ptr<apollo::cyber::Writer<rainbowdash::common::ExceptionPtr>> writer_exception_ = nullptr;
    std::shared_ptr<apollo::cyber::Writer<rainbowdash::aeb::AEBController>> writer_controller_ = nullptr;
    std::shared_ptr<apollo::cyber::Node> node_ = nullptr;

    std::string file_path_;
    NodeCfg node_cfg_;
    std::string vehicle_param_path_;

    AEBClass aeb_;

    CommonBool abe_enabled_{CommonBool::NONE};              // HMI的AEB使能信号 :启用AEB, 禁用AEB
    FCWSensitivity fcw_sensitivity_{FCWSensitivity::LOW};   // HMI的FCW前碰撞预警三级灵敏度设置：低灵敏度/中灵敏度/高灵敏度
    CommonBool fcw_enabled_{CommonBool::NONE};              // HMI的FCW使能信号 :启用FCW, 禁用FCW
    CommonBool acc_started_{CommonBool::NONE};              // HMI的ACC使能信号

    AEBConf aeb_conf_;
    VehiclePropertyParam vehicle_param_;
};

}



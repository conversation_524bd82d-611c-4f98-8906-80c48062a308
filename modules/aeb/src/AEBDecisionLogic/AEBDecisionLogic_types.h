//
// File: AEBDecisionLogic_types.h
//
// Code generated for Simulink model 'AEBDecisionLogic'.
//
// Model version                  : 7.2
// Simulink Coder version         : 23.2 (R2023b) 01-Aug-2023
// C/C++ source code generated on : Tue Sep 24 15:58:42 2024
//
// Target selection: ert.tlc
// Embedded hardware selection: Intel->x86-64 (Windows64)
// Code generation objectives: Unspecified
// Validation result: Not run
//
#ifndef RTW_HEADER_AEBDecisionLogic_types_h_
#define RTW_HEADER_AEBDecisionLogic_types_h_
#include "aeb_rtwtypes.h"
#include "polygon2d.h"
#include <map>

//typedef int32_T trackLogicType;
//
//// enum trackLogicType
//const trackLogicType trackLogicType_Invalid = 0;// Default value
//const trackLogicType trackLogicType_History = 1;
//const trackLogicType trackLogicType_Score = 2;
//const trackLogicType trackLogicType_Integrated = 3;

//struct BusRadarDetectionsObjectAttributes{
//  real_T TargetIndex{};
//  real_T SNR{};
//};

struct BusMultiObjectTracker1Tracks{
  uint32_t track_id_{};
  double min_x_{};  // 0
  double min_y_{};  // 2
  double rel_v_x_{};    // 1
  double rel_v_y_{};    // 3
  double rel_a_x_{};    // 4
  double rel_a_y_{};    // 5
  Memory_parking::math::Polygon2d target_;  // target 四个点，在自车坐标系下的坐标，逆时针分布
};


struct BusMultiObjectTracker{
  int32_t tracks_num_{};
  std::map<int32_t, BusMultiObjectTracker1Tracks> map_tracks_;   // key is object id; value is BusMultiObjectTracker1Tracks.
  //BusMultiObjectTracker1Tracks Tracks[20];
};

//#ifndef SS_UINT64
//#define SS_UINT64                      21
//#endif
//
//#ifndef SS_INT64
//#define SS_INT64                       22
//#endif

#endif                                 // RTW_HEADER_AEBDecisionLogic_types_h_

//
// File trailer for generated code.
//
// [EOF]
//

#include "AEBDecisionLogic.h"

#include <utility>
#include "aeb_rtwtypes.h"

//#define D_VEHICLE_WIDTH (2.0f)
//#define D_VEHICLE_WIDTH_OFFSET (0.3f)
using namespace mega::aeb;
extern const std::vector<std::pair<double, double>> lat_calibration_table;

// Model step function
// 判断与前车是否有碰撞风险
void AEBDecisionLogicClass::step(const BusMultiObjectTracker &in_tracks, const RelatedSignals &signals, RelatedTracks &tracks) {
    real_T maxX{1000.0};
    real_T D_VEHICLE_WIDTH = vehicle_param_.width_;
    real_T D_VEHICLE_WIDTH_OFFSET = aeb_conf_.vehicle_width_offset;
    tracks.track_nums_ = 0;

    // 判断左转，右转，直行还是变道
    if (((signals.steering_angle_ >= aeb_conf_.TurnSteeringAngle) || (signals.steering_angle_ <= -aeb_conf_.TurnSteeringAngle)) and (signals.velocity_ <= aeb_conf_.TurnVelocity)) {  // 左转，速度30km/h以下，方向盘转角大于60度 or 右转，速度30km/h以下，方向盘转角大于60度
        if (signals.steering_angle_ >= aeb_conf_.TurnSteeringAngle) {
            AINFO << " Turn left. steering angle " << signals.steering_angle_ << " . velocity " << signals.velocity_;
        } else if (signals.steering_angle_ <= -aeb_conf_.TurnSteeringAngle) {
            AINFO << " Turn right. steering angle " << signals.steering_angle_ << " . velocity " << signals.velocity_;
        }

        for (const auto & target : in_tracks.map_tracks_) {
            auto min_x = target.second.min_x_;
            auto min_y = target.second.min_y_;
            // 判断障碍物是否在自车前方, 静止或者慢速车辆
            if ((min_x < maxX) && (min_x > 0.0)) {
                if (target.second.rel_v_x_ < 0) {
                    tracks.tracks_[tracks.track_nums_] = {0, target.first, target.second.rel_v_x_, min_x, TrackInfo::ObsDirection::TURN, target.second.rel_v_y_, min_y, target.second.target_};
                    tracks.track_nums_++;
                    continue;
                }
            }
        }
    } else if ((std::fabs(signals.steering_angle_) >= aeb_conf_.ChangeLaneSteeringAngle) && (signals.velocity_ >= aeb_conf_.TurnVelocity)) {  // 变道, 方向盘转角大于5度,速度大于30km/h, 认为自车在变道
        AINFO << " Change lane. steering angle " << signals.steering_angle_ << " . velocity " << signals.velocity_;
    } else {    // 直行 or 弯道（弯道曲率半径>=125m）
        for (const auto & target : in_tracks.map_tracks_) {
            auto min_x = target.second.min_x_;
            auto min_y = target.second.min_y_;
            // 判断障碍物是否在自车前方
            if ((min_x < maxX) && (min_x > 0.0) &&
                (min_y >= -(D_VEHICLE_WIDTH / 2 + D_VEHICLE_WIDTH_OFFSET)) &&
                (min_y <= (D_VEHICLE_WIDTH / 2 + D_VEHICLE_WIDTH_OFFSET))) {
                if (target.second.rel_v_x_ < 0) {
                    tracks.tracks_[tracks.track_nums_] = {0, target.first, target.second.rel_v_x_, min_x, TrackInfo::ObsDirection::PARALLEL, target.second.rel_v_y_, min_y, target.second.target_};
                    tracks.track_nums_++;
                    continue;
                }
            }
            if ((min_x < maxX) && (min_x > 0.0) && !same_sign(target.second.target_.min_y(), target.second.target_.max_y())) {  // 障碍车横跨自车x轴，认为障碍车在自车前方
                if (target.second.rel_v_x_ < 0) {
                    tracks.tracks_[tracks.track_nums_] = {0, target.first, target.second.rel_v_x_, min_x, TrackInfo::ObsDirection::PARALLEL, target.second.rel_v_y_, min_y, target.second.target_};
                    tracks.track_nums_++;
                    continue;
                }
            }
            // 判断障碍物是否在自车前方, 并且障碍物运动方向是否与自车运动方向垂直
            auto v_obs_x = target.second.rel_v_x_ + signals.velocity_;  // obs velocity x = v_rel_x + v_ego_x;
            auto v_obs_y = target.second.rel_v_y_;  // obs velocity y = v_rel_y + v_ego_y; v_ego_y = 0;
            constexpr auto epsilon{0.5};
            if ((min_x < maxX) && (min_x > 0.0) && (std::fabs(v_obs_x) < epsilon) && (std::fabs(v_obs_y) > epsilon)) {
                if ((std::fabs(min_y) > (D_VEHICLE_WIDTH / 2 + D_VEHICLE_WIDTH_OFFSET) && v_obs_y < 0) ||
                    (min_y < -(D_VEHICLE_WIDTH / 2 + D_VEHICLE_WIDTH_OFFSET) && v_obs_y > 0)) {
                    if (!same_sign(min_y, v_obs_y)) {
                        tracks.tracks_[tracks.track_nums_] = {0, target.first, target.second.rel_v_x_, min_x, TrackInfo::ObsDirection::VERTICAL, target.second.rel_v_y_, min_y, target.second.target_};
                        tracks.track_nums_++;
                        continue;
                    }
                }
            }
            // 判断障碍物是否在自车前方, 并且障碍物运动方向是否切入自车车道
            if ((min_x < maxX) && (min_x > 0.0) && (std::fabs(v_obs_x) > epsilon) && (std::fabs(v_obs_y) > epsilon)) {
                if ((std::fabs(min_y) > (D_VEHICLE_WIDTH / 2 + D_VEHICLE_WIDTH_OFFSET) && v_obs_y < 0) ||
                    (min_y < -(D_VEHICLE_WIDTH / 2 + D_VEHICLE_WIDTH_OFFSET) && v_obs_y > 0)) {
                    if (!same_sign(min_y, v_obs_y)) {
                        tracks.tracks_[tracks.track_nums_] = {0, target.first, target.second.rel_v_x_, min_x, TrackInfo::ObsDirection::OBLIQUE, target.second.rel_v_y_, min_y, target.second.target_};
                        tracks.track_nums_++;
                        continue;
                    }
                }
            }
        }
    }
    AINFO << __func__ << " obs count:" << tracks.track_nums_;
}

bool AEBDecisionLogicClass::same_sign(const double a, const double b) {
    return std::copysign(1, a) == std::copysign(1, b);
}

void AEBDecisionLogicClass::initialize(const VehiclePropertyParam &vehicle_param, const AEBConf &aeb_conf) {
    aeb_conf_ = aeb_conf;
    vehicle_param_ = vehicle_param;
}

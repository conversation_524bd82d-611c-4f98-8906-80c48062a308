#ifndef RTW_HEADER_AEBDecisionLogic_h_
#define RTW_HEADER_AEBDecisionLogic_h_

#include "aeb_rtwtypes.h"
#include "AEBDecisionLogic_types.h"
#include "config/aeb_conf.hpp"
#include "config/aeb_vehicle_property_param.h"
#include "AEBControl/AEBControl_types.h"
#include <cstddef>

struct TrackInfo {
    uint32_t track_type_{};          // 人，两轮车，乘用车
    int32_t track_id_{};             // 对象ID
    double relative_velocity_x_{};   // 相对速度x，对应obs_rel_x
    double relative_distance_x_{};   // 相对距离x, 对应min_x，为正数
    enum class ObsDirection : int32_t { // 自车相对障碍物的位置关系
        PARALLEL = 0,   // 平行，障碍物在自车前方
        VERTICAL,       // 垂直，交叉路口，障碍物与自车行驶方向垂直
        OBLIQUE,        // cutin/cutout, 障碍物切入自车车道/障碍物切出自车车道
        TURN,           // 自车转弯
    };
    ObsDirection obs_direction_{ObsDirection::PARALLEL};    // 自车相对障碍物的位置关系
    double relative_velocity_y_{};   // 相对速度y，对应obs_rel_y，可正/可负
    double relative_distance_y_{};   // 相对距离y, 对应min_y，可正/可负
    Memory_parking::math::Polygon2d target_;    // 目标多边形，顶点逆时针保存
    int8_t rtb_en_{-1};      // 如果是默认值，表示这个Target没有compute TTC。保证每个Target的TTC只被计算一次。
    double TTC_{};
};

struct RelatedTracks {
    uint32_t track_nums_{};
    uint32_t road_type_{};
    int8_t rtb_en_{-1};      // 如果是默认值，表示这个Target没有compute TTC。保证每个Target的TTC只被计算一次。
    double TTC_{};
    double rel_velocity_{};
    TrackInfo tracks_[20]{}; // 目标障碍物
};

namespace mega::aeb{
// Class declaration for model AEBDecisionLogic
class AEBDecisionLogicClass {
// public data and function members
public:
    // Constructor
    AEBDecisionLogicClass() = default;

    //explicit AEBDecisionLogicClass(BusMultiObjectTracker inTracks);

    // Destructor
    virtual ~AEBDecisionLogicClass() = default;

    /**
     * @brief Initializes the AEBDecisionLogicClass instance with vehicle and AEB configuration parameters.
     *
     * This function sets up the internal state of the AEBDecisionLogicClass by assigning the provided
     * vehicle property parameters and AEB configuration. The vehicle property parameters include
     * dimensions, motion characteristics, and safety distances, while the AEB configuration contains
     * parameters specific to the Automatic Emergency Braking system.
     *
     * @param[in] vehicle_param A structure containing vehicle-specific properties such as length, width,
     *                          wheelbase, and safety distances. These parameters define the physical and
     *                          operational characteristics of the vehicle.
     * @param[in] aeb_conf A structure containing configuration parameters for the AEB system. These
     *                     parameters control the behavior and thresholds of the automatic braking logic.
     *
     * @note This function must be called before invoking other methods of the AEBDecisionLogicClass to
     * ensure that the internal state is properly initialized.
     */
    void initialize(const VehiclePropertyParam &vehicle_param, const AEBConf &aeb_conf);
    // model step function
    /**
     * @file AEBDecisionLogicClass.cpp
     * @brief 自动紧急刹车(AEB)决策逻辑类实现文件
     *
     * 该文件包含用于判断前车碰撞风险的核心逻辑实现
     */

    /**
     * @brief 判断与前车是否存在碰撞风险并更新跟踪列表
     *
     * 该函数通过遍历所有跟踪目标，识别出距离本车最近的前方车辆(Lead Car)，
     * 并根据车辆状态更新风险跟踪列表。采用车辆位置坐标和车道宽度进行空间关系判断。
     *
     * @param[in,out] tracks 相关车辆跟踪信息结构体，包含：
     *                 - trackNums: 当前跟踪的车辆数量
     *                 - tracks: 车辆跟踪数组，每个元素包含：
     *                   - flags: 状态标志位
     *                   - trackID: 车辆ID
     *                   - velocity: 车辆速度
     *                   - position: 车辆纵向位置
     *
     * @note 算法流程：
     * 1. 初始化最大距离阈值和跟踪ID
     * 2. 遍历所有跟踪目标：
     *    - 检查车辆是否位于本车前方且处于车道范围内
     *    - 更新最近前车信息
     * 3. 根据识别结果更新MIO状态：
     *    - 存在有效前车时复制其状态
     *    - 无有效前车时设置默认安全状态
     * 4. 将MIO状态添加到跟踪列表
     */
    void step(const BusMultiObjectTracker &in_tracks, const RelatedSignals &signals, RelatedTracks &tracks);
protected:
    /**
     * @brief Determines whether two double-precision floating-point numbers have the same sign.
     *
     * This function evaluates the signs of the two input values by leveraging the `std::copysign` function.
     * It returns true if both numbers have identical signs (both positive or both negative), and false otherwise.
     * The comparison accounts for the sign of zero, treating +0.0 and -0.0 as having different signs.
     *
     * @param[in] a The first double-precision floating-point number to compare.
     * @param[in] b The second double-precision floating-point number to compare.
     *
     * @return True if the signs of `a` and `b` are the same, false otherwise.
     */
    static bool same_sign(double a, double b);
private:
    AEBConf aeb_conf_;
    VehiclePropertyParam vehicle_param_;
};
}

#endif

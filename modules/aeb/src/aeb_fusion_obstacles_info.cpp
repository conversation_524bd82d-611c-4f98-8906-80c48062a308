#include "aeb_fusion_obstacles_info.hpp"
#include "aeb_component.h"
#include "modules/common/base/logproto.h"
#include "math_utils.h"
#include <mutex>
#include <Eigen/Dense>

extern std::shared_ptr<mega::aeb::AebComponent> g_aeb_component;

namespace mega::aeb {
constexpr auto PI{3.14159265358979323846};

FusionObstaclesInfo g_obs_fusion;
RelatedSignals g_signals;
std::mutex g_mutex_fusion_obs;
std::mutex g_mutex_radar_obs;
std::mutex g_mutex_chassis;
std::mutex g_mutex_vehicle_state;
RadarObstacles g_radar_obstacles;
VehicleState g_vehicle_state;

Eigen::Matrix4d g_sensor_trans{
        {0.999033, -0.0127507, -0.0420819, 3.76994},
        {0.0121994, 0.999837, -0.0133332, -0.0401635},
        {0.042245, 0.0128069, 0.999025, -0.626697},
        {0, 0, 0, 1},
};

Eigen::Matrix4d g_sensor_rotate{
        {0.999033, -0.0127507, -0.0420819, 0.0},
        {0.0121994, 0.999837, -0.0133332, 0.0},
        {0.042245, 0.0128069, 0.999025, 0.0},
        {0, 0, 0, 1},
};

void update_radar_detect_obstacles(const RadarPosition radar_position, const std::shared_ptr<rainbowdash::drivers::FareoRadarDetections> &msg) {
    if (msg == nullptr) {
        AERROR << __func__ << "MSG is null.";
        return;
    }
    RadarObstacles radar_obstacles;
    radar_obstacles.obstacles_.reserve(msg->detection_list().size());
    radar_obstacles.measurement_time_ = msg->measurement_time();
    for (const auto &obs : msg->detection_list()) {
        RadarObstacleInfo radar_obs;
        radar_obs.frame_id_ = obs.frame_id();
        radar_obs.object_id_ = obs.object_id();
        radar_obs.position_ = {obs.position().x(), obs.position().y()};
        radar_obs.velocity_ = {obs.velocity().x(), obs.velocity().y()};
        radar_obs.accelerate_ = {obs.accelerate().x(), obs.accelerate().y()};
        radar_obs.length_ = obs.length();
        radar_obs.length_quality_ = obs.length_quality();
        radar_obs.width_ = obs.width();
        radar_obs.width_quality_ = obs.width_quality();
        radar_obs.track_age_ = obs.track_age();
        radar_obs.exist_probability_ = obs.exist_probablity();
        radar_obs.orientation_rad_ = obs.orientation_rad();
        radar_obs.orientation_quality_ = obs.orientation_quality();
        radar_obs.yaw_rate_ = obs.yaw_rate();
        radar_obs.yaw_rate_quality_ = obs.yaw_rate_quality();
        radar_obs.measurement_time_ = obs.measurement_time();
        radar_obs.radar_position_ = radar_position;
        radar_obstacles.obstacles_.emplace_back(radar_obs);
    }
    // 时间戳从小到大排序
    std::sort(radar_obstacles.obstacles_.begin(), radar_obstacles.obstacles_.end(), [](const RadarObstacleInfo &a, const RadarObstacleInfo &b) {return a.measurement_time_ < b.measurement_time_; });
    std::lock_guard<std::mutex> lock(g_mutex_radar_obs);
    g_radar_obstacles = radar_obstacles;
}

void update_new_radar_detect_obstacles(const std::shared_ptr<rainbowdash::drivers::NewFareoRadarDetections> &msg) {
    if (msg == nullptr) {
        AERROR << __func__ << "MSG is null.";
        return;
    }
    static uint64_t count = 0;
    count++;
    if(count % 50 == 0){
        LogProto(__FILE__,__LINE__,__FUNCTION__, msg);
    }
    RelatedSignals signals;
    get_signals(signals);
    RadarObstacles radar_obstacles;
    radar_obstacles.obstacles_.reserve(msg->detection_list().size());
    radar_obstacles.measurement_time_ = msg->measurement_time();
    for (const auto &obs : msg->detection_list()) {
        if (static_cast<RadarPosition>(obs.radar_name()) == RadarPosition::FC) {
            RadarObstacleInfo radar_obs;
            radar_obs.frame_id_ = obs.frame_id();
            radar_obs.object_id_ = obs.object_id();
            radar_obs.position_ = {obs.position().x(), obs.position().y()};
            radar_obs.velocity_ = {obs.velocity()*std::cos(obs.orientation_rad())-signals.velocity_, obs.velocity()*std::sin(obs.orientation_rad())};                     // NewFareoRadarDetections中的velocity是世界速度
            radar_obs.accelerate_ = {obs.accelerate()*std::cos(obs.orientation_rad())-signals.acc_x_, obs.accelerate()*std::sin(obs.orientation_rad())-signals.acc_y_};   // NewFareoRadarDetections中的accelerate是世界加速度
            radar_obs.length_ = obs.length();
            radar_obs.length_quality_ = obs.length_covariance();
            radar_obs.width_ = obs.width();
            radar_obs.width_quality_ = obs.width_covariance();
            radar_obs.track_age_ = obs.track_age();
            radar_obs.exist_probability_ = obs.exist_probablity();
            radar_obs.orientation_rad_ = obs.orientation_rad();
            radar_obs.orientation_quality_ = obs.orientation_covariance();
            radar_obs.yaw_rate_ = obs.yaw_rate();
            radar_obs.yaw_rate_quality_ = obs.yaw_rate_covariance();
            radar_obs.measurement_time_ = obs.measurement_time();
            radar_obs.radar_position_ = static_cast<RadarPosition>(obs.radar_name());
            radar_obstacles.obstacles_.emplace_back(radar_obs);
        }
    }
    // 时间戳从小到大排序
    std::sort(radar_obstacles.obstacles_.begin(), radar_obstacles.obstacles_.end(), [](const RadarObstacleInfo &a, const RadarObstacleInfo &b) {return a.measurement_time_ < b.measurement_time_; });
    std::lock_guard<std::mutex> lock(g_mutex_radar_obs);
    g_radar_obstacles = radar_obstacles;
}

void update_vehicle_state(const std::shared_ptr<rainbowdash::location::Pose> &msg) {
    if (msg == nullptr) {
        AERROR << __func__ << "MSG is null.";
        return;
    }

    static uint64_t count = 0;
    count++;
    if(count % 50 == 0){
        LogProto(__FILE__, __LINE__, __FUNCTION__, msg);
    }

    std::unique_lock<std::mutex> lock(g_mutex_vehicle_state);
    g_vehicle_state.heading_ = msg->heading();
    g_vehicle_state.position_[0] = msg->position().x();
    g_vehicle_state.position_[1] = msg->position().y();
    g_vehicle_state.position_[2] = msg->position().z();
}

void update_fusion_obstacles(const std::shared_ptr<rainbowdash::location::FusionObstacles> &msg) {
    if (msg == nullptr) {
        AERROR << __func__ << "MSG is null.";
        return;
    }

    RelatedSignals signals;
    get_signals(signals);

    VehicleState vehicle_state;
    get_vehicle_state(vehicle_state);

    // update obstacles_handle
    FusionObstaclesInfo obs_fusion;
    obs_fusion.fusion_obstacles_.reserve(msg->obstacle_raws().size());
    obs_fusion.timestamp_ = msg->header().timestamp();
    for (const auto &obs: msg->obstacle_raws()) {
        bool is_static{false};
        if (obs.trajectory().trajectory_point().empty()) {
            is_static = true;       // 没有预测轨迹，认为是静止的
        } else if (std::hypot(obs.velocity().x(), obs.velocity().y()) < g_aeb_component->get_aeb_conf().judge_static_v_limit_for_car) {
            is_static = true;       // 障碍物的世界速度小于某一个阈值，认为是静止的
        } else if (auto last_obs = g_obs_fusion.fusion_obstacles_.find(obs.id()); last_obs != g_obs_fusion.fusion_obstacles_.end()) {     // found
            auto delta_s = std::hypot(last_obs->second.position_world_coord_[0] - obs.position().x(), last_obs->second.position_world_coord_[1] - obs.position().y());
            if (delta_s < g_aeb_component->get_aeb_conf().static_obs_dis_min_delta_for_car) {
                is_static = true;   // 障碍物帧间位移小于某一个阈值，认为是静止的
            }
        }
        auto x_r = (obs.position().x() - vehicle_state.position_[0]) * cos(vehicle_state.heading_) +
                (obs.position().y() - vehicle_state.position_[1]) * sin(vehicle_state.heading_);
        auto y_r = -sin(vehicle_state.heading_) * (obs.position().x() - vehicle_state.position_[0]) +
                cos(vehicle_state.heading_) * (obs.position().y() - vehicle_state.position_[1]);
        FusionObstacleInfo fusion_obs{};
        fusion_obs.position_ = {x_r, y_r, 0};
        if (is_static) {    // 障碍物静止
            fusion_obs.velocity_ = {-signals.velocity_, 0, 0};
            fusion_obs.accelerate_ = {-signals.acc_x_, -signals.acc_y_, 0};
        } else {
            fusion_obs.velocity_ = {obs.velocity().x()-signals.velocity_, obs.velocity().y(), obs.velocity().z()};
            fusion_obs.accelerate_ = {obs.accelerate().x()-signals.acc_x_, obs.accelerate().y()-signals.acc_y_, obs.accelerate().z()};
        }
        fusion_obs.position_world_coord_ = {obs.position().x(), obs.position().y(), 0};  // 世界坐标系的位置
        fusion_obs.length_ = obs.length();
        fusion_obs.width_ = obs.width();
        fusion_obs.height_ = obs.height();
        fusion_obs.theta_ = NormalizeAngle(obs.orientation_rad()-vehicle_state.heading_);
        fusion_obs.id_ = obs.id();
        fusion_obs.type_ = static_cast<mega::aeb::FusionObstacleType>(obs.type());
        obs_fusion.fusion_obstacles_.emplace(obs.id(), fusion_obs);
        //AINFO << obs.position().x() << " " << obs.position().y() << " " << obs.position().z() << "  velocity=" << obs.velocity().x() << " " << obs.velocity().y() << " " << obs.velocity().z();
    }

    std::unique_lock<std::mutex> lock(g_mutex_fusion_obs);
    g_obs_fusion = obs_fusion;
}

void update_long_chassis_info(const std::shared_ptr<rainbowdash::control_by_wire::Chassis> &msg) {
    if (msg == nullptr) {
        AERROR << __func__ << "MSG is null.";
        return;
    }

    static uint64_t count = 0;
    count++;
    if(count % 50 == 0){
        LogProto(__FILE__,__LINE__,__FUNCTION__, msg);
    }

    std::unique_lock<std::mutex> lock(g_mutex_chassis);
    g_signals.velocity_ = msg->vehicle_speed().vehicle_speed();                             // unit is m/s.
    g_signals.steering_angle_ = msg->steering_system().steering_wheel_angle() * PI / 180;   // unit is rad. 左正右负
    g_signals.pre_brake_status_ = msg->brake_percentage() > g_aeb_component->get_aeb_conf().BrakePercentage;
    g_signals.accel_pedal_st_ = msg->throttle_perceptage() / 100;
    g_signals.steering_rot_spd_ = msg->steering_system().steering_wheel_rate() * PI / 180;  // unit is rad/s.
    g_signals.acc_x_ = msg->acceleration().longitudinal();
    g_signals.acc_y_ = msg->acceleration().lateral();
}

void get_fusion_obs(FusionObstaclesInfo &obs_fusion) {
    std::unique_lock<std::mutex> lock(g_mutex_fusion_obs);
    obs_fusion = g_obs_fusion;
}

void get_radar_obs(RadarObstacles &radar_obstacles) {
    std::unique_lock<std::mutex> lock(g_mutex_radar_obs);
    radar_obstacles = g_radar_obstacles;
}

void get_signals(RelatedSignals &signals) {
    std::unique_lock<std::mutex> lock(g_mutex_chassis);
    signals = g_signals;
}

void sensor_to_host_trans(const Vec2d& sensor, Vec2d &sensor_to_host) {
    Eigen::Vector4d source(sensor.x(), sensor.y(), 0, 1);
    auto dest = g_sensor_trans * source;
    sensor_to_host.set_x(dest[0]);
    sensor_to_host.set_y(dest[1]);
}

void sensor_to_host_rotate(const Vec2d& sensor, Vec2d &sensor_to_host) {
    Eigen::Vector4d source(sensor.x(), sensor.y(), 0, 1);
    auto dest = g_sensor_rotate * source;
    sensor_to_host.set_x(dest[0]);
    sensor_to_host.set_y(dest[1]);
}

void get_vehicle_state(VehicleState &vehicle_state) {
    std::unique_lock<std::mutex> lock(g_mutex_vehicle_state);
    vehicle_state = g_vehicle_state;
}
} // namespace acc_planning

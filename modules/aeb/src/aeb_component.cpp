#include "aeb_component.h"
#include "thirdparty/recommend_protocols/drivers/proto/radar.pb.h"
#include "config/aeb_vehicle_property_param.h"
#include "config/aeb_conf.hpp"
#include "modules/common/base/logproto.h"
#include <sys/time.h>
#include <utility>

// 方向盘转角和前轮转角的对应关系
std::vector<std::pair<double, double>> lat_calibration_table{{-460.74, -27.73}, {-400.021, -24.9346}, {-299.38, -18.464}, {-199.363, -11.6462}, {-100.066, -5.65623},
                                                             {100.623, 6.01519}, {200.145, 11.9209}, {299.529, 17.1562}, {401.0, 25.5566}, {500.682, 33.7488}};

namespace mega::aeb {
AebComponent::AebComponent(std::shared_ptr<apollo::cyber::Node> node,
                           NodeCfg node_cfg, std::string file_path, std::string vehicle_param_path) :
        node_(std::move(node)), file_path_(std::move(file_path)), node_cfg_(std::move(node_cfg)),
        vehicle_param_path_(std::move(vehicle_param_path)) {

}

void AebComponent::init() {
    AINFO << __func__ << "Conf file: " << file_path_ << " is loaded.";
    std::string aeb_cfg_path = file_path_ + "/aeb_conf.json";
    aeb_conf_ = AEBConf(aeb_cfg_path);
    abe_enabled_ = aeb_conf_.EnableAEB ? CommonBool::TRUE : CommonBool::FALSE;
    fcw_enabled_ = aeb_conf_.EnableFCW ? CommonBool::TRUE : CommonBool::FALSE;
    fcw_sensitivity_ = static_cast<FCWSensitivity>(aeb_conf_.FCWSensitivity);
    std::string ego_file_path = file_path_ + "/vehicle_property_conf.json";
    vehicle_param_ = VehiclePropertyParam(ego_file_path);

    aeb_.init(vehicle_param_, aeb_conf_);

    std::weak_ptr<AebComponent> self = shared_from_this();

    auto aeb_timer_callback = [self](){
        auto aeb_component = self.lock();
        if (aeb_component != nullptr){
            aeb_component->proc();
        }
    };

    if (control_proc_timer_ == nullptr) {
#ifdef CYBER_TIMER_EN
        control_proc_timer_ = std::make_shared<apollo::cyber::Timer>(aeb_conf_.time_period, aeb_timer_callback, false, "aeb_control_proc");
#else
        control_proc_timer_ = std::make_unique<TimerCommon::Timer>(aeb_conf_.time_period, aeb_timer_callback, "aeb_control_proc");
#endif
        if (control_proc_timer_ != nullptr){
            control_proc_timer_->Start();
        }
    }

    // create cyber reader
    auto vehicle_state = node_cfg_.getSubChannel("location_vehicleinfo");    // vehicle state: position and heading
    auto reader_vehicle_state = node_->CreateReader<rainbowdash::location::Pose>(
            vehicle_state.name, update_vehicle_state);

    auto fusion_obstacles = node_cfg_.getSubChannel("fusion_obstacles");    // 定位融合感知和毫米波雷达后的障碍物
    auto reader_fusion_obstacles = node_->CreateReader<rainbowdash::location::FusionObstacles>(
            fusion_obstacles.name, update_fusion_obstacles);

    // fc radar
    auto radar_obstacle = node_cfg_.getSubChannel("new_valeo_radar_detect_fc");
    auto reader_radar_obstacles = node_->CreateReader<rainbowdash::drivers::NewFareoRadarDetections>(
            radar_obstacle.name, update_new_radar_detect_obstacles);

    auto chassis = node_cfg_.getSubChannel("io_out_chassis");
    auto reader_long_chassis = node_->CreateReader<rainbowdash::control_by_wire::Chassis>(
            chassis.name, update_long_chassis_info);

    auto hmi_aeb = node_cfg_.getSubChannel("hmi_aeb");
    apollo::cyber::ReaderConfig hmi_aeb_cfg;
    hmi_aeb_cfg.channel_name = hmi_aeb.name;
    hmi_aeb_cfg.pending_queue_size = hmi_aeb.pendingQueueSize;
    auto hmi_aeb_msg_callback = [self](const std::shared_ptr<rainbowdash::interactive::HMIAEBMsg> &msg){
        auto aeb_component = self.lock();
        if (aeb_component != nullptr){
            aeb_component->update_hmi_aeb_msg(msg);
        }
    };
    auto reader_hmi_aeb = node_->CreateReader<rainbowdash::interactive::HMIAEBMsg>(
            hmi_aeb_cfg, hmi_aeb_msg_callback);

    auto decision_to_planning_trigger = node_cfg_.getSubChannel("decision_to_planning_trigger");
    apollo::cyber::ReaderConfig decision_to_planning_trigger_cfg;
    decision_to_planning_trigger_cfg.channel_name = decision_to_planning_trigger.name;
    decision_to_planning_trigger_cfg.pending_queue_size = decision_to_planning_trigger.pendingQueueSize;
    auto decision_to_planning_trigger_callback = [self](const std::shared_ptr<rainbowdash::planning::HwpDecisionToPlanningTriggerMsg> &msg){
        auto aeb_component = self.lock();
        if (aeb_component != nullptr){
            aeb_component->update_decision_to_planning_msg(msg);
        }
    };
    auto reader_decision_to_planning = node_->CreateReader<rainbowdash::planning::HwpDecisionToPlanningTriggerMsg>(
            decision_to_planning_trigger_cfg, decision_to_planning_trigger_callback);

    // create cyber writer
    if (writer_exception_ == nullptr) {
        auto exception_cfg = node_cfg_.getPubChannel("channel_exception");
        writer_exception_ = node_->CreateWriter<rainbowdash::common::ExceptionPtr>(exception_cfg.name);
    }

    if (writer_controller_ == nullptr) {
        auto AEBController_cfg = node_cfg_.getPubChannel("aeb_aebcontroller");
        writer_controller_ = node_->CreateWriter<rainbowdash::aeb::AEBController>(AEBController_cfg.name);
    }
}

void AebComponent::exception_pub(const uint32_T code, const std::string &name) {
    rainbowdash::common::ExceptionPtr cmd;
    cmd.set_timestamp(get_timestamp(nullptr));
    cmd.set_code(code);
    cmd.set_name(name);
    writer_exception_->Write(cmd);

    AINFO << " exception_pub: " << __FUNCTION__ << "code: " << code << ". name: " << name;
}

void AebComponent::proc() {
    if ((abe_enabled_ == CommonBool::TRUE || fcw_enabled_ == CommonBool::TRUE) && acc_started_ != CommonBool::TRUE) { // Determine whether aeb is enabled
        AEBCtrl ctrl_out{};
        if (aeb_conf_.UseFusion) {
            AINFO << __func__ << " use fusion.";
            aeb_.tick_fusion_obs(fcw_sensitivity_, ctrl_out);
        } else {
            AINFO << __func__ << " do not use fusion.";
            aeb_.tick_radar_obs(fcw_sensitivity_, ctrl_out);
        }
        AINFO << ": AEBCtrl :BrakePrefillStatus" << (int) ctrl_out.brake_prefill_status_ << " :HBALevel="
              << (int) ctrl_out.hba_level_ << ":LatentWarningActive="
              << (int) ctrl_out.latent_warning_active_ << ":FCWStatus=" << (int) ctrl_out.fwc_status_
              << ":EBAStatus=" << (int) ctrl_out.eba_status_ << ":deceleration="
              << ctrl_out.deceleration_;
        if (fcw_enabled_ == CommonBool::TRUE) {
            if (ctrl_out.eba_status_ == 3) {
                exception_pub(0x0C010005, "AEB_ACUTE_WARNING_LEVEL4");
            } else if (ctrl_out.eba_status_ == 2) {
                exception_pub(0x0C010004, "AEB_ACUTE_WARNING_LEVEL3");
            } else if (ctrl_out.eba_status_ == 1) {
                exception_pub(0x0C010003, "AEB_ACUTE_WARNING_LEVEL2");
            } else if (ctrl_out.fwc_status_ >= 1) {
                exception_pub(0x0C010002, "AEB_PRE_WARNING");
            } else if (ctrl_out.latent_warning_active_ == 1) {
                exception_pub(0x0C010001, "AEB_LATENT_WARNING");
            }
        }
        if (abe_enabled_ == CommonBool::TRUE) {
            // fixme : Send only when needed
            auto cmd = std::make_shared<rainbowdash::aeb::AEBController>();
            cmd->set_timestamp(get_timestamp(nullptr));
            cmd->set_brakeprefillstatus(ctrl_out.brake_prefill_status_);
            cmd->set_hbalevel(ctrl_out.hba_level_);
            cmd->set_fcwlevel(ctrl_out.fwc_status_);
            cmd->set_ebastatus(ctrl_out.eba_status_);
            cmd->set_deceleration(0.0f - ctrl_out.deceleration_);
            writer_controller_->Write(cmd);
            static uint64_t count = 0;
            count++;
            if(count % 50 == 0){
                LogProto(__FILE__,__LINE__,__FUNCTION__, cmd);
            }
        }
    } else {
        static uint64_t count = 0;
        count++;
        if(count % 50 == 0){
            if (abe_enabled_ != CommonBool::TRUE) {
                AINFO << "Not enable AEB.";
            }
            if (fcw_enabled_ != CommonBool::TRUE) {
                AINFO << "Not enable FCW.";
            }
            if (acc_started_ == CommonBool::TRUE) {
                AINFO << "Start ACC.";
            }
        }
    }
}

double AebComponent::get_timestamp(struct timeval *pt) {
    double N = 1000.0;
    struct timeval tv{};
    if (pt == nullptr) {
        gettimeofday(&tv, nullptr); // get current time
    } else {
        tv = *pt;
    }
    double milliseconds = tv.tv_sec * N + tv.tv_usec / N; // calculate milliseconds
    return milliseconds / 1000;
}

void AebComponent::update_hmi_aeb_msg(const std::shared_ptr<rainbowdash::interactive::HMIAEBMsg>& msg) {
    if (msg == nullptr) {
        // 处理空指针情况，例如记录日志、抛出异常或返回错误码
        AERROR << "msg is null";
        return;
    }
    LogProto(__FILE__,__LINE__,__FUNCTION__,msg);

    switch (msg->payload_case()) {
        case rainbowdash::interactive::HMIAEBMsg::kAebEnabled :
            update_hmi_aeb_enabled(msg->aeb_enabled());
            break;
        case rainbowdash::interactive::HMIAEBMsg::kFcwSensitivity :
            update_hmi_aeb_fcw_sensitivity(msg->fcw_sensitivity());
            break;
        case rainbowdash::interactive::HMIAEBMsg::kFcwEnabled :
            update_hmi_fcw_enabled(msg->fcw_enabled());
            break;
        case rainbowdash::interactive::HMIAEBMsg::PAYLOAD_NOT_SET :
        default :
            AERROR << __func__ << " : msg payload_case is not set.";
            break;
    }
}

void AebComponent::update_hmi_aeb_enabled(const rainbowdash::interactive::AEBEnabled& msg) {
    abe_enabled_ = static_cast<CommonBool>(msg.aeb_enabled());
}

void AebComponent::update_hmi_aeb_fcw_sensitivity(const rainbowdash::interactive::FCWSensitivity& msg) {
    fcw_sensitivity_ = static_cast<FCWSensitivity>(msg.fcw_sensitivity());
}

void AebComponent::update_decision_to_planning_msg(const std::shared_ptr<rainbowdash::planning::HwpDecisionToPlanningTriggerMsg> &msg) {
    if (msg == nullptr) {
        // 处理空指针情况，例如记录日志、抛出异常或返回错误码
        AERROR << "msg is null";
        return;
    }
    LogProto(__FILE__,__LINE__,__FUNCTION__,msg);
    switch (msg->payload_case()) {
        case rainbowdash::planning::HwpDecisionToPlanningTriggerMsg::kStartPlan :
            acc_started_ = static_cast<CommonBool>(msg->start_plan().start());
            break;
        case rainbowdash::planning::HwpDecisionToPlanningTriggerMsg::kStopPlan :
            AINFO << __func__ << " : do nothing.";
            break;
        case rainbowdash::planning::HwpDecisionToPlanningTriggerMsg::PAYLOAD_NOT_SET :
        default :
            AERROR << __func__ << " : msg payload_case is not set.";
            break;
    }
}

AEBConf AebComponent::get_aeb_conf() {
    return aeb_conf_;
}

VehiclePropertyParam AebComponent::get_vehicle_param() {
    return vehicle_param_;
}

void AebComponent::update_hmi_fcw_enabled(const rainbowdash::interactive::FCWEnabled &msg) {
    fcw_enabled_ = static_cast<CommonBool>(msg.fcw_enabled());
}

}


#pragma once

#include <iostream>
#include <vector>
#include <memory>
#include "thirdparty/recommend_protocols/perception/proto/obstacle_drive.pb.h"
#include "thirdparty/recommend_protocols/location/proto/obstacle_in_map.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/radar.pb.h"
#include "AEBControl/AEBControl.h"
#include "vec2d.h"

namespace mega::aeb {
enum class RadarPosition : int32_t {
    FC = 0, // 前中
    FL,     // 前左
    FR,     // 前右
    RL,     // 后左
    RR,     // 后右
};

struct RadarObstacleInfo {
    int32_t frame_id_{};
    int32_t object_id_{};

    Vec2d position_{};
    Vec2d velocity_{};
    Vec2d accelerate_{};

    float length_{};
    float length_quality_{};

    float width_{};
    float width_quality_{};

    int32_t track_age_{};
    float exist_probability_{};

    float orientation_rad_{};
    float orientation_quality_{};

    float yaw_rate_{};
    float yaw_rate_quality_{};

    //repeated float covariance = 16;

    double measurement_time_{};

    RadarPosition radar_position_{RadarPosition::FC};
};

struct RadarObstacles{
    double measurement_time_ = 0.0;
    std::vector<RadarObstacleInfo> obstacles_;
};

struct VehicleState {
    double heading_{};  // unit is rad.
    std::array<double, 3> position_{};
};

enum class FusionObstacleType : int32_t {
    UNKNOWN = 0,
    UNKNOWN_MOVABLE = 1,
    UNKNOWN_UNMOVABLE = 2,
    CAR = 3,
    VAN = 4,
    TRUCK = 5,
    BUS = 6,
    CYCLIST = 7,
    MOTORCYCLIST = 8,
    TRICYCLIST = 9,
    PEDESTRIAN = 10,
    TRAFFICCONE = 11,
};

struct FusionObstacleInfo {
    std::array<double, 3> position_{};              // 相对自车后轴中心的位置
    std::array<double, 3> velocity_{};              // 相对自车的速度
    std::array<double, 3> accelerate_{};            // 相对自车的加速度
    std::array<double, 3> position_world_coord_{};  // 世界坐标系的位置
    double length_ = 0;  // obstacle length.
    double width_ = 0;   // obstacle width.
    double height_ = 0;  // obstacle height.
    double theta_ = 0;   // 相对自车heading的角度
    int32_t id_ = 0;
    FusionObstacleType type_ = FusionObstacleType::UNKNOWN;
};

struct FusionObstaclesInfo {
    double timestamp_ = 0.0;
    std::unordered_map<int32_t, FusionObstacleInfo> fusion_obstacles_;
    int32_t road_type_{};
};

void get_signals(RelatedSignals &signals);

void get_fusion_obs(FusionObstaclesInfo &obs_fusion);

void get_radar_obs(RadarObstacles &radar_obstacles);

void update_fusion_obstacles(const std::shared_ptr<rainbowdash::location::FusionObstacles> &msg);

/**
 * @brief Updates the radar detection data for obstacles and storing the updated information.
 *
 * This function processes a shared pointer to a FareoRadarDetections message. It extracts obstacle details,
 * and updates a global variable with the new obstacle data.
 *
 * @param radar_position [in] The position of the radar (e.g., front center, front left) as an enum value.
 * @param msg [in] A shared pointer to a FareoRadarDetections object containing the raw radar detection data.
 */
void update_radar_detect_obstacles(RadarPosition radar_position, const std::shared_ptr<rainbowdash::drivers::FareoRadarDetections> &msg);

void update_new_radar_detect_obstacles(const std::shared_ptr<rainbowdash::drivers::NewFareoRadarDetections> &msg);

void update_long_chassis_info(const std::shared_ptr<rainbowdash::control_by_wire::Chassis> &msg);

void update_vehicle_state(const std::shared_ptr<rainbowdash::location::Pose> &msg);

/**
 * @brief Transforms a 2D sensor coordinate to the host coordinate system.
 *
 * This function takes a 2D vector representing a point in the sensor's
 * coordinate system and transforms it into the host's coordinate system using
 * a predefined transformation matrix. The transformed coordinates are stored
 * in the provided reference to a `Vec2d` object.
 *
 * @param sensor [in] A `Vec2d` object representing the point in the sensor's
 *               coordinate system.
 * @param sensor_to_host [out] A reference to a `Vec2d` object where the
 *                       transformed coordinates will be stored.
 */
void sensor_to_host_trans(const Vec2d& sensor, Vec2d &sensor_to_host);

void sensor_to_host_rotate(const Vec2d& sensor, Vec2d &sensor_to_host);

void get_vehicle_state(VehicleState &vehicle_state);
}  // namespace acc_planning

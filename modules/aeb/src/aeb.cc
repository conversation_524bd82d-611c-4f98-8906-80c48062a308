#include "aeb.h"
#include "polygon2d.h"
#include <cmath>

namespace mega::aeb {
// 计算矩形四个顶点的坐标
void AEBClass::calculate_fusion_tracks(const FusionObstacleInfo &obs, BusMultiObjectTracker &out_tracks) const {
    // 旋转矩阵的cos和sin
    double cos_theta = cos(obs.theta_);
    double sin_theta = sin(obs.theta_);

    // 未旋转时，矩形的四个顶点相对于中心点的坐标（矩形长沿x轴，宽沿y轴）, 逆时针
    std::array<std::array<double, 2>, 4> local_vertices = {{
       {obs.length_ / 2, obs.width_ / 2},    // 顶点1
       {-obs.length_ / 2, obs.width_ / 2},   // 顶点2
       {-obs.length_ / 2, -obs.width_ / 2},  // 顶点3
       {obs.length_ / 2, -obs.width_ / 2}    // 顶点4
    }};

    double min_x = obs.position_[0];
    double min_y = obs.position_[1];

    // 传感器Sensor的全局位姿
    double x_s{};
    double y_s{};
    // 传感器Sensor相对于车身的位姿
    double x_r{};
    double y_r{};

    std::vector<Vec2d> points_target;
    points_target.reserve(4);
    // 计算旋转后的顶点坐标
    for (int i = 0; i < 4; ++i) {
        x_r = local_vertices[i][0];
        y_r = local_vertices[i][1];

        // 旋转后的x, y坐标（x为竖轴，y为横轴）
        x_s = x_r * cos_theta - y_r * sin_theta + obs.position_[0]; // 新的x坐标（竖轴）
        y_s = x_r * sin_theta + y_r * cos_theta + obs.position_[1]; // 新的y坐标（横轴）

        if (std::fabs(x_s) < std::fabs(min_x)) {
            min_x = x_s;
        }
        if (std::fabs(y_s) < std::fabs(min_y)) {
            min_y = y_s;
        }
        points_target.emplace_back(x_s, y_s);
    }

    BusMultiObjectTracker1Tracks track{};
    track.track_id_ = obs.id_;
    track.min_x_ = min_x - (vehicle_param_.length_ - vehicle_param_.back_edge_to_rearCenter_);    // min_x是相对车辆后轴中心。
    track.min_y_ = min_y;
    track.rel_v_x_ = obs.velocity_[0];
    track.rel_v_y_ = obs.velocity_[1];
    track.rel_a_y_ = obs.accelerate_[1];
    track.rel_a_x_ = obs.accelerate_[0];

    Memory_parking::math::Polygon2d target(points_target);
    track.target_ = target;

    out_tracks.map_tracks_[obs.id_] = track;
    out_tracks.tracks_num_ = out_tracks.map_tracks_.size();

    AINFO << __func__ << ":index=" << obs.id_ << " :x=" << obs.position_[0] << ":y="
          << obs.position_[1] << ":theta=" << obs.theta_ << "State:" << min_x << " " << min_y << " "
          << obs.velocity_[0] << " " << obs.velocity_[1] << " " << obs.accelerate_[0]
          << " " << obs.accelerate_[1];
}

// 计算矩形四个顶点的坐标
void AEBClass::calculate_radar_tracks(const RadarObstacleInfo &obs, BusMultiObjectTracker &out_tracks) const {
    // 雷达系旋转矩阵的cos和sin
    auto cos_theta = cos(obs.orientation_rad_);
    auto sin_theta = sin(obs.orientation_rad_);

    // 未旋转时，矩形的四个顶点相对于中心点的坐标（矩形长沿x轴，宽沿y轴）, 逆时针
    std::array<std::array<double, 2>, 4> local_vertices = {{
       {obs.length_ / 2, obs.width_ / 2},    // 顶点1
       {-obs.length_ / 2, obs.width_ / 2},   // 顶点2
       {-obs.length_ / 2, -obs.width_ / 2},  // 顶点3
       {obs.length_ / 2, -obs.width_ / 2}    // 顶点4
   }};

    double tmp_x = 0;
    double tmp_y = 0;
    // 将localVertices转换到雷达坐标系
    for (int i = 0; i < 4; ++i) {
        double x = local_vertices[i][0];
        double y = local_vertices[i][1];

        // 旋转后的x, y坐标（x为竖轴，y为横轴）
        tmp_x = x * cos_theta - y * sin_theta + obs.position_.x(); // 雷达坐标系的x坐标（竖轴）
        tmp_y = x * sin_theta + y * cos_theta + obs.position_.y(); // 雷达坐标系的y坐标（横轴）
        local_vertices[i][0] = tmp_x;
        local_vertices[i][1] = tmp_y;
    }

    // 计算旋转到自车坐标系后的顶点坐标，将localVertices从雷达坐标系转换到自车坐标系
    std::vector<Vec2d> points_target;
    points_target.reserve(4);
    Vec2d sensor_to_host_position{obs.position_.x(), obs.position_.y()};
    Vec2d sensor_to_host_velocity{obs.velocity_.x(), obs.velocity_.y()};
    Vec2d sensor_to_host_accelerate{obs.accelerate_.x(), obs.accelerate_.y()};
    if (aeb_conf_.RadarTransform) {
        sensor_to_host_trans({obs.position_.x(), obs.position_.y()}, sensor_to_host_position);
        sensor_to_host_rotate({obs.velocity_.x(), obs.velocity_.y()}, sensor_to_host_velocity);
        sensor_to_host_rotate({obs.accelerate_.x(), obs.accelerate_.y()}, sensor_to_host_accelerate);
    }

    double min_x = sensor_to_host_position.x();
    double min_y = sensor_to_host_position.y();
    for (int i = 0; i < 4; ++i) {
        // 乘以外参矩阵
        Vec2d sensor_to_host{local_vertices[i][0], local_vertices[i][1]};
        if (aeb_conf_.RadarTransform) {
            sensor_to_host_trans({local_vertices[i][0], local_vertices[i][1]}, sensor_to_host);
        }
        double x = sensor_to_host.x();
        double y = sensor_to_host.y();
        if (std::fabs(x) < std::fabs(min_x)) {
            min_x = x;
        }
        if (std::fabs(y) < std::fabs(min_y)) {
            min_y = y;
        }
        points_target.emplace_back(x, y);
    }

    BusMultiObjectTracker1Tracks track{};
    track.track_id_ = obs.object_id_;
    track.min_x_ = min_x - (vehicle_param_.length_ - vehicle_param_.back_edge_to_rearCenter_);    // min_x是相对车辆后轴中心。
    track.min_y_ = min_y;
    track.rel_v_x_ = sensor_to_host_velocity.x();
    track.rel_v_y_ = sensor_to_host_velocity.y();
    track.rel_a_y_ = sensor_to_host_accelerate.y();
    track.rel_a_x_ = sensor_to_host_accelerate.x();

    Memory_parking::math::Polygon2d target(points_target);
    track.target_ = target;

    out_tracks.map_tracks_[obs.object_id_] = track;
    out_tracks.tracks_num_ = out_tracks.map_tracks_.size();

    AINFO << __func__ << ":index=" << obs.object_id_ << " :x=" << sensor_to_host_position.x() << " :y="
          << sensor_to_host_position.y() << " :theta=" << obs.orientation_rad_ << " State:" << min_x << " " << min_y << " "
          << sensor_to_host_velocity.x() << " " << sensor_to_host_velocity.y() << " " << sensor_to_host_accelerate.x()
          << " " << sensor_to_host_accelerate.y();
}

void AEBClass::tick_fusion_obs(const FCWSensitivity fcw_sensitivity, AEBCtrl &aeb_out) {
    // 获取底盘信号，自车的速度和加速度等信息
    AEBControl::TracksAndSignals in_aebctrl{};
    get_signals(in_aebctrl.related_signals_i_);

    if (in_aebctrl.related_signals_i_.velocity_ > 0.3) {
        // 0. 挑选障碍物
        FusionObstaclesInfo fusion_obs;
        get_fusion_obs(fusion_obs);

        BusMultiObjectTracker in_tracks{};
        in_tracks.tracks_num_ = 0;
        for (const auto &obs: fusion_obs.fusion_obstacles_) {
            calculate_fusion_tracks(obs.second, in_tracks);
            if (in_tracks.tracks_num_ >= 20) {
                break;
            }
        }

        // 1. 挑选自车道上的障碍物
        //auto decisionPtr = std::make_unique<AEBDecisionLogicClass>(in_tracks);
        aeb_decision_.step(in_tracks, in_aebctrl.related_signals_i_, in_aebctrl.related_tracks_e_);

        aeb_control_.step(fcw_sensitivity, in_aebctrl, aeb_out);

        AINFO << __func__ << ":velocity=" << in_aebctrl.related_signals_i_.velocity_ << ":SteeringAngle="
              << in_aebctrl.related_signals_i_.steering_angle_ << ":PreBrakeStatus="
              << static_cast<uint32_t>(in_aebctrl.related_signals_i_.pre_brake_status_) << ":AccelPedalSt="
              << in_aebctrl.related_signals_i_.accel_pedal_st_ << ":SteeringRotSpd="
              << in_aebctrl.related_signals_i_.steering_rot_spd_;
    } else {
        AINFO << __func__ << ": velocity=" << in_aebctrl.related_signals_i_.velocity_ << " : Stand still.";
    }
}

void AEBClass::tick_radar_obs(const FCWSensitivity fcw_sensitivity, AEBCtrl &aeb_out) {
    // 获取底盘信号，自车的速度和加速度等信息
    AEBControl::TracksAndSignals in_aebctrl{};
    get_signals(in_aebctrl.related_signals_i_);

    if (in_aebctrl.related_signals_i_.velocity_ > 0.3) {
        // 0. 挑选障碍物
        RadarObstacles radar_obs;
        get_radar_obs(radar_obs);

        BusMultiObjectTracker in_tracks{};
        in_tracks.tracks_num_ = 0;
        for (const auto &obs: radar_obs.obstacles_) {
            calculate_radar_tracks(obs, in_tracks);
            if (in_tracks.tracks_num_ >= 20) {
                break;
            }
        }

        // 1. 挑选自车道上的障碍物
        //auto decisionPtr = std::make_unique<AEBDecisionLogicClass>(in_tracks);
        aeb_decision_.step(in_tracks, in_aebctrl.related_signals_i_, in_aebctrl.related_tracks_e_);

        // 2. 对自车道上的障碍物做风险评估
        aeb_control_.step(fcw_sensitivity, in_aebctrl, aeb_out);

        AINFO << __func__ << ":velocity=" << in_aebctrl.related_signals_i_.velocity_ << ":SteeringAngle="
              << in_aebctrl.related_signals_i_.steering_angle_ << ":PreBrakeStatus="
              << static_cast<uint32_t>(in_aebctrl.related_signals_i_.pre_brake_status_) << ":AccelPedalSt="
              << in_aebctrl.related_signals_i_.accel_pedal_st_ << ":SteeringRotSpd="
              << in_aebctrl.related_signals_i_.steering_rot_spd_;
    } else {
        AINFO << __func__ << ": velocity=" << in_aebctrl.related_signals_i_.velocity_ << " : Stand still.";
    }
}

void AEBClass::init(const VehiclePropertyParam &vehicle_param, const AEBConf &aeb_conf) {
    vehicle_param_ = vehicle_param;
    aeb_conf_ = aeb_conf;
    aeb_control_.initialize(vehicle_param, aeb_conf);
    aeb_decision_.initialize(vehicle_param, aeb_conf);
}
}

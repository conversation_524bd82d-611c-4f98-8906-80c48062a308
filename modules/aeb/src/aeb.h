#pragma once

#include "AEBDecisionLogic/AEBDecisionLogic.h"
#include "AEBControl/AEBControl.h"
#include "aeb_fusion_obstacles_info.hpp"
#include <memory>

namespace mega::aeb {
class AEBClass {
public:
    AEBClass() = default;

    virtual ~AEBClass() = default;

    /**
     * @brief 初始化AEBClass实例的核心参数和内部状态
     *
     * 该函数用于设置AEBClass实例的车辆属性参数和AEB配置参数，并初始化内部的控制和决策模块。
     * 具体操作包括：
     * - 将输入的车辆属性参数和AEB配置参数存储到类成员变量中
     * - 调用AEB控制模块的initialize方法，传递车辆属性和AEB配置以完成初始化
     * - 调用AEB决策逻辑模块的initialize方法，传递车辆属性和AEB配置以完成初始化
     *
     * @param vehicle_param 输入参数，表示车辆的属性参数，包括但不限于车速、加速度、转向角等
     * @param aeb_conf 输入参数，表示AEB系统的配置参数，包括但不限于碰撞预警阈值、制动策略等
     *
     */
    void init(const VehiclePropertyParam& vehicle_param, const AEBConf &aeb_conf);

    /**
     * @brief 障碍物融合处理主循环函数 - 更新AEB控制输出
     *
     * 该函数执行以下核心操作：
     * 1. 获取车辆底盘信号（速度、加速度等）
     * 2. 当车速超过阈值时：
     *    a. 获取融合障碍物数据
     *    b. 计算障碍物跟踪数据（最多20个）
     *    c. 执行AEB决策逻辑
     *    d. 更新AEB控制系统状态
     *    e. 输出最终控制结果
     *
     * @param fcw_sensitivity 输入参数，表示前向碰撞预警的灵敏度级别
     * @param aeb_out 输出参数，用于存储更新后的AEB控制输出数据
     */
    void tick_fusion_obs(FCWSensitivity fcw_sensitivity, AEBCtrl &aeb_out);
    /**
     * @brief 雷达障碍物处理主循环函数 - 更新AEB控制输出
     *
     * 该函数执行以下核心操作：
     * 1. 获取车辆底盘信号（速度、加速度等）
     * 2. 当车速超过阈值时：
     *    a. 重置计数器并获取雷达障碍物数据
     *    b. 计算障碍物跟踪数据（最多20个）
     *    c. 执行AEB决策逻辑
     *    d. 更新AEB控制系统状态
     *    e. 输出最终控制结果
     *
     * @param fcw_sensitivity 输入参数，灵敏度。UI设置
     * @param aeb_out 输出参数，用于存储更新后的AEB控制输出数据
     *
     * @return void
     *
     * @details
     * 完整处理流程：
     * - 信号采集阶段：通过get_signals()获取车辆状态信号（速度、转向角、预刹车状态等）
     * - 速度阈值检查：仅当车速>0.3m/s时执行后续处理（防止低速误触发）
     * - 障碍物跟踪计算：
     *   - 调用get_radar_obs()获取原始雷达障碍物数据
     *   - 遍历障碍物列表，调用calculate_radar_tracks()计算跟踪数据
     *   - 限制最大跟踪数量（20个）防止内存溢出
     * - 决策逻辑执行：
     *   - 创建AEB决策逻辑对象
     *   - 执行step()进行碰撞风险评估和决策计算
     * - 控制输出更新：
     *   - 设置AEB控制系统输入
     *   - 执行控制系统step()更新状态
     *   - 通过getExternalOutputs()获取最终控制输出
     *
     * @note 重要参数说明：
     * - 速度阈值0.3m/s：根据系统需求可调整，用于平衡响应速度和误报率
     * - 最大跟踪数20：需与下游处理模块容量匹配，防止缓冲区溢出
     */
    void tick_radar_obs(FCWSensitivity fcw_sensitivity, AEBCtrl &aeb_out);
protected:
    void calculate_fusion_tracks(const FusionObstacleInfo &obs, BusMultiObjectTracker &out_tracks) const;
    /**
     * @brief 计算毫米波雷达障碍物四个顶点距离自车的最小的x和y坐标，并记录障碍物的速度和加速度。
     *
     * 该函数通过旋转矩阵对障碍物矩形顶点进行坐标变换，障碍物坐标首先转换到雷达坐标系，然后在转换到自车坐标系，计算障碍物边界框的最小坐标值，
     * 并生成跟踪对象数据存入输出结构体。最终通过引用参数更新跟踪列表。
     *
     * @param obs 输入雷达障碍物信息，包含位置、朝向、尺寸、速度、加速度等参数
     * @param out_tracks 输出跟踪数据结构，用于存储计算后的跟踪对象
     *
     * @return void
     *
     * @details
     * 算法流程：
     * 1. 计算障碍物朝向的三角函数值（cosθ, sinθ）
     * 2. 定义障碍物局部坐标系下的矩形顶点坐标（以障碍物中心为原点）
     * 3. 通过坐标旋转公式将局部坐标转换为全局坐标：
     *    x y 乘以外参矩阵，再进行坐标转换
     *    - x' = x*cosθ - y*sinθ + 中心x
     *    - y' = x*sinθ + y*cosθ + 中心y
     * 4. 在坐标变换过程中跟踪最小x坐标/y坐标
     * 5. 创建跟踪对象并填充状态数据：
     *    - State[0]: 最小x坐标（竖轴）
     *    - State[1]: x方向速度
     *    - State[2]: 最小y坐标（横轴）
     *    - State[3]: y方向速度
     *    - State[4]: x方向加速度
     *    - State[5]: y方向加速度
     * 6. 将跟踪对象添加到输出列表并更新计数器
     *
     */
    void calculate_radar_tracks(const RadarObstacleInfo &obs, BusMultiObjectTracker &out_tracks) const;
private:
    AEBControl aeb_control_{};
    AEBDecisionLogicClass aeb_decision_{};
    VehiclePropertyParam vehicle_param_{};
    mega::aeb::AEBConf aeb_conf_;
};
}

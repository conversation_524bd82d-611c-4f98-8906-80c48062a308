#include "AEBControl.h"
#include "AEBControl_types.h"
#include "aeb_rtwtypes.h"
#include <cmath>
#include "math_utils.h"

// Named constants for Chart: '<S2>/Chart'
const real32_T AEBCont_MinBrakePrefillVelocity{1.11111116F};

const uint8_T AEBControl_IN_BrakePrefill{1U};

const uint8_T AEBControl_IN_IDLE{2U};

const uint8_T AEBControl_IN_QuitWaiting_AEB{3U};

// Named constants for Chart: '<S3>/Chart'
//const real_T AEBCo_InhibitionEBAAccelPedalSt{2.0};

const real_T AEBCo_MinBrakePrefillVelocity_k{1.1111111111111112};

//const real_T AEBCon_InhibitionSteeringRotSpd{1.75};

//const real_T AEBCont_InhibitionSteeringAngle{2.1};

const uint8_T AEBControl_IN_EBAWorking{1U};

//const real_T AEBControl_QuitSteeringAngle{6.28};

//const real_T AEBControl_QuitSteeringRotSpd{3.32};

// Named constants for Chart: '<S4>/Chart'
//const real_T AEBContr_InhibitionAccelPedalSt{2.0};

const uint8_T AEBControl_IN_AcuteWarning{1U};

const uint8_T AEBControl_IN_FCWPreWarning{2U};

const uint8_T AEBControl_IN_IDLE_FCW{3U};

const uint8_T AEBControl_IN_QuitWaiting_FWC{4U};

//const real_T AEBControl_minFCWWorkVelocity{8.3333333333333339};

namespace nlohmann {
    template<> struct adl_serializer<Memory_parking::math::Polygon2d>{
        static void to_json(json& j, const Memory_parking::math::Polygon2d& polygon){
            for (const auto &point : polygon.points()) {
                json json_point;
                json_point["x"] = point.x();
                json_point["y"] = point.y();
                j.emplace_back(json_point);
            }
        }

        static void from_json(const json &j, Memory_parking::math::Polygon2d& polygon) {
            std::vector<Vec2d> points;
            points.reserve(4);
            for (const auto &json_point : j) {
                Vec2d point;
                point.set_x(json_point["x"]);
                point.set_y(json_point["y"]);
                points.emplace_back(point);
            }
            polygon = Memory_parking::math::Polygon2d(points);
        }
    };
}

using namespace mega::aeb;
extern const std::vector<std::pair<double, double>> lat_calibration_table;

void AEBControl::AEBControl_CheckTracksClose(const RelatedTracks *tracks, uint8_T *close_result) const {
    *close_result = 0U;
    auto b = static_cast<int32_T>(tracks->track_nums_);
    for (int32_T i{0}; i < b; i++) {
        real_T rel_velocity = tracks->tracks_[i].relative_velocity_x_;
        if ((rel_velocity < 0.0) && (tracks->tracks_[i].relative_distance_x_ / rel_velocity > -aeb_conf_.TracksCloseTTC)) {
            *close_result = 1U;
        }
    }
}

void AEBControl::AEBControl_CalEBALevel_Init(uint8_T *eba_level, real_T *min_velocity) {
    *eba_level = 0U;
    *min_velocity = 0.0;
    block_states_.eba_level_ = 0U;
}

void AEBControl::AEBControl_CalEBALevel(const FCWSensitivity fcw_sensitivity, const RelatedSignals *signals, RelatedTracks *tracks, uint8_T *eba_level, real_T *min_velocity) {
    double max_a{};
    uint8_t rtb_en{};

    json_predict_ = nlohmann::json::object();   // clear JSON object
    for (uint32_t i{0}; i < tracks->track_nums_; i++) {
        double cur_TTC{};
        if (tracks->tracks_[i].relative_velocity_x_ < 0.0) {   // 相对速度小于0,才会走入下面的分支
            // 障碍物在自车前方 or 交叉路口，障碍物与自车行驶方向垂直 or cutin/cutout, 障碍物切入自车车道/障碍物切出自车车道 or 自车转弯
            if (tracks->tracks_[i].rtb_en_ == -1) {
                auto result = collision_check(fcw_sensitivity, tracks->tracks_[i], *signals);
                tracks->tracks_[i].TTC_ = -result.second;
                if (!result.first) { // 没有碰撞风险
                    tracks->tracks_[i].rtb_en_ = 0;
                    continue;
                } else {    // 有碰撞风险
                    tracks->tracks_[i].rtb_en_ = 1;
                    cur_TTC = -result.second;   // cur_TTC是负数
                }
            } else if (tracks->tracks_[i].rtb_en_ == 1) {   // 有碰撞风险
                cur_TTC = tracks->tracks_[i].TTC_;
            } else {    // 没有碰撞风险
                continue;
            }
        }

        auto rel_v = tracks->tracks_[i].relative_velocity_x_;
        if (rel_v < -1.0) {
            double rel_dis = tracks->tracks_[i].relative_distance_x_;    //  相对距离，正数
            double cur_a{};
            double dis_tmp{};
            double t_a{};
            t_a = signals->velocity_ * signals->velocity_ / 2.0F / static_cast<real32_T>(rel_dis);
            if (t_a > 10.0F) {
                t_a = 10.0F;
            } else if (t_a < 0.1) {
                t_a = 0.1F;
            }

            auto obs_v = signals->velocity_ + static_cast<real32_T>(rel_v);  // 障碍物速度
            dis_tmp = obs_v * obs_v / (2.0 * t_a);
            if (obs_v < 0.0F) { // 对向来车
                dis_tmp = 0.0F - dis_tmp;
            }

            auto delta_dis = (-cur_TTC) * signals->velocity_ - aeb_conf_.SafetyDistance;    // 预留安全距离1.5m
            if (delta_dis < 0 || std::fabs(delta_dis) < 0.000001) { // 距离很近
                cur_a = 7;  // 全力制动
            } else if (obs_v < 0.0F) {  // 对向来车，还有段距离，通过TTC判断是否刹车
                double min_ttc{};
                if (fcw_sensitivity == FCWSensitivity::HIGH) {
                    min_ttc = aeb_conf_.DynamicTTCHigh;
                } else if (fcw_sensitivity == FCWSensitivity::MEDIUM) {
                    min_ttc = aeb_conf_.DynamicTTCMedium;
                } else if (fcw_sensitivity == FCWSensitivity::LOW) {
                    min_ttc = aeb_conf_.DynamicTTCLow;
                }
                if ((std::fabs(cur_TTC) < min_ttc) && (std::fabs(cur_TTC) >= min_ttc-0.5f)) {
                    cur_a = 2;  // 报警
                } else if ((std::fabs(cur_TTC) < min_ttc-0.5f) && (std::fabs(cur_TTC) >= min_ttc-1.0f)) {
                    cur_a = 5;  // 部分制动
                }  else if (std::fabs(cur_TTC) < min_ttc-1.0f) {
                    cur_a = 7;  // 全力制动
                } else {
                    cur_a = 1;  // do nothing
                }
            } else {
                cur_a = signals->velocity_ * signals->velocity_ / (2.0*delta_dis);
            }
            if (cur_a > max_a) {
                max_a = cur_a;
            }
            AINFO << __func__ << " : cur_a=" << cur_a << " : max_a=" << max_a << " : rel_dis=" << rel_dis << ":t_a=" << t_a
                << ": velocity=" << signals->velocity_ << ": delta_dis=" << delta_dis;
            rtb_en = 1U;
        } else {
            AINFO << __func__ << " relative velocity is too small. " << rel_v;
        }
    }

    if (!json_predict_.empty()) {   // save JSON to file
        std::ofstream os;
        os.open("collision_check.json", std::ios::out | std::ios::trunc);
        if (os.is_open()){
            os << json_predict_;
            // Check if the write operation was successful
            if (os.fail()){
                // Log an error
                AERROR << "Error: Failed to write to the file collision_check.json. reason: " << strerror(errno);
            }
        } else {
            AERROR << "error: can not find or create the file which named collision_check.json . reason: " << strerror(errno);
        }
        os.close();
    }

    if (rtb_en == 0) {
        *eba_level = 0U;
        *min_velocity = 0.0;
    } else {
        *eba_level = static_cast<uint8_T>((static_cast<uint32_T>(max_a >= aeb_conf_.NotProcessedDeceleration) +
                                           (max_a >= aeb_conf_.PartialDeceleration)) +
                                          (max_a >= aeb_conf_.FullForceDeceleration));
        *min_velocity = static_cast<real_T>((signals->velocity_ <= 22.222222222222221) + (signals->velocity_ <= 16.666666666666668)) * 2.4;
    }
    AINFO << __func__ << " : result=" << static_cast<uint32_t>(rtb_en) << " : eba_level=" << static_cast<uint32_t>(*eba_level) << " : min_velocity=" << *min_velocity;
}

void AEBControl::AEBControl_EnabledSubsystem(boolean_T enable, uint8_T *level) {
    if (enable) {
        *level = 0U;
    }
}

void AEBControl::AEBControl_CalFCWLevel_Init(uint8_T *fcw_level) {
    *fcw_level = 0U;
    block_states_.fcw_level_ = 0U;
}

void AEBControl::AEBControl_CalFCWLevel(const FCWSensitivity fcw_sensitivity, const RelatedSignals *signals, RelatedTracks *tracks, uint8_T *fcw_level) {
    double rel_velocity_m{};
    double TTC{-1000.0F};
    int8_t rtb_en{-1};
    if (tracks->rtb_en_ == -1) {
        rtb_en = compute_TTC(fcw_sensitivity, signals, tracks, TTC, rel_velocity_m); // TTC是负数，rel_velocity_m是正数
        tracks->rtb_en_ = rtb_en;
        tracks->TTC_ = TTC;
        tracks->rel_velocity_ = rel_velocity_m;
    } else {    // 已经计算过TTC
        rtb_en = tracks->rtb_en_;
        TTC = tracks->TTC_;
        rel_velocity_m = tracks->rel_velocity_;
    }

    AEBControl_EnabledSubsystem((rtb_en == 0), fcw_level);
    if (rtb_en > 0) {
        auto AEB_timeMargin = aeb_conf_.WiredControlDelay;
        auto FCW_timeToReact = aeb_conf_.TimeToReact;
        auto AEB_PB1_decel = aeb_conf_.NotProcessedDeceleration;
        auto AEB_PB2_decel = aeb_conf_.PartialDeceleration;
        auto AEB_FB_decel = aeb_conf_.FullForceDeceleration;
        auto FCW_driver_decel = aeb_conf_.FCW_driver_decel;
        *fcw_level = static_cast<uint8_T>(((static_cast<uint32_T>(0.0F - TTC <= rel_velocity_m / FCW_driver_decel + FCW_timeToReact) +
                                           (0.0F - TTC <= rel_velocity_m / AEB_PB1_decel + AEB_timeMargin)) +
                                           (0.0F - TTC <= rel_velocity_m / AEB_PB2_decel + AEB_timeMargin)) +
                                           (0.0F - TTC <= rel_velocity_m / AEB_FB_decel + AEB_timeMargin));
    }

    AINFO << __func__ << " : result=" << static_cast<int32_t>(rtb_en) << " : rty_FCWLevel="
          << static_cast<uint32_t>(*fcw_level) << " : TTC=" << TTC << " : rtb_velocity_a=" << rel_velocity_m;
}

void AEBControl::AEBControl_CalHBALevel_Init(uint8_T *hba_level) {
    *hba_level = 0U;
}

void AEBControl::AEBControl_CalHBALevel(const FCWSensitivity fcw_sensitivity, const RelatedSignals *signals, RelatedTracks *tracks, uint8_T *hba_level) {
    double rel_velocity_m{0.0};
    double TTC{-1000.0};
    int8_t rtb_en{-1};
    if (tracks->rtb_en_ == -1) {
        rtb_en = compute_TTC(fcw_sensitivity, signals, tracks, TTC, rel_velocity_m); // TTC是负数，rel_velocity_m是正数
        tracks->rtb_en_ = rtb_en;
        tracks->TTC_ = TTC;
        tracks->rel_velocity_ = rel_velocity_m;
    } else {    // 已经计算过TTC
        rtb_en = tracks->rtb_en_;
        TTC = tracks->TTC_;
        rel_velocity_m = tracks->rel_velocity_;
    }

    AEBControl_EnabledSubsystem((rtb_en == 0), hba_level);
    if (rtb_en > 0) {   // 加线控延迟
        auto AEB_PB1_decel = aeb_conf_.NotProcessedDeceleration;
        auto AEB_PB2_decel = aeb_conf_.PartialDeceleration;
        auto AEB_FB_decel = aeb_conf_.FullForceDeceleration;
        *hba_level = static_cast<uint8_T>((static_cast<uint32_T>(0.0F - TTC <= rel_velocity_m / AEB_PB1_decel + aeb_conf_.WiredControlDelay) +
                                          (0.0F - TTC <= rel_velocity_m / AEB_PB2_decel + aeb_conf_.WiredControlDelay)) +
                                          (0.0F - TTC <= rel_velocity_m / AEB_FB_decel + aeb_conf_.WiredControlDelay));
    }

    AINFO << __func__ << " : result=" << static_cast<int32_t>(rtb_en) << " : hba_level=" << static_cast<uint32_t>(*hba_level) << " : TTC=" << TTC << " : rel_velocity_m=" << rel_velocity_m;
}

void AEBControl::step(const FCWSensitivity fcw_sensitivity, TracksAndSignals &tracks_and_signals, AEBCtrl &aeb_control_out) {
    real_T min_work_velocity{};
    int32_T b_i{};
    uint32_T qY{};
    uint8_T last_latent_warning_status{};
    boolean_T exitg1{};
    boolean_T guard1{};
    switch (block_states_.bp_status_) {
        case AEBControl_IN_BrakePrefill:
            if (block_states_.quit_available_bp_ == 1) {
                aeb_control_out.brake_prefill_status_ = 0U;
                block_states_.bp_status_ = AEBControl_IN_QuitWaiting_AEB;
                block_states_.ticks_p_ = 0U;
            } else {
                qY = block_states_.ticks_p_ + /*MW:OvSatOk*/ 1U;
                if (block_states_.ticks_p_ + 1U < block_states_.ticks_p_) {
                    qY = MAX_uint32_T;
                }

                block_states_.ticks_p_ = qY;
                if (block_states_.ticks_p_ > 12U) {
                    if ((block_states_.ticks_p_ > 100U) ||
                        (abs(tracks_and_signals.related_signals_i_.steering_angle_) > 6.28000020980835) ||
                        (tracks_and_signals.related_signals_i_.steering_rot_spd_ > 3.3199999332427979)) {
                        block_states_.quit_available_bp_ = 1U;
                    } else {
                        AEBControl_CheckTracksClose(&tracks_and_signals.related_tracks_e_, &block_states_.close_result_);
                        block_states_.quit_available_bp_ = (block_states_.close_result_ == 0);
                    }
                } else {
                    block_states_.quit_available_bp_ = 0U;
                }
            }
            break;
        case AEBControl_IN_IDLE:
            if (block_states_.close_result_ == 1) {
                block_states_.bp_status_ = AEBControl_IN_BrakePrefill;
                block_states_.ticks_p_ = 0U;
                aeb_control_out.brake_prefill_status_ = 1U;
            } else if ((tracks_and_signals.related_signals_i_.velocity_ < AEBCont_MinBrakePrefillVelocity) ||
                       (tracks_and_signals.related_signals_i_.pre_brake_status_ == 1) ||
                       ((std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleInhibitionFCW) && (tracks_and_signals.related_signals_i_.velocity_> 9.0)) ||
                       (tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdInhibitionFCW) ||
                       (tracks_and_signals.related_signals_i_.accel_pedal_st_ > aeb_conf_.AccelPedalStInhibitionFCW)) {
                block_states_.close_result_ = 0U;
            } else {
                AEBControl_CheckTracksClose(&tracks_and_signals.related_tracks_e_, &block_states_.close_result_);
            }
            break;
        default:
            // case IN_QuitWaiting:
            if (block_states_.ticks_p_ > 400U) {
                block_states_.bp_status_ = AEBControl_IN_IDLE;
                block_states_.close_result_ = 0U;
            } else {
                qY = block_states_.ticks_p_ + /*MW:OvSatOk*/ 1U;
                if (block_states_.ticks_p_ + 1U < block_states_.ticks_p_) {
                    qY = MAX_uint32_T;
                }

                block_states_.ticks_p_ = qY;
            }
            break;
    }

    // HBA Level
    AINFO << __func__ << ": HBA status = " << (int) block_states_.hba_status_;
    if (block_states_.hba_status_ == AEBControl_IN_BrakePrefill) {
        if (block_states_.quit_available_hba_ == 1) {
            block_states_.hba_status_ = AEBControl_IN_IDLE;
            aeb_control_out.hba_level_ = 0U;
        } else if ((std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleEvasiveEBA) ||
                   (tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdEvasiveEBA)) {
            block_states_.quit_available_hba_ = 1U;
        } else {
            AEBControl_CalHBALevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_signals_.hba_level_);
            aeb_control_out.hba_level_ = block_signals_.hba_level_;
            block_states_.quit_available_hba_ = (aeb_control_out.hba_level_ == 0);
        }
        // case IN_IDLE:
    } else if (aeb_control_out.hba_level_ > 0) {
        block_states_.hba_status_ = AEBControl_IN_BrakePrefill;
    } else if ((tracks_and_signals.related_signals_i_.velocity_ < AEBCo_MinBrakePrefillVelocity_k) ||
               ((std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleInhibitionFCW) && (tracks_and_signals.related_signals_i_.velocity_ > 9.0)) ||
               (tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdInhibitionFCW) ||
               (tracks_and_signals.related_signals_i_.accel_pedal_st_ > aeb_conf_.AccelPedalStInhibitionFCW)) {
        aeb_control_out.hba_level_ = 0U;
        AINFO << __func__ << " : HBA Preconditions not met.";
    } else {
        AEBControl_CalHBALevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_signals_.hba_level_);
        aeb_control_out.hba_level_ = block_signals_.hba_level_;
    }

    // FCW LatentWarning
    last_latent_warning_status = block_states_.last_latent_warning_status_;
    auto MinLatentWarningVelocity = aeb_conf_.MinLatentWarningVelocity;
    min_work_velocity = MinLatentWarningVelocity;
    block_states_.last_latent_warning_status_ = 0U;
    if (last_latent_warning_status == 1) {
        auto MinQuitLatentWarningVelocity = aeb_conf_.MinQuitLatentWarningVelocity;
        min_work_velocity = MinQuitLatentWarningVelocity;
    }

    if (tracks_and_signals.related_signals_i_.velocity_ > min_work_velocity) {
        b_i = 0;
        exitg1 = false;
        while ((!exitg1) && (b_i <= static_cast<int32_T>(tracks_and_signals.related_tracks_e_.track_nums_) - 1)) {
            if ((std::fabs(tracks_and_signals.related_tracks_e_.tracks_[b_i].relative_distance_x_ / tracks_and_signals.related_signals_i_.velocity_) < aeb_conf_.EnableLatentWarningHeadWay) &&
                (std::fabs(tracks_and_signals.related_tracks_e_.tracks_[b_i].relative_velocity_x_) < aeb_conf_.EnableLatentWarningVelocityRel)) {  // 与前车的相对速度的绝对值小于2m/s；并且 与前车的车间时距（相对距离/自车速度）小于0.9s
                block_states_.last_latent_warning_status_ = 1U;
                exitg1 = true;
            } else {
                b_i++;
            }
        }
    }

    // PreWarning and AcuteWarning
    AINFO << __func__ << ":FCW status = " << (int) block_states_.fcw_status_;
    switch (block_states_.fcw_status_) {
        case AEBControl_IN_AcuteWarning:
            if (block_states_.quit_available_fcw_ == 1.0) {
                block_states_.fcw_status_ = AEBControl_IN_QuitWaiting_FWC;
                block_states_.ticks_ = 0.0;
            } else {
                block_states_.ticks_++;
                aeb_control_out.fwc_status_ = block_states_.fcw_level_;
                if (block_states_.ticks_ > 20.0) {
                    block_states_.quit_available_fcw_ = 1.0;
                } else {
                    AEBControl_CalFCWLevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_signals_.fcw_level_);
                    block_states_.fcw_level_ = block_signals_.fcw_level_;
                    block_states_.quit_available_fcw_ = (block_states_.fcw_level_ == 0);
                }
            }
            break;
        case AEBControl_IN_FCWPreWarning:
            if (block_states_.quit_available_fcw_ == 1.0) {
                block_states_.fcw_status_ = AEBControl_IN_QuitWaiting_FWC;
                block_states_.ticks_ = 0.0;
            } else if (block_states_.fcw_level_ > 1) {
                block_states_.fcw_status_ = AEBControl_IN_AcuteWarning;
                block_states_.ticks_ = 0.0;
            } else {
                block_states_.ticks_++;
                if ((block_states_.ticks_ > 40.0) ||
                    (std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleEvasiveEBA) ||
                    (tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdEvasiveEBA)) {
                    block_states_.quit_available_fcw_ = 1.0;
                } else {
                    AEBControl_CalFCWLevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_signals_.fcw_level_);
                    block_states_.fcw_level_ = block_signals_.fcw_level_;
                    block_states_.quit_available_fcw_ = (block_states_.fcw_level_ == 0);
                }
            }
            break;
        case AEBControl_IN_IDLE_FCW:
            if (block_states_.fcw_level_ >= 1) {
                block_states_.fcw_status_ = AEBControl_IN_FCWPreWarning;
                block_states_.ticks_ = 0.0;
                aeb_control_out.fwc_status_ = 1U;
            } else if ((!(tracks_and_signals.related_signals_i_.velocity_ < aeb_conf_.MinFCWWorkVelocity)) &&
                       (tracks_and_signals.related_signals_i_.pre_brake_status_ == 0) &&
                       (!((std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleInhibitionFCW) && (tracks_and_signals.related_signals_i_.velocity_ > 9.0))) &&
                       (!(tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdInhibitionFCW)) &&
                       (!(tracks_and_signals.related_signals_i_.accel_pedal_st_ > aeb_conf_.AccelPedalStInhibitionFCW))) {
                AEBControl_CalFCWLevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_signals_.fcw_level_);
                block_states_.fcw_level_ = block_signals_.fcw_level_;
            } else {
                AINFO << __func__ << " : FCW Preconditions not met.";
            }
            break;
        default:
            // case IN_QuitWaiting:
            if (block_states_.ticks_ > 400.0) {
                block_states_.fcw_status_ = AEBControl_IN_IDLE_FCW;
                aeb_control_out.fwc_status_ = 0U;
                block_states_.fcw_level_ = 0U;
            } else {
                block_states_.ticks_++;
            }
            break;
    }

    // EBA
    guard1 = false;
    AINFO << __func__ << ": EBA status = " << (int) block_states_.eba_status_ << ". block_states_.EBALevel = " << (int) block_states_.eba_level_;
    switch (block_states_.eba_status_) {
        case AEBControl_IN_EBAWorking:
            if (block_states_.quit_available_eba_ == 1.0) {
                block_states_.eba_status_ = AEBControl_IN_QuitWaiting_AEB;
                block_states_.ticks_c_ = 0.0;
                aeb_control_out.eba_status_ = 0U;
                aeb_control_out.deceleration_ = 0.0;
            } else if ((tracks_and_signals.related_signals_i_.accel_pedal_st_ > aeb_conf_.AccelPedalStInhibitionEBA) ||
                       (std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleEvasiveEBA) ||
                       (tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdEvasiveEBA)) {
                block_states_.quit_available_eba_ = 1.0;
            } else {
                AEBControl_CalEBALevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_states_.eba_level_, &min_work_velocity);
                if (block_states_.eba_level_ == 0) {
                    block_states_.ticks_c_++;
                    if (block_states_.ticks_c_ > 20.0) {
                        block_states_.quit_available_eba_ = 1.0;
                    } else {
                        guard1 = true;
                    }
                } else {
                    guard1 = true;
                }
            }
            break;
        case AEBControl_IN_IDLE:
            if (block_states_.eba_level_ > 0) {
                block_states_.tp_idle_ = 0U;
                block_states_.eba_status_ = AEBControl_IN_EBAWorking;
                block_states_.quit_available_eba_ = 0.0;
                aeb_control_out.eba_status_ = 1U;
                block_states_.ticks_c_ = 1.0;
            } else if ((!(tracks_and_signals.related_signals_i_.velocity_ < aeb_conf_.MinEBAWorkVelocity)) &&
                       (!((std::fabs(tracks_and_signals.related_signals_i_.steering_angle_) > aeb_conf_.SteeringAngleInhibitionFCW) && (tracks_and_signals.related_signals_i_.velocity_> 9.0))) &&
                       (!(tracks_and_signals.related_signals_i_.steering_rot_spd_ > aeb_conf_.SteeringRotSpdInhibitionFCW)) &&
                       (!(tracks_and_signals.related_signals_i_.accel_pedal_st_ > aeb_conf_.AccelPedalStInhibitionEBA))) {
                AEBControl_CalEBALevel(fcw_sensitivity, &tracks_and_signals.related_signals_i_, &tracks_and_signals.related_tracks_e_, &block_states_.eba_level_, &min_work_velocity);
            } else {
                AINFO << __func__ << " : EBA Preconditions not met.";
            }
            break;
        default:
            // case IN_QuitWaiting:
            if (block_states_.ticks_c_ > 10.0) {
                block_states_.tp_idle_ = 1U;
                block_states_.eba_status_ = AEBControl_IN_IDLE;
                aeb_control_out.eba_status_ = 0U;
                block_states_.eba_level_ = 0U;
            } else {
                block_states_.ticks_c_++;
            }
            break;
    }

    if (guard1) {
        switch (block_states_.eba_level_) {
            case 1:
                block_states_.ticks_c_ = 0.0;
                aeb_control_out.eba_status_ = 1U;
                break;
            case 2:
                block_states_.ticks_c_ = 0.0;
                aeb_control_out.eba_status_ = 2U;
                if ((tracks_and_signals.related_signals_i_.velocity_ < 22.0F) && (aeb_control_out.deceleration_ < aeb_conf_.DecelerationEBALevel2)) {
                    aeb_control_out.deceleration_ = aeb_conf_.DecelerationEBALevel2;
                }
                break;
            case 3:
                block_states_.ticks_c_ = 0.0;
                aeb_control_out.eba_status_ = 3U;
                if ((tracks_and_signals.related_signals_i_.velocity_ < 22.0F) && (aeb_control_out.deceleration_ < aeb_conf_.DecelerationEBALevel3)) {
                    aeb_control_out.deceleration_ = aeb_conf_.DecelerationEBALevel3;
                }
                break;
        }
    }

    aeb_control_out.latent_warning_active_ = block_states_.last_latent_warning_status_;
    aeb_control_out.fwc_status_ = block_states_.fcw_level_;
}

void AEBControl::initialize(const VehiclePropertyParam &vehicle_param, const AEBConf &aeb_conf) {
    aeb_conf_ = aeb_conf;
    vehicle_param_ = vehicle_param;
    real_T Merge1;
    uint8_T Merge_i;
    block_states_.bp_status_ = AEBControl_IN_IDLE;
    block_states_.hba_status_ = AEBControl_IN_IDLE;
    AEBControl_CalHBALevel_Init(&block_signals_.hba_level_);
    block_states_.fcw_status_ = AEBControl_IN_IDLE_FCW;
    AEBControl_CalFCWLevel_Init(&block_signals_.fcw_level_);
    block_states_.tp_idle_ = 1U;
    block_states_.eba_status_ = AEBControl_IN_IDLE;
    AEBControl_CalEBALevel_Init(&Merge_i, &Merge1);
    block_states_.close_result_ = 0U;
}

void AEBControl::terminate() {
    // (no terminate code required)
}

AEBControl::RT_MODEL_AEBControl_T *AEBControl::getRTM() {
    return (&realtime_model_);
}

int8_t AEBControl::compute_TTC(FCWSensitivity fcw_sensitivity, const RelatedSignals *signals,
                               RelatedTracks *tracks, double &TTC, double &rel_velocity) {
    TTC = {-1000.0F};
    double cur_TTC{};
    int8_t rtb_en{0};
    json_predict_ = nlohmann::json::object();   // clear JSON object
    for (uint32_t i = 0; i < tracks->track_nums_; i++) {
        AINFO << __func__ << " track_id_: " << tracks->tracks_[i].track_id_ << ". relative_velocity_x_: " << tracks->tracks_[i].relative_velocity_x_
            << ". relative_distance_x_: " << tracks->tracks_[i].relative_distance_x_ << ". relative_velocity_y_: " << tracks->tracks_[i].relative_velocity_y_
            << ". relative_distance_y_: " << tracks->tracks_[i].relative_distance_y_ << ". obs direction: " << static_cast<int32_t>(tracks->tracks_[i].obs_direction_);
        // 障碍物在自车前方 or 交叉路口，障碍物与自车行驶方向垂直 or 障碍物在自车前方，障碍物切入自车行驶的车道 or 自车转弯，障碍物在自车前方
        if (tracks->tracks_[i].relative_velocity_x_ < 0.0) {   // 相对速度小于0,才会走入下面的分支
            auto result = collision_check(fcw_sensitivity, tracks->tracks_[i], *signals);
            cur_TTC = -result.second;   // cur_TTC是负数
            tracks->tracks_[i].TTC_ = cur_TTC;
            AINFO << __func__ << " collision:" << result.first << " . TTC:" << cur_TTC;
            if (result.first) { // 有碰撞风险
                if (cur_TTC > TTC) {
                    TTC = cur_TTC;
                    rel_velocity = -tracks->tracks_[i].relative_velocity_x_;  // rtb_velocity是正数
                }
                rtb_en = 1;
                tracks->tracks_[i].rtb_en_ = 1;
            } else {    // 无碰撞风险
                tracks->tracks_[i].rtb_en_ = 0;
            }
        }
    }

    if (!json_predict_.empty()) {   // save JSON to file
        std::ofstream os;
        os.open("collision_check.json", std::ios::out | std::ios::trunc);
        if (os.is_open()){
            os << json_predict_;
            // Check if the write operation was successful
            if (os.fail()){
                // Log an error
                AERROR << "Error: Failed to write to the file collision_check.json. reason: " << strerror(errno) << ")";
            }
        } else {
            AERROR << "error: can not find or create the file which named collision_check.json . reason: " << strerror(errno) << ")";
        }
        os.close();
    }
    AINFO << __func__ << " result : " << static_cast<int32_t>(rtb_en) << " : TTC = " << TTC << ". rel_velocity = " << rel_velocity << ". Obs count = " << tracks->track_nums_;
    return rtb_en;
}

/// Runs the collision checking algorithm with the current parameters.
///
/// Returns true if a collision is predicted to occur under the configured TTC, along with its collision time in ms.
std::pair<bool, float> AEBControl::collision_check(const FCWSensitivity fcw_sensitivity, const TrackInfo &target, const RelatedSignals &signals) {
    auto v_obs_x = target.relative_velocity_x_ + signals.velocity_;  // obs velocity x = v_rel_x + v_ego_x;
    auto v_obs_y = target.relative_velocity_y_;  // obs velocity y = v_rel_y + v_ego_y; v_ego_y = 0;
    constexpr auto epsilon{0.000001};
    double min_ttc{2.7};
    if ((std::fabs(v_obs_x) < epsilon) && (std::fabs(v_obs_y) < epsilon)) { // 障碍物静止
        if (fcw_sensitivity == FCWSensitivity::HIGH) {
            min_ttc = aeb_conf_.StaticTTCHigh;
        } else if (fcw_sensitivity == FCWSensitivity::MEDIUM) {
            min_ttc = aeb_conf_.StaticTTCMedium;
        } else if (fcw_sensitivity == FCWSensitivity::LOW) {
            min_ttc = aeb_conf_.StaticTTCLow;
        }
    } else {    // 运动障碍物
        if (fcw_sensitivity == FCWSensitivity::HIGH) {
            min_ttc = aeb_conf_.DynamicTTCHigh;
        } else if (fcw_sensitivity == FCWSensitivity::MEDIUM) {
            min_ttc = aeb_conf_.DynamicTTCMedium;
        } else if (fcw_sensitivity == FCWSensitivity::LOW) {
            min_ttc = aeb_conf_.DynamicTTCLow;
        }
    }

    // Convert ttc from s to millis
    auto ttc = static_cast<size_t>(min_ttc * 1000.0f);

    if (json_predict_.contains("predict_ego")) {
        json_predict_.erase("predict_ego");
    }

    // Collision check by integrating over our model
    size_t t{};
    for (t = 0; t <= ttc; t += aeb_conf_.TTCSampleStep) {
        // Predict ego position at time with definite integral of forward kinematics
        auto [pos, heading] = predict_ego_pos(t, signals);
        auto obb_ego = create_obb(pos, heading);

        // Predict target positon at time with definite integral of forward kinematics
        auto predict_target = predict_target_pos(t, target, signals);

        json_predict_["predict_ego"].emplace_back(obb_ego);
        json_predict_["predict_target"].emplace_back(predict_target.target_);
        // Actually collision check
        if (obb_ego.HasOverlap(predict_target.target_)) {
            json_predict_["ttc"].emplace_back(t);
            return {true, static_cast<double>(t)/1000.0};   // Convert t from ms to seconds
        }
    }
    json_predict_["ttc"].emplace_back(t);
    return {false, 0.0};
}

std::pair<Vec2d, double> AEBControl::predict_ego_pos(const size_t &time, const RelatedSignals &signals) const {
    const auto t = static_cast<float>(time) / 1000.0f;  // convert ms to s
    // 使用插值拟合函数将方向盘角度映射到前轮转角
    const auto phi = linearSearch(signals.steering_angle_ * 180.0 / M_PI, lat_calibration_table) * M_PI / 180.0;  // 前轮转角, unit is rad.
    const auto s = signals.velocity_;           // 自车速度
    const auto l = vehicle_param_.wheel_base_;   // 自车轴距

    constexpr auto epsilon{0.000001};
    if (std::fabs(phi) < epsilon) {   // 直线位移 = 时间×速度
        const auto displacement = t * s;
        return {{displacement, 0.0}, 0.0};
    }

    // Forward kinematics equations integrated over time. Based on: https://www.xarg.org/book/kinematics/ackerman-steering/
    // phi 很小，tan(phi)约等于phi。
    // theta 很小，tan(theta)约等于theta。
    // derivative of x = s * cos(theta), x = s * t * cos(theta) = s * t * sin(theta)/tan(theta) 约等于 s * t * sin(theta)/theta
    // derivative of theta = (s / l ) * tan(phi), theta = (s * t / l) * tan(phi)
    // x = (s * t * sin((s * t / l) * tan(phi)))/((s * t / l) * tan(phi)) = sin((s * t / l) * tan(phi)) * l / tan(phi)
    //
    // r = l/tan(phi) 转弯半径
    // y = r * (1-cos(theta)) = (l / tan(phi)) * (1-cos(theta))
    const auto x = l * (1.0f / tan(phi)) * sin((s * t * tan(phi)) / l);
    const auto y = l * (1.0f / tan(phi)) * (-cos((s * t * tan(phi)) / l) + 1.0f);

    // Integrate heading separately for OBB
    const auto heading = (s * t * tan(phi)) / l;

    return {{x, y}, heading};
}

TrackInfo AEBControl::predict_target_pos(const size_t &time, const TrackInfo &track, const RelatedSignals &signals) const {
    TrackInfo track_new = track;
    auto v_obs_x = track.relative_velocity_x_ + signals.velocity_;  // obs velocity x = v_rel_x + v_ego_x;
    auto v_obs_y = track.relative_velocity_y_;  // obs velocity y = v_rel_y + v_ego_y; v_ego_y = 0;
    constexpr auto epsilon{0.000001};
    if ((std::fabs(v_obs_x) < epsilon) && (std::fabs(v_obs_y) < epsilon)) { // 障碍物静止
        return track_new;
    }
    auto delta_t = static_cast<double>(time) / 1000.0;  // Convert time from ms to seconds
    track_new.relative_distance_x_ = track.relative_distance_x_ + track.relative_velocity_x_ * delta_t;
    track_new.relative_distance_y_ = track.relative_distance_y_ + track.relative_velocity_y_ * delta_t;
    auto points = track_new.target_.points();
    for (auto &point : points) {
        point.set_x(point.x() + v_obs_x * delta_t);
        point.set_y(point.y() + v_obs_y * delta_t);
    }
    Memory_parking::math::Polygon2d target_new(points);
    track_new.target_ = target_new;
    return track_new;
}

Memory_parking::math::Polygon2d AEBControl::create_obb(const Vec2d &pos, const double yaw) const {
    // 旋转矩阵的cos和sin
    double cos_theta = cos(yaw);
    double sin_theta = sin(yaw);

    std::vector<Vec2d> points;
    points.reserve(4);
    points.emplace_back(vehicle_param_.length_ - vehicle_param_.back_edge_to_rearCenter_ + vehicle_param_.long_safe_dis_, vehicle_param_.width_/2+vehicle_param_.lat_safe_dis_);    // right_up
    points.emplace_back(-vehicle_param_.back_edge_to_rearCenter_ - vehicle_param_.long_safe_dis_, vehicle_param_.width_/2+vehicle_param_.lat_safe_dis_);    // left_up
    points.emplace_back(-vehicle_param_.back_edge_to_rearCenter_ - vehicle_param_.long_safe_dis_, -(vehicle_param_.width_/2+vehicle_param_.lat_safe_dis_)); // left_down
    points.emplace_back(vehicle_param_.length_ - vehicle_param_.back_edge_to_rearCenter_ + vehicle_param_.long_safe_dis_, -(vehicle_param_.width_/2+vehicle_param_.lat_safe_dis_)); // right_down
    // 从自车坐标转换世界坐标
    for (auto &point : points) {
        // 旋转后的x, y坐标（x为竖轴，y为横轴）
        auto tmp_x = point.x() * cos_theta - point.y() * sin_theta + pos.x(); // 新的x坐标（竖轴）
        auto tmp_y = point.x() * sin_theta + point.y() * cos_theta + pos.y(); // 新的y坐标（横轴）
        point.set_x(tmp_x);
        point.set_y(tmp_y);
    }
    Memory_parking::math::Polygon2d ego_box(points);
    return ego_box;
}
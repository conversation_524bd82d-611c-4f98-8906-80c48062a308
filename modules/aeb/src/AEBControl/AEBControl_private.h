//
// File: AEBControl_private.h
//
// Code generated for Simulink model 'AEBControl'.
//
// Model version                  : 1.23
// Simulink Coder version         : 23.2 (R2023b) 01-Aug-2023
// C/C++ source code generated on : Mon Nov 11 14:09:37 2024
//
// Target selection: ert.tlc
// Embedded hardware selection: Intel->x86-64 (Windows64)
// Code generation objectives: Unspecified
// Validation result: Not run
//
#ifndef RTW_HEADER_AEBControl_private_h_
#define RTW_HEADER_AEBControl_private_h_
#include "aeb_rtwtypes.h"
#include "AEBControl_types.h"

// Exported data define

// Definition for custom storage class: Define
//#define AEB_FB_decel                   7.0                       // Referenced by:
                                                                 //  '<S16>/const5'
                                                                 //  '<S14>/const5'
                                                                 //  '<S22>/const5'

//#define AEB_PB1_decel                  3.8                       // Referenced by:
                                                                 //  '<S16>/const6'
                                                                 //  '<S14>/const1'
                                                                 //  '<S22>/const1'

//#define AEB_PB2_decel                  5.3                       // Referenced by:
                                                                 //  '<S16>/const4'
                                                                 //  '<S14>/const4'
                                                                 //  '<S22>/const4'

//#define AEB_timeMargin                 0.4                       // Referenced by: '<S16>/const7'
//#define FCW_driver_decel               4.0                       // Referenced by: '<S16>/const1'
//#define FCW_timeToReact                1.2                       // Referenced by: '<S16>/const3'
//#define MinLatentWarningVelocity     18.055555555555554        // Referenced by: '<S6>/Constant'
//#define MinQuitLatentWarningVelocity   16.666666666666668        // Referenced by: '<S6>/Constant1'
#endif                                 // RTW_HEADER_AEBControl_private_h_

//
// File trailer for generated code.
//
// [EOF]
//

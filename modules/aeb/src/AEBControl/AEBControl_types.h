#ifndef RTW_HEADER_AEBControl_types_h_
#define RTW_HEADER_AEBControl_types_h_
#include "aeb_rtwtypes.h"

struct RelatedSignals {
    real_T accel_pedal_st_{};
    uint8_T pre_brake_status_{};    // 制动踏板信号置真: 1 : 踩下刹车踏板; 0：未踩下刹车踏板
    real_T steering_rot_spd_{};
    real_T steering_angle_{};       // unit is rad. 左正右负
    double velocity_{};             // unit is m/s.
    uint8_T gear_{};
    double acc_x_{};                // longitudinal acceleration
    double acc_y_{};                // lateral acceleration
};


struct AEBCtrl {
    uint8_T brake_prefill_status_{};
    uint8_T hba_level_{};
    uint8_T latent_warning_active_{};
    uint8_T fwc_status_{};
    uint8_T eba_status_{};
    real_T deceleration_{};
};

#endif                                 // RTW_HEADER_AEBControl_types_h_
#ifndef RTW_HEADER_AEBControl_h_
#define RTW_HEADER_AEBControl_h_

#include "aeb_rtwtypes.h"
#include "AEBControl_types.h"

// Includes for objects with custom storage classes
#include "AEBControl_private.h"
#include "config/aeb_conf.hpp"
#include "config/aeb_vehicle_property_param.h"
#include "AEBDecisionLogic/AEBDecisionLogic.h"

// Macros for accessing real-time model data structure
#ifndef rtmGetErrorStatus
#define rtmGetErrorStatus(rtm)         ((rtm)->errorStatus)
#endif

#ifndef rtmSetErrorStatus
#define rtmSetErrorStatus(rtm, val)    ((rtm)->errorStatus = (val))
#endif

namespace mega::aeb {
// Class declaration for model AEBControl
class AEBControl final {
// public data and function members
public:
    // Block signals (default storage)
    struct BlockSignals {
        uint8_T hba_level_{};                     // '<S19>/Merge'
        uint8_T fcw_level_{};                   // '<S11>/Merge'
    };

    // Block states (default storage) for system '<Root>'
    struct BlockStates {
        real_T quit_available_fcw_;          // '<S4>/Chart'
        real_T ticks_;                      // '<S4>/Chart'
        real_T quit_available_eba_;            // '<S3>/Chart'
        real_T ticks_c_;                    // '<S3>/Chart'
        uint32_T ticks_p_;                  // '<S2>/Chart'
        uint8_T last_latent_warning_status_;// '<S6>/lastLatentWarningStatus'
        uint8_T quit_available_hba_;         // '<S5>/Chart'
        uint8_T hba_status_;                // '<S5>/Chart' HBA工作状态
        uint8_T fcw_level_;                  // '<S4>/Chart'
        uint8_T fcw_status_;                // '<S4>/Chart' FCW工作状态
        uint8_T eba_level_;                  // '<S3>/Chart'
        uint8_T eba_status_;                // '<S3>/Chart' EBA工作状态
        uint8_T tp_idle_;                   // '<S3>/Chart'
        uint8_T quit_available_bp_;          // '<S2>/Chart'
        uint8_T close_result_;               // '<S2>/Chart'
        uint8_T bp_status_;                 // '<S2>/Chart' 制动准备（BP）工作状态
    };

    // External inputs (root import signals with default storage)
    struct TracksAndSignals {
        RelatedTracks related_tracks_e_;     // '<Root>/RelatedTracks'
        RelatedSignals related_signals_i_;   // '<Root>/RelatedSignals'
    };

    // External outputs (root outports fed by signals with default storage)
    //AEBCtrl aeb_ctrl_{};                 // '<Root>/AEBCtrl'

    // Real-time Model Data Structure
    struct RT_MODEL_AEBControl_T {
        const char_T *volatile errorStatus;
    };

    // Copy Constructor
    AEBControl(AEBControl const &) = delete;

    // Assignment Operator
    AEBControl &operator=(AEBControl const &) & = delete;

    // Move Constructor
    AEBControl(AEBControl &&) = delete;

    // Move Assignment Operator
    AEBControl &operator=(AEBControl &&) = delete;

    // Real-Time Model get method
    AEBControl::RT_MODEL_AEBControl_T *getRTM();

    /**
     * @brief Initializes the AEBControl model with the provided vehicle parameters and AEB configuration.
     *
     * This function sets up the internal states and signals of the AEBControl model based on the input parameters.
     * It initializes the block states to their default idle conditions, configures the HBA, FCW, and EBA levels,
     * and prepares the model for subsequent operations. The initialization ensures that all necessary parameters
     * and states are properly configured before the model is used for further processing.
     *
     * @param vehicle_param [in] A structure containing vehicle-specific properties required for initialization.
     * @param aeb_conf [in] A structure containing configuration parameters for the AEB system.
     */
    void initialize(const VehiclePropertyParam &vehicle_param, const AEBConf &aeb_conf);

    /**
     * Executes a single step of the model's processing logic.
     * This function processes the input data related to forward collision warning (FCW) sensitivity,
     * track and signal information, and advanced emergency braking (AEB) control output.
     * It updates the state of the system based on the provided inputs and performs necessary computations
     * to determine the appropriate control actions for the AEB system.
     *
     * @param fcw_sensitivity [in] The sensitivity level for the forward collision warning system.
     *                        This parameter influences how aggressively the system responds to potential collisions.
     * @param tracks_and_signals [in/out] A reference to the structure containing data about detected tracks
     *                           and associated signals. This includes information about objects in the environment
     *                           and their movement patterns.
     * @param aeb_control_out [out] A reference to the structure that holds the output control signals
     *                        for the advanced emergency braking system. This is updated during the step execution.
     */
    void step(FCWSensitivity fcw_sensitivity, TracksAndSignals &tracks_and_signals, AEBCtrl &aeb_control_out);

    // model terminate function
    static void terminate();

    // Constructor
    AEBControl() = default;

    // Destructor
    ~AEBControl() = default;

// private data and function members
private:
    // private member function(s) for subsystem '<S7>/CheckTracksClose'
    void AEBControl_CheckTracksClose(const RelatedTracks *tracks, uint8_T *close_result) const;

    // private member function(s) for subsystem '<S10>/CalEBALevel'
    void AEBControl_CalEBALevel_Init(uint8_T *eba_level, real_T *min_velocity);

    /**
     * Calculates the Emergency Brake Assist (EBA) level based on the provided sensitivity, signals, and tracks.
     * This function evaluates the collision risk for each track in the provided list, considering factors such as
     * relative velocity, distance, and time-to-collision (TTC). It determines the appropriate braking level and minimum
     * velocity required to avoid or mitigate a potential collision. The results are stored in the output parameters
     * `eba_level` and `min_velocity`.
     *
     * The function performs the following steps:
     * - Iterates through all tracks to assess collision risk using TTC and relative velocity.
     * - Determines the braking acceleration (`cur_a`) for each track based on the collision risk and vehicle dynamics.
     * - Tracks with no collision risk are ignored, while those with risk are processed further.
     * - Updates the maximum braking acceleration (`max_a`) across all tracks.
     * - Saves collision-related data to a JSON file if any collision checks were performed.
     * - Computes the EBA level and minimum velocity based on the maximum braking acceleration and vehicle speed.
     *
     * @param fcw_sensitivity [in] Sensitivity level for forward collision warning (FCW), which influences the TTC thresholds.
     * @param signals [in] Pointer to a structure containing vehicle-related signals such as velocity.
     * @param tracks [in/out] Pointer to a structure containing information about detected tracks, including their positions,
     *               velocities, and collision states.
     * @param eba_level [out] Pointer to a variable where the calculated EBA level will be stored. The level indicates the
     *                  intensity of braking required (e.g., no action, partial braking, full braking).
     * @param min_velocity [out] Pointer to a variable where the minimum velocity threshold will be stored. This value is
     *                     derived based on the vehicle's current velocity and configuration parameters.
     *
     */
    void AEBControl_CalEBALevel(FCWSensitivity fcw_sensitivity, const RelatedSignals *signals,
                                    RelatedTracks *tracks, uint8_T *eba_level, real_T *min_velocity);

    // private member function(s) for subsystem '<S18>/Enabled Subsystem'
    void AEBControl_EnabledSubsystem(boolean_T enable, uint8_T *level);

    // private member function(s) for subsystem '<S17>/CalFCWLevel'
    void AEBControl_CalFCWLevel_Init(uint8_T *fcw_level);

    /**
     * Calculates the Forward Collision Warning (FCW) level based on the provided sensitivity, signals, and tracks.
     * This function determines the FCW level by computing or retrieving the Time-To-Collision (TTC) and relative velocity.
     * If the TTC has not been previously calculated, it computes the TTC and updates the track data. Otherwise, it uses
     * the precomputed values stored in the tracks. The FCW level is then determined based on the computed or retrieved
     * values and a set of predefined deceleration thresholds.
     *
     * The function also logs the result, including the computed FCW level, TTC, and relative velocity.
     *
     * @param fcw_sensitivity [in] Sensitivity level for Forward Collision Warning, influencing the computation of TTC.
     * @param signals [in] Pointer to the structure containing related signals required for TTC calculation.
     * @param tracks [in/out] Pointer to the structure containing track-related data, including precomputed TTC and velocity if available.
     * @param fcw_level [out] Pointer to a variable where the resulting FCW level will be stored. The level indicates the urgency
     *                  of the warning based on the collision risk.
     */
    void AEBControl_CalFCWLevel(FCWSensitivity fcw_sensitivity, const RelatedSignals *signals, RelatedTracks *tracks, uint8_T *fcw_level);

    // private member function(s) for subsystem '<S18>/CalHBALevel'
    void AEBControl_CalHBALevel_Init(uint8_T *hba_level);

    /**
     * Calculates the High Beam Assist (HBA) level based on the provided sensitivity, signals, and tracks.
     * This function determines the Time To Collision (TTC) and relative velocity between the ego vehicle
     * and the tracked object. It evaluates whether the HBA system should be enabled or disabled based
     * on the computed TTC and relative velocity. Additionally, it considers deceleration thresholds
     * and wired control delay to determine the appropriate HBA level.
     *
     * The function first checks if the TTC has already been computed for the given tracks. If not,
     * it computes the TTC and updates the track's state. Otherwise, it reuses the precomputed values.
     * The HBA level is then calculated based on the relationship between the TTC, relative velocity,
     * and predefined deceleration thresholds.
     *
     * @param fcw_sensitivity [in] Sensitivity level for Forward Collision Warning (FCW), influencing the computation of TTC.
     * @param signals [in] Pointer to the structure containing relevant sensor signals used in TTC calculation.
     * @param tracks [in/out] Pointer to the structure holding track-related information, including precomputed TTC and enable state.
     * @param hba_level [out] Pointer to a variable where the calculated HBA level will be stored. The level indicates
     *                  the intensity or activation state of the High Beam Assist system.
     *
     */
    void AEBControl_CalHBALevel(FCWSensitivity fcw_sensitivity, const RelatedSignals *signals, RelatedTracks *tracks, uint8_T *hba_level);

    /**
     * Computes the Time To Collision (TTC) for a set of tracks based on sensitivity and signal data.
     * This function iterates through all tracks, evaluates collision risk, and determines the TTC
     * for each track with a negative relative velocity along the x-axis. The TTC is updated to the
     * maximum value among tracks that pose a collision risk. Additionally, the function logs relevant
     * information and saves collision-related data to a JSON file if applicable.
     *
     * @param fcw_sensitivity [in] Sensitivity level for forward collision warning, influencing collision detection.
     * @param signals [in] Pointer to a structure containing related signals used in collision checks.
     * @param tracks [in/out] Pointer to a structure containing track data, including relative velocities and distances.
     * @param TTC [out] Reference to a double variable where the computed maximum TTC will be stored.
     * @param rel_velocity [out] Reference to a double variable where the relative velocity corresponding to the maximum TTC will be stored.
     * @return int8_t Returns 1 if any track poses a collision risk, otherwise returns 0.
     */
    int8_t compute_TTC(FCWSensitivity fcw_sensitivity, const RelatedSignals *signals,
                       RelatedTracks *tracks, double &TTC, double &rel_velocity);
    /**
     * @brief Performs a collision check between the ego vehicle and a target object.
     *
     * This function evaluates the possibility of a collision between the ego vehicle and a specified target
     * based on the provided sensitivity level, target information, and vehicle signals. The function calculates
     * the Time-To-Collision (TTC) threshold based on whether the target is stationary or moving and performs
     * a step-by-step collision prediction over time intervals.
     *
     * The collision check involves predicting the positions of both the ego vehicle and the target at each
     * time step within the TTC range. Oriented Bounding Boxes (OBBs) are used to represent the ego vehicle's
     * position, while the target's predicted position is checked for overlap with the OBB.
     *
     * If a collision is detected during the prediction, the function returns true along with the time (in seconds)
     * at which the collision is expected to occur. Otherwise, it returns false with a time value of 0.0.
     *
     * @param fcw_sensitivity [in] Sensitivity level for Forward Collision Warning (FCW). Determines the TTC thresholds
     *                        for both static and dynamic targets. Valid values are HIGH, MEDIUM, or LOW.
     * @param target [in] Information about the target object, including its relative velocity and position.
     *               Used to predict the target's future position.
     * @param signals [in] Vehicle-related signals, such as velocity, used to compute the ego vehicle's motion.
     * @return std::pair<bool, float> A pair where the first element indicates whether a collision is expected
     *                                 (true if a collision is detected, false otherwise), and the second element
     *                                 specifies the time (in seconds) at which the collision would occur.
     *                                 If no collision is detected, the time value is 0.0.
     *
     */
    std::pair<bool, float> collision_check(FCWSensitivity fcw_sensitivity, const TrackInfo &target, const RelatedSignals &signals);

    /**
     * @brief Predicts the future position and heading of the ego vehicle based on kinematic equations.
     *
     * This function calculates the future position and heading of the ego vehicle using forward kinematics
     * derived from Ackerman steering principles. The prediction is based on the provided time duration,
     * vehicle-related signals, and calibration data for steering angle interpolation.
     *
     * The function handles two scenarios:
     * 1. When the steering angle is effectively zero (vehicle moving in a straight line), the displacement
     *    is calculated as the product of time and velocity.
     * 2. When the steering angle is non-zero, the function computes the vehicle's position using kinematic
     *    equations that account for the turning radius and angular displacement.
     *
     * The steering angle is mapped to the front wheel angle using a linear interpolation function with a
     * predefined calibration table. The calculations assume small-angle approximations for both the steering
     * angle and the resulting heading angle.
     *
     * @param time [in] The time duration (in milliseconds) for which the future position is predicted.
     * @param signals [in] A structure containing vehicle-related signals, including velocity and steering angle.
     * @return A pair consisting of:
     *         - A Vec2d object representing the predicted 2D position (x, y) of the ego vehicle in meters.
     *         - A double value representing the predicted heading angle (in radians) of the ego vehicle.
     *
     * @note The function assumes the steering angle is provided in radians and converts it to degrees
     *       internally for interpolation purposes.
     * @note The function uses small-angle approximations (e.g., tan(phi) ≈ phi) for simplification.
     * @note The vehicle's wheelbase and calibration table are used in the calculations.
     */
    std::pair<Vec2d, double> predict_ego_pos(const size_t &time, const RelatedSignals &signals) const;

    /**
     * @brief Creates an oriented bounding box (OBB) for the ego vehicle in world coordinates.
     *
     * This function calculates the oriented bounding box of the ego vehicle based on its position, yaw angle, and predefined safety margins.
     * The bounding box is represented as a polygon with four vertices, which are initially defined in the vehicle's local coordinate system.
     * These vertices are then transformed into the world coordinate system using a rotation matrix derived from the yaw angle and the provided position.
     *
     * The calculation involves:
     * - Defining the four corners of the bounding box in the vehicle's local coordinate system, incorporating longitudinal and lateral safety distances.
     * - Applying a rotation transformation to align the bounding box with the vehicle's orientation in the world coordinate system.
     * - Translating the rotated points to the vehicle's position in the world coordinate system.
     *
     * The resulting polygon represents the oriented bounding box of the ego vehicle, which can be used for collision detection or other geometric computations.
     *
     * @param pos [in] The position of the ego vehicle in the world coordinate system.
     * @param yaw [in] The yaw angle of the ego vehicle, representing its orientation in radians.
     * @return Memory_parking::math::Polygon2d A polygon object representing the oriented bounding box of the ego vehicle.
     */
    Memory_parking::math::Polygon2d create_obb(const Vec2d &pos, double yaw) const;

    /**
     * @brief Predicts the future position of a target based on its current track information and velocity.
     *
     * This function calculates the updated position of a target after a specified time interval.
     * If the target's velocity is below a threshold (0.1 units), it is considered stationary, and
     * the original track information is returned without modification.
     * Otherwise, the relative distances in both x and y directions are updated based on the target's
     * velocity and the given time interval. The points defining the target's polygon are also adjusted
     * accordingly to reflect the new position.
     *
     * @param time [in] The time interval for prediction, in milliseconds.
     * @param track [in] The current track information of the target, including its position, velocity, and shape.
     * @param signals [in] A structure containing vehicle-related signals, including velocity and steering angle.
     * @return TrackInfo A new TrackInfo object representing the predicted position and updated shape of the target.
     */
    TrackInfo predict_target_pos(const size_t &time, const TrackInfo &track, const RelatedSignals &signals) const;
private:
    // Block signals
    BlockSignals block_signals_{};

    // Block states
    BlockStates block_states_{};

    // Real-Time Model
    RT_MODEL_AEBControl_T realtime_model_{};

    mega::aeb::AEBConf aeb_conf_;
    mega::aeb::VehiclePropertyParam vehicle_param_;

    nlohmann::json json_predict_;
};
}

#endif                                 // RTW_HEADER_AEBControl_h_


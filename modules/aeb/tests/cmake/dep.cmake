cmake_minimum_required(VERSION 3.10)
project(test_aeb_server)
include(CMakeParseArguments)
include(CMakeDependentOption)

#set(CMAKE_CXX_STANDARD 14)
#set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0")
set(CMAKE_CXX_FLAGS " ${CMAKE_CXX_FLAGS} -fno-access-control")

if(GTEST_EXPORTS_FILE)
    include("${GTEST_EXPORTS_FILE}")
    if(NOT TARGET gtest)
        message(STATUS "Google Test was not found!")
        message(FATAL_ERROR "GTEST_EXPORTS_FILE: ${GTEST_EXPORTS_FILE}")
    endif()
else()
    message(STATUS "GTEST_EXPORTS_FILE not defined!")
endif()

# The following are because <PERSON>Make has no FindGMock
# GMOCK_INCLUDE_DIR is unlikely to be different from GTEST_INCLUDE_DIRS but just in case
find_path(GMOCK_INCLUDE_DIR gmock/gmock.h)
find_library(GMOCK_LIB NAMES libgmock.a)
find_library(GMOCK_LIB_MAIN NAMES libgmock_main.a)

set(GMOCK_LIBRARIES ${GMOCK_LIB} ${GMOCK_LIB_MAIN})
set(GMOCK_INCLUDE_DIRS ${GMOCK_INCLUDE_DIR})

message(STATUS "---------------------------------------------------")
message(STATUS "./test/ - Importing dependencies ------------------")

find_package(GTest REQUIRED)

function(add_gtest name)
    cmake_parse_arguments(add_gtest "" "" "SOURCE;LIBS;DEP_TARGETS" ${ARGN})

    #    foreach(sourcefile ${add_gtest_SOURCE})
    #        list(APPEND sourcepath ${CMAKE_CURRENT_SOURCE_DIR}/${sourcefile})
    #    endforeach(sourcefile)
    set(sourcepath ${add_gtest_SOURCE})
    LINK_DIRECTORIES(${PROJECT_SOURCE_DIR}/libs/)
    add_executable(gtest_${name} ${sourcepath})
    target_include_directories(gtest_${name} PRIVATE ${CMAKE_CURRENT_LIST_DIR} ${GTEST_INCLUDE_DIRS} ${GMOCK_INCLUDE_DIRS} ${PROJECT_SOURCE_DIR} ${AIS_AEB_DIR})
    target_link_libraries(gtest_${name} ${GTEST_BOTH_LIBRARIES} ${GMOCK_LIBRARIES} Boost::program_options Threads::Threads protobuf protobuf_ pnc_math cyber uuid fastrtps fastcdr gflags glog)
    target_compile_options(gtest_${name} PRIVATE -std=gnu++1z)
    # Add the dependency for gtest/gmock
    add_test(NAME ${name} COMMAND gtest_${name} "--gtest_break_on_failure")
    # Add further target dependencies if any
    if(add_gtest_DEP_TARGETS)
        add_dependencies(gtest_${name} ${add_gtest_DEP_TARGETS})
    endif()
#    add_custom_command(
#            TARGET gtest_${name}
#            POST_BUILD
#            COMMAND cp -r ${PROJECT_SOURCE_DIR}/conf ${PROJECT_BINARY_DIR}/
#    )
endfunction()

file(GLOB_RECURSE TEST_PRJ_SOURCES ${AIS_AEB_DIR}/../*.cpp ${AIS_AEB_DIR}/../*.cc modules/common/base/base64.cc)

add_gtest(${PROJECT_NAME}
    SOURCE
        ${TEST_PRJ_SOURCES}
    LIBS

    DEP_TARGETS
)

//
// Created by wq on 24-12-2.
//
#include <gtest/gtest.h>
#include <fstream>
#include <cyber/common/log.h>
#include <cyber/cyber.h>
#include <google/protobuf/util/json_util.h>
#include "aeb_fusion_obstacles_info.hpp"
#include "modules/common/base/json/json.hpp"
#include "aeb_component.h"
#include "AEBControl/AEBControl_types.h"

extern "C" int run_aeb_server(int argc, char* argv[]);
extern std::shared_ptr<mega::aeb::AebComponent> g_aeb_component;

class AEBServerTest : public ::testing::TestWithParam<std::string> {
protected:
    void SetUp() override {
        // 初始化PlanningServer对象
        char *argv[] = { strdup("./../gtest_test_aeb_server"), nullptr };
        apollo::cyber::Init(argv[0]);
        run_aeb_server(1, argv);
        if (g_aeb_component != nullptr) {
            if(g_aeb_component->control_proc_timer_ != nullptr){
                g_aeb_component->control_proc_timer_->Stop();
            }
        }
    }

    void TearDown() override {
        // 清理资源
        apollo::cyber::SetState(apollo::cyber::State::STATE_SHUTTING_DOWN);
    }

    std::string ROOT_PATH{"./../../modules/aeb/tests/src/"};   // current dir is adas-core/build/output

};

TEST_F(AEBServerTest, Test_update_radar_detect_obstacles) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    // 执行被测函数
    std::string str_json = R"..({
        "header": {
            "timestamp": 1743048961.70435,
            "moduleName": "io_server",
            "sequenceNum": 2402
        },
        "detectionList": [{
            "position": {
                "x": 17.0546875,
                "y": 0.421875
            },
            "velocity": {},
            "accelerate": {},
            "length": 1,
            "lengthQuality": 1,
            "width": 0.59375,
            "trackAge": 116,
            "orientationRad": 0.0191328116,
            "yawRate": 0.009765625,
            "covariance": [1.015625, 0.015625, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.633
        }, {
            "frameId": 1,
            "objectId": 1,
            "position": {
                "x": 28.859375,
                "y": -7.28125
            },
            "velocity": {
                "x": -9.3828125,
                "y": -0.1796875
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.375
            },
            "length": 5.5,
            "lengthQuality": 1,
            "width": 1.3984375,
            "trackAge": 18,
            "orientationRad": -3.12344527,
            "yawRate": 0.0087890625,
            "covariance": [1.0234375, 0.0703125, 0.015625, 0.4296875, 21.046875, 2.1328125, 0.015625, 0.015625, 0],
            "measurementTime": 1743048961.634
        }, {
            "frameId": 2,
            "objectId": 3,
            "position": {
                "x": 62.453125,
                "y": -5.765625
            },
            "velocity": {
                "x": -9.328125,
                "y": 0.390625
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.125
            },
            "length": 3,
            "lengthQuality": 1,
            "width": 0.609375,
            "trackAge": 72,
            "orientationRad": 3.09921098,
            "yawRate": -0.037109375,
            "covariance": [0.984375, 0.078125, 0, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.634
        }, {
            "frameId": 3,
            "objectId": 4,
            "position": {
                "x": 42.109375,
                "y": -7.3828125
            },
            "velocity": {
                "x": -9.3671875,
                "y": -0.1640625
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 7.5,
            "lengthQuality": 1,
            "width": 1.453125,
            "trackAge": 27,
            "orientationRad": 3.15780473,
            "yawRate": -0.005859375,
            "covariance": [1.03125, 0.0390625, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.634
        }, {
            "frameId": 4,
            "objectId": 5,
            "position": {
                "x": 100.71875,
                "y": -5.15625
            },
            "velocity": {
                "x": -9.359375,
                "y": 0.3984375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.125
            },
            "length": 3.5,
            "lengthQuality": 1,
            "width": 1.3984375,
            "trackAge": 26,
            "orientationRad": 3.09823442,
            "yawRate": -0.025390625,
            "covariance": [1.03125, 0.1875, -0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
            "measurementTime": 1743048961.635
        }, {
            "frameId": 5,
            "objectId": 6,
            "position": {
                "x": 129.6171875,
                "y": -5.625
            },
            "velocity": {
                "x": -9.375,
                "y": -0.28125
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 8.25,
            "lengthQuality": 1,
            "width": 2.03125,
            "trackAge": 9,
            "orientationRad": 3.1714766,
            "yawRate": -0.0048828125,
            "covariance": [1.0546875, 0.1484375, 0.0078125, 0.4375, 21.03125, 2.1484375, 0.0078125, 0.0078125, 0],
            "measurementTime": 1743048961.635
        }, {
            "frameId": 6,
            "objectId": 7,
            "position": {
                "x": 12.9375,
                "y": -7.46875
            },
            "velocity": {
                "x": -9.3359375,
                "y": -0.2734375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 10,
            "lengthQuality": 1,
            "width": 1.7265625,
            "trackAge": 190,
            "orientationRad": 3.16952348,
            "yawRate": 0.0087890625,
            "covariance": [1, 0.0234375, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.0546875, 0.0546875, 0],
            "measurementTime": 1743048961.635
        }, {
            "frameId": 7,
            "objectId": 10,
            "position": {
                "x": 22.6875,
                "y": 3.1484375
            },
            "velocity": {
                "x": -9.328125,
                "y": -0.5078125
            },
            "accelerate": {
                "x": 9.25,
                "y": 0.25
            },
            "length": 4.5,
            "lengthQuality": 1,
            "width": 1.1015625,
            "trackAge": 70,
            "orientationRad": -3.08828902,
            "yawRate": 0.001953125,
            "covariance": [1.015625, 0.03125, 0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.635
        }, {
            "frameId": 8,
            "objectId": 12,
            "position": {
                "x": 5.71875,
                "y": 4.2734375
            },
            "velocity": {
                "x": -9.3125,
                "y": -0.265625
            },
            "accelerate": {
                "x": 9.25,
                "y": 0.25
            },
            "length": 6.7421875,
            "lengthQuality": 1,
            "width": 1.625,
            "trackAge": 60,
            "orientationRad": -3.11367965,
            "yawRate": -0.0185546875,
            "covariance": [1, 0.046875, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.015625, 0.015625, 0],
            "measurementTime": 1743048961.636
        }, {
            "frameId": 9,
            "objectId": 14,
            "position": {
                "x": 52.7421875,
                "y": -5.671875
            },
            "velocity": {
                "x": -9.34375,
                "y": 0.09375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 8.25,
            "lengthQuality": 1,
            "width": 1.3984375,
            "trackAge": 58,
            "orientationRad": 3.13046098,
            "covariance": [1.0234375, 0.03125, -0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.636
        }, {
            "position": {
                "x": 17.0703125,
                "y": 0.421875
            },
            "velocity": {},
            "accelerate": {},
            "length": 1,
            "lengthQuality": 1,
            "width": 0.59375,
            "trackAge": 117,
            "orientationRad": 0.0191328116,
            "yawRate": 0.009765625,
            "covariance": [1.015625, 0.015625, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.687
        }, {
            "frameId": 1,
            "objectId": 1,
            "position": {
                "x": 28.3359375,
                "y": -7.3125
            },
            "velocity": {
                "x": -9.3828125,
                "y": -0.1953125
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.375
            },
            "length": 5.5,
            "lengthQuality": 1,
            "width": 1.3984375,
            "trackAge": 19,
            "orientationRad": -3.12149215,
            "yawRate": 0.0029296875,
            "covariance": [1.0234375, 0.0703125, 0.015625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
            "measurementTime": 1743048961.687
        }, {
            "frameId": 2,
            "objectId": 3,
            "position": {
                "x": 61.9296875,
                "y": -5.71875
            },
            "velocity": {
                "x": -9.3515625,
                "y": 0.421875
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 3,
            "lengthQuality": 1,
            "width": 0.609375,
            "trackAge": 73,
            "orientationRad": 3.09628129,
            "yawRate": -0.0341796875,
            "covariance": [0.984375, 0.078125, -0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.688
        }, {
            "frameId": 3,
            "objectId": 4,
            "position": {
                "x": 41.6171875,
                "y": -7.3984375
            },
            "velocity": {
                "x": -9.390625,
                "y": -0.1640625
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.375
            },
            "length": 7.5,
            "lengthQuality": 1,
            "width": 1.453125,
            "trackAge": 28,
            "orientationRad": 3.15780473,
            "yawRate": -0.005859375,
            "covariance": [1.03125, 0.0390625, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.688
        }, {
            "frameId": 4,
            "objectId": 5,
            "position": {
                "x": 100.2265625,
                "y": -5.1484375
            },
            "velocity": {
                "x": -9.3125,
                "y": 0.375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.125
            },
            "length": 3.5,
            "lengthQuality": 1,
            "width": 1.3984375,
            "trackAge": 27,
            "orientationRad": 3.10018754,
            "yawRate": -0.0166015625,
            "covariance": [1.03125, 0.1953125, -0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.688
        }, {
            "frameId": 5,
            "objectId": 6,
            "position": {
                "x": 129.1015625,
                "y": -5.7265625
            },
            "velocity": {
                "x": -9.390625,
                "y": -0.3984375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.375
            },
            "length": 8.25,
            "lengthQuality": 1,
            "width": 2.0234375,
            "trackAge": 10,
            "orientationRad": 3.18319535,
            "yawRate": -0.0029296875,
            "covariance": [1.046875, 0.15625, 0.0078125, 0.4375, 20.984375, 2.1328125, 0.0078125, 0.0078125, 0],
            "measurementTime": 1743048961.689
        }, {
            "frameId": 6,
            "objectId": 7,
            "position": {
                "x": 12.40625,
                "y": -7.4765625
            },
            "velocity": {
                "x": -9.3984375,
                "y": -0.28125
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 10,
            "lengthQuality": 1,
            "width": 1.7265625,
            "trackAge": 191,
            "orientationRad": 3.1705,
            "yawRate": 0.0107421875,
            "covariance": [1, 0.0234375, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.015625, 0.015625, 0],
            "measurementTime": 1743048961.689
        }, {
            "frameId": 7,
            "objectId": 10,
            "position": {
                "x": 22.1875,
                "y": 3.15625
            },
            "velocity": {
                "x": -9.359375,
                "y": -0.484375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.375
            },
            "length": 4.5,
            "lengthQuality": 1,
            "width": 1.1015625,
            "trackAge": 71,
            "orientationRad": -3.09024215,
            "yawRate": -0.001953125,
            "covariance": [1.015625, 0.03125, 0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.689
        }, {
            "frameId": 8,
            "objectId": 12,
            "position": {
                "x": 5.3203125,
                "y": 4.234375
            },
            "velocity": {
                "x": -9.2890625,
                "y": -0.28125
            },
            "accelerate": {
                "x": 9.25,
                "y": 0.25
            },
            "length": 6.7421875,
            "lengthQuality": 1,
            "width": 1.625,
            "trackAge": 61,
            "orientationRad": -3.11172652,
            "yawRate": -0.0166015625,
            "covariance": [1, 0.046875, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.015625, 0.015625, 0],
            "measurementTime": 1743048961.69
        }, {
            "frameId": 9,
            "objectId": 14,
            "position": {
                "x": 52.21875,
                "y": -5.671875
            },
            "velocity": {
                "x": -9.359375,
                "y": 0.09375
            },
            "accelerate": {
                "x": 9.375,
                "y": 0.25
            },
            "length": 8.25,
            "lengthQuality": 1,
            "width": 1.3984375,
            "trackAge": 59,
            "orientationRad": 3.13046098,
            "yawRate": 0.001953125,
            "covariance": [1.0234375, 0.03125, -0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1743048961.69
        }],
        "measurementTime": 1587320
    })..";
    auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
    nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
    EXPECT_FALSE(jf.is_discarded());
    google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
    update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    mega::aeb::RadarObstacles radar_obstacles{};
    get_radar_obs(radar_obstacles);
    EXPECT_EQ(radar_obstacles.obstacles_.size(), 20);
}
// 新的法雷奥数据
TEST_F(AEBServerTest, Test_update_new_radar_detect_obstacles) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    // 执行被测函数
    std::string str_json = R"..( {"header":{"timestamp":1748246640.261251,"moduleName":"io_server","sequenceNum":3301},"detectionList":[{"position":{"x":5.4921875,"y":-0.6015625},"velocity":0.0078125,"accelerate":0.359375,"length":5.109375,"lengthCovariance":1,"width":1.3984375,"trackAge":255,"existProbablity":0.996078432,"orientationRad":3.08009601,"orientationCovariance":0.0078125,"covariance":[1.0234375,0.046875,-0.0546875,21.0390625,0.0078125],"measurementTime":1748246640.198}],"measurementTime":2894339})..";
    auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
    nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
    EXPECT_FALSE(jf.is_discarded());
    google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
    mega::aeb::update_new_radar_detect_obstacles(msg);
    mega::aeb::RadarObstacles radar_obstacles{};
    get_radar_obs(radar_obstacles);
    EXPECT_EQ(radar_obstacles.obstacles_.size(), 1);
}

TEST_F(AEBServerTest, Test_update_long_chassis_info) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    std::string str_json = R"..({"header":{"timestamp":1744964975.63524,"moduleName":"io_server","sequenceNum":32799},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":9.1093752119276257,"wheelSpeedFl":9.0781254238552513,"wheelSpeedFr":9.0468745761447487,"wheelSpeedRl":9.1249995761447487,"wheelSpeedRr":9.0781254238552513},"throttlePerceptage":1.5,"acceleration":{"longitudinal":4.163336342344337e-16,"lateral":0.059999998658895493},"yawRate":-0.00261799432,"steeringSystem":{"steeringWheelAngle":-0.30000001192092896},"curTorque":100})..";
    auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
    nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
    EXPECT_FALSE(jf.is_discarded());
    google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
    // 执行被测函数
    mega::aeb::update_long_chassis_info(msg);
    RelatedSignals signals{};
    mega::aeb::get_signals(signals);
    EXPECT_LT(std::fabs(signals.velocity_ - 9.1093752119276257), 0.000001);
}

// 新的法雷奥数据：前车减速到刹停，自车直行
TEST_F(AEBServerTest, Test_tick_radar_obs1) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748246631.0210619,"moduleName":"io_server","sequenceNum":15999},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.8437498940361872,"wheelSpeedFl":3.828125,"wheelSpeedFr":3.8437498940361872,"wheelSpeedRl":3.8437498940361872,"wheelSpeedRr":3.8437498940361872},"acceleration":{"longitudinal":-0.059999998658895493,"lateral":0.090000003576278687},"yawRate":0.00314159319,"steeringSystem":{"steeringWheelAngle":4.3000001907348633,"steeringWheelTorque":0.550000011920929},"curTorque":-72,"slopeEstimation":-0.00658379821,"massEstimation":2357.39087})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748246631.0199389,"moduleName":"io_server","sequenceNum":3211},"detectionList":[{"position":{"x":16.9609375,"y":-0.5546875},"velocity":2.6171875,"accelerate":-0.015625,"length":5.109375,"lengthCovariance":1,"width":1.3984375,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.0781071857,"orientationCovariance":0.0078125,"yawRate":-0.0107421875,"covariance":[1.015625,0.109375,-0.046875,21.0390625,0],"measurementTime":1748246630.902},{"position":{"x":16.9296875,"y":-0.5703125},"velocity":2.625,"length":5.109375,"lengthCovariance":1,"width":1.3984375,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.0790837482,"orientationCovariance":0.0078125,"yawRate":-0.0107421875,"covariance":[1.015625,0.109375,-0.046875,21.0390625,0],"measurementTime":1748246630.958}],"measurementTime":2885099})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
        mega::aeb::RadarObstacles radar_obstacles{};
        get_radar_obs(radar_obstacles);
        EXPECT_EQ(radar_obstacles.obstacles_.size(), 2);
    }
    AEBCtrl ctrl_out{};
    g_aeb_component->aeb_.tick_radar_obs(g_aeb_component->fcw_sensitivity_, ctrl_out);
    EXPECT_EQ(ctrl_out.fwc_status_, 0);
    EXPECT_EQ(ctrl_out.hba_level_, 0);
    EXPECT_EQ(ctrl_out.brake_prefill_status_, 0);
    EXPECT_EQ(ctrl_out.deceleration_, 0);
    EXPECT_EQ(ctrl_out.eba_status_, 0);
    EXPECT_EQ(ctrl_out.latent_warning_active_, 0);
}

// 新的法雷奥数据：前车减速到刹停，自车跟前车左转
TEST_F(AEBServerTest, Test_tick_radar_obs2) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748246818.9415019,"moduleName":"io_server","sequenceNum":25199},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.7031249735090468,"wheelSpeedFl":1.6562500264909532,"wheelSpeedFr":2.0937499735090466,"wheelSpeedRl":1.484375,"wheelSpeedRr":1.937*************},"throttlePerceptage":0.3,"acceleration":{"longitudinal":-0.18000000715255737,"lateral":0.7****************},"yawRate":0.275412977,"steeringSystem":{"steeringWheelAngle":372.60000610351562,"steeringWheelTorque":1.2799999713897705},"curTorque":129,"slopeEstimation":-0.0137070538})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748246819.0111189,"moduleName":"io_server","sequenceNum":5045},"detectionList":[{"position":{"x":15.25,"y":2.46875},"velocity":1.28125,"accelerate":0.078125,"length":1.0078125,"lengthCovariance":0.90625,"width":0.4765625,"widthCovariance":0.0078125,"trackAge":20,"existProbablity":0.596078455,"orientationRad":-0.280255616,"orientationCovariance":0.0078125,"yawRate":-0.0048828125,"covariance":[0.890625,0.0546875,-0.1171875,21.0390625,0.0078125],"measurementTime":1748246818.896},{"frameId":1,"objectId":1,"position":{"x":5.1015625,"y":-3.4765625},"velocity":0.296875,"length":0.75,"lengthCovariance":1,"width":1.2578125,"widthCovariance":0.0078125,"trackAge":180,"existProbablity":0.996078432,"orientationRad":-0.147443116,"orientationCovariance":0.03125,"yawRateCovariance":0.0078125,"covariance":[0.8359375,0.328125,0.3984375,21.0390625,0.6015625],"measurementTime":1748246818.896},{"frameId":2,"objectId":4,"position":{"x":5.2109375,"y":-8.796875},"velocity":0.3671875,"length":0.9921875,"lengthCovariance":1,"width":0.859375,"widthCovariance":0.015625,"trackAge":223,"existProbablity":0.984313726,"orientationRad":-1.3134588,"orientationCovariance":0.0703125,"yawRateCovariance":0.0078125,"covariance":[1.3125,1.1328125,-0.8828125,21.0390625,1.59375],"measurementTime":1748246818.897},{"position":{"x":15.6015625,"y":3.0078125},"velocity":1.2109375,"accelerate":0.2734375,"length":2.5390625,"lengthCovariance":1,"width":1.3203125,"widthCovariance":0.0078125,"trackAge":21,"existProbablity":0.521568656,"orientationRad":0.415056884,"orientationCovariance":0.0078125,"yawRateCovariance":0.03125,"covariance":[0.90625,0.265625,0.390625,30.875,0.0078125],"measurementTime":1748246818.952},{"frameId":1,"objectId":1,"position":{"x":4.953125,"y":-3.546875},"velocity":0.296875,"accelerate":-0.0078125,"length":0.75,"lengthCovariance":1,"width":1.2578125,"widthCovariance":0.0078125,"trackAge":181,"existProbablity":0.996078432,"orientationRad":-0.162091553,"orientationCovariance":0.03125,"yawRateCovariance":0.0078125,"covariance":[0.8515625,0.3359375,0.40625,21.0390625,0.65625],"measurementTime":1748246818.953},{"frameId":2,"objectId":4,"position":{"x":4.984375,"y":-8.875},"velocity":0.3671875,"length":0.9921875,"lengthCovariance":1,"width":0.859375,"widthCovariance":0.015625,"trackAge":224,"existProbablity":0.984313726,"orientationRad":-1.32810724,"orientationCovariance":0.0703125,"yawRateCovariance":0.0078125,"covariance":[1.359375,1.234375,-0.9296875,21.0390625,1.65625],"measurementTime":1748246818.953}],"measurementTime":3073091})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
        mega::aeb::RadarObstacles radar_obstacles{};
        get_radar_obs(radar_obstacles);
        EXPECT_EQ(radar_obstacles.obstacles_.size(), 6);
    }
    AEBCtrl ctrl_out{};
    g_aeb_component->aeb_.tick_radar_obs(g_aeb_component->fcw_sensitivity_, ctrl_out);
    EXPECT_EQ(ctrl_out.fwc_status_, 0);
    EXPECT_EQ(ctrl_out.hba_level_, 0);
    EXPECT_EQ(ctrl_out.brake_prefill_status_, 0);
    EXPECT_EQ(ctrl_out.deceleration_, 0);
    EXPECT_EQ(ctrl_out.eba_status_, 0);
    EXPECT_EQ(ctrl_out.latent_warning_active_, 0);
}

// 新的法雷奥数据：前车减速到刹停，自车跟右前方车辆左转,右前方车辆跟自车不在同一个车道
TEST_F(AEBServerTest, Test_tick_radar_obs3) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748246903.880178,"moduleName":"io_server","sequenceNum":29349},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.515625,"wheelSpeedFl":3.359375,"wheelSpeedFr":3.8437498940361872,"wheelSpeedRl":3.28125,"wheelSpeedRr":3.7656248940361872},"acceleration":{"longitudinal":-0.*****************,"lateral":1.3999999761581421},"yawRate":0.299498528,"steeringSystem":{"steeringWheelAngle":218.5,"steeringWheelRate":60,"steeringWheelTorque":1.6100000143051147},"curTorque":-72,"slopeEstimation":-0.00517645525})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748246903.9744742,"moduleName":"io_server","sequenceNum":5873},"detectionList":[{"position":{"x":9.7109375,"y":1.8984375},"velocity":2.65625,"accelerate":0.375,"length":3.2421875,"lengthCovariance":0.8671875,"width":1.3828125,"widthCovariance":0.0078125,"trackAge":198,"existProbablity":0.996078432,"orientationRad":0.806658447,"yawRate":0.28515625,"covariance":[0.3359375,0.4453125,0.359375,21.0390625,0.046875],"measurementTime":1748246903.903},{"frameId":1,"objectId":9,"position":{"x":-3.0625,"y":2.15625},"velocity":0.046875,"length":1,"lengthCovariance":1,"width":0.4296875,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":3.07618976,"orientationCovariance":0.6015625,"yawRateCovariance":0.0234375,"covariance":[5.546875,0.734375,1.8046875,21.0390625,2.5078125],"measurementTime":1748246903.903}],"measurementTime":3158043})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
        mega::aeb::RadarObstacles radar_obstacles{};
        get_radar_obs(radar_obstacles);
        EXPECT_EQ(radar_obstacles.obstacles_.size(), 2);
    }
    AEBCtrl ctrl_out{};
    g_aeb_component->aeb_.tick_radar_obs(g_aeb_component->fcw_sensitivity_, ctrl_out);
    EXPECT_EQ(ctrl_out.fwc_status_, 0);
    EXPECT_EQ(ctrl_out.hba_level_, 0);
    EXPECT_EQ(ctrl_out.brake_prefill_status_, 0);
    EXPECT_EQ(ctrl_out.deceleration_, 0);
    EXPECT_EQ(ctrl_out.eba_status_, 0);
    EXPECT_EQ(ctrl_out.latent_warning_active_, 0);
}

// 新的法雷奥数据：前车减速到刹停，自车跟车右转
TEST_F(AEBServerTest, Test_tick_radar_obs4) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748246989.477216,"moduleName":"io_server","sequenceNum":33549},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.5312498940361872,"wheelSpeedFl":3.8125001059638128,"wheelSpeedFr":3.3906250529819064,"wheelSpeedRl":3.7343751059638128,"wheelSpeedRr":3.3281249470180936},"brakePercentage":10,"acceleration":{"longitudinal":-0.*****************,"lateral":-1.559999942779541},"yawRate":-0.246091455,"steeringSystem":{"steeringWheelAngle":-185.10000610351562,"steeringWheelRate":84,"steeringWheelTorque":-1.7899999618530273},"curTorque":-72,"slopeEstimation":-0.0148368543})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748246989.5719881,"moduleName":"io_server","sequenceNum":6707},"detectionList":[{"position":{"x":6.7421875,"y":-4.6171875},"velocity":2.1953125,"accelerate":-0.015625,"length":2.71875,"lengthCovariance":0.9921875,"width":1.078125,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.567365,"orientationCovariance":0.015625,"yawRate":-0.095703125,"yawRateCovariance":0.0078125,"covariance":[0.6796875,0.3984375,-0.4140625,21.0390625,0.0234375],"measurementTime":1748246989.47},{"position":{"x":6.6875,"y":-4.6015625},"velocity":2.203125,"accelerate":0.0078125,"length":2.71875,"lengthCovariance":0.9921875,"width":1.078125,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.552716553,"orientationCovariance":0.015625,"yawRate":-0.091796875,"yawRateCovariance":0.0078125,"covariance":[0.6875,0.3984375,-0.40625,21.0390625,0.0390625],"measurementTime":1748246989.526}],"measurementTime":3243666})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
        mega::aeb::RadarObstacles radar_obstacles{};
        get_radar_obs(radar_obstacles);
        EXPECT_EQ(radar_obstacles.obstacles_.size(), 2);
    }
    AEBCtrl ctrl_out{};
    g_aeb_component->aeb_.tick_radar_obs(g_aeb_component->fcw_sensitivity_, ctrl_out);
    EXPECT_EQ(ctrl_out.fwc_status_, 0);
    EXPECT_EQ(ctrl_out.hba_level_, 0);
    EXPECT_EQ(ctrl_out.brake_prefill_status_, 0);
    EXPECT_EQ(ctrl_out.deceleration_, 0);
    EXPECT_EQ(ctrl_out.eba_status_, 0);
    EXPECT_EQ(ctrl_out.latent_warning_active_, 0);
}

// 新的法雷奥数据：自车左转，对向车道车辆直行
TEST_F(AEBServerTest, Test_tick_radar_obs5) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748247119.9710839,"moduleName":"io_server","sequenceNum":39949},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":2.5,"wheelSpeedFl":2.3906249470180936,"wheelSpeedFr":2.65625,"wheelSpeedRl":2.34375,"wheelSpeedRr":2.6406251059638128},"acceleration":{"longitudinal":-0.029999999329447746,"lateral":0.*****************},"yawRate":0.171216831,"steeringSystem":{"steeringWheelAngle":177.60000610351562,"steeringWheelRate":36,"steeringWheelTorque":1.2300000190734863},"curTorque":26,"slopeEstimation":-0.00376977958})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748247119.980092,"moduleName":"io_server","sequenceNum":7983},"detectionList":[{"position":{"x":19.1328125,"y":5.0546875},"velocity":2.71875,"accelerate":-0.203125,"length":3.25,"lengthCovariance":0.9921875,"width":0.515625,"widthCovariance":0.015625,"trackAge":71,"existProbablity":0.996078432,"orientationRad":3.03224444,"yawRate":0.001953125,"covariance":[1.0390625,0.03125,0.0078125,21.0390625,0.0078125],"measurementTime":1748247119.897},{"frameId":1,"objectId":2,"position":{"x":69.59375,"y":-39.703125},"velocity":0.15625,"length":4.59375,"lengthCovariance":1,"width":0.7109375,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.220685303,"orientationCovariance":0.015625,"yawRateCovariance":0.0078125,"covariance":[1.2890625,0.1328125,0.171875,21.0390625,0.921875],"measurementTime":1748247119.897},{"frameId":2,"objectId":5,"position":{"x":35.65625,"y":-30.4765625},"velocity":0.2109375,"length":4.4921875,"lengthCovariance":1,"width":0.5,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.203107178,"orientationCovariance":0.0078125,"yawRateCovariance":0.0078125,"covariance":[1.4921875,0.0703125,0.09375,21.0390625,1.140625],"measurementTime":1748247119.897},{"frameId":3,"objectId":6,"position":{"x":22.75,"y":-1.9921875},"velocity":0.0390625,"length":0.75,"lengthCovariance":1,"width":1.3125,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.529411793,"orientationRad":2.91212726,"orientationCovariance":0.1796875,"covariance":[1.0859375,0.1015625,0.15625,21.0390625,0.5234375],"measurementTime":1748247119.898},{"frameId":4,"objectId":10,"position":{"x":23.53125,"y":-26.5078125},"velocity":0.0546875,"length":4.2421875,"lengthCovariance":1,"width":0.421875,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.327130616,"orientationCovariance":0.03125,"yawRateCovariance":0.0078125,"covariance":[4.5234375,0.15625,-0.25,21.0390625,2.21875],"measurementTime":1748247119.898},{"position":{"x":19.015625,"y":4.875},"velocity":2.6875,"accelerate":-0.2265625,"length":3.5,"lengthCovariance":0.9296875,"width":0.53125,"widthCovariance":0.015625,"trackAge":72,"existProbablity":0.996078432,"orientationRad":3.02345538,"yawRate":-0.0087890625,"covariance":[0.96875,0.03125,0,21.0390625,0],"measurementTime":1748247119.953},{"frameId":1,"objectId":2,"position":{"x":69.109375,"y":-40.3046875},"velocity":0.15625,"length":4.59375,"lengthCovariance":1,"width":0.7109375,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.229474366,"orientationCovariance":0.015625,"yawRateCovariance":0.0078125,"covariance":[1.3359375,0.140625,0.1796875,21.0390625,0.9765625],"measurementTime":1748247119.953},{"frameId":2,"objectId":5,"position":{"x":35.25,"y":-30.7890625},"velocity":0.2109375,"length":4.4921875,"lengthCovariance":1,"width":0.5,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.211896241,"orientationCovariance":0.0078125,"yawRateCovariance":0.0078125,"covariance":[1.5625,0.078125,0.09375,21.0390625,1.1953125],"measurementTime":1748247119.954},{"frameId":3,"objectId":6,"position":{"x":22.59375,"y":-2.1953125},"velocity":0.0390625,"length":0.75,"lengthCovariance":1,"width":1.3125,"widthCovariance":0.015625,"trackAge":255,"existProbablity":0.517647088,"orientationRad":2.90333819,"orientationCovariance":0.1796875,"covariance":[1.1015625,0.1015625,0.1640625,21.0390625,0.578125],"measurementTime":1748247119.954},{"frameId":4,"objectId":10,"position":{"x":23.15625,"y":-26.7109375},"velocity":0.0546875,"length":4.2421875,"lengthCovariance":1,"width":0.421875,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.335919678,"orientationCovariance":0.03125,"yawRateCovariance":0.0078125,"covariance":[4.796875,0.1640625,-0.2734375,21.0390625,2.2734375],"measurementTime":1748247119.954}],"measurementTime":3374091})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
        mega::aeb::RadarObstacles radar_obstacles{};
        get_radar_obs(radar_obstacles);
        EXPECT_EQ(radar_obstacles.obstacles_.size(), 10);
    }
    AEBCtrl ctrl_out{};
    g_aeb_component->aeb_.tick_radar_obs(g_aeb_component->fcw_sensitivity_, ctrl_out);
    EXPECT_EQ(ctrl_out.fwc_status_, 0);
    EXPECT_EQ(ctrl_out.hba_level_, 0);
    EXPECT_EQ(ctrl_out.brake_prefill_status_, 0);
    EXPECT_EQ(ctrl_out.deceleration_, 0);
    EXPECT_EQ(ctrl_out.eba_status_, 0);
    EXPECT_EQ(ctrl_out.latent_warning_active_, 0);
}

TEST_F(AEBServerTest, Test_proc_disable_aeb) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::FALSE;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::NONE;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());
}

TEST_F(AEBServerTest, Test_proc_disable_fcw) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::FALSE;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::NONE;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());
}

TEST_F(AEBServerTest, Test_proc_acc_started) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::TRUE;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());
}

TEST_F(AEBServerTest, Test_proc) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1744964975.63524,"moduleName":"io_server","sequenceNum":32799},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":9.1093752119276257,"wheelSpeedFl":9.0781254238552513,"wheelSpeedFr":9.0468745761447487,"wheelSpeedRl":9.1249995761447487,"wheelSpeedRr":9.0781254238552513},"throttlePerceptage":1.5,"acceleration":{"longitudinal":4.163336342344337e-16,"lateral":0.059999998658895493},"yawRate":-0.00261799432,"steeringSystem":{"steeringWheelAngle":-0.30000001192092896},"curTorque":100})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1744964975.6972461,"moduleName":"io_server","sequenceNum":6573},"detectionList":[{"position":{"x":5.4609375,"y":6.390625},"velocity":{"x":-9.0078125,"y":0.8515625},"accelerate":{"x":9,"y":-0.125},"length":3.5078125,"lengthQuality":1,"width":0.609375,"trackAge":29,"orientationRad":60.7632751,"yawRate":-0.0703125,"covariance":[1.0703125,0.5234375,-0.171875,0.4296875,21.0390625,2.1328125,0.515625,0.515625,1],"measurementTime":1744964975.596},{"frameId":1,"objectId":3,"position":{"x":14.1640625,"y":12.890625},"velocity":{"x":-8.5546875,"y":0.28125},"accelerate":{"x":8.5,"y":-0.375},"length":4.7421875,"lengthQuality":1,"width":0.8125,"trackAge":115,"orientationRad":3.108,"yawRate":0.0009765625,"covariance":[1.1484375,0.78125,-0.0859375,0.4296875,21.0390625,2.1328125,0.2421875,0.2421875,0],"measurementTime":1744964975.596},{"frameId":2,"objectId":4,"position":{"x":30.984375,"y":6.9453125},"velocity":{"x":-9.328125,"y":0.03125},"accelerate":{"x":9.25,"y":0.875},"length":3.2578125,"lengthQuality":1,"width":1.109375,"trackAge":60,"orientationRad":3.13729692,"yawRate":-0.009765625,"covariance":[1.0546875,0.1328125,0,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1744964975.596},{"frameId":3,"objectId":8,"position":{"x":12.34375,"y":-5.5546875},"velocity":{"x":-9.2578125,"y":-0.0703125},"accelerate":{"x":9.125,"y":0.625},"length":9.75,"lengthQuality":1,"width":1.40625,"trackAge":32,"orientationRad":3.1480391,"yawRate":0.0009765625,"covariance":[1,0.03125,0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1744964975.5969999},{"frameId":4,"objectId":10,"position":{"x":14.2734375,"y":-2.5859375},"velocity":{"x":-9.3046875,"y":-0.328125},"accelerate":{"x":9.25,"y":0.75},"length":7.25,"lengthQuality":1,"width":1.3984375,"trackAge":9,"orientationRad":3.17538285,"yawRate":-0.0166015625,"covariance":[1.0703125,0.0625,0.015625,0.4375,21.03125,2.1484375,0.015625,0.015625,0],"measurementTime":1744964975.5969999},{"frameId":5,"objectId":12,"position":{"x":55.3125,"y":7.1484375},"velocity":{"x":-9.3125,"y":0.0078125},"accelerate":{"x":9.25,"y":0.625},"length":2.96875,"lengthQuality":1,"width":0.59375,"trackAge":7,"orientationRad":3.1402266,"yawRate":-0.0009765625,"covariance":[1.0625,0.109375,-0.0078125,0.453125,21.9765625,2.28125,0.0078125,0.0078125,0],"measurementTime":1744964975.5969999},{"frameId":6,"objectId":14,"position":{"x":60.953125,"y":-3.75},"velocity":{"x":-9.21875,"y":0.3046875},"accelerate":{"x":9.125,"y":0.5},"length":5.25,"lengthQuality":1,"width":1.3984375,"trackAge":21,"orientationRad":3.108,"yawRate":0.0234375,"covariance":[1.015625,0.1484375,-0.0234375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1744964975.598},{"position":{"x":4.9609375,"y":6.4375},"velocity":{"x":-9,"y":0.890625},"accelerate":{"x":9},"length":3.5078125,"lengthQuality":1,"width":0.609375,"trackAge":30,"orientationRad":60.7593689,"yawRate":-0.0703125,"covariance":[1.09375,0.578125,-0.1796875,0.4296875,21.0390625,2.1328125,0.5546875,0.5546875,1],"measurementTime":1744964975.651},{"frameId":1,"objectId":3,"position":{"x":13.6875,"y":12.90625},"velocity":{"x":-8.5546875,"y":0.28125},"accelerate":{"x":8.5,"y":-0.375},"length":4.7421875,"lengthQuality":1,"width":0.8125,"trackAge":116,"orientationRad":3.108,"yawRate":0.0009765625,"covariance":[1.1640625,0.875,-0.0859375,0.4296875,21.0390625,2.1328125,0.28125,0.28125,0],"measurementTime":1744964975.651},{"frameId":2,"objectId":4,"position":{"x":30.484375,"y":6.8671875},"velocity":{"x":-9.2890625,"y":-0.0234375},"accelerate":{"x":9.25,"y":0.875},"length":3.2578125,"lengthQuality":1,"width":1.109375,"trackAge":61,"orientationRad":3.14315629,"yawRate":-0.0087890625,"covariance":[1.0546875,0.1171875,0,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1744964975.651},{"frameId":3,"objectId":8,"position":{"x":11.8203125,"y":-5.546875},"velocity":{"x":-9.171875,"y":-0.0703125},"accelerate":{"x":9.25,"y":0.5},"length":9.75,"lengthQuality":1,"width":1.40625,"trackAge":33,"orientationRad":3.1480391,"yawRate":0.0009765625,"covariance":[1,0.03125,0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1744964975.652},{"frameId":4,"objectId":12,"position":{"x":54.7890625,"y":7.2734375},"velocity":{"x":-9.3359375,"y":0.1171875},"accelerate":{"x":9.25,"y":0.75},"length":2.96875,"lengthQuality":1,"width":0.59375,"trackAge":8,"orientationRad":3.12850785,"yawRate":0.0009765625,"covariance":[1.0703125,0.1171875,-0.0078125,0.4453125,21.28125,2.1953125,0.015625,0.015625,0],"measurementTime":1744964975.652},{"frameId":5,"objectId":14,"position":{"x":60.4296875,"y":-3.7578125},"velocity":{"x":-9.2109375,"y":0.3203125},"accelerate":{"x":9.125,"y":0.625},"length":5.25,"lengthQuality":1,"width":1.40625,"trackAge":22,"orientationRad":3.10604692,"yawRate":0.005859375,"covariance":[1.015625,0.1328125,-0.0234375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1744964975.652}],"measurementTime":1163624})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());

    // 当ACC激活时安全距离报警应被抑制激活
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::TRUE;
    // 执行被测函数
    testing::internal::CaptureStderr();
    for (int i = 0; i < 50; ++i) {
        EXPECT_NO_THROW(g_aeb_component->proc());
    }
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("Start ACC.");
    EXPECT_NE(pos, std::string::npos);
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;

    // 当关闭功能时安全距离报警功能关闭。
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::FALSE;
    // 执行被测函数
    testing::internal::CaptureStderr();
    for (int i = 0; i < 50; ++i) {
        EXPECT_NO_THROW(g_aeb_component->proc());
    }
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("Not enable AEB.");
    EXPECT_NE(pos, std::string::npos);

    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::FALSE;
    // 执行被测函数
    testing::internal::CaptureStderr();
    for (int i = 0; i < 50; ++i) {
        EXPECT_NO_THROW(g_aeb_component->proc());
    }
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("Not enable FCW.");
    EXPECT_NE(pos, std::string::npos);

    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
}

// 没有符合FCW条件的障碍物，不报警。
TEST_F(AEBServerTest, Test_proc_one_obs) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":7.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1745232020.1429679,"moduleName":"io_server","sequenceNum":3462},"detectionList":[{"position":{"x":16.5703125,"y":-11.2890625},"velocity":{"x":-7.3828125,"y":-0.0859375},"accelerate":{"x":7.5,"y":-1.5},"length":7,"lengthQuality":1,"width":2.59375,"trackAge":255,"orientationRad":3.15292192,"yawRate":0.001953125,"covariance":[1.0078125,0.03125,0.0078125,0.4296875,21.0390625,2.1328125,0.0234375,0.0234375,0],"measurementTime":1745232020.009},{"frameId":1,"objectId":1,"position":{"x":20.1015625,"y":0.75},"velocity":{"x":-1.0703125,"y":-0.015625},"accelerate":{"x":1.125,"y":-1.625},"length":3.34375,"lengthQuality":1,"width":0.6171875,"trackAge":122,"orientationRad":3.15878129,"yawRate":0.0078125,"covariance":[1.015625,0.015625,0.0078125,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.009},{"frameId":2,"objectId":2,"position":{"x":52.2109375,"y":-13.1796875},"velocity":{"x":-7.4296875,"y":-0.1953125},"accelerate":{"x":7.5,"y":-1.625},"length":10.75,"lengthQuality":1,"width":1.4140625,"trackAge":41,"orientationRad":3.16757035,"yawRate":-0.0078125,"covariance":[1.03125,0.03125,0.03125,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.009},{"frameId":3,"objectId":3,"position":{"x":13.0859375,"y":3.90625},"velocity":{"x":-7.5,"y":0.015625},"accelerate":{"x":7.5,"y":-1.5},"length":3.5,"lengthQuality":1,"width":0.7109375,"trackAge":13,"orientationRad":60.855072,"yawRate":0.029296875,"covariance":[1.0625,0.0390625,-0.0078125,0.4296875,21.0546875,2.125,0.015625,0.015625,0],"measurementTime":1745232020.009},{"frameId":4,"objectId":4,"position":{"x":64.4140625,"y":-12.75},"velocity":{"x":-7.5546875,"y":-0.8203125},"accelerate":{"x":7.625,"y":-1.75},"length":5.75,"lengthQuality":1,"width":1.421875,"trackAge":27,"orientationRad":3.24862504,"yawRate":-0.015625,"covariance":[0.96875,0.0625,0.125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.01},{"frameId":5,"objectId":5,"position":{"x":80.4453125,"y":-9.96875},"velocity":{"x":-10.15625,"y":-1.1875},"accelerate":{"x":10.25,"y":5.625},"length":2.5,"lengthQuality":1,"width":1.9921875,"trackAge":7,"orientationRad":3.2574141,"yawRate":0.052734375,"covariance":[1.1796875,0.1171875,0.0546875,0.453125,21.9765625,2.28125,3.171875,3.171875,3],"measurementTime":1745232020.01},{"frameId":6,"objectId":6,"position":{"x":10.28125,"y":-7.328125},"velocity":{"x":-6.7734375,"y":2.984375},"accelerate":{"x":7.375},"length":2.75,"lengthQuality":1,"width":0.59375,"trackAge":68,"orientationRad":60.4419861,"yawRate":-0.108398438,"covariance":[2.09375,1.8203125,0.1796875,0.4296875,21.0390625,2.1328125,1.0703125,1.0703125,1],"measurementTime":1745232020.01},{"frameId":7,"objectId":9,"position":{"x":5.3515625,"y":4.53125},"velocity":{"x":-7.6171875,"y":-0.2734375},"accelerate":{"x":7.625,"y":-1.125},"length":6,"lengthQuality":1,"width":1.65625,"trackAge":95,"orientationRad":-3.10684371,"yawRate":0.00390625,"covariance":[1.015625,0.03125,0.0234375,0.4296875,21.0390625,2.1328125,0.078125,0.078125,0],"measurementTime":1745232020.011},{"frameId":8,"objectId":11,"position":{"x":19.5703125,"y":4.7109375},"velocity":{"x":-7.515625,"y":0.21875},"accelerate":{"x":7.5,"y":-1.625},"length":5.7421875,"lengthQuality":1,"width":1.5234375,"trackAge":33,"orientationRad":60.8287048,"yawRate":0.0234375,"covariance":[1.0078125,0.046875,-0.046875,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.011},{"frameId":9,"objectId":12,"position":{"x":34.7265625,"y":-13.8359375},"velocity":{"x":-7.5,"y":-0.3359375},"accelerate":{"x":7.5,"y":-1.625},"length":8.5,"lengthQuality":1,"width":1.4140625,"trackAge":44,"orientationRad":-3.09805465,"yawRate":-0.0009765625,"covariance":[1,0.0234375,0.046875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.011},{"frameId":10,"objectId":13,"position":{"x":43.4375,"y":-9.328125},"velocity":{"x":-7.46875,"y":0.140625},"accelerate":{"x":7.5,"y":-1.375},"length":3.25,"lengthQuality":1,"width":0.59375,"trackAge":32,"orientationRad":60.8384705,"yawRate":0.033203125,"covariance":[1.0546875,0.078125,-0.0390625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.011},{"position":{"x":16.1484375,"y":-11.2890625},"velocity":{"x":-7.3203125,"y":-0.0546875},"accelerate":{"x":7.375,"y":-1.625},"length":7,"lengthQuality":1,"width":2.59375,"trackAge":255,"orientationRad":3.1480391,"yawRate":-0.009765625,"covariance":[1.0078125,0.03125,0,0.4296875,21.0390625,2.1328125,0.0390625,0.0390625,0],"measurementTime":1745232020.065},{"frameId":1,"objectId":1,"position":{"x":20.046875,"y":0.7421875},"velocity":{"x":-0.953125,"y":-0.015625},"accelerate":{"x":1,"y":-1.875},"length":3.34375,"lengthQuality":1,"width":0.6171875,"trackAge":123,"orientationRad":3.15975785,"yawRate":0.0078125,"covariance":[1.015625,0.015625,0.0078125,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.065},{"frameId":2,"objectId":2,"position":{"x":51.78125,"y":-13.1953125},"velocity":{"x":-7.296875,"y":-0.1953125},"accelerate":{"x":7.375,"y":-1.75},"length":10.75,"lengthQuality":1,"width":1.4140625,"trackAge":42,"orientationRad":3.16757035,"yawRate":-0.00390625,"covariance":[1.03125,0.03125,0.03125,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.065},{"frameId":3,"objectId":3,"position":{"x":13,"y":3.890625},"velocity":{"x":-7.34375},"accelerate":{"x":7.375,"y":-1.75},"length":4.25,"lengthQuality":1,"width":0.71875,"trackAge":14,"orientationRad":60.8570251,"yawRate":0.01953125,"covariance":[1.0546875,0.0390625,-0.0078125,0.4296875,21.0546875,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.066},{"frameId":4,"objectId":4,"position":{"x":63.953125,"y":-12.7890625},"velocity":{"x":-7.4140625,"y":-0.7890625},"accelerate":{"x":7.5,"y":-2},"length":5.75,"lengthQuality":1,"width":1.421875,"trackAge":28,"orientationRad":3.24667192,"yawRate":-0.013671875,"covariance":[0.96875,0.0625,0.1171875,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.066},{"frameId":5,"objectId":5,"position":{"x":79.5625,"y":-10.1015625},"velocity":{"x":-11,"y":-1.484375},"accelerate":{"x":10.875,"y":6.625},"length":2.5,"lengthQuality":1,"width":1.9921875,"trackAge":8,"orientationRad":3.27499223,"yawRate":0.0673828125,"covariance":[1.1875,0.1328125,0.0546875,0.4453125,21.28125,2.1953125,2.7265625,2.7265625,3],"measurementTime":1745232020.066},{"frameId":6,"objectId":6,"position":{"x":9.8984375,"y":-7.1640625},"velocity":{"x":-6.7578125,"y":3.03125},"accelerate":{"x":7.375},"length":2.75,"lengthQuality":1,"width":0.59375,"trackAge":69,"orientationRad":60.4361267,"yawRate":-0.108398438,"covariance":[2.21875,2.015625,0.21875,0.4296875,21.0390625,2.1328125,1.109375,1.109375,1],"measurementTime":1745232020.066},{"frameId":7,"objectId":9,"position":{"x":5.0234375,"y":4.5},"velocity":{"x":-7.3359375,"y":-0.2578125},"accelerate":{"x":7.5,"y":-1.75},"length":6,"lengthQuality":1,"width":1.65625,"trackAge":96,"orientationRad":-3.10782027,"yawRate":0.0048828125,"covariance":[1.015625,0.03125,0.0234375,0.4296875,21.0390625,2.1328125,0.03125,0.03125,0],"measurementTime":1745232020.0669999},{"frameId":8,"objectId":11,"position":{"x":19.1796875,"y":4.734375},"velocity":{"x":-7.3984375,"y":0.2109375},"accelerate":{"x":7.375,"y":-1.75},"length":5.7421875,"lengthQuality":1,"width":1.5234375,"trackAge":34,"orientationRad":60.8287048,"yawRate":0.0234375,"covariance":[1.0078125,0.046875,-0.046875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.0669999},{"frameId":9,"objectId":12,"position":{"x":34.265625,"y":-13.859375},"velocity":{"x":-7.359375,"y":-0.328125},"accelerate":{"x":7.375,"y":-1.75},"length":8.5,"lengthQuality":1,"width":1.421875,"trackAge":45,"orientationRad":-3.09805465,"yawRate":-0.005859375,"covariance":[1,0.0234375,0.046875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.0669999},{"frameId":10,"objectId":13,"position":{"x":43.0703125,"y":-9.3828125},"velocity":{"x":-7.46875,"y":0.09375},"accelerate":{"x":7.375,"y":-1.25},"length":3.25,"lengthQuality":1,"width":0.59375,"trackAge":33,"orientationRad":60.8443298,"yawRate":0.0322265625,"covariance":[1.046875,0.078125,-0.0390625,0.4296875,21.0390625,2.1328125,0.046875,0.046875,0],"measurementTime":1745232020.0669999},{"position":{"x":15.734375,"y":-11.2890625},"velocity":{"x":-7.203125,"y":-0.046875},"accelerate":{"x":7.25,"y":-1.75},"length":7,"lengthQuality":1,"width":2.59375,"trackAge":255,"orientationRad":3.14706254,"yawRate":-0.015625,"covariance":[1.015625,0.03125,0,0.4296875,21.0390625,2.1328125,0.046875,0.046875,0],"measurementTime":1745232020.122},{"frameId":1,"objectId":1,"position":{"x":19.9921875,"y":0.7421875},"velocity":{"x":-0.8359375,"y":-0.015625},"accelerate":{"x":0.875,"y":-1.875},"length":3.34375,"lengthQuality":1,"width":0.6171875,"trackAge":124,"orientationRad":3.15975785,"yawRate":0.0078125,"covariance":[1.015625,0.015625,0.0078125,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.122},{"frameId":2,"objectId":2,"position":{"x":51.40625,"y":-13.203125},"velocity":{"x":-7.171875,"y":-0.1875},"accelerate":{"x":7.25,"y":-2},"length":10.75,"lengthQuality":1,"width":1.4140625,"trackAge":43,"orientationRad":3.16757035,"yawRate":-0.0009765625,"covariance":[1.03125,0.03125,0.03125,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.1229999},{"frameId":3,"objectId":3,"position":{"x":12.609375,"y":3.84375},"velocity":{"x":-7.21875,"y":-0.0390625},"accelerate":{"x":7.25,"y":-2},"length":4.25,"lengthQuality":1,"width":0.734375,"trackAge":15,"orientationRad":-3.13711715,"yawRate":0.01953125,"covariance":[1.0546875,0.03125,-0.0078125,0.4296875,21.0546875,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.1229999},{"frameId":4,"objectId":4,"position":{"x":63.546875,"y":-12.8203125},"velocity":{"x":-7.296875,"y":-0.7734375},"accelerate":{"x":7.375,"y":-2.125},"length":5.75,"lengthQuality":1,"width":1.421875,"trackAge":29,"orientationRad":3.24569535,"yawRate":-0.0146484375,"covariance":[0.9765625,0.0625,0.1171875,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1745232020.1229999},{"frameId":5,"objectId":5,"position":{"x":78.96875,"y":-10.2421875},"velocity":{"x":-10.9140625,"y":-1.671875},"accelerate":{"x":11.125,"y":6.25},"length":2.5,"lengthQuality":1,"width":1.9921875,"trackAge":9,"orientationRad":3.29257035,"yawRate":0.0791015625,"covariance":[1.21875,0.1484375,0.078125,0.4375,21.03125,2.1484375,2.53125,2.53125,3],"measurementTime":1745232020.1229999},{"frameId":6,"objectId":6,"position":{"x":9.515625,"y":-6.9921875},"velocity":{"x":-6.7421875,"y":3.0703125},"accelerate":{"x":7.375},"length":2.75,"lengthQuality":1,"width":0.59375,"trackAge":70,"orientationRad":60.4302673,"yawRate":-0.108398438,"covariance":[2.3515625,2.21875,0.2734375,0.4296875,21.0390625,2.1328125,1.1484375,1.1484375,1],"measurementTime":1745232020.124},{"frameId":7,"objectId":9,"position":{"x":4.6875,"y":4.484375},"velocity":{"x":-7.265625,"y":-0.25},"accelerate":{"x":7.375,"y":-2},"length":6,"lengthQuality":1,"width":1.65625,"trackAge":97,"orientationRad":-3.10879683,"yawRate":0.001953125,"covariance":[1.015625,0.0390625,0.03125,0.4296875,21.0390625,2.1328125,0.0390625,0.0390625,0],"measurementTime":1745232020.124},{"frameId":8,"objectId":11,"position":{"x":18.796875,"y":4.6953125},"velocity":{"x":-7.2578125,"y":0.171875},"accelerate":{"x":7.25,"y":-2},"length":5.7421875,"lengthQuality":1,"width":1.5234375,"trackAge":35,"orientationRad":60.8335876,"yawRate":0.0234375,"covariance":[1.0078125,0.046875,-0.0390625,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.124},{"frameId":9,"objectId":12,"position":{"x":33.6953125,"y":-13.890625},"velocity":{"x":-7.265625,"y":-0.328125},"accelerate":{"x":7.25,"y":-1.875},"length":8.25,"lengthQuality":1,"width":1.421875,"trackAge":46,"orientationRad":-3.09805465,"yawRate":-0.0048828125,"covariance":[1,0.0234375,0.046875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1745232020.125}],"measurementTime":1054648})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    EXPECT_NO_THROW(g_aeb_component->proc());
}

TEST_F(AEBServerTest, Test_proc_fcw_latent_warning) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":27.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "position": {
                    "x": 16.5703125,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3828125,
                    "y": -0.0859375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.15292192,
                "yawRate": 0.001953125,
                "covariance": [1.0078125, 0.03125, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0234375, 0.0234375, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.1015625,
                    "y": 0.75
                },
                "velocity": {
                    "x": -1.0703125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1.125,
                    "y": -1.625
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 122,
                "orientationRad": 3.15878129,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 52.2109375,
                    "y": -13.1796875
                },
                "velocity": {
                    "x": -7.4296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 41,
                "orientationRad": 3.16757035,
                "yawRate": -0.0078125,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13.0859375,
                    "y": 3.90625
                },
                "velocity": {
                    "x": -7.5,
                    "y": 0.015625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 3.5,
                "lengthQuality": 1,
                "width": 0.7109375,
                "trackAge": 13,
                "orientationRad": 60.855072,
                "yawRate": 0.029296875,
                "covariance": [1.0625, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.125, 0.015625, 0.015625, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 64.4140625,
                    "y": -12.75
                },
                "velocity": {
                    "x": -7.5546875,
                    "y": -0.8203125
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.75
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 27,
                "orientationRad": 3.24862504,
                "yawRate": -0.015625,
                "covariance": [0.96875, 0.0625, 0.125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 80.4453125,
                    "y": -9.96875
                },
                "velocity": {
                    "x": -10.15625,
                    "y": -1.1875
                },
                "accelerate": {
                    "x": 10.25,
                    "y": 5.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 7,
                "orientationRad": 3.2574141,
                "yawRate": 0.052734375,
                "covariance": [1.1796875, 0.1171875, 0.0546875, 0.453125, 21.9765625, 2.28125, 3.171875, 3.171875, 3],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 30.28125,
                    "y": -7.328125
                },
                "velocity": {
                    "x": -6.7734375,
                    "y": 2.984375
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 68,
                "orientationRad": 60.4419861,
                "yawRate": -0.108398438,
                "covariance": [2.09375, 1.8203125, 0.1796875, 0.4296875, 21.0390625, 2.1328125, 1.0703125, 1.0703125, 1],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.3515625,
                    "y": 4.53125
                },
                "velocity": {
                    "x": -7.6171875,
                    "y": -0.2734375
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.125
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 95,
                "orientationRad": -3.10684371,
                "yawRate": 0.00390625,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.078125, 0.078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.5703125,
                    "y": 4.7109375
                },
                "velocity": {
                    "x": -7.515625,
                    "y": 0.21875
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 33,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.7265625,
                    "y": -13.8359375
                },
                "velocity": {
                    "x": -7.5,
                    "y": -0.3359375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 44,
                "orientationRad": -3.09805465,
                "yawRate": -0.0009765625,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.4375,
                    "y": -9.328125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.140625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.375
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 32,
                "orientationRad": 60.8384705,
                "yawRate": 0.033203125,
                "covariance": [1.0546875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "position": {
                    "x": 16.1484375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3203125,
                    "y": -0.0546875
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.625
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.1480391,
                "yawRate": -0.009765625,
                "covariance": [1.0078125, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.046875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.953125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 123,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.78125,
                    "y": -13.1953125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 42,
                "orientationRad": 3.16757035,
                "yawRate": -0.00390625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13,
                    "y": 3.890625
                },
                "velocity": {
                    "x": -7.34375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.71875,
                "trackAge": 14,
                "orientationRad": 60.8570251,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.953125,
                    "y": -12.7890625
                },
                "velocity": {
                    "x": -7.4140625,
                    "y": -0.7890625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -2
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 28,
                "orientationRad": 3.24667192,
                "yawRate": -0.013671875,
                "covariance": [0.96875, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 79.5625,
                    "y": -10.1015625
                },
                "velocity": {
                    "x": -11,
                    "y": -1.484375
                },
                "accelerate": {
                    "x": 10.875,
                    "y": 6.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 8,
                "orientationRad": 3.27499223,
                "yawRate": 0.0673828125,
                "covariance": [1.1875, 0.1328125, 0.0546875, 0.4453125, 21.28125, 2.1953125, 2.7265625, 2.7265625, 3],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 30.8984375,
                    "y": -7.1640625
                },
                "velocity": {
                    "x": -6.7578125,
                    "y": 3.03125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 69,
                "orientationRad": 60.4361267,
                "yawRate": -0.108398438,
                "covariance": [2.21875, 2.015625, 0.21875, 0.4296875, 21.0390625, 2.1328125, 1.109375, 1.109375, 1],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.0234375,
                    "y": 4.5
                },
                "velocity": {
                    "x": -7.3359375,
                    "y": -0.2578125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.75
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 96,
                "orientationRad": -3.10782027,
                "yawRate": 0.0048828125,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.03125, 0.03125, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.1796875,
                    "y": 4.734375
                },
                "velocity": {
                    "x": -7.3984375,
                    "y": 0.2109375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 34,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.265625,
                    "y": -13.859375
                },
                "velocity": {
                    "x": -7.359375,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 45,
                "orientationRad": -3.09805465,
                "yawRate": -0.005859375,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.0703125,
                    "y": -9.3828125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.09375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.25
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 33,
                "orientationRad": 60.8443298,
                "yawRate": 0.0322265625,
                "covariance": [1.046875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "position": {
                    "x": 15.734375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.203125,
                    "y": -0.046875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.75
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.14706254,
                "yawRate": -0.015625,
                "covariance": [1.015625, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 19.9921875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.8359375,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 0.875,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 124,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.40625,
                    "y": -13.203125
                },
                "velocity": {
                    "x": -7.171875,
                    "y": -0.1875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 43,
                "orientationRad": 3.16757035,
                "yawRate": -0.0009765625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 12.609375,
                    "y": 3.84375
                },
                "velocity": {
                    "x": -7.21875,
                    "y": -0.0390625
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.734375,
                "trackAge": 15,
                "orientationRad": -3.13711715,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.03125, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.546875,
                    "y": -12.8203125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.7734375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2.125
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 29,
                "orientationRad": 3.24569535,
                "yawRate": -0.0146484375,
                "covariance": [0.9765625, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 78.96875,
                    "y": -10.2421875
                },
                "velocity": {
                    "x": -10.9140625,
                    "y": -1.671875
                },
                "accelerate": {
                    "x": 11.125,
                    "y": 6.25
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 9,
                "orientationRad": 3.29257035,
                "yawRate": 0.0791015625,
                "covariance": [1.21875, 0.1484375, 0.078125, 0.4375, 21.03125, 2.1484375, 2.53125, 2.53125, 3],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 30.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -6.7421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 4.6875,
                    "y": 4.484375
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.25
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 97,
                "orientationRad": -3.10879683,
                "yawRate": 0.001953125,
                "covariance": [1.015625, 0.0390625, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 18.796875,
                    "y": 4.6953125
                },
                "velocity": {
                    "x": -7.2578125,
                    "y": 0.171875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 35,
                "orientationRad": 60.8335876,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 33.6953125,
                    "y": -13.890625
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.875
                },
                "length": 8.25,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 46,
                "orientationRad": -3.09805465,
                "yawRate": -0.0048828125,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.125
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    testing::internal::CaptureStderr();
    // 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_LATENT_WARNING");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 1);
}

TEST_F(AEBServerTest, Test_proc_pre_warning) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":9.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "position": {
                    "x": 16.5703125,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3828125,
                    "y": -0.0859375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.15292192,
                "yawRate": 0.001953125,
                "covariance": [1.0078125, 0.03125, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0234375, 0.0234375, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.1015625,
                    "y": 0.75
                },
                "velocity": {
                    "x": -1.0703125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1.125,
                    "y": -1.625
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 122,
                "orientationRad": 3.15878129,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 52.2109375,
                    "y": -13.1796875
                },
                "velocity": {
                    "x": -7.4296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 41,
                "orientationRad": 3.16757035,
                "yawRate": -0.0078125,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13.0859375,
                    "y": 3.90625
                },
                "velocity": {
                    "x": -7.5,
                    "y": 0.015625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 3.5,
                "lengthQuality": 1,
                "width": 0.7109375,
                "trackAge": 13,
                "orientationRad": 60.855072,
                "yawRate": 0.029296875,
                "covariance": [1.0625, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.125, 0.015625, 0.015625, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 64.4140625,
                    "y": -12.75
                },
                "velocity": {
                    "x": -7.5546875,
                    "y": -0.8203125
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.75
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 27,
                "orientationRad": 3.24862504,
                "yawRate": -0.015625,
                "covariance": [0.96875, 0.0625, 0.125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 80.4453125,
                    "y": -9.96875
                },
                "velocity": {
                    "x": -10.15625,
                    "y": -1.1875
                },
                "accelerate": {
                    "x": 10.25,
                    "y": 5.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 7,
                "orientationRad": 3.2574141,
                "yawRate": 0.052734375,
                "covariance": [1.1796875, 0.1171875, 0.0546875, 0.453125, 21.9765625, 2.28125, 3.171875, 3.171875, 3],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 30.28125,
                    "y": -7.328125
                },
                "velocity": {
                    "x": -6.7734375,
                    "y": 2.984375
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 68,
                "orientationRad": 60.4419861,
                "yawRate": -0.108398438,
                "covariance": [2.09375, 1.8203125, 0.1796875, 0.4296875, 21.0390625, 2.1328125, 1.0703125, 1.0703125, 1],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.3515625,
                    "y": 4.53125
                },
                "velocity": {
                    "x": -7.6171875,
                    "y": -0.2734375
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.125
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 95,
                "orientationRad": -3.10684371,
                "yawRate": 0.00390625,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.078125, 0.078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.5703125,
                    "y": 4.7109375
                },
                "velocity": {
                    "x": -7.515625,
                    "y": 0.21875
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 33,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.7265625,
                    "y": -13.8359375
                },
                "velocity": {
                    "x": -7.5,
                    "y": -0.3359375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 44,
                "orientationRad": -3.09805465,
                "yawRate": -0.0009765625,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.4375,
                    "y": -9.328125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.140625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.375
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 32,
                "orientationRad": 60.8384705,
                "yawRate": 0.033203125,
                "covariance": [1.0546875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "position": {
                    "x": 16.1484375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3203125,
                    "y": -0.0546875
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.625
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.1480391,
                "yawRate": -0.009765625,
                "covariance": [1.0078125, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.046875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.953125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 123,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.78125,
                    "y": -13.1953125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 42,
                "orientationRad": 3.16757035,
                "yawRate": -0.00390625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13,
                    "y": 3.890625
                },
                "velocity": {
                    "x": -7.34375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.71875,
                "trackAge": 14,
                "orientationRad": 60.8570251,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.953125,
                    "y": -12.7890625
                },
                "velocity": {
                    "x": -7.4140625,
                    "y": -0.7890625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -2
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 28,
                "orientationRad": 3.24667192,
                "yawRate": -0.013671875,
                "covariance": [0.96875, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 79.5625,
                    "y": -10.1015625
                },
                "velocity": {
                    "x": -11,
                    "y": -1.484375
                },
                "accelerate": {
                    "x": 10.875,
                    "y": 6.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 8,
                "orientationRad": 3.27499223,
                "yawRate": 0.0673828125,
                "covariance": [1.1875, 0.1328125, 0.0546875, 0.4453125, 21.28125, 2.1953125, 2.7265625, 2.7265625, 3],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 30.8984375,
                    "y": -7.1640625
                },
                "velocity": {
                    "x": -6.7578125,
                    "y": 3.03125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 69,
                "orientationRad": 60.4361267,
                "yawRate": -0.108398438,
                "covariance": [2.21875, 2.015625, 0.21875, 0.4296875, 21.0390625, 2.1328125, 1.109375, 1.109375, 1],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.0234375,
                    "y": 4.5
                },
                "velocity": {
                    "x": -7.3359375,
                    "y": -0.2578125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.75
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 96,
                "orientationRad": -3.10782027,
                "yawRate": 0.0048828125,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.03125, 0.03125, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.1796875,
                    "y": 4.734375
                },
                "velocity": {
                    "x": -7.3984375,
                    "y": 0.2109375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 34,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.265625,
                    "y": -13.859375
                },
                "velocity": {
                    "x": -7.359375,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 45,
                "orientationRad": -3.09805465,
                "yawRate": -0.005859375,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.0703125,
                    "y": -9.3828125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.09375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.25
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 33,
                "orientationRad": 60.8443298,
                "yawRate": 0.0322265625,
                "covariance": [1.046875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "position": {
                    "x": 15.734375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.203125,
                    "y": -0.046875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.75
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.14706254,
                "yawRate": -0.015625,
                "covariance": [1.015625, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 19.9921875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -10.8359375,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 0.875,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 124,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.40625,
                    "y": -13.203125
                },
                "velocity": {
                    "x": -7.171875,
                    "y": -0.1875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 43,
                "orientationRad": 3.16757035,
                "yawRate": -0.0009765625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 12.609375,
                    "y": 3.84375
                },
                "velocity": {
                    "x": -7.21875,
                    "y": -0.0390625
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.734375,
                "trackAge": 15,
                "orientationRad": -3.13711715,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.03125, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.546875,
                    "y": -12.8203125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.7734375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2.125
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 29,
                "orientationRad": 3.24569535,
                "yawRate": -0.0146484375,
                "covariance": [0.9765625, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 78.96875,
                    "y": -10.2421875
                },
                "velocity": {
                    "x": -10.9140625,
                    "y": -1.671875
                },
                "accelerate": {
                    "x": 11.125,
                    "y": 6.25
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 9,
                "orientationRad": 3.29257035,
                "yawRate": 0.0791015625,
                "covariance": [1.21875, 0.1484375, 0.078125, 0.4375, 21.03125, 2.1484375, 2.53125, 2.53125, 3],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 30.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -6.7421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 4.6875,
                    "y": 4.484375
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.25
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 97,
                "orientationRad": -3.10879683,
                "yawRate": 0.001953125,
                "covariance": [1.015625, 0.0390625, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 18.796875,
                    "y": 4.6953125
                },
                "velocity": {
                    "x": -7.2578125,
                    "y": 0.171875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 35,
                "orientationRad": 60.8335876,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 33.6953125,
                    "y": -13.890625
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.875
                },
                "length": 8.25,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 46,
                "orientationRad": -3.09805465,
                "yawRate": -0.0048828125,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.125
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    testing::internal::CaptureStderr();
    // 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_PRE_WARNING");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 4);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
}

TEST_F(AEBServerTest, Test_proc_acute_warning) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":9.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "position": {
                    "x": 16.5703125,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3828125,
                    "y": -0.0859375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.15292192,
                "yawRate": 0.001953125,
                "covariance": [1.0078125, 0.03125, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0234375, 0.0234375, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.1015625,
                    "y": 0.75
                },
                "velocity": {
                    "x": -1.0703125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1.125,
                    "y": -1.625
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 122,
                "orientationRad": 3.15878129,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 52.2109375,
                    "y": -13.1796875
                },
                "velocity": {
                    "x": -7.4296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 41,
                "orientationRad": 3.16757035,
                "yawRate": -0.0078125,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13.0859375,
                    "y": 3.90625
                },
                "velocity": {
                    "x": -7.5,
                    "y": 0.015625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 3.5,
                "lengthQuality": 1,
                "width": 0.7109375,
                "trackAge": 13,
                "orientationRad": 60.855072,
                "yawRate": 0.029296875,
                "covariance": [1.0625, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.125, 0.015625, 0.015625, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 64.4140625,
                    "y": -12.75
                },
                "velocity": {
                    "x": -7.5546875,
                    "y": -0.8203125
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.75
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 27,
                "orientationRad": 3.24862504,
                "yawRate": -0.015625,
                "covariance": [0.96875, 0.0625, 0.125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 80.4453125,
                    "y": -9.96875
                },
                "velocity": {
                    "x": -10.15625,
                    "y": -1.1875
                },
                "accelerate": {
                    "x": 10.25,
                    "y": 5.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 7,
                "orientationRad": 3.2574141,
                "yawRate": 0.052734375,
                "covariance": [1.1796875, 0.1171875, 0.0546875, 0.453125, 21.9765625, 2.28125, 3.171875, 3.171875, 3],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 10.28125,
                    "y": -7.328125
                },
                "velocity": {
                    "x": -6.7734375,
                    "y": 2.984375
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 68,
                "orientationRad": 60.4419861,
                "yawRate": -0.108398438,
                "covariance": [2.09375, 1.8203125, 0.1796875, 0.4296875, 21.0390625, 2.1328125, 1.0703125, 1.0703125, 1],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.3515625,
                    "y": 4.53125
                },
                "velocity": {
                    "x": -7.6171875,
                    "y": -0.2734375
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.125
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 95,
                "orientationRad": -3.10684371,
                "yawRate": 0.00390625,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.078125, 0.078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.5703125,
                    "y": 4.7109375
                },
                "velocity": {
                    "x": -7.515625,
                    "y": 0.21875
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 33,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.7265625,
                    "y": -13.8359375
                },
                "velocity": {
                    "x": -7.5,
                    "y": -0.3359375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 44,
                "orientationRad": -3.09805465,
                "yawRate": -0.0009765625,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.4375,
                    "y": -9.328125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.140625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.375
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 32,
                "orientationRad": 60.8384705,
                "yawRate": 0.033203125,
                "covariance": [1.0546875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "position": {
                    "x": 16.1484375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3203125,
                    "y": -0.0546875
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.625
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.1480391,
                "yawRate": -0.009765625,
                "covariance": [1.0078125, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.046875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.953125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 123,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.78125,
                    "y": -13.1953125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 42,
                "orientationRad": 3.16757035,
                "yawRate": -0.00390625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13,
                    "y": 3.890625
                },
                "velocity": {
                    "x": -7.34375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.71875,
                "trackAge": 14,
                "orientationRad": 60.8570251,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.953125,
                    "y": -12.7890625
                },
                "velocity": {
                    "x": -7.4140625,
                    "y": -0.7890625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -2
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 28,
                "orientationRad": 3.24667192,
                "yawRate": -0.013671875,
                "covariance": [0.96875, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 79.5625,
                    "y": -10.1015625
                },
                "velocity": {
                    "x": -11,
                    "y": -1.484375
                },
                "accelerate": {
                    "x": 10.875,
                    "y": 6.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 8,
                "orientationRad": 3.27499223,
                "yawRate": 0.0673828125,
                "covariance": [1.1875, 0.1328125, 0.0546875, 0.4453125, 21.28125, 2.1953125, 2.7265625, 2.7265625, 3],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.8984375,
                    "y": -7.1640625
                },
                "velocity": {
                    "x": -6.7578125,
                    "y": 3.03125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 69,
                "orientationRad": 60.4361267,
                "yawRate": -0.108398438,
                "covariance": [2.21875, 2.015625, 0.21875, 0.4296875, 21.0390625, 2.1328125, 1.109375, 1.109375, 1],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.0234375,
                    "y": 4.5
                },
                "velocity": {
                    "x": -7.3359375,
                    "y": -0.2578125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.75
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 96,
                "orientationRad": -3.10782027,
                "yawRate": 0.0048828125,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.03125, 0.03125, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.1796875,
                    "y": 4.734375
                },
                "velocity": {
                    "x": -7.3984375,
                    "y": 0.2109375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 34,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.265625,
                    "y": -13.859375
                },
                "velocity": {
                    "x": -7.359375,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 45,
                "orientationRad": -3.09805465,
                "yawRate": -0.005859375,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.0703125,
                    "y": -9.3828125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.09375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.25
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 33,
                "orientationRad": 60.8443298,
                "yawRate": 0.0322265625,
                "covariance": [1.046875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "position": {
                    "x": 15.734375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.203125,
                    "y": -0.046875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.75
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.14706254,
                "yawRate": -0.015625,
                "covariance": [1.015625, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 19.9921875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -10.8359375,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 0.875,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 124,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.40625,
                    "y": -13.203125
                },
                "velocity": {
                    "x": -7.171875,
                    "y": -0.1875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 43,
                "orientationRad": 3.16757035,
                "yawRate": -0.0009765625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 12.609375,
                    "y": 3.84375
                },
                "velocity": {
                    "x": -7.21875,
                    "y": -0.0390625
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.734375,
                "trackAge": 15,
                "orientationRad": -3.13711715,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.03125, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.546875,
                    "y": -12.8203125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.7734375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2.125
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 29,
                "orientationRad": 3.24569535,
                "yawRate": -0.0146484375,
                "covariance": [0.9765625, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 78.96875,
                    "y": -10.2421875
                },
                "velocity": {
                    "x": -10.9140625,
                    "y": -1.671875
                },
                "accelerate": {
                    "x": 11.125,
                    "y": 6.25
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 9,
                "orientationRad": 3.29257035,
                "yawRate": 0.0791015625,
                "covariance": [1.21875, 0.1484375, 0.078125, 0.4375, 21.03125, 2.1484375, 2.53125, 2.53125, 3],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -6.7421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 4.6875,
                    "y": 4.484375
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.25
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 97,
                "orientationRad": -3.10879683,
                "yawRate": 0.001953125,
                "covariance": [1.015625, 0.0390625, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 18.796875,
                    "y": 4.6953125
                },
                "velocity": {
                    "x": -7.2578125,
                    "y": 0.171875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 35,
                "orientationRad": 60.8335876,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 33.6953125,
                    "y": -13.890625
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.875
                },
                "length": 8.25,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 46,
                "orientationRad": -3.09805465,
                "yawRate": -0.0048828125,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.125
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->proc();
    testing::internal::CaptureStderr();// 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
}
// 新法雷奥数据，自车直线接近前车
TEST_F(AEBServerTest, Test_proc_eba_level_2) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748933446.9434609,"moduleName":"io_server","sequenceNum":10849},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":9.4125002119276257,"wheelSpeedFl":7.3125002119276257,"wheelSpeedFr":7.3125002119276257,"wheelSpeedRl":7.3125002119276257,"wheelSpeedRr":7.3281251059638128},"brakePercentage":29,"acceleration":{"longitudinal":-1.25,"lateral":0.10999999940395355},"yawRate":0.00680678478,"steeringSystem":{"steeringWheelAngle":3.****************,"steeringWheelTorque":0.67000001668930054},"curTorque":-848,"slopeEstimation":-0.00147304614,"massEstimation":2489.05444})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748933447.280272,"moduleName":"io_server","sequenceNum":2158},"detectionList":[{"objectId":2,"position":{"x":8.1796875,"y":-0.59375},"velocity":3.703125,"accelerate":-0.1015625,"length":4.0390625,"lengthCovariance":1,"width":1.0703125,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.0165837444,"orientationCovariance":0.015625,"yawRate":0.00390625,"yawRateCovariance":0.0078125,"covariance":[1.015625,0.203125,-0.0390625,21.0390625,0.0234375],"measurementTime":1748933447.192},{"objectId":2,"position":{"x":8.0234375,"y":-0.59375},"velocity":3.7109375,"accelerate":-0.046875,"length":4.0390625,"lengthCovariance":1,"width":1.0703125,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.0156071819,"orientationCovariance":0.015625,"yawRate":0.00390625,"yawRateCovariance":0.0078125,"covariance":[1.015625,0.203125,-0.03125,21.0390625,0.0234375],"measurementTime":1748933447.248}],"measurementTime":2148384})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    g_aeb_component->proc();
    g_aeb_component->proc();
    testing::internal::CaptureStderr();// 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL3");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
}

TEST_F(AEBServerTest, Test_proc_eba_level_3) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":19.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "position": {
                    "x": 16.5703125,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3828125,
                    "y": -0.0859375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.15292192,
                "yawRate": 0.001953125,
                "covariance": [1.0078125, 0.03125, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0234375, 0.0234375, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.1015625,
                    "y": 0.75
                },
                "velocity": {
                    "x": -1.0703125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1.125,
                    "y": -1.625
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 122,
                "orientationRad": 3.15878129,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 52.2109375,
                    "y": -13.1796875
                },
                "velocity": {
                    "x": -7.4296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 41,
                "orientationRad": 3.16757035,
                "yawRate": -0.0078125,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13.0859375,
                    "y": 3.90625
                },
                "velocity": {
                    "x": -7.5,
                    "y": 0.015625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 3.5,
                "lengthQuality": 1,
                "width": 0.7109375,
                "trackAge": 13,
                "orientationRad": 60.855072,
                "yawRate": 0.029296875,
                "covariance": [1.0625, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.125, 0.015625, 0.015625, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 64.4140625,
                    "y": -12.75
                },
                "velocity": {
                    "x": -7.5546875,
                    "y": -0.8203125
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.75
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 27,
                "orientationRad": 3.24862504,
                "yawRate": -0.015625,
                "covariance": [0.96875, 0.0625, 0.125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 80.4453125,
                    "y": -9.96875
                },
                "velocity": {
                    "x": -10.15625,
                    "y": -1.1875
                },
                "accelerate": {
                    "x": 10.25,
                    "y": 5.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 7,
                "orientationRad": 3.2574141,
                "yawRate": 0.052734375,
                "covariance": [1.1796875, 0.1171875, 0.0546875, 0.453125, 21.9765625, 2.28125, 3.171875, 3.171875, 3],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 10.28125,
                    "y": -7.328125
                },
                "velocity": {
                    "x": -6.7734375,
                    "y": 2.984375
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 68,
                "orientationRad": 60.4419861,
                "yawRate": -0.108398438,
                "covariance": [2.09375, 1.8203125, 0.1796875, 0.4296875, 21.0390625, 2.1328125, 1.0703125, 1.0703125, 1],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.3515625,
                    "y": 4.53125
                },
                "velocity": {
                    "x": -7.6171875,
                    "y": -0.2734375
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.125
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 95,
                "orientationRad": -3.10684371,
                "yawRate": 0.00390625,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.078125, 0.078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.5703125,
                    "y": 4.7109375
                },
                "velocity": {
                    "x": -7.515625,
                    "y": 0.21875
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 33,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.7265625,
                    "y": -13.8359375
                },
                "velocity": {
                    "x": -7.5,
                    "y": -0.3359375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 44,
                "orientationRad": -3.09805465,
                "yawRate": -0.0009765625,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.4375,
                    "y": -9.328125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.140625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.375
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 32,
                "orientationRad": 60.8384705,
                "yawRate": 0.033203125,
                "covariance": [1.0546875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "position": {
                    "x": 16.1484375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3203125,
                    "y": -0.0546875
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.625
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.1480391,
                "yawRate": -0.009765625,
                "covariance": [1.0078125, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.046875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.953125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 123,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.78125,
                    "y": -13.1953125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 42,
                "orientationRad": 3.16757035,
                "yawRate": -0.00390625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13,
                    "y": 3.890625
                },
                "velocity": {
                    "x": -7.34375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.71875,
                "trackAge": 14,
                "orientationRad": 60.8570251,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.953125,
                    "y": -12.7890625
                },
                "velocity": {
                    "x": -7.4140625,
                    "y": -0.7890625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -2
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 28,
                "orientationRad": 3.24667192,
                "yawRate": -0.013671875,
                "covariance": [0.96875, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 79.5625,
                    "y": -10.1015625
                },
                "velocity": {
                    "x": -11,
                    "y": -1.484375
                },
                "accelerate": {
                    "x": 10.875,
                    "y": 6.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 8,
                "orientationRad": 3.27499223,
                "yawRate": 0.0673828125,
                "covariance": [1.1875, 0.1328125, 0.0546875, 0.4453125, 21.28125, 2.1953125, 2.7265625, 2.7265625, 3],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.8984375,
                    "y": -7.1640625
                },
                "velocity": {
                    "x": -6.7578125,
                    "y": 3.03125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 69,
                "orientationRad": 60.4361267,
                "yawRate": -0.108398438,
                "covariance": [2.21875, 2.015625, 0.21875, 0.4296875, 21.0390625, 2.1328125, 1.109375, 1.109375, 1],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.0234375,
                    "y": 4.5
                },
                "velocity": {
                    "x": -7.3359375,
                    "y": -0.2578125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.75
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 96,
                "orientationRad": -3.10782027,
                "yawRate": 0.0048828125,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.03125, 0.03125, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.1796875,
                    "y": 4.734375
                },
                "velocity": {
                    "x": -7.3984375,
                    "y": 0.2109375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 34,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.265625,
                    "y": -13.859375
                },
                "velocity": {
                    "x": -7.359375,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 45,
                "orientationRad": -3.09805465,
                "yawRate": -0.005859375,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.0703125,
                    "y": -9.3828125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.09375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.25
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 33,
                "orientationRad": 60.8443298,
                "yawRate": 0.0322265625,
                "covariance": [1.046875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "position": {
                    "x": 15.734375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.203125,
                    "y": -0.046875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.75
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.14706254,
                "yawRate": -0.015625,
                "covariance": [1.015625, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 19.9921875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -10.8359375,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 0.875,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 124,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.40625,
                    "y": -13.203125
                },
                "velocity": {
                    "x": -7.171875,
                    "y": -0.1875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 43,
                "orientationRad": 3.16757035,
                "yawRate": -0.0009765625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 12.609375,
                    "y": 3.84375
                },
                "velocity": {
                    "x": -7.21875,
                    "y": -0.0390625
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.734375,
                "trackAge": 15,
                "orientationRad": -3.13711715,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.03125, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.546875,
                    "y": -12.8203125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.7734375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2.125
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 29,
                "orientationRad": 3.24569535,
                "yawRate": -0.0146484375,
                "covariance": [0.9765625, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 78.96875,
                    "y": -10.2421875
                },
                "velocity": {
                    "x": -10.9140625,
                    "y": -1.671875
                },
                "accelerate": {
                    "x": 11.125,
                    "y": 6.25
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 9,
                "orientationRad": 3.29257035,
                "yawRate": 0.0791015625,
                "covariance": [1.21875, 0.1484375, 0.078125, 0.4375, 21.03125, 2.1484375, 2.53125, 2.53125, 3],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -6.7421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 4.6875,
                    "y": 4.484375
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.25
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 97,
                "orientationRad": -3.10879683,
                "yawRate": 0.001953125,
                "covariance": [1.015625, 0.0390625, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 18.796875,
                    "y": 4.6953125
                },
                "velocity": {
                    "x": -7.2578125,
                    "y": 0.171875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 35,
                "orientationRad": 60.8335876,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 33.6953125,
                    "y": -13.890625
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.875
                },
                "length": 8.25,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 46,
                "orientationRad": -3.09805465,
                "yawRate": -0.0048828125,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.125
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->proc();
    g_aeb_component->proc();
    testing::internal::CaptureStderr();// 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
}

TEST_F(AEBServerTest, Test_proc_exit_eba_level_3) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":19.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "position": {
                    "x": 16.5703125,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3828125,
                    "y": -0.0859375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.15292192,
                "yawRate": 0.001953125,
                "covariance": [1.0078125, 0.03125, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0234375, 0.0234375, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.1015625,
                    "y": 0.75
                },
                "velocity": {
                    "x": -1.0703125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1.125,
                    "y": -1.625
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 122,
                "orientationRad": 3.15878129,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 52.2109375,
                    "y": -13.1796875
                },
                "velocity": {
                    "x": -7.4296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 41,
                "orientationRad": 3.16757035,
                "yawRate": -0.0078125,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13.0859375,
                    "y": 3.90625
                },
                "velocity": {
                    "x": -7.5,
                    "y": 0.015625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 3.5,
                "lengthQuality": 1,
                "width": 0.7109375,
                "trackAge": 13,
                "orientationRad": 60.855072,
                "yawRate": 0.029296875,
                "covariance": [1.0625, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.125, 0.015625, 0.015625, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 64.4140625,
                    "y": -12.75
                },
                "velocity": {
                    "x": -7.5546875,
                    "y": -0.8203125
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.75
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 27,
                "orientationRad": 3.24862504,
                "yawRate": -0.015625,
                "covariance": [0.96875, 0.0625, 0.125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 80.4453125,
                    "y": -9.96875
                },
                "velocity": {
                    "x": -10.15625,
                    "y": -1.1875
                },
                "accelerate": {
                    "x": 10.25,
                    "y": 5.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 7,
                "orientationRad": 3.2574141,
                "yawRate": 0.052734375,
                "covariance": [1.1796875, 0.1171875, 0.0546875, 0.453125, 21.9765625, 2.28125, 3.171875, 3.171875, 3],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 10.28125,
                    "y": -7.328125
                },
                "velocity": {
                    "x": -6.7734375,
                    "y": 2.984375
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 68,
                "orientationRad": 60.4419861,
                "yawRate": -0.108398438,
                "covariance": [2.09375, 1.8203125, 0.1796875, 0.4296875, 21.0390625, 2.1328125, 1.0703125, 1.0703125, 1],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.3515625,
                    "y": 4.53125
                },
                "velocity": {
                    "x": -7.6171875,
                    "y": -0.2734375
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.125
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 95,
                "orientationRad": -3.10684371,
                "yawRate": 0.00390625,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.078125, 0.078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.5703125,
                    "y": 4.7109375
                },
                "velocity": {
                    "x": -7.515625,
                    "y": 0.21875
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 33,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.7265625,
                    "y": -13.8359375
                },
                "velocity": {
                    "x": -7.5,
                    "y": -0.3359375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 44,
                "orientationRad": -3.09805465,
                "yawRate": -0.0009765625,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.4375,
                    "y": -9.328125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.140625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.375
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 32,
                "orientationRad": 60.8384705,
                "yawRate": 0.033203125,
                "covariance": [1.0546875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "position": {
                    "x": 16.1484375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3203125,
                    "y": -0.0546875
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.625
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.1480391,
                "yawRate": -0.009765625,
                "covariance": [1.0078125, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.046875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.953125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 123,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.78125,
                    "y": -13.1953125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 42,
                "orientationRad": 3.16757035,
                "yawRate": -0.00390625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13,
                    "y": 3.890625
                },
                "velocity": {
                    "x": -7.34375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.71875,
                "trackAge": 14,
                "orientationRad": 60.8570251,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.953125,
                    "y": -12.7890625
                },
                "velocity": {
                    "x": -7.4140625,
                    "y": -0.7890625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -2
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 28,
                "orientationRad": 3.24667192,
                "yawRate": -0.013671875,
                "covariance": [0.96875, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 79.5625,
                    "y": -10.1015625
                },
                "velocity": {
                    "x": -11,
                    "y": -1.484375
                },
                "accelerate": {
                    "x": 10.875,
                    "y": 6.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 8,
                "orientationRad": 3.27499223,
                "yawRate": 0.0673828125,
                "covariance": [1.1875, 0.1328125, 0.0546875, 0.4453125, 21.28125, 2.1953125, 2.7265625, 2.7265625, 3],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.8984375,
                    "y": -7.1640625
                },
                "velocity": {
                    "x": -6.7578125,
                    "y": 3.03125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 69,
                "orientationRad": 60.4361267,
                "yawRate": -0.108398438,
                "covariance": [2.21875, 2.015625, 0.21875, 0.4296875, 21.0390625, 2.1328125, 1.109375, 1.109375, 1],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.0234375,
                    "y": 4.5
                },
                "velocity": {
                    "x": -7.3359375,
                    "y": -0.2578125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.75
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 96,
                "orientationRad": -3.10782027,
                "yawRate": 0.0048828125,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.03125, 0.03125, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.1796875,
                    "y": 4.734375
                },
                "velocity": {
                    "x": -7.3984375,
                    "y": 0.2109375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 34,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.265625,
                    "y": -13.859375
                },
                "velocity": {
                    "x": -7.359375,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 45,
                "orientationRad": -3.09805465,
                "yawRate": -0.005859375,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.0703125,
                    "y": -9.3828125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.09375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.25
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 33,
                "orientationRad": 60.8443298,
                "yawRate": 0.0322265625,
                "covariance": [1.046875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "position": {
                    "x": 15.734375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.203125,
                    "y": -0.046875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.75
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.14706254,
                "yawRate": -0.015625,
                "covariance": [1.015625, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 19.9921875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -10.8359375,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 0.875,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 124,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.40625,
                    "y": -13.203125
                },
                "velocity": {
                    "x": -7.171875,
                    "y": -0.1875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 43,
                "orientationRad": 3.16757035,
                "yawRate": -0.0009765625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 12.609375,
                    "y": 3.84375
                },
                "velocity": {
                    "x": -7.21875,
                    "y": -0.0390625
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.734375,
                "trackAge": 15,
                "orientationRad": -3.13711715,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.03125, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.546875,
                    "y": -12.8203125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.7734375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2.125
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 29,
                "orientationRad": 3.24569535,
                "yawRate": -0.0146484375,
                "covariance": [0.9765625, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 78.96875,
                    "y": -10.2421875
                },
                "velocity": {
                    "x": -10.9140625,
                    "y": -1.671875
                },
                "accelerate": {
                    "x": 11.125,
                    "y": 6.25
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 9,
                "orientationRad": 3.29257035,
                "yawRate": 0.0791015625,
                "covariance": [1.21875, 0.1484375, 0.078125, 0.4375, 21.03125, 2.1484375, 2.53125, 2.53125, 3],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -6.7421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 4.6875,
                    "y": 4.484375
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.25
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 97,
                "orientationRad": -3.10879683,
                "yawRate": 0.001953125,
                "covariance": [1.015625, 0.0390625, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 18.796875,
                    "y": 4.6953125
                },
                "velocity": {
                    "x": -7.2578125,
                    "y": 0.171875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 35,
                "orientationRad": 60.8335876,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 33.6953125,
                    "y": -13.890625
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.875
                },
                "length": 8.25,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 46,
                "orientationRad": -3.09805465,
                "yawRate": -0.0048828125,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.125
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->proc();
    g_aeb_component->proc();
    testing::internal::CaptureStderr();// 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":0.7812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":377.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    g_aeb_component->proc();
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.quit_available_eba_, 1.0);
}
// 交叉路口汇车, 同向行驶，不能误报
TEST_F(AEBServerTest, Test_proc_cross_road_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745739010.163425,"moduleName":"io_server","sequenceNum":16299},"gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":13.828125,"wheelSpeedFl":13.734374576144749,"wheelSpeedFr":13.812499576144749,"wheelSpeedRl":13.843750423855251,"wheelSpeedRr":13.828125},"brakePercentage":10,"acceleration":{"longitudinal":-0.*****************,"lateral":-0.18000000715255737},"yawRate":-0.011519175,"steeringSystem":{"steeringWheelAngle":-3.5999999046325684,"steeringWheelRate":8},"curTorque":-232,"slopeEstimation":-0.0150074679,"massEstimation":2381.14966})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1745739010.162425,"moduleName":"io_server","sequenceNum":3261},"detectionList":[{
            "frameId": 6,
            "objectId": 11,
            "position": {
                "x": 19.2734375,
                "y": 3.7578125
            },
            "velocity": {
                "x": -14.203125,
                "y": -0.3359375
            },
            "accelerate": {
                "x": 14.25,
                "y": 0.5
            },
            "length": 5.5,
            "lengthQuality": 1,
            "width": 1.4140625,
            "trackAge": 31,
            "orientationRad": 3.16464067,
            "yawRate": -0.0078125,
            "covariance": [1.0234375, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1745739010.037
        }],"measurementTime":2687216})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->proc();
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
}
// 交叉路口汇车, 垂直行驶，无碰撞
TEST_F(AEBServerTest, Test_proc_cross_road_vertical_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745739010.163425,"moduleName":"io_server","sequenceNum":16299},"gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":13.828125,"wheelSpeedFl":13.734374576144749,"wheelSpeedFr":13.812499576144749,"wheelSpeedRl":13.843750423855251,"wheelSpeedRr":13.828125},"brakePercentage":10,"acceleration":{"longitudinal":-0.*****************,"lateral":-0.18000000715255737},"yawRate":-0.011519175,"steeringSystem":{"steeringWheelAngle":-3.5999999046325684,"steeringWheelRate":8},"curTorque":-232,"slopeEstimation":-0.0150074679,"massEstimation":2381.14966})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1745739010.162425,"moduleName":"io_server","sequenceNum":3261},"detectionList":[{
            "frameId": 6,
            "objectId": 11,
            "position": {
                "x": 19.2734375,
                "y": 5.7578125
            },
            "velocity": {
                "x": -14.203125,
                "y": -13.3359375
            },
            "accelerate": {
                "y": 14.25,
                "x": 0.5
            },
            "length": 5.5,
            "lengthQuality": 1,
            "width": 1.4140625,
            "trackAge": 31,
            "orientationRad": 1.582320335,
            "yawRate": -0.0078125,
            "covariance": [1.0234375, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1745739010.037
        }],"measurementTime":2687216})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->proc();
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
}

// 交叉路口汇车, 垂直行驶，有碰撞
TEST_F(AEBServerTest, Test_proc_cross_road_vertical_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745739010.163425,"moduleName":"io_server","sequenceNum":16299},"gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":13.828125,"wheelSpeedFl":13.734374576144749,"wheelSpeedFr":13.812499576144749,"wheelSpeedRl":13.843750423855251,"wheelSpeedRr":13.828125},"brakePercentage":10,"acceleration":{"longitudinal":-0.*****************,"lateral":-0.18000000715255737},"yawRate":-0.011519175,"steeringSystem":{"steeringWheelAngle":-3.5999999046325684,"steeringWheelRate":8},"curTorque":-232,"slopeEstimation":-0.0150074679,"massEstimation":2381.14966})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1745739010.162425,"moduleName":"io_server","sequenceNum":3261},"detectionList":[{
            "frameId": 6,
            "objectId": 11,
            "position": {
                "x": 19.2734375,
                "y": 5.7578125
            },
            "velocity": {
                "x": -14.203125,
                "y": -1.8359375
            },
            "accelerate": {
                "y": 14.25,
                "x": 0.5
            },
            "length": 5.5,
            "lengthQuality": 1,
            "width": 1.4140625,
            "trackAge": 31,
            "orientationRad": 1.582320335,
            "yawRate": -0.0078125,
            "covariance": [1.0234375, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1745739010.037
        }],"measurementTime":2687216})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    g_aeb_component->proc();
    testing::internal::CaptureStderr();// 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    testing::internal::CaptureStderr();// 执行被测函数
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL3");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
}

// cutin，无碰撞
TEST_F(AEBServerTest, Test_proc_cutin_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745739010.163425,"moduleName":"io_server","sequenceNum":16299},"gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":13.828125,"wheelSpeedFl":13.734374576144749,"wheelSpeedFr":13.812499576144749,"wheelSpeedRl":13.843750423855251,"wheelSpeedRr":13.828125},"brakePercentage":10,"acceleration":{"longitudinal":-0.*****************,"lateral":-0.18000000715255737},"yawRate":-0.011519175,"steeringSystem":{"steeringWheelAngle":-3.5999999046325684,"steeringWheelRate":8},"curTorque":-232,"slopeEstimation":-0.0150074679,"massEstimation":2381.14966})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1745739010.162425,"moduleName":"io_server","sequenceNum":3261},"detectionList":[{
            "frameId": 6,
            "objectId": 11,
            "position": {
                "x": 19.2734375,
                "y": 5.7578125
            },
            "velocity": {
                "x": -2.203125,
                "y": -9.3359375
            },
            "accelerate": {
                "y": 14.25,
                "x": 0.5
            },
            "length": 5.5,
            "lengthQuality": 1,
            "width": 1.4140625,
            "trackAge": 31,
            "orientationRad": 0.182320335,
            "yawRate": -0.0078125,
            "covariance": [1.0234375, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
            "measurementTime": 1745739010.037
        }],"measurementTime":2687216})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->proc();
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
}

// cutin，有碰撞风险，预警
TEST_F(AEBServerTest, Test_proc_cutin_warning) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":8.3812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -3.6421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    testing::internal::CaptureStderr();
    // 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_PRE_WARNING");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 2);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);
    g_aeb_component->proc();
    testing::internal::CaptureStderr();
    // 执行被测函数
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 2);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);
}

// cutin，有碰撞
TEST_F(AEBServerTest, Test_proc_cutin_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1745232020.0478649,"moduleName":"io_server","sequenceNum":17249},"drivingMode":"AUTO_SPEED_ONLY","gearLocationb":"DRIVING","vehicleSignal":{"turnSignal":"TURN_LEFT","emergencyLight":true,"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":9.2812498940361872,"wheelSpeedFl":7.265625,"wheelSpeedFr":7.2500001059638128,"wheelSpeedRl":7.265625,"wheelSpeedRr":7.2968747880723743},"acceleration":{"longitudinal":-2.****************,"lateral":0.10999999940395355},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.3999999761581421},"curTorque":-1})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1745232020.1429679,
                "moduleName": "io_server",
                "sequenceNum": 3462
            },
            "detectionList": [{
                "position": {
                    "x": 16.5703125,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3828125,
                    "y": -0.0859375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.15292192,
                "yawRate": 0.001953125,
                "covariance": [1.0078125, 0.03125, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0.0234375, 0.0234375, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.1015625,
                    "y": 0.75
                },
                "velocity": {
                    "x": -1.0703125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1.125,
                    "y": -1.625
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 122,
                "orientationRad": 3.15878129,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 52.2109375,
                    "y": -13.1796875
                },
                "velocity": {
                    "x": -7.4296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 41,
                "orientationRad": 3.16757035,
                "yawRate": -0.0078125,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13.0859375,
                    "y": 3.90625
                },
                "velocity": {
                    "x": -7.5,
                    "y": 0.015625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.5
                },
                "length": 3.5,
                "lengthQuality": 1,
                "width": 0.7109375,
                "trackAge": 13,
                "orientationRad": 60.855072,
                "yawRate": 0.029296875,
                "covariance": [1.0625, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.125, 0.015625, 0.015625, 0],
                "measurementTime": 1745232020.009
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 64.4140625,
                    "y": -12.75
                },
                "velocity": {
                    "x": -7.5546875,
                    "y": -0.8203125
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.75
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 27,
                "orientationRad": 3.24862504,
                "yawRate": -0.015625,
                "covariance": [0.96875, 0.0625, 0.125, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 80.4453125,
                    "y": -9.96875
                },
                "velocity": {
                    "x": -10.15625,
                    "y": -1.1875
                },
                "accelerate": {
                    "x": 10.25,
                    "y": 5.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 7,
                "orientationRad": 3.2574141,
                "yawRate": 0.052734375,
                "covariance": [1.1796875, 0.1171875, 0.0546875, 0.453125, 21.9765625, 2.28125, 3.171875, 3.171875, 3],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 10.28125,
                    "y": -7.328125
                },
                "velocity": {
                    "x": -6.7734375,
                    "y": 2.984375
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 68,
                "orientationRad": 60.4419861,
                "yawRate": -0.108398438,
                "covariance": [2.09375, 1.8203125, 0.1796875, 0.4296875, 21.0390625, 2.1328125, 1.0703125, 1.0703125, 1],
                "measurementTime": 1745232020.01
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.3515625,
                    "y": 4.53125
                },
                "velocity": {
                    "x": -7.6171875,
                    "y": -0.2734375
                },
                "accelerate": {
                    "x": 7.625,
                    "y": -1.125
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 95,
                "orientationRad": -3.10684371,
                "yawRate": 0.00390625,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.078125, 0.078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.5703125,
                    "y": 4.7109375
                },
                "velocity": {
                    "x": -7.515625,
                    "y": 0.21875
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 33,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.7265625,
                    "y": -13.8359375
                },
                "velocity": {
                    "x": -7.5,
                    "y": -0.3359375
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.625
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 44,
                "orientationRad": -3.09805465,
                "yawRate": -0.0009765625,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.011
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.4375,
                    "y": -9.328125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.140625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.375
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 32,
                "orientationRad": 60.8384705,
                "yawRate": 0.033203125,
                "covariance": [1.0546875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.011
            }, {
                "position": {
                    "x": 16.1484375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.3203125,
                    "y": -0.0546875
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.625
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.1480391,
                "yawRate": -0.009765625,
                "covariance": [1.0078125, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 20.046875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -0.953125,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 1,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 123,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.78125,
                    "y": -13.1953125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.1953125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 42,
                "orientationRad": 3.16757035,
                "yawRate": -0.00390625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.065
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 13,
                    "y": 3.890625
                },
                "velocity": {
                    "x": -7.34375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.71875,
                "trackAge": 14,
                "orientationRad": 60.8570251,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.0390625, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.953125,
                    "y": -12.7890625
                },
                "velocity": {
                    "x": -7.4140625,
                    "y": -0.7890625
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -2
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 28,
                "orientationRad": 3.24667192,
                "yawRate": -0.013671875,
                "covariance": [0.96875, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 79.5625,
                    "y": -10.1015625
                },
                "velocity": {
                    "x": -11,
                    "y": -1.484375
                },
                "accelerate": {
                    "x": 10.875,
                    "y": 6.625
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 8,
                "orientationRad": 3.27499223,
                "yawRate": 0.0673828125,
                "covariance": [1.1875, 0.1328125, 0.0546875, 0.4453125, 21.28125, 2.1953125, 2.7265625, 2.7265625, 3],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.8984375,
                    "y": -7.1640625
                },
                "velocity": {
                    "x": -6.7578125,
                    "y": 3.03125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 69,
                "orientationRad": 60.4361267,
                "yawRate": -0.108398438,
                "covariance": [2.21875, 2.015625, 0.21875, 0.4296875, 21.0390625, 2.1328125, 1.109375, 1.109375, 1],
                "measurementTime": 1745232020.066
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 5.0234375,
                    "y": 4.5
                },
                "velocity": {
                    "x": -7.3359375,
                    "y": -0.2578125
                },
                "accelerate": {
                    "x": 7.5,
                    "y": -1.75
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 96,
                "orientationRad": -3.10782027,
                "yawRate": 0.0048828125,
                "covariance": [1.015625, 0.03125, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.03125, 0.03125, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 19.1796875,
                    "y": 4.734375
                },
                "velocity": {
                    "x": -7.3984375,
                    "y": 0.2109375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 34,
                "orientationRad": 60.8287048,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 34.265625,
                    "y": -13.859375
                },
                "velocity": {
                    "x": -7.359375,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.75
                },
                "length": 8.5,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 45,
                "orientationRad": -3.09805465,
                "yawRate": -0.005859375,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "frameId": 10,
                "objectId": 13,
                "position": {
                    "x": 43.0703125,
                    "y": -9.3828125
                },
                "velocity": {
                    "x": -7.46875,
                    "y": 0.09375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -1.25
                },
                "length": 3.25,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 33,
                "orientationRad": 60.8443298,
                "yawRate": 0.0322265625,
                "covariance": [1.046875, 0.078125, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.0669999
            }, {
                "position": {
                    "x": 15.734375,
                    "y": -11.2890625
                },
                "velocity": {
                    "x": -7.203125,
                    "y": -0.046875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.75
                },
                "length": 7,
                "lengthQuality": 1,
                "width": 2.59375,
                "trackAge": 255,
                "orientationRad": 3.14706254,
                "yawRate": -0.015625,
                "covariance": [1.015625, 0.03125, 0, 0.4296875, 21.0390625, 2.1328125, 0.046875, 0.046875, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 1,
                "objectId": 1,
                "position": {
                    "x": 19.9921875,
                    "y": 0.7421875
                },
                "velocity": {
                    "x": -10.8359375,
                    "y": -0.015625
                },
                "accelerate": {
                    "x": 0.875,
                    "y": -1.875
                },
                "length": 3.34375,
                "lengthQuality": 1,
                "width": 0.6171875,
                "trackAge": 124,
                "orientationRad": 3.15975785,
                "yawRate": 0.0078125,
                "covariance": [1.015625, 0.015625, 0.0078125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.122
            }, {
                "frameId": 2,
                "objectId": 2,
                "position": {
                    "x": 51.40625,
                    "y": -13.203125
                },
                "velocity": {
                    "x": -7.171875,
                    "y": -0.1875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 10.75,
                "lengthQuality": 1,
                "width": 1.4140625,
                "trackAge": 43,
                "orientationRad": 3.16757035,
                "yawRate": -0.0009765625,
                "covariance": [1.03125, 0.03125, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 3,
                "objectId": 3,
                "position": {
                    "x": 12.609375,
                    "y": 3.84375
                },
                "velocity": {
                    "x": -7.21875,
                    "y": -0.0390625
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 4.25,
                "lengthQuality": 1,
                "width": 0.734375,
                "trackAge": 15,
                "orientationRad": -3.13711715,
                "yawRate": 0.01953125,
                "covariance": [1.0546875, 0.03125, -0.0078125, 0.4296875, 21.0546875, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 4,
                "objectId": 4,
                "position": {
                    "x": 63.546875,
                    "y": -12.8203125
                },
                "velocity": {
                    "x": -7.296875,
                    "y": -0.7734375
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2.125
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 29,
                "orientationRad": 3.24569535,
                "yawRate": -0.0146484375,
                "covariance": [0.9765625, 0.0625, 0.1171875, 0.4296875, 21.0390625, 2.1328125, 0.0078125, 0.0078125, 0],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 78.96875,
                    "y": -10.2421875
                },
                "velocity": {
                    "x": -10.9140625,
                    "y": -1.671875
                },
                "accelerate": {
                    "x": 11.125,
                    "y": 6.25
                },
                "length": 2.5,
                "lengthQuality": 1,
                "width": 1.9921875,
                "trackAge": 9,
                "orientationRad": 3.29257035,
                "yawRate": 0.0791015625,
                "covariance": [1.21875, 0.1484375, 0.078125, 0.4375, 21.03125, 2.1484375, 2.53125, 2.53125, 3],
                "measurementTime": 1745232020.1229999
            }, {
                "frameId": 6,
                "objectId": 6,
                "position": {
                    "x": 9.515625,
                    "y": -6.9921875
                },
                "velocity": {
                    "x": -5.7421875,
                    "y": 3.0703125
                },
                "accelerate": {
                    "x": 7.375
                },
                "length": 2.75,
                "lengthQuality": 1,
                "width": 0.59375,
                "trackAge": 70,
                "orientationRad": 60.4302673,
                "yawRate": -0.108398438,
                "covariance": [2.3515625, 2.21875, 0.2734375, 0.4296875, 21.0390625, 2.1328125, 1.1484375, 1.1484375, 1],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 7,
                "objectId": 9,
                "position": {
                    "x": 4.6875,
                    "y": 4.484375
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.25
                },
                "accelerate": {
                    "x": 7.375,
                    "y": -2
                },
                "length": 6,
                "lengthQuality": 1,
                "width": 1.65625,
                "trackAge": 97,
                "orientationRad": -3.10879683,
                "yawRate": 0.001953125,
                "covariance": [1.015625, 0.0390625, 0.03125, 0.4296875, 21.0390625, 2.1328125, 0.0390625, 0.0390625, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 8,
                "objectId": 11,
                "position": {
                    "x": 18.796875,
                    "y": 4.6953125
                },
                "velocity": {
                    "x": -7.2578125,
                    "y": 0.171875
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -2
                },
                "length": 5.7421875,
                "lengthQuality": 1,
                "width": 1.5234375,
                "trackAge": 35,
                "orientationRad": 60.8335876,
                "yawRate": 0.0234375,
                "covariance": [1.0078125, 0.046875, -0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.124
            }, {
                "frameId": 9,
                "objectId": 12,
                "position": {
                    "x": 33.6953125,
                    "y": -13.890625
                },
                "velocity": {
                    "x": -7.265625,
                    "y": -0.328125
                },
                "accelerate": {
                    "x": 7.25,
                    "y": -1.875
                },
                "length": 8.25,
                "lengthQuality": 1,
                "width": 1.421875,
                "trackAge": 46,
                "orientationRad": -3.09805465,
                "yawRate": -0.0048828125,
                "covariance": [1, 0.0234375, 0.046875, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1745232020.125
            }],
            "measurementTime": 1054648
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    //g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    testing::internal::CaptureStderr();
    // 执行被测函数
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_PRE_WARNING");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 4);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
    g_aeb_component->proc();
    testing::internal::CaptureStderr();
    // 执行被测函数
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL3");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 4);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
}

// turn left，对向，静止车辆，距离足够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_left_static_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..( {"header":{"timestamp":1746008362.70422,"moduleName":"io_server","sequenceNum":72949},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.171875,"wheelSpeedFl":1.1562499735090468,"wheelSpeedFr":1.171875,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.1875000264909532},"acceleration":{"longitudinal":4.163336342344337e-16,"lateral":-0.*****************},"yawRate":-0.00523598865,"steeringSystem":{"steeringWheelAngle":170.8000001907348633,"steeringWheelRate":6},"curTorque":144,"slopeEstimation":0.00441353815,"massEstimation":2311.99146})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":16.5703125,"y":-6.90625},"velocity":{ "x": -1.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn left 同车道，静止车辆，距离足够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_left_static_no_collision2) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":2.4218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":16.5703125,"y":6.90625},"velocity":{ "x": -2.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn left 同车道，静止车辆，距离足够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_left_static_no_collision3) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.4218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":8.5703125,"y":2.90625},"velocity":{ "x": -3.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":50.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn left 同车道，静止车辆，距离不够远，可能会发生碰撞, 触发告警
TEST_F(AEBServerTest, Test_proc_turn_left_static_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":4.4218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":6.5703125,"y":2.90625},"velocity":{ "x": -4.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":50.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);
}

// turn left 同车道，静止车辆，距离不够远，可能会发生碰撞, 触发AEB
TEST_F(AEBServerTest, Test_proc_turn_left_static_collision2) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":5.4218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":6.5703125,"y":2.90625},"velocity":{ "x": -5.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":50.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL3");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
}

// turn left 同车道，慢速车辆，距离足够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1748513796.1187379,"moduleName":"io_server","sequenceNum":12899},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":4.21875,"wheelSpeedFl":4.093750052981906,"wheelSpeedFr":4.4218749470180931,"wheelSpeedRl":4.093750052981906,"wheelSpeedRr":4.3437499470180931},"throttlePerceptage":13.6,"acceleration":{"longitudinal":0.62999999523162842,"lateral":0.87999999523162842},"yawRate":0.194255173,"steeringSystem":{"steeringWheelAngle":124.19999694824219,"steeringWheelRate":4,"steeringWheelTorque":1.2999999523162842},"curTorque":832,"slopeEstimation":-0.00555893499})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1748513796.124738,"moduleName":"io_server","sequenceNum":2581},"detectionList":[{"position":{"x":11,"y":-12.453125},"velocity":2.5703125,"length":0.75,"lengthCovariance":1,"width":0.40625,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.654279053,"orientationCovariance":0.078125,"yawRate":-0.0478515625,"yawRateCovariance":0.015625,"covariance":[1.2421875,0.9453125,-0.203125,21.0390625,1.078125],"measurementTime":1748513795.999},{"frameId":1,"objectId":1,"position":{"x":13.03125,"y":4.890625},"velocity":4.1953125,"accelerate":-0.1015625,"length":3.25,"lengthCovariance":0.9921875,"width":1.25,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":0.542986572,"yawRate":0.143554688,"covariance":[0.671875,0.3359375,0.421875,21.0390625,0.0078125],"measurementTime":1748513795.999},{"position":{"x":10.7421875,"y":-12.65625},"velocity":2.5703125,"length":0.75,"lengthCovariance":1,"width":0.40625,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":-0.666974366,"orientationCovariance":0.0859375,"yawRate":-0.0478515625,"yawRateCovariance":0.015625,"covariance":[1.3046875,1.015625,-0.203125,21.0390625,1.1328125],"measurementTime":1748513796.055},{"frameId":1,"objectId":1,"position":{"x":13.0625,"y":4.859375},"velocity":4.1875,"accelerate":-0.1484375,"length":3.25,"lengthCovariance":0.9921875,"width":1.25,"widthCovariance":0.0078125,"trackAge":255,"existProbablity":0.996078432,"orientationRad":0.539080322,"yawRate":0.143554688,"covariance":[0.6640625,0.3359375,0.421875,21.0390625,0.0078125],"measurementTime":1748513796.055}],"measurementTime":1582784})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn left 同车道，慢速车辆，距离不够远，可能会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.4218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":8.5703125,"y":2.90625},"velocity":{ "x": -2.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":50.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn left 同车道，慢速车辆，距离不够远，可能会发生碰撞, 触发告警
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_collision2) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":6.0218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746008362.6582241,
                "moduleName": "io_server",
                "sequenceNum": 14601
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 4,
                "position": {
                    "x": 5.5703125,
                    "y": 2.90625
                },
                "velocity": {
                    "x": -2.5078125,
                    "y": 1.0078125
                },
                "accelerate": {},
                "length": 4.5703125,
                "lengthQuality": 1,
                "width": 1.9140625,
                "trackAge": 136,
                "orientationRad": 50.7847595,
                "yawRate": 0.0029296875,
                "covariance": [1.0078125, 0.0390625, 0.0234375, 0.4296875, 21.0390625, 2.1328125, 0.0703125, 0.0703125, 0],
                "measurementTime": 1746008362.632
            }],
            "measurementTime": 2099776
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);
}

// turn left 同车道，慢速车辆，距离不够远，可能会发生碰撞, 触发AEB
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_collision3) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008364.719218,"moduleName":"io_server","sequenceNum":73049},"gearLocationb":"REVERSE","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":5.4218750264909532,"wheelSpeedFl":1.3906249735090468,"wheelSpeedFr":1.****************,"wheelSpeedRl":1.40625,"wheelSpeedRr":1.****************},"acceleration":{"longitudinal":0.070000000298023224,"lateral":-0.12999999523162842},"yawRate":-0.0256563425,"steeringSystem":{"steeringWheelAngle":149.299999237060547},"curTorque":1,"slopeEstimation":-0.000339629478})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1746008362.6582241,"moduleName":"io_server","sequenceNum":14601},"detectionList":[{"objectId":2,"position":{"x":76.78125,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":98,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.265625,0.265625,0],"measurementTime":1746008362.523},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":134,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.3828125,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.523},{"frameId":2,"objectId":10,"position":{"x":31.359375,"y":-4.0546875},"velocity":{"x":0.796875,"y":0.2734375},"accelerate":{"x":-0.875,"y":-2.75},"length":2,"lengthQuality":1,"width":1.546875,"trackAge":116,"orientationRad":-2.81192183,"yawRate":0.0087890625,"covariance":[0.8203125,0.1953125,0.375,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.524},{"objectId":2,"position":{"x":76.8046875,"y":5.0703125},"velocity":{"x":0.0078125,"y":-0.0078125},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":99,"orientationRad":2.21444535,"yawRate":-0.001953125,"covariance":[0.0390625,0.046875,-0.015625,0.4296875,21.0390625,2.1328125,0.3203125,0.3203125,0],"measurementTime":1746008362.579},{"frameId":1,"objectId":4,"position":{"x":16.546875,"y":-6.90625},"velocity":{},"accelerate":{},"length":4.5703125,"width":1.9140625,"trackAge":135,"orientationRad":60.7847595,"yawRate":0.0029296875,"covariance":[0.4140625,0.0390625,0.0703125,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.579},{"frameId":2,"objectId":10,"position":{"x":31.3828125,"y":-4.078125},"velocity":{"x":0.0078125},"accelerate":{"x":-0.5,"y":-0.75},"length":2,"lengthQuality":1,"width":1.53125,"trackAge":117,"orientationRad":-2.81192183,"yawRate":0.0068359375,"covariance":[0.8125,0.1875,0.3671875,0.4296875,21.0390625,2.1328125,0,0,0],"measurementTime":1746008362.579},{"position":{"x":99.3515625,"y":-2.3828125},"velocity":{"x":0.78125},"accelerate":{"x":0.75},"length":3.8046875,"lengthQuality":1,"width":0.625,"trackAge":5,"orientationRad":-0.00332812499,"covariance":[1.1015625,0.078125,0,0.453125,26.078125,2.5078125,0,0,0],"measurementTime":1746008362.632},{"frameId":1,"objectId":2,"position":{"x":76.84375,"y":5.078125},"velocity":{},"accelerate":{},"length":1.9921875,"lengthQuality":1,"width":2,"trackAge":100,"orientationRad":2.21444535,"yawRate":-0.0009765625,"covariance":[0.03125,0.0390625,-0.015625,0.4296875,21.0390625,2.1328125,0.0078125,0.0078125,0],"measurementTime":1746008362.632},{"frameId":2,"objectId":4,"position":{"x":6.5703125,"y":2.90625},"velocity":{ "x": -4.0078125, "y": -0.0078125},"accelerate":{},"length":4.5703125,"lengthQuality":1,"width":1.9140625,"trackAge":136,"orientationRad":50.7847595,"yawRate":0.0029296875,"covariance":[1.0078125,0.0390625,0.0234375,0.4296875,21.0390625,2.1328125,0.0703125,0.0703125,0],"measurementTime":1746008362.632}],"measurementTime":2099776})..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    //g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
}

// turn left 对向目标车辆直行，慢速车辆，距离够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_direct_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008510.8122151,"moduleName":"io_server","sequenceNum":80249},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.796875,"wheelSpeedFl":1.71875,"wheelSpeedFr":1.8906250264909532,"wheelSpeedRl":1.7031249735090468,"wheelSpeedRr":1.875},"throttlePerceptage":0.6,"acceleration":{"longitudinal":0.05000000074505806,"lateral":4.163336342344337e-16},"yawRate":0.100530982,"steeringSystem":{"steeringWheelAngle":126,"steeringWheelRate":44},"curTorque":444,"slopeEstimation":0.0050022346})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746008510.786166,
                "moduleName": "io_server",
                "sequenceNum": 16062
            },
            "detectionList": [{
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 5.875,
                    "y": 4.3125
                },
                "velocity": {
                    "x": -3.90625,
                    "y": 0.0625
                },
                "accelerate": {
                    "x": 1.875
                },
                "length": 4.8,
                "lengthQuality": 1,
                "width": 1.7015625,
                "trackAge": 45,
                "orientationRad": 3.10604692,
                "yawRate": 0.025390625,
                "covariance": [1.0546875, 0.0234375, 0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746008510.754
            }],
            "measurementTime": 2247896
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn left 对向目标车辆直行，慢速车辆，距离不够远，触发报警
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_direct_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008510.8122151,"moduleName":"io_server","sequenceNum":80249},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":4.096875,"wheelSpeedFl":1.71875,"wheelSpeedFr":1.8906250264909532,"wheelSpeedRl":1.7031249735090468,"wheelSpeedRr":1.875},"throttlePerceptage":0.6,"acceleration":{"longitudinal":0.05000000074505806,"lateral":4.163336342344337e-16},"yawRate":0.100530982,"steeringSystem":{"steeringWheelAngle":126,"steeringWheelRate":44},"curTorque":444,"slopeEstimation":0.0050022346})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746008510.786166,
                "moduleName": "io_server",
                "sequenceNum": 16062
            },
            "detectionList": [{
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 16.875,
                    "y": 4.3125
                },
                "velocity": {
                    "x": -6.95625,
                    "y": 0.0625
                },
                "accelerate": {
                    "x": 1.875
                },
                "length": 4.8,
                "lengthQuality": 1,
                "width": 1.7015625,
                "trackAge": 45,
                "orientationRad": 3.10604692,
                "yawRate": 0.025390625,
                "covariance": [1.0546875, 0.0234375, 0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746008510.754
            }],
            "measurementTime": 2247896
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 1);
}

// turn left 对向目标车辆直行，快速车辆，距离不够远，触发AEB
TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_direct_collision2) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746008510.8122151,"moduleName":"io_server","sequenceNum":80249},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":8.296875,"wheelSpeedFl":1.71875,"wheelSpeedFr":1.8906250264909532,"wheelSpeedRl":1.7031249735090468,"wheelSpeedRr":1.875},"throttlePerceptage":0.6,"acceleration":{"longitudinal":0.05000000074505806,"lateral":4.163336342344337e-16},"yawRate":0.100530982,"steeringSystem":{"steeringWheelAngle":126,"steeringWheelRate":44},"curTorque":444,"slopeEstimation":0.0050022346})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746008510.786166,
                "moduleName": "io_server",
                "sequenceNum": 16062
            },
            "detectionList": [{
                "frameId": 5,
                "objectId": 5,
                "position": {
                    "x": 9.875,
                    "y": 2.3125
                },
                "velocity": {
                    "x": -11.90625,
                    "y": 0.0625
                },
                "accelerate": {
                    "x": 1.875
                },
                "length": 4.8,
                "lengthQuality": 1,
                "width": 1.7015625,
                "trackAge": 45,
                "orientationRad": 3.10604692,
                "yawRate": 0.025390625,
                "covariance": [1.0546875, 0.0234375, 0.0390625, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746008510.754
            }],
            "measurementTime": 2247896
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
}

// turn right 对向目标车辆静止，距离够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_right_static_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746009232.62041,"moduleName":"io_server","sequenceNum":115649},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.9093750264909532,"wheelSpeedFl":1.171875,"wheelSpeedFr":1.0781249735090468,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.0625000132454765},"brakePercentage":10,"acceleration":{"longitudinal":-0.029999999329447746,"lateral":-0.*****************},"yawRate":-0.0680678487,"steeringSystem":{"steeringWheelAngle":-161.89999389648438,"steeringWheelRate":36},"curTorque":367,"slopeEstimation":0.00074841833})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746009232.5268359,
                "moduleName": "io_server",
                "sequenceNum": 23145
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 9,
                "position": {
                    "x": 6.9375,
                    "y": -3.8359375
                },
                "velocity": {
                    "x": -1.375,
                    "y": 0.296875
                },
                "accelerate": {
                    "x": 1.5,
                    "y": -0.625
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.40625,
                "trackAge": 112,
                "orientationRad": 3.2026567,
                "yawRate": -0.0029296875,
                "covariance": [0.96875, 0.046875, -0.1953125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746009232.477
            }],
            "measurementTime": 2969624
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn right 自车道目标车辆静止，会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_right_static_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746009232.62041,"moduleName":"io_server","sequenceNum":115649},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":2.9093750264909532,"wheelSpeedFl":1.171875,"wheelSpeedFr":1.0781249735090468,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.0625000132454765},"brakePercentage":10,"acceleration":{"longitudinal":-0.029999999329447746,"lateral":-0.*****************},"yawRate":-0.0680678487,"steeringSystem":{"steeringWheelAngle":-161.89999389648438,"steeringWheelRate":36},"curTorque":367,"slopeEstimation":0.00074841833})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746009232.5268359,
                "moduleName": "io_server",
                "sequenceNum": 23145
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 9,
                "position": {
                    "x": 2.9375,
                    "y": -2.8359375
                },
                "velocity": {
                    "x": -2.9,
                    "y": 0.096875
                },
                "accelerate": {
                    "x": 0.15,
                    "y": -0.625
                },
                "length": 5.05,
                "lengthQuality": 1,
                "width": 1.70625,
                "trackAge": 112,
                "orientationRad": 1.5026567,
                "yawRate": -0.0029296875,
                "covariance": [0.96875, 0.046875, -0.1953125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746009232.477
            }],
            "measurementTime": 2969624
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
}

// turn right 自车道目标车辆慢速行驶，会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_right_dynamic_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746009232.62041,"moduleName":"io_server","sequenceNum":115649},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.9093750264909532,"wheelSpeedFl":1.171875,"wheelSpeedFr":1.0781249735090468,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.0625000132454765},"brakePercentage":10,"acceleration":{"longitudinal":-0.029999999329447746,"lateral":-0.*****************},"yawRate":-0.0680678487,"steeringSystem":{"steeringWheelAngle":-161.89999389648438,"steeringWheelRate":36},"curTorque":367,"slopeEstimation":0.00074841833})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746009232.5268359,
                "moduleName": "io_server",
                "sequenceNum": 23145
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 9,
                "position": {
                    "x": 2.9375,
                    "y": -2.8359375
                },
                "velocity": {
                    "x": -1.4,
                    "y": -1.196875
                },
                "accelerate": {
                    "x": 0.15,
                    "y": -0.625
                },
                "length": 5.05,
                "lengthQuality": 1,
                "width": 1.70625,
                "trackAge": 112,
                "orientationRad": 2.5026567,
                "yawRate": -0.0029296875,
                "covariance": [0.96875, 0.046875, -0.1953125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746009232.477
            }],
            "measurementTime": 2969624
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL3");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
}

// turn right 对向目标两轮车直行，慢速车辆，距离够远，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_right_dynamic_direct_slow_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746009232.62041,"moduleName":"io_server","sequenceNum":115649},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":2.9093750264909532,"wheelSpeedFl":1.171875,"wheelSpeedFr":1.0781249735090468,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.0625000132454765},"brakePercentage":10,"acceleration":{"longitudinal":-0.029999999329447746,"lateral":-0.*****************},"yawRate":-0.0680678487,"steeringSystem":{"steeringWheelAngle":-161.89999389648438,"steeringWheelRate":36},"curTorque":367,"slopeEstimation":0.00074841833})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746009232.5268359,
                "moduleName": "io_server",
                "sequenceNum": 23145
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 9,
                "position": {
                    "x": 22.9375,
                    "y": -3.8359375
                },
                "velocity": {
                    "x": -1.375,
                    "y": 0.096875
                },
                "accelerate": {
                    "x": 1.5,
                    "y": -0.625
                },
                "length": 5.75,
                "lengthQuality": 1,
                "width": 1.40625,
                "trackAge": 112,
                "orientationRad": 3.1026567,
                "yawRate": -0.0029296875,
                "covariance": [0.96875, 0.046875, -0.1953125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746009232.477
            }],
            "measurementTime": 2969624
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn right 对向目标两轮车直行，快速车辆，不会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_right_dynamic_direct_fast_no_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746009232.62041,"moduleName":"io_server","sequenceNum":115649},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.9093750264909532,"wheelSpeedFl":1.171875,"wheelSpeedFr":1.0781249735090468,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.0625000132454765},"brakePercentage":10,"acceleration":{"longitudinal":-0.029999999329447746,"lateral":-0.*****************},"yawRate":-0.0680678487,"steeringSystem":{"steeringWheelAngle":-161.89999389648438,"steeringWheelRate":36},"curTorque":367,"slopeEstimation":0.00074841833})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746009232.5268359,
                "moduleName": "io_server",
                "sequenceNum": 23145
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 9,
                "position": {
                    "x": 12.9375,
                    "y": -3.8359375
                },
                "velocity": {
                    "x": -7.375,
                    "y": 0.096875
                },
                "accelerate": {
                    "x": 1.5,
                    "y": -0.625
                },
                "length": 5.05,
                "lengthQuality": 1,
                "width": 1.70625,
                "trackAge": 112,
                "orientationRad": 3.1026567,
                "yawRate": -0.0029296875,
                "covariance": [0.96875, 0.046875, -0.1953125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746009232.477
            }],
            "measurementTime": 2969624
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

// turn right 对向目标两轮车直行，正常速度车辆，会发生碰撞
TEST_F(AEBServerTest, Test_proc_turn_right_dynamic_direct_collision) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1746009232.62041,"moduleName":"io_server","sequenceNum":115649},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":2.9093750264909532,"wheelSpeedFl":1.171875,"wheelSpeedFr":1.0781249735090468,"wheelSpeedRl":1.171875,"wheelSpeedRr":1.0625000132454765},"brakePercentage":10,"acceleration":{"longitudinal":-0.029999999329447746,"lateral":-0.*****************},"yawRate":-0.0680678487,"steeringSystem":{"steeringWheelAngle":-161.89999389648438,"steeringWheelRate":36},"curTorque":367,"slopeEstimation":0.00074841833})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({
            "header": {
                "timestamp": 1746009232.5268359,
                "moduleName": "io_server",
                "sequenceNum": 23145
            },
            "detectionList": [{
                "frameId": 2,
                "objectId": 9,
                "position": {
                    "x": 10.9375,
                    "y": -3.8359375
                },
                "velocity": {
                    "x": -5.375,
                    "y": 0.096875
                },
                "accelerate": {
                    "x": 1.5,
                    "y": -0.625
                },
                "length": 5.05,
                "lengthQuality": 1,
                "width": 1.70625,
                "trackAge": 112,
                "orientationRad": 3.1026567,
                "yawRate": -0.0029296875,
                "covariance": [0.96875, 0.046875, -0.1953125, 0.4296875, 21.0390625, 2.1328125, 0, 0, 0],
                "measurementTime": 1746009232.477
            }],
            "measurementTime": 2969624
        })..";
        auto msg = std::make_shared<rainbowdash::drivers::FareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        update_radar_detect_obstacles(mega::aeb::RadarPosition::FC, msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    g_aeb_component->aeb_.aeb_conf_.RadarTransform = true;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL3");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 2);
}

// fusion 直行跟车 turn left 同车道，慢速车辆，距离足够远，不会发生碰撞
TEST_F(AEBServerTest, Test_fusion_proc) {
    g_aeb_component->init();    //重新初始化所有数据
    {// 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1749196768.29025,"moduleName":"io_server","sequenceNum":2749},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":4.0781248940361872,"wheelSpeedFl":4.015625052981906,"wheelSpeedFr":4.015625052981906,"wheelSpeedRl":4.0781248940361872,"wheelSpeedRr":4.0781248940361872},"brakePercentage":27,"acceleration":{"longitudinal":-0.550000011920929,"lateral":0.090000003576278687},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1.1000000238418579,"steeringWheelTorque":0.070000000298023224},"curTorque":-295,"slopeEstimation":0.020138042,"massEstimation":2239.60693})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {   // 设置Vehicle State
        std::string str_json = R"..({"header":{"timestamp":1749196767.704,"sequenceNum":2999},"position":{"x":38.*************,"y":26.572643081220924},"orientation":{"qz":0.38528466187746213,"qw":0.922662710906049},"linearVelocity":{"x":4.296875},"heading":0.79095937799002181,"linearAccelerationVrf":{"x":-0.31797018635405694,"y":0.2197428600872999,"z":0.18759182545891157},"angularVelocityVrf":{"x":0.0014156463664713667,"y":0.00067096906890708012,"z":0.0025185740326423563},"eulerAngles":{"z":0.79095937799002181},"poseState":true})..";
        auto msg = std::make_shared<rainbowdash::location::Pose>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_vehicle_state(msg);
    }
    {// 设置视觉融合毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1749196768.27},"obstacleRaws":[{"id":480,"position":{"x":53.599952697753906,"y":42.126644134521484},"orientationRad":0.79236865043640137,"velocity":{"x":1.0914373397827148,"y":1.0604723691940308},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":4.7962417602539062,"width":1.855268120765686,"height":1.8917094469070435,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":53.599998474121094,"y":42.126998901367188,"theta":0.79199999570846558},"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":53.708999633789062,"y":42.233001708984375,"theta":0.79199999570846558},"relativeTime":0.1,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":53.818000793457031,"y":42.3390007019043,"theta":0.79199999570846558},"relativeTime":0.2,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":53.926998138427734,"y":42.444999694824219,"theta":0.79199999570846558},"relativeTime":0.30000000000000004,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.0369987487793,"y":42.550998687744141,"theta":0.79199999570846558},"relativeTime":0.4,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.145999908447266,"y":42.657001495361328,"theta":0.79199999570846558},"relativeTime":0.5,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.255001068115234,"y":42.76300048828125,"theta":0.79199999570846558},"relativeTime":0.60000000000000009,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.363998413085938,"y":42.868999481201172,"theta":0.79199999570846558},"relativeTime":0.70000000000000007,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.472999572753906,"y":42.974998474121094,"theta":0.79199999570846558},"relativeTime":0.8,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.582000732421875,"y":43.081001281738281,"theta":0.79199999570846558},"relativeTime":0.9,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.691001892089844,"y":43.1870002746582,"theta":0.79199999570846558},"relativeTime":1,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.800998687744141,"y":43.292999267578125,"theta":0.79199999570846558},"relativeTime":1.1,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":54.909999847412109,"y":43.398998260498047,"theta":0.79199999570846558},"relativeTime":1.2000000000000002,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.019001007080078,"y":43.505001068115234,"theta":0.79199999570846558},"relativeTime":1.3,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.127998352050781,"y":43.611000061035156,"theta":0.79199999570846558},"relativeTime":1.4000000000000001,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.23699951171875,"y":43.716999053955078,"theta":0.79199999570846558},"relativeTime":1.5,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.346000671386719,"y":43.823001861572266,"theta":0.79199999570846558},"relativeTime":1.6,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.455001831054688,"y":43.929000854492188,"theta":0.79199999570846558},"relativeTime":1.7000000000000002,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.564998626708984,"y":44.0359992980957,"theta":0.79199999570846558},"relativeTime":1.8,"vx":1.090999960899353,"vy":1.059999942779541},{"pathPoint":{"x":55.673999786376953,"y":44.141998291015625,"theta":0.79199999570846558},"relativeTime":1.9000000000000001,"vx":1.090999960899353,"vy":1.059999942779541}]}},{"id":482,"position":{"x":74.578338623046875,"y":46.542121887207031},"orientationRad":1.475003719329834,"velocity":{"x":1.0772696733474731,"y":0.99072390794754028},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":2.9921875,"width":0.875,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":74.5780029296875,"y":46.541999816894531,"theta":1.4750000238418579},"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":74.685997009277344,"y":46.640998840332031,"theta":1.4750000238418579},"relativeTime":0.1,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":74.793998718261719,"y":46.7400016784668,"theta":1.4750000238418579},"relativeTime":0.2,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":74.9020004272461,"y":46.8390007019043,"theta":1.4750000238418579},"relativeTime":0.30000000000000004,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.009002685546875,"y":46.9379997253418,"theta":1.4750000238418579},"relativeTime":0.4,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.116996765136719,"y":47.0369987487793,"theta":1.4750000238418579},"relativeTime":0.5,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.2249984741211,"y":47.137001037597656,"theta":1.4750000238418579},"relativeTime":0.60000000000000009,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.332000732421875,"y":47.236000061035156,"theta":1.4750000238418579},"relativeTime":0.70000000000000007,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.44000244140625,"y":47.334999084472656,"theta":1.4750000238418579},"relativeTime":0.8,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.5479965209961,"y":47.433998107910156,"theta":1.4750000238418579},"relativeTime":0.9,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.655998229980469,"y":47.533000946044922,"theta":1.4750000238418579},"relativeTime":1,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.76300048828125,"y":47.631999969482422,"theta":1.4750000238418579},"relativeTime":1.1,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.871002197265625,"y":47.730998992919922,"theta":1.4750000238418579},"relativeTime":1.2000000000000002,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":75.978996276855469,"y":47.830001831054688,"theta":1.4750000238418579},"relativeTime":1.3,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":76.086997985839844,"y":47.929000854492188,"theta":1.4750000238418579},"relativeTime":1.4000000000000001,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":76.194000244140625,"y":48.027999877929688,"theta":1.4750000238418579},"relativeTime":1.5,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":76.302001953125,"y":48.126998901367188,"theta":1.4750000238418579},"relativeTime":1.6,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":76.410003662109375,"y":48.226001739501953,"theta":1.4750000238418579},"relativeTime":1.7000000000000002,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":76.516998291015625,"y":48.325000762939453,"theta":1.4750000238418579},"relativeTime":1.8,"vx":1.0770000219345093,"vy":0.99099999666213989},{"pathPoint":{"x":76.625,"y":48.423999786376953,"theta":1.4750000238418579},"relativeTime":1.9000000000000001,"vx":1.0770000219345093,"vy":0.99099999666213989}]}},{"id":485,"position":{"x":88.3125991821289,"y":70.810195922851562},"orientationRad":0.79531615972518921,"velocity":{"x":1.0826059579849243,"y":0.99794661998748779},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":0.9921875,"width":0.4296875,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":88.313003540039062,"y":70.80999755859375,"theta":0.79500001668930054},"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":88.4209976196289,"y":70.910003662109375,"theta":0.79500001668930054},"relativeTime":0.1,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":88.528999328613281,"y":71.010002136230469,"theta":0.79500001668930054},"relativeTime":0.2,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":88.637001037597656,"y":71.110000610351562,"theta":0.79500001668930054},"relativeTime":0.30000000000000004,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":88.746002197265625,"y":71.208999633789062,"theta":0.79500001668930054},"relativeTime":0.4,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":88.853996276855469,"y":71.308998107910156,"theta":0.79500001668930054},"relativeTime":0.5,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":88.961997985839844,"y":71.40899658203125,"theta":0.79500001668930054},"relativeTime":0.60000000000000009,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.069999694824219,"y":71.509002685546875,"theta":0.79500001668930054},"relativeTime":0.70000000000000007,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.179000854492188,"y":71.609001159667969,"theta":0.79500001668930054},"relativeTime":0.8,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.287002563476562,"y":71.708000183105469,"theta":0.79500001668930054},"relativeTime":0.9,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.3949966430664,"y":71.807998657226562,"theta":0.79500001668930054},"relativeTime":1,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.502998352050781,"y":71.907997131347656,"theta":0.79500001668930054},"relativeTime":1.1,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.61199951171875,"y":72.008003234863281,"theta":0.79500001668930054},"relativeTime":1.2000000000000002,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.720001220703125,"y":72.107002258300781,"theta":0.79500001668930054},"relativeTime":1.3,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.8280029296875,"y":72.207000732421875,"theta":0.79500001668930054},"relativeTime":1.4000000000000001,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":89.936996459960938,"y":72.306999206542969,"theta":0.79500001668930054},"relativeTime":1.5,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":90.044998168945312,"y":72.406997680664062,"theta":0.79500001668930054},"relativeTime":1.6,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":90.152999877929688,"y":72.507003784179688,"theta":0.79500001668930054},"relativeTime":1.7000000000000002,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":90.261001586914062,"y":72.606002807617188,"theta":0.79500001668930054},"relativeTime":1.8,"vx":1.0829999446868896,"vy":0.99800002574920654},{"pathPoint":{"x":90.370002746582031,"y":72.706001281738281,"theta":0.79500001668930054},"relativeTime":1.9000000000000001,"vx":1.0829999446868896,"vy":0.99800002574920654}]}},{"id":490,"position":{"x":85.231216430664062,"y":66.411018371582031},"orientationRad":0.79726922512054443,"velocity":{"x":1.0613975524902344,"y":1.0242869853973389},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":1,"width":0.3984375,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":85.231002807617188,"y":66.411003112792969,"theta":0.796999990940094},"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.336997985839844,"y":66.51300048828125,"theta":0.796999990940094},"relativeTime":0.1,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.443000793457031,"y":66.615997314453125,"theta":0.796999990940094},"relativeTime":0.2,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.550003051757812,"y":66.718002319335938,"theta":0.796999990940094},"relativeTime":0.30000000000000004,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.655998229980469,"y":66.820999145507812,"theta":0.796999990940094},"relativeTime":0.4,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.762001037597656,"y":66.9229965209961,"theta":0.796999990940094},"relativeTime":0.5,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.867996215820312,"y":67.0260009765625,"theta":0.796999990940094},"relativeTime":0.60000000000000009,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":85.9739990234375,"y":67.127998352050781,"theta":0.796999990940094},"relativeTime":0.70000000000000007,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.080001831054688,"y":67.2300033569336,"theta":0.796999990940094},"relativeTime":0.8,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.185997009277344,"y":67.333000183105469,"theta":0.796999990940094},"relativeTime":0.9,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.292999267578125,"y":67.43499755859375,"theta":0.796999990940094},"relativeTime":1,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.399002075195312,"y":67.538002014160156,"theta":0.796999990940094},"relativeTime":1.1,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.504997253417969,"y":67.639999389648438,"theta":0.796999990940094},"relativeTime":1.2000000000000002,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.611000061035156,"y":67.742996215820312,"theta":0.796999990940094},"relativeTime":1.3,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.717002868652344,"y":67.845001220703125,"theta":0.796999990940094},"relativeTime":1.4000000000000001,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.822998046875,"y":67.947998046875,"theta":0.796999990940094},"relativeTime":1.5,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":86.929000854492188,"y":68.050003051757812,"theta":0.796999990940094},"relativeTime":1.6,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":87.036003112792969,"y":68.1520004272461,"theta":0.796999990940094},"relativeTime":1.7000000000000002,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":87.141998291015625,"y":68.254997253417969,"theta":0.796999990940094},"relativeTime":1.8,"vx":1.0609999895095825,"vy":1.0240000486373901},{"pathPoint":{"x":87.248001098632812,"y":68.357002258300781,"theta":0.796999990940094},"relativeTime":1.9000000000000001,"vx":1.0609999895095825,"vy":1.0240000486373901}]}},{"id":494,"position":{"x":58.511650085449219,"y":47.440261840820312},"orientationRad":0.79236865043640137,"velocity":{"x":1.1207211017608643,"y":1.3019348382949829},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":4.8666849136352539,"width":1.8612637519836426,"height":1.93934166431427,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":58.512001037597656,"y":47.439998626708984,"theta":0.79199999570846558},"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":58.624000549316406,"y":47.569999694824219,"theta":0.79199999570846558},"relativeTime":0.1,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":58.736000061035156,"y":47.701000213623047,"theta":0.79199999570846558},"relativeTime":0.2,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":58.847999572753906,"y":47.831001281738281,"theta":0.79199999570846558},"relativeTime":0.30000000000000004,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":58.959999084472656,"y":47.96099853515625,"theta":0.79199999570846558},"relativeTime":0.4,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.071998596191406,"y":48.090999603271484,"theta":0.79199999570846558},"relativeTime":0.5,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.183998107910156,"y":48.221000671386719,"theta":0.79199999570846558},"relativeTime":0.60000000000000009,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.296001434326172,"y":48.352001190185547,"theta":0.79199999570846558},"relativeTime":0.70000000000000007,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.408000946044922,"y":48.481998443603516,"theta":0.79199999570846558},"relativeTime":0.8,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.520000457763672,"y":48.61199951171875,"theta":0.79199999570846558},"relativeTime":0.9,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.631999969482422,"y":48.742000579833984,"theta":0.79199999570846558},"relativeTime":1,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.743999481201172,"y":48.872001647949219,"theta":0.79199999570846558},"relativeTime":1.1,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.856998443603516,"y":49.002998352050781,"theta":0.79199999570846558},"relativeTime":1.2000000000000002,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":59.969001770019531,"y":49.132999420166016,"theta":0.79199999570846558},"relativeTime":1.3,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":60.081001281738281,"y":49.26300048828125,"theta":0.79199999570846558},"relativeTime":1.4000000000000001,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":60.193000793457031,"y":49.393001556396484,"theta":0.79199999570846558},"relativeTime":1.5,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":60.305000305175781,"y":49.522998809814453,"theta":0.79199999570846558},"relativeTime":1.6,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":60.416999816894531,"y":49.653999328613281,"theta":0.79199999570846558},"relativeTime":1.7000000000000002,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":60.528999328613281,"y":49.784000396728516,"theta":0.79199999570846558},"relativeTime":1.8,"vx":1.1210000514984131,"vy":1.3020000457763672},{"pathPoint":{"x":60.640998840332031,"y":49.91400146484375,"theta":0.79199999570846558},"relativeTime":1.9000000000000001,"vx":1.1210000514984131,"vy":1.3020000457763672}]}},{"id":495,"position":{"x":96.693931579589844,"y":81.710128784179688},"orientationRad":0.79336297512054443,"velocity":{"x":1.0535215139389038,"y":1.0170722007751465},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":1,"width":0.4140625,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":96.694000244140625,"y":81.709999084472656,"theta":0.7929999828338623},"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":96.799003601074219,"y":81.811996459960938,"theta":0.7929999828338623},"relativeTime":0.1,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":96.904998779296875,"y":81.91400146484375,"theta":0.7929999828338623},"relativeTime":0.2,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.010002136230469,"y":82.014999389648438,"theta":0.7929999828338623},"relativeTime":0.30000000000000004,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.114997863769531,"y":82.116996765136719,"theta":0.7929999828338623},"relativeTime":0.4,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.221000671386719,"y":82.219001770019531,"theta":0.7929999828338623},"relativeTime":0.5,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.325996398925781,"y":82.319999694824219,"theta":0.7929999828338623},"relativeTime":0.60000000000000009,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.430999755859375,"y":82.4219970703125,"theta":0.7929999828338623},"relativeTime":0.70000000000000007,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.537002563476562,"y":82.524002075195312,"theta":0.7929999828338623},"relativeTime":0.8,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.641998291015625,"y":82.625,"theta":0.7929999828338623},"relativeTime":0.9,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.747001647949219,"y":82.726997375488281,"theta":0.7929999828338623},"relativeTime":1,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.852996826171875,"y":82.8290023803711,"theta":0.7929999828338623},"relativeTime":1.1,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":97.958000183105469,"y":82.930999755859375,"theta":0.7929999828338623},"relativeTime":1.2000000000000002,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.064002990722656,"y":83.031997680664062,"theta":0.7929999828338623},"relativeTime":1.3,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.168998718261719,"y":83.134002685546875,"theta":0.7929999828338623},"relativeTime":1.4000000000000001,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.274002075195312,"y":83.236000061035156,"theta":0.7929999828338623},"relativeTime":1.5,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.379997253417969,"y":83.336997985839844,"theta":0.7929999828338623},"relativeTime":1.6,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.485000610351562,"y":83.439002990722656,"theta":0.7929999828338623},"relativeTime":1.7000000000000002,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.589996337890625,"y":83.541000366210938,"theta":0.7929999828338623},"relativeTime":1.8,"vx":1.0540000200271606,"vy":1.0169999599456787},{"pathPoint":{"x":98.695999145507812,"y":83.642997741699219,"theta":0.7929999828338623},"relativeTime":1.9000000000000001,"vx":1.0540000200271606,"vy":1.0169999599456787}]}},{"id":499,"position":{"x":84.916351318359375,"y":82.007492065429688},"orientationRad":0.79238647222518921,"velocity":{"x":1.0578123331069946,"y":1.0343115329742432},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":1,"width":0.3984375,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":84.916000366210938,"y":82.007003784179688,"theta":0.79199999570846558},"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.022003173828125,"y":82.111000061035156,"theta":0.79199999570846558},"relativeTime":0.1,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.127998352050781,"y":82.213996887207031,"theta":0.79199999570846558},"relativeTime":0.2,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.234001159667969,"y":82.318000793457031,"theta":0.79199999570846558},"relativeTime":0.30000000000000004,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.338996887207031,"y":82.4209976196289,"theta":0.79199999570846558},"relativeTime":0.4,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.444999694824219,"y":82.5250015258789,"theta":0.79199999570846558},"relativeTime":0.5,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.5510025024414,"y":82.627998352050781,"theta":0.79199999570846558},"relativeTime":0.60000000000000009,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.656997680664062,"y":82.732002258300781,"theta":0.79199999570846558},"relativeTime":0.70000000000000007,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.76300048828125,"y":82.834999084472656,"theta":0.79199999570846558},"relativeTime":0.8,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.867996215820312,"y":82.938003540039062,"theta":0.79199999570846558},"relativeTime":0.9,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":85.9739990234375,"y":83.041999816894531,"theta":0.79199999570846558},"relativeTime":1,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.080001831054688,"y":83.1449966430664,"theta":0.79199999570846558},"relativeTime":1.1,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.185997009277344,"y":83.2490005493164,"theta":0.79199999570846558},"relativeTime":1.2000000000000002,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.291999816894531,"y":83.351997375488281,"theta":0.79199999570846558},"relativeTime":1.3,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.397003173828125,"y":83.456001281738281,"theta":0.79199999570846558},"relativeTime":1.4000000000000001,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.502998352050781,"y":83.558998107910156,"theta":0.79199999570846558},"relativeTime":1.5,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.609001159667969,"y":83.662002563476562,"theta":0.79199999570846558},"relativeTime":1.6,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.714996337890625,"y":83.765998840332031,"theta":0.79199999570846558},"relativeTime":1.7000000000000002,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.819999694824219,"y":83.869003295898438,"theta":0.79199999570846558},"relativeTime":1.8,"vx":1.0579999685287476,"vy":1.034000039100647},{"pathPoint":{"x":86.9260025024414,"y":83.9729995727539,"theta":0.79199999570846558},"relativeTime":1.9000000000000001,"vx":1.0579999685287476,"vy":1.034000039100647}]}},{"id":504,"position":{"x":70.753768920898438,"y":60.167377471923828},"orientationRad":1.4808632135391235,"velocity":{"x":1.0832502841949463,"y":1.0057624578475952},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":2.78125,"width":1.6171875,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":70.753997802734375,"y":60.166999816894531,"theta":1.4809999465942383},"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":70.86199951171875,"y":60.268001556396484,"theta":1.4809999465942383},"relativeTime":0.1,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":70.970001220703125,"y":60.368999481201172,"theta":1.4809999465942383},"relativeTime":0.2,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.0790023803711,"y":60.469001770019531,"theta":1.4809999465942383},"relativeTime":0.30000000000000004,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.186996459960938,"y":60.569999694824219,"theta":1.4809999465942383},"relativeTime":0.4,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.294998168945312,"y":60.669998168945312,"theta":1.4809999465942383},"relativeTime":0.5,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.403999328613281,"y":60.770999908447266,"theta":1.4809999465942383},"relativeTime":0.60000000000000009,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.512001037597656,"y":60.870998382568359,"theta":1.4809999465942383},"relativeTime":0.70000000000000007,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.620002746582031,"y":60.972000122070312,"theta":1.4809999465942383},"relativeTime":0.8,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.728996276855469,"y":61.073001861572266,"theta":1.4809999465942383},"relativeTime":0.9,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.836997985839844,"y":61.173000335693359,"theta":1.4809999465942383},"relativeTime":1,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":71.944999694824219,"y":61.273998260498047,"theta":1.4809999465942383},"relativeTime":1.1,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.054000854492188,"y":61.374000549316406,"theta":1.4809999465942383},"relativeTime":1.2000000000000002,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.162002563476562,"y":61.474998474121094,"theta":1.4809999465942383},"relativeTime":1.3,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.2699966430664,"y":61.575000762939453,"theta":1.4809999465942383},"relativeTime":1.4000000000000001,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.378997802734375,"y":61.675998687744141,"theta":1.4809999465942383},"relativeTime":1.5,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.48699951171875,"y":61.777000427246094,"theta":1.4809999465942383},"relativeTime":1.6,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.595001220703125,"y":61.876998901367188,"theta":1.4809999465942383},"relativeTime":1.7000000000000002,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.7040023803711,"y":61.978000640869141,"theta":1.4809999465942383},"relativeTime":1.8,"vx":1.0829999446868896,"vy":1.00600004196167},{"pathPoint":{"x":72.811996459960938,"y":62.077999114990234,"theta":1.4809999465942383},"relativeTime":1.9000000000000001,"vx":1.0829999446868896,"vy":1.00600004196167}]}},{"id":506,"position":{"x":82.144119262695312,"y":62.491241455078125},"orientationRad":0.78945678472518921,"velocity":{"x":1.0840173959732056,"y":1.0489007234573364},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":1,"width":0.3984375,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":82.143997192382812,"y":62.491001129150391,"theta":0.78899997472763062},"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.252998352050781,"y":62.596000671386719,"theta":0.78899997472763062},"relativeTime":0.1,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.361000061035156,"y":62.701000213623047,"theta":0.78899997472763062},"relativeTime":0.2,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.469001770019531,"y":62.805999755859375,"theta":0.78899997472763062},"relativeTime":0.30000000000000004,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.5780029296875,"y":62.9109992980957,"theta":0.78899997472763062},"relativeTime":0.4,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.685997009277344,"y":63.015998840332031,"theta":0.78899997472763062},"relativeTime":0.5,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.794998168945312,"y":63.120998382568359,"theta":0.78899997472763062},"relativeTime":0.60000000000000009,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":82.902999877929688,"y":63.224998474121094,"theta":0.78899997472763062},"relativeTime":0.70000000000000007,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.011001586914062,"y":63.330001831054688,"theta":0.78899997472763062},"relativeTime":0.8,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.120002746582031,"y":63.435001373291016,"theta":0.78899997472763062},"relativeTime":0.9,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.227996826171875,"y":63.540000915527344,"theta":0.78899997472763062},"relativeTime":1,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.336997985839844,"y":63.645000457763672,"theta":0.78899997472763062},"relativeTime":1.1,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.444999694824219,"y":63.75,"theta":0.78899997472763062},"relativeTime":1.2000000000000002,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.5530014038086,"y":63.854999542236328,"theta":0.78899997472763062},"relativeTime":1.3,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.662002563476562,"y":63.959999084472656,"theta":0.78899997472763062},"relativeTime":1.4000000000000001,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.7699966430664,"y":64.06500244140625,"theta":0.78899997472763062},"relativeTime":1.5,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.877998352050781,"y":64.168998718261719,"theta":0.78899997472763062},"relativeTime":1.6,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":83.98699951171875,"y":64.274002075195312,"theta":0.78899997472763062},"relativeTime":1.7000000000000002,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":84.095001220703125,"y":64.378997802734375,"theta":0.78899997472763062},"relativeTime":1.8,"vx":1.0839999914169312,"vy":1.0490000247955322},{"pathPoint":{"x":84.2040023803711,"y":64.484001159667969,"theta":0.78899997472763062},"relativeTime":1.9000000000000001,"vx":1.0839999914169312,"vy":1.0490000247955322}]}},{"id":511,"position":{"x":63.550273895263672,"y":37.448223114013672},"orientationRad":0.78164428472518921,"velocity":{"x":1.1137884855270386,"y":1.0899480581283569},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":2.453125,"width":0.3984375,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":63.549999237060547,"y":37.448001861572266,"theta":0.78200000524520874},"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":63.6619987487793,"y":37.556999206542969,"theta":0.78200000524520874},"relativeTime":0.1,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":63.772998809814453,"y":37.666000366210938,"theta":0.78200000524520874},"relativeTime":0.2,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":63.883998870849609,"y":37.775001525878906,"theta":0.78200000524520874},"relativeTime":0.30000000000000004,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":63.995998382568359,"y":37.883998870849609,"theta":0.78200000524520874},"relativeTime":0.4,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.107002258300781,"y":37.993000030517578,"theta":0.78200000524520874},"relativeTime":0.5,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.219001770019531,"y":38.102001190185547,"theta":0.78200000524520874},"relativeTime":0.60000000000000009,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.330001831054688,"y":38.21099853515625,"theta":0.78200000524520874},"relativeTime":0.70000000000000007,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.441001892089844,"y":38.319999694824219,"theta":0.78200000524520874},"relativeTime":0.8,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.5530014038086,"y":38.429000854492188,"theta":0.78200000524520874},"relativeTime":0.9,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.66400146484375,"y":38.537998199462891,"theta":0.78200000524520874},"relativeTime":1,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.7750015258789,"y":38.646999359130859,"theta":0.78200000524520874},"relativeTime":1.1,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.887001037597656,"y":38.756000518798828,"theta":0.78200000524520874},"relativeTime":1.2000000000000002,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":64.998001098632812,"y":38.8650016784668,"theta":0.78200000524520874},"relativeTime":1.3,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":65.110000610351562,"y":38.9739990234375,"theta":0.78200000524520874},"relativeTime":1.4000000000000001,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":65.221000671386719,"y":39.083000183105469,"theta":0.78200000524520874},"relativeTime":1.5,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":65.332000732421875,"y":39.192001342773438,"theta":0.78200000524520874},"relativeTime":1.6,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":65.444000244140625,"y":39.300998687744141,"theta":0.78200000524520874},"relativeTime":1.7000000000000002,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":65.555000305175781,"y":39.409999847412109,"theta":0.78200000524520874},"relativeTime":1.8,"vx":1.1139999628067017,"vy":1.0900000333786011},{"pathPoint":{"x":65.666999816894531,"y":39.519001007080078,"theta":0.78200000524520874},"relativeTime":1.9000000000000001,"vx":1.1139999628067017,"vy":1.0900000333786011}]}},{"id":514,"position":{"x":94.142257690429688,"y":78.1624755859375},"orientationRad":0.79336297512054443,"velocity":{"x":1.060375452041626,"y":1.0290393829345703},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":1,"width":0.3984375,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":94.141998291015625,"y":78.162002563476562,"theta":0.7929999828338623},"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.248001098632812,"y":78.264999389648438,"theta":0.7929999828338623},"relativeTime":0.1,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.353996276855469,"y":78.367996215820312,"theta":0.7929999828338623},"relativeTime":0.2,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.459999084472656,"y":78.471000671386719,"theta":0.7929999828338623},"relativeTime":0.30000000000000004,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.566001892089844,"y":78.5739974975586,"theta":0.7929999828338623},"relativeTime":0.4,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.6719970703125,"y":78.677001953125,"theta":0.7929999828338623},"relativeTime":0.5,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.778999328613281,"y":78.779998779296875,"theta":0.7929999828338623},"relativeTime":0.60000000000000009,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.885002136230469,"y":78.883003234863281,"theta":0.7929999828338623},"relativeTime":0.70000000000000007,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":94.990997314453125,"y":78.986000061035156,"theta":0.7929999828338623},"relativeTime":0.8,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.097000122070312,"y":79.088996887207031,"theta":0.7929999828338623},"relativeTime":0.9,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.2030029296875,"y":79.192001342773438,"theta":0.7929999828338623},"relativeTime":1,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.308998107910156,"y":79.293998718261719,"theta":0.7929999828338623},"relativeTime":1.1,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.415000915527344,"y":79.397003173828125,"theta":0.7929999828338623},"relativeTime":1.2000000000000002,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.521003723144531,"y":79.5,"theta":0.7929999828338623},"relativeTime":1.3,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.626998901367188,"y":79.602996826171875,"theta":0.7929999828338623},"relativeTime":1.4000000000000001,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.733001708984375,"y":79.706001281738281,"theta":0.7929999828338623},"relativeTime":1.5,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.838996887207031,"y":79.808998107910156,"theta":0.7929999828338623},"relativeTime":1.6,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":95.944999694824219,"y":79.912002563476562,"theta":0.7929999828338623},"relativeTime":1.7000000000000002,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":96.0510025024414,"y":80.014999389648438,"theta":0.7929999828338623},"relativeTime":1.8,"vx":1.059999942779541,"vy":1.0290000438690186},{"pathPoint":{"x":96.156997680664062,"y":80.117996215820312,"theta":0.7929999828338623},"relativeTime":1.9000000000000001,"vx":1.059999942779541,"vy":1.0290000438690186}]}},{"id":515,"position":{"x":113.54264831542969,"y":93.712997436523438},"orientationRad":0.78750354051589966,"velocity":{"x":1.0848567485809326,"y":1.0394270420074463},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":1.5,"width":0.3984375,"height":2,"type":"CAR","trajectory":{"trajectoryPoint":[{"pathPoint":{"x":113.54299926757812,"y":93.712997436523438,"theta":0.78799998760223389},"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":113.6510009765625,"y":93.817001342773438,"theta":0.78799998760223389},"relativeTime":0.1,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":113.76000213623047,"y":93.9209976196289,"theta":0.78799998760223389},"relativeTime":0.2,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":113.86799621582031,"y":94.0250015258789,"theta":0.78799998760223389},"relativeTime":0.30000000000000004,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":113.97699737548828,"y":94.128997802734375,"theta":0.78799998760223389},"relativeTime":0.4,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.08499908447266,"y":94.233001708984375,"theta":0.78799998760223389},"relativeTime":0.5,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.19400024414062,"y":94.336997985839844,"theta":0.78799998760223389},"relativeTime":0.60000000000000009,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.302001953125,"y":94.441001892089844,"theta":0.78799998760223389},"relativeTime":0.70000000000000007,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.41100311279297,"y":94.544998168945312,"theta":0.78799998760223389},"relativeTime":0.8,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.51899719238281,"y":94.648002624511719,"theta":0.78799998760223389},"relativeTime":0.9,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.62699890136719,"y":94.751998901367188,"theta":0.78799998760223389},"relativeTime":1,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.73600006103516,"y":94.856002807617188,"theta":0.78799998760223389},"relativeTime":1.1,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.84400177001953,"y":94.959999084472656,"theta":0.78799998760223389},"relativeTime":1.2000000000000002,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":114.9530029296875,"y":95.064002990722656,"theta":0.78799998760223389},"relativeTime":1.3,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":115.06099700927734,"y":95.167999267578125,"theta":0.78799998760223389},"relativeTime":1.4000000000000001,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":115.16999816894531,"y":95.272003173828125,"theta":0.78799998760223389},"relativeTime":1.5,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":115.27799987792969,"y":95.3759994506836,"theta":0.78799998760223389},"relativeTime":1.6,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":115.38700103759766,"y":95.4800033569336,"theta":0.78799998760223389},"relativeTime":1.7000000000000002,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":115.49500274658203,"y":95.583999633789062,"theta":0.78799998760223389},"relativeTime":1.8,"vx":1.0850000381469727,"vy":1.0390000343322754},{"pathPoint":{"x":115.60399627685547,"y":95.688003540039062,"theta":0.78799998760223389},"relativeTime":1.9000000000000001,"vx":1.0850000381469727,"vy":1.0390000343322754}]}},{"id":528,"position":{"x":135.61335754394531,"y":141.5985107421875},"orientationRad":0.79236865043640137,"velocity":{"x":2.5911893844604492,"y":2.910409688949585},"accelerate":{"x":-0.51524966955184937,"y":-0.52248328924179077},"length":4.6296396255493164,"width":1.8861149549484253,"height":1.68673837184906,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":135.61300659179688,"y":141.5989990234375,"theta":0.79199999570846558},"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":135.87199401855469,"y":141.88999938964844,"theta":0.79199999570846558},"relativeTime":0.1,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":136.13200378417969,"y":142.18099975585938,"theta":0.79199999570846558},"relativeTime":0.2,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":136.39100646972656,"y":142.47200012207031,"theta":0.79199999570846558},"relativeTime":0.30000000000000004,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":136.64999389648438,"y":142.76300048828125,"theta":0.79199999570846558},"relativeTime":0.4,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":136.90899658203125,"y":143.05400085449219,"theta":0.79199999570846558},"relativeTime":0.5,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":137.16799926757812,"y":143.34500122070312,"theta":0.79199999570846558},"relativeTime":0.60000000000000009,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":137.427001953125,"y":143.63600158691406,"theta":0.79199999570846558},"relativeTime":0.70000000000000007,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":137.68600463867188,"y":143.927001953125,"theta":0.79199999570846558},"relativeTime":0.8,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":137.94500732421875,"y":144.21800231933594,"theta":0.79199999570846558},"relativeTime":0.9,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":138.20500183105469,"y":144.50900268554688,"theta":0.79199999570846558},"relativeTime":1,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":138.46400451660156,"y":144.80000305175781,"theta":0.79199999570846558},"relativeTime":1.1,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":138.72300720214844,"y":145.09100341796875,"theta":0.79199999570846558},"relativeTime":1.2000000000000002,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":138.98199462890625,"y":145.38200378417969,"theta":0.79199999570846558},"relativeTime":1.3,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":139.24099731445312,"y":145.67300415039062,"theta":0.79199999570846558},"relativeTime":1.4000000000000001,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":139.5,"y":145.96400451660156,"theta":0.79199999570846558},"relativeTime":1.5,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":139.75900268554688,"y":146.2550048828125,"theta":0.79199999570846558},"relativeTime":1.6,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":140.01800537109375,"y":146.54600524902344,"theta":0.79199999570846558},"relativeTime":1.7000000000000002,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":140.27799987792969,"y":146.83700561523438,"theta":0.79199999570846558},"relativeTime":1.8,"vx":2.5910000801086426,"vy":2.9100000858306885},{"pathPoint":{"x":140.53700256347656,"y":147.12800598144531,"theta":0.79199999570846558},"relativeTime":1.9000000000000001,"vx":2.5910000801086426,"vy":2.9100000858306885}]}}]})..";
        auto msg = std::make_shared<rainbowdash::location::FusionObstacles>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_fusion_obstacles(msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);
}

TEST_F(AEBServerTest, Test_proc_turn_left_dynamic_direct_collision3) {
    g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    {
        // 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1749439672.975224,"moduleName":"io_server","sequenceNum":72549},"gearLocationb":"DRIVING","vehicleSignal":{"standstillSt":"FALSE","epbSt":"FALSE","driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":3.07813,"wheelSpeedFl":3.2187498940361872,"wheelSpeedFr":3.3749998940361872,"wheelSpeedRl":3.1875001059638128,"wheelSpeedRr":3.3906250529819064},"brakePercentage":21,"acceleration":{"longitudinal":-1.****************,"lateral":0.81000000238418579},"yawRate":0.114144541,"steeringSystem":{"steeringWheelAngle":120.009999694824,"steeringWheelRate":100,"steeringWheelTorque":1.6299999952316284},"curTorque":-68,"slopeEstimation":-0.0111319516})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }

    {
        // 设置毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1749439673.1618478,"moduleName":"io_server","sequenceNum":14656},"detectionList":[{"position":{"x":7.6796875,"y":1.328125},"velocity":1.703125,"accelerate":0.140625,"length":0.75,"lengthCovariance":1,"width":0.3984375,"widthCovariance":0.0078125,"trackAge":46,"existProbablity":0.996078432,"orientationRad":-3.05759931,"orientationCovariance":0.0234375,"yawRate":0.0498046875,"yawRateCovariance":0.0078125,"covariance":[1.0078125,0.0390625,0.1171875,21.0390625,0.0234375],"measurementTime":1749439673.039},{"position":{"x":7.4140625,"y":1.2578125},"velocity":1.6484375,"accelerate":-0.1015625,"length":0.75,"lengthCovariance":0.984375,"width":0.3984375,"widthCovariance":0.0078125,"trackAge":47,"existProbablity":0.996078432,"orientationRad":-3.05759931,"orientationCovariance":0.0234375,"yawRate":0.05078125,"yawRateCovariance":0.0078125,"covariance":[0.9921875,0.0390625,0.1171875,21.0390625,0.015625],"measurementTime":1749439673.096}],"measurementTime":1412256})..";
        auto msg = std::make_shared<rainbowdash::drivers::NewFareoRadarDetections>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_new_radar_detect_obstacles(msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->aeb_conf_.UseFusion = false;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
}

// fusion 直行，静止车辆
TEST_F(AEBServerTest, Test_fusion_direct_static_proc) {
    //g_aeb_component->aeb_.init(g_aeb_component->get_vehicle_param(), g_aeb_component->get_aeb_conf());    //重新初始化所有数据
    g_aeb_component->init();    //重新初始化所有数据
    {// 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1750226251.3279181,"moduleName":"io_server","sequenceNum":1999},"gearLocationb":"DRIVING","vehicleSignal":{"lowBeam":true,"standstillSt":"FALSE","epbSt":"FALSE","driverbeltSwsigSt":1,"passengerBeltSt":1,"rrBeltSt":1,"driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.312*************,"wheelSpeedFl":1.312*************,"wheelSpeedFr":1.312*************,"wheelSpeedRl":1.312*************,"wheelSpeedRr":1.312*************},"brakePercentage":9,"acceleration":{"longitudinal":0.0099999997764825821,"lateral":-0.12999999523162842},"yawRate":0.00209439523,"steeringSystem":{"steeringWheelAngle":1,"steeringWheelTorque":-0.37000000476837158},"curTorque":320,"slopeEstimation":0.00640164129})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {   // 设置Vehicle State
        std::string str_json = R"..({"header":{"timestamp":1750226251.194,"sequenceNum":1999},"position":{"x":7.693554616922901,"y":-0.16828219503013297},"orientation":{"qz":-0.01513723767200525,"qw":0.99981490002940276},"linearVelocity":{"x":1.312*************},"heading":-0.030273497483185086,"linearAccelerationVrf":{"x":-0.046671194738965277,"y":0.13280046082831393,"z":-0.057138740595377976},"angularVelocityVrf":{"x":-0.0038014683207986491,"y":-0.0023961617664902722,"z":0.00080295643642761547},"eulerAngles":{"z":-0.030273497483185086},"poseState":true})..";
        auto msg = std::make_shared<rainbowdash::location::Pose>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_vehicle_state(msg);
    }
    {// 设置视觉融合毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1750226251.194},"obstacleRaws":[{"id":62,"position":{"x":16.617610931396484,"y":-0.19780531525611877},"orientationRad":-0.030273512005805969,"velocity":{"x":-0.****************,"y":0.093034379184246063},"accelerate":{"x":-0.046649809926748276,"y":0.001412685145623982},"length":4.5072331428527832,"width":1.826876163482666,"height":1.607573390007019,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":16.618000030517578,"y":-0.19799999892711639,"theta":-0.029999999329447746},"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.565999984741211,"y":-0.18899999558925629,"theta":-0.029999999329447746},"relativeTime":0.1,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.513999938964844,"y":-0.17900000512599945,"theta":-0.029999999329447746},"relativeTime":0.2,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.461999893188477,"y":-0.17000000178813934,"theta":-0.029999999329447746},"relativeTime":0.30000000000000004,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.409999847412109,"y":-0.16099999845027924,"theta":-0.029999999329447746},"relativeTime":0.4,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.357999801635742,"y":-0.15099999308586121,"theta":-0.029999999329447746},"relativeTime":0.5,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.305999755859375,"y":-0.14200000464916229,"theta":-0.029999999329447746},"relativeTime":0.60000000000000009,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.253999710083008,"y":-0.13300000131130219,"theta":-0.029999999329447746},"relativeTime":0.70000000000000007,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.201999664306641,"y":-0.12300000339746475,"theta":-0.029999999329447746},"relativeTime":0.8,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.149999618530273,"y":-0.11400000005960464,"theta":-0.029999999329447746},"relativeTime":0.9,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.097999572753906,"y":-0.10499999672174454,"theta":-0.029999999329447746},"relativeTime":1,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":16.045999526977539,"y":-0.0949999988079071,"theta":-0.029999999329447746},"relativeTime":1.1,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.994000434875488,"y":-0.0860000029206276,"theta":-0.029999999329447746},"relativeTime":1.2000000000000002,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.942000389099121,"y":-0.076999999582767487,"theta":-0.029999999329447746},"relativeTime":1.3,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.890000343322754,"y":-0.068000003695487976,"theta":-0.029999999329447746},"relativeTime":1.4000000000000001,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.838000297546387,"y":-0.057999998331069946,"theta":-0.029999999329447746},"relativeTime":1.5,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.78600025177002,"y":-0.048999998718500137,"theta":-0.029999999329447746},"relativeTime":1.6,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.734000205993652,"y":-0.039999999105930328,"theta":-0.029999999329447746},"relativeTime":1.7000000000000002,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.682000160217285,"y":-0.029999999329447746,"theta":-0.029999999329447746},"relativeTime":1.8,"vx":-0.51999998092651367,"vy":0.093000002205371857},{"pathPoint":{"x":15.630000114440918,"y":-0.020999999716877937,"theta":-0.029999999329447746},"relativeTime":1.9000000000000001,"vx":-0.51999998092651367,"vy":0.093000002205371857}]}},{"id":66,"position":{"x":34.232517242431641,"y":-3.7826669216156006},"orientationRad":-0.030273512005805969,"velocity":{"x":0.69206136465072632,"y":-0.069809600710868835},"accelerate":{"x":-0.046649809926748276,"y":0.001412685145623982},"length":12.592301368713379,"width":2.6413784027099609,"height":3.7064304351806641,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":34.233001708984375,"y":-3.7829999923706055,"theta":-0.029999999329447746},"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.301998138427734,"y":-3.7899999618530273,"theta":-0.029999999329447746},"relativeTime":0.1,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.370998382568359,"y":-3.7969999313354492,"theta":-0.029999999329447746},"relativeTime":0.2,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.439998626708984,"y":-3.8039999008178711,"theta":-0.029999999329447746},"relativeTime":0.30000000000000004,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.508998870849609,"y":-3.8110001087188721,"theta":-0.029999999329447746},"relativeTime":0.4,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.578998565673828,"y":-3.8180000782012939,"theta":-0.029999999329447746},"relativeTime":0.5,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.647998809814453,"y":-3.8250000476837158,"theta":-0.029999999329447746},"relativeTime":0.60000000000000009,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.716999053955078,"y":-3.8320000171661377,"theta":-0.029999999329447746},"relativeTime":0.70000000000000007,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.7859992980957,"y":-3.8389999866485596,"theta":-0.029999999329447746},"relativeTime":0.8,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.854999542236328,"y":-3.8450000286102295,"theta":-0.029999999329447746},"relativeTime":0.9,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.924999237060547,"y":-3.8519999980926514,"theta":-0.029999999329447746},"relativeTime":1,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":34.993999481201172,"y":-3.8589999675750732,"theta":-0.029999999329447746},"relativeTime":1.1,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.0629997253418,"y":-3.8659999370574951,"theta":-0.029999999329447746},"relativeTime":1.2000000000000002,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.131999969482422,"y":-3.872999906539917,"theta":-0.029999999329447746},"relativeTime":1.3,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.201000213623047,"y":-3.880000114440918,"theta":-0.029999999329447746},"relativeTime":1.4000000000000001,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.270999908447266,"y":-3.88700008392334,"theta":-0.029999999329447746},"relativeTime":1.5,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.340000152587891,"y":-3.8940000534057617,"theta":-0.029999999329447746},"relativeTime":1.6,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.409000396728516,"y":-3.9010000228881836,"theta":-0.029999999329447746},"relativeTime":1.7000000000000002,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.478000640869141,"y":-3.9079999923706055,"theta":-0.029999999329447746},"relativeTime":1.8,"vx":0.69199997186660767,"vy":-0.070000000298023224},{"pathPoint":{"x":35.547000885009766,"y":-3.9149999618530273,"theta":-0.029999999329447746},"relativeTime":1.9000000000000001,"vx":0.69199997186660767,"vy":-0.070000000298023224}]}},{"id":71,"position":{"x":15.753561019897461,"y":2.3760042190551758},"orientationRad":-0.030273512005805969,"velocity":{"x":0.47729259729385376,"y":-0.26516556739807129},"accelerate":{"x":-0.046649809926748276,"y":0.001412685145623982},"length":11.497333526611328,"width":2.6245813369750977,"height":3.2294216156005859,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":15.753999710083008,"y":2.375999927520752,"theta":-0.029999999329447746},"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":15.800999641418457,"y":2.3489999771118164,"theta":-0.029999999329447746},"relativeTime":0.1,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":15.848999977111816,"y":2.3229999542236328,"theta":-0.029999999329447746},"relativeTime":0.2,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":15.897000312805176,"y":2.2960000038146973,"theta":-0.029999999329447746},"relativeTime":0.30000000000000004,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":15.944000244140625,"y":2.2699999809265137,"theta":-0.029999999329447746},"relativeTime":0.4,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":15.991999626159668,"y":2.2430000305175781,"theta":-0.029999999329447746},"relativeTime":0.5,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.040000915527344,"y":2.2170000076293945,"theta":-0.029999999329447746},"relativeTime":0.60000000000000009,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.08799934387207,"y":2.190000057220459,"theta":-0.029999999329447746},"relativeTime":0.70000000000000007,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.135000228881836,"y":2.1640000343322754,"theta":-0.029999999329447746},"relativeTime":0.8,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.183000564575195,"y":2.13700008392334,"theta":-0.029999999329447746},"relativeTime":0.9,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.231000900268555,"y":2.1110000610351562,"theta":-0.029999999329447746},"relativeTime":1,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.278999328613281,"y":2.0840001106262207,"theta":-0.029999999329447746},"relativeTime":1.1,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.326000213623047,"y":2.0580000877380371,"theta":-0.029999999329447746},"relativeTime":1.2000000000000002,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.374000549316406,"y":2.0309998989105225,"theta":-0.029999999329447746},"relativeTime":1.3,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.422000885009766,"y":2.005000114440918,"theta":-0.029999999329447746},"relativeTime":1.4000000000000001,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.469999313354492,"y":1.9780000448226929,"theta":-0.029999999329447746},"relativeTime":1.5,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.517000198364258,"y":1.9520000219345093,"theta":-0.029999999329447746},"relativeTime":1.6,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.565000534057617,"y":1.9249999523162842,"theta":-0.029999999329447746},"relativeTime":1.7000000000000002,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.613000869750977,"y":1.8990000486373901,"theta":-0.029999999329447746},"relativeTime":1.8,"vx":0.*****************,"vy":-0.26499998569488525},{"pathPoint":{"x":16.659999847412109,"y":1.871999979019165,"theta":-0.029999999329447746},"relativeTime":1.9000000000000001,"vx":0.*****************,"vy":-0.26499998569488525}]}}]})..";
        auto msg = std::make_shared<rainbowdash::location::FusionObstacles>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_fusion_obstacles(msg);
    }
    g_aeb_component->acc_started_ = mega::aeb::CommonBool::NONE;
    g_aeb_component->abe_enabled_ = mega::aeb::CommonBool::TRUE;
    g_aeb_component->fcw_enabled_ = mega::aeb::CommonBool::TRUE;
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 0);

    {// 设置底盘信息
        std::string str_json = R"..({"header":{"timestamp":1750226252.3287351,"moduleName":"io_server","sequenceNum":2049},"gearLocationb":"DRIVING","vehicleSignal":{"lowBeam":true,"standstillSt":"FALSE","epbSt":"FALSE","driverbeltSwsigSt":1,"passengerBeltSt":1,"rrBeltSt":1,"driveReadySt":1},"vehicleSpeed":{"vehicleSpeed":1.0468749867545233,"wheelSpeedFl":1.0468749867545233,"wheelSpeedFr":1.0468749867545233,"wheelSpeedRl":1.0625000132454765,"wheelSpeedRr":1.0312500264909532},"brakePercentage":1,"acceleration":{"longitudinal":-0.34999999403953552,"lateral":-0.019999999552965164},"yawRate":0.000523598806,"steeringSystem":{"steeringWheelAngle":0.*****************,"steeringWheelTorque":-0.31000000238418579},"curTorque":316,"slopeEstimation":0.00135562639})..";
        auto msg = std::make_shared<rainbowdash::control_by_wire::Chassis>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        // 执行被测函数
        mega::aeb::update_long_chassis_info(msg);
    }
    {   // 设置Vehicle State
        std::string str_json = R"..({"header":{"timestamp":1750226252.196,"sequenceNum":2049},"position":{"x":8.92233350390292,"y":-0.20483411004113142},"orientation":{"qz":-0.014816134103094604,"qw":0.99976212982639767},"linearVelocity":{"x":1.0781249735090468},"heading":-0.02962955806600398,"linearAccelerationVrf":{"x":-0.****************,"y":0.20188766536616093,"z":0.077081099474372514},"angularVelocityVrf":{"x":0.0052194106250233289,"y":0.0014848280480067747,"z":0.00017401171360898157},"eulerAngles":{"z":-0.02962955806600398},"poseState":true})..";
        auto msg = std::make_shared<rainbowdash::location::Pose>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_vehicle_state(msg);
    }
    {// 设置视觉融合毫米波雷达数据
        std::string str_json = R"..({"header":{"timestamp":1750226252.196},"obstacleRaws":[{"id":62,"position":{"x":16.758703231811523,"y":-0.28399044275283813},"orientationRad":-0.029629569500684738,"velocity":{"x":-0.073764406144618988,"y":-0.044665850698947906},"accelerate":{"x":-0.*****************,"y":0.01259120274335146},"length":4.5944652557373047,"width":1.8556780815124512,"height":1.6321719884872437,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":16.759000778198242,"y":-0.28400000929832458,"theta":-0.029999999329447746},"vx":-0.07****************,"vy":-0.045000001788139343}]}},{"id":66,"position":{"x":35.797599792480469,"y":-3.8550915718078613},"orientationRad":-0.029629569500684738,"velocity":{"x":1.9924064874649048,"y":-0.052728865295648575},"accelerate":{"x":-0.*****************,"y":0.01259120274335146},"length":8.46869945526123,"width":2.5657923221588135,"height":3.6491894721984863,"trajectory":{"trajectoryPoint":[{"pathPoint":{"x":35.798000335693359,"y":-3.8550000190734863,"theta":-0.029999999329447746},"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":35.997001647949219,"y":-3.8599998950958252,"theta":-0.029999999329447746},"relativeTime":0.1,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":36.195999145507812,"y":-3.8659999370574951,"theta":-0.029999999329447746},"relativeTime":0.2,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":36.395000457763672,"y":-3.8710000514984131,"theta":-0.029999999329447746},"relativeTime":0.30000000000000004,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":36.595001220703125,"y":-3.875999927520752,"theta":-0.029999999329447746},"relativeTime":0.4,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":36.793998718261719,"y":-3.88100004196167,"theta":-0.029999999329447746},"relativeTime":0.5,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":36.993000030517578,"y":-3.88700008392334,"theta":-0.029999999329447746},"relativeTime":0.60000000000000009,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":37.192001342773438,"y":-3.8919999599456787,"theta":-0.029999999329447746},"relativeTime":0.70000000000000007,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":37.391998291015625,"y":-3.8970000743865967,"theta":-0.029999999329447746},"relativeTime":0.8,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":37.590999603271484,"y":-3.9030001163482666,"theta":-0.029999999329447746},"relativeTime":0.9,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":37.790000915527344,"y":-3.9079999923706055,"theta":-0.029999999329447746},"relativeTime":1,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":37.988998413085938,"y":-3.9130001068115234,"theta":-0.029999999329447746},"relativeTime":1.1,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":38.1879997253418,"y":-3.9179999828338623,"theta":-0.029999999329447746},"relativeTime":1.2000000000000002,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":38.38800048828125,"y":-3.9240000247955322,"theta":-0.029999999329447746},"relativeTime":1.3,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":38.587001800537109,"y":-3.9289999008178711,"theta":-0.029999999329447746},"relativeTime":1.4000000000000001,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":38.7859992980957,"y":-3.9340000152587891,"theta":-0.029999999329447746},"relativeTime":1.5,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":38.985000610351562,"y":-3.9389998912811279,"theta":-0.029999999329447746},"relativeTime":1.6,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":39.185001373291016,"y":-3.9449999332427979,"theta":-0.029999999329447746},"relativeTime":1.7000000000000002,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":39.383998870849609,"y":-3.9500000476837158,"theta":-0.029999999329447746},"relativeTime":1.8,"vx":1.9919999837875366,"vy":-0.05299999937415123},{"pathPoint":{"x":39.583000183105469,"y":-3.9549999237060547,"theta":-0.029999999329447746},"relativeTime":1.9000000000000001,"vx":1.9919999837875366,"vy":-0.05299999937415123}]}}]})..";
        auto msg = std::make_shared<rainbowdash::location::FusionObstacles>();
        nlohmann::json jf = nlohmann::json::parse(str_json, nullptr, false); // parse without exceptions
        EXPECT_FALSE(jf.is_discarded());
        google::protobuf::util::JsonStringToMessage(jf.dump(), msg.get());
        mega::aeb::update_fusion_obstacles(msg);
    }
    // 执行被测函数
    g_aeb_component->proc();

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    auto console_msg = ::testing::internal::GetCapturedStderr();
    auto pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL2");
    EXPECT_NE(pos, std::string::npos);

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);

    // 执行被测函数
    testing::internal::CaptureStderr();
    g_aeb_component->proc();
    console_msg = ::testing::internal::GetCapturedStderr();
    pos = console_msg.find("AEB_ACUTE_WARNING_LEVEL4");
    EXPECT_NE(pos, std::string::npos);

    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.fcw_level_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.last_latent_warning_status_, 0);
    EXPECT_EQ(g_aeb_component->aeb_.aeb_control_.block_states_.eba_level_, 3);
}
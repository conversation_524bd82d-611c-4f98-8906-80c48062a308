{"nodeName": "io_server", "subChannels": {"io_in_system_check_req": {"name": "io/in/systerm_check_req", "cycleS": -1, "pendingQueueSize": 1}, "io_in_shake_hand_req": {"name": "io/in/shake_hand_req", "cycleS": -1, "pendingQueueSize": 1}, "io_in_acc_shake_hand_req": {"name": "io/in/acc_shake_hand_req", "cycleS": -1, "pendingQueueSize": 1}, "io_stop": {"name": "io/stop", "cycleS": -1, "pendingQueueSize": 1}, "io_in_chassisctl": {"name": "io/in/chassisctl", "cycleS": 0.02, "pendingQueueSize": 1}, "io_in_trq_chassisctl": {"name": "io/in/trq_chassisctl", "cycleS": 0.02, "pendingQueueSize": 1}, "control_aeb": {"name": "control/aeb", "cycleS": -1, "pendingQueueSize": 1}, "control_lamp": {"name": "control/lamp", "cycleS": -1, "pendingQueueSize": 1}, "io_in_changan_holdpressure": {"name": "io/in/changan_holdpressure", "cycleS": -1, "pendingQueueSize": 1}, "app_rpa_resp": {"name": "channel/app_rpa_resp", "cycleS": -1, "pendingQueueSize": 1}, "key_rpa_resp": {"name": "channel/key_rpa_resp", "cycleS": -1, "pendingQueueSize": 1}, "test_io_takeover": {"name": "test/io_in/takeover", "cycleS": 0.01, "pendingQueueSize": 1}, "test_io_control": {"name": "test/io_in/control", "cycleS": 0.01, "pendingQueueSize": 1}, "test_unit_control": {"name": "test/io_in/unittest", "cycleS": 0.01, "pendingQueueSize": 1}, "test_enable_uss": {"name": "test/io_in/enalbe<PERSON>", "cycleS": 0.01, "pendingQueueSize": 1}, "decision_out_apa_status": {"name": "decision/out/apa_status", "cycleS": -1, "pendingQueueSize": 1}, "location_request_channel_init": {"name": "location/req_channel_for_init", "cycleS": 0.01, "pendingQueueSize": 1}, "aeb_aebcontroller": {"name": "aeb/aebcontroller", "cycleS": -1, "pendingQueueSize": 2}}, "pubChannels": {"io_wheelspeed": {"name": "channel/wheelspeed", "cycleS": 0.02, "pendingQueueSize": 1}, "io_out_chassisinfo_lon": {"name": "io/out/chassisinfo_lon", "cycleS": 0.02, "pendingQueueSize": 1}, "io_out_chassisinfo_lat": {"name": "io/out/chassisinfo_lat", "cycleS": 0.02, "pendingQueueSize": 1}, "io_out_system_check_ret": {"name": "io/out/systerm_check_ret", "cycleS": -1, "pendingQueueSize": 1}, "io_out_shake_hand_ret": {"name": "io/out/shake_hand_ret", "cycleS": -1, "pendingQueueSize": 1}, "io_out_acc_shake_hand_ret": {"name": "io/out/acc_shake_hand_ret", "cycleS": -1, "pendingQueueSize": 1}, "app_rpa_req": {"name": "channel/app_rpa_req", "cycleS": -1, "pendingQueueSize": 1}, "key_rpa_req": {"name": "channel/key_rpa_req", "cycleS": -1, "pendingQueueSize": 1}, "imu": {"name": "channel/rawimu", "cycleS": 0.02, "pendingQueueSize": 1}, "chassis_imu": {"name": "channel/ChassisImu", "cycleS": 0.02, "pendingQueueSize": 1}, "driver_ultrasonic": {"name": "channel/ultrasonic", "cycleS": 0.02, "pendingQueueSize": 1}, "vehicle_body_info": {"name": "io/out/vehicle_body_info", "cycleS": 0.01, "pendingQueueSize": 1}, "io_out_chassis_gearshift": {"name": "io/out/chassis_gearshift", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_speed": {"name": "vehicle_body_info/speed", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_drive_ready_st": {"name": "vehicle_body_info/drive_ready_st", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_doorswst": {"name": "vehicle_body_info/door_sw_st", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_driverbelt_swsigst": {"name": "vehicle_body_info/driverbelt_swsig_st", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_rearvmirrorfb_st": {"name": "vehicle_body_info/rearvmirrorfb_st", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_pot_backdoor_posst": {"name": "vehicle_body_info/pot_backdoor_posst", "cycleS": -1, "pendingQueueSize": 1}, "vehicle_body_info_pedal_travel_sensor": {"name": "vehicle_body_info/pedal_travel_sensor", "cycleS": -1, "pendingQueueSize": 1}, "gear_info_for_location": {"name": "channel/location/gear_info", "cycleS": -1, "pendingQueueSize": 1}, "standstill_info_for_location": {"name": "channel/location/standstill_info", "cycleS": -1, "pendingQueueSize": 1}, "steer_angle_info_for_location": {"name": "channel/location/steer_angle_info", "cycleS": -1, "pendingQueueSize": 1}, "test_chassis_info_debug": {"name": "test/io_out/io_chassis_info_debug", "cycleS": 0.01, "pendingQueueSize": 1}, "io_out_apa_status_ack": {"name": "io/out/apa_status_ack", "cycleS": -1, "pendingQueueSize": 1}, "l2_chassis": {"name": "channel/chassis", "cycleS": -1, "pendingQueueSize": 1}, "radar_detect_fc": {"name": "sensor/radar/radar_detect_fc", "cycleS": -1, "pendingQueueSize": 1}, "radar_detect_fl": {"name": "sensor/radar/radar_detect_fl", "cycleS": -1, "pendingQueueSize": 1}, "radar_detect_fr": {"name": "sensor/radar/radar_detect_fr", "cycleS": -1, "pendingQueueSize": 1}, "radar_detect_rl": {"name": "sensor/radar/radar_detect_rl", "cycleS": -1, "pendingQueueSize": 1}, "radar_detect_rr": {"name": "sensor/radar/radar_detect_rr", "cycleS": -1, "pendingQueueSize": 1}, "radar_detect": {"name": "sensor/radar/radar_detect", "cycleS": -1, "pendingQueueSize": 1}, "radar_cloud_fc": {"name": "sensor/radar/radar_cloud_fc", "cycleS": -1, "pendingQueueSize": 1}, "radar_cloud_fl": {"name": "sensor/radar/radar_cloud_fl", "cycleS": -1, "pendingQueueSize": 1}, "radar_cloud_fr": {"name": "sensor/radar/radar_cloud_fr", "cycleS": -1, "pendingQueueSize": 1}, "radar_cloud_rl": {"name": "sensor/radar/radar_cloud_rl", "cycleS": -1, "pendingQueueSize": 1}, "radar_cloud_rr": {"name": "sensor/radar/radar_cloud_rr", "cycleS": -1, "pendingQueueSize": 1}}}
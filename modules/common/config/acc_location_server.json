{"nodeName": "acc_location_server", "subChannels": {"driver_corrimu": {"name": "acc/sensors/imu1", "cycleS": 0.01, "pendingQueueSize": 1}, "driver_rawimu": {"name": "channel/rawimu", "cycleS": 0.01, "pendingQueueSize": 5}, "carla_imu": {"name": "sensors/imu", "cycleS": 0.01, "pendingQueueSize": 5}, "driver_chassisimu": {"name": "channel/ChassisImu", "cycleS": 0.01, "pendingQueueSize": 5}, "io_wheelspeed": {"name": "channel/wheelspeed", "cycleS": 0.02, "pendingQueueSize": 5}, "carla_wheelspeed": {"name": "ego_vehicle/wheel_speed", "cycleS": 0.02, "pendingQueueSize": 5}, "io_wheelpulse": {"name": "channel/wheelspeed_old", "cycleS": 0.02, "pendingQueueSize": 5}, "novatel_bestpos": {"name": "channel/bestgnsspos", "cycleS": 0.2, "pendingQueueSize": 0}, "carla_gnss": {"name": "sensors/gnss", "cycleS": 0.2, "pendingQueueSize": 0}, "hmi_out_session_id": {"name": "acc/hmi/session", "cycleS": -1, "pendingQueueSize": 1}, "hmi_notice": {"name": "acc/hmi/notice_upload", "cycleS": 0.2, "pendingQueueSize": 1}, "camera_obstacles": {"name": "perception/camera_obstacles", "cycleS": 0.1, "pendingQueueSize": 1}, "radar_obstacles": {"name": "perception/radar_obstacles", "cycleS": 0.1, "pendingQueueSize": 1}, "fareo_radar_detect": {"name": "abandon/sensor/radar/radar_detect_fc", "cycleS": 0.02, "pendingQueueSize": 1}, "new_fareo_radar_detect": {"name": "sensor/radar/radar_detect", "cycleS": 0.02, "pendingQueueSize": 1}, "decision_to_planning_update": {"name": "acc/decision/update_planning", "cycleS": 1, "pendingQueueSize": 3}, "asensing_ins570d": {"name": "channel/imu_INS570D", "cycleS": 0.01, "pendingQueueSize": 5}, "hmi_msg": {"name": "acc/hmi/out/msg", "cycleS": -1, "pendingQueueSize": 4}, "decision_trigger_planning": {"name": "acc/decision/trigger_planning", "cycleS": -1, "pendingQueueSize": 3}, "camera_lanes": {"name": "perception/camera_lanes", "cycleS": 0.1, "pendingQueueSize": 1}, "arcsoft_perception": {"name": "channel/arcsoft/perception", "cycleS": 0.02, "pendingQueueSize": 3}}, "pubChannels": {"location_vehicleinfo": {"name": "acc/location/vehicleinfo", "cycleS": 0.01, "pendingQueueSize": 1}, "perception_obstacles_carbody_info": {"name": "acc/location/nearest_carbody_info", "cycleS": 0.01, "pendingQueueSize": 1}, "location_replanning": {"name": "acc/location/replanning", "cycleS": -1, "pendingQueueSize": 1}, "location_out_location_err": {"name": "acc/location/out/location_err", "cycleS": 1, "pendingQueueSize": 1}, "location_vehicle_way_points_info": {"name": "acc/location/way_points_info", "cycleS": 0.01, "pendingQueueSize": 1}, "location_obstacles_clines_in_map": {"name": "acc/location/obstacles_clines_in_map", "cycleS": 0.05, "pendingQueueSize": 1}, "fusion_obstacles": {"name": "acc/fusion/obstacles_in_map", "cycleS": 0.1, "pendingQueueSize": 1}, "fusion_lanes": {"name": "acc/fusion/lanes_info_in_map", "cycleS": 0.1, "pendingQueueSize": 1}, "location_reference_line_in_map": {"name": "acc/location/reference_line_in_map", "cycleS": 0.05, "pendingQueueSize": 1}, "location_exception": {"name": "sync/channel/exception", "cycleS": 1, "pendingQueueSize": 1}}}
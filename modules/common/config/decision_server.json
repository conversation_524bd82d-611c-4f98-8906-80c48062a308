{"nodeName": "decision_server", "subChannels": {"channel_exception": {"name": "sync/channel/exception", "cycleS": -1, "pendingQueueSize": 2}, "hmi_msg": {"name": "acc/hmi/out/msg", "cycleS": -1, "pendingQueueSize": 4}, "io_out_shake_hand_ret": {"name": "io/out/acc_shake_hand_ret", "cycleS": -1, "pendingQueueSize": 1}, "hmi_out_session_id": {"name": "acc/hmi/session", "cycleS": -1, "pendingQueueSize": 1}, "io_out_chassis": {"name": "channel/chassis", "cycleS": -1, "pendingQueueSize": 1}}, "pubChannels": {"channel_exception": {"name": "sync/channel/exception", "cycleS": -1, "pendingQueueSize": 1}, "io_in_shake_hand_req": {"name": "io/in/acc_shake_hand_req", "cycleS": -1, "pendingQueueSize": 1}, "decision_apa_state": {"name": "acc/decision/apa_state", "cycleS": 1, "pendingQueueSize": 1}, "decision_to_planning_update": {"name": "acc/decision/update_planning", "cycleS": 1, "pendingQueueSize": 3}, "decision_to_planning_trigger": {"name": "acc/decision/trigger_planning", "cycleS": 1, "pendingQueueSize": 2}}}
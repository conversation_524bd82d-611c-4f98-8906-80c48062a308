{"nodeName": "apa_tracking_server", "subChannels": {"location_vehicleinfo": {"name": "location/vehicleinfo", "cycleS": 0.01, "pendingQueueSize": 1}, "io_out_chassisinfo_lon": {"name": "io/out/chassisinfo_lon", "cycleS": 0.02, "pendingQueueSize": 1}, "track_in_actsign": {"name": "track/in/actsign", "cycleS": -1, "pendingQueueSize": 1}}, "pubChannels": {"planning_path": {"name": "planning/path", "cycleS": -1, "pendingQueueSize": 1}, "planning_pathstate": {"name": "planning/pathstate", "cycleS": -1, "pendingQueueSize": 1}, "tracking_out_state": {"name": "tracking/out/state", "cycleS": -1, "pendingQueueSize": 1}, "channel_exception": {"name": "channel/exception", "cycleS": -1, "pendingQueueSize": 2}, "tracking_out_reverse_disremark_func": {"name": "tracking/out/reverse_disremark_func", "cycleS": -1, "pendingQueueSize": 1}}}
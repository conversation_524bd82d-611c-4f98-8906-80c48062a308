{"nodeName": "acc_control_server", "subChannels": {"acc_chassis": {"name": "channel/chassis", "cycleS": -1, "pendingQueueSize": 1}, "acc_trajectory": {"name": "acc/planning/out/trajectory", "cycleS": 1, "pendingQueueSize": 1}, "acc_localization": {"name": "acc/location/vehicleinfo", "cycleS": 1, "pendingQueueSize": 1}, "hmi_velocity": {"name": "hmi/velocity", "cycleS": -1, "pendingQueueSize": 1}, "hmi_cruise_interval": {"name": "hmi/cruise_interval", "cycleS": -1, "pendingQueueSize": 1}, "decision_out_acc_state": {"name": "acc/decision/acc_state", "cycleS": -1, "pendingQueueSize": 1}, "decision_out_lcc_state": {"name": "acc/decision/lcc_state", "cycleS": -1, "pendingQueueSize": 1}, "decision_to_planning_update": {"name": "acc/decision/update_planning", "cycleS": 1, "pendingQueueSize": 3}, "decision_to_planning_trigger": {"name": "acc/decision/trigger_planning", "cycleS": 1, "pendingQueueSize": 2}}, "pubChannels": {"acc_control": {"name": "io/in/trq_chassisctl", "cycleS": -1, "pendingQueueSize": 1}, "aion_acc_control": {"name": "io/in/trq_chassisctl", "cycleS": -1, "pendingQueueSize": 1}, "control_debug": {"name": "channel/control_debug", "cycleS": -1, "pendingQueueSize": 1}, "exception": {"name": "sync/channel/exception", "cycleS": -1, "pendingQueueSize": 1}}}
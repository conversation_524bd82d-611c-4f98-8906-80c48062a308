{"nodeName": "aeb_server", "subChannels": {"valeo_radar_detect_fc": {"name": "abandon/sensor/radar/radar_detect_fc", "cycleS": -1, "pendingQueueSize": 1}, "new_valeo_radar_detect_fc": {"name": "sensor/radar/radar_detect", "cycleS": -1, "pendingQueueSize": 1}, "io_out_chassis": {"name": "channel/chassis", "cycleS": -1, "pendingQueueSize": 1}, "hmi_aeb": {"name": "aeb/hmi/out/msg", "cycleS": -1, "pendingQueueSize": 3}, "decision_to_planning_trigger": {"name": "acc/decision/trigger_planning", "cycleS": 1, "pendingQueueSize": 2}, "fusion_obstacles": {"name": "acc/fusion/obstacles_in_map", "cycleS": 0.1, "pendingQueueSize": 1}, "location_vehicleinfo": {"name": "acc/location/vehicleinfo", "cycleS": 0.1, "pendingQueueSize": 1}}, "pubChannels": {"aeb_aebcontroller": {"name": "aeb/aebcontroller", "cycleS": -1, "pendingQueueSize": 2}, "channel_exception": {"name": "sync/channel/exception", "cycleS": -1, "pendingQueueSize": 2}}}
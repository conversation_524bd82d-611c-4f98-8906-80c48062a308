{"nodeName": "acc_planning_server", "subChannels": {"location_pose": {"name": "acc/location/vehicleinfo", "cycleS": 0.02, "pendingQueueSize": 1}, "chassis_info": {"name": "channel/chassis", "cycleS": -1, "pendingQueueSize": 1}, "fusion_obstacles": {"name": "acc/fusion/obstacles_in_map", "cycleS": 0.1, "pendingQueueSize": 1}, "fusion_lanes": {"name": "acc/fusion/lanes_info_in_map", "cycleS": 0.1, "pendingQueueSize": 1}, "NOA_fusion_lanes": {"name": "acc/fusion/NOA_lanes_info_in_map", "cycleS": 0.1, "pendingQueueSize": 1}, "decision_trigger_planning": {"name": "acc/decision/trigger_planning", "cycleS": -1, "pendingQueueSize": 3}, "decision_update_planning": {"name": "acc/decision/update_planning", "cycleS": -1, "pendingQueueSize": 3}, "debug_in_planning_status": {"name": "acc/debug/planning_status", "cycleS": -1, "pendingQueueSize": 1}}, "pubChannels": {"acc_planing_out_trajectory": {"name": "acc/planning/out/trajectory", "cycleS": 0.1, "pendingQueueSize": 1}, "debug_out_planing_status": {"name": "acc/debug/planning_status", "cycleS": -1, "pendingQueueSize": 1}, "acc_visual_plan": {"name": "acc/visual/planning_msg", "cycleS": -1, "pendingQueueSize": 1}, "acc_plan_exception": {"name": "sync/channel/exception", "cycleS": -1, "pendingQueueSize": 2}, "acc_lat_lattice": {"name": "acc/planning/out/lat_lattice", "cycleS": 0.1, "pendingQueueSize": 1}}}
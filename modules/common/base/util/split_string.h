#pragma once
#include <string>
#include <string_view>
#include <vector>
namespace base::util {
// port from https://www.bfilipek.com/2018/07/string-view-perf-followup.html
int split(const std::string &str, const std::string &delims, std::vector<std::string> *output);
std::vector<std::string> split(const std::string &str, const std::string &delims = " ");
std::vector<std::string_view> split(std::string_view str, std::string_view delims = " ", bool empty_field=false);

int split(const std::string_view str, const std::string_view delims, bool empty_field, std::vector<std::string_view>* output);
int split(const std::string &str, const std::string &delims, std::vector<std::string_view> *output);
} // namespace base::util

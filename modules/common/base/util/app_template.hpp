#pragma once
#include <boost/program_options.hpp>
#include <boost/optional.hpp>
#include <boost/program_options.hpp>
#include <boost/program_options.hpp>
#include <boost/make_shared.hpp>
#include <fstream>
#include <cstdlib>
#include <functional>
#include <string>
#include <chrono>
#include <exception>
#include <stdexcept>
#include <iostream>
#include <cstdlib>
#include <sys/time.h>

namespace adas {
namespace bpo = boost::program_options;

inline double getTimeS(struct timeval * pt)
{
    double N = 1000.0;
    struct timeval tv;
    if(pt==nullptr)
        gettimeofday(&tv, NULL); // get current time
    else
        tv = *pt;
    double milliseconds = tv.tv_sec * N + tv.tv_usec / N; // calculate milliseconds
    return milliseconds / 1000;
}


class app_template {
public:
    using sstring = std::string;
    using configuration_reader = std::function<void (boost::program_options::variables_map&)>;
    struct config {
        sstring name = "Ada<PERSON>";
        std::chrono::duration<double> default_task_quota = std::chrono::microseconds(500);
        bool auto_handle_sigint_sigterm = true;
        config() {}
    };
    struct reactor_config {
        std::chrono::duration<double> task_quota{0.5e-3}; ///< default time between polls
        bool auto_handle_sigint_sigterm = true;  ///< automatically terminate on SIGINT/SIGTERM
    };
private:
    config _cfg;
    configuration_reader _conf_reader;
    boost::program_options::options_description _opts;
    boost::program_options::options_description _opts_conf_file;
    boost::program_options::positional_options_description _pos_opts;
    boost::optional<boost::program_options::variables_map> _configuration; 

public:
    struct positional_option {
        const char* name;
        const boost::program_options::value_semantic* value_semantic;
        const char* help;
        int max_count;
    };
public:
    static reactor_config reactor_config_from_app_config(config cfg) {
        reactor_config ret;
        ret.auto_handle_sigint_sigterm = cfg.auto_handle_sigint_sigterm;
        ret.task_quota = cfg.default_task_quota;
        return ret;
    }
    explicit app_template(config cfg = config()):
    _cfg(std::move(cfg)),
    _conf_reader(get_default_configuration_reader()),
    _opts(_cfg.name + " options") {
        _opts.add_options()
            ("help,h", "show help message")
            ;
        _opts.add(_opts_conf_file);
    }

    boost::program_options::options_description& get_options_description(){
        return _opts;
    }

    boost::program_options::options_description& get_conf_file_options_description(){
        return _opts_conf_file;
    }
    
    configuration_reader get_default_configuration_reader() {
        return [this] (bpo::variables_map& configuration) {
            //do other
        };
    }

    boost::program_options::options_description_easy_init add_options(){
        return _opts.add_options();
    }

    int run(int ac, char** av, const std::string& conf){
        bpo::variables_map configuration;
        try {
            bpo::store(bpo::command_line_parser(ac, av)
                    .options(_opts)
                    .positional(_pos_opts)
                    .run()
            , configuration);
            _conf_reader(configuration);
        } catch (bpo::error& e) {
            std::cout << "error: "<<e.what()<< "\n\nTry --help.\n";
            return 2;
        }
        if (configuration.count("help")) {
            std::cout << _opts << "\n";
            return 1;
        }
        try {
            bpo::notify(configuration);
            configuration.emplace("argv0", boost::program_options::variable_value(std::string(av[0]), false));
        } catch (const std::exception& e) {
            std::cerr << "could not initialize adas: " << e.what()/*std::current_exception()*/ << std::endl;
            return -1;
        }
        _configuration = {std::move(configuration)};
        //std::cout <<"if value : "<<_configuration.has_value()<<"\nopts : \n"<< _opts << "\n";

        auto &config = *_configuration;
        auto apa_config = config[conf].as<std::string>();
        try{
            bpo::store(bpo::parse_config_file(apa_config.c_str(), get_options_description(), true), config);
        }catch(const std::exception & e){
            std::cerr <<"file "<<apa_config<<" parse failed!"<<" what : "<<e.what()<< std::endl;
            return -2;
        }
        return 0;
    }

    void add_positional_options(std::initializer_list<positional_option> options){
        for (auto&& o : options) {
            _opts.add(boost::make_shared<bpo::option_description>(o.name, o.value_semantic, o.help));
            _pos_opts.add(o.name, o.max_count);
        }
    }
    boost::program_options::variables_map& configuration(){
        return *_configuration;
    }
};
} // namespace adas

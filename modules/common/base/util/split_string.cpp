#include "base/util/split_string.h"
#include <algorithm>
namespace base::util {
std::vector<std::string> split(const std::string &str, const std::string &delims) {
    std::vector<std::string> output;
    split(str, delims, &output);
    return std::move(output);
}
// uses string::find_first_of
int split(const std::string &str, const std::string &delims, std::vector<std::string> *output) {
    // output.reserve(str.length() / 4);
    size_t first = 0;

    while (first < str.size()) {
        const auto second = str.find_first_of(delims, first);

        if (first != second) {
            output->emplace_back(str.data() + first,
                                 str.data() + (second == std::string::npos ? str.size() : second));
        }

        if (second == std::string::npos)
            break;

        first = second + 1;
    }

    return output->size();
}
int split(const std::string_view str, const std::string_view delims, bool empty_field, std::vector<std::string_view>* output) {
    for (auto first = str.data(), second = str.data(), last = first + str.size();
         second != last && first != last;
         first = second + 1) {
        second = std::find_first_of(first, last, std::cbegin(delims), std::cend(delims));
        //printf("%p:%lu:%lu\n", first, last - first, second - first);
        if (first != second || empty_field)
            output->emplace_back(first, second - first);
    }
    return output->size();
}
// based on the JFT's comment:
std::vector<std::string_view> split(std::string_view str, std::string_view delims, bool empty_field) {
    std::vector<std::string_view> output;
    // output.reserve(str.size() / 2);
    split(std::move(str), std::move(delims), empty_field, &output);
    return output;
}

} // namespace base::util

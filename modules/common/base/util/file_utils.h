#pragma once
#include <cstdint>
#include <functional>
#include <string>
namespace base::util {
typedef std::function<bool(const char *const, size_t)> ReaderProc;
std::string file_contents(const std::string &filename);
uint64_t fast_line_reader(const std::string &filename,
                          ReaderProc fn,
                          bool skip_empty_line,
                          bool skip_comment);
uint64_t fast_line_reader(const char *const filename,
                          ReaderProc fn,
                          bool skip_empty_line,
                          bool skip_comment);
uint64_t simple_line_reader(const char *const filename,
                            ReaderProc fn,
                            bool skip_empty_line,
                            bool skip_comment);
} // namespace base::util

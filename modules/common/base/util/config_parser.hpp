#pragma once
#include <string>
#include <vector>
#include "json/json.hpp"
#include "cyber/cyber.h"



struct Channel
{
    std::string name;
    double cycleS;
    uint32_t pendingQueueSize;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(Channel, name, cycleS, pendingQueueSize);
};

struct Node_info
{
    std::string nodeName;
    nlohmann::json subChannels;
    nlohmann::json pubChannels;

};

class NodeCfg
{
public:
    NodeCfg(){}
    NodeCfg(const std::string& fileName/* args */)
    {
        AINFO << __FUNCTION__ << " : Config file path is: " << fileName;
        _fileName = fileName;
        std::ifstream ifs(fileName);
        if (!ifs.is_open()) {
            AERROR << "Can not find config file: " << fileName;
            return;
        }
        //nlohmann::json jcfg = nlohmann::json::parse(ifs);
        nlohmann::json jcfg;
        ifs >> jcfg;
        ifs.close();
        AINFO << jcfg.dump();

        _info.nodeName = jcfg["nodeName"];
        _info.subChannels = jcfg["subChannels"];
        _info.pubChannels = jcfg["pubChannels"];

        //AERROR << _info.nodeName;
        //AERROR << jsubChannels.dump();
        //AERROR << jpubChannels.dump();

        //for (auto jsChannel: jsubChannels) {
            //AERROR << jsChannel.dump();
            //Channel channel = jsChannel.get<Channel>();
            ////_info.subChannels.push_back(channel);
            //_info.subChannels.insert(pair)
        //}

        //for (auto jpChannel: jpubChannels) {
            //AERROR << jpChannel.dump();
            //Channel channel = jpChannel.get<Channel>();
            //_info.pubChannels.push_back(channel);
        //}

    }

    void init(const std::string& fileName)
    {
        AINFO << __FUNCTION__ << " : Config file path is: " << fileName;
        _fileName = fileName;
        std::ifstream ifs(fileName);
        if (!ifs.is_open()) {
            AERROR << "Can not find config file: " << fileName;
            return;
        }
        //nlohmann::json jcfg = nlohmann::json::parse(ifs);
        nlohmann::json jcfg;
        ifs >> jcfg;
        ifs.close();
        AINFO << jcfg.dump();

        _info.nodeName = jcfg["nodeName"];
        _info.subChannels = jcfg["subChannels"];
        _info.pubChannels = jcfg["pubChannels"];
    }
    //Node_info& getCfg() { return _info;}

    bool isValid() {
        if (_info.nodeName != "") {
            return true;
        }
        else {
            AERROR << "Config file is invalid: " << _fileName;
            return false;
        }
    }

    std::string& getName() {
        return _info.nodeName;
    }

    Channel getSubChannel(const std::string& key) {
        Channel channel;
        channel.name = "invalid/channel";
        try
        {
            channel = _info.subChannels[key].get<Channel>();
        }
        catch(const std::exception& e)
        {
            AERROR << __FUNCTION__ <<" Failed, key = " << key << " \n" << e.what() << '\n';
            assert(false);
        }
        return channel;
    }

    Channel getPubChannel(const std::string& key) {
        Channel channel;
        channel.name = "invalid/channel";
        try
        {
            /* code */
            channel = _info.pubChannels[key].get<Channel>();
        }
        catch(const std::exception& e)
        {
            AERROR << __FUNCTION__ <<" Failed, key = " << key << " \n" << e.what() << '\n';
            assert(false);
        }
        return channel;
    }
#ifdef BUILD_TEST
    Channel getPubChannel(const std::string& key, bool& ret) {
        Channel channel;
        channel.name = "invalid/channel";
        try
        {
            /* code */
            channel = _info.pubChannels[key].get<Channel>();
            ret = true;
        }
        catch(const std::exception& e)
        {
            // AERROR << __FUNCTION__ <<" Failed, key = " << key << " \n" << e.what() << '\n';
            ret = false;
        }

        return channel;
    }
#endif
private:
    /* data */
    Node_info _info;
    std::string _fileName;

};




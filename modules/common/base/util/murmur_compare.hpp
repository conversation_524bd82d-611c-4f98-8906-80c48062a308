/*************************************************************************
 > File Name: base/util/murmur_compare.hpp
 > Author: yafeng.zhao 
 > Mail: <EMAIL>
 > Created Time: 2022年11月17日 星期四 11时16分07秒
 ************************************************************************/

#pragma once

#include <climits>
#include <cmath>
#include <type_traits>
namespace base{
namespace util{

#if 0
/*
(f,d)
(f,f)
(d,d)
(d,f)
*/
template<typename T0,typename T1,
    typename = typename std::enable_if<std::is_floating_point<T0>::value>::type,
    typename = typename std::enable_if<std::is_floating_point<T1>::value>::type>
bool equal(T0 f1, T1 f2){
    if(std::fabs(f1 - f2) < std::numeric_limits<double>::epsilon()){
        return true;
    }
    return false;
}
#endif

template<typename T1,
    typename = typename std::enable_if<std::is_floating_point<T1>::value>::type>
bool equal(T1 f1, T1 f2){
    if(std::fabs(f1 - f2) < std::numeric_limits<double>::epsilon()){
        return true;
    }
    return false;
}
} // namespace util
} // namespace base

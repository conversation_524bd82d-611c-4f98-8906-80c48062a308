/*************************************************************************
 > File Name: modules/common/inc/heart_beat.hpp
 > Author: yafeng.zhao
 > Mail: <EMAIL>
 > Created Time: 2022年07月26日 星期二 14时53分51秒
 ************************************************************************/

#pragma once
#include "cyber/cyber.h"

#include "thirdparty/recommend_protocols/common/proto/header.pb.h"

#define RegisterMonitor(node_ptr, channel_name)                                \
  auto monitor_server = node_ptr->CreateService<rainbowdash::common::Verify,   \
                                                rainbowdash::common::Verify>(  \
      channel_name,                                                            \
      [](const std::shared_ptr<rainbowdash::common::Verify> &request,          \
         std::shared_ptr<rainbowdash::common::Verify> &response) {             \
        response->set_val(request->val() + 1);                                 \
      });

#include "base/util/file_utils.h"
#include <cstring>
#include <errno.h>
#include <fcntl.h>
#include <iostream>
#include <fstream>
#include <memory>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
namespace base:util {
uint64_t fast_line_reader(const char *const filename,
                          ReaderProc fn,
                          bool skip_empty_line,
                          bool skip_comment) {
    int fd = open(filename, O_RDONLY); // open file
    if (fd == -1) {
        std::cerr << "Could not open \"" << filename << "\" for reading (" << strerror(errno)
                  << ")." << std::endl;
        return -1;
    }
    struct stat fs;
    if (fstat(fd, &fs) == -1) {
        std::cerr << "Could not stat \"" << filename << "\" for reading (" << strerror(errno)
                  << ")." << std::endl;
        close(fd);
        return -1;
    }

    // announce the desire to sequentialy read this file
    posix_fadvise(fd, 0, 0, 1);
    // silent error handling - weak error

    char *buf =
        static_cast<char *>(mmap(0, static_cast<size_t>(fs.st_size), PROT_READ, MAP_SHARED, fd, 0));
    if (buf == MAP_FAILED) {
        std::cerr << "Could not memory map file \"" << filename << "\" (" << strerror(errno) << ")."
                  << std::endl;
        close(fd);
        return -1;
    }

    char *buff_end = buf + fs.st_size;
    char *begin = buf, *end = NULL;

    // search for newline in the remainder in the file
    while ((end = static_cast<char *>(
                memchr(begin, '\n', static_cast<size_t>(buff_end - begin)))) != NULL) {
        auto nlen = end - begin;
        if (skip_empty_line && nlen == 0) {
            continue;
        }
        if (!skip_comment || begin[0] != '#') {
            fn(begin, nlen);
        }

        if (end != buff_end)
            begin = end + 1;
        else
            break;
    }

// enable if you are working with malformed text files, proper text file needs
// to end with a newline
#ifdef MALFORMED_TEXFILE
    fn(begin, buff_end);
#endif

    munmap(buf, static_cast<size_t>(fs.st_size));
    // silent error handling - weak error

    close(fd);
    return 0;
}
uint64_t simple_line_reader(const char *const filename,
                            ReaderProc fn,
                            bool skip_empty_line,
                            bool skip_comment) {
    std::unique_ptr<FILE, decltype(&fclose)> fp(fopen(filename, "r"), &fclose);
    if (fp.get() == nullptr) {
        return -1;
    }
    char *line = NULL;
    size_t len = 0;
    ssize_t read;
    while ((read = getline(&line, &len, fp.get())) != -1) {
        if (read == 0 || (read == 1 && *line == '\n')) {
            if (!skip_empty_line) {
                if (!fn(line, read)) {
                    break;
                }
            }
        }
        else {
            if (skip_comment && line[0] == '#') {
                continue;
            }
            if (!fn(line, read)) {
                break;
            }
        }
    }
    free(line);
    return 0;
}
std::string file_contents(const std::string &filename) {
	std::ifstream ifs(filename);
    if (!ifs)
    {
        throw std::runtime_error("Failed open file ");
    }
    return std::string((std::istreambuf_iterator<char>(ifs)), (std::istreambuf_iterator<char>()));
}

} // namespace base::util

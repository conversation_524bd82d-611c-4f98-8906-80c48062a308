#pragma once
#include <sys/time.h>
#include "cyber/cyber.h"
#include <google/protobuf/util/json_util.h>



enum class eState {
    UNDEFINE = 0,
    SUSPENDING = 1,
    READY = 2,
    RUNNING = 3,
    EXCEPTION = 4,
    TERMINATED = 5,
};
#ifndef __QNX__
template<typename T>
auto transEnum(const T val)
{
    auto a = static_cast<typename std::underlying_type<T>::type>(val);
    return a;
}
#endif

// decltype(AINFO)& operator << (decltype(AINFO) & out, eState & state)
// {
//     return out << transEnum(state);
// }

// decltype(AINFO)& operator << (decltype(AINFO) & out, const eState & state)
// {
//     return out << transEnum(state);
// }

#define ADD_LOG AINFO
#define TASK_END " TASK_END_TAG "

class ModuleBase {
public:
    eState setState(const eState & newState)
    {
        std::lock_guard<std::mutex> lock(_stateMutex);
        AINFO << __FUNCTION__ << " oldState=" << (int)_state << " newState=" << (int)newState;
        //AINFO << __FUNCTION__ << "oldState=" << _state << " newState=" << newState;
        eState lastState = _state;
        _state = newState;

        return lastState;
    }

    eState getState()
    {
        std::lock_guard<std::mutex> lock(_stateMutex);
        return _state;
    }

    static bool save_map_json(const std::string& file_name, const std::string& json_string){
      std::ofstream os;
      os.open(file_name, std::ios::out);
      if (!os.is_open()){
        std::cout << "error: can not find or create the file which named " << file_name << std::endl;
        AINFO <<"error: can not find or create the file which named " << file_name << ".";
        return false;
      }

      os << json_string;
      os.close();
      return true;

    }

    double getTimeS(struct timeval * pt)
    {
        double N = 1000.0;
        struct timeval te;
        if(pt==nullptr)
            gettimeofday(&te, NULL); // get current time
        else
            te = *pt;
        double milliseconds = te.tv_sec*N+ te.tv_usec/N; // calculate milliseconds
        return milliseconds/1000;
    }

    void setDependChannel(uint32_t channelId)
    {
        _dependChannelBitMap |= 1 << channelId;
    }

    void clearDependChannel(uint32_t channelId)
    {
        _dependChannelBitMap &= ~((1u) << channelId);
    }

    void MonitorChannel(double delta_time_s, double channel_cycle_s)
    {
        #if 0
        if (time_stamp_last_.size() < dependChannelNum+1) {
            time_stamp_last_.emplace_back(0);
        }

        if (time_stamp_last_[dependChannelNum] == 0) {
          if (cur_time_stamp != 0)
            setDependChannel(dependChannelNum);
        } else if (cur_time_stamp - time_stamp_last_[dependChannelNum] > 3*channel_cycle_s) {
          clearDependChannel(dependChannelNum);
        } else {
          setDependChannel(dependChannelNum);
        }

        time_stamp_last_[dependChannelNum] = cur_time_stamp;
        dependChannelNum++;
        #endif
    }

    bool isAllDependChannelsOk(uint32_t dependChannelNum)
    {
        static uint32_t lastBitMap=0xffffffff;
        uint32_t mask = (1 << dependChannelNum) - 1;
        bool allChannelsOk = mask == (mask & _dependChannelBitMap);
        if (_dependChannelBitMap != lastBitMap) {
            //AINFO << __FUNCTION__ << "mask=0x" << std::hex << mask << " _dependChannelBitMap=0x" <<std::hex << _dependChannelBitMap;
            lastBitMap = _dependChannelBitMap;
        }
        return allChannelsOk;
    }

    virtual bool dataMonitor(){
        return true;
    }


protected:
    uint32_t dependChannelNum = 0;

private:
    eState _state = eState::TERMINATED;
    std::mutex _stateMutex;
    std::atomic<uint32_t> _dependChannelBitMap{0};

};

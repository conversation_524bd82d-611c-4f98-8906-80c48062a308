#pragma once


/* apa states defination */
#define DECISION_STATE_PENDING "Pending"
#define DECISION_STATE_READY "Ready"
#define DECISION_STATE_PLANNING "Planning"
#define DECISION_STATE_PARKING "Parking"
#define DECISION_STATE_PAUSE "Pause"
#define DECISION_STATE_FINISH "Finish"
#define DECISION_STATE_FAILED "Failed"

/* tracking states defination */
#define TRACKING_STATE_PENDING "Pending"
#define TRACKING_STATE_READY "Ready"
#define TRACKING_STATE_PLANNING "Planning"
#define TRACKING_STATE_RUNNING "Running"
#define TRACKING_STATE_PAUSE "Pause"
#define TRACKING_STATE_FINISH "Finish"
#define TRACKING_STATE_FAILED "Failed"



/* events defination */
#define EVENT_NONE "none" // none event
#define EVENT_INNER_PARKING_IN_SCENARIO "parking_in_scenario"  // HMI use it
#define EVENT_INNER_PARKING_OUT_SCENARIO "parking_out_scenario"  // HMI use it

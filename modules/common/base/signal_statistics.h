/*************************************************************************
 > File Name: signal_Statistics.h
 > Author: binghao.li 
 > Mail: <EMAIL>
 > Created Time: 2023年08月03日 
 ************************************************************************/

#pragma once
#ifndef _SIGNAL_Statistics_H
#define _SIGNAL_Statistics_H
#include "cyber/cyber.h"
#include "singleton.hpp"

using apollo::cyber::Time;

struct periodic_signal {
    periodic_signal() {
       time_last_ = apollo::cyber::Time::MonoTime();
       time_last_calc_ = apollo::cyber::Time(time_last_.ToNanosecond());
       msg_time_ = apollo::cyber::Time(time_last_.ToNanosecond() + 1);
    }
    uint64_t first_time, last_time, interval, cnt;
    uint64_t limit_time_delay; //最大延迟
    uint64_t limit_publish_hz; //最小频率
    uint64_t frame_counter_;
    uint64_t check_frequency_;
    double ratio;
    apollo::cyber::Time msg_time_;
    apollo::cyber::Time time_last_;
    apollo::cyber::Time time_last_calc_;
};

class SignalStatistics {
public:
  SignalStatistics();
  virtual ~SignalStatistics();
  void register_statistic(const std::string& name, uint64_t max_delay_time, uint64_t min_pub_hz, uint64_t check_frequency = 200);
  bool message_count(const std::string &channel);
  periodic_signal* get_perio(const std::string &channel){
    return channel_metrics_map[channel];
  }
private:
  std::unordered_map<std::string, periodic_signal*> channel_metrics_map;
  int count = 0;
};


#endif

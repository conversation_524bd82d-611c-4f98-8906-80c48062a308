/*************************************************************************
 > File Name: exception_type.h
 > Author: yafeng.zhao
 > Mail: <EMAIL>
 > Created Time: 2023年08月21日 星期一 17时37分39秒
 ************************************************************************/

#pragma once
#ifndef _EXCEPTION_TYPE_H
#define _EXCEPTION_TYPE_H

namespace exception {

#define MODULE_OFFSET 24
#define MODULE_MASK 0xff
enum class ExceptionModule : int {
  // 31-24 bit
  NONE = 0x00,
  SYSTEM = 0x01,
  EXCEPTION = 0x02,
  LOCATION = 0x03,
  DECISION = 0x04,
  PLANNING = 0x05,
  CONTROL = 0x06,
  WIRE_CONTROL = 0x07,
  MCU = 0x08,
  TRACKING = 0x09,
  INTERACTIVE = 0x0A,
  HWP_PLAN = 0x0B,
  AEB = 0x0C,
  HWP_CONTROL = 0x0D
};

#define LEVEL_OFFSET 16
#define LEVEL_MASK 0xff
enum class ExceptionLevel : int {
  // 24-16 bit
  LEVEL0 = 0x00,      // High
  LEVEL1 = 0x01,      // Middle
  LEVEL2 = 0x02,      // Low
  LEVEL_NULL = 0xff,  // null
};

#define MERGE(module, level, exception_code)   \
  (((unsigned int)(module) << MODULE_OFFSET) | \
   ((unsigned int)(level) << LEVEL_OFFSET) | (exception_code))

#define SPLIT_MODULE(exception_code) \
  (exception::ExceptionModule)(      \
      ((exception_code) & (MODULE_MASK << MODULE_OFFSET)) >> MODULE_OFFSET)

#define SPLIT_LEVEL(exception_code) \
  (exception::ExceptionLevel)(      \
      ((exception_code) & (LEVEL_MASK << LEVEL_OFFSET)) >> LEVEL_OFFSET)

#define GET_EXCEPTION_NAME(NAME) (#NAME)
#define GET_NAME(name) (#name)

inline std::string GET_MODULE_NAME(uint32_t exception_code) {
  ExceptionModule em = (ExceptionModule)(SPLIT_MODULE(exception_code));
  if (em == ExceptionModule::NONE) return GET_NAME(ExceptionModule::NONE);
  if (em == ExceptionModule::SYSTEM) return GET_NAME(ExceptionModule::SYSTEM);
  if (em == ExceptionModule::EXCEPTION)
    return GET_NAME(ExceptionModule::EXCEPTION);
  if (em == ExceptionModule::LOCATION)
    return GET_NAME(ExceptionModule::LOCATION);
  if (em == ExceptionModule::DECISION)
    return GET_NAME(ExceptionModule::DECISION);
  if (em == ExceptionModule::PLANNING)
    return GET_NAME(ExceptionModule::PLANNING);
  if (em == ExceptionModule::CONTROL) return GET_NAME(ExceptionModule::CONTROL);
  if (em == ExceptionModule::WIRE_CONTROL)
    return GET_NAME(ExceptionModule::WIRE_CONTROL);
  if (em == ExceptionModule::MCU) return GET_NAME(ExceptionModule::MCU);
  if (em == ExceptionModule::TRACKING)
    return GET_NAME(ExceptionModule::TRACKING);
  if (em == ExceptionModule::INTERACTIVE)
    return GET_NAME(ExceptionModule::INTERACTIVE);
  if (em == ExceptionModule::HWP_PLAN)
    return GET_NAME(ExceptionModule::HWP_PLAN);
  if (em == ExceptionModule::AEB) return GET_NAME(ExceptionModule::AEB);
  if (em == ExceptionModule::HWP_CONTROL) 
    return GET_NAME(ExceptionModule::HWP_CONTROL);

  return GET_NAME(NONE);
}

#define CTLEXCEPT_TYPE                                      \
  X_MACRO(CTL_SYSERROR_Init_CREATEEXPLIBRARY,               \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0000)) \
  X_MACRO(CTL_SYSERROR_Init_CTEATEBEHAVIOR,                 \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0001)) \
  X_MACRO(CTL_SYSERROR_Init_WRITECONF,                      \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0002)) \
  X_MACRO(CTL_SYSERROR_LON_TRAJECTORYDISVEL,                \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0100)) \
  X_MACRO(CTL_SYSERROR_LON_TRAJECTORYPLANRAMP,              \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0101)) \
  X_MACRO(CTL_SYSERROR_LON_TRAJECTORYPLANSPD,               \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0102)) \
  X_MACRO(CTL_SYSERROR_LON_VEHICLELONCTL,                   \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0103)) \
  X_MACRO(CTL_SYSERROR_LON_UPDATEVMAX,                      \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0104)) \
  X_MACRO(CTL_SYSERROR_LAT_WHEE_COMPUTE,                    \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0200)) \
  X_MACRO(CTL_SYSERROR_LAT_STEERCOMPUTE,                    \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0201)) \
  X_MACRO(CTL_SYSERROR_OHTER_DATAPROCESS,                   \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0300)) \
  X_MACRO(CTL_SYSERROR_OHTER_NOEVENT,                       \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0x0301)) \
  X_MACRO(CTL_SYSERROR_MAX_MAX,                             \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL0, 0xffff)) \
  X_MACRO(CTL_PARKFAILED_WARN_COLLISION_LIMITER,            \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL1, 0x0000)) \
  X_MACRO(CTL_PARKFAILED_WARN_SPEEDBUMPFAILED,              \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL1, 0x0001)) \
  X_MACRO(CTL_PARKFAILED_WARN_COLLISIONUNDRIVEABLE,         \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL1, 0x0002)) \
  X_MACRO(CTL_PARKFAILED_WARN_EXTERNALROUTEUNREASONABLE,    \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL1, 0x0003)) \
  X_MACRO(CTL_PARKFAILED_UNEXCEPTSTANDSTILL,                \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL1, 0x0004)) \
  X_MACRO(CTL_PARKFAILED_SLOTCENCREATFAILED,                \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL1, 0x0005)) \
  X_MACRO(CTL_PARKFAILED_WARN_EXTERNALDRIVERTAKEOVER,       \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL2, 0x0000)) \
  X_MACRO(CTL_PARKFAILED_WARN_EXTERNALPGEARSTOP,            \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL2, 0x0001)) \
  X_MACRO(CTL_PARKFAILED_EGOOVERSLOT,                       \
          MERGE(exception::ExceptionModule::CONTROL,        \
                exception::ExceptionLevel::LEVEL2, 0x0002))

#define TRACKEXCEPT_TYPE                                           \
  X_MACRO(TRACK_SYSERROR_Init_CREATEEXPKIBRARY,                    \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0x0000))        \
  X_MACRO(TRACK_SYSERROR_Init_CREATEBEHAVIOR,                      \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0x0001))        \
  X_MACRO(TRACK_SYSERROR_Init_WRITECONF,                           \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0x0002))        \
  X_MACRO(TRACK_SYSERROR_OTHER_DATAPROCESS,                        \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0x0300))        \
  X_MACRO(TRACK_SYSERROR_OTHER_NOEVENT,                            \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0x0301))        \
  X_MACRO(TRACK_SYSERROR_MAX_MAX,                                  \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0xffff))        \
  X_MACRO(TRACK_SYSERROR_OTHER_EXTERNALSYSERROR,                   \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL0, 0x0302))        \
  X_MACRO(TRACK_ROUTEFAILED_REMARKFAILED_EXTERNALDISABLETRACKING,  \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL1, 0x0000))        \
  X_MACRO(TRACK_ROUTEFAILED_REMARKFAILED_INTERNALBACKWARDDISLARGE, \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL1, 0x0001))        \
  X_MACRO(TRACK_ROUTEFAILED_REMARKFAILED_INTERNALDELTADISLARGE,    \
          MERGE(exception::ExceptionModule::TRACKING,              \
                exception::ExceptionLevel::LEVEL1, 0x0002))

enum class ExceptionType {
  // 15-0 bit
  // system
  ERR_SYS_IMU_LOSS =
      MERGE(ExceptionModule::SYSTEM, ExceptionLevel::LEVEL0, 0x0001),
  ERR_SYS_WHEELSPEED_LOSS =
      MERGE(ExceptionModule::SYSTEM, ExceptionLevel::LEVEL0, 0x0002),
  ERR_SYS_TASK_TIMEOUT =
      MERGE(ExceptionModule::SYSTEM, ExceptionLevel::LEVEL1, 0x0002),
  // control
  CTLEXCEPT_SYS_ERROR_Init_Error_REGISTER_DATA =
      MERGE(ExceptionModule::CONTROL, ExceptionLevel::LEVEL0, 0x0000),
  // io
  MCU_HEARTBEAT_EXCEPT =
      MERGE(ExceptionModule::WIRE_CONTROL, ExceptionLevel::LEVEL0, 0x0001),
  RECEIVE_STOP_EVENT =
      MERGE(ExceptionModule::WIRE_CONTROL, ExceptionLevel::LEVEL0, 0x0002),

  // planning
  PLAN_ESCAPE_OBS_FAILD =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0001),  // done
  PLAN_GOAL_STALL_OCCUPIED =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0002),  // done
  PLAN_TIMEOUT =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0003),  // done
  PLAN_GOAL_STALL_NARROW =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0004),  // done
  PLAN_OBS_INVADE_STALL_LEFT =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0005),  // done
  PLAN_OBS_INVADE_STALL_RIGHT =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0006),  // done
  PLAN_OBS_INVADE_STALL_FRONT =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0007),  // done
  PARKING_OUT_DRIVER_CAN_DRIVE_OUT_HIMSELF =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0008),
  PARKING_OUT_OUTTER_SPACE_IS_NARRAW =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x0009),
  PARKING_OUT_AROUNDING_SPACE_IS_NARRAW =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x000A),
  PLAN_PATH_SMOOTH_FAILED =
      MERGE(ExceptionModule::PLANNING, ExceptionLevel::LEVEL1, 0x000B),  // done

  // avp_slam
  LOC_WHEEL_FAIL =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x0001),
  LOC_IMU_FAIL =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x0002),
  LOC_CONFIG_PARA_FAILED =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x0003),
  LOC_IMU_VALUE_ERROR =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x0004),
  LOC_NO_CHOSEN_PARK =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x0005),
  LOC_GPS_VALUE_ERROR =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x0006),
  LOC_PERCP_LAGGING =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0001),
  LOC_PERCP_TF_FAILED =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0002),
  LOC_PERCP_POS_FAR =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0003),
  LOC_TIME_UNSYNC_ERROR =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0004),
  LOC_IMU_LAGGING =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0005),
  LOC_WHEEL_LAGGING =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0006),
  LOC_IMU_LAGGING_RECOVER =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0007),
  LOC_WHEEL_LAGGING_RECOVER =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0008),
  LOC_WHEEL_SLIPPAGE =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL1, 0x0009),
  LOC_LANE_CENTER_LINES_DISAPPEAR =
      MERGE(ExceptionModule::LOCATION, ExceptionLevel::LEVEL0, 0x000A),
// perception
// control & track
#define X_MACRO(a, b) a = b,
  CTLEXCEPT_TYPE TRACKEXCEPT_TYPE
#undef X_MACRO

      // mcu
      SOC_EXCEPT_EPS =
          MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0001),
  SIG_EXCEPT_EPS = MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0003),
  EPS_EXCEPT_EPS = MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0006),
  SOC_EXCEPT_IBC = MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0011),
  SIG_EXCEPT_IBC = MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0012),
  IBC_EXCEPT_IBC = MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0017),
  SOC_EXCEPT_GEAR_IBC =
      MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0021),
  SIG_EXCEPT_GEAR_IBC =
      MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0023),
  IBC_EXCEPT_GEAR_IBC =
      MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0027),
  IBC_TIME_OUT_GEAR_IBC =
      MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0028),
  TIME_OUT_GEAR_IBC =
      MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0029),
  USS_AVOID = MERGE(ExceptionModule::MCU, ExceptionLevel::LEVEL0, 0x0003A),

  // interactive
  INTER_SELF_CHECK =
      MERGE(ExceptionModule::INTERACTIVE, ExceptionLevel::LEVEL1, 0x0001),

  // decision
  DEC_EXCEPT_SAFE_STOP_TIMEOUT =
      MERGE(ExceptionModule::DECISION, ExceptionLevel::LEVEL0, 0x0005),
  DEC_EXCEPT_PAUSE_CONTINUE_TIMEOUT =
      MERGE(ExceptionModule::DECISION, ExceptionLevel::LEVEL0, 0x0006),

  // hwp plan
  HWP_PLAN_OBSTACLE_SUSPEND =
      MERGE(ExceptionModule::HWP_PLAN, ExceptionLevel::LEVEL2, 0x02ff),
  HWP_PLAN_CEN_LINE_FAILED =
      MERGE(ExceptionModule::HWP_PLAN, ExceptionLevel::LEVEL0, 0x0100),
  HWP_LKA_LATPLAN_FAILED =
      MERGE(ExceptionModule::HWP_PLAN, ExceptionLevel::LEVEL0, 0x0101),
  HWP_PLAN_LANE_BOUND_FAILED =
      MERGE(ExceptionModule::HWP_PLAN, ExceptionLevel::LEVEL2, 0x0200),
  HWP_PLAN_NEIBOR_LINE_FAILED =
      MERGE(ExceptionModule::HWP_PLAN, ExceptionLevel::LEVEL2, 0x0300),
  HWP_PLAN_SUSPEND =
      MERGE(ExceptionModule::HWP_PLAN, ExceptionLevel::LEVEL2, 0xffff),
  // AEB
  AEB_LATENT_WARNING =
      MERGE(ExceptionModule::AEB, ExceptionLevel::LEVEL1, 0x0001),
  AEB_PRE_WARNING = MERGE(ExceptionModule::AEB, ExceptionLevel::LEVEL1, 0x0002),
  AEB_ACUTE_WARNING_LEVEL2 =
      MERGE(ExceptionModule::AEB, ExceptionLevel::LEVEL1, 0x0003),
  AEB_ACUTE_WARNING_LEVEL3 =
      MERGE(ExceptionModule::AEB, ExceptionLevel::LEVEL1, 0x0004),
  AEB_ACUTE_WARNING_LEVEL4 =
      MERGE(ExceptionModule::AEB, ExceptionLevel::LEVEL1, 0x0005),
    // hwp control
    HWP_CONTROL_CHASSIS_LOSS =
        MERGE(ExceptionModule::HWP_CONTROL, ExceptionLevel::LEVEL0, 0x0001),
    HWP_CONTROL_LOCATION_LOSS =
        MERGE(ExceptionModule::HWP_CONTROL, ExceptionLevel::LEVEL0, 0x0002),
    HWP_CONTROL_PLAN_LOSS =
        MERGE(ExceptionModule::HWP_CONTROL, ExceptionLevel::LEVEL0, 0x0003)
};

#endif
}

#include <iostream>
#include <fstream>
#include <string>
#include <memory>
#include <vector>
#include <regex>
#include <sstream>
#include <glob.h>
#include <cstring>
#include <sys/wait.h>

#include "modules/common/base/base64.h"
#include "google/protobuf/dynamic_message.h"
#include "google/protobuf/text_format.h"
#include "google/protobuf/util/json_util.h"

// 获取路径中目录部分
std::string get_directory(const std::string& path) {
    size_t pos = path.find_last_of("/\\");
    return (pos == std::string::npos) ? "." : path.substr(0, pos);
}

// 获取文件名
std::string get_filename(const std::string& path) {
    size_t pos = path.find_last_of("/\\");
    return (pos == std::string::npos) ? path : path.substr(pos + 1);
}

// 匹配通配符路径
std::vector<std::string> expand_glob(const std::string& pattern) {
    glob_t glob_result;
    std::vector<std::string> files;
    memset(&glob_result, 0, sizeof(glob_result));

    if (glob(pattern.c_str(), GLOB_TILDE, nullptr, &glob_result) == 0) {
        for (size_t i = 0; i < glob_result.gl_pathc; ++i) {
            files.emplace_back(glob_result.gl_pathv[i]);
        }
    } else {
        std::cerr << "Glob failed for pattern: " << pattern << std::endl;
    }

    globfree(&glob_result);
    return files;
}

// 自动判断并解压缩 gz/zip 文件，返回解压后的文件路径
bool decompress_if_needed(const std::string& filepath, std::string& out_decompressed_path) {
    if (filepath.size() >= 3 && filepath.substr(filepath.size() - 3) == ".gz") {
        out_decompressed_path = filepath.substr(0, filepath.size() - 3);
        std::string command = "gzip -dc \"" + filepath + "\" > \"" + out_decompressed_path + "\"";
        int ret = system(command.c_str());
        return WIFEXITED(ret) && WEXITSTATUS(ret) == 0;
    } else if (filepath.size() >= 4 && filepath.substr(filepath.size() - 4) == ".zip") {
        out_decompressed_path = filepath + ".extracted";
        std::string command = "unzip -p \"" + filepath + "\" > \"" + out_decompressed_path + "\"";
        int ret = system(command.c_str());
        return WIFEXITED(ret) && WEXITSTATUS(ret) == 0;
    } else {
        out_decompressed_path = filepath;
        return true;
    }
}

// 解码并写入输出流
bool DecodeProtoFromEncodedData(const std::string& head, const std::string& encoded_data,
                                const std::string& type_name, std::ofstream& outfile) {
    const auto* descriptor =
        google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(type_name);
    if (!descriptor) {
        std::cerr << "Failed to get descriptor for type: " << type_name << std::endl;
        return false;
    }

    google::protobuf::DynamicMessageFactory factory;
    std::unique_ptr<google::protobuf::Message> message(factory.GetPrototype(descriptor)->New());

    std::vector<char> decoded_vector = base64_decode(encoded_data);
    std::string decoded_data(decoded_vector.begin(), decoded_vector.end());

    if (decoded_data.empty()) {
        std::cerr << "Base64 decoding failed" << std::endl;
        return false;
    }

    if (!message->ParseFromString(decoded_data)) {
        std::cerr << "Failed to parse the protobuf message, head: " << head << std::endl;
        return false;
    }

    std::string json_output;
    google::protobuf::util::JsonPrintOptions options;
    options.add_whitespace = false;
    options.preserve_proto_field_names = false;
    options.always_print_primitive_fields = false;

    auto status = google::protobuf::util::MessageToJsonString(*message, &json_output, options);
    if (!status.ok()) {
        std::cerr << "转换失败: " << status.ToString() << std::endl;
        return false;
    }

    outfile << head << " " << json_output << "\n";
    return true;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <logfile or wildcard>" << std::endl;
        return -1;
    }

    std::vector<std::string> input_files;
    for (int i = 1; i < argc; ++i) {
        std::vector<std::string> matched = expand_glob(argv[i]);
        input_files.insert(input_files.end(), matched.begin(), matched.end());
    }

    if (input_files.empty()) {
        std::cerr << "No files matched the pattern." << std::endl;
        return -1;
    }

    for (const auto& input_file : input_files) {
        std::string actual_file;
        if (!decompress_if_needed(input_file, actual_file)) {
            std::cerr << "跳过无法解压的文件: " << input_file << std::endl;
            continue;
        }

        std::ifstream logfile(actual_file);
        if (!logfile.is_open()) {
            std::cerr << "Failed to open log file: " << actual_file << std::endl;
            continue;
        }

        std::string output_file = get_directory(input_file) + "/decode_" + get_filename(actual_file);
        std::ofstream outfile(output_file, std::ios::trunc);
        if (!outfile.is_open()) {
            std::cerr << "Failed to create output file: " << output_file << std::endl;
            continue;
        }

        std::string line, type_name, head;
        int analyse_num = 0;
        int origin_num = 0;
        std::regex type_data_regex(R"(type=([^\s]+)\s+data=([A-Za-z0-9+/=]+))");

        while (std::getline(logfile, line)) {
            std::smatch match;
            if (std::regex_search(line, match, type_data_regex)) {
                type_name = match[1];
                std::string encoded_data = match[2];
                head = line.substr(0, match.position(1));
                std::size_t pos = head.find(" encodedData size=");
                if (pos != std::string::npos) {
                    head = head.substr(0, pos);
                }

                if (!encoded_data.empty()) {
                    DecodeProtoFromEncodedData(head, encoded_data, type_name, outfile);
                    analyse_num++;
                }
            } else {
                outfile << line << "\n";
                origin_num++;
            }
        }

        std::cout << "File: " << input_file << " 解析完成！解析条数: " << analyse_num
                  << ", 原始日志行数: " << origin_num << std::endl;
    }

    std::cout << "------------------------解析结束------------------------" << std::endl;
    return 0;
}

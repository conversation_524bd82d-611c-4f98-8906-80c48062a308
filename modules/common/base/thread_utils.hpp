/*************************************************************************
 > File Name: modules/common/base/thread_utils.hpp
 > Author: ya<PERSON>.zhao
 > Mail: <EMAIL>
 > Created Time: 2024年03月13日 星期三 13时11分35秒
 ************************************************************************/

#pragma once
#ifndef MODULES_COMMON_BASE_THREAD_UTILS_HPP
#define MODULES_COMMON_BASE_THREAD_UTILS_HPP
#include <iostream>
#include <pthread.h>
#if __QNX__
// 包含 QNX Neutrino 系统相关的头文件
#include <sys/neutrino.h>
#endif
#include <cstring>
#include <unordered_map>
#include <tuple>
#include "cyber/cyber.h"


// 设置线程名称
inline bool set_thread_name(pthread_t thread, const char* name) {
    // QNX has 15 char limit including null terminator
    char truncated_name[16] = {0}; 
    strncpy(truncated_name, name, 15);
    
    int result = pthread_setname_np(thread, truncated_name);
    if (result != 0) {
        AERROR << "Failed to set thread name: " << strerror(result);
        return false;
    }
    return true;
}

// 设置线程绑核策略
inline void set_thread_affinity(pthread_t thread, int core_id) {
#if __QNX__
    int result = ThreadCtlExt(0, thread, _NTO_TCTL_RUNMASK_GET_AND_SET, &core_id);
#else
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(core_id, &cpuset);
    int result = pthread_setaffinity_np(thread, sizeof(cpu_set_t), &cpuset);
#endif
    if (result != 0) {
        AERROR << "Failed to set thread affinity: " << strerror(result);
    }
}

// 设置线程优先级
inline void set_thread_priority(pthread_attr_t* attr, int priority) {
    // 设置调度参数
    /*
    sched_param param;
    param.sched_priority = priority;
    pthread_attr_setschedparam(attr, &param);
    */
}

inline void read_thread_attributes_from_file(
        std::unordered_map<std::string, std::tuple<int, int>> & map,
        const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        AERROR << "Failed to open file: " << filename;
        return;
    }else{
        map.clear();
    }
    std::string line;
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string thread_name;
        int core;
        int priority;
        if (iss >> thread_name >> core >> priority) {
            map[thread_name] = std::make_tuple(core, priority);
        } else {
            AERROR << "Invalid line format: " << line;
        }
    }
    file.close();
}



inline bool set_thread_attributes(const std::string& thread_name, pthread_t tid) {
    static std::unordered_map<std::string, std::tuple<int, int>> thread_attribute_pool = {
        {"routing_plan_thread_",  {0, 20}},
        {"gridmap_thread_",       {1, 20}},
        {"sensor_massage_thread_",{2, 20}},
        {"image_process_thread_", {3, 20}}
    };

#if 1
    read_thread_attributes_from_file(thread_attribute_pool, "/usr/vendor/apa/conf/thread_attributes.txt");
    //read_thread_attributes_from_file(thread_attribute_pool, "./thread_attributes.txt");
#endif

    auto iter = thread_attribute_pool.find(thread_name);
    if(iter != thread_attribute_pool.end()) {
        auto second = iter->second;
        auto t_core = std::get<0>(second);
        auto t_pri =  std::get<1>(second);
        AINFO <<"Thread info (name,core,priority) : ("<<thread_name<<","<<t_core<<","<<t_pri<< ") map size :" <<thread_attribute_pool.size();
        if(t_core < 0 || t_core > (int(std::thread::hardware_concurrency()) - 1)){
            set_thread_name(tid, thread_name.data());
        }else{
            std::string new_thread_name = thread_name + "core:" + std::to_string(t_core);
            set_thread_name(tid, new_thread_name.data());
            set_thread_affinity(tid, t_core);
        }
        return true;
    }else{
        AERROR << "Thread attributes not found for thread: " << thread_name;
    }
    return false;
}
#endif

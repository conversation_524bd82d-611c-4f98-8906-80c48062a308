#ifndef MODULES_COMMON_BASE_MEMORY_POOL_VECTOR_HPP
#define MODULES_COMMON_BASE_MEMORY_POOL_VECTOR_HPP

#include <cstdlib>
#include <vector>
#include <foonathan/memory/memory_pool.hpp>
#include <foonathan/memory/container.hpp>  


namespace mega
{

using Pool = foonathan::memory::memory_pool<foonathan::memory::array_pool>;

template <typename T>
class PoolManager
{
public:
    static Pool& get(std::size_t block_size = 4 * 1024 * 1024)
    {
        static Pool pool(sizeof(T), block_size);
        return pool;
    }
};

template <typename T, bool UseScoped = false>
class PoolVector
{
public:
    using RawAllocator = foonathan::memory::std_allocator<T, Pool>;
    using Allocator = std::conditional_t<UseScoped,
        std::scoped_allocator_adaptor<RawAllocator>,
        RawAllocator>;

    explicit PoolVector(Pool& external_pool)
        : vector_(Allocator(external_pool)) {}

    std::vector<T, Allocator>& get() { return vector_; }
    const std::vector<T, Allocator>& get() const { return vector_; }

    T& operator[](std::size_t i) { return vector_[i]; }
    const T& operator[](std::size_t i) const { return vector_[i]; }

    void push_back(const T& val) { vector_.push_back(val); }
    void push_back(T&& val) { vector_.push_back(std::move(val)); }

    template <typename... Args>
    void emplace_back(Args&&... args) { vector_.emplace_back(std::forward<Args>(args)...); }

    void reserve(std::size_t n) { vector_.reserve(n); }

    std::size_t size() const { return vector_.size(); }

    T& back() {
        return vector_.back();
    }

    T& front() {
        return vector_.front();
    }

    T& at(std::size_t i) {
        return vector_.at(i);
    }

    void pop_back() {
        vector_.pop_back();
    }

    void clear() {
        vector_.clear();
    }

    auto begin() { return vector_.begin(); }
    auto end() { return vector_.end(); }

    void swap(PoolVector<T>& other) noexcept {
        vector_.swap(other.vector_);
    }

    void insert(typename std::vector<T, Allocator>::iterator pos, const T& val) {
        vector_.insert(pos, val);
    }

    void insert(typename std::vector<T, Allocator>::iterator pos, T&& val) {
        vector_.insert(pos, std::move(val));
    }

    template <typename InputIterator>
    void insert(typename std::vector<T, Allocator>::iterator pos, InputIterator first, InputIterator last) {
        vector_.insert(pos, first, last);
    }
private:
    std::vector<T, Allocator> vector_;
};

/// === 比较操作符重载 ===
template <typename T>
inline bool operator==(const PoolVector<T>& lhs, const PoolVector<T>& rhs) {
    return lhs.get() == rhs.get();
}

template <typename T>
inline bool operator!=(const PoolVector<T>& lhs, const PoolVector<T>& rhs) {
    return lhs.get() != rhs.get();
}

template <typename T>
inline bool operator<(const PoolVector<T>& lhs, const PoolVector<T>& rhs) {
    return lhs.get() < rhs.get();
}

template <typename T>
inline bool operator<=(const PoolVector<T>& lhs, const PoolVector<T>& rhs) {
    return lhs.get() <= rhs.get();
}

template <typename T>
inline bool operator>(const PoolVector<T>& lhs, const PoolVector<T>& rhs) {
    return lhs.get() > rhs.get();
}

template <typename T>
inline bool operator>=(const PoolVector<T>& lhs, const PoolVector<T>& rhs) {
    return lhs.get() >= rhs.get();
}


template <typename T>
inline void swap(PoolVector<T>& lhs, PoolVector<T>& rhs) noexcept {
    lhs.swap(rhs);
}

}

#endif
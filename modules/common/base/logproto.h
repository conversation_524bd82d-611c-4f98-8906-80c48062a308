#ifndef _LOGPROTO_H_
#define _LOGPROTO_H_
#include "google/protobuf/descriptor.h"
#include "google/protobuf/message.h"
#include "modules/common/base/base64.h"
#include "cyber/cyber.h"

inline void LogProtoImpl(const std::string& filename,const int line,const std::string& funcname, const std::shared_ptr<google::protobuf::Message>& msg) {
  if (!msg) {
    AINFO << funcname << " received null shared_ptr";
    return;
  }
  int ByteSize = msg->ByteSize();
  void* msg_arrary_buffer = malloc(ByteSize);
  if (!msg_arrary_buffer) {
    AINFO << __func__ << " msg_arrary_buffer allocation failed";
    return;
  }

  msg->SerializeToArray(msg_arrary_buffer, ByteSize);
  std::string encodedData = base64_encode(static_cast<char*>(msg_arrary_buffer), ByteSize);
  AINFO << " [FILE] " << filename
        << " [LINE] " << line
        << " [FUNC] " << funcname
        << " encodedData size=" << encodedData.size()
        << " type=" << msg->GetDescriptor()->full_name()
        << " data=" << encodedData;

  free(msg_arrary_buffer);
}

// Overload: if input is already a shared_ptr
inline void LogProto(const std::string& filename,const int line,const std::string& funcname, const std::shared_ptr<google::protobuf::Message>& msg) {
  LogProtoImpl(filename,line,funcname, msg);
}

// Overload: if input is a reference, convert to shared_ptr
inline void LogProto(const std::string& filename,const int line,const std::string& funcname, const google::protobuf::Message& msg) {
  // Create a copy of the message and wrap it in shared_ptr
  std::shared_ptr<google::protobuf::Message> copy(msg.New());
  copy->CopyFrom(msg);
  LogProtoImpl(filename,line,funcname, copy);
}
#endif
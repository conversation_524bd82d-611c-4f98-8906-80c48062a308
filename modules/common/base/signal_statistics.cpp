/*************************************************************************
 > File Name: signal_Statistics.cpp
 > Author: binghao.li 
 > Mail: <EMAIL>
 > Created Time: 2023年08月03日 
 ************************************************************************/

#include <iostream>
#include "signal_statistics.h"

SignalStatistics::SignalStatistics(){}

SignalStatistics::~SignalStatistics(){
    for(auto it = channel_metrics_map.begin(); it != channel_metrics_map.end(); it++){
        delete it->second;
    }
}

void SignalStatistics::register_statistic(const std::string& name, uint64_t max_delay_time, uint64_t min_pub_hz, uint64_t check_frequency){
   struct periodic_signal *channel_metrics = new periodic_signal(); 
   channel_metrics->first_time = 0;
   channel_metrics->last_time = 0;
   channel_metrics->interval = 0;
   channel_metrics->cnt = 0;
   channel_metrics->frame_counter_ = 0;
   channel_metrics->ratio = 0;
   channel_metrics->limit_time_delay = max_delay_time; //最大延迟
   channel_metrics->limit_publish_hz = min_pub_hz; //最小频率
    channel_metrics->check_frequency_ = check_frequency;
   this->channel_metrics_map[name] = channel_metrics;
}

bool SignalStatistics::message_count(const std::string &channel){
    if (channel_metrics_map.find(channel) == channel_metrics_map.end()){
        AINFO << __FUNCTION__ << " channel not exist : " << channel;
        return false;
    }
    auto rate = channel_metrics_map[channel];
    uint64_t now = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    double double_now = now / 1e9;         
    if (rate->cnt == 0) {
        rate->first_time = now;
        rate->last_time = now;
        rate->cnt++;
    } else {
        rate->interval = now - rate->last_time;
        rate->last_time = now;
        rate->cnt++;
    }
    rate->frame_counter_++;
    rate->msg_time_ = apollo::cyber::Time::MonoTime();
    if (rate->frame_counter_ >= rate->check_frequency_ )
    {
        // delay
        if(rate->interval > rate->limit_time_delay){
            AINFO <<"Signal_Statistics channel : [" <<channel<<"], last receive at " << rate->last_time;
        }
        // hz
        auto time_now = apollo::cyber::Time::MonoTime();
        auto curMsgTime = rate->msg_time_;
        auto deltaTime = curMsgTime - rate->time_last_;
        double frame_ratio_ = rate->frame_counter_ / deltaTime.ToSecond();
        rate->time_last_ = curMsgTime;
        rate->time_last_calc_ = time_now;
        rate->ratio = frame_ratio_;
        AINFO <<"Signal_Statistics channel : [" <<channel<<"], ratio " << frame_ratio_;
        if(frame_ratio_ < rate->limit_publish_hz){
            AINFO <<"Signal_Statistics  less than limit pub hz channel : [" <<channel<<"], ratio " << frame_ratio_;
        }
        rate->frame_counter_ = 0;
    }
    return true;
    
}

#include "thirdparty/recommend_protocols/drivers/proto/camera.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"
#include "avm_weima.h"
#include "cyber/cyber.h"
#include <google/protobuf/util/json_util.h>
#include <memory>
#include <vector>
#include "base/util/app_template.hpp"
#include <sys/time.h>

static double get_timestamp(struct timeval * pt) {
	double N = 1000.0;
	struct timeval te;
	if(pt==nullptr)
		gettimeofday(&te, NULL); // get current time
	else
		te = *pt;
	double milliseconds = te.tv_sec*N+ te.tv_usec/N; // calculate milliseconds
	return milliseconds/1000;
}

using rainbowdash::common::Header;
using rainbowdash::drivers::Image;
using rainbowdash::drivers::ImageSet;
using apollo::cyber::Node;

static void sendFrameCallback(AvmImageData & image_data, void * extra) {
	static int seq = 0;
	std::shared_ptr<apollo::cyber::Writer<Image>> talker = *((std::shared_ptr<apollo::cyber::Writer<Image>> *) extra);
	auto msg = std::make_shared<Image>();
	auto header = msg->mutable_header();

	header->set_timestamp(get_timestamp(nullptr));
	header->set_module_name("avm_node");
	header->set_sequence_num(seq);
	header->set_version(0);

	msg->set_measurement_time(image_data._ts);
	msg->set_encoding("RGB8");
	msg->set_height(image_data._height);
	msg->set_width(image_data._width);
	msg->set_depth(image_data._depth);
	msg->set_data(image_data._bytes, image_data._width*image_data._height*image_data._depth);
	msg->set_start("bottomleft");

	talker->Write(msg);
	seq++;

	return;
}

static void sendRawCallback(std::vector<std::shared_ptr<CameraData>> & batch, void * extra) {
    std::string names[4]={"front", "rear", "left", "right"};
	if(batch.size()!=4)
		return;
	static int seq = 0;
	std::shared_ptr<apollo::cyber::Writer<ImageSet>> talker = *((std::shared_ptr<apollo::cyber::Writer<ImageSet>> *) extra);
	auto msg = std::make_shared<ImageSet>();
	auto header = msg->mutable_header();

	header->set_timestamp(get_timestamp(nullptr));
	header->set_module_name("avm_node");
	header->set_sequence_num(seq);
	header->set_version(0);
	msg->set_measurement_time(batch[0]->_ts);

    auto images = msg->mutable_images();
	for(unsigned int i=0; i<batch.size(); i++){
		auto image = std::make_shared<Image>();
		switch(batch[i]->_type){
			case CameraData::RGB888:
                                image->set_encoding("RGB888");
				image->set_depth(3);
				break;
			case CameraData::YUV422:
                                image->set_encoding("YUV422");
				image->set_depth(2);
				break;
            default:break;
		}
		//image->set_depth(2);
		image->set_height(batch[i]->_height);
		image->set_width(batch[i]->_width);
		image->set_data(batch[i]->_data, batch[i]->_length);
		(*images)[names[i]] = *image;
	}

	talker->Write(msg);
	seq++;
}


static void sendRawRearCallback(std::vector<std::shared_ptr<CameraData>> & batch, void * extra){
	//std::string names[4]={"front", "rear", "left", "right"};
	//auto &select_rear_name = names[1];
	if(batch.size()!=4)
		return;

	auto &select_rear_image = batch[1];
	std::shared_ptr<apollo::cyber::Writer<Image>> talker = *((std::shared_ptr<apollo::cyber::Writer<Image>> *) extra);
	auto image = std::make_shared<Image>();

	switch(select_rear_image->_type){
		case CameraData::RGB888:
							image->set_encoding("RGB888");
			image->set_depth(3);
			break;
		case CameraData::YUV422:
							image->set_encoding("YUV422");
			image->set_depth(2);
			break;
        default:break;
	}
	image->set_height(select_rear_image->_height);
	image->set_width(select_rear_image->_width);
	image->set_data(select_rear_image->_data, select_rear_image->_length);
	talker->Write(image);
}

int main(int argc, char *argv[]) {
    adas::app_template app;
    namespace bpo = boost::program_options;
    // clang-off
    app.add_options()
     ("node_name",bpo::value<std::string>()->default_value(""),"apa control node name")
     ("node_cfg",bpo::value<std::string>()->default_value("conf/apa_control_server.json"),"node config json file")
     ("source_path",bpo::value<std::string>()->default_value("../yuv_test/"),"data source path")
     ("dev",bpo::value<std::vector<std::string>>(),"dev name")
     ("type",bpo::value<uint8_t>()->default_value(1),"set read mode stream/file")
     ("conf",bpo::value<std::string>()->default_value("conf/avm/avm.ini"), "template");
    // clang-on
    if(const auto &ret = app.run(argc,argv,"conf") ; ret != 0){
        if(ret == 1){
            std::cout <<"show help!"<< std::endl;
            return 0;
        }
        std::cout <<"command_line or conf_file parse failed !"<< std::endl;
        return -1;
    }
    auto &&config = app.configuration();
    auto node_cfg = config["node_cfg"].as<std::string>();
    auto source_path = config["source_path"].as<std::string>();
    auto node_name = config["node_name"].as<std::string>();
    auto dev = config["dev"].as<std::vector<std::string>>();
    auto type = config["type"].as<uint8_t>();
    bpo::notify(config);

    std::cout <<"source_path : "<<source_path<< std::endl;
    std::cout <<"node_name : "<<node_name<< std::endl;
    std::cout <<"type : "<<type<< std::endl;
    auto dev_size = dev.size();
    if (dev_size  < 1) {
        printf("usage: %s config_directory dev_name ...\n", argv[0]);
        return -1;
    }
    std::cout <<"dev size : "<<dev_size<<" dev list : ";
    for(unsigned int i = 0; i < dev_size; i++){
        std::cout <<dev[i] << ((i == (dev.size() - 1)) ?"\n":" ");
    }
    if(node_name == ""){
        printf("usage: %s node name don't set ...\n", argv[0]);
        return -1;
    }
    AINFO << __FUNCTION__ << "argc " << argc;

    apollo::cyber::Init(argv[0]);
    auto node = apollo::cyber::CreateNode(node_name);
    auto talker = node->CreateWriter<Image>("channel/rgb_camera");
    auto raw_talker = node->CreateWriter<ImageSet>("channel/raw_images");
    auto raw_rear_talker = node->CreateWriter<Image>("channel/raw_rear_images");
	std::shared_ptr<MixedBatcher> batcher = nullptr;
#ifdef NOT_USE_CAMERA
	std::mutex channel_mutex_;
	const char *channel_names[] = {"avm_front", "avm_rear", "avm_right", "avm_left"};
  	int channel_size = (sizeof(channel_names) / sizeof(*channel_names));
  	batcher = std::make_shared<MixedBatcher>(channel_size, 1000);

	// create img readers
    for (int i = 0; i < channel_size; i++) {
		node->CreateReader<CompressedImage>( channel_names[i],
		   [i, channel_names, &batcher, &channel_mutex_](const std::shared_ptr<CompressedImage> &data) -> void {
			std::lock_guard<std::mutex> lock(channel_mutex_);
			// printf("************%-20s:%f, %f\n", channel_names[i], data->measurement_time(), data->header().timestamp());
			batcher->AddImg(i, data);
			});
  	}
#endif
    DataSource_t data_source;
    data_source.type = type;
    data_source.n_camera = dev_size;
    data_source.dev_names = dev;
    data_source.path = source_path;
    DataToAVM(data_source, sendFrameCallback, (void*)&talker, sendRawRearCallback, (void*)&raw_rear_talker, batcher);
    //DataToAVM(data_source, sendFrameCallback, (void*)&talker, sendRawCallback, (void*)&raw_talker);
    //StreamToAVM(argc-2, argv+2, argv[1]);
    apollo::cyber::WaitForShutdown();

    return 0;
}
# if 0
int main(int argc, char * argv[]) {
    AINFO << __FUNCTION__ << "argc " << argc;

    apollo::cyber::Init(argv[0]);

	auto node = apollo::cyber::CreateNode("avm_node");
	auto talker = node->CreateWriter<Image>("channel/rgb_camera");
	auto raw_talker = node->CreateWriter<ImageSet>("channel/raw_images");

    if (argc < 2) {
		printf("usage: %s config_directory\n", argv[0]);
		return -1;
    }
    std::cout << "config path is "<< argv[1] <<std::endl;



	DataSource_t data_source;
	data_source.type = 0;
	data_source.path = argv[1];
	DataToAVM(data_source, sendFrameCallback, (void*)&talker, sendRawCallback, (void*)&raw_talker);
	return 0;
}
#endif

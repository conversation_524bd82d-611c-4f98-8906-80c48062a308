set(AVM "${CMAKE_SOURCE_DIR}/modules/drivers/avm/cpp")
set(AVM_STREAM_SOURCE ON)
add_executable(avm_server ${AVM}/avm.cpp ${PROTO_CC_FILES})
target_link_libraries(avm_server avm_weima cyber protobuf Boost::program_options protobuf-lite uuid atomic fastrtps fastcdr pthread m z rt gflags glog)
if(${AVM_STREAM_SOURCE})
    target_compile_options(avm_server PRIVATE "-DAVM_STREAM_SOURCE=${AVM_STREAM_SOURCE}")
endif()
target_compile_options(avm_server PRIVATE -std=gnu++1z)
add_custom_command(
    TARGET avm_server
    POST_BUILD
    COMMAND mkdir -p ${CMAKE_BINARY_DIR}/output
    COMMAND mkdir -p ${CMAKE_BINARY_DIR}/output/bin
    COMMAND mkdir -p ${CMAKE_BINARY_DIR}/output/lib
    COMMAND mkdir -p ${CMAKE_BINARY_DIR}/output/conf
    COMMAND mkdir -p ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND mv ${CMAKE_BINARY_DIR}/avm_server ${CMAKE_BINARY_DIR}/output/bin
    COMMAND cp ${CMAKE_SOURCE_DIR}/modules/drivers/avm/conf/avm.ini ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/res/weima/weima_calib_20230620/cam.json ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.fs ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.vs ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.mtl ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.obj ${CMAKE_BINARY_DIR}/output/conf/avm
    COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.bmp ${CMAKE_BINARY_DIR}/output/conf/avm
    #COMMAND python ${CMAKE_SOURCE_DIR}/script/copy_linux_depends.py ${CMAKE_BINARY_DIR}/avm_server -o ${CMAKE_BINARY_DIR}/output/lib
    # avm to output
    #COMMAND mkdir -p ${CMAKE_BINARY_DIR}/avm
    #COMMAND mkdir -p ${CMAKE_BINARY_DIR}/avm/config
    #COMMAND mkdir -p ${CMAKE_BINARY_DIR}/yuv_test
    #COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/res/weima/weima_calib_20221024/cam.json ${CMAKE_BINARY_DIR}/yuv_test
    #COMMAND mv avm_server ${CMAKE_BINARY_DIR}/avm
    #COMMAND cp ${CMAKE_SOURCE_DIR}/script/run.sh ${CMAKE_BINARY_DIR}
    #COMMAND cp ${CMAKE_SOURCE_DIR}/modules/drivers/avm/conf/avm.ini ${CMAKE_BINARY_DIR}/avm/config
    #COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.fs ${CMAKE_BINARY_DIR}
    #COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.vs ${CMAKE_BINARY_DIR}
    #COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.mtl ${CMAKE_BINARY_DIR}
    #COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.obj ${CMAKE_BINARY_DIR}
    #COMMAND cp ${CMAKE_SOURCE_DIR}/thirdparty/avm/bin/*.bmp ${CMAKE_BINARY_DIR}
    )

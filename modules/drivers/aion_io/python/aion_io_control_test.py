
import time
import sys
import os
import signal

from cyber.python.cyber_py3 import cyber
from thirdparty.recommend_protocols.drivers.aion_io.proto.aion_chassis_pb2 import AionChassisInfo
from thirdparty.recommend_protocols.drivers.io.proto.control_by_wire_input_pb2 import LatAndLongControlCmd
from thirdparty.recommend_protocols.drivers.io.proto.control_by_wire_input_pb2 import TorqueControlData
from thirdparty.recommend_protocols.drivers.io.proto.control_by_wire_input_pb2 import LatAnd<PERSON><PERSON><PERSON>hakeHandCmd
from thirdparty.recommend_protocols.common.proto.header_pb2 import CommonBool
from thirdparty.recommend_protocols.common.proto.header_pb2 import CommonGearPosition
from thirdparty.recommend_protocols.drivers.proto.imu_pb2 import Imu
from thirdparty.recommend_protocols.location.proto.pose_pb2 import Pose

str = "************************************************************\n\
* q:    quit code\n\
* a:    shake hand all\n\
* g:    shake hand lat\n\
* k:    shake hand lon\n\
* h:    shake hand all quit\n\
* y:    steering angle control mode, input steering angle to contorl steering angle\n\
************************************************************"

class simple_control:
    def __init__(self):
        print("start aion control test.")
        print(str)
        self.simple_contol_node = cyber.Node("aion_control_test")

        self.control_writer = self.simple_contol_node.create_writer('aion_io/chassisctl',LatAndLongControlCmd,1)
        self.takeover_writer = self.simple_contol_node.create_writer("aion_io/takeover",LatAndLongShakeHandCmd,1)
        self.control_cmd = LatAndLongControlCmd()
        self.takeover_cmd = LatAndLongShakeHandCmd()
        self.trq_ctrl_data = TorqueControlData()

        self.simple_contol_node.create_reader("aion_io/chassisinfo",AionChassisInfo,self.chassis_callback)
        self.simple_contol_node.create_reader("channel/corrimu",Imu,self.imu_callback)
        self.simple_contol_node.create_reader("location/vehicleinfo",Pose,self.spd_location_callback)
        self.simple_contol_node.create_reader("location/rtk",Pose,self.rtk_location_callback)
        self.chassis_info = AionChassisInfo()
        self.imu_info = Imu()
        self.spd_location = Pose()
        self.rtk_location = Pose()
        
        self.control_mode = 0
        self.tar_steer_angle = 0.0
        self.pre_steer_angle = 0.0
        self.in_session = False
        self.seq = 0
        self.outfile = 'y_200_recorded.csv'

        pass

    def imu_callback(self,msg):
        self.imu_info = msg

    def spd_location_callback(self,msg):
        self.spd_location = msg

    def rtk_location_callback(self,msg):
        self.rtk_location = msg

    def chassis_callback(self,msg):
        self.chassis_info = msg

    def takeover_control(self,lat,lon):
        self.takeover_cmd.header.timestamp = time.time()
        self.takeover_cmd.activate_lat = CommonBool.TRUE if 1 == lat else CommonBool.FALSE
        self.takeover_cmd.activate_long = CommonBool.TRUE if 1 == lon else CommonBool.FALSE
        self.takeover_writer.write(self.takeover_cmd)
    

    def publish_steer_angle_control(self, steer_angle):
        self.control_cmd.header.timestamp = time.time()
        self.trq_ctrl_data.is_braking = CommonBool.FALSE
        self.trq_ctrl_data.is_driving = CommonBool.FALSE
        self.trq_ctrl_data.tar_torque = 0
        self.trq_ctrl_data.tar_deceleration = 0
        self.trq_ctrl_data.is_steering = CommonBool.TRUE
        self.trq_ctrl_data.tar_steer_angle = steer_angle
        self.trq_ctrl_data.put_gear = CommonBool.FALSE
        self.trq_ctrl_data.tar_gear = CommonGearPosition.PARKING
        self.control_cmd.data.Pack(self.trq_ctrl_data)

        self.control_writer.write(self.control_cmd)


    def run(self):
        while not cyber.is_shutdown():
            signal.signal(signal.SIGINT, self.signal_handler)

            k = input('[Prompt] continue test or not?  [q] for Quit:  ')
            self.key_control(k)
            print(k)
            if (k == 'q'):
                return

    def key_control(self, k):
        if k == 'a':
            self.takeover_control(1,1)

        elif k == 'g':
            self.takeover_control(1,0)

        elif k == 'k':
            self.takeover_control(0,1)

        elif k == 'h':
            self.takeover_control(0,0)

        #takeover
        elif k == 'y':
            if(0 == self.control_mode):
                tar_angle = input("[Prompt] Single control mode. Steering angle control mode, Please input steering angle(double). [999] for Quit this loop\ntar_steer_angle=")
                self.tar_steer_angle = float(tar_angle)
                if(abs(999 - self.tar_steer_angle) > 0.01):
                    self.outfile = 'y_' + tar_angle + '_recorded.csv'
                    self.file = open(self.outfile, 'w')
                    self.file.write(
                        "time,tar_steer_angle,eps_steer_angle,speed,spd_location_x,spd_location_y,rtk_location_x," +
                        "rtk_location_y\n"
                    )
                    
                    self.publish_steer_angle_control(self.tar_steer_angle)
                    time.sleep(0.02)
                    self.publish_steer_angle_control(self.tar_steer_angle)
                    self.in_session = True

                    while self.in_session:
                        now = time.time()
                        self.record_data(now)
                        sleep_time = 0.01 - (time.time() - now)
                        if sleep_time > 0:
                            time.sleep(sleep_time)
                else:
                    return
        
        elif k == 'v':
            pass


    def signal_handler(self, signal, frame):
        self.in_session = False

    def record_data(self,time):
        self.write_file(time)
        if self.in_session == False:
            self.file.close()
            
    def write_file(self, time):
        """
        Write Message to File
        """
        self.seq = self.seq + 1
        if(100 == self.seq):
            self.seq = 0
            print("time=%.4f,tar_steer_angle=%.4f,eps_steer_angle=%.4f,speed=%.4f,spd_location_x=%.4f,spd_location_y=%.4f,rtk_location_x=%.4f,rtk_location_y=%.4f\n" % 
                (time,self.tar_steer_angle, self.chassis_info.eps_steer_angle,self.chassis_info.speed,self.spd_location.position.x,
                    self.spd_location.position.y, self.rtk_location.position.x, self.rtk_location.position.y))
        
        self.file.write(
            "%.4f,%s,%s,%s,%s,%s,%s,%s\n" %
            (time,self.tar_steer_angle, self.chassis_info.eps_steer_angle,self.chassis_info.speed,self.spd_location.position.x,
                    self.spd_location.position.y, self.rtk_location.position.x, self.rtk_location.position.y))


if __name__=="__main__":
    cyber.init()
    # main()
    s_ctl  = simple_control()
    s_ctl.run()
    cyber.shutdown()
    pass




#!/usr/bin/env python3

"""
进行数据的批处理，并进一步进行处理
"""
import math
from pickle import FALSE, TRUE
import sys
from time import sleep

import numpy as np
import tkinter.filedialog

from process import get_start_index
from process import preprocess ,preprocessalldata
from process import process
from plot_results import plot_result_csv
import os
import re

# files_path_tmp = "/home/<USER>/code/adas_core_aion/adas-core/modules/drivers/aion_io/python/vehicle_calibration"
files_path_tmp = os.getcwd()
class Plotter(object):
    """
    plot the speed info
    """

    def __init__(self, filename):
        """
        init the speed info
        """

        np.set_printoptions(precision=3)
        
        print(files_path_tmp)
        self.file = open(files_path_tmp+'/'+'result.csv', 'a')
        # self.file_one = open(filename + ".result", 'w')
        self.tablespeed = []
        self.tableacc = []
        self.tablecmd = []

    def prase_data(self, filename):
        tablespeed = []
        tableacc = []
        cmd = 0
        with open(filename, 'r') as f:

            for line in f:
                items = line.split(',')
                cmd = round(float(items[0]),3)
                speed = round(float(items[1]),3)
                acc = round(float(items[2]),3)
                tablespeed.append(speed)
                tableacc.append(acc)
            self.tablecmd.append(cmd)
            self.tablespeed.append(tablespeed)
            self.tableacc.append(tableacc)

        # return tablecmd, tablespeed, tableacc

    def process_data(self, filename):
        """
        load the file and preprocess th data
        """

        # self.data = preprocess(filename)
        self.data = preprocessalldata(filename)

        self.tablecmd, self.tablespeed, self.tableacc, self.speedsection, self.accsection, self.timesection = process(
            self.data)

    def save_data(self):
        """
        save_data
        """
        for i in range(len(self.tablecmd)):
            for j in range(len(self.tablespeed[i])):
                self.file.write("%s, %s, %s\n" %
                                (self.tablecmd[i], self.tablespeed[i][j],
                                 self.tableacc[i][j]))
                # self.file_one.write("%s, %s, %s\n" %
                #                     (self.tablecmd[i], self.tablespeed[i][j],
                #                      self.tableacc[i][j]))

def is_in_range(cur, start, end):
    if cur > start and cur <= end:
        return 1
    return 0

def main():
    """
    demo
    """
    try:
        # open(files_path_tmp+'/'+'result.csv', 'w')
        os.remove(files_path_tmp+'/'+'result.csv')
        print(f"{files_path_tmp} 已被删除.")
    except OSError as e:
        print(f"错误: {files_path_tmp} : {e.strerror}")
    
    # file = open(files_path_tmp+'/'+'result.csv', 'a')
    # file.write('')
    # sleep(10)
    start_torque = int(000-10)
    end_torque = int(3000+10)

    # files_path = "/home/<USER>/code/adas_core_aion/adas-core/modules/drivers/aion_io/python/vehicle_calibration/data_handle/batch_log_pure"
    # files_path = "/home/<USER>/code/adas_core_aion/adas-core/modules/drivers/aion_io/python/vehicle_calibration/data_handle/batch_log_pure_handle"
    files_path = os.getcwd() + "/data_handle/batch_log_pure_handle"
    print(files_path)

    file_list = os.listdir(files_path)
    sorted_files = sorted(file_list, key=lambda x: int(re.findall(r'\d+', x)[0]))

    # 打印结果
    for file in sorted_files:
        cur_torque = int(re.findall(r'\d+', file)[0])
        print('cur_torque=',cur_torque)
        if 1 == is_in_range(cur_torque, start_torque, end_torque):
            file_path = files_path+'/'+file
            plotter = Plotter(file_path)
            plotter.prase_data(file_path)
            plotter.save_data()
            print('save result to:', file_path + ".result")
    
    plot_result_csv(files_path_tmp+'/'+'result.csv')


if __name__ == '__main__':
    main()

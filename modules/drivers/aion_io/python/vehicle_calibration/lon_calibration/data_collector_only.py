#!/usr/bin/env python3

import os
from pickle import TRUE
import signal
import sys
import time
from plot_data import Plotter

from cyber.python.cyber_py3 import cyber
from thirdparty.recommend_protocols.drivers.aion_io.proto.aion_chassis_pb2 import AionChassisInfo
from thirdparty.recommend_protocols.drivers.io.proto.control_by_wire_input_pb2 import LatAndLongControlCmd
from thirdparty.recommend_protocols.drivers.io.proto.control_by_wire_input_pb2 import TorqueControlData
from thirdparty.recommend_protocols.drivers.io.proto.control_by_wire_input_pb2 import LatAndLongShakeHandCmd
from thirdparty.recommend_protocols.common.proto.header_pb2 import CommonBool
from thirdparty.recommend_protocols.common.proto.header_pb2 import CommonGearPosition
from thirdparty.recommend_protocols.drivers.proto.imu_pb2 import Imu
from thirdparty.recommend_protocols.location.proto.pose_pb2 import Pose


class DataCollector(object):
    def __init__(self,node) -> None:
        self.control_writer = node.create_writer('aion_io/chassisctl',LatAndLongControlCmd,1)
        self.takeover_writer = node.create_writer("aion_io/takeover",LatAndLongShakeHandCmd,1)
        self.control_cmd = LatAndLongControlCmd()
        self.takeover_cmd = LatAndLongShakeHandCmd()
        self.trq_ctrl_data = TorqueControlData()

        node.create_reader("aion_io/chassisinfo",AionChassisInfo,self.chassis_callback)
        node.create_reader("channel/corrimu",Imu,self.imu_callback)
        self.chassis_info = AionChassisInfo()
        self.imu_info = Imu()

        self.in_session = False
        self.outfile = 'c_200_recorded.csv'
        self.ready = False
        self.seq = 0
        pass

    def imu_callback(self,msg):
        self.imu_info = msg

    def chassis_callback(self,msg):
        self.chassis_info = msg

    def takeover_control(self,lat,lon):
        self.takeover_cmd.header.timestamp = time.time()
        self.takeover_cmd.activate_lat = CommonBool.TRUE if 1 == lat else CommonBool.FALSE
        self.takeover_cmd.activate_long = CommonBool.TRUE if 1 == lon else CommonBool.FALSE
        self.takeover_writer.write(self.takeover_cmd)

     # IDNRP 01234    
    def pre_ready(self,tar_gear,lat_t):
        tar_decacc = -1.0
        if((1 == lat_t and 3 != self.chassis_info.eps_takeover_status) or 3 != self.chassis_info.lon_takeover_status):
            self.takeover_control(lat_t,1)
        elif(tar_gear == self.chassis_info.vcu_current_gear_level):
            self.control_cmd.header.timestamp = time.time()
            self.trq_ctrl_data.is_braking = CommonBool.TRUE
            self.trq_ctrl_data.is_driving = CommonBool.FALSE
            self.trq_ctrl_data.tar_torque = 0
            self.trq_ctrl_data.tar_deceleration = tar_decacc
            self.trq_ctrl_data.is_steering = CommonBool.TRUE if 1 == lat_t else CommonBool.FALSE
            self.trq_ctrl_data.tar_steer_angle = 0
            self.trq_ctrl_data.put_gear = CommonBool.FALSE
            self.trq_ctrl_data.tar_gear = CommonGearPosition.NONE
            self.control_cmd.data.Pack(self.trq_ctrl_data)
            self.control_writer.write(self.control_cmd)
            self.ready = True
            print("Pre Ready OK!!!!!!!!!!!!!")
        else:
            self.control_cmd.header.timestamp = time.time()
            self.trq_ctrl_data.is_braking = CommonBool.TRUE
            self.trq_ctrl_data.is_driving = CommonBool.FALSE
            self.trq_ctrl_data.tar_torque = 0
            self.trq_ctrl_data.tar_deceleration = tar_decacc
            self.trq_ctrl_data.is_steering = CommonBool.TRUE if 1 == lat_t else CommonBool.FALSE
            self.trq_ctrl_data.tar_steer_angle = 0
            self.trq_ctrl_data.put_gear = CommonBool.DRIVING
            self.trq_ctrl_data.tar_gear = CommonGearPosition.NONE
            self.control_writer.write(self.control_cmd)

    def acc_dec_control_test(self,acc_dec):
        self.control_cmd.header.timestamp = time.time()
        self.trq_ctrl_data.is_braking = CommonBool.TRUE
        self.trq_ctrl_data.is_driving = CommonBool.FALSE
        self.trq_ctrl_data.tar_torque = 0
        self.trq_ctrl_data.tar_deceleration = acc_dec
        self.trq_ctrl_data.is_steering = CommonBool.FALSE
        self.trq_ctrl_data.tar_steer_angle = 0
        self.trq_ctrl_data.put_gear = CommonBool.FALSE
        self.trq_ctrl_data.tar_gear = CommonGearPosition.NONE
        self.control_cmd.data.Pack(self.trq_ctrl_data)
        self.control_writer.write(self.control_cmd)

    def run(self,cmd):
        signal.signal(signal.SIGINT, self.signal_handler)

        if(cmd[0] == 't'):
            self.takeover_control(int(cmd[1]),int(cmd[2]))
        elif(cmd[0] == 'd'):
            self.acc_dec_control_test(float(cmd[1]))
        elif(cmd[0] == 'p'):
            self.ready = False
            while False == self.ready:
                now = time.time()
                self.pre_ready(int(cmd[1]),int(cmd[2]))#gear lat_t
                sleep_time = 0.01 - (time.time() - now)
                if sleep_time > 0:
                    time.sleep(sleep_time)
        elif(cmd[0] == 'c'):
            self.outfile = 'c_' + cmd[1] + '_recorded.csv'
            if(3 != self.chassis_info.lon_takeover_status or True != self.ready):
                print("Please ready frist!!!!!!!!!")
                return
            # while os.path.exists(self.outfile):
            #     self.outfile = 'c_' + cmd[1] + '_recorded.csv'
            self.file = open(self.outfile, 'w')
            self.file.write(
                "time,io,ctlmode,tar_torque,tar_steer_angle,tar_deceleration,speed," +
                "lon_acceleration,lat_acceleration,eps_steer_angle,yaw_rate,vcu_torque,acc_x," +
                "acc_y,acc_z,deal_a,deal_b,deal_c\n"
            )
            
            self.publish_control(float(cmd[1]))
            time.sleep(0.02)
            self.publish_control(float(cmd[1]))
            self.in_session = True

            while self.in_session:
                now = time.time()
                self.record_data(now)
                sleep_time = 0.01 - (time.time() - now)
                if sleep_time > 0:
                    time.sleep(sleep_time)

    def signal_handler(self, signal, frame):
        self.in_session = False

    def publish_control(self, torque):
        self.control_cmd.header.timestamp = time.time()
        self.trq_ctrl_data.is_braking = CommonBool.FALSE
        self.trq_ctrl_data.is_driving = CommonBool.TRUE
        self.trq_ctrl_data.tar_torque = torque
        self.trq_ctrl_data.tar_deceleration = 0
        self.trq_ctrl_data.is_steering = CommonBool.FALSE
        self.trq_ctrl_data.tar_steer_angle = 0
        self.trq_ctrl_data.put_gear = CommonBool.FALSE
        self.trq_ctrl_data.tar_gear = CommonGearPosition.PARKING
        self.control_writer.write(self.control_cmd)

    def record_data(self,time):
        self.write_file(time, 1)
        if self.in_session == False:
            self.file.close()
            
    def write_file(self, time, io):
        """
        Write Message to File
        """
        self.seq = self.seq + 1
        if(100 == self.seq):
            self.seq = 0
            print("time=%.4f,tar_torque=%.4f,eps_steer_angle=%.4f,speed=%.4f,wheel_torque=%.4f,acc_x=%.4f,acc_y=%.4f,acc_z=%.4f\n" % 
                (time,self.trq_ctrl_data.tar_torque, self.chassis_info.eps_steer_angle,self.chassis_info.speed,self.chassis_info.vcu_actual_vehicle_wheel_torque,
                    self.imu_info.linear_acceleration.x, self.imu_info.linear_acceleration.y, self.imu_info.linear_acceleration.z))
        
        self.file.write(
            "%.4f,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n" %
            (time, io, 1, self.trq_ctrl_data.tar_torque, self.trq_ctrl_data.tar_steer_angle, self.trq_ctrl_data.tar_deceleration,
             self.chassis_info.speed, self.chassis_info.lon_acceleration, self.chassis_info.lat_acceleration, self.chassis_info.eps_steer_angle,
             self.chassis_info.yaw_rate, self.chassis_info.vcu_actual_vehicle_wheel_torque,
             self.imu_info.linear_acceleration.x, self.imu_info.linear_acceleration.y, self.imu_info.linear_acceleration.z,0.0,0.0,0.0))
    
    def record_data_only_run(self):
        self.outfile = "test"
        self.file = open(self.outfile, 'w')
        self.file.write(
            "time,io,ctlmode,tar_torque,tar_steer_angle,tar_deceleration,speed," +
            "lon_acceleration,lat_acceleration,eps_steer_angle,yaw_rate,vcu_torque,acc_x," +
            "acc_y,acc_z,deal_a,deal_b,deal_c\n"
        )
        self.in_session = True
        while self.in_session:
            now = time.time()
            self.record_data(now)
            sleep_time = 0.01 - (time.time() - now)
            if sleep_time > 0:
                time.sleep(sleep_time)
def main():
    node = cyber.Node("data_collector")
    
    data_collector = DataCollector(node)
    plotter = Plotter()
    while True:
        cmd = input("Enter commands: ").split()
        if len(cmd) == 0:
            print('Quiting.')
            break
        elif len(cmd) == 1:
            if cmd[0] == "q":
                break
            elif cmd[0] == "p":
                print('Plotting handle result.')
                if os.path.exists(data_collector.outfile):
                    plotter.process_data(data_collector.outfile)
                    plotter.plot_result()
                    pass
                else:
                    print('File does not exist: %s' % data_collector.outfile)
            elif cmd[0] == "ps":
                print('Plotting source result.')
                if os.path.exists(data_collector.outfile):
                    plotter.process_all_data(data_collector.outfile)
                    plotter.plot_xy()
                    pass
                else:
                    print('File does not exist: %s' % data_collector.outfile)
            elif cmd[0] == "x":
                print('Removing last result.')
                if os.path.exists(data_collector.outfile):
                    os.remove(data_collector.outfile)
                else:
                    print('File does not exist: %s' % data_collector.outfile)
            elif cmd[0] == "s":
                print('Save result.')
                
        elif len(cmd) >= 2:
            data_collector.run(cmd)

if __name__ == '__main__':
    cyber.init()
    # main()
    node = cyber.Node("data_collector_only")
    
    data_collector = DataCollector(node)
    data_collector.record_data_only_run()
    cyber.shutdown()


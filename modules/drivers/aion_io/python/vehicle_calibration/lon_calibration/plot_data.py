#!/usr/bin/env python3

import sys

import matplotlib.pyplot as plt
import numpy as np
import tkinter.filedialog

from process import get_start_index
from process import preprocess,preprocessalldata
from process import process


class Plotter(object):
    """
    Plot the speed info
    """

    def __init__(self):
        """
        Init the speed info
        """
        np.set_printoptions(precision=3)
        self.file = open('temp_result.csv', 'a')

    def process_data(self, filename):
        """
        Load the file and preprocess th data
        """
        # self.data = preprocess(filename)
        self.data = preprocessalldata(filename)

        self.tablecmd, self.tablespeed, self.tableacc, self.speedsection, self.accsection, self.timesection = process(
            self.data)
    
    def process_all_data(self, filename):
        """
        process all data
        """
        self.data = preprocessalldata(filename)
       
    def plot_result(self):
        """
        Plot the desired data
        """
        fig, axarr = plt.subplots(2, 1, sharex=True)
        plt.tight_layout()
        fig.subplots_adjust(hspace=0)

        axarr[0].plot(
            self.data['time'], self.data['tar_deceleration'], label='Brake CMD')
        axarr[0].plot(
            self.data['time'], self.data['tar_torque'], label='Throttle CMD')
        axarr[0].legend(fontsize='medium')
        axarr[0].grid(True)
        axarr[0].set_title('Command')

        axarr[1].plot(
            self.data['time'],
            self.data['speed'],
            label='Vehicle Speed')

        for i in range(len(self.timesection)):
            axarr[1].plot(
                self.timesection[i],
                self.speedsection[i],
                label='Speed Segment')
            axarr[1].plot(
                self.timesection[i], self.accsection[i], label='IMU Segment')

        axarr[1].legend(fontsize='medium')
        axarr[1].grid(True)
        axarr[1].set_title('Speed')

        mng = plt.get_current_fig_manager()
        mng.full_screen_toggle()

        # plt.tight_layout(pad=0.20)
        fig.canvas.mpl_connect('key_press_event', self.press)
        plt.show()

    def plot_xy(self):
        plt.figure()
        plt.plot(
            self.data['time'],
            self.data['speed'],
            label='Vehicle Speed',
            linestyle='--',
            marker='.')
        plt.legend(loc='upper center') 
        plt.figure()
        plt.plot(
            self.data['time'],
            self.data['acc_x'],
            label='Vehicle acc_x')
        plt.legend(loc='upper center') 
        plt.figure()
        plt.plot(
            self.data['time'],
            self.data['speed'],
            label='Vehicle Speed')
        plt.plot(
            self.data['time'],
            self.data['deal_a'],
            label='Vehicle deal_a')
        plt.legend(loc='upper center') 
        plt.figure()
        plt.plot(
            self.data['time'],
            self.data['acc_x'],
            label='Vehicle acc_x')
        plt.plot(
            self.data['time'],
            self.data['deal_b'],
            label='Vehicle deal_b')
        plt.legend(loc='upper center') 
        plt.figure()
        plt.plot(
            self.data['time'],
            self.data['tar_torque'],
            label='Vehicle tar_torque')
        plt.legend(loc='upper center') 
        plt.figure()
        plt.plot(
            self.data['time'],
            self.data['deal_c'],
            label='Vehicle source_acc_x')
        plt.legend(loc='upper center') 
        # plt.figure()
        # plt.plot(
        #     self.data['time'],
        #     self.data['vcu_torque'],
        #     label='Vehicle vcu_torque')
        # plt.legend(loc='upper center') 
        # plt.figure()
        # plt.plot(
        #     self.data['time'],
        #     self.data['tar_steer_angle'],
        #     label='Vehicle tar_steer_angle')
        # plt.legend(loc='upper center') 
        # plt.figure()
        # plt.plot(
        #     self.data['time'],
        #     self.data['eps_steer_angle'],
        #     label='Vehicle eps_steer_angle')
        # plt.legend(loc='upper center') 
        # plt.figure()
        # plt.plot(
        #     self.data['time'],
        #     self.data['lon_acceleration'],
        #     label='Vehicle lon_acceleration')
        # plt.legend(loc='upper center') 
        # plt.figure()
        # plt.plot(
        #     self.data['time'],
        #     self.data['lat_acceleration'],
        #     label='Vehicle_lat_acceleration')
        # plt.legend(loc='upper center') 
        # plt.figure()
        # plt.plot(
        #     self.data['time'],
        #     self.data['yaw_rate'],
        #     label='yaw_rate')
        # plt.legend(loc='upper center') 
        plt.show()

    def press(self, event):
        """
        Keyboard events during plotting
        """
        if event.key == 'q' or event.key == 'Q':
            self.file.close()
            plt.close()

        if event.key == 'w' or event.key == 'W':
            for i in range(len(self.tablecmd)):
                for j in range(len(self.tablespeed[i])):
                    self.file.write("%s, %s, %s\n" %
                                    (self.tablecmd[i], self.tablespeed[i][j],
                                     self.tableacc[i][j]))
            print("Finished writing results")


def main():
    """
    demo
    """
    if len(sys.argv) == 2:
        # Get the latest file
        file_path = sys.argv[1]
    else:
        file_path = tkinter.filedialog.askopenfilename(
            initialdir="/home/<USER>/.ros",
            filetypes=(("csv files", ".csv"), ("all files", "*.*")))
    print('File path: %s' % file_path)
    plotter = Plotter()
    plotter.process_data(file_path)
    print('Finished reading the file.')
    # plotter.plot_result()
    plotter.plot_xy()


if __name__ == '__main__':
    main()

import math
import matplotlib.pyplot as plt
import random
import numpy as np

class Tracking_Differentiator:
    def __init__(self,h,r,h0,x1k_0,x2k_0):
        self.h = h
        self.r = r
        self.h0 = h0
        self.x1k_0 = x1k_0
        self.x2k_0 = x2k_0
        print("Tracking-Differentiator")

    def reset_param(self,h,r,h0,x1k_0,x2k_0):
        self.h = h
        self.r = r
        self.h0 = h0
        self.x1k_0 = x1k_0
        self.x2k_0 = x2k_0
        print("Tracking-Differentiator Reset")

    def sgn(self,x):
        if x < 0:
            return -1
        elif x == 0:
            return 0
        else:
            return 1

    def fhan(self,x1,x2k):
        d = self.r * self.h0
        d0 = d * self.h0
        y = x1 + self.h * x2k
        a0 = math.sqrt(d*d + 8.0*self.r*math.fabs(y))
        a1 = 0.0
        if d0 >= math.fabs(y):
            a1 = x2k + y/self.h0
        elif d0 < math.fabs(y):
            a1 = x2k + self.sgn(y)*(a0-d)/2.0
        
        if d >= math.fabs(a1):
            return -self.r*a1/d
        elif d < math.fabs(a1):
            return -self.r*self.sgn(a1)

    def tracking_differentiator(self,vk):
        res = []
        x1k_1 = self.x1k_0 + self.h*self.x2k_0
        x2k_1 = self.x2k_0 + self.h*self.fhan(self.x1k_0-vk,self.x2k_0)
        self.x1k_0 = x1k_1
        self.x2k_0 = x2k_1
        res.append(x1k_1)
        res.append(x2k_1)
        return res

def gauss_noisy_xy(x, y):
    resx=[]
    resy=[]
    mu = 0
    sigma = 0.05
    for i in range(len(x)):
        resx.append(x[i] + random.gauss(mu, sigma))
        resy.append(y[i] + random.gauss(mu, sigma))
    res=[]
    res.append(resx)
    res.append(resy)
    return res


def gauss_noisy_x(x):
    res=[]
    mu = 0
    sigma = 0.1
    for i in range(len(x)):
        res.append(x[i] + random.gauss(mu, sigma))
    return res

# v_t = t*sin(t)  
# div_v_t = sin(t)+t*cos(t)
# y
# div_y
if __name__=="__main__":
    h = 0.05
    r = 150
    h0 = 0.2
    td = Tracking_Differentiator(h,r,h0,0,0)
    t = np.arange(0, 70, h)
    v_t = t*np.sin(t)+10.0
    div_v_t = np.sin(t) + t*np.cos(t)
    nosiy=gauss_noisy_x(v_t)
    # nosiy_xy=gauss_noisy_xy(t,v_t)
    fig = plt.figure()
    plt.plot(t,nosiy,'.')
    y = []
    div_y = []
    for i in range(t.size):
        res = td.tracking_differentiator(nosiy[i])
        y.append(res[0])
        div_y.append(res[1])

    fig = plt.figure()
    plt.plot(t,v_t,'.')
    plt.plot(t,y)
    fig = plt.figure()
    plt.plot(t,div_v_t,'.')
    plt.plot(t,div_y)
    plt.show()
    pass




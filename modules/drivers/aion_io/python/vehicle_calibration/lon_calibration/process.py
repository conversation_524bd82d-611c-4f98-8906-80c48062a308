#!/usr/bin/env python3

import math
import warnings

import numpy as np
import scipy.signal as signal
from tracking_differentiator import Tracking_Differentiator


warnings.simplefilter('ignore', np.RankWarning)

SPEED_INTERVAL = 0.2
SPEED_DELAY = 130  # Speed report delay relative to IMU information
Tracking_DELAY = 230


def preprocess(filename):
    data = np.genfromtxt(filename, delimiter=',', names=True)
    # data = data[np.where(data['io'] == 0)[0]]
    # data = data[np.argsort(data['time'])]
    data['time'] = data['time'] - data['time'][get_start_index(data)]

    b, a = signal.butter(6, 0.05, 'low')
    data['acc_x'] = signal.filtfilt(b, a, data['acc_x'])

    data['acc_x'] = np.append(data['acc_x'][-SPEED_DELAY // 10:],
                            data['acc_x'][0:-SPEED_DELAY // 10])

    return data

def preprocessalldata(filename):#数据预处理
    data = np.genfromtxt(filename, delimiter=',', names=True)
    # data = data[np.where(data['io'] == 0)[0]]
    # data = data[np.argsort(data['time'])]
    data['time'] = data['time'] - data['time'][0]

    for i in range(data['acc_x'].size):
        data['acc_x'][i] = data['acc_x'][i]
        data['speed'][i] = data['speed'][i]/3.6
        data['deal_c'][i] = data['acc_x'][i]

    b, a = signal.butter(6, 0.05, 'low')
    data['acc_x'] = signal.filtfilt(b, a, data['acc_x'])

    data['acc_x'] = np.append(data['acc_x'][-SPEED_DELAY // 10:],
                            data['acc_x'][0:-SPEED_DELAY // 10])
    data['acc_x'] = data['acc_x']

    h = 0.01#积分步长，这里为时间，采样周期
    r = 150#速度因子
    h0 = 0.2#滤波因子
    td_start = False
    td_set = False
    td = Tracking_Differentiator(h,r,h0,data['speed'][0],data['acc_x'][0])
    for i in range(data['speed'].size):
        if(data['speed'][i]>1.0/3.6):#过滤不可见的速度
            td_start = True
            if(False == td_set):
                td.reset_param(h,r,h0,data['speed'][i],data['acc_x'][i])
                td_set = True
        if(True == td_start):
            res = td.tracking_differentiator(data['speed'][i])
            data['deal_a'][i] = res[0]#speed
            data['deal_b'][i] = res[1]#acc
    
    data['deal_a'] = np.append(data['deal_a'][Tracking_DELAY // 10:],
                            data['deal_a'][0:Tracking_DELAY // 10])

    data['deal_b'] = np.append(data['deal_b'][Tracking_DELAY // 10:],
                            data['deal_b'][0:Tracking_DELAY // 10])
    return data


def get_start_index(data):
    if np.all(data['speed'] == 0):
        return 0

    ind = 0
    while True:
        if abs(data['speed'][ind]) < 0.01:
            ind += 1
        else:
            break
    return ind

#只取加速阶段
def process(data):
    """
    process data
    """
    np.set_printoptions(precision=3)#控制python小数点的精度

    if np.all(data['speed'] == 0):
        print("All Speed = 0")
        return [], [], [], [], [], []

    start_index = get_start_index(data)#起始位置为速度索引大于0.01的位置

    # print "Start index: ", start_index
    data = data[start_index:]
    data['time'] = data['time'] - data['time'][0]

    # transition = np.where(
    #     np.logical_or(
    #         np.diff(data['tar_torque']) != 0, np.diff(data['tar_deceleration']) != 0))[
    #             0]#找到发送加减速命令的位置
    # transition = np.insert(np.append(transition, len(data) - 1), 0, 0)
    # print "Transition indexes: ", transition

    speedsegments = []
    timesegments = []
    accsegments = []
    tablespeed = []
    tableacc = []
    tablecmd = []

# for i in range(len(data['time']) - 1):
    # print "process transition index:", data['time'][transition[i]], ":", data['time'][transition[i + 1]]
    speedsection = data['speed']
    timesection = data['time']
    brake = data['tar_deceleration']
    throttle = data['tar_torque']
    imusection = data['acc_x']
    # if brake == 0 and throttle == 0:
    #     continue
    # print "Brake CMD: ", brake, " Throttle CMD: ", throttle
    firstindex = 0

    while speedsection[firstindex] == 0:
        firstindex += 1
    firstindex = max(firstindex - 2, 0)
    speedsection = speedsection[firstindex:]
    timesection = timesection[firstindex:]
    imusection = imusection[firstindex:]
    throttle = throttle[firstindex:]

    # if speedsection[0] < speedsection[-1]:
    #     is_increase = True
    #     lastindex = np.argmax(speedsection)
    # else:
    #     is_increase = False
    #     lastindex = np.argmin(speedsection)

    # 这里的分段暂时只考虑加速度到扭矩的映射关系，完全相信车辆的减速度接口，
    # 后续可以根据实际的减速度响应情况实现判断是否需要减速度的标定
    is_increase = True
    lastindex = np.argmax(speedsection)#找到速度最大的位置

    speedsection = speedsection[0:lastindex + 1]
    timesection = timesection[0:lastindex + 1]
    imusection = imusection[0:lastindex + 1]

    speedmin = np.min(speedsection)
    speedmax = np.max(speedsection)
    speedrange = np.arange(
        max(0, round(speedmin / SPEED_INTERVAL) * SPEED_INTERVAL),
        min(speedmax, 28.0), SPEED_INTERVAL)#对速度table做等间距采样，以0.2为步长
    # print "Speed min, max", speedmin, speedmax, is_increase, firstindex, lastindex, speedsection[-1]
    accvalue = []
    speedvalue = []
    for value in speedrange:
        val_ind = 0
        if is_increase:
            while val_ind < len(
                    speedsection) - 1 and value > speedsection[val_ind]:
                val_ind += 1
        else:
            while val_ind < len(
                    speedsection) - 1 and value < speedsection[val_ind]:
                val_ind += 1
        if val_ind == 0:
            imu_value = imusection[val_ind]
        else:
            # slope = (imusection[val_ind] - imusection[val_ind - 1]) / (
            #     speedsection[val_ind] - speedsection[val_ind - 1])
            # imu_value = imusection[val_ind - 1] + slope * (
            #     value - speedsection[val_ind - 1])
            imu_value = imusection[val_ind]
        speedvalue.append(round(value,3))
        accvalue.append(round(imu_value,3))

    # if brake == 0:
    #     cmd = throttle
    # else:
    #     cmd = -brake
    # print "Overall CMD: ", cmd
    # print "Time: ", timesection
    # print "Speed: ", speedrange
    # print "Acc: ", accvalue
    # print cmd
    cmd = throttle[0]
    tablecmd.append(round(cmd,3))
    tablespeed.append(speedvalue)
    tableacc.append(accvalue)

    speedsegments.append(speedsection)
    accsegments.append(imusection)
    timesegments.append(timesection)

    return tablecmd, tablespeed, tableacc, speedsegments, accsegments, timesegments

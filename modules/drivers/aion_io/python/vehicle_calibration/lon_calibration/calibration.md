how to run calibration

整体标定过程：
1.运行data_collector.py采集数据，采集多组csv数据；
2.运行process_data.py处理数据，并选择csv文件，进行数据组合；
3.运行plot_results.py作图分析数据；
4.运行result2pb.py生成最终可用的标定文件。


data_collector.py
进行数据的收集，以及数据的绘制，进行人眼判定数据的合理性
q 退出数据收集脚本
p 对收集到的数据进行绘制（只取加速阶段的数据）
    1.对所有收集到的数据进行预处理，时间减去基点时间，速度加速度进行单位换算，x轴加速度进行滤波，利用微分跟踪器处理得到新的速度和加速度
    2.提取单次加速数据的有效数据（只取加速阶段），包含命令torque，速度，加速度。这里对速度数据进行了处理，采用0.2步长对速度最大最小值重新进行了排列，并提取对应的torque和加速度
    3.对2提取到的数据进行绘制
ps 对收集到的原始数据进行绘制
    1.对所有收集到的数据进行预处理，时间减去基点时间，速度加速度进行单位换算，x轴加速度进行滤波，利用微分跟踪器处理得到新的速度和加速度
    2.绘制曲线，速度、加速度、速度-处理速度、加速度-处理加速度、目标扭矩、原始加速度、（当前加速度）
x 删除对应的数据

t x x :接管，x=1接管，x=2退出接管
d f :减速控制， f=deacc
p x x :数据处理预准备，包含横向接管（必纵向接管）、保压、挂挡，需要先踩下刹车，p tar_gear lat_t，tar_gear（IDNRP 01234）lat_t(1接管 2退出接管)，一般用（ p 1 0），表示控制预处理，代表挂入D档，横向不接管。
c x :扭矩控制，并进行数据录制，必须先进行预准备，x=tar_torque(0~3800Nm)


plot_data.py
进行绘图，输入可以为单个csv文件，主要用作单个csv文件的作图分析。（输入为csv）

plot_grid.py
作图，后续根据录制的数据分析该文件的作用

plot_results.py 
作图，可以用于观察不同torque下的速度和加速度的曲线，判断关系。（输入为result.csv）



process_data.py
选择之前保存的csv文件，进行相关数据处理，并保存为其他格式
    1.对所有收集到的数据进行预处理，时间减去基点时间，速度加速度进行单位换算，x轴加速度进行滤波，利用微分跟踪器处理得到新的速度和加速度
    2.提取单次加速数据的有效数据（只取加速阶段），包含命令torque，速度，加速度
    3.对步骤2中得到的数据进行保存，保存格式result.csv\ filename.result
    note:     filename.result：将csv文件转换后的数据的转换格式
                    result.csv：包含所有filename.result文件


process.py
数据处理具体模块
    a.数据预处理模块，时间减去基点时间，速度加速度进行单位换算，x轴加速度进行滤波，利用微分跟踪器处理得到新的速度和加速度
    b.加速阶段数据的提取和处理，最终输出命令torque，速度，加速度

proto_utils.py
proto相关的处理

result2pb.py
将process_data.py保存的数据进行转换为pb格式（result.csv）
load_calibration_raw_data：提取文件中的数据，torque，速度和加速度；以速度为key，torque和acc为value制作速度表；对加速度取均值；最终转换为以速度为key，acc和torque为value的速度表
get_calibration_table_pb：将速度表转换成表定标


process_data_batch.py
进行数据的批处理，输入为csv文件，遍历并排序输入目录下所有的csv文件，然后输出csv.result文件


process_data_batch2.py
进行数据的批处理，输入为csv.result文件，遍历并排序输入目录下所有的csv.result文件，可选择的在一个范围内进行绘图，能够根据绘图结果进行数据的人工处理。



//德赛测试增加内容
修改：增加data_collector_and_control.py和data_collector_only.py文件
    data_collector_only.py：只进行数据的收集，其中很多内容可以删除，但是未经测试，先不删除
    data_collector_and_control.py：应该是给德赛进行控制以及数据收集的最终代码，可以单独运行，进行控车以及标定数据收
操作：
    启动imu
    cd ~/ros2cyber/imu_ros2cyber/
    bash run_imu
    启动之后回车，等待数据跳变。

    cd code/yunxiao/DRIVE_IO/adas-core/
    ./compile.sh -u zyx -co
    cd build
    export CYBER_IP=**********
    ./zlgcandbc_aion_io/zlgcandbc_aion_io_server        //控制以及数据转发模块

    cd code/yunxiao/DRIVE_IO/adas-core/
    ./compile.sh -u zyx -co
    cd build/python
    export CYBER_IP=**********
    python data_collector_and_control.py  //控车加记录数据


    cd code/yunxiao/DRIVE_IO/adas-core/
    ./compile.sh -u zyx -co
    cd build/python
    export CYBER_IP=**********
    python data_collector_only.py    //保存数据到test

    cd /home/<USER>/code/yunxiao/DRIVE_IO/adas-core/modules/drivers/aion_io/python/vehicle_calibration  //控制脚本说明路径

    cd /home/<USER>/code/yunxiao/DRIVE_IO/adas-core/build/python    //test路径


    pwd:123456
    export CYBER_IP=**********

    t x x :接管，x=1接管，x=2退出接管
    d f :减速控制， f=deacc
    p x x :数据处理预准备，包含横向接管（必纵向接管）、保压、挂挡，需要先踩下刹车，p tar_gear lat_t，tar_gear（IDNRP 01234）lat_t(1接管 2退出接管)，一般用（ p 1 0），表示控制预处理，代表挂入D档，横向不接管。
    c x :扭矩控制，并进行数据录制，必须先进行预准备，x=tar_torque(0~3800Nm)


#!/usr/bin/env python3

import math
import sys

import numpy as np
import tkinter.filedialog

from process import get_start_index
from process import preprocess ,preprocessalldata
from process import process


class Plotter(object):
    """
    plot the speed info
    """

    def __init__(self, filename):
        """
        init the speed info
        """

        np.set_printoptions(precision=3)
        self.file = open('result.csv', 'a')
        self.file_one = open(filename + ".result", 'w')

    def process_data(self, filename):
        """
        load the file and preprocess th data
        """

        # self.data = preprocess(filename)
        self.data = preprocessalldata(filename)

        self.tablecmd, self.tablespeed, self.tableacc, self.speedsection, self.accsection, self.timesection = process(
            self.data)

    def save_data(self):
        """
        save_data
        """
        for i in range(len(self.tablecmd)):
            for j in range(len(self.tablespeed[i])):
                self.file.write("%s, %s, %s\n" %
                                (self.tablecmd[i], self.tablespeed[i][j],
                                 self.tableacc[i][j]))
                self.file_one.write("%s, %s, %s\n" %
                                    (self.tablecmd[i], self.tablespeed[i][j],
                                     self.tableacc[i][j]))


def main():
    """
    demo
    """
    if len(sys.argv) == 2:
        # get the latest file
        file_path = sys.argv[1]
    else:
        file_path = tkinter.filedialog.askopenfilename(
            initialdir="/home/<USER>/.ros",
            filetypes=(("csv files", ".csv"), ("all files", "*.*")))
        # file_path = 'c_300_recorded.csv'
    plotter = Plotter(file_path)
    plotter.process_data(file_path)
    plotter.save_data()
    print('save result to:', file_path + ".result")


if __name__ == '__main__':
    main()

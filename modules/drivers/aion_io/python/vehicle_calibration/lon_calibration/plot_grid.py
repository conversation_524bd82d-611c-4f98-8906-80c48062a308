#!/usr/bin/env python3

import math
import sys

import tkinter.filedialog

import matplotlib.pyplot as plt
import numpy as np
from matplotlib import cm as cmx
from matplotlib import colors as mcolors

markers = [
    "o", "v", "^", "<", ">", "1", "2", "3", "4", "8", "s", "p", "*", "+", "x",
    "d", "|", "_"
]

# if len(sys.argv) < 2:
#     print('Usage: %s result.csv' % sys.argv[0])
#     sys.exit(0)

# fn = sys.argv[1]
fn = tkinter.filedialog.askopenfilename(
        initialdir="/workdir/adas-core",
        filetypes=(("csv files", ".csv"), ("all files", "*.*")))

speed_table = {}
with open(fn, 'r') as f:
    for line in f:
        items = line.split(',')
        cmd = round(float(items[0]))
        speed = float(items[1])
        acc = round(float(items[2]), 2)
        if speed in speed_table:
            cmd_table = speed_table[speed]
            if cmd in cmd_table:
                cmd_table[cmd].append(acc)
            else:
                cmd_table[cmd] = [acc]
        else:
            cmd_table = {}
            cmd_table[cmd] = [acc]
            speed_table[speed] = cmd_table

for speed in speed_table:
    cmd_dict = speed_table[speed]
    speed_list = []
    acc_list = []
    for cmd in cmd_dict:
        for acc in cmd_dict[cmd]:
            speed_list.append(speed)
            acc_list.append(acc)
    plt.plot(speed_list, acc_list, 'b.')
plt.show()

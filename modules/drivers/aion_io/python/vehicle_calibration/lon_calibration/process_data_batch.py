#!/usr/bin/env python3

"""
进行数据的批处理，没有进行任何处理
"""
import math
import sys

import numpy as np
import tkinter.filedialog

from process import get_start_index
from process import preprocess ,preprocessalldata
from process import process
import os
import re


class Plotter(object):
    """
    plot the speed info
    """

    def __init__(self, filename):
        """
        init the speed info
        """

        np.set_printoptions(precision=3)
        self.file = open('result.csv', 'a')
        self.file_one = open(filename + ".result", 'w')

    def process_data(self, filename):
        """
        load the file and preprocess th data
        """

        # self.data = preprocess(filename)
        self.data = preprocessalldata(filename)

        self.tablecmd, self.tablespeed, self.tableacc, self.speedsection, self.accsection, self.timesection = process(
            self.data)

    def save_data(self):
        """
        save_data
        """
        for i in range(len(self.tablecmd)):
            for j in range(len(self.tablespeed[i])):
                self.file.write("%s, %s, %s\n" %
                                (self.tablecmd[i], self.tablespeed[i][j],
                                 self.tableacc[i][j]))
                self.file_one.write("%s, %s, %s\n" %
                                    (self.tablecmd[i], self.tablespeed[i][j],
                                     self.tableacc[i][j]))


def traverse_dir(path):
    for file in os.listdir(path):
        file_path = os.path.join(path, file)
        if os.path.isdir(file_path):
            print("files_path: ", file_path)
            traverse_dir(file_path)
        else:
            print("files_path: ", file_path)

def main():
    """
    demo
    """

    files_path = "/home/<USER>/code/adas_core_aion/adas-core/modules/drivers/aion_io/python/vehicle_calibration/data_handle/batch_log"
    file_list = os.listdir(files_path)
    sorted_files = sorted(file_list, key=lambda x: int(re.findall(r'\d+', x)[0]))

    # 打印结果
    for file in sorted_files:
        file_path = files_path+'/'+file
        plotter = Plotter(file_path)
        plotter.process_data(file_path)
        plotter.save_data()
        print('save result to:', file_path + ".result")


if __name__ == '__main__':
    main()

clear;
% folder = './data';
% files = dir(folder)

% 定义文件夹路径 
folderPath = './data'; % 替换为你的文件夹路径 
% 获取文件夹中的所有文件 
files = dir(fullfile(folderPath, '*.*')); 
% 提取文件名和对应的数字 
fileNames_ori = {files.name};
fileNames = fileNames_ori(3:end);
fileNumbers = zeros(size(fileNames));
% 提取文件名中的数字 
for i = 1:length(fileNames) 
    % 提取数字部分（假设文件名中只包含一个数字） 
    fileName = fileNames{i}; 
    numStr = regexp(fileName, '[-+]?\d+', 'match', 'once'); 
    % 获取第一个匹配的数字 
    if ~isempty(numStr) 
        fileNumbers(i) = str2double(numStr); 
    end
end
% 根据数字对文件进行排序 
[~, sortedIndices] = sort(fileNumbers); 
% 创建一个排序后的文件列表 
sortedFiles = fileNames(sortedIndices);

data_R = [];
wheel_base = 2.92; %m
for i = 1:length(sortedFiles)
     
    filename = sortedFiles(i);
    disp(filename{1});
    data = readmatrix(filename{1});
    
    degNumStr = regexp(filename{1}, '[-+]?\d+', 'match', 'once');

    tar_steer_angle = data(:, 2);
    eps_steer_angle = data(:, 3);
    speed = data(:, 4);
    spd_location_x = data(:, 5);
    spd_location_y = data(:, 6);
    rtk_location_x = data(:, 7);
    rtk_location_y = data(:, 8);
    t = (0:0.01:(length(tar_steer_angle)-1)*0.01)';
    
    [rtk_location_x_res, rtk_location_y_res] = rtk_data_pre_handle(rtk_location_x', rtk_location_y',speed');
%     plot(rtk_location_x_res, rtk_location_y_res, '.');
    par = CircleFitByPratt([rtk_location_x_res',rtk_location_y_res']);
    
    eps_steer_angle_res = steer_angle_data_pre_handle(eps_steer_angle',speed');
    mean_eps_steer_angle = mean(eps_steer_angle_res);
    
    % 绘制拟合的圆
    theta = linspace(0, 2*pi, 100);
    x_circle = par(3) * cos(theta) + par(1);
    y_circle = par(3) * sin(theta) + par(2);
%     if(i == 40)
%         figure(i)
%         subplot(3,1,1);
%         plot(rtk_location_x_res, rtk_location_y_res, '-',x_circle, y_circle,'.');
%         title(degNumStr);
%         subplot(3,1,2);
%         plot(t,tar_steer_angle, t,eps_steer_angle);
%         subplot(3,1,3);
%         plot(t,speed);
%     end

    data_R(i,1) = tar_steer_angle(1);
    data_R(i,2) = mean_eps_steer_angle;
    if(tar_steer_angle(1) > 0)
        data_R(i,3) = par(3);
    else
        data_R(i,3) = -par(3);
    end
    data_R(i,4) = rad2deg(atan(wheel_base / data_R(i,3)));
    
end

figure(length(data_R)+1);
for i = 1:length(data_R)
    theta = linspace(0, 2*pi, 100);
    x_circle = data_R(i,3) * cos(theta);
    y_circle = data_R(i,3) * sin(theta);
    plot(x_circle, y_circle,'-');
    hold on;
end

figure(length(data_R)+2);
t1=(0:1:length(data_R)-1)';
plot(t1, data_R(:,4))

figure(length(data_R)+3);
plot(data_R(:,1), data_R(:,4))
hold on;
plot(data_R(:,2), data_R(:,4))

csvwrite('./csv_test.csv',data_R);

% close all;



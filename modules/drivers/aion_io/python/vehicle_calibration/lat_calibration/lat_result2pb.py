#!/usr/bin/env python3

import sys

import numpy as np
import tkinter.filedialog

import proto_utils as proto_utils
from modules.control.acc_control.proto import calibration_table_pb2

def load_calibration_raw_data(fn):
    table = {}
    with open(fn, 'r') as f:
        for line in f:
            items = line.split(',')
            steer_angle = round(float(items[1]), 2) #一段时间内的方向盘均值
            steer_tire_angle = round(float(items[3]), 2) #拟合圆的半径
            table[steer_angle] = steer_tire_angle
    return table


def get_calibration_table_pb(table):
    calibration_table_pb = calibration_table_pb2.LatControlCalibrationTable()
    steer_angles = list(table.keys())
    steer_angles.sort()
    for steer_angle in steer_angles:
        item = calibration_table_pb.calibration.add()
        item.steer_angle = round(steer_angle,2) 
        item.steer_wheel_angle = round(table[steer_angle],2) 
    return calibration_table_pb


if __name__ == '__main__':
    # if len(sys.argv) != 3:
    #     print("Usage: %s old_control_conf.pb.txt result.csv" % sys.argv[0])
    #     sys.exit(0)
    # fn = 'c_300_recorded.csv.result'
    fn = tkinter.filedialog.askopenfilename(
            initialdir="/workdir/adas-core",
            filetypes=(("csv files", ".csv"), ("all files", "*.*")))
    
    speed_table_dict = load_calibration_raw_data(fn)
    calibration_table_pb = get_calibration_table_pb(speed_table_dict)

    with open('lat_calibration_table.pb.txt', 'w') as f:
        f.write(str(calibration_table_pb))

function [data1_res, data2_res] = rtk_data_pre_handle(data1,data2,data3)
    data1_res=[];
    data2_res=[];
    idx=1;
    if(length(data1) ~= length(data2) || length(data1) ~= length(data3))
        disp('not equal, and return...');
        return;
    end
    data1_pre = data1(1);
    data2_pre = data2(1);
    for i=1:length(data1)
        if(abs(data3(i)) > 0.001 && data1(i) ~= 0 && data2(i) ~= 0)
            data1_res(idx) = data1(i);
            data2_res(idx) = data2(i);
            idx = idx + 1;
        end
    end
end
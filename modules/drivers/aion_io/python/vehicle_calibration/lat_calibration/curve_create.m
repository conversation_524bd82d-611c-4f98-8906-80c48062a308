
% time	tar_steer_angle	eps_steer_angle	speed	spd_location_x	spd_location_y	rtk_location_x	rtk_location_y
% readmatrix('y_-200_recorded.csv');
data = readmatrix('y_-400_recorded.csv');
tar_steer_angle = data(:, 2);
eps_steer_angle = data(:, 3);
t = (0:0.01:(length(tar_steer_angle)-1)*0.01)';
speed = data(:, 4);
spd_location_x = data(:, 5);
spd_location_y = data(:, 6);
rtk_location_x = data(:, 7);
rtk_location_y = data(:, 8);
% figure(1)
% plot(spd_location_x, spd_location_y, '.');
% hold on
figure(2)
[rtk_location_x_res, rtk_location_y_res] = data_pre_handle(rtk_location_x', rtk_location_y',speed');
plot(rtk_location_x_res, rtk_location_y_res, '.');

figure(3)
[spd_location_x_res, spd_location_y_res] = data_pre_handle(spd_location_x', spd_location_y',speed');
plot(spd_location_x_res, spd_location_y_res, '.');

% figure(4)
% plot(t,tar_steer_angle);
% hold on;
% plot(t,eps_steer_angle);
% legend('tar_steer_angle','eps_steer_angle')
% 
par = CircleFitByPratt([rtk_location_x_res',rtk_location_y_res'])

% 绘制拟合的圆
theta = linspace(0, 2*pi, 100);
x_circle = par(3) * cos(theta) + par(1);
y_circle = par(3) * sin(theta) + par(2);
figure(5)
plot(rtk_location_x_res, rtk_location_y_res);
hold on
plot(x_circle, y_circle);

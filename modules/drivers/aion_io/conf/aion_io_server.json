{"nodeName": "aion_io_server", "subChannels": {"io_in_acc_shake_hand_req": {"name": "io/in/acc_shake_hand_req", "cycleS": -1, "pendingQueueSize": 1}, "io_in_trq_chassisctl": {"name": "io/in/trq_chassisctl", "cycleS": 0.02, "pendingQueueSize": 1}, "decision_to_planning_update": {"name": "acc/decision/update_planning", "cycleS": 1, "pendingQueueSize": 3}, "decision_out_acc_state": {"name": "acc/decision/acc_state", "cycleS": -1, "pendingQueueSize": 1}, "decision_out_lcc_state": {"name": "acc/decision/lcc_state", "cycleS": -1, "pendingQueueSize": 1}, "aeb_aebcontroller": {"name": "aeb/aebcontroller", "cycleS": -1, "pendingQueueSize": 2}}, "pubChannels": {"io_wheelspeed": {"name": "channel/wheelspeed", "cycleS": 0.02, "pendingQueueSize": 1}, "io_chassisinfo": {"name": "aion_io/chassisinfo", "cycleS": 0.02, "pendingQueueSize": 1}, "l2_chassis": {"name": "channel/chassis", "cycleS": -1, "pendingQueueSize": 1}, "io_out_acc_shake_hand_ret": {"name": "io/out/acc_shake_hand_ret", "cycleS": -1, "pendingQueueSize": 1}}}
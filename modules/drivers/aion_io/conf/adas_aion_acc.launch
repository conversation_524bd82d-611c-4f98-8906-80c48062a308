<launch>
    <env CYBER_PATH="/usr/local/cyber_from_source/include/cyber" CYBER_IP="127.0.0.1" />
    <node pkg="bin/" type="zlgcandbc_aion_io_server" name="zlgcandbc_aion_io_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="avp_slam_server" name="" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="acc_planning_server" name="drive_planning_server" arg="" exception_handler="exit" />
    <node pkg="bin/" type="acc_control_server" name="drive_control_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="interactive_server" name="interactive_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="hwp_decision_server" name="hwp_decision_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="drive_predict_server" name="drive_predict_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="drive_radar_server" name="drive_radar_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="drive_fusion_server" name="drive_fusion_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="aeb_server" name="aeb_server" arg="" exception_handler="respawn" />
    <node pkg="bin/" type="uploader" name="uploader" arg="" exception_handler="respawn" />
</launch>

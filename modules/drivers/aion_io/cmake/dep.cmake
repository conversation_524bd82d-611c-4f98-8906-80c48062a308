set(ZLGCANDBC_AION_IO "modules/drivers/aion_io/cpp/")
if( ${BUILD_BST_COMPILE})
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")

    if (NOT CMAKE_BUILD_TYPE)
        set(CMAKE_BUILD_TYPE "Release")
    endif()

    add_compile_options("-O2")
    
    add_executable(bst_aion_io_server ${ZLGCANDBC_AION_IO}/aion_bst_io_server.cpp ${ZLGCANDBC_AION_IO}/bst_aion.cpp ${PROTO_CC_FILES})
    target_link_libraries(bst_aion_io_server can_hal can_hal_config_proto ads_log)
    target_link_libraries(bst_aion_io_server  Vector_DBC autoplt cyber cyber_proto fastrtps protobuf pthread )
    target_link_libraries(bst_aion_io_server  Boost::program_options  protobuf-lite atomic uuid  fastcdr  m z rt gflags	glog)
    target_compile_options(bst_aion_io_server PRIVATE -std=gnu++1z)
    add_custom_command(
        TARGET bst_aion_io_server
        POST_BUILD
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/bst_aion_io
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/bst_aion_io/config
        COMMAND mv bst_aion_io_server ${PROJECT_BINARY_DIR}/bst_aion_io
        COMMAND cp ${PROJECT_SOURCE_DIR}/script/run.sh ${PROJECT_BINARY_DIR}
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/conf/aion_io.ini ${PROJECT_BINARY_DIR}/bst_aion_io/config
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/conf/aion_io_server.json ${PROJECT_BINARY_DIR}/bst_aion_io/config

        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/bst_aion_io/dbc
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/dbc/Client_1210.dbc ${PROJECT_BINARY_DIR}/bst_aion_io/dbc

        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/aion_io_control_test.py ${PROJECT_BINARY_DIR}/python

        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/data_collector.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/plot_data.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/plot_grid.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/plot_results.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/process_data.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/process.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/tracking_differentiator.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/proto_utils.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/result2pb.py ${PROJECT_BINARY_DIR}/python
        )


else()
    add_subdirectory("thirdparty/zlgcandbc/")
    add_executable(zlgcandbc_aion_io_server ${ZLGCANDBC_AION_IO}/aion_io_server.cpp ${ZLGCANDBC_AION_IO}/zlgcandbc_aion.cpp ${PROTO_CC_FILES})
    target_link_libraries(zlgcandbc_aion_io_server zlgcandbc acc_control_lib pnc_math cyber Boost::program_options protobuf protobuf-lite atomic uuid fastrtps fastcdr pthread m z rt gflags	glog)
    target_compile_options(zlgcandbc_aion_io_server PRIVATE -std=gnu++1z)
    add_custom_command(
        TARGET zlgcandbc_aion_io_server
        POST_BUILD
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
        COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
        COMMAND mv zlgcandbc_aion_io_server ${PROJECT_BINARY_DIR}/output/bin
        COMMAND cp ${PROJECT_SOURCE_DIR}/script/run.sh ${PROJECT_BINARY_DIR}
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/conf/aion_io.ini ${PROJECT_BINARY_DIR}/output/conf
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/conf/aion_io_server.json ${PROJECT_BINARY_DIR}/output/conf
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/dbc/Client_1210.dbc ${PROJECT_BINARY_DIR}/output/conf
        COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_linux_depends.py ${PROJECT_BINARY_DIR}/output/bin/zlgcandbc_aion_io_server -o ${PROJECT_BINARY_DIR}/output/lib

        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/aion_io_control_test.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/conf/adas_aion_acc.launch ${PROJECT_BINARY_DIR}/output
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/conf/adas_aion_io.launch ${PROJECT_BINARY_DIR}/output
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/data_collector.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/data_collector_and_control.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/plot_data.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/plot_grid.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/plot_results.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/process_data.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/process.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/tracking_differentiator.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/proto_utils.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lon_calibration/result2pb.py ${PROJECT_BINARY_DIR}/python
        COMMAND cp ${PROJECT_SOURCE_DIR}/modules/drivers/aion_io/python/vehicle_calibration/lat_calibration/lat_result2pb.py ${PROJECT_BINARY_DIR}/python
        )
endif()
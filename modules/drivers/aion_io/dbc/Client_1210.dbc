VERSION ""


NS_ : 
 NS_DESC_
 CM_
 BA_DEF_
 BA_
 VAL_
 CAT_DEF_
 CAT_
 FILTER
 BA_DEF_DEF_
 EV_DATA_
 ENVVAR_DATA_
 SGTYPE_
 SGTYPE_VAL_
 BA_DEF_SGTYPE_
 BA_SGTYPE_
 SIG_TYPE_REF_
 VAL_TABLE_
 SIG_GROUP_
 SIG_VALTYPE_
 SIGTYPE_VALTYPE_
 BO_TX_BU_
 BA_DEF_REL_
 BA_REL_
 BA_DEF_DEF_REL_
 BU_SG_REL_
 BU_EV_REL_
 BU_BO_REL_
 SG_MUL_VAL_

BS_:


BU_: CHASSIS_PCAN_STATUS VCU_PCAN_STATUS MCU_control EPS CGW BCU ASDM ACM ADC SCM VCU MP5 AVAS


BO_ 75 AcmBody: 8 MCU_control
 SG_ ACM_CRC : 7|8@0+ (1,0) [0|255] "0" CGW
 SG_ ACM_MsgCntr : 15|4@0+ (1,0) [0|15] "0" CGW
 SG_ LatitudeAcc : 23|16@0+ (0.001,-2) [-2|2] "g" AVAS
 SG_ YawRate : 39|16@0+ (0.01,-180) [-180|180] "Deg/sec" CGW
 SG_ LongitudeAcc : 55|16@0+ (0.001,-2) [-2|2] "g" AVAS


BO_ 110 ADC_AccLongReq: 8 MCU_control
 SG_ ADC_CRC : 7|8@0+ (1,0) [0|255] "" CGW
 SG_ Acc_Mode : 11|3@0+ (1,0) [0|7] "" CGW
 SG_ Acc_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW
 SG_ Acc_Decel_Req : 18|11@0+ (0.005,-7.22) [-7.22|3.015] "m/s2" CGW
 SG_ Acc_Decel_ReqSts : 19|1@0+ (1,0) [0|1] "" CGW
 SG_ ADC_LngCtrlReq : 22|1@0+ (1,0) [0|1] "" AVAS
 SG_ ADC_VehTrqReq : 37|14@0+ (1,-5000) [-5000|5000] "Nm" AVAS
 SG_ ADC_GearReq : 58|3@0+ (1,0) [0|8] "" AVAS
 SG_ ADC_GearReqVD : 59|1@0+ (1,0) [0|0] "" AVAS
 SG_ ADC_IncresePressureReq : 63|1@0+ (1,0) [0|0] "" AVAS


BO_ 74 ADC_EPSReq: 8 MCU_control
 SG_ ADC_CRC : 7|8@0+ (1,0) [0|255] "" EPS,CGW
 SG_ ADC_EpsSpdDown : 11|2@0+ (1,0) [0|3] "" AVAS
 SG_ ADC_MsgCntr : 15|4@0+ (1,0) [0|15] "" EPS,CGW
 SG_ ADC_SteerTorqReq : 19|10@0+ (0.02,-10.24) [-10.24|10.22] "Nm" EPS,CGW
 SG_ ADC_LatCtrlReq : 23|3@0+ (1,0) [0|7] "" EPS
 SG_ ADC_SteerAngReq : 55|15@0+ (0.1,-1638.4) [-1638.4|1638.3] "Deg" EPS,CGW


BO_ 109 AebReq: 7 MCU_control
 SG_ AEB_CRC : 7|8@0+ (1,0) [0|255] "" CGW,BCU
 SG_ AEB_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW,BCU
 SG_ AEBDecelReq : 22|1@0+ (1,0) [0|1] "" CGW,BCU
 SG_ AEBTarDecel : 35|16@0+ (0.0004882,-16) [-16|15.99] "m/s2" CGW,BCU


BO_ 157 BodyStatus: 8 MCU_control
 SG_ BCM_CLOSURE_CRC : 7|8@0+ (1,0) [0|255] "" CGW
 SG_ BCM_CLOSURE_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW
 SG_ DoorAjarFrntRiSts : 19|2@0+ (1,0) [0|3] "" CGW
 SG_ DoorAjarReRiSts : 23|2@0+ (1,0) [0|3] "" CGW
 SG_ DoorAjarFrntLeSts : 25|2@0+ (1,0) [0|3] "" CGW
 SG_ DoorAjarReLeSts : 31|2@0+ (1,0) [0|3] "" CGW
 SG_ VehSpd : 55|13@0+ (0.05625,0) [0|360] "km/h" CGW,ACM,ADC,SCM


BO_ 88 BrkSymSts: 7 MCU_control
 SG_ CDDSAvl : 24|1@0+ (1,0) [0|1] "" CGW,ADC
 SG_ CDDSActv : 26|1@0+ (1,0) [0|1] "" CGW,ADC


BO_ 614 CAM_1: 8 CHASSIS_PCAN_STATUS
 SG_ CAM_TSR_HitTarVerDis : 7|8@0+ (0.1,0) [0|25.4] "m" AVAS
 SG_ CAM_TSR_HitTarLatDis2OLM : 15|8@0+ (0.1,0) [0|25.4] "m" AVAS
 SG_ CAM_TSR_HitTarLatDis : 17|10@0+ (0.1,-51.2) [-25.4|25.4] "m" AVAS
 SG_ CAM_TSR_VisualSpdLimitHitTar : 23|5@0+ (1,0) [0|31] "km/h" AVAS
 SG_ CAM_TSR_VisualSpdLimitNotHit2 : 34|5@0+ (1,0) [0|31] "km/h" AVAS
 SG_ CAM_TSR_VisualSpdLimitNotHit1 : 39|5@0+ (1,0) [0|31] "km/h" AVAS
 SG_ CAM_TSR_OvSpdlimType : 45|4@0+ (1,0) [0|15] "-" AVAS


BO_ 630 CAM_2: 8 CHASSIS_PCAN_STATUS
 SG_ CAM_SystemType : 1|2@0+ (1,0) [0|3] "-" AVAS
 SG_ CAM_SysFailureFlag : 3|2@0+ (1,0) [0|3] "-" AVAS
 SG_ CAM_WarnTone : 7|4@0+ (1,0) [0|15] "-" AVAS
 SG_ CAM_ButtonPress : 8|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_SideZoneStatusF : 13|2@0+ (1,0) [0|3] "" AVAS
 SG_ CAM_SideZoneStatusR : 15|2@0+ (1,0) [0|3] "" AVAS
 SG_ CAM_RRMidDistance : 23|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_RLMidDistance : 31|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_RRDistance : 39|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_RLDistance : 47|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_RSLSideDistance : 55|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_RSRSideDistance : 63|8@0+ (1,0) [0|255] "cm" AVAS


BO_ 646 CAM_3: 8 CHASSIS_PCAN_STATUS
 SG_ CAM_FRDistance : 7|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_FLDistance : 15|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_FRMidDistance : 23|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_FLMidDistance : 31|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_FSRSideDistance : 39|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_FSLSideDistance : 47|8@0+ (1,0) [0|255] "cm" AVAS
 SG_ CAM_RRMidSnsErrFlag : 48|1@0+ (1,0) [0|1] "-" AVAS
 SG_ CAM_RLMidSnsErrFlag : 49|1@0+ (1,0) [0|1] "-" AVAS
 SG_ CAM_RRSnsErrFlag : 50|1@0+ (1,0) [0|1] "-" AVAS
 SG_ CAM_RLSnsErrFlag : 51|1@0+ (1,0) [0|1] "-" AVAS
 SG_ CAM_RSLSnsErrFlag : 52|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_RSRSnsErrFlag : 53|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_FRSnsErrFlag : 54|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_FLSnsErrFlag : 55|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_FRMidSnsErrFlag : 56|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_FLMidSnsErrFlag : 57|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_FSLSnsErrFlag : 58|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_FSRSnsErrFlag : 59|1@0+ (1,0) [0|1] "" AVAS
 SG_ CAM_CtrlModulerrFlag : 60|1@0+ (1,0) [0|1] "-" AVAS
 SG_ CAM_BattVoltErrFlag : 61|1@0+ (1,0) [0|1] "-" AVAS


BO_ 95 DriverStatus: 8 MCU_control
 SG_ VCUAccrPedlOvrd : 29|1@0+ (1,0) [0|1] "" CGW,BCU


BO_ 137 EPB_02: 8 CHASSIS_PCAN_STATUS
 SG_ EPB_SwitchSt : 1|2@0+ (1,0) [0|3] "" CGW
 SG_ BrkPedlSts : 3|2@0+ (1,0) [0|3] "" CGW
 SG_ EPB_FailSt : 12|1@0+ (1,0) [0|1] "" AVAS
 SG_ Vehspd : 22|13@0+ (1,0) [0|360] "km/h" CGW
 SG_ Vehspdsts : 23|1@0+ (1,0) [0|1] "" CGW
 SG_ BrkPedPst : 39|8@0+ (0.392,0) [0|99.96] "%" CGW
 SG_ BrkPedPstVD : 40|1@0+ (1,0) [0|1] "" AVAS
 SG_ Brk_Msgcntr : 61|4@0+ (1,0) [0|16] "" CGW


BO_ 65 EpsStatus02: 8 CHASSIS_PCAN_STATUS
 SG_ EPS_02_CRC : 7|8@0+ (1,0) [0|255] "" CGW,ADC
 SG_ EPS_PinionAgValid : 9|2@0+ (1,0) [0|3] "" CGW,ADC
 SG_ EPS_02_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW,ADC
 SG_ EPS_PinionAg : 23|15@0+ (0.1,-1638.4) [-1638.4|1638.2] "Deg" CGW,ADC
 SG_ EPS_MotorTq : 55|10@0+ (0.02,-10.24) [-10.24|10.22] "Nm" CGW,ADC
 SG_ EPS_MotorTqValid : 59|2@0+ (1,0) [0|3] "" CGW


BO_ 73 EPSSts: 8 CHASSIS_PCAN_STATUS
 SG_ EPS_D_CRC : 7|8@0+ (1,0) [0|255] "" CGW
 SG_ EPS_Steer_angleandspdvalid : 9|1@0+ (1,0) [0|1] "" CGW
 SG_ EPS_Steer_angledir : 10|1@0+ (1,0) [0|1] "" CGW
 SG_ EPS_Steeragspddir : 11|1@0+ (1,0) [0|1] "" CGW
 SG_ EPS_D_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW
 SG_ EPS_SteeringAngle : 23|13@0+ (0.1,0) [0|800] "Deg" CGW
 SG_ EPS_FaultSts : 24|1@0+ (1,0) [0|1] "" AVAS
 SG_ EPS_ToqCtrlAbortFeedback : 36|4@0+ (1,0) [0|15] "" CGW
 SG_ EPS_LatCtrlMode : 39|3@0+ (1,0) [0|7] "" CGW
 SG_ EPS_SteeringAngleSpd : 52|9@0+ (5,0) [0|2550] "Deg/sec" CGW
 SG_ EPS_AngCtrlAbortFeedback : 59|4@0+ (1,0) [0|15] "" AVAS


BO_ 674 IC_1: 8 CHASSIS_PCAN_STATUS
 SG_ IC_TSR_SLCAheadDis : 3|4@0+ (1,0) [0|12] "-" AVAS
 SG_ IC_SysCalibrationSt : 5|2@0+ (1,0) [0|3] "" AVAS
 SG_ IC_CameraBlockageSt : 6|1@0+ (1,0) [0|1] "" EPS
 SG_ IC_LateralCtrlQuitWarn : 7|1@0+ (1,0) [0|1] "" AVAS
 SG_ IC_TSR_ISDRestOfDis : 11|4@0+ (1,0) [0|12] "-" AVAS
 SG_ IC_TSR_SpdLimType : 13|2@0+ (1,0) [0|3] "-" AVAS
 SG_ IC_CruiseMode : 15|2@0+ (1,0) [0|3] "" AVAS
 SG_ IC_1_MsgCounter : 19|4@0+ (1,0) [0|15] "" AVAS
 SG_ IC_TSR_AudioWarnEnable : 20|1@0+ (1,0) [0|1] "-" AVAS
 SG_ ADAS_SVMLaneMarkingReq : 21|1@0+ (1,0) [0|1] "-" BCU
 SG_ IC_LKS_LaneAssistType : 23|2@0+ (1,0) [0|3] "" EPS
 SG_ IC_LKS_St : 26|3@0+ (1,0) [0|7] "" EPS
 SG_ IC_LeftTrackingSt : 28|2@0+ (1,0) [0|3] "" EPS
 SG_ IC_RightTrackingSt : 30|2@0+ (1,0) [0|3] "" EPS
 SG_ IC_LKS_TakeoverReq : 31|1@0+ (1,0) [0|1] "" EPS
 SG_ IC_HWAHF_HandsOnIcon : 34|3@0+ (1,0) [0|7] "" AVAS
 SG_ IC_TSR_SpdLimit : 39|5@0+ (1,0) [0|31] "" EPS
 SG_ IC_TSR_SpdLimitUnit : 41|2@0+ (1,0) [0|3] "" AVAS
 SG_ IC_TSR_OperatingSt : 44|3@0+ (1,0) [0|7] "" EPS
 SG_ IC_HMA_St : 47|3@0+ (1,0) [0|7] "" EPS
 SG_ IC_HMA_HighBeamReq : 48|1@0+ (1,0) [0|1] "" EPS
 SG_ IC_TSR_Enable : 49|1@0+ (1,0) [0|1] "" EPS
 SG_ IC_HMA_Enable : 50|1@0+ (1,0) [0|1] "" EPS
 SG_ IC_TSR_VisualWarn : 51|1@0+ (1,0) [0|1] "-" AVAS
 SG_ IC_TSR_AcousticWarn : 52|1@0+ (1,0) [0|1] "-" AVAS
 SG_ IC_TJA_ICA_St : 55|3@0+ (1,0) [0|7] "" AVAS
 SG_ IC_1_DrvReq : 63|5@0+ (1,0) [0|31] "" AVAS


BO_ 678 IC_2: 8 CHASSIS_PCAN_STATUS
 SG_ MRR_ACCMode : 2|3@0+ (1,0) [0|7] "" VCU_PCAN_STATUS,EPS
 SG_ MRR_ACCTextInfo : 7|5@0+ (1,0) [0|31] "" VCU_PCAN_STATUS
 SG_ MRR_ACCFaultSt : 9|2@0+ (1,0) [0|3] "" AVAS
 SG_ MRR_ACCActiveSt : 10|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_ACCGoNotifier : 11|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_ACCFuncCancel : 13|2@0+ (1,0) [0|3] "" AVAS
 SG_ MRR_ACCTakeOverReq : 14|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_ObjectCapturedSt : 15|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_TargetSpd : 16|9@0+ (0.5,0) [0|255.5] "km/h" AVAS
 SG_ MRR_TargetDistanceSt : 19|3@0+ (1,0) [0|7] "" AVAS
 SG_ MRR_TauGapSet : 22|3@0+ (1,0) [0|7] "" AVAS
 SG_ ADAS_ShifttoParkGearReq : 23|1@0+ (1,0) [0|1] "" VCU_PCAN_STATUS
 SG_ MRR_FCWOffSt : 32|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_FCWFaultSt : 33|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_FCWInterventionSt : 34|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_FCWPreWarn : 36|2@0+ (1,0) [0|3] "" AVAS
 SG_ MRR_FCWLatentWarn : 38|2@0+ (1,0) [0|3] "" AVAS
 SG_ MRR_AEB_PEDPFaultSt : 39|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_AEBFaultSt : 40|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_AEBOffSt : 41|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_AEBInterventionSt : 42|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_FCWWarmDistance : 44|2@0+ (1,0) [0|3] "" AVAS
 SG_ MRR_ISLACC_St : 45|1@0+ (1,0) [0|1] "" AVAS
 SG_ MRR_ACCQuitSound : 46|1@0+ (1,0) [0|1] "" AVAS
 SG_ IC_MRR_1_MsgCounter : 51|4@0+ (1,0) [0|15] "" VCU_PCAN_STATUS
 SG_ MRR_PEBSObjType : 55|4@0+ (1,0) [0|15] "" AVAS


BO_ 126 LLC_Req: 8 MCU_control
 SG_ LLC_CRC : 7|8@0+ (1,0) [0|255] "" AVAS
 SG_ LLC_MsgCntr : 15|4@0+ (1,0) [0|15] "" AVAS
 SG_ ADC_APAGearLvlReq : 17|2@0+ (1,0) [0|7] "" AVAS
 SG_ ADC_APAGearLvlReqVD : 19|1@0+ (1,0) [0|1] "" AVAS
 SG_ ADC_APAStopDist : 35|12@0+ (1,0) [0|256] "cm" AVAS
 SG_ ADC_APASpdLimit : 63|8@0+ (1,0) [0|32] "km/h" AVAS


BO_ 259 SCM_Req: 8 MCU_control
 SG_ SCM_BrkLiReq : 1|2@0+ (1,0) [0|0] "" AVAS
 SG_ SCM_WiperReq : 5|2@0+ (1,0) [0|0] "" AVAS
 SG_ SCM_TurnLeftLightReq : 8|1@0+ (1,0) [0|0] "" AVAS
 SG_ SCM_TurnRightLightReq : 10|1@0+ (1,0) [0|0] "" AVAS
 SG_ SCM_EmeglampReq : 25|2@0+ (1,0) [0|0] "" AVAS
 SG_ SCM_HornrReq : 28|1@0+ (1,0) [0|0] "" AVAS


BO_ 291 SCMsts: 8 MCU_control
 SG_ SCM_CRC : 7|8@0+ (1,0) [0|255] "" BCU,ADC
 SG_ AccTimeGapIncSwtSts : 10|2@0+ (1,0) [0|3] "" ADC
 SG_ SCM_MsgCntr : 15|4@0+ (1,0) [0|15] "" BCU,ADC
 SG_ ModeTogDecSwtSts : 25|2@0+ (1,0) [0|3] "" ADC
 SG_ AccSetSwtSts : 28|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ MenuPushSwtSts : 31|2@0+ (1,0) [0|3] "" CGW
 SG_ AccSpdDecSwtSts : 34|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ FogLiPushSwtSts : 37|2@0+ (1,0) [0|3] "" BCU
 SG_ HiLowBeamPushSwtSts : 39|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ SCMFailSts : 41|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ ModeTogIncSwtSts : 44|2@0+ (1,0) [0|3] "" ADC
 SG_ TurnIndcrSwtSts : 47|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ MenuRiPushSwtSts : 49|2@0+ (1,0) [0|3] "" CGW
 SG_ MenuOrVolUpPushSwtSts : 51|2@0+ (1,0) [0|3] "" CGW
 SG_ AccTimeGapDecSwtSts : 53|2@0+ (1,0) [0|3] "" ADC
 SG_ MenuOrVolDwnPushSwtSts : 55|2@0+ (1,0) [0|3] "" CGW
 SG_ SWCFailSts : 57|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ AccSpdIncOrResuSwtSts : 59|2@0+ (1,0) [0|3] "" BCU,ADC
 SG_ MenuLePushSwtSts : 61|2@0+ (1,0) [0|3] "" CGW
 SG_ CCPushSwtSts : 63|2@0+ (1,0) [0|3] "" CGW


BO_ 150 VCU_13: 8 VCU_PCAN_STATUS
 SG_ VCU_13_CRC : 7|8@0+ (1,0) [0|255] "" CGW
 SG_ VCU_13_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW
 SG_ Accpedacttravelvalid : 31|1@0+ (1,0) [0|1] "" CGW
 SG_ VCU_ACCPedalAcPst : 39|8@0+ (0.392,0) [0|99.96] "" CGW
 SG_ VCU_CrntGearLvl : 42|3@0+ (1,0) [0|7] "" AVAS
 SG_ VCU_ActVehWheelTorq : 53|14@0+ (1,0) [0|10000] "" AVAS


BO_ 566 VehicleLights: 4 BCU
 SG_ BCM_FrontFogLampSt : 2|2@0+ (1,0) [0|3] "" CGW
 SG_ BCM_RearFogLampSt : 4|2@0+ (1,0) [0|3] "" AVAS
 SG_ FrntwiperSts : 7|3@0+ (1,0) [0|7] "" CGW
 SG_ BCM_TurnLampSt : 9|2@0+ (1,0) [0|3] "" CGW
 SG_ BCM_BeamSt : 19|2@0+ (1,0) [0|3] "" CGW
 SG_ BCM_BrkLightSts : 25|2@0+ (1,0) [0|3] "" AVAS


BO_ 81 WHLspdFront: 8 CHASSIS_PCAN_STATUS
 SG_ WF_CRC : 7|8@0+ (1,0) [0|255] "" CGW
 SG_ WF_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW
 SG_ WhlspdFLdir : 18|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdFLsts : 23|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdFR : 31|13@0+ (0.05625,0) [0|360] "km/h" CGW
 SG_ WhlspdFRdir : 42|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdFRsts : 47|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdFL : 55|13@0+ (0.05625,0) [0|360] "km/h" CGW


BO_ 82 WHLspdRear: 8 CHASSIS_PCAN_STATUS
 SG_ WR_CRC : 7|8@0+ (1,0) [0|15] "" CGW
 SG_ WR_MsgCntr : 15|4@0+ (1,0) [0|15] "" CGW
 SG_ WhlspdRLdir : 18|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdRLsts : 23|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdRR : 31|13@0+ (0.05625,0) [0|360] "km/h" CGW
 SG_ WhlspdRRdir : 42|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdRRsts : 47|2@0+ (1,0) [0|3] "" CGW
 SG_ WhlspdRL : 55|13@0+ (0.05625,0) [0|360] "km/h" CGW


CM_ SG_ 110 Acc_Decel_ReqSts "unstable Decel";
CM_ SG_ 74 ADC_EpsSpdDown "if request angle request , cheat  eps spd";
CM_ SG_ 88 CDDSAvl "Controlled Deceleration for DAS (Driver Assistant System) available     ";
CM_ SG_ 88 CDDSActv "Controlled Deceleration for DAS (Driver Assistant System) active   ";
CM_ SG_ 614 CAM_TSR_HitTarVerDis "vertical distance between the center of the speed limit sign and the center point of the rear axle of ego vehicle";
CM_ SG_ 614 CAM_TSR_HitTarLatDis2OLM "lateral distance between the center of the speed limit sign and the most outside lane marking detected by CAM";
CM_ SG_ 614 CAM_TSR_HitTarLatDis "lateral distance between the center of the speed limit sign and the middle of ego vehicle";
CM_ SG_ 614 CAM_TSR_VisualSpdLimitHitTar "the value of speed limit sign detected by CAM, which is already jduge as belong to current road by CAM.";
CM_ SG_ 614 CAM_TSR_VisualSpdLimitNotHit2 "the value of speed limit sign detected by CAM, which is already jduge as NOT belong to current road by CAM.";
CM_ SG_ 614 CAM_TSR_VisualSpdLimitNotHit1 "the value of speed limit sign detected by CAM, which is already jduge as NOT belong to current road by CAM.";
CM_ SG_ 614 CAM_TSR_OvSpdlimType "the type of speed limit type detected by IFC";
CM_ SG_ 630 CAM_SystemType "system type";
CM_ SG_ 630 CAM_SysFailureFlag "CAM 's subfunctionsystem failure flagCAM 's subfunctionsystem failure flag";
CM_ SG_ 630 CAM_WarnTone "Warning toneWarning tone";
CM_ SG_ 630 CAM_ButtonPress "RPA button press status";
CM_ SG_ 630 CAM_SideZoneStatusF "Front side zone status";
CM_ SG_ 630 CAM_SideZoneStatusR "Rear side zone status";
CM_ SG_ 630 CAM_RRMidDistance "Distance from rear right middle sensorDistance from rear right middle sensor";
CM_ SG_ 630 CAM_RLMidDistance "Distance from rear left middle sensorDistance from rear left middle sensor";
CM_ SG_ 630 CAM_RRDistance "Distance from rear right  sensorDistance from rear right  sensor";
CM_ SG_ 630 CAM_RLDistance "Distance from rear left sensorDistance from rear left sensor";
CM_ SG_ 630 CAM_RSLSideDistance "Distance from rear left side sensorDistance from rear left side sensor";
CM_ SG_ 630 CAM_RSRSideDistance "Distance from rear right side sensorDistance from rear right side sensor";
CM_ SG_ 646 CAM_FRDistance "Distance from front right sensorDistance from front right sensor";
CM_ SG_ 646 CAM_FLDistance "Distance from front left sensorDistance from front left sensor";
CM_ SG_ 646 CAM_FRMidDistance "Distance from front right middle sensorDistance from front right middle sensor";
CM_ SG_ 646 CAM_FLMidDistance "Distance from front left middle sensorDistance from front left middle sensor";
CM_ SG_ 646 CAM_FSRSideDistance "Distance from front right side sensorDistance from front right side sensor";
CM_ SG_ 646 CAM_FSLSideDistance "Distance from front left side sensorDistance from front left side sensor";
CM_ SG_ 646 CAM_RRMidSnsErrFlag "Error flag of rear right middle sensor";
CM_ SG_ 646 CAM_RLMidSnsErrFlag "Error flag of rear leftt middle sensor";
CM_ SG_ 646 CAM_RRSnsErrFlag "Error flag of rear right sensor";
CM_ SG_ 646 CAM_RLSnsErrFlag "Error flag of rear left sensor";
CM_ SG_ 646 CAM_RSLSnsErrFlag "Error flag of rear left side sensor";
CM_ SG_ 646 CAM_RSRSnsErrFlag "Error flag of rear right side sensor";
CM_ SG_ 646 CAM_FRSnsErrFlag "Error flag of front right sensor";
CM_ SG_ 646 CAM_FLSnsErrFlag "Error flag of front left sensor";
CM_ SG_ 646 CAM_FRMidSnsErrFlag "Error flag of front right middle sensor";
CM_ SG_ 646 CAM_FLMidSnsErrFlag "Error flag of front left middle sensor";
CM_ SG_ 646 CAM_FSLSnsErrFlag "Error flag of front left side sensor";
CM_ SG_ 646 CAM_FSRSnsErrFlag "Error flag of front right side sensor";
CM_ SG_ 646 CAM_CtrlModulerrFlag "Error flag of EEPROM.";
CM_ SG_ 646 CAM_BattVoltErrFlag "Error flag of IGN voltage.";
CM_ SG_ 674 IC_TSR_SLCAheadDis "Speed Limit Camera Ahead distance ahead(percentage)";
CM_ SG_ 674 IC_SysCalibrationSt "IFC(Intelligent Front Camera) system calibration status";
CM_ SG_ 674 IC_CameraBlockageSt "IFC(Intelligent Front Camera) camera blockage status";
CM_ SG_ 674 IC_LateralCtrlQuitWarn "Lateral control quit warning, which would be used to let the driver pay attention to the situation that lateral control is quit while the longitudinal contrl may still working.";
CM_ SG_ 674 IC_TSR_ISDRestOfDis "the distance(percentage) has passed durning currnt interval speed dectection.";
CM_ SG_ 674 IC_TSR_SpdLimType "TSR specific speed limit type";
CM_ SG_ 674 IC_CruiseMode "Cruise Mode";
CM_ SG_ 674 IC_1_MsgCounter "Check the message counter consistency at least on every message reception";
CM_ SG_ 674 IC_TSR_AudioWarnEnable "TSR(Traffic Sign Recognition) audio warning on off status";
CM_ SG_ 674 ADAS_SVMLaneMarkingReq "ADAS request SVM(Suround View Module) to send out the lane marking detection information.";
CM_ SG_ 674 IC_LKS_LaneAssistType "operation mode of IFC(Intelligent Front Camera)";
CM_ SG_ 674 IC_LKS_St "LKS state information";
CM_ SG_ 674 IC_LeftTrackingSt "Left line tracking/intervention/warning status";
CM_ SG_ 674 IC_RightTrackingSt "Right line tracking/intervention/warning status";
CM_ SG_ 674 IC_LKS_TakeoverReq "Take over request to driver";
CM_ SG_ 674 IC_HWAHF_HandsOnIcon "TJA/ICA and HWA HandsOff function request driver hands on icon";
CM_ SG_ 674 IC_TSR_SpdLimit "TSR(Traffic Sign Recognition)Speed limit values";
CM_ SG_ 674 IC_TSR_SpdLimitUnit "TSR(Traffic Sign Recognition) speed limit unit";
CM_ SG_ 674 IC_TSR_OperatingSt "Operation status of traffic sign functions";
CM_ SG_ 674 IC_HMA_St "HMA (High Beam Assistant)status";
CM_ SG_ 674 IC_HMA_HighBeamReq "HMA (High Beam Assistant)high beam request";
CM_ SG_ 674 IC_TSR_Enable "TSR(Traffic Sign Recognition) enable status";
CM_ SG_ 674 IC_HMA_Enable "HMA (High Beam Assistant)enable status";
CM_ SG_ 674 IC_TSR_VisualWarn "TSR(Traffic Sign Recognition) visual warning";
CM_ SG_ 674 IC_TSR_AcousticWarn "TSR(Traffic Sign Recognition) acoustic  warning";
CM_ SG_ 674 IC_TJA_ICA_St "TJA/ICA��Traffic Jam Assist/Integrated Cruise Assist�� Status";
CM_ SG_ 674 IC_1_DrvReq "Driver Operation Remind";
CM_ SG_ 678 MRR_ACCMode "This signal indicates ACC(Adaptive cruise control )status";
CM_ SG_ 678 MRR_ACCTextInfo "ACC text infor for driver";
CM_ SG_ 678 MRR_ACCFaultSt "Error indication of the ACC(Adaptive Cruise Control) system";
CM_ SG_ 678 MRR_ACCActiveSt "This signal indicates if the ACC(Adaptive Cruise Control)) is actived";
CM_ SG_ 678 MRR_ACCGoNotifier "ACC(Adaptive Cruise Control) go notifier signal for indication";
CM_ SG_ 678 MRR_ACCFuncCancel "Alert signal to inform of ACC(Adaptive Cruise Control) function cancel";
CM_ SG_ 678 MRR_ACCTakeOverReq "ACC(Adaptive Cruise Control) request for driver take over";
CM_ SG_ 678 MRR_ObjectCapturedSt "Status of object capturing";
CM_ SG_ 678 MRR_TargetSpd "Desired target speed from the driver";
CM_ SG_ 678 MRR_TargetDistanceSt "Desired distance stage for the display, distance stage 4 indicates the furthest distance";
CM_ SG_ 678 MRR_TauGapSet "The set timeGap from driver, should be displayed to driver and if the set value changes";
CM_ SG_ 678 ADAS_ShifttoParkGearReq "ADAS request transmission/VCU to switch to Park gear";
CM_ SG_ 678 MRR_FCWOffSt "This signal indicates if the FCW (Front Collision Warning) is deactivated";
CM_ SG_ 678 MRR_FCWFaultSt "This signal indicates the FCW (Front Collision Warning) fault status";
CM_ SG_ 678 MRR_FCWInterventionSt "This signal indicates if the FCW(Front Collision Warning) intervents";
CM_ SG_ 678 MRR_FCWPreWarn "Acoustic pre warning.Used with  signal MRR_ FCWActiveSt";
CM_ SG_ 678 MRR_FCWLatentWarn "Visual warning. used with signal MRR_ FCWActiveSt";
CM_ SG_ 678 MRR_AEB_PEDPFaultSt "This signal indicates the AEB_PEDP(AEB_Pedestrian Protection) fault status";
CM_ SG_ 678 MRR_AEBFaultSt "This signal indicates the AEB(Auto Emerrgency Braking) fault status";
CM_ SG_ 678 MRR_AEBOffSt "This signal indicates if the AEB(Auto Emerrgency Braking) is deactivated";
CM_ SG_ 678 MRR_AEBInterventionSt "This signal indicates if the AEB(Auto Emerrgency Braking) intervents";
CM_ SG_ 678 MRR_FCWWarmDistance "FCW warming distance";
CM_ SG_ 678 MRR_ACCQuitSound "ACC quit sound remind";
CM_ SG_ 678 IC_MRR_1_MsgCounter "Check the message counter consistency at least on every message reception";
CM_ SG_ 678 MRR_PEBSObjType "The type of SDF target object when PEBS(includled FCW/FCW-Peds or AEB/AEB-Peds )is active.";

BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 0;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 0;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 0;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" INT 0 0;
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 0;
BA_DEF_ SG_  "GenSigCycleTimeActive" INT 0 0;
BA_DEF_ SG_  "GenSigCycleTime" INT 0 0;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_  "Baudrate" INT 0 1000000;
BA_DEF_  "BusType" STRING ;
BA_DEF_  "NmType" STRING ;
BA_DEF_  "Manufacturer" STRING ;
BA_DEF_ BO_  "TpTxIndex" INT 0 255;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "NmStationAddress" HEX 0 255;
BA_DEF_ BU_  "NmNode" ENUM  "no","yes";
BA_DEF_ BO_  "NmMessage" ENUM  "no","yes";
BA_DEF_  "NmAsrWaitBusSleepTime" INT 0 65535;
BA_DEF_  "NmAsrTimeoutTime" INT 1 65535;
BA_DEF_  "NmAsrRepeatMessageTime" INT 0 65535;
BA_DEF_ BU_  "NmAsrNodeIdentifier" HEX 0 255;
BA_DEF_ BU_  "NmAsrNode" ENUM  "no","yes";
BA_DEF_  "NmAsrMessageCount" INT 1 256;
BA_DEF_ BO_  "NmAsrMessage" ENUM  "no","yes";
BA_DEF_ BU_  "NmAsrCanMsgReducedTime" INT 1 65535;
BA_DEF_  "NmAsrCanMsgCycleTime" INT 1 65535;
BA_DEF_ BU_  "NmAsrCanMsgCycleOffset" INT 0 65535;
BA_DEF_  "NmAsrBaseAddress" HEX 0 2047;
BA_DEF_ BU_  "ILUsed" ENUM  "no","yes";
BA_DEF_  "ILTxTimeout" INT 0 65535;
BA_DEF_ SG_  "GenSigTimeoutValue" INT 0 65535;
BA_DEF_ SG_  "GenSigTimeoutTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "no","yes";
BA_DEF_ BO_  "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_  "DiagUudtResponse" ENUM  "false","true";
BA_DEF_ BO_  "DiagUudResponse" ENUM  "False","True";
BA_DEF_ BO_  "DiagState" ENUM  "no","yes";
BA_DEF_ BO_  "DiagResponse" ENUM  "no","yes";
BA_DEF_ BO_  "DiagRequest" ENUM  "no","yes";
BA_DEF_  "DBName" STRING ;
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigCycleTimeActive" 0;
BA_DEF_DEF_  "GenSigCycleTime" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "Baudrate" 500000;
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NmType" "";
BA_DEF_DEF_  "Manufacturer" "Vector";
BA_DEF_DEF_  "TpTxIndex" 0;
BA_DEF_DEF_  "NodeLayerModules" " ";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmNode" "no";
BA_DEF_DEF_  "NmMessage" "no";
BA_DEF_DEF_  "NmAsrWaitBusSleepTime" 1500;
BA_DEF_DEF_  "NmAsrTimeoutTime" 2000;
BA_DEF_DEF_  "NmAsrRepeatMessageTime" 3200;
BA_DEF_DEF_  "NmAsrNodeIdentifier" 50;
BA_DEF_DEF_  "NmAsrNode" "no";
BA_DEF_DEF_  "NmAsrMessageCount" 128;
BA_DEF_DEF_  "NmAsrMessage" "no";
BA_DEF_DEF_  "NmAsrCanMsgReducedTime" 320;
BA_DEF_DEF_  "NmAsrCanMsgCycleTime" 640;
BA_DEF_DEF_  "NmAsrCanMsgCycleOffset" 0;
BA_DEF_DEF_  "NmAsrBaseAddress" 1280;
BA_DEF_DEF_  "ILUsed" "no";
BA_DEF_DEF_  "ILTxTimeout" 0;
BA_DEF_DEF_  "GenSigTimeoutValue" 0;
BA_DEF_DEF_  "GenSigTimeoutTime" 0;
BA_DEF_DEF_  "GenMsgILSupport" "no";
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "DiagUudtResponse" "false";
BA_DEF_DEF_  "DiagUudResponse" "False";
BA_DEF_DEF_  "DiagState" "no";
BA_DEF_DEF_  "DiagResponse" "no";
BA_DEF_DEF_  "DiagRequest" "no";
BA_DEF_DEF_  "DBName" "";
BA_ "Manufacturer" "Vector";
BA_ "NmType" "NmAsr";
BA_ "BusType" "CAN";
BA_ "Baudrate" 500000;
BA_ "NmAsrWaitBusSleepTime" 2000;
BA_ "DBName" "Client_1210_2";

BA_ "GenMsgSendType" BO_ 75 0;
BA_ "GenMsgCycleTime" BO_ 75 20;
BA_ "GenMsgSendType" BO_ 110 0;
BA_ "GenMsgCycleTime" BO_ 110 20;
BA_ "GenMsgSendType" BO_ 74 0;
BA_ "GenMsgCycleTime" BO_ 74 20;
BA_ "GenMsgSendType" BO_ 109 0;
BA_ "GenMsgCycleTime" BO_ 109 20;
BA_ "GenMsgSendType" BO_ 157 0;
BA_ "GenMsgCycleTime" BO_ 157 100;
BA_ "GenMsgSendType" BO_ 88 0;
BA_ "GenMsgCycleTime" BO_ 88 20;
BA_ "GenMsgSendType" BO_ 614 0;
BA_ "GenMsgCycleTime" BO_ 614 0;
BA_ "GenMsgSendType" BO_ 630 0;
BA_ "GenMsgCycleTime" BO_ 630 0;
BA_ "GenMsgSendType" BO_ 646 0;
BA_ "GenMsgCycleTime" BO_ 646 0;
BA_ "GenMsgSendType" BO_ 95 0;
BA_ "GenMsgCycleTime" BO_ 95 50;
BA_ "GenMsgSendType" BO_ 137 0;
BA_ "GenMsgCycleTime" BO_ 137 20;
BA_ "GenMsgSendType" BO_ 65 0;
BA_ "GenMsgCycleTime" BO_ 65 20;
BA_ "GenMsgSendType" BO_ 73 0;
BA_ "GenMsgCycleTime" BO_ 73 50;
BA_ "GenMsgSendType" BO_ 674 0;
BA_ "GenMsgCycleTime" BO_ 674 0;
BA_ "GenMsgSendType" BO_ 678 0;
BA_ "GenMsgCycleTime" BO_ 678 0;
BA_ "GenMsgSendType" BO_ 126 0;
BA_ "GenMsgCycleTime" BO_ 126 20;
BA_ "GenMsgSendType" BO_ 259 0;
BA_ "GenMsgCycleTime" BO_ 259 0;
BA_ "GenMsgSendType" BO_ 291 0;
BA_ "GenMsgCycleTime" BO_ 291 0;
BA_ "GenMsgSendType" BO_ 150 0;
BA_ "GenMsgCycleTime" BO_ 150 20;
BA_ "GenMsgSendType" BO_ 566 0;
BA_ "GenMsgCycleTime" BO_ 566 100;
BA_ "GenMsgSendType" BO_ 81 0;
BA_ "GenMsgCycleTime" BO_ 81 0;
BA_ "GenMsgSendType" BO_ 82 0;
BA_ "GenMsgCycleTime" BO_ 82 0;

BA_ "GenSigStartValue" SG_ 75 ACM_CRC 0;
BA_ "GenSigStartValue" SG_ 75 ACM_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 75 LatitudeAcc 2000;
BA_ "GenSigStartValue" SG_ 75 YawRate 18000;
BA_ "GenSigStartValue" SG_ 75 LongitudeAcc 2000;
BA_ "GenSigStartValue" SG_ 110 ADC_CRC 0;
BA_ "GenSigStartValue" SG_ 110 Acc_Mode 0;
BA_ "GenSigStartValue" SG_ 110 Acc_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 110 Acc_Decel_Req 1444;
BA_ "GenSigStartValue" SG_ 110 Acc_Decel_ReqSts 0;
BA_ "GenSigStartValue" SG_ 110 ADC_LngCtrlReq 0;
BA_ "GenSigStartValue" SG_ 110 ADC_VehTrqReq 5000;
BA_ "GenSigStartValue" SG_ 110 ADC_GearReq 0;
BA_ "GenSigStartValue" SG_ 110 ADC_GearReqVD 0;
BA_ "GenSigStartValue" SG_ 110 ADC_IncresePressureReq 0;
BA_ "GenSigStartValue" SG_ 74 ADC_CRC 0;
BA_ "GenSigStartValue" SG_ 74 ADC_EpsSpdDown 0;
BA_ "GenSigStartValue" SG_ 74 ADC_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 74 ADC_SteerTorqReq 512;
BA_ "GenSigStartValue" SG_ 74 ADC_LatCtrlReq 0;
BA_ "GenSigStartValue" SG_ 74 ADC_SteerAngReq 16384;
BA_ "GenSigStartValue" SG_ 109 AEB_CRC 0;
BA_ "GenSigStartValue" SG_ 109 AEB_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 109 AEBDecelReq 0;
BA_ "GenSigStartValue" SG_ 109 AEBTarDecel 32773.4535026628;
BA_ "GenSigStartValue" SG_ 157 BCM_CLOSURE_CRC 0;
BA_ "GenSigStartValue" SG_ 157 BCM_CLOSURE_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 157 DoorAjarFrntRiSts 0;
BA_ "GenSigStartValue" SG_ 157 DoorAjarReRiSts 0;
BA_ "GenSigStartValue" SG_ 157 DoorAjarFrntLeSts 0;
BA_ "GenSigStartValue" SG_ 157 DoorAjarReLeSts 0;
BA_ "GenSigStartValue" SG_ 157 VehSpd 0;
BA_ "GenSigStartValue" SG_ 88 CDDSAvl 0;
BA_ "GenSigStartValue" SG_ 88 CDDSActv 0;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_HitTarVerDis 0;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_HitTarLatDis2OLM 0;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_HitTarLatDis 512;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_VisualSpdLimitHitTar 0;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_VisualSpdLimitNotHit2 0;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_VisualSpdLimitNotHit1 0;
BA_ "GenSigStartValue" SG_ 614 CAM_TSR_OvSpdlimType 0;
BA_ "GenSigStartValue" SG_ 630 CAM_SystemType 0;
BA_ "GenSigStartValue" SG_ 630 CAM_SysFailureFlag 0;
BA_ "GenSigStartValue" SG_ 630 CAM_WarnTone 0;
BA_ "GenSigStartValue" SG_ 630 CAM_ButtonPress 0;
BA_ "GenSigStartValue" SG_ 630 CAM_SideZoneStatusF 0;
BA_ "GenSigStartValue" SG_ 630 CAM_SideZoneStatusR 0;
BA_ "GenSigStartValue" SG_ 630 CAM_RRMidDistance 255;
BA_ "GenSigStartValue" SG_ 630 CAM_RLMidDistance 255;
BA_ "GenSigStartValue" SG_ 630 CAM_RRDistance 255;
BA_ "GenSigStartValue" SG_ 630 CAM_RLDistance 255;
BA_ "GenSigStartValue" SG_ 630 CAM_RSLSideDistance 255;
BA_ "GenSigStartValue" SG_ 630 CAM_RSRSideDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_FRDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_FLDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_FRMidDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_FLMidDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_FSRSideDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_FSLSideDistance 255;
BA_ "GenSigStartValue" SG_ 646 CAM_RRMidSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_RLMidSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_RRSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_RLSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_RSLSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_RSRSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_FRSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_FLSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_FRMidSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_FLMidSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_FSLSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_FSRSnsErrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_CtrlModulerrFlag 0;
BA_ "GenSigStartValue" SG_ 646 CAM_BattVoltErrFlag 0;
BA_ "GenSigStartValue" SG_ 95 VCUAccrPedlOvrd 0;
BA_ "GenSigStartValue" SG_ 137 EPB_SwitchSt 0;
BA_ "GenSigStartValue" SG_ 137 BrkPedlSts 0;
BA_ "GenSigStartValue" SG_ 137 EPB_FailSt 0;
BA_ "GenSigStartValue" SG_ 137 Vehspd 0;
BA_ "GenSigStartValue" SG_ 137 Vehspdsts 0;
BA_ "GenSigStartValue" SG_ 137 BrkPedPst 0;
BA_ "GenSigStartValue" SG_ 137 BrkPedPstVD 0;
BA_ "GenSigStartValue" SG_ 137 Brk_Msgcntr 0;
BA_ "GenSigStartValue" SG_ 65 EPS_02_CRC 0;
BA_ "GenSigStartValue" SG_ 65 EPS_PinionAgValid 0;
BA_ "GenSigStartValue" SG_ 65 EPS_02_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 65 EPS_PinionAg 16384;
BA_ "GenSigStartValue" SG_ 65 EPS_MotorTq 512;
BA_ "GenSigStartValue" SG_ 65 EPS_MotorTqValid 0;
BA_ "GenSigStartValue" SG_ 73 EPS_D_CRC 0;
BA_ "GenSigStartValue" SG_ 73 EPS_Steer_angleandspdvalid 0;
BA_ "GenSigStartValue" SG_ 73 EPS_Steer_angledir 0;
BA_ "GenSigStartValue" SG_ 73 EPS_Steeragspddir 0;
BA_ "GenSigStartValue" SG_ 73 EPS_D_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 73 EPS_SteeringAngle 0;
BA_ "GenSigStartValue" SG_ 73 EPS_FaultSts 0;
BA_ "GenSigStartValue" SG_ 73 EPS_ToqCtrlAbortFeedback 0;
BA_ "GenSigStartValue" SG_ 73 EPS_LatCtrlMode 0;
BA_ "GenSigStartValue" SG_ 73 EPS_SteeringAngleSpd 0;
BA_ "GenSigStartValue" SG_ 73 EPS_AngCtrlAbortFeedback 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_SLCAheadDis 0;
BA_ "GenSigStartValue" SG_ 674 IC_SysCalibrationSt 0;
BA_ "GenSigStartValue" SG_ 674 IC_CameraBlockageSt 0;
BA_ "GenSigStartValue" SG_ 674 IC_LateralCtrlQuitWarn 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_ISDRestOfDis 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_SpdLimType 0;
BA_ "GenSigStartValue" SG_ 674 IC_CruiseMode 0;
BA_ "GenSigStartValue" SG_ 674 IC_1_MsgCounter 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_AudioWarnEnable 0;
BA_ "GenSigStartValue" SG_ 674 ADAS_SVMLaneMarkingReq 0;
BA_ "GenSigStartValue" SG_ 674 IC_LKS_LaneAssistType 0;
BA_ "GenSigStartValue" SG_ 674 IC_LKS_St 0;
BA_ "GenSigStartValue" SG_ 674 IC_LeftTrackingSt 0;
BA_ "GenSigStartValue" SG_ 674 IC_RightTrackingSt 0;
BA_ "GenSigStartValue" SG_ 674 IC_LKS_TakeoverReq 0;
BA_ "GenSigStartValue" SG_ 674 IC_HWAHF_HandsOnIcon 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_SpdLimit 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_SpdLimitUnit 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_OperatingSt 0;
BA_ "GenSigStartValue" SG_ 674 IC_HMA_St 0;
BA_ "GenSigStartValue" SG_ 674 IC_HMA_HighBeamReq 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_Enable 0;
BA_ "GenSigStartValue" SG_ 674 IC_HMA_Enable 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_VisualWarn 0;
BA_ "GenSigStartValue" SG_ 674 IC_TSR_AcousticWarn 0;
BA_ "GenSigStartValue" SG_ 674 IC_TJA_ICA_St 0;
BA_ "GenSigStartValue" SG_ 674 IC_1_DrvReq 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCMode 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCTextInfo 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCFaultSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCActiveSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCGoNotifier 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCFuncCancel 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCTakeOverReq 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ObjectCapturedSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_TargetSpd 0;
BA_ "GenSigStartValue" SG_ 678 MRR_TargetDistanceSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_TauGapSet 0;
BA_ "GenSigStartValue" SG_ 678 ADAS_ShifttoParkGearReq 0;
BA_ "GenSigStartValue" SG_ 678 MRR_FCWOffSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_FCWFaultSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_FCWInterventionSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_FCWPreWarn 0;
BA_ "GenSigStartValue" SG_ 678 MRR_FCWLatentWarn 0;
BA_ "GenSigStartValue" SG_ 678 MRR_AEB_PEDPFaultSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_AEBFaultSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_AEBOffSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_AEBInterventionSt 0;
BA_ "GenSigStartValue" SG_ 678 MRR_FCWWarmDistance 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ISLACC_St 0;
BA_ "GenSigStartValue" SG_ 678 MRR_ACCQuitSound 0;
BA_ "GenSigStartValue" SG_ 678 IC_MRR_1_MsgCounter 0;
BA_ "GenSigStartValue" SG_ 678 MRR_PEBSObjType 0;
BA_ "GenSigStartValue" SG_ 126 LLC_CRC 0;
BA_ "GenSigStartValue" SG_ 126 LLC_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 126 ADC_APAGearLvlReq 0;
BA_ "GenSigStartValue" SG_ 126 ADC_APAGearLvlReqVD 0;
BA_ "GenSigStartValue" SG_ 126 ADC_APAStopDist 0;
BA_ "GenSigStartValue" SG_ 126 ADC_APASpdLimit 0;
BA_ "GenSigStartValue" SG_ 259 SCM_BrkLiReq 0;
BA_ "GenSigStartValue" SG_ 259 SCM_WiperReq 0;
BA_ "GenSigStartValue" SG_ 259 SCM_TurnLeftLightReq 0;
BA_ "GenSigStartValue" SG_ 259 SCM_TurnRightLightReq 0;
BA_ "GenSigStartValue" SG_ 259 SCM_EmeglampReq 0;
BA_ "GenSigStartValue" SG_ 259 SCM_HornrReq 0;
BA_ "GenSigStartValue" SG_ 291 SCM_CRC 0;
BA_ "GenSigStartValue" SG_ 291 AccTimeGapIncSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 SCM_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 291 ModeTogDecSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 AccSetSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 MenuPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 AccSpdDecSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 FogLiPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 HiLowBeamPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 SCMFailSts 0;
BA_ "GenSigStartValue" SG_ 291 ModeTogIncSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 TurnIndcrSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 MenuRiPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 MenuOrVolUpPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 AccTimeGapDecSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 MenuOrVolDwnPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 SWCFailSts 0;
BA_ "GenSigStartValue" SG_ 291 AccSpdIncOrResuSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 MenuLePushSwtSts 0;
BA_ "GenSigStartValue" SG_ 291 CCPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 150 VCU_13_CRC 0;
BA_ "GenSigStartValue" SG_ 150 VCU_13_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 150 Accpedacttravelvalid 0;
BA_ "GenSigStartValue" SG_ 150 VCU_ACCPedalAcPst 0;
BA_ "GenSigStartValue" SG_ 150 VCU_CrntGearLvl 0;
BA_ "GenSigStartValue" SG_ 150 VCU_ActVehWheelTorq 0;
BA_ "GenSigStartValue" SG_ 566 BCM_FrontFogLampSt 0;
BA_ "GenSigStartValue" SG_ 566 BCM_RearFogLampSt 0;
BA_ "GenSigStartValue" SG_ 566 FrntwiperSts 0;
BA_ "GenSigStartValue" SG_ 566 BCM_TurnLampSt 0;
BA_ "GenSigStartValue" SG_ 566 BCM_BeamSt 0;
BA_ "GenSigStartValue" SG_ 566 BCM_BrkLightSts 0;
BA_ "GenSigStartValue" SG_ 81 WF_CRC 0;
BA_ "GenSigStartValue" SG_ 81 WF_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 81 WhlspdFLdir 0;
BA_ "GenSigStartValue" SG_ 81 WhlspdFLsts 0;
BA_ "GenSigStartValue" SG_ 81 WhlspdFR 0;
BA_ "GenSigStartValue" SG_ 81 WhlspdFRdir 0;
BA_ "GenSigStartValue" SG_ 81 WhlspdFRsts 0;
BA_ "GenSigStartValue" SG_ 81 WhlspdFL 0;
BA_ "GenSigStartValue" SG_ 82 WR_CRC 0;
BA_ "GenSigStartValue" SG_ 82 WR_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 82 WhlspdRLdir 0;
BA_ "GenSigStartValue" SG_ 82 WhlspdRLsts 0;
BA_ "GenSigStartValue" SG_ 82 WhlspdRR 0;
BA_ "GenSigStartValue" SG_ 82 WhlspdRRdir 0;
BA_ "GenSigStartValue" SG_ 82 WhlspdRRsts 0;
BA_ "GenSigStartValue" SG_ 82 WhlspdRL 0;

VAL_ 110 Acc_Mode 7 "failure mode" 6 "standstill mode" 5 "override mode" 4 "brake only mode" 3 "active control mode" 2 "standby mode" 1 "passive mode" 0 "off mode" ;
VAL_ 110 Acc_Decel_ReqSts 1 "request" 0 "not request" ;
VAL_ 110 ADC_LngCtrlReq 3 "reserved" 2 "reserved" 1 "active" 0 "inactive" ;
VAL_ 110 ADC_GearReq 3 "D" 2 "R" 1 "P" 0 "NO Request" ;
VAL_ 110 ADC_GearReqVD 1 "valid" 0 "invalid" ;
VAL_ 74 ADC_EpsSpdDown 2 "Speed Zero" 1 "Normal" 0 "defult" ;
VAL_ 74 ADC_LatCtrlReq 7 "Signal not available" 6 "Reserved_6" 5 "Reserved_5" 3 "Ang request" 2 "Toq  request" 1 "Ready" 0 "No request" ;
VAL_ 109 AEBDecelReq 1 " Request" 0 "No request" ;
VAL_ 157 DoorAjarFrntRiSts 3 " Invalid" 2 "Reserved" 1 "Closed" 0 "Opened" ;
VAL_ 157 DoorAjarReRiSts 3 " Invalid" 2 "Reserved" 1 "Closed" 0 "Opened" ;
VAL_ 157 DoorAjarFrntLeSts 3 " Invalid" 2 "Reserved" 1 "Closed" 0 "Opened" ;
VAL_ 157 DoorAjarReLeSts 3 " Invalid" 2 "Reserved" 1 "Closed" 0 "Opened" ;
VAL_ 88 CDDSAvl 1 " available" 0 "Not available" ;
VAL_ 88 CDDSActv 1 " Active" 0 "Not active" ;
VAL_ 614 CAM_TSR_OvSpdlimType 15 "others" 14 "Not used" 13 "Not used" 12 "Not used" 11 "Not used" 10 "Not used" 9 "Not used" 8 "Not used" 7 "Not used" 6 "Not used" 5 "speed limit cancel" 4 "electronic speed limit" 3 "ramp" 2 "multiple speed limit" 1 "signal speed limit" 0 "not detected" ;
VAL_ 630 CAM_SystemType 3 "Not used" 2 "Not used" 1 "APA(Automatic Parking Assistant)" 0 "RPA(Reverse Parking Assistant)" ;
VAL_ 630 CAM_SysFailureFlag 3 "System failure" 2 "Rear failure" 1 "Front failure" 0 "Not failure" ;
VAL_ 630 CAM_WarnTone 15 "Not used" 14 "Not used" 13 "Not used" 12 "Not used" 11 "Not used" 10 "Not used" 9 "Not used" 8 "Not used" 7 "Not used" 6 "Not used" 5 "Mute" 4 "Slow intermittent tone" 3 "Medium-speed intermittent tone" 2 "Fast intermittent tone" 1 "Continuous tone" 0 "Not active" ;
VAL_ 630 CAM_ButtonPress 1 "Press" 0 "Not press" ;
VAL_ 630 CAM_SideZoneStatusF 3 "Not used" 2 "Not used" 1 "Left and right" 0 "None" ;
VAL_ 630 CAM_SideZoneStatusR 3 "Not used" 2 "Not used" 1 "Left and right" 0 "None" ;
VAL_ 630 CAM_RRMidDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 630 CAM_RLMidDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 630 CAM_RRDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 630 CAM_RLDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 630 CAM_RSLSideDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 630 CAM_RSRSideDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_FRDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_FLDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_FRMidDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_FLMidDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_FSRSideDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_FSLSideDistance 255 "No Obstacle Detected" 254 "Invalid" 253 "Invalid" 252 "Invalid" 251 "Invalid" ;
VAL_ 646 CAM_RRMidSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_RLMidSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_RRSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_RLSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_RSLSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_RSRSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_FRSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_FLSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_FRMidSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_FLMidSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_FSLSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_FSRSnsErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_CtrlModulerrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 646 CAM_BattVoltErrFlag 1 "Fault" 0 "Not fault" ;
VAL_ 95 VCUAccrPedlOvrd 1 " request" 0 "no request" ;
VAL_ 137 EPB_SwitchSt 3 "invalid" 2 "Applied" 1 "Released" 0 "not pressed" ;
VAL_ 137 BrkPedlSts 3 "invalid" 2 "reserved" 1 "pressed" 0 "not pressed" ;
VAL_ 137 EPB_FailSt 1 "Failure" 0 "No failure" ;
VAL_ 137 Vehspdsts 1 "invalid" 0 "valid" ;
VAL_ 137 BrkPedPstVD 1 "invalid" 0 "valid" ;
VAL_ 65 EPS_PinionAgValid 3 "Reserved" 2 "Signal not available" 1 "Invalid" 0 "Valid" ;
VAL_ 65 EPS_MotorTqValid 3 "Reserved" 2 "Signal not available" 1 "Invalid" 0 "Valid" ;
VAL_ 73 EPS_Steer_angleandspdvalid 1 "invalid" 0 "valid" ;
VAL_ 73 EPS_Steer_angledir 1 "Right(minus)" 0 "Left(plus)" ;
VAL_ 73 EPS_Steeragspddir 1 "Right(minus)" 0 "Left(plus)" ;
VAL_ 73 EPS_FaultSts 1 "Fault" 0 "No Fault" ;
VAL_ 73 EPS_ToqCtrlAbortFeedback 17 "APA Interruption" 16 "EPS Permanently error" 9 "Failed Temp Time Over Limit" 8 "Request Torque Value over range" 7 "Request Torque Slope over range" 6 "EPS Temporary error" 5 "IFC_2 Signal error" 4 "IFC Activation Request Error" 3 "Vehicle Speed Invalid" 2 "Driver override" 1 "Driver hands off" 0 "No interruption" ;
VAL_ 73 EPS_LatCtrlMode 7 "invalid" 3 "AngCtrl" 2 "ToqCtrl" 1 "Quit" 0 "Manual" ;
VAL_ 674 IC_TSR_SLCAheadDis 15 "not used" 14 "not used" 13 "not used" 12 "1" 11 "11/12" 10 "5/6" 9 "3/4" 8 "2/3" 7 "7/12" 6 "1/2" 5 "5/12" 4 "1/3" 3 "1/4" 2 "1/6" 1 "1/12" 0 "0/12" ;
VAL_ 674 IC_SysCalibrationSt 3 "Not used" 2 "Calibration Failed" 1 "Calibration In Process" 0 "Calibration Success" ;
VAL_ 674 IC_CameraBlockageSt 1 "Blocked" 0 "Not blocked" ;
VAL_ 674 IC_LateralCtrlQuitWarn 1 "Warning" 0 "No warning" ;
VAL_ 674 IC_TSR_ISDRestOfDis 15 "not used" 14 "not used" 13 "not used" 12 "1" 11 "11/12" 10 "5/6" 9 "3/4" 8 "2/3" 7 "7/12" 6 "1/2" 5 "5/12" 4 "1/3" 3 "1/4" 2 "1/6" 1 "1/12" 0 "0/12" ;
VAL_ 674 IC_TSR_SpdLimType 3 "Not used" 2 "Interval speed detection" 1 "Camera detection" 0 "not specific" ;
VAL_ 674 IC_CruiseMode 3 "Not used" 2 "TJA-ICA Mode" 1 "ACC Mode" 0 "Not selected" ;
VAL_ 674 IC_TSR_AudioWarnEnable 1 "Enable" 0 "Disable" ;
VAL_ 674 ADAS_SVMLaneMarkingReq 1 "Request" 0 "Not request" ;
VAL_ 674 IC_LKS_LaneAssistType 3 "Steering and Warning" 2 "Warning" 1 "Steering" 0 "Not selected" ;
VAL_ 674 IC_LKS_St 7 "Not used" 6 "Not used" 5 "Not used" 4 "Camera blocked" 3 "LKS failure" 2 "LKS active" 1 "LKS standby" 0 "LKS Off" ;
VAL_ 674 IC_LeftTrackingSt 3 "Warning" 2 "Intervention" 1 "Line tracking" 0 "No display" ;
VAL_ 674 IC_RightTrackingSt 3 "Warning" 2 "Intervention" 1 "Line tracking" 0 "No display" ;
VAL_ 674 IC_LKS_TakeoverReq 1 "Active" 0 "Not active" ;
VAL_ 674 IC_HWAHF_HandsOnIcon 7 "reserved" 6 "reserved" 5 "reserved" 4 "reserved" 3 "request not met 2" 2 "request not met 1" 1 "request met" 0 "no request" ;
VAL_ 674 IC_TSR_SpdLimit 31 "Not used" 30 "Not used" 29 "Not used" 28 "Not used" 27 "130" 26 "125" 25 "120" 24 "115" 23 "110" 22 "105" 21 "100" 20 "95" 19 "90" 18 "85" 17 "80" 16 "75" 15 "70" 14 "65" 13 "60" 12 "55" 11 "50" 10 "45" 9 "40" 8 "35" 7 "30" 6 "25" 5 "20" 4 "15" 3 "10" 2 "5" 1 "Cancelled" 0 "No display" ;
VAL_ 674 IC_TSR_SpdLimitUnit 3 "Not used" 2 "Miles/h" 1 "km/h" 0 "Unknown" ;
VAL_ 674 IC_TSR_OperatingSt 7 "Not used" 6 "Not used" 5 "Not used" 4 "TSR failure" 3 "Navigation only mode" 2 "Vision only mode" 1 "Fusion mode" 0 "Off" ;
VAL_ 674 IC_HMA_St 7 "Not used" 6 "Not used" 5 "Not used" 4 "Camera blocked" 3 "HMA failure" 2 "HMA active" 1 "HMA standby" 0 "HMA off" ;
VAL_ 674 IC_HMA_HighBeamReq 1 "High beam request" 0 "Low beam request" ;
VAL_ 674 IC_TSR_Enable 1 "Enable" 0 "Disable" ;
VAL_ 674 IC_HMA_Enable 1 "Enable" 0 "Disable" ;
VAL_ 674 IC_TSR_VisualWarn 1 "warning" 0 "No warning" ;
VAL_ 674 IC_TSR_AcousticWarn 1 "warning" 0 "No warning" ;
VAL_ 674 IC_TJA_ICA_St 7 "Not used" 6 "Not used" 5 "Not used" 4 "Failure" 3 "Active by target vehicle" 2 "Active by line" 1 "Passive" 0 "Off" ;
VAL_ 674 IC_1_DrvReq 31 "Reserved" 30 "Reserved" 29 "Reserved" 28 "Req_Exit Park Gear" 27 "Req_PS ID selection" 26 "Req_Close Trunk" 25 "Req_Gear R and Brake" 24 "Req_Press Brake Pedal" 23 "Req_Confirm_Press DM Swich(FAPA:Reserved)" 22 "Req_Buckle Seat Belt" 21 "Req_Close Door" 20 "Req_Release Brake" 19 "Req_Slow_Down" 18 "Reserved" 17 "Req_Gear_D" 16 "Reserved" 15 "Reserved" 14 "Req_POC Direction Select" 13 "Req_Repress_Parking Switch (Newer one)" 12 "Req_Process bar" 11 "Req_Function off" 10 "Req_Function Select" 9 "Req_Press_ Parking Switch" 8 "Req_Leave_Car (RPA)" 7 "Req_Stop" 6 "Req_Connect_Phone  (RPA)" 5 "Req_SearchingProcess" 4 "Req_Surround_View��Reserved)(internal signal with NRCS)" 3 "Req Gear_P and EPB_Applied (EPB inactive for RPA)" 2 "Req_Turn_lever��Reserved)" 1 "Req_ RPA or  APA Selection By Gear" 0 "No Request" ;
VAL_ 678 MRR_ACCMode 7 "Failure mode" 6 "Not used" 5 "Override" 4 "Brake only mode" 3 "Active control mode" 2 "Standby mode" 1 "passive mode" 0 "Off mode" ;
VAL_ 678 MRR_ACCTextInfo 31 "Not used" 30 "Not used" 29 "Not used" 28 "Not used" 27 "Not used" 26 "Not used" 25 "Not used" 24 "Not used" 23 "Not used" 22 "Not used" 21 "Not used" 20 "Not used" 19 "Not used" 18 "Not used" 17 "Not used" 16 "Not used" 15 "Not used" 14 "Not used" 13 "Not used" 12 "Not used" 11 "Radar blind" 10 "Not used" 9 "Not used" 8 "ACC switched off" 7 "ACC switched on" 6 "ACC and PEBS error" 5 "Cruise Speed set as TSR" 4 "Turn on ACC first" 3 "Active" 2 "ACC active control is cancelled" 1 "Unable to activate ACC" 0 "No display" ;
VAL_ 678 MRR_ACCFaultSt 3 "Not used" 2 "Not used" 1 "Fault" 0 "Not fault" ;
VAL_ 678 MRR_ACCActiveSt 1 "Active" 0 "Not active" ;
VAL_ 678 MRR_ACCGoNotifier 1 "Go notifier alert" 0 "No alert" ;
VAL_ 678 MRR_ACCFuncCancel 3 "Not used" 2 "ACC condition dissatisfy" 1 "ACC function cancel" 0 "Not active" ;
VAL_ 678 MRR_ACCTakeOverReq 1 "Driver takeover request" 0 "No request" ;
VAL_ 678 MRR_ObjectCapturedSt 1 "Object captured" 0 "No object captured" ;
VAL_ 678 MRR_TargetDistanceSt 7 "Not used" 6 "Not used" 5 "Not used" 4 "Distance 4" 3 "Distance 3" 2 "Distance 2" 1 "Distance 1" 0 "Distance 0" ;
VAL_ 678 MRR_TauGapSet 7 "Not used" 6 "Not used" 5 "Not used" 4 "TauGap 4" 3 "TauGap3" 2 "TauGap 2" 1 "TauGap 1" 0 "TauGap 0" ;
VAL_ 678 ADAS_ShifttoParkGearReq 1 "Request" 0 "No request" ;
VAL_ 678 MRR_FCWOffSt 1 "Off" 0 "On" ;
VAL_ 678 MRR_FCWFaultSt 1 "Fault" 0 "Not fault" ;
VAL_ 678 MRR_FCWInterventionSt 1 "Intervention" 0 "No intervention" ;
VAL_ 678 MRR_FCWPreWarn 3 "Not used" 2 "Not used" 1 "Warning" 0 "No warning" ;
VAL_ 678 MRR_FCWLatentWarn 3 "Not used" 2 "Not used" 1 "Warning" 0 "No warning" ;
VAL_ 678 MRR_AEB_PEDPFaultSt 1 "Fault" 0 "Not fault" ;
VAL_ 678 MRR_AEBFaultSt 1 "Fault" 0 "Not fault" ;
VAL_ 678 MRR_AEBOffSt 1 "Off" 0 "On" ;
VAL_ 678 MRR_AEBInterventionSt 1 "Intervention" 0 "No intervention" ;
VAL_ 678 MRR_FCWWarmDistance 3 "Not used" 2 "Far" 1 "Close" 0 "Normal" ;
VAL_ 678 MRR_ISLACC_St 1 "on" 0 "off" ;
VAL_ 678 MRR_ACCQuitSound 1 "sound remind" 0 "No remind" ;
VAL_ 678 MRR_PEBSObjType 15 "Not used" 14 "Not used" 13 "Not used" 12 "Not used" 11 "Not used" 10 "Not used" 9 "Not used" 8 "Not used" 7 "Not used" 6 "Not used" 5 "Unknown" 4 "Trunk" 3 "Cyclist" 2 "Pedestrian" 1 "Car" 0 "No display" ;
VAL_ 126 ADC_APAGearLvlReq 3 "Drive" 2 "Reversed" 1 "Park" 0 "No request" ;
VAL_ 126 ADC_APAGearLvlReqVD 1 "Valid" 0 "Invalid" ;
VAL_ 259 SCM_WiperReq 3 "invalid" 2 "high speed" 1 "low speed" 0 "off" ;
VAL_ 259 SCM_EmeglampReq 1 "on" 0 "off" ;
VAL_ 291 AccTimeGapIncSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 ModeTogDecSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 AccSetSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 MenuPushSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 AccSpdDecSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 FogLiPushSwtSts 3 " Invalid" 2 "Reserved" 1 "Push switch pressed" 0 "Push switch not pressed" ;
VAL_ 291 HiLowBeamPushSwtSts 3 " Invalid" 2 "Low beam or high beam command" 1 "Flash command" 0 "No command (Default position)" ;
VAL_ 291 SCMFailSts 3 " Invalid" 2 "Reserved" 1 "Fail" 0 "Normal" ;
VAL_ 291 ModeTogIncSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 TurnIndcrSwtSts 3 " Invalid" 2 "Turn indicator right" 1 "Turn indicator left" 0 "No turn indicator" ;
VAL_ 291 MenuRiPushSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 MenuOrVolUpPushSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 AccTimeGapDecSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 MenuOrVolDwnPushSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 SWCFailSts 3 " Invalid" 2 "Reserved" 1 "Fail" 0 "Normal" ;
VAL_ 291 AccSpdIncOrResuSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 MenuLePushSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 291 CCPushSwtSts 3 " Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 150 Accpedacttravelvalid 1 "invalid" 0 "valid" ;
VAL_ 150 VCU_CrntGearLvl 4 "Park" 3 "Reversed" 2 "Neutral" 1 "Drive" 0 "Invalid" ;
VAL_ 566 BCM_FrontFogLampSt 3 "invalid" 2 "reserved" 1 "on" 0 "off" ;
VAL_ 566 BCM_RearFogLampSt 3 "invalid" 2 "reserved" 1 "on" 0 "off" ;
VAL_ 566 FrntwiperSts 7 "invalid" 6 "reserved" 5 "reserved" 4 "reserved" 3 "reserved" 2 "high speed" 1 "low speed" 0 "off" ;
VAL_ 566 BCM_TurnLampSt 3 "hazard light" 2 "right light" 1 "left light" 0 "off" ;
VAL_ 566 BCM_BeamSt 3 "invalid" 2 "hight beam" 1 "low beam" 0 "off" ;
VAL_ 566 BCM_BrkLightSts 3 "invalid" 2 "reserved" 1 "on" 0 "off" ;
VAL_ 81 WhlspdFLdir 3 "invalid" 1 "backward" 0 "forward" ;
VAL_ 81 WhlspdFLsts 3 "reserved" 2 "initial" 1 "valid" 0 "invalid" ;
VAL_ 81 WhlspdFRdir 3 "invalid" 1 "backward" 0 "forward" ;
VAL_ 81 WhlspdFRsts 3 "reserved" 2 "initial" 1 "valid" 0 "invalid" ;
VAL_ 82 WhlspdRLdir 3 "invalid" 1 "backward" 0 "forward" ;
VAL_ 82 WhlspdRLsts 3 "reserved" 2 "initial" 1 "valid" 0 "invalid" ;
VAL_ 82 WhlspdRRdir 3 "invalid" 1 "backward" 0 "forward" ;
VAL_ 82 WhlspdRRsts 3 "reserved" 2 "initial" 1 "valid" 0 "invalid" ;


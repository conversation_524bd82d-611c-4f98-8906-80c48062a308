#include "bst_aion.h"

std::shared_ptr<bst_aion::BSTCanDbcAion> pIOControl = nullptr;

int main(int argc, char* argv[]) 
{
   adas::app_template app;
    namespace bpo = boost::program_options;
    std::cout <<"cplusplus : "<<__cplusplus<< std::endl;
    // clang-off
    app.add_options()
     ("node_name",bpo::value<std::string>()->default_value(""),"apa control node name")
     ("bst_node_cfg",bpo::value<std::string>()->default_value("bst_aion_io/config/aion_io_server.json"),"node config json file")
     ("bst_dbc_file",bpo::value<std::string>()->default_value("bst_aion_io/dbc/Client_1210.dbc"),"node config json file")
     ("conf",bpo::value<std::string>()->default_value("bst_aion_io/config/aion_io.ini"), "template");
    // clang-on
    if(const auto &ret = app.run(argc,argv,"conf") ; ret != 0){
        if(ret == 1){
            std::cout <<"show help!"<< std::endl;
            return 0;
        }
        std::cout <<"command_line or conf_file parse failed !"<< std::endl;
        return -1;
    }
    auto &&config = app.configuration();
    auto bst_node_cfg = config["bst_node_cfg"].as<std::string>();
    auto bst_dbc_file = config["bst_dbc_file"].as<std::string>();
    auto node_name = config["node_name"].as<std::string>();
    bpo::notify(config);

    std::cout <<"node_name : "<<node_name<< std::endl;
    std::cout <<"bst_dbc_file : "<<bst_dbc_file<< std::endl;
    std::cout <<"bst_node_cfg : "<<bst_node_cfg<< std::endl;

    AINFO << __FUNCTION__ << "argc " << argc;
    
#if 0 
 if (argc != 2) {
    std::cout << "Usage: ./bst_io_serve /path/to/xxx.dbc" << std::endl;
    return 1;
  }
#endif

  std::cout << "Waitting for init..." << std::endl;
  ADSNode::Init(argv[0]);
   
  NodeCfg nodeCfg(bst_node_cfg);
  if (!nodeCfg.isValid()) {
      return 1; 
  }

  if(node_name == ""){
    node_name = nodeCfg.getName();
  }
  std::shared_ptr<ADSNode>node= std::make_shared<ADSNode>(node_name);

  if(nullptr == pIOControl){
      try{
          pIOControl = std::make_shared<bst_aion::BSTCanDbcAion>(node,nodeCfg,bst_dbc_file.c_str());
      }
      catch(const std::exception& e) {
          AINFO << __FUNCTION__ << e.what() << '\n';
          return 1;
      }
  }

  std::cout << "Init done. Enjoy." << std::endl;
  apollo::cyber::WaitForShutdown();
}
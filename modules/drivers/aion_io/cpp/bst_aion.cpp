#include "bst_aion.h"

namespace bst_aion
{    
    std::shared_ptr<Message> decoded_message = nullptr;
    Vector::DBC::Network dbc_network;
    CanHal& can_instance = CanHal::GetInstance();
    std::mutex mtx;
    std::condition_variable cv;
    std::atomic_bool send_exit = false;

    std::shared_ptr<Message> decodeMessage(uint64_t timestamp, unsigned int canIdentifier, 
                                      std::vector<std::uint8_t>& canData) {
      auto& messages = dbc_network.messages;
      auto search = messages.find(canIdentifier);
      if (search == messages.end()) {
      std::cout << "Not found: " << canIdentifier << std::endl;
      return nullptr;
      }

      Vector::DBC::Message& message = messages[canIdentifier];
      std::shared_ptr<Message> output(new Message(timestamp, message.name));

      for (const auto& signal : message.signals) {
      //std::cout << "  Signal " << signal.second.name << std::endl;
      unsigned int rawValue = signal.second.decode(canData);
      //std::cout << "    Raw Value: 0x" << rawValue << std::endl;
      double physicalValue = signal.second.rawToPhysicalValue(rawValue);
      //std::cout << "    Physical Value: " << physicalValue << std::endl;

      Signal temp(signal.second.name, rawValue, physicalValue);
      output->signals.push_back(temp);
      }

      return output;
    }
    class CanCbTest : public CanCbIf {
            public:
                CanCbTest() {}

                void callback_fun(CanDev_t can_channel,
                                const hal_can_frame_t& hal_can_frame_inst) override {
                    std::cout << "-----------------callback-----------------------" << std::endl;
                    int delta_can_frame_time =
                        hal_can_frame_inst.timestamp_nano / 1e6 -
                        m_last_can_frame_time;
                    m_last_can_frame_time =
                        static_cast<long>(hal_can_frame_inst.timestamp_nano / 1e6);
                    // std::cout << "[CAN_HAL_TEST] can channel " << dec << can_channel
                    //           << " recieved data" << std::endl;
                    // std::cout << "[CAN_HAL_TEST] can frame counter " << dec
                    //           << m_counter++ << std::endl;
                    // std::cout << "[CAN_HAL_TEST] can ID: "
                    //           << "0x" << hex << hal_can_frame_inst.can_id << std::endl;
                    // std::cout << "[CAN_HAL_TEST] can dlc: " << dec
                    //           << (int)hal_can_frame_inst.dlc << std::endl;
                    // std::cout << "[CAN_HAL_TEST] can delta timestamp ms: " << dec
                    //           << delta_can_frame_time << std::endl;
                    std::vector<std::uint8_t> can_data(hal_can_frame_inst.data_buf, hal_can_frame_inst.data_buf + hal_can_frame_inst.dlc);
                    // std::cout << "[CAN_HAL_TEST] can data: ";
                    // for (int i = 0; i < hal_can_frame_inst.dlc; i++) {
                    //     std::cout << "0x" << hex << (int)hal_can_frame_inst.data_buf[i]
                    //               << " ";
                    // }
                    // std::cout << std::endl;
                    std::unique_lock<std::mutex> lck(mtx);
                    decoded_message = decodeMessage(m_last_can_frame_time, hal_can_frame_inst.can_id, can_data);
                    cv.notify_one();
                    //std::cout << "----------------------------------------" << std::endl;
                }

            private:
                long m_last_can_frame_time = 0;
                uint64_t m_counter = 0;
        };

    BSTCanDbcAion::BSTCanDbcAion(std::shared_ptr <ADSNode> node, NodeCfg& nodecfg, const char* dbc_path)
    :pNode(node),
    nodeCfg(nodecfg){
      ChassisInfoReset();
      resetCanMsg();
      resetControlCmd();
      StartDBC(dbc_path);
      InitChassisInfoSend();
      InitSub();
      InitPub();
      InitTimer();
    }

    BSTCanDbcAion::~BSTCanDbcAion(){
      send_exit  = true;
      cv.notify_all();
      if (this->writeChassisInfo.joinable())
      {
          this->writeChassisInfo.join();
      }
    }

    void BSTCanDbcAion::InitChassisInfoSend(){
      this->writeChassisInfo = std::thread(std::bind(&BSTCanDbcAion::IOChassisInfoRecv, this));
    }

    void BSTCanDbcAion::InitTimer(){
      if (pTimerIOControlCmdSend == nullptr) {
        pTimerIOControlCmdSend = std::make_shared<apollo::cyber::Timer>(10, std::bind(&BSTCanDbcAion::IOControlCmdSend,this), false);
        pTimerIOControlCmdSend->Start();
      }
    }


    void BSTCanDbcAion::StartDBC(const char *dbc_path){
      std::string can_config_path = "/etc/adsp/conf/sensor_manager/can_hal_config.pb.txt";
      std::shared_ptr<CanCbTest> can_cb = make_shared<CanCbTest>();
      uint64_t counter = 0;
      CanCbId_t can_cb_id = 0;
      ChassisInfoReset();

      // init dbc
      std::ifstream ifs(dbc_path);
      std::cout << "dbc_path: " << dbc_path << std::endl;
      if (!ifs.is_open()) {
        std::cout << "Unable to open dbc file" << std::endl;
      }
      ifs >> dbc_network;
      if (!dbc_network.successfullyParsed) {
        std::cout << "Unable to parse dbc file" << std::endl;
      }
      /* loop over messages */
      for (const auto & message : dbc_network.messages) {
        std::cout << "Message id: " << message.second.id << std::endl;
        std::cout << "Message name: " << message.second.name << std::endl;
        std::cout << "Message size: " << message.second.size << std::endl;
    
        /* loop over signals of this messages */
        for (const auto & signal : message.second.signals) {
          std::cout << "  Signal name: " << signal.second.name << std::endl;
          std::cout << "  Signal comment: " << signal.second.comment << std::endl;
        }
      }

      /* 初始化CAN总线 */
      if (can_instance.CanChannelInit(can_config_path)) {
          std::cout << "[CAN_HAL_TEST] can init success!" << std::endl;
      }

      can_cb_id =
          can_instance.CanRegisterCallback(CAN_DEV_0, can_cb);
      std::cout << "[CAN_HAL_TEST] wating for can frame..." << std::endl;

    }

    void BSTCanDbcAion::IOChassisInfoRecv(){
      // update chassis info
      while (!send_exit)
      {
        std::unique_lock<std::mutex> lck(mtx);
        cv.wait(lck);
        if(Status::ERROR == UpdateChassisInfo()){
          AINFO << __FUNCTION__ << ": update chassis info failed.";
        }
        //deal and send chassis info
        IOChassisInfoSend(chassis_info_);
      }
    }

    void BSTCanDbcAion::IOControlCmdSend(){//10ms
      AionControl aion_control_cmd_tmp;
      AionTakeover aion_takeover_cmd_tmp;
      std::unique_lock<std::mutex> lock_contorl(control_mux_, std::defer_lock);
      lock_contorl.lock();
      aion_control_cmd_tmp = aion_control_cmd_;
      aion_takeover_cmd_tmp = aion_takeover_;
      lock_contorl.unlock();
      
      IOExceptionHandle();
      ShakeHandCmdHandle(aion_takeover_cmd_tmp, adc_eps_req_);
      ControlCmdHandle(aion_control_cmd_tmp,adc_eps_req_,adc_acc_long_req_);
      LampControl(aion_control_cmd_tmp,scm_req_);

      sendCanMessage();
    }

    void BSTCanDbcAion::sendCanMessage(){
      static uint32_t msg_counter = 0; 
      uint64_t timestampus = 0;
      CanMessage EpsReqCommand(timestampus,"ADC_EPSReq");
      CanMessage longitudinalCommand(timestampus,"ADC_AccLongReq");
      CanMessage SCMReqCommand(timestampus,"SCM_Req");

      setCanMessage(EpsReqCommand ,longitudinalCommand, SCMReqCommand);

      static int32_t seq_pub1 = 0;
      if(++seq_pub1 >=2){//20ms pub
          seq_pub1 = 0;
          EpsReqCommand.signals.emplace_back("ADC_MsgCntr",msg_counter,0.0,true);
          longitudinalCommand.signals.emplace_back("Acc_MsgCntr",msg_counter,0.0,true);
          if (Status::ERROR == Send(EpsReqCommand)) {
            AINFO << __FUNCTION__ << ": Send EpsReqCommand Command failed!";
            std::cout<< __FUNCTION__ << ": Send EpsReqCommand Command failed!"<<std::endl;
          }else{
          if(2 == adc_eps_req_.ADC_EpsSpdDown){
            if((++pub_sero_speed_times_)>20){
              pub_sero_speed_times_=20;
            }
          }
        }
        if (Status::ERROR == Send(longitudinalCommand)) {
          AINFO << __FUNCTION__ << ": Send longitudinal Command failed!";
          std::cout<< __FUNCTION__ << ": Send longitudinal Command failed!"<<std::endl;
        }
        msg_counter++;
        if(msg_counter >= 16){
          msg_counter = 0;
        }
      }
      
      static int32_t seq_pub2 = 0;
      if(++seq_pub2 >=5){//50ms pub
        seq_pub2 = 0;
        if (Status::ERROR == Send(SCMReqCommand)) {
          AINFO << __FUNCTION__ << ": Send SCMReqCommand Command failed!";
          std::cout<< __FUNCTION__ << ": Send SCMReqCommand Command failed!"<<std::endl;
        }
    }
  }

    void BSTCanDbcAion::setCanMessage(CanMessage& eps_can_mesage,CanMessage& longitudinalCommand,
                                                                                  CanMessage& lamp_can_mesage){
        //eps_can_mesage
        eps_can_mesage.signals.emplace_back("ADC_EpsSpdDown",adc_eps_req_.ADC_EpsSpdDown,0.0,true);
        eps_can_mesage.signals.emplace_back("ADC_LatCtrlReq",adc_eps_req_.ADC_LatCtrlReq,0.0,true);
        eps_can_mesage.signals.emplace_back("ADC_SteerAngReq",0,adc_eps_req_.ADC_SteerAngReq,false);
        eps_can_mesage.signals.emplace_back("ADC_SteerTorqReq",0,adc_eps_req_.ADC_SteerTorqReq,false);
        //longitudinalCommand
        longitudinalCommand.signals.emplace_back("ADC_GearReqVD", adc_acc_long_req_.ADC_GearReqVD,0.0,true);
        longitudinalCommand.signals.emplace_back("ADC_GearReq", adc_acc_long_req_.ADC_GearReq,0.0,true);

        longitudinalCommand.signals.emplace_back("Acc_Mode",adc_acc_long_req_.Acc_Mode,0.0,true);
        longitudinalCommand.signals.emplace_back("ADC_LngCtrlReq",adc_acc_long_req_.ADC_LngCtrlReq,0.0,true);
        longitudinalCommand.signals.emplace_back("ADC_VehTrqReq",0.0,adc_acc_long_req_.ADC_VehTrqReq,false);

        longitudinalCommand.signals.emplace_back("Acc_Decel_ReqSts",adc_acc_long_req_.Acc_Decel_ReqSts,0.0,true);
        longitudinalCommand.signals.emplace_back("Acc_Decel_Req",0,adc_acc_long_req_.Acc_Decel_Req,false);
        //lamp_can_mesage
        lamp_can_mesage.signals.emplace_back("SCM_TurnLeftLightReq",scm_req_.SCM_TurnLeftLightReq,0.0,true);
        lamp_can_mesage.signals.emplace_back("SCM_TurnRightLightReq",scm_req_.SCM_TurnRightLightReq,0.0,true);   
    }

    void BSTCanDbcAion::ShakeHandCmdHandle(const AionTakeover& aion_takeover_cmd_tmp,ADC_EPSReq& adc_eps_req){
      if(2 == aion_takeover_cmd_tmp.lat_takeover() && 
          2 == aion_takeover_cmd_tmp.lon_takeover() && 
          chassis_info_ready_.load() && 
          ShakeHandStatus::SHAKEHANDREQQUIT == eps_req_status_ &&
          ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
            resetCanMsg();
            resetControlCmd();
      }
      else if(2 == aion_takeover_cmd_tmp.lon_takeover() && 
          chassis_info_ready_.load() && 
          ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
            resetCanMsg();
            resetControlCmd();
      }

      if(1 == aion_takeover_cmd_tmp.lat_takeover() && chassis_info_ready_.load()){
        if(ShakeHandStatus::DEFAULT == eps_req_status_ || ShakeHandStatus::SHAKEHANDREQQUIT == eps_req_status_){
            if(0 ==chassis_info_.EPS_FaultSts && 0 == chassis_info_.EPS_AngCtrlAbortFeedback){
              eps_req_status_ = ShakeHandStatus::COUNTERPARTSTATUS;
            }
        }
        if(ShakeHandStatus::COUNTERPARTSTATUS == eps_req_status_){
          adc_eps_req.ADC_EpsSpdDown = 1;
          adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
          pub_sero_speed_times_ = 0;
          eps_req_status_ = ShakeHandStatus::SHAKEHANDREQ;
        }
        if(ShakeHandStatus::SHAKEHANDREQ == eps_req_status_){
          adc_eps_req.ADC_EpsSpdDown = 2;
          adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
          if(pub_sero_speed_times_>=10){
            eps_req_status_ = ShakeHandStatus::SHAKEHANDREQ2;
          }
        }
        if(ShakeHandStatus::SHAKEHANDREQ2 == eps_req_status_){
          adc_eps_req.ADC_EpsSpdDown = 2;
          adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
          adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::ANGREQUEST);
            if(3 ==chassis_info_.EPS_LatCtrlMode){
              cmd_angle_pre_ = chassis_info_deal_.eps_steer_angle;
              eps_req_status_ = ShakeHandStatus::SHAKEHANDSUS;
            }
        }
      }
       else if(2 == aion_takeover_cmd_tmp.lat_takeover() && chassis_info_ready_.load()){
        // if(ShakeHandStatus::SHAKEHANDSUS == eps_req_status_)
        {
          adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::NOREQUEST);
          adc_eps_req.ADC_EpsSpdDown = 0;
          adc_eps_req.ADC_SteerAngReq = 0.0;
          adc_eps_req.ADC_SteerTorqReq = 0.0;
            // if(0 ==chassis_info_.EPS_LatCtrlMode){
            eps_req_status_ = ShakeHandStatus::SHAKEHANDREQQUIT;
            // }
        }
       }
       else{
        AINFO << __FUNCTION__ << ": lat_takeover=" << aion_takeover_cmd_tmp.lat_takeover()<<", has_chassis_info="<<chassis_info_ready_.load() ;
      }

      if(1 == aion_takeover_cmd_tmp.lon_takeover() && chassis_info_ready_.load()){
        if(ShakeHandStatus::DEFAULT == lon_req_status_ || ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
          lon_req_status_ = ShakeHandStatus::SHAKEHANDSUS;
        }
      }
      else if(2 == aion_takeover_cmd_tmp.lon_takeover() && chassis_info_ready_.load()){
        if(ShakeHandStatus::SHAKEHANDSUS == lon_req_status_){
          lon_req_status_ = ShakeHandStatus::SHAKEHANDREQQUIT;
        }
      }
      else{
        AINFO << __FUNCTION__ << ": lon_takeover=" << aion_takeover_cmd_tmp.lon_takeover()<<", has_chassis_info="<<chassis_info_ready_.load() ;
      }
    }


    void BSTCanDbcAion::ControlCmdHandle(const AionControl& aion_control_cmd_tmp,ADC_EPSReq& adc_eps_req
                                                                                          ,ADC_AccLongReq& adc_acc_long_req){
      //EPS
      if(aion_control_cmd_tmp.is_steering()){
        double cmd_angle = aion_control_cmd_tmp.tar_steer_angle();
        if(ShakeHandStatus::SHAKEHANDSUS == eps_req_status_ && 0 == eps_fault_type_){
          if(cmd_angle > cmd_angle_pre_ + steering_angle_limit_){
            cmd_angle = cmd_angle_pre_ + steering_angle_limit_;
          }
          else if(cmd_angle < cmd_angle_pre_ - steering_angle_limit_){
            cmd_angle = cmd_angle_pre_ - steering_angle_limit_;
          }
          adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::ANGREQUEST);
          adc_eps_req.ADC_EpsSpdDown = 2;
          adc_eps_req.ADC_SteerTorqReq = 0.0;
          adc_eps_req.ADC_SteerAngReq = cmd_angle;
          cmd_angle_pre_ = cmd_angle;
       }
      }
      //highspeed gear
      if(1 == aion_control_cmd_tmp.is_tar_gear_sts()){
        if(lon_req_status_ == ShakeHandStatus::SHAKEHANDSUS){
          uint32_t cmd_gear = aion_control_cmd_tmp.high_spd_tar_gear();
          adc_acc_long_req.ADC_GearReqVD = static_cast<uint32_t>(ADCGearReqVD::VALID);
          if(cmd_gear == chassis_info_.VCU_CrntGearLvl){//IDNRP 01234    
            adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::NOREQUEST);
          }
          else{
            switch (cmd_gear)//0123 NPRD
            {
            case static_cast<uint32_t>(VCU_CrntGearLvl::Invalid):
              adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::NOREQUEST);
              break;
            case static_cast<uint32_t>(VCU_CrntGearLvl::Drive):
              adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::D);
              break;
            case static_cast<uint32_t>(VCU_CrntGearLvl::Neutral):
              adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::NOREQUEST);
              break;
            case static_cast<uint32_t>(VCU_CrntGearLvl::Reversed):
              adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::R);
              break;
            case static_cast<uint32_t>(VCU_CrntGearLvl::Park):
              adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::P);
              break;
            default:
              break;
            }
          }
        }
      }
       //acc
      if(aion_control_cmd_tmp.is_driving()){
        if(lon_req_status_ == ShakeHandStatus::SHAKEHANDSUS){
          uint32_t cmd_torque = aion_control_cmd_tmp.tar_torque();
          adc_acc_long_req.Acc_Mode = static_cast<uint32_t>(AccMode::ACTIVE);
          adc_acc_long_req.ADC_LngCtrlReq = static_cast<uint32_t>(LngCtrlReq::ACTIVE);
          adc_acc_long_req.ADC_VehTrqReq = cmd_torque;

          adc_acc_long_req.Acc_Decel_ReqSts = static_cast<uint32_t>(AccDecelReqSts::NOREQUEST);
          adc_acc_long_req.Acc_Decel_Req = 0.0;
        }
      }
      //decacc
      if(aion_control_cmd_tmp.is_braking()){
        if(lon_req_status_ == ShakeHandStatus::SHAKEHANDSUS){
          double acc_dec = aion_control_cmd_tmp.tar_deceleration();
          adc_acc_long_req.Acc_Mode = static_cast<uint32_t>(AccMode::OFF);
          adc_acc_long_req.ADC_LngCtrlReq = static_cast<uint32_t>(LngCtrlReq::INACTIVE);
          adc_acc_long_req.ADC_VehTrqReq = 0.0;

          adc_acc_long_req.Acc_Decel_ReqSts = static_cast<uint32_t>(AccDecelReqSts::REQUEST);
          if(acc_dec<-7.0){acc_dec = -7.0;}
          adc_acc_long_req.Acc_Decel_Req = acc_dec;
        }
      }
    }

    void BSTCanDbcAion::resetCanMsg(){
      adc_eps_req_.reset();
      adc_acc_long_req_.reset();
      scm_req_.reset();
    }

    void BSTCanDbcAion::resetControlCmd(){
      std::unique_lock<std::mutex> lock_contorl(control_mux_);
      aion_control_cmd_.set_is_driving(false);
      aion_control_cmd_.set_is_braking(false);
      aion_control_cmd_.set_tar_torque(0.0);
      aion_control_cmd_.set_tar_deceleration(0.0);
      aion_control_cmd_.set_is_steering(false);
      aion_control_cmd_.set_tar_steer_angle(0.0);
      aion_control_cmd_.set_is_tar_gear_sts(0);
      aion_control_cmd_.set_high_spd_tar_gear(0);
      aion_control_cmd_.set_tar_acc_dec(0.0);
      aion_control_cmd_.set_lamp_ctl(0);
    }

    void BSTCanDbcAion::IOExceptionHandle(){
       if(0 !=chassis_info_.EPS_FaultSts || 0 != chassis_info_.EPS_AngCtrlAbortFeedback){
        eps_fault_type_ = 1;
        AINFO<<__FUNCTION__<<": eps_fault_type_="<<eps_fault_type_.load()<<", EPS_FaultSts="<<
                    chassis_info_.EPS_FaultSts<<",EPS_AngCtrlAbortFeedback="<<chassis_info_.EPS_AngCtrlAbortFeedback;
       }
       if(0 != chassis_info_.EPS_AngCtrlAbortFeedback){
        std::cout<<__FUNCTION__<<": eps_fault_type_="<<eps_fault_type_.load()<<", EPS_FaultSts="<<
                    chassis_info_.EPS_FaultSts<<",EPS_AngCtrlAbortFeedback="<<chassis_info_.EPS_AngCtrlAbortFeedback<<std::endl;
       }
    }

    void BSTCanDbcAion::LampControl(const AionControl& aion_control_cmd_tmp,SCM_Req& scm_req){
      uint32_t cmd_lamp = aion_control_cmd_tmp.lamp_ctl();
      switch (cmd_lamp)
        {
          case 0: 
            scm_req.SCM_TurnLeftLightReq = 0;
            scm_req.SCM_TurnRightLightReq = 0;
            break;
          case 1:
            scm_req.SCM_TurnLeftLightReq = 1;
            scm_req.SCM_TurnRightLightReq = 0;
            break;
          case 2:
            scm_req.SCM_TurnLeftLightReq = 0;
            scm_req.SCM_TurnRightLightReq = 1;
            break;
          case 3:
            // SCMReqCommand.signals.emplace_back("SCM_EmeglampReq",1,0.0,true);
            break;
          default:
            break;
        }
    }

    void BSTCanDbcAion::InitSub(){
      auto contorlcmd = nodeCfg.getSubChannel("control_chassisctl"); 
      auto pReaderControlCmd = pNode->CreateReader<AionControl>(contorlcmd.name, 
                                                                              std::bind(&BSTCanDbcAion::ControlCmdCallback,this,std::placeholders::_1)); 
      auto contorltakeover = nodeCfg.getSubChannel("io_takeover"); 
      auto pReaderContorlTakeover = pNode->CreateReader<AionTakeover>(contorltakeover.name, 
                                                                              std::bind(&BSTCanDbcAion::ControlTakeoveCallback,this,std::placeholders::_1));
    }

    void BSTCanDbcAion::ControlCmdCallback(const std::shared_ptr <AionControl>& msg){
      std::string json_string;
      google::protobuf::util::MessageToJsonString(*msg, &json_string);
      AINFO << __FUNCTION__ << json_string;
      std::unique_lock<std::mutex> lock_contorl(control_mux_);
      aion_control_cmd_ = *msg;
    }

    void BSTCanDbcAion::ControlTakeoveCallback(const std::shared_ptr <AionTakeover>& msg){
      std::string json_string;
      google::protobuf::util::MessageToJsonString(*msg, &json_string);
      AINFO << __FUNCTION__ << json_string;
      std::unique_lock<std::mutex> lock_lampctl(control_mux_);
      aion_takeover_ = *msg;
    }

    void BSTCanDbcAion::InitPub(){
      if (nullptr == pWriterChassisInfo) {
        auto IoChassisInfo = nodeCfg.getPubChannel("io_chassisinfo");
        pWriterChassisInfo = pNode->CreateWriter<AionChassisInfo>(IoChassisInfo.name);
      }
      if(nullptr == pWriterWheelSpeed){
        auto IoWheelSpeed = nodeCfg.getPubChannel("io_wheelspeed");
        pWriterWheelSpeed = pNode->CreateWriter<AionWheelSpeed>(IoWheelSpeed.name);
      }
    }

    void BSTCanDbcAion::IOChassisInfoSend(ChassisInfo& chassis_info){
      static uint32_t seq = 0;
      RecordSourceChassisInfo(chassis_info);

      
      if(Status::ERROR == DealChassisInfo(chassis_info_deal_)){
        AINFO << __FUNCTION__ << ": deal chassis info failed.";
      }

      RecordDealChassisInfo(chassis_info_deal_);

      AionChassisInfo aion_chassis_info;
      auto header = aion_chassis_info.mutable_header();
      header->set_timestamp(getTimeS(nullptr));
      header->set_module_name("aion_io_node");
      header->set_sequence_num(seq);
      header->set_version(0);
      seq++;

      aion_chassis_info.set_velocity(chassis_info_deal_.velocity);

      aion_chassis_info.set_lon_acceleration(chassis_info_deal_.lon_acceleration);
      aion_chassis_info.set_lat_acceleration(chassis_info_deal_.lat_acceleration);
      aion_chassis_info.set_yaw_rate(chassis_info_deal_.yaw_rate);

      aion_chassis_info.set_brake_system_status(chassis_info_deal_.brake_system_status);
      aion_chassis_info.set_brake_padal_position(chassis_info_deal_.brake_padal_position);

      aion_chassis_info.set_eps_steer_angle(chassis_info_deal_.eps_steer_angle);
      aion_chassis_info.set_eps_steer_angle_speed(chassis_info_deal_.eps_steer_angle_speed);
      aion_chassis_info.set_eps_fault_status(chassis_info_deal_.eps_fault_status);
      aion_chassis_info.set_eps_lat_ctl_mode(chassis_info_deal_.eps_lat_ctl_mode);

      aion_chassis_info.set_vcu_current_gear_level(chassis_info_deal_.vcu_current_gear_level);
      aion_chassis_info.set_vcu_actual_vehicle_wheel_torque(chassis_info_deal_.vcu_actual_vehicle_wheel_torque);

      aion_chassis_info.set_bcm_turn_lamp_status(chassis_info_deal_.bcm_turn_lamp_status);

      aion_chassis_info.set_wheel_speed_front_left(chassis_info_deal_.wheel_speed_front_left);
      aion_chassis_info.set_wheel_speed_front_right(chassis_info_deal_.wheel_speed_front_right);
      aion_chassis_info.set_wheel_speed_rear_left(chassis_info_deal_.wheel_speed_rear_left);
      aion_chassis_info.set_wheel_speed_rear_right(chassis_info_deal_.wheel_speed_rear_right);
      aion_chassis_info.set_speed(chassis_info_deal_.speed);

      aion_chassis_info.set_eps_takeover_status(static_cast<uint32_t>(eps_req_status_));
      aion_chassis_info.set_lon_takeover_status(static_cast<uint32_t>(lon_req_status_));
      aion_chassis_info.set_exception_type(eps_fault_type_);
      aion_chassis_info.set_eps_angctrl_abort_feedback(chassis_info.EPS_AngCtrlAbortFeedback);

      pWriterChassisInfo->Write(aion_chassis_info);

      std::string json_string;
      google::protobuf::util::MessageToJsonString(aion_chassis_info, &json_string);
      AINFO << __FUNCTION__ << json_string;
    }

    Status BSTCanDbcAion::UpdateChassisInfo(){

      AINFO << __FUNCTION__ << ": dbc_recv_start.";
      // auto can_msg = dbc_.Recv();
      auto can_msg = decoded_message;
      AINFO << __FUNCTION__ << ": dbc_recv_finish.";

      if(can_msg == nullptr){
        return Status::ERROR;
      }
      AINFO << __FUNCTION__ << " timestamp is "<<can_msg->timestamp;
      // std::cout<<"i = "<<i<<", can_msg->name = "<<can_msg->name<<std::endl;
      if("AcmBody" == can_msg->name){
        AINFO << __FUNCTION__ << ": praseAcmBody.";
        PraseAcmBody(can_msg);
        chassis_info_ready_ = true;
      }
      else if("BrkSymSts" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseBrkSymSts.";
        chassis_info_ready_ = true;
        PraseBrkSymSts(can_msg);
      }
      else if("EPB_02" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseEPB_02.";
        chassis_info_ready_ = true;
        PraseEPB_02(can_msg);
      }
      else if("EPSSts" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseEPSSts.";
        chassis_info_ready_ = true;
        PraseEPSSts(can_msg);
      }
      else if("VCU_13" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseVCU_13.";
        chassis_info_ready_ = true;
        PraseVCU_13(can_msg);
      }
      else if("WHLspdFront" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseWHLspdFront.";
        chassis_info_ready_ = true;
        PraseWHLspdFront(can_msg);
      }
      else if("WHLspdRear" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseWHLspdRear.";
        chassis_info_ready_ = true;
        PraseWHLspdRear(can_msg);
      }
      else if("VehicleLights" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseVehicleLights.";
        chassis_info_ready_ = true;
        PraseVehicleLights(can_msg);
      }
      else{
        AINFO << __FUNCTION__ << ": PraseOtherMsg -> "<< can_msg->name;
      }

      if(false == chassis_info_ready_){
        return Status::ERROR;
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseAcmBody(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("LatitudeAcc" == s.name){
          chassis_info_.LatitudeAcc = s.physical*G;
          AINFO << __FUNCTION__ << ": Cur_LatitudeAcc=" << s.physical;
        }
        else if("LatitudeAcc" == s.name){
          chassis_info_.YawRate = s.physical;
          AINFO << __FUNCTION__ << ": Cur_YawRate=" << s.physical;
        }
        else if("LatitudeAcc" == s.name){
          chassis_info_.LongitudeAcc = s.physical*G;
          AINFO << __FUNCTION__ << ": Cur_LongitudeAcc=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseBrkSymSts(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("CDDSActv" == s.name){
          chassis_info_.CDDSActv = s.physical;
          AINFO << __FUNCTION__ << ": Cur_CDDSActv=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseEPB_02(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("Vehspd" == s.name){
          chassis_info_.Vehspd = s.physical;
          AINFO << __FUNCTION__ << ": Cur_Vehspd=" << s.physical;
        }
        else if("BrkPedPst" == s.name){
          chassis_info_.BrkPedPst = s.physical;
          AINFO << __FUNCTION__ << ": Cur_BrkPedPst=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseEPSSts(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("EPS_Steer_angledir" == s.name){
          chassis_info_.EPS_Steer_angledir = s.physical;
          AINFO << __FUNCTION__ << ": Cur_EPS_Steer_angledir=" << s.physical;
        }
        else if("EPS_Steeragspddir" == s.name){
          chassis_info_.EPS_Steeragspddir = s.physical;
          AINFO << __FUNCTION__ << ": Cur_EPS_Steeragspddir=" << s.physical;
        }
        else if("EPS_SteeringAngle" == s.name){
          chassis_info_.EPS_SteeringAngle = s.physical;
          AINFO << __FUNCTION__ << ": Cur_EPS_SteeringAngle=" << s.physical;
        }
        else if("EPS_FaultSts" == s.name){
          chassis_info_.EPS_FaultSts = s.physical;
          AINFO << __FUNCTION__ << ": Cur_EPS_FaultSts=" << s.physical;
        }
        else if("EPS_LatCtrlMode" == s.name){
          chassis_info_.EPS_LatCtrlMode = s.physical;
          AINFO << __FUNCTION__ << ": Cur_EPS_LatCtrlMode=" << s.physical;
        }
        else if("EPS_SteeringAngleSpd" == s.name){
          chassis_info_.EPS_SteeringAngleSpd = s.physical;
          AINFO << __FUNCTION__ << ": Cur_EPS_SteeringAngleSpd=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseVCU_13(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("VCU_CrntGearLvl" == s.name){
          chassis_info_.VCU_CrntGearLvl = s.physical;
          AINFO << __FUNCTION__ << ": Cur_VCU_CrntGearLvl=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseWHLspdFront(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("WhlspdFLdir" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFLdir = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdFLdir=" << s.physical;
        }
        else if("WhlspdFLsts" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFLsts = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdFLsts=" << s.physical;
        }
        else if("WhlspdFR" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFR = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdFR=" << s.physical;
        }
        else if("WhlspdFRdir" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFRdir = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdFRdir=" << s.physical;
        }
        else if("WhlspdFRsts" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFRsts = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdFRsts=" << s.physical;
        }
        else if("WhlspdFL" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFL = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdFL=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseWHLspdRear(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("WhlspdRLdir" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRLdir = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdRLdir=" << s.physical;
        }
        else if("WhlspdRLsts" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRLsts = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdRLsts=" << s.physical;
        }
        else if("WhlspdRR" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRR = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdRR=" << s.physical;
        }
        else if("WhlspdRRdir" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRRdir = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdRRdir=" << s.physical;
        }
        else if("WhlspdRRsts" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRRsts = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdRRsts=" << s.physical;
        }
        else if("WhlspdRL" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRL = s.physical;
          AINFO << __FUNCTION__ << ": Cur_WhlspdRL=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::PraseVehicleLights(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("BCM_TurnLampSt" == s.name){
          chassis_info_.BCM_TurnLampSt = s.physical;
          AINFO << __FUNCTION__ << ": Cur_BCM_TurnLampSt=" << s.physical;
        }
        else{
          AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status BSTCanDbcAion::DealChassisInfo() {
      
      ChassisInfoDeal chassis_info_deal;
      DealChassisInfo(chassis_info_deal);

      if(chassis_info_ready_.load()){
        return Status::OK;
      }
      else{
        AINFO << __FUNCTION__ << "Cant find valid information.";
        return Status::ERROR;
      }
    }

    Status BSTCanDbcAion::DealChassisInfo(ChassisInfoDeal& chassis_info_deal){
      static double last_eps_steer_angle = 0.0;
      static bool first_set_steer_angle_flag = false;
      static bool steer_angle_speed_dir = false;
      chassis_info_deal.velocity = chassis_info_.Vehspd;
      chassis_info_deal.lon_acceleration = chassis_info_.LongitudeAcc;
      chassis_info_deal.lat_acceleration = chassis_info_.LatitudeAcc;
      chassis_info_deal.yaw_rate = chassis_info_.YawRate;
      chassis_info_deal.brake_system_status = chassis_info_.CDDSActv;
      chassis_info_deal.brake_padal_position = chassis_info_.BrkPedPst;
      chassis_info_deal.eps_steer_angle = (chassis_info_.EPS_Steer_angledir == 0)?(-chassis_info_.EPS_SteeringAngle):(chassis_info_.EPS_SteeringAngle);
      if(false == first_set_steer_angle_flag){
        first_set_steer_angle_flag = true;
        last_eps_steer_angle = chassis_info_deal.eps_steer_angle;
      }
      // bool steer_angle_speed_dir = static_cast<bool>(chassis_info_deal.eps_steer_angle - last_eps_steer_angle > 0);
      if(chassis_info_deal.eps_steer_angle - last_eps_steer_angle > 0){
        steer_angle_speed_dir = true;
      }
      else if(chassis_info_deal.eps_steer_angle - last_eps_steer_angle < 0){
        steer_angle_speed_dir = false;
      }
      chassis_info_deal.eps_steer_angle_speed = (steer_angle_speed_dir == false)?(chassis_info_.EPS_SteeringAngleSpd):(-chassis_info_.EPS_SteeringAngleSpd);
      chassis_info_deal.eps_fault_status = chassis_info_.EPS_FaultSts;
      chassis_info_deal.eps_lat_ctl_mode = chassis_info_.EPS_LatCtrlMode;
      chassis_info_deal.vcu_current_gear_level = chassis_info_.VCU_CrntGearLvl;
      chassis_info_deal.vcu_actual_vehicle_wheel_torque = chassis_info_.VCU_ActVehWheelTorq;
      chassis_info_deal.bcm_turn_lamp_status = chassis_info_.BCM_TurnLampSt;
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        chassis_info_deal.wheel_speed_front_left = chassis_info_.WheelSpeedFront.WhlspdFL;
      }
      else if(1 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        chassis_info_deal.wheel_speed_front_left = -chassis_info_.WheelSpeedFront.WhlspdFL;
      }
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        chassis_info_deal.wheel_speed_front_right = chassis_info_.WheelSpeedFront.WhlspdFR;
      }
      else if(1 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        chassis_info_deal.wheel_speed_front_right = -chassis_info_.WheelSpeedFront.WhlspdFR;
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        chassis_info_deal.wheel_speed_rear_left = chassis_info_.WheelSpeedRear.WhlspdRL;
      }
      else if(1 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        chassis_info_deal.wheel_speed_rear_left = -chassis_info_.WheelSpeedRear.WhlspdRL;
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        chassis_info_deal.wheel_speed_rear_right = chassis_info_.WheelSpeedRear.WhlspdRR;
      }
      else if(1 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        chassis_info_deal.wheel_speed_rear_right = chassis_info_.WheelSpeedRear.WhlspdRR;
      }
      chassis_info_deal.speed = (chassis_info_deal.wheel_speed_rear_right+chassis_info_deal.wheel_speed_rear_left)/2.0;

      last_eps_steer_angle = chassis_info_deal.eps_steer_angle;
      return Status::OK;
    }

    Status BSTCanDbcAion::Send(CanMessage& msg)  {
        unsigned int can_id = 0x0;
        std::cout << "!!!!!!!!!!!send!!!!!!!!!!!" << std::endl;
        auto& messages = dbc_network.messages;
        for (auto it = messages.begin(); it != messages.end(); ++it) {
          if (it->second.name == msg.name) {
            can_id = it->second.id;
          }
        }
        if (can_id == 0x0)
          return Status::ERROR;

        std::vector<std::uint8_t> canData;
        std::uint8_t size = messages[can_id].size;
        canData.resize(size);
        for (auto& s : msg.signals) {
          if (!s.use_raw)
            s.raw = messages[can_id].signals[s.name].physicalToRawValue(s.physical);
          messages[can_id].signals[s.name].encode(canData, s.raw);
        }

        uint8_t can_data_buf[size];
        std::memcpy(can_data_buf, canData.data(), size);
        can_instance.Write(CAN_DEV_0, can_id, size, can_data_buf);
        std::cout << std::endl;
        for (size_t i = 0; i < size; i++)
        {
            std::cout << "  " << (int)canData[i] ;
        }
        std::cout << std::endl;
        
        return Status::OK;
      }

    bool BSTCanDbcAion::dataMonitor(){
        return true;
    }

    void BSTCanDbcAion::ChassisInfoReset(){
      chassis_info_.LatitudeAcc = 0.0;chassis_info_.YawRate = 0.0;chassis_info_.LongitudeAcc = 0.0; 
      chassis_info_.CDDSActv = 0;
      chassis_info_.Vehspd = 0.0;chassis_info_.BrkPedPst = 0.0;
      chassis_info_.EPS_Steer_angledir = 0;chassis_info_.EPS_Steeragspddir = 0;chassis_info_.EPS_SteeringAngle = 0.0;
      chassis_info_.EPS_FaultSts = 0;chassis_info_.EPS_ToqCtrlAbortFeedback = 0;chassis_info_.EPS_LatCtrlMode = 0;
      chassis_info_.EPS_SteeringAngleSpd = 0;chassis_info_.EPS_AngCtrlAbortFeedback = 0;
      chassis_info_.VCU_CrntGearLvl = 0;chassis_info_.VCU_ActVehWheelTorq = 0;
      chassis_info_.WheelSpeedFront.WF_CRC = 0;chassis_info_.WheelSpeedFront.WF_MsgCntr = 0;chassis_info_.WheelSpeedFront.WhlspdFL = 0;
      chassis_info_.WheelSpeedFront.WhlspdFLdir = 0;chassis_info_.WheelSpeedFront.WhlspdFLsts = 0;chassis_info_.WheelSpeedFront.WhlspdFR = 0;
      chassis_info_.WheelSpeedFront.WhlspdFRdir = 0;chassis_info_.WheelSpeedFront.WhlspdFRsts = 0;
      chassis_info_.WheelSpeedRear.WR_CRC = 0;chassis_info_.WheelSpeedRear.WR_MsgCntr = 0;chassis_info_.WheelSpeedRear.WhlspdRL = 0;
      chassis_info_.WheelSpeedRear.WhlspdRLdir = 0;chassis_info_.WheelSpeedRear.WhlspdRLsts = 0;chassis_info_.WheelSpeedRear.WhlspdRR = 0;
      chassis_info_.WheelSpeedRear.WhlspdRRdir = 0;chassis_info_.WheelSpeedRear.WhlspdRRsts = 0;
    }

    void BSTCanDbcAion::RecordSourceChassisInfo(ChassisInfo& chassis_info_tmp ){
      AINFO << __FUNCTION__ << ": LatitudeAcc=" << chassis_info_tmp.LatitudeAcc << ", LongitudeAcc=" << chassis_info_tmp.LongitudeAcc
      << ", YawRate=" << chassis_info_tmp.YawRate << ", CDDSActv=" << chassis_info_tmp.CDDSActv
      << ", Vehspd=" << chassis_info_tmp.Vehspd << ", BrkPedPst=" << chassis_info_tmp.BrkPedPst
      << ", EPS_Steer_angledir=" << chassis_info_tmp.EPS_Steer_angledir << ", EPS_Steeragspddir=" << chassis_info_tmp.EPS_Steeragspddir
      << ", EPS_SteeringAngle=" << chassis_info_tmp.EPS_SteeringAngle << ", EPS_FaultSts=" << chassis_info_tmp.EPS_FaultSts
      << ", EPS_ToqCtrlAbortFeedback=" << chassis_info_tmp.EPS_ToqCtrlAbortFeedback << ", EPS_LatCtrlMode=" << chassis_info_tmp.EPS_LatCtrlMode
      << ", EPS_SteeringAngleSpd=" << chassis_info_tmp.EPS_SteeringAngleSpd << ", EPS_AngCtrlAbortFeedback=" << chassis_info_tmp.EPS_AngCtrlAbortFeedback
      << ", VCU_CrntGearLvl=" << chassis_info_tmp.VCU_CrntGearLvl << ", VCU_ActVehWheelTorq=" << chassis_info_tmp.VCU_ActVehWheelTorq
      << ", WhlspdRLdir=" << chassis_info_tmp.WheelSpeedRear.WhlspdRLdir << ", WhlspdRLsts=" << chassis_info_tmp.WheelSpeedRear.WhlspdRLsts
      << ", WhlspdRL=" << chassis_info_tmp.WheelSpeedRear.WhlspdRL << ", WhlspdRRdir=" << chassis_info_tmp.WheelSpeedRear.WhlspdRRdir
      << ", WhlspdRRsts=" << chassis_info_tmp.WheelSpeedRear.WhlspdRRsts << ", WhlspdRR=" << chassis_info_tmp.WheelSpeedRear.WhlspdRR;
    }

    void BSTCanDbcAion::RecordDealChassisInfo(ChassisInfoDeal& chassis_info_deal){
      AINFO << __FUNCTION__ << ": velocity=" << chassis_info_deal.velocity << ", lon_acceleration=" << chassis_info_deal.lon_acceleration
      << ", lat_acceleration=" << chassis_info_deal.lat_acceleration << ", yaw_rate=" << chassis_info_deal.yaw_rate
      << ", brake_system_status=" << chassis_info_deal.brake_system_status << ", brake_padal_position=" << chassis_info_deal.brake_padal_position
      << ", eps_steer_angle=" << chassis_info_deal.eps_steer_angle << ", eps_steer_angle_speed=" << chassis_info_deal.eps_steer_angle_speed
      << ", eps_fault_status=" << chassis_info_deal.eps_fault_status << ", eps_lat_ctl_mode=" << chassis_info_deal.eps_lat_ctl_mode
      << ", vcu_current_gear_level=" << chassis_info_deal.vcu_current_gear_level << ", vcu_actual_vehicle_wheel_torque=" << chassis_info_deal.vcu_actual_vehicle_wheel_torque
      << ", bcm_turn_lamp_status=" << chassis_info_deal.bcm_turn_lamp_status << ", wheel_speed_front_left=" << chassis_info_deal.wheel_speed_front_left
      << ", wheel_speed_front_right=" << chassis_info_deal.wheel_speed_front_right << ", wheel_speed_rear_left=" << chassis_info_deal.wheel_speed_rear_left
      << ", wheel_speed_rear_left=" << chassis_info_deal.wheel_speed_rear_left << ", wheel_speed_rear_right=" << chassis_info_deal.wheel_speed_rear_right
      << ", speed=" << chassis_info_deal.speed;
    }

}

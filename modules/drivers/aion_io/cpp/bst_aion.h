#ifndef BST_AION_H_
#define BST_AION_H_

#include <atomic>
#include <iostream>
#include <thread>             // std::thread
#include <mutex>              // std::mutex, std::unique_lock
#include <condition_variable> // std::condition_variable

#include <google/protobuf/util/json_util.h>
#include "base/util/errno.h"
#include "base/util/module_base.hpp"
#include "base/util/config_parser.hpp"
#include "base/util/app_template.hpp"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_chassis.pb.h"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_control.pb.h"
#include "chassis_info.h"
#include "dbc_data_type.h"

#include <autoplt/include/ADSNode.h>
#include <autoplt/include/ADSTime.h>
#include "cyber/common/file.h"
#include <modules/sensor_hal/can_hal/can_hal.h>
#include <Vector/DBC.h>

using namespace autoplt;
using namespace autoplt::sensor_manager::hal;
using aion_io::chassisinfo::AionChassisInfo;
using aion_io::chassisinfo::AionWheelSpeed;
using aion_io::contorl::AionControl;
using aion_io::contorl::AionStart;
using aion_io::contorl::AionLampCtl;
using aion_io::contorl::AionTakeover;

#define G 9.8

namespace bst_aion
{

    class BSTCanDbcAion: public ModuleBase
    {
    public:
        explicit BSTCanDbcAion(std::shared_ptr <ADSNode> node, NodeCfg& nodecfg, const char* dbc_path);
        ~BSTCanDbcAion();

        bool dataMonitor();

    private:
        void StartDBC(const char *dbc_path);
        Status Send(CanMessage& msg);

        void InitCan();
        void InitChassisInfoSend();
        void InitSub();
        void InitPub();

        void ControlCmdCallback(const std::shared_ptr <AionControl>& msg);
        void ControlTakeoveCallback(const std::shared_ptr <AionTakeover>& msg);

        void ShakeHandCmdHandle(const AionTakeover& aion_takeover_cmd_tmp,ADC_EPSReq& adc_eps_req);
        void ControlCmdHandle(const AionControl& aion_control_cmd_tmp,ADC_EPSReq& adc_eps_req,
                                                            ADC_AccLongReq& adc_acc_long_req);
        void LampControl(const AionControl& aion_control_cmd_tmp,SCM_Req& scm_req);
        void sendCanMessage();
        void setCanMessage(CanMessage& eps_can_mesage,CanMessage& longitudinalCommand,CanMessage& lamp_can_mesage);

        void InitTimer();
        void IOChassisInfoRecv();
        void IOControlCmdSend();
        void IOChassisInfoSend(ChassisInfo& chassis_info);

        void resetCanMsg();
        void resetControlCmd();
        void IOExceptionHandle();

        Status UpdateChassisInfo();
        Status DealChassisInfo();
        Status DealChassisInfo(ChassisInfoDeal& chassis_info_deal);
        Status PraseAcmBody(std::shared_ptr<CanMessage>& canmsg);
        Status PraseBrkSymSts(std::shared_ptr<CanMessage>& canmsg);
        Status PraseEPB_02(std::shared_ptr<CanMessage>& canmsg);
        Status PraseEPSSts(std::shared_ptr<CanMessage>& canmsg);
        Status PraseVCU_13(std::shared_ptr<CanMessage>& canmsg);
        Status PraseWHLspdFront(std::shared_ptr<CanMessage>& canmsg);
        Status PraseWHLspdRear(std::shared_ptr<CanMessage>& canmsg);
        Status PraseVehicleLights(std::shared_ptr<CanMessage>& canmsg);

        void ChassisInfoReset();
        void RecordSourceChassisInfo(ChassisInfo& chassis_info_tmp);
        void RecordDealChassisInfo(ChassisInfoDeal& chassis_info_deal);

    private:
        std::shared_ptr <ADSNode> pNode = nullptr;
        NodeCfg nodeCfg;
        ChassisInfo chassis_info_;
        ChassisInfoDeal chassis_info_deal_;
        std::shared_ptr <apollo::cyber::Timer> pTimerIOChassisInfoRecv = nullptr;//2ms
        std::shared_ptr <apollo::cyber::Timer> pTimerIOControlCmdSend = nullptr;//10ms
        std::atomic<bool> chassis_info_ready_{false};
        ShakeHandStatus eps_req_status_ = ShakeHandStatus::DEFAULT;
        ShakeHandStatus lon_req_status_ = ShakeHandStatus::DEFAULT;
        std::shared_ptr<Writer <AionChassisInfo>> pWriterChassisInfo = nullptr;
        std::shared_ptr<Writer <AionWheelSpeed>> pWriterWheelSpeed = nullptr;
        std::mutex control_mux_;
        AionControl aion_control_cmd_;
        AionTakeover aion_takeover_;
        double steering_angle_limit_ = 9.0;
        double cmd_angle_pre_ = 0.0;
        ADC_EPSReq adc_eps_req_;
        ADC_AccLongReq adc_acc_long_req_;
        SCM_Req scm_req_;
        std::atomic<uint32_t> eps_fault_type_{0};
        uint32_t pub_sero_speed_times_ = 0;
        std::thread writeChassisInfo;
    };

} // namespace bst_aion

#endif
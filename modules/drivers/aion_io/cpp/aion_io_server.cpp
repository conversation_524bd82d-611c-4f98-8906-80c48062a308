#include "zlgcandbc_aion.h"

std::shared_ptr<zlgcandbc_aion::ZlgCanDbcAion> pIOControl = nullptr;

int main(int argc, char* argv[]) 
{
   adas::app_template app;
    namespace bpo = boost::program_options;
    std::cout <<"cplusplus : "<<__cplusplus<< std::endl;
    // clang-off
    app.add_options()
     ("node_name",bpo::value<std::string>()->default_value(""),"apa control node name")
     ("node_cfg",bpo::value<std::string>()->default_value("conf/aion_io_server.json"),"node config json file")
     ("dbc_file",bpo::value<std::string>()->default_value("conf/Client_1210.dbc"),"node config json file")
     ("conf",bpo::value<std::string>()->default_value("conf/aion_io.ini"), "template");
    // clang-on
    if(const auto &ret = app.run(argc,argv,"conf") ; ret != 0){
        if(ret == 1){
            std::cout <<"show help!"<< std::endl;
            return 0;
        }
        std::cout <<"command_line or conf_file parse failed !"<< std::endl;
        return -1;
    }
    auto &&config = app.configuration();
    auto node_cfg = config["node_cfg"].as<std::string>();
    auto dbc_file = config["dbc_file"].as<std::string>();
    auto node_name = config["node_name"].as<std::string>();
    bpo::notify(config);

    std::cout <<"node_name : "<<node_name<< std::endl;
    std::cout <<"dbc_file : "<<dbc_file<< std::endl;
    std::cout <<"node_cfg : "<<node_cfg<< std::endl;

    AINFO << __FUNCTION__ << "argc " << argc;
    
#if 0 
 if (argc != 2) {
    std::cout << "Usage: ./zlgcandbc_io_serve /path/to/xxx.dbc" << std::endl;
    return 1;
  }
#endif

  std::cout << "Waitting for init..." << std::endl;
  apollo::cyber::Init(argv[0]);

  NodeCfg nodeCfg(node_cfg);
  if (!nodeCfg.isValid()) {
      return 1; 
  }

  if(node_name == ""){
    node_name = nodeCfg.getName();
  }
  std::shared_ptr<Node>node(apollo::cyber::CreateNode(node_name));

  if(nullptr == pIOControl){
      try{
          pIOControl = std::make_shared<zlgcandbc_aion::ZlgCanDbcAion>(node,nodeCfg,dbc_file.c_str());
      }
      catch(const std::exception& e) {
          AINFO << __FUNCTION__ << e.what() << '\n';
          return 1;
      }
  }

  std::cout << "Init done. Enjoy." << std::endl;
  apollo::cyber::WaitForShutdown();
}
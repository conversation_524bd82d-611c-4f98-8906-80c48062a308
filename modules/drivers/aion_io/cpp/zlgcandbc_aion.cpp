#include "zlgcandbc_aion.h"

namespace zlgcandbc_aion
{

    ZlgCanDbcAion::ZlgCanDbcAion(std::shared_ptr <Node> node, NodeCfg& nodecfg, const char* dbc_path)
    :pNode(node),
    nodeCfg(nodecfg){
        InitConfigure();
        ClearBuffer();
        StartDBC(dbc_path);
        ClearBuffer();
        for (int i=0; i<100; i++) {
          dbc_.Recv();
          usleep(1000);
        }
        // InitConfigure();
        ChassisInfoReset();
        resetCanMsg();
        resetControlCmd();
        InitSub();
        InitPub();
        InitTimer();
    }

    ZlgCanDbcAion::~ZlgCanDbcAion(){
        ClearBuffer();
        StopDBC();
    }

    bool ZlgCanDbcAion::InitConfigure(){
      std::string file_path = "conf/control_conf.pb.txt";
      if(false == apollo::cyber::common::GetProtoFromFile(file_path, &control_conf_)){
          AERROR << __func__<< ": Unable to load control conf file: " <<file_path;
          std::exit(0);
      }
      auto lat_control_interpolation = control_conf_.lat_pid_controller_conf().lat_calibration_table();
      AINFO << "lat_control_interpolation calibration_size is "
                  << lat_control_interpolation.calibration_size();
      for (const auto &calibration : lat_control_interpolation.calibration()) {
          steer_angle_linear_interpolation_.emplace_back(std::make_pair(calibration.steer_angle(),
                                      calibration.steer_wheel_angle()));
      }
      AINFO << "lat_control_interpolation init sucess...";
      return true;  
    }

    void ZlgCanDbcAion::InitTimer(){
      if (pTimerIOChassisInfoRecv == nullptr) {
        pTimerIOChassisInfoRecv = std::make_shared<apollo::cyber::Timer>(4, std::bind(&ZlgCanDbcAion::IOChassisInfoRecv,this), false);
        pTimerIOChassisInfoRecv->Start();
        ClearBuffer();
      }
      if (pTimerIOControlCmdSend == nullptr) {
        pTimerIOControlCmdSend = std::make_shared<apollo::cyber::Timer>(10, std::bind(&ZlgCanDbcAion::IOControlCmdSend,this), false);
        pTimerIOControlCmdSend->Start();
      }
    }

    void ZlgCanDbcAion::StartDBC(const char *dbc_path){
        if (dbc_.Init(0, dbc_path) == zlgcandbc::FAILED) {
          throw std::runtime_error("Init ZlgcanDbc instance failed");
        }

        if (dbc_.Start(0) == zlgcandbc::FAILED) {
          dbc_.Stop();
          if (dbc_.Start(0) == zlgcandbc::FAILED) {
            throw std::runtime_error("Start channel 0 failed");
          }
        }
    }

    void ZlgCanDbcAion::StopDBC(){
        dbc_.Stop();
        dbc_.Deinit();
    }

    Status ZlgCanDbcAion::ClearBuffer(){
        if(dbc_.ClearBuffer() == zlgcandbc::ErrorCode::SUCCESS){
            return Status::OK;
        }
        AINFO << __FUNCTION__ << "Clear dbc buffer failed. ";
        return Status::ERROR;
    }

    void ZlgCanDbcAion::IOChassisInfoRecv(){
      // update chassis info
      if(Status::ERROR == UpdateChassisInfo()){
        AINFO << __FUNCTION__ << ": update chassis info failed.";
      }
      //deal and send chassis info
      IOChassisInfoSend(chassis_info_);
    }

    void ZlgCanDbcAion::IOControlCmdSend(){//10ms
      static int32_t seq = 0;
      if(seq >= 100){
        AERROR << "AION IO CONTROL IS RUNNING....";
        seq = 0;
      }
      else{
        seq++;
      }
      LatAndLongControlCmd aion_control_cmd_tmp;
      LatAndLongShakeHandCmd aion_takeover_cmd_tmp;
      std::unique_lock<std::mutex> lock_contorl(control_mux_, std::defer_lock);
      lock_contorl.lock();
      aion_control_cmd_tmp = aion_control_cmd_;
      aion_takeover_cmd_tmp = aion_takeover_;
      lock_contorl.unlock();
      
      IOExceptionHandle();
      ShakeHandCmdHandle(aion_takeover_cmd_tmp, adc_eps_req_);
      ControlCmdHandle(aion_control_cmd_tmp,adc_eps_req_,adc_acc_long_req_);
      // if ((aeb_ctrl_.AEBStatus != 0) && (!aeb_ctrl_.EgoCarStop))
      // {
      //   adc_acc_long_req_.ADC_LngCtrlReq = 0;
      //   adc_acc_long_req_.ADC_VehTrqReq = 0;
      //   adc_acc_long_req_.Acc_Decel_Req = aeb_ctrl_.Deceleration;
      //   adc_acc_long_req_.Acc_Decel_ReqSts = 1;
      //   AINFO << __FUNCTION__ << ":Acc_Decel_Req=" << adc_acc_long_req_.Acc_Decel_Req;
      // }
      LampControl(aion_control_cmd_tmp,scm_req_);

      sendCanMessage();
    }

    void ZlgCanDbcAion::sendCanMessage(){
      static uint32_t msg_counter = 0; 
      uint64_t timestampus = 0;
      CanMessage EpsReqCommand(timestampus,"ADC_EPSReq");
      CanMessage longitudinalCommand(timestampus,"ADC_AccLongReq");
      CanMessage SCMReqCommand(timestampus,"SCM_Req");

      setCanMessage(EpsReqCommand ,longitudinalCommand, SCMReqCommand);

      static int32_t seq_pub1 = 0;
      if(++seq_pub1 >=2){//20ms pub
          seq_pub1 = 0;
          EpsReqCommand.signals.emplace_back("ADC_MsgCntr",msg_counter,0.0,true);
          longitudinalCommand.signals.emplace_back("Acc_MsgCntr",msg_counter,0.0,true);
          if (Status::ERROR == Send(EpsReqCommand)) {
            AINFO << __FUNCTION__ << ": Send EpsReqCommand Command failed!";
            std::cout<< __FUNCTION__ << ": Send EpsReqCommand Command failed!"<<std::endl;
          }else{
          if(2 == adc_eps_req_.ADC_EpsSpdDown){
            if((++pub_sero_speed_times_)>20){
              pub_sero_speed_times_=20;
            }
          }
        }
        if (Status::ERROR == Send(longitudinalCommand)) {
          AINFO << __FUNCTION__ << ": Send longitudinal Command failed!";
          std::cout<< __FUNCTION__ << ": Send longitudinal Command failed!"<<std::endl;
        }
        msg_counter++;
        if(msg_counter >= 16){
          msg_counter = 0;
        }
      }
      
      static int32_t seq_pub2 = 0;
      if(++seq_pub2 >=5){//50ms pub
        seq_pub2 = 0;
        if (Status::ERROR == Send(SCMReqCommand)) {
          AINFO << __FUNCTION__ << ": Send SCMReqCommand Command failed!";
          std::cout<< __FUNCTION__ << ": Send SCMReqCommand Command failed!"<<std::endl;
        }
    }
  }

    void ZlgCanDbcAion::setCanMessage(CanMessage& eps_can_mesage,CanMessage& longitudinalCommand,
                                                                                  CanMessage& lamp_can_mesage){
        //eps_can_mesage
        eps_can_mesage.signals.emplace_back("ADC_EpsSpdDown",adc_eps_req_.ADC_EpsSpdDown,0.0,true);
        eps_can_mesage.signals.emplace_back("ADC_LatCtrlReq",adc_eps_req_.ADC_LatCtrlReq,0.0,true);
        eps_can_mesage.signals.emplace_back("ADC_SteerAngReq",0,adc_eps_req_.ADC_SteerAngReq,false);
        eps_can_mesage.signals.emplace_back("ADC_SteerTorqReq",0,adc_eps_req_.ADC_SteerTorqReq,false);
        //longitudinalCommand
        longitudinalCommand.signals.emplace_back("ADC_GearReqVD", adc_acc_long_req_.ADC_GearReqVD,0.0,true);
        longitudinalCommand.signals.emplace_back("ADC_GearReq", adc_acc_long_req_.ADC_GearReq,0.0,true);

        longitudinalCommand.signals.emplace_back("Acc_Mode",adc_acc_long_req_.Acc_Mode,0.0,true);
        longitudinalCommand.signals.emplace_back("ADC_LngCtrlReq",adc_acc_long_req_.ADC_LngCtrlReq,0.0,true);
        longitudinalCommand.signals.emplace_back("ADC_VehTrqReq",0.0,adc_acc_long_req_.ADC_VehTrqReq,false);

        longitudinalCommand.signals.emplace_back("Acc_Decel_ReqSts",adc_acc_long_req_.Acc_Decel_ReqSts,0.0,true);
        longitudinalCommand.signals.emplace_back("Acc_Decel_Req",0,adc_acc_long_req_.Acc_Decel_Req,false);
        //lamp_can_mesage
        lamp_can_mesage.signals.emplace_back("SCM_TurnLeftLightReq",scm_req_.SCM_TurnLeftLightReq,0.0,true);
        lamp_can_mesage.signals.emplace_back("SCM_TurnRightLightReq",scm_req_.SCM_TurnRightLightReq,0.0,true);   
        AINFO << __func__ 
                    << ": EPS, ADC_EpsSpdDown="<<adc_eps_req_.ADC_EpsSpdDown << ", ADC_LatCtrlReq="<<adc_eps_req_.ADC_LatCtrlReq 
                    << ", ADC_SteerAngReq="<<adc_eps_req_.ADC_SteerAngReq << ", ADC_SteerTorqReq="<<adc_eps_req_.ADC_SteerTorqReq
                    << ", LON, ADC_GearReqVD="<<adc_acc_long_req_.ADC_GearReqVD<< ", ADC_GearReq="<<adc_acc_long_req_.ADC_GearReq
                    << ", Acc_Mode="<<adc_acc_long_req_.Acc_Mode<< ", ADC_LngCtrlReq="<<adc_acc_long_req_.ADC_LngCtrlReq
                    << ", ADC_VehTrqReq="<<adc_acc_long_req_.ADC_VehTrqReq<< ", Acc_Decel_ReqSts="<<adc_acc_long_req_.Acc_Decel_ReqSts
                    << ", Acc_Decel_Req="<<adc_acc_long_req_.Acc_Decel_Req;
    }

    void ZlgCanDbcAion::ShakeHandCmdHandle(const LatAndLongShakeHandCmd& aion_takeover_cmd_tmp,ADC_EPSReq& adc_eps_req){
      if(CommonBool::FALSE == aion_takeover_cmd_tmp.activate_lat() && 
          CommonBool::FALSE== aion_takeover_cmd_tmp.activate_long() && 
          chassis_info_ready_.load() && 
          ShakeHandStatus::SHAKEHANDREQQUIT == eps_req_status_ &&
          ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
            resetCanMsg();
            resetControlCmd();
      }
      else if(CommonBool::FALSE== aion_takeover_cmd_tmp.activate_long() && 
          chassis_info_ready_.load() && 
          ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
            resetCanMsg();
            resetControlCmd();
      }

      if(CommonBool::TRUE== aion_takeover_cmd_tmp.activate_lat() && chassis_info_ready_.load()){
        if(1 == eps_fault_type_){
          adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::NOREQUEST);
          adc_eps_req.ADC_EpsSpdDown = 2;
          adc_eps_req.ADC_SteerTorqReq = 0.0;
          adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;

          eps_req_status_ = ShakeHandStatus::DEFAULT;
        }
        else{
          if(ShakeHandStatus::DEFAULT == eps_req_status_ || ShakeHandStatus::SHAKEHANDREQQUIT == eps_req_status_){
              if(0 ==chassis_info_.EPS_FaultSts && 0 == chassis_info_.EPS_AngCtrlAbortFeedback){
                if(is_frist_lat_sh_success){
                  eps_req_status_ = ShakeHandStatus::SHAKEHANDREQ2;
                }
                else{
                  eps_req_status_ = ShakeHandStatus::COUNTERPARTSTATUS;
                }
              }
          }
          if(ShakeHandStatus::COUNTERPARTSTATUS == eps_req_status_){
            adc_eps_req.ADC_EpsSpdDown = 1;
            adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
            static uint32_t init_num = 0;
            if( ++init_num>10 ){
              init_num = 0;
              pub_sero_speed_times_ = 0;
              eps_req_status_ = ShakeHandStatus::SHAKEHANDREQ;
            }
          }
          if(ShakeHandStatus::SHAKEHANDREQ == eps_req_status_){
            adc_eps_req.ADC_EpsSpdDown = 2;
            adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
            if(pub_sero_speed_times_>=20){
              eps_req_status_ = ShakeHandStatus::SHAKEHANDREQ2;
            }
          }
          if(ShakeHandStatus::SHAKEHANDREQ2 == eps_req_status_){
            adc_eps_req.ADC_EpsSpdDown = 2;
            adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
            adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::ANGREQUEST);
              if(3 ==chassis_info_.EPS_LatCtrlMode){
                cmd_angle_pre_ = chassis_info_deal_.eps_steer_angle;
                eps_req_status_ = ShakeHandStatus::SHAKEHANDSUS;
                is_frist_lat_sh_success = true;
              }
          }
        }
        
        
      }
       else if(CommonBool::FALSE == aion_takeover_cmd_tmp.activate_lat() && chassis_info_ready_.load()){
        // if(ShakeHandStatus::SHAKEHANDSUS == eps_req_status_)
        {
          adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::NOREQUEST);
          adc_eps_req.ADC_EpsSpdDown = 2;
          // adc_eps_req.ADC_SteerAngReq = 0.0;
          adc_eps_req.ADC_SteerTorqReq = 0.0;
          adc_eps_req.ADC_SteerAngReq = chassis_info_deal_.eps_steer_angle;
            // if(0 ==chassis_info_.EPS_LatCtrlMode){
            eps_req_status_ = ShakeHandStatus::SHAKEHANDREQQUIT;
            // }
        }
       }
       else{
        AINFO << __FUNCTION__ << ": lat_takeover=" << static_cast<int>(aion_takeover_cmd_tmp.activate_lat())<<", has_chassis_info="<<chassis_info_ready_ ;
      }

      if(CommonBool::TRUE== aion_takeover_cmd_tmp.activate_long() && chassis_info_ready_.load()){
        if(ShakeHandStatus::DEFAULT == lon_req_status_ || ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
          lon_req_status_ = ShakeHandStatus::SHAKEHANDSUS;
        }
      }
      else if(CommonBool::FALSE== aion_takeover_cmd_tmp.activate_long() && chassis_info_ready_.load()){
        if(ShakeHandStatus::SHAKEHANDSUS == lon_req_status_){
          lon_req_status_ = ShakeHandStatus::SHAKEHANDREQQUIT;
        }
      }
      else{
        AINFO << __FUNCTION__ << ": lon_takeover=" << static_cast<int>(aion_takeover_cmd_tmp.activate_long())<<", has_chassis_info="<<chassis_info_ready_ ;
      }

      if(ShakeHandStatus::SHAKEHANDSUS == lon_req_status_){
          output_data_shake_hand_.long_active = CommonBool::TRUE;
          output_data_shake_hand_.long_shakehand_err_type = CommonErrorType::NONE;
          output_data_shake_hand_.long_shakehand_err_reason = LonShakeHandErrReason::NONE;
      }
      else if(ShakeHandStatus::SHAKEHANDREQQUIT == lon_req_status_){
          output_data_shake_hand_.long_active = CommonBool::FALSE;
      }
      else{
          output_data_shake_hand_.long_active = CommonBool::NONE;
      }

      if(ShakeHandStatus::SHAKEHANDSUS == eps_req_status_){
          output_data_shake_hand_.lat_active = CommonBool::TRUE;
          output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::NONE;
          output_data_shake_hand_.lat_shakehand_err_reason = LatShakeHandErrReason::NONE;
      }
      else if(ShakeHandStatus::SHAKEHANDREQQUIT == eps_req_status_){
          output_data_shake_hand_.lat_active = CommonBool::FALSE;
      }
      else{
          output_data_shake_hand_.lat_active = CommonBool::NONE;
      }

      // l2 driving_mode
      if(ShakeHandStatus::SHAKEHANDSUS == lon_req_status_ &&
          ShakeHandStatus::SHAKEHANDSUS == eps_req_status_){
          chassis_.driving_mode = 1;
      }
      else if (ShakeHandStatus::SHAKEHANDSUS == lon_req_status_){
          chassis_.driving_mode = 3;
      }
      else if (ShakeHandStatus::SHAKEHANDSUS == eps_req_status_){
          chassis_.driving_mode = 2;
      }
      else{
          chassis_.driving_mode = 0;
      }
      
      ShakeHandStateInfoPub(output_data_shake_hand_);
    }


    void ZlgCanDbcAion::ControlCmdHandle(const LatAndLongControlCmd& aion_control_cmd_tmp,ADC_EPSReq& adc_eps_req
                                                                                          ,ADC_AccLongReq& adc_acc_long_req){
      //EPS
      if(!aion_control_cmd_tmp.has_data()){
        AINFO<<__func__<<": not has data.";
        return;
      }
      if(aion_control_cmd_tmp.data().Is<TorqueControlData>()){
        TorqueControlData torque_control_data;
        aion_control_cmd_tmp.data().UnpackTo(&torque_control_data);
        if(CommonBool::TRUE == torque_control_data.is_steering()){
          double cmd_angle = torque_control_data.tar_steer_angle();
          if(ShakeHandStatus::SHAKEHANDSUS == eps_req_status_ && 0 == eps_fault_type_){
            if(cmd_angle > cmd_angle_pre_ + steering_angle_limit_){
              cmd_angle = cmd_angle_pre_ + steering_angle_limit_;
            }
            else if(cmd_angle < cmd_angle_pre_ - steering_angle_limit_){
              cmd_angle = cmd_angle_pre_ - steering_angle_limit_;
            }
            adc_eps_req.ADC_LatCtrlReq = static_cast<uint32_t>(ADCLatCtrlReq::ANGREQUEST);
            adc_eps_req.ADC_EpsSpdDown = 2;
            adc_eps_req.ADC_SteerTorqReq = 0.0;
            adc_eps_req.ADC_SteerAngReq = cmd_angle;
            cmd_angle_pre_ = cmd_angle;
        }
        }
        //highspeed gear
        if(CommonBool::TRUE == torque_control_data.put_gear()){
          if(lon_req_status_ == ShakeHandStatus::SHAKEHANDSUS){
            uint32_t cmd_gear = 
              CommonGearToAionGear(static_cast<uint32_t>(torque_control_data.tar_gear()));
            adc_acc_long_req.ADC_GearReqVD = static_cast<uint32_t>(ADCGearReqVD::VALID);
            if(cmd_gear == chassis_info_.VCU_CrntGearLvl){//IDNRP 01234    
              adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::NOREQUEST);
            }
            else{
              switch (cmd_gear)//0123 NPRD
              {
              case static_cast<uint32_t>(VCU_CrntGearLvl::Invalid):
                adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::NOREQUEST);
                break;
              case static_cast<uint32_t>(VCU_CrntGearLvl::Drive):
                adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::D);
                break;
              case static_cast<uint32_t>(VCU_CrntGearLvl::Neutral):
                adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::NOREQUEST);
                break;
              case static_cast<uint32_t>(VCU_CrntGearLvl::Reversed):
                adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::R);
                break;
              case static_cast<uint32_t>(VCU_CrntGearLvl::Park):
                adc_acc_long_req.ADC_GearReq = static_cast<uint32_t>(ADCGearReq::P);
                break;
              default:
                break;
              }
            }
          }
        }
        //acc
        if(CommonBool::TRUE == torque_control_data.is_driving()){
          if(lon_req_status_ == ShakeHandStatus::SHAKEHANDSUS){
            uint32_t cmd_torque = torque_control_data.tar_torque();
            adc_acc_long_req.Acc_Mode = static_cast<uint32_t>(AccMode::ACTIVE);
            adc_acc_long_req.ADC_LngCtrlReq = static_cast<uint32_t>(LngCtrlReq::ACTIVE);
            adc_acc_long_req.ADC_VehTrqReq = cmd_torque;

            adc_acc_long_req.Acc_Decel_ReqSts = static_cast<uint32_t>(AccDecelReqSts::NOREQUEST);
            adc_acc_long_req.Acc_Decel_Req = 0.0;
          }
        }
        //decacc
        if(CommonBool::TRUE == torque_control_data.is_braking()){
          if(lon_req_status_ == ShakeHandStatus::SHAKEHANDSUS){
            double acc_dec = torque_control_data.tar_deceleration();
            adc_acc_long_req.Acc_Mode = static_cast<uint32_t>(AccMode::OFF);
            adc_acc_long_req.ADC_LngCtrlReq = static_cast<uint32_t>(LngCtrlReq::INACTIVE);
            adc_acc_long_req.ADC_VehTrqReq = 0.0;

            adc_acc_long_req.Acc_Decel_ReqSts = static_cast<uint32_t>(AccDecelReqSts::REQUEST);
            if(acc_dec<-7.0){acc_dec = -7.0;}
            adc_acc_long_req.Acc_Decel_Req = acc_dec;
          }
        }
      }
    }

    void ZlgCanDbcAion::resetCanMsg(){
      adc_eps_req_.reset();
      adc_acc_long_req_.reset();
      scm_req_.reset();
    }

    void ZlgCanDbcAion::resetControlCmd(){
      std::unique_lock<std::mutex> lock_contorl(control_mux_);
      auto ctrl_msg = aion_control_cmd_.mutable_data();

      TorqueControlData trq_ctrl_data;
      trq_ctrl_data.set_is_driving(CommonBool::NONE);
      trq_ctrl_data.set_is_braking(CommonBool::NONE);
      trq_ctrl_data.set_tar_torque(0.0);
      trq_ctrl_data.set_tar_deceleration(0.0);
      trq_ctrl_data.set_is_steering(CommonBool::NONE);
      trq_ctrl_data.set_tar_steer_angle(0.0);
      trq_ctrl_data.set_put_gear(CommonBool::NONE);
      trq_ctrl_data.set_tar_gear(CommonGearPosition::NONE);
      trq_ctrl_data.set_tar_acc(0.0);
      trq_ctrl_data.set_lamp_ctl(0);

      ctrl_msg->PackFrom(trq_ctrl_data);
    }

    void ZlgCanDbcAion::IOExceptionHandle(){
       if(0 !=chassis_info_.EPS_FaultSts || 0 != chassis_info_.EPS_AngCtrlAbortFeedback){
        eps_fault_type_ = 1;
        AINFO<<__FUNCTION__<<": eps_fault_type_="<<eps_fault_type_<<", EPS_FaultSts="<<
                    chassis_info_.EPS_FaultSts<<",EPS_AngCtrlAbortFeedback="<<chassis_info_.EPS_AngCtrlAbortFeedback;
       }
       if(0 ==chassis_info_.EPS_FaultSts && 0 == chassis_info_.EPS_AngCtrlAbortFeedback){
        eps_fault_type_ = 0;
        AINFO<<__FUNCTION__<<": eps_fault_type_="<<eps_fault_type_<<", EPS_FaultSts="<<
                    chassis_info_.EPS_FaultSts<<",EPS_AngCtrlAbortFeedback="<<chassis_info_.EPS_AngCtrlAbortFeedback;
       }
    }

    void ZlgCanDbcAion::LampControl(const LatAndLongControlCmd& aion_control_cmd_tmp,SCM_Req& scm_req){
      if(!aion_control_cmd_tmp.has_data()){
        AINFO<<__func__<<": not has data.";
        return;
      }
      if(aion_control_cmd_tmp.data().Is<TorqueControlData>()){
        TorqueControlData torque_control_data;
        aion_control_cmd_tmp.data().UnpackTo(&torque_control_data);
        uint32_t cmd_lamp = torque_control_data.lamp_ctl();
        switch (cmd_lamp)
          {
            case 0: 
              scm_req.SCM_TurnLeftLightReq = 0;
              scm_req.SCM_TurnRightLightReq = 0;
              break;
            case 1:
              scm_req.SCM_TurnLeftLightReq = 1;
              scm_req.SCM_TurnRightLightReq = 0;
              break;
            case 2:
              scm_req.SCM_TurnLeftLightReq = 0;
              scm_req.SCM_TurnRightLightReq = 1;
              break;
            case 3:
              // SCMReqCommand.signals.emplace_back("SCM_EmeglampReq",1,0.0,true);
              break;
            default:
              break;
          }
      }
    }

    void ZlgCanDbcAion::updateAebController(const std::shared_ptr <rainbowdash::aeb::AEBController>& msg)
    {
      if (msg == nullptr)
      {
        return;
      }

      // aeb_ctrl_.AEBStatus = msg->aebstatus();
      // aeb_ctrl_.FCWActivate = msg->fcwactivate();
      // aeb_ctrl_.Deceleration = msg->deceleration();
      // aeb_ctrl_.EgoCarStop = msg->egocarstop();
    }

    void ZlgCanDbcAion::InitSub(){
      auto contorlcmd = nodeCfg.getSubChannel("io_in_trq_chassisctl"); 
      auto pReaderControlCmd = pNode->CreateReader<LatAndLongControlCmd>(contorlcmd.name, 
                                                                              std::bind(&ZlgCanDbcAion::ControlCmdCallback,this,std::placeholders::_1)); 
      auto contorltakeover = nodeCfg.getSubChannel("io_in_acc_shake_hand_req"); 
      auto pReaderContorlTakeover = pNode->CreateReader<LatAndLongShakeHandCmd>(contorltakeover.name, 
                                                                              std::bind(&ZlgCanDbcAion::ControlTakeoveCallback,this,std::placeholders::_1));
      
      auto decision_to_planning_update = nodeCfg.getSubChannel("decision_to_planning_update"); 
      apollo::cyber::ReaderConfig decision_to_planning_update_cfg;
      decision_to_planning_update_cfg.channel_name = decision_to_planning_update.name;
      decision_to_planning_update_cfg.pending_queue_size = decision_to_planning_update.pendingQueueSize;
      auto pReaderHwpDecisionToPlanningUpdateMsg = pNode->CreateReader<HwpDecisionToPlanningUpdateMsg>(decision_to_planning_update_cfg, 
                                                                              std::bind(&ZlgCanDbcAion::HwpDecisionToPlanningUpdateMsgCallback,this,std::placeholders::_1));
      auto decision_out_acc_state = nodeCfg.getSubChannel("decision_out_acc_state"); 
      auto pReaderDecisionACCState = pNode->CreateReader<ACCState>(decision_out_acc_state.name, 
                                                                              std::bind(&ZlgCanDbcAion::DecisionACCStateCallback,this,std::placeholders::_1));
      auto decision_out_lcc_state = nodeCfg.getSubChannel("decision_out_lcc_state"); 
      auto pReaderDecisionLCCState = pNode->CreateReader<LCCState>(decision_out_lcc_state.name, 
                                                                              std::bind(&ZlgCanDbcAion::DecisionLCCStateCallback,this,std::placeholders::_1));
      auto aeb_controller = nodeCfg.getSubChannel("aeb_aebcontroller");
      auto p_aeb_controller_ = pNode->CreateReader<rainbowdash::aeb::AEBController>(aeb_controller.name, 
                                                                              std::bind(&ZlgCanDbcAion::updateAebController,this,std::placeholders::_1));
    }

    void ZlgCanDbcAion::ControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& msg){
      std::string json_string;
      google::protobuf::util::MessageToJsonString(*msg, &json_string);
      AINFO << __FUNCTION__ << json_string;
      if(!msg->has_data()){
        AINFO<<__func__<<": not has data.";
        return;
      }
      if(msg->data().Is<TorqueControlData>()){
        TorqueControlData torque_control_data;
        msg->data().UnpackTo(&torque_control_data);
        AINFO << __FUNCTION__ << ", control_cmd: is_steering=" << static_cast<int>(torque_control_data.is_steering()) <<
                      ", tar_steer_angle=" << torque_control_data.tar_steer_angle() <<
                      ", is_driving=" << static_cast<int>(torque_control_data.is_driving()) <<
                      ", tar_torque=" << torque_control_data.tar_torque() <<
                      ", is_braking=" << static_cast<int>(torque_control_data.is_braking()) <<
                      ", tar_deceleration=" << torque_control_data.tar_deceleration() << ",";

      }
      std::unique_lock<std::mutex> lock_contorl(control_mux_);
      aion_control_cmd_ = *msg;
    }

    void ZlgCanDbcAion::ControlTakeoveCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& msg){
      std::string json_string;
      google::protobuf::util::MessageToJsonString(*msg, &json_string);
      AINFO << __FUNCTION__ << json_string;
      AINFO<<__FUNCTION__<<", shake_hand_cmd: activate_lat="<<static_cast<int>(msg->activate_lat())
                      <<",  activate_long="<<static_cast<int>(msg->activate_long())<<",";
      std::unique_lock<std::mutex> lock_lampctl(control_mux_);
      aion_takeover_ = *msg;
      shakehand_valid_ = 1;
    }

    void ZlgCanDbcAion::HwpDecisionToPlanningUpdateMsgCallback(const std::shared_ptr <HwpDecisionToPlanningUpdateMsg>& msg){
      //AINFO << __FUNCTION__ << msg->ShortDebugString();
      switch (msg->payload_case()) {
          case rainbowdash::planning::HwpDecisionToPlanningUpdateMsg::kDriverSetting :
              chassis_.maximum_user_speed = msg->driver_setting().custom_cruise_velocity();
              // chassis_.time_gap = msg->driver_setting().custom_cruise_interval();
              break;           
          case rainbowdash::planning::HMIMsg::PAYLOAD_NOT_SET :
          default :
              AERROR << "msg payload_case is not set.";
              break;
      }
    }

    void ZlgCanDbcAion::DecisionACCStateCallback(const std::shared_ptr <ACCState>& msg){
      //AINFO << __FUNCTION__ << msg->ShortDebugString();
    }

    void ZlgCanDbcAion::DecisionLCCStateCallback(const std::shared_ptr <LCCState>& msg){
      //AINFO << __FUNCTION__ << msg->ShortDebugString();
    }

    void ZlgCanDbcAion::InitPub(){
      if (nullptr == pWriterChassisInfo) {
        auto IoChassisInfo = nodeCfg.getPubChannel("io_chassisinfo");
        pWriterChassisInfo = pNode->CreateWriter<AionChassisInfo>(IoChassisInfo.name);
      }
      if (nullptr == pWriterChassis) {
        auto IoChassis = nodeCfg.getPubChannel("l2_chassis");
        pWriterChassis = pNode->CreateWriter<Chassis>(IoChassis.name);
      }
      if(nullptr == pWriterWheelSpeed){
        auto IoWheelSpeed = nodeCfg.getPubChannel("io_wheelspeed");
        pWriterWheelSpeed = pNode->CreateWriter<WheelSpeedInfo>(IoWheelSpeed.name);
      }
      if(pWriterShakeHandStateInfo == nullptr){
        auto io_out_acc_shake_hand_ret = nodeCfg.getPubChannel("io_out_acc_shake_hand_ret");
        pWriterShakeHandStateInfo = pNode->CreateWriter<ShakeHandStateInfo>(io_out_acc_shake_hand_ret.name);
      }
    }

    void ZlgCanDbcAion::IOChassisInfoSend(ChassisInfo& chassis_info){
      static uint32_t seq = 0;
      RecordSourceChassisInfo(chassis_info);

      
      if(Status::ERROR == DealChassisInfo(chassis_info_deal_)){
        AINFO << __FUNCTION__ << ": deal chassis info failed.";
      }

      RecordDealChassisInfo(chassis_info_deal_);

      AionChassisInfo aion_chassis_info;
      auto header = aion_chassis_info.mutable_header();
      header->set_timestamp(getTimeS(nullptr));
      header->set_module_name("aion_io_node");
      header->set_sequence_num(seq);
      header->set_version(0);
      seq++;

      aion_chassis_info.set_velocity(chassis_info_deal_.velocity);

      aion_chassis_info.set_lon_acceleration(chassis_info_deal_.lon_acceleration);
      aion_chassis_info.set_lat_acceleration(chassis_info_deal_.lat_acceleration);
      aion_chassis_info.set_yaw_rate(chassis_info_deal_.yaw_rate);

      aion_chassis_info.set_brake_system_status(chassis_info_deal_.brake_system_status);
      aion_chassis_info.set_brake_padal_position(chassis_info_deal_.brake_padal_position);

      aion_chassis_info.set_eps_steer_angle(chassis_info_deal_.eps_steer_angle);
      aion_chassis_info.set_eps_steer_angle_speed(chassis_info_deal_.eps_steer_angle_speed);
      aion_chassis_info.set_eps_fault_status(chassis_info_deal_.eps_fault_status);
      aion_chassis_info.set_eps_lat_ctl_mode(chassis_info_deal_.eps_lat_ctl_mode);

      aion_chassis_info.set_vcu_current_gear_level(chassis_info_deal_.vcu_current_gear_level);
      aion_chassis_info.set_vcu_actual_vehicle_wheel_torque(chassis_info_deal_.vcu_actual_vehicle_wheel_torque);

      aion_chassis_info.set_bcm_turn_lamp_status(chassis_info_deal_.bcm_turn_lamp_status);

      aion_chassis_info.set_wheel_speed_front_left(chassis_info_deal_.wheel_speed_front_left);
      aion_chassis_info.set_wheel_speed_front_right(chassis_info_deal_.wheel_speed_front_right);
      aion_chassis_info.set_wheel_speed_rear_left(chassis_info_deal_.wheel_speed_rear_left);
      aion_chassis_info.set_wheel_speed_rear_right(chassis_info_deal_.wheel_speed_rear_right);
      aion_chassis_info.set_speed(chassis_info_deal_.speed);

      aion_chassis_info.set_eps_takeover_status(static_cast<uint32_t>(eps_req_status_));
      aion_chassis_info.set_lon_takeover_status(static_cast<uint32_t>(lon_req_status_));
      aion_chassis_info.set_exception_type(eps_fault_type_);
      aion_chassis_info.set_eps_angctrl_abort_feedback(chassis_info.EPS_AngCtrlAbortFeedback);

      pWriterChassisInfo->Write(aion_chassis_info);

      ChassisPub(chassis_);

      WheelSpeedInfo wheel_speed_info;
      auto ws_header = wheel_speed_info.mutable_header();
      wheel_speed_info.set_measurement_time(getTimeS(nullptr));
      ws_header->set_timestamp(getTimeS(nullptr));
      ws_header->set_module_name("aion_io_node");
      ws_header->set_sequence_num(seq);
      ws_header->set_version(0);

      if(0 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
          wheel_speed_info.set_wheelspeed_fl(chassis_info_.WheelSpeedFront.WhlspdFL/3.6);
          wheel_speed_info.set_wheelsign_fl(1);
      }else if(1 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
          wheel_speed_info.set_wheelspeed_fl(chassis_info_.WheelSpeedFront.WhlspdFL/3.6);
          wheel_speed_info.set_wheelsign_fl(-1);
      }
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
          wheel_speed_info.set_wheelspeed_fr(chassis_info_.WheelSpeedFront.WhlspdFR/3.6);
          wheel_speed_info.set_wheelsign_fr(1);
      }else if(1 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
          wheel_speed_info.set_wheelspeed_fr(chassis_info_.WheelSpeedFront.WhlspdFR/3.6);
          wheel_speed_info.set_wheelsign_fr(-1);
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
          wheel_speed_info.set_wheelspeed_rl(chassis_info_.WheelSpeedRear.WhlspdRL/3.6);
          wheel_speed_info.set_wheelsign_rl(1);
      }else if(1 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
          wheel_speed_info.set_wheelspeed_rl(chassis_info_.WheelSpeedRear.WhlspdRL/3.6);
          wheel_speed_info.set_wheelsign_rl(-1);
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
          wheel_speed_info.set_wheelspeed_rr(chassis_info_.WheelSpeedRear.WhlspdRR/3.6);
          wheel_speed_info.set_wheelsign_rr(1);
      }else if(1 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
          wheel_speed_info.set_wheelspeed_rr(chassis_info_.WheelSpeedRear.WhlspdRR/3.6);
          wheel_speed_info.set_wheelsign_rr(-1);
      }
      pWriterWheelSpeed->Write(wheel_speed_info);

      std::string json_string;
      google::protobuf::util::MessageToJsonString(aion_chassis_info, &json_string);
      AINFO << __FUNCTION__ << json_string;
    }

    Status ZlgCanDbcAion::UpdateChassisInfo(){

      AINFO << __FUNCTION__ << ": dbc_recv_start.";
      // auto can_msg = dbc_.Recv();
      auto can_msgs = dbc_.Receive();
      AINFO << __FUNCTION__ << ": dbc_recv_finish.";

      if (can_msgs.empty()) {
        AINFO << __FUNCTION__ << ": get empty can_msg.";
        return Status::ERROR;
      }
      else{
        AINFO << __FUNCTION__ << ": get "<<can_msgs.size()<<" can_msg...";
        // std::cout<< __FUNCTION__ << ": receive "<<can_msgs.size()<<" can_msg..."<<std::endl;
      }

      int can_msgs_size = can_msgs.size();
      for(int i=0;i<can_msgs_size;i++){
        // std::cout<<"i = "<<i<<std::endl;
        auto can_msg = can_msgs.at(i);
        if(can_msg == nullptr){
          continue;
        }
        AINFO << __FUNCTION__ << ": can_msg["<<i<<"] timestamp is "<<can_msg->timestamp;
        // std::cout<<"i = "<<i<<", can_msg->name = "<<can_msg->name<<std::endl;
        if("AcmBody" == can_msg->name){
          AINFO << __FUNCTION__ << ": praseAcmBody.";
          PraseAcmBody(can_msg);
          chassis_info_ready_ = true;
        }
        else if("BrkSymSts" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseBrkSymSts.";
          chassis_info_ready_ = true;
          PraseBrkSymSts(can_msg);
        }
        else if("EPB_02" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseEPB_02.";
          chassis_info_ready_ = true;
          PraseEPB_02(can_msg);
        }
        else if("EPSSts" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseEPSSts.";
          chassis_info_ready_ = true;
          PraseEPSSts(can_msg);
        }
        else if("VCU_13" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseVCU_13.";
          chassis_info_ready_ = true;
          PraseVCU_13(can_msg);
        }
        else if("WHLspdFront" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseWHLspdFront.";
          chassis_info_ready_ = true;
          PraseWHLspdFront(can_msg);
        }
        else if("WHLspdRear" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseWHLspdRear.";
          chassis_info_ready_ = true;
          PraseWHLspdRear(can_msg);
        }
        else if("VehicleLights" == can_msg->name){
          AINFO << __FUNCTION__ << ": PraseVehicleLights.";
          chassis_info_ready_ = true;
          PraseVehicleLights(can_msg);
        }
        else{
          // AINFO << __FUNCTION__ << ": PraseOtherMsg -> "<< can_msg->name;
        }
      }

      if(false == chassis_info_ready_){
        return Status::ERROR;
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseAcmBody(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("LatitudeAcc" == s.name){
          chassis_info_.LatitudeAcc = s.physical*G;
          // AINFO << __FUNCTION__ << ": Cur_LatitudeAcc=" << s.physical;
        }
        else if("YawRate" == s.name){
          chassis_info_.YawRate = s.physical/180.0*3.141593;
          // AINFO << __FUNCTION__ << ": Cur_YawRate=" << s.physical;
        }
        else if("LongitudeAcc" == s.name){
          chassis_info_.LongitudeAcc = s.physical*G;
          // AINFO << __FUNCTION__ << ": Cur_LongitudeAcc=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseBrkSymSts(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("CDDSActv" == s.name){
          chassis_info_.CDDSActv = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_CDDSActv=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseEPB_02(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("Vehspd" == s.name){
          chassis_info_.Vehspd = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_Vehspd=" << s.physical;
        }
        else if("BrkPedPst" == s.name){
          chassis_info_.BrkPedPst = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_BrkPedPst=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseEPSSts(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("EPS_Steer_angledir" == s.name){
          chassis_info_.EPS_Steer_angledir = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_EPS_Steer_angledir=" << s.physical;
        }
        else if("EPS_Steeragspddir" == s.name){
          chassis_info_.EPS_Steeragspddir = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_EPS_Steeragspddir=" << s.physical;
        }
        else if("EPS_SteeringAngle" == s.name){
          chassis_info_.EPS_SteeringAngle = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_EPS_SteeringAngle=" << s.physical;
        }
        else if("EPS_FaultSts" == s.name){
          chassis_info_.EPS_FaultSts = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_EPS_FaultSts=" << s.physical;
        }
        else if("EPS_LatCtrlMode" == s.name){
          chassis_info_.EPS_LatCtrlMode = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_EPS_LatCtrlMode=" << s.physical;
        }
        else if("EPS_SteeringAngleSpd" == s.name){
          chassis_info_.EPS_SteeringAngleSpd = s.physical/180.0*3.141593;
          // AINFO << __FUNCTION__ << ": Cur_EPS_SteeringAngleSpd=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseVCU_13(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("VCU_CrntGearLvl" == s.name){
          chassis_info_.VCU_CrntGearLvl = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_VCU_CrntGearLvl=" << s.physical;
        }
        if("VCU_ACCPedalAcPst" == s.name){
          chassis_info_.VCU_ACCPedalAcPst = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_VCU_ACCPedalAcPst=" << s.physical;
        }
        if("VCU_ActVehWheelTorq" == s.name){
          chassis_info_.VCU_ActVehWheelTorq = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_VCU_ActVehWheelTorq=" << s.physical;
        }
	else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseWHLspdFront(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("WhlspdFLdir" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFLdir = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdFLdir=" << s.physical;
        }
        else if("WhlspdFLsts" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFLsts = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdFLsts=" << s.physical;
        }
        else if("WhlspdFR" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFR = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdFR=" << s.physical;
        }
        else if("WhlspdFRdir" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFRdir = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdFRdir=" << s.physical;
        }
        else if("WhlspdFRsts" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFRsts = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdFRsts=" << s.physical;
        }
        else if("WhlspdFL" == s.name){
          chassis_info_.WheelSpeedFront.WhlspdFL = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdFL=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseWHLspdRear(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("WhlspdRLdir" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRLdir = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdRLdir=" << s.physical;
        }
        else if("WhlspdRLsts" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRLsts = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdRLsts=" << s.physical;
        }
        else if("WhlspdRR" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRR = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdRR=" << s.physical;
        }
        else if("WhlspdRRdir" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRRdir = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdRRdir=" << s.physical;
        }
        else if("WhlspdRRsts" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRRsts = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdRRsts=" << s.physical;
        }
        else if("WhlspdRL" == s.name){
          chassis_info_.WheelSpeedRear.WhlspdRL = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_WhlspdRL=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::PraseVehicleLights(std::shared_ptr<CanMessage>& canmsg){
      auto signals = canmsg->signals;
      for(auto s:signals){
        if("BCM_TurnLampSt" == s.name){
          chassis_info_.BCM_TurnLampSt = s.physical;
          // AINFO << __FUNCTION__ << ": Cur_BCM_TurnLampSt=" << s.physical;
        }
        else{
          // AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
        }
      }
      return Status::OK;
    }

    Status ZlgCanDbcAion::DealChassisInfo() {
      
      ChassisInfoDeal chassis_info_deal;
      DealChassisInfo(chassis_info_deal);

      if(chassis_info_ready_.load()){
        return Status::OK;
      }
      else{
        AINFO << __FUNCTION__ << "Cant find valid information.";
        return Status::ERROR;
      }
    }

    Status ZlgCanDbcAion::DealChassisInfo(ChassisInfoDeal& chassis_info_deal){
      static double last_eps_steer_angle = 0.0;
      static bool first_set_steer_angle_flag = false;
      static bool steer_angle_speed_dir = false;
      chassis_info_deal.velocity = chassis_info_.Vehspd;
      chassis_info_deal.lon_acceleration = chassis_info_.LongitudeAcc;
      chassis_info_deal.lat_acceleration = chassis_info_.LatitudeAcc;
      chassis_info_deal.yaw_rate = chassis_info_.YawRate;
      chassis_info_deal.brake_system_status = chassis_info_.CDDSActv;
      chassis_info_deal.brake_padal_position = chassis_info_.BrkPedPst;
      chassis_info_deal.eps_steer_angle = (chassis_info_.EPS_Steer_angledir == 0)?(-chassis_info_.EPS_SteeringAngle):(chassis_info_.EPS_SteeringAngle);
      if(false == first_set_steer_angle_flag){
        first_set_steer_angle_flag = true;
        last_eps_steer_angle = chassis_info_deal.eps_steer_angle;
      }
      // bool steer_angle_speed_dir = static_cast<bool>(chassis_info_deal.eps_steer_angle - last_eps_steer_angle > 0);
      if(chassis_info_deal.eps_steer_angle - last_eps_steer_angle > 0){
        steer_angle_speed_dir = true;
      }
      else if(chassis_info_deal.eps_steer_angle - last_eps_steer_angle < 0){
        steer_angle_speed_dir = false;
      }
      chassis_info_deal.eps_steer_angle_speed = (steer_angle_speed_dir == false)?(chassis_info_.EPS_SteeringAngleSpd):(-chassis_info_.EPS_SteeringAngleSpd);
      chassis_info_deal.eps_fault_status = chassis_info_.EPS_FaultSts;
      chassis_info_deal.eps_lat_ctl_mode = chassis_info_.EPS_LatCtrlMode;
      chassis_info_deal.vcu_current_gear_level = chassis_info_.VCU_CrntGearLvl;
      chassis_info_deal.vcu_actual_vehicle_wheel_torque = chassis_info_.VCU_ActVehWheelTorq;
      chassis_info_deal.bcm_turn_lamp_status = chassis_info_.BCM_TurnLampSt;
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        chassis_info_deal.wheel_speed_front_left = chassis_info_.WheelSpeedFront.WhlspdFL;
      }
      else if(1 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        chassis_info_deal.wheel_speed_front_left = -chassis_info_.WheelSpeedFront.WhlspdFL;
      }
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        chassis_info_deal.wheel_speed_front_right = chassis_info_.WheelSpeedFront.WhlspdFR;
      }
      else if(1 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        chassis_info_deal.wheel_speed_front_right = -chassis_info_.WheelSpeedFront.WhlspdFR;
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        chassis_info_deal.wheel_speed_rear_left = chassis_info_.WheelSpeedRear.WhlspdRL;
      }
      else if(1 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        chassis_info_deal.wheel_speed_rear_left = -chassis_info_.WheelSpeedRear.WhlspdRL;
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        chassis_info_deal.wheel_speed_rear_right = chassis_info_.WheelSpeedRear.WhlspdRR;
      }
      else if(1 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        chassis_info_deal.wheel_speed_rear_right = chassis_info_.WheelSpeedRear.WhlspdRR;
      }
      chassis_info_deal.speed = (chassis_info_deal.wheel_speed_rear_right+chassis_info_deal.wheel_speed_rear_left)/2.0/3.6;

      last_eps_steer_angle = chassis_info_deal.eps_steer_angle;

      if(0 == chassis_info_.BCM_TurnLampSt){
        chassis_.vehicle_light.turn_signal = 0;
        chassis_.vehicle_light.emergency_light = false;
      }
      else if(1 == chassis_info_.BCM_TurnLampSt){
        chassis_.vehicle_light.turn_signal = 1;
      }
      else if(2 == chassis_info_.BCM_TurnLampSt){
        chassis_.vehicle_light.turn_signal = 2;
      }
      else if(3 == chassis_info_.BCM_TurnLampSt){
        chassis_.vehicle_light.emergency_light = true;
      }

      chassis_.vehicle_speed.vehicle_speed = chassis_info_deal.speed;
      chassis_.vehicle_speed.wheel_speed_fl = chassis_info_.WheelSpeedFront.WhlspdFL / 3.6;
      chassis_.vehicle_speed.wheel_speed_fr = chassis_info_.WheelSpeedFront.WhlspdFR / 3.6;
      chassis_.vehicle_speed.wheel_speed_rl = chassis_info_.WheelSpeedRear.WhlspdRL / 3.6;
      chassis_.vehicle_speed.wheel_speed_rr = chassis_info_.WheelSpeedRear.WhlspdRR / 3.6;

      chassis_.brake_percentage = chassis_info_.BrkPedPst;
      chassis_.throttle_perceptage = chassis_info_.VCU_ACCPedalAcPst;

      chassis_.acceleration.longitudinal = chassis_info_.LongitudeAcc;
      chassis_.acceleration.lateral = chassis_info_.LatitudeAcc;
      chassis_.yaw_rate = chassis_info_.YawRate;

      chassis_.steering_system.steering_wheel_angle = chassis_info_deal.eps_steer_angle;
      chassis_.steering_system.steering_wheel_rate = chassis_info_.EPS_SteeringAngleSpd;
      double wheel_steer_angle = 0.0;
      wheel_steer_angle = linearSearch(
        chassis_.steering_system.steering_wheel_angle , steer_angle_linear_interpolation_);
      chassis_.steering_system.steering_angle = wheel_steer_angle;

      chassis_.cur_torque = chassis_info_.VCU_ActVehWheelTorq;//0
      return Status::OK;
    }

    Status ZlgCanDbcAion::Send(CanMessage& msg)  {
        auto err = dbc_.Send(msg);
        if (err == zlgcandbc::FAILED)
          return Status::ERROR;
        else
          return Status::OK;
      }

    bool ZlgCanDbcAion::dataMonitor(){
        return true;
    }

    void ZlgCanDbcAion::ChassisInfoReset(){
      chassis_info_.LatitudeAcc = 0.0;chassis_info_.YawRate = 0.0;chassis_info_.LongitudeAcc = 0.0; 
      chassis_info_.CDDSActv = 0;
      chassis_info_.Vehspd = 0.0;chassis_info_.BrkPedPst = 0.0;
      chassis_info_.EPS_Steer_angledir = 0;chassis_info_.EPS_Steeragspddir = 0;chassis_info_.EPS_SteeringAngle = 0.0;
      chassis_info_.EPS_FaultSts = 0;chassis_info_.EPS_ToqCtrlAbortFeedback = 0;chassis_info_.EPS_LatCtrlMode = 0;
      chassis_info_.EPS_SteeringAngleSpd = 0;chassis_info_.EPS_AngCtrlAbortFeedback = 0;
      chassis_info_.VCU_CrntGearLvl = 0;chassis_info_.VCU_ActVehWheelTorq = 0;
      chassis_info_.WheelSpeedFront.WF_CRC = 0;chassis_info_.WheelSpeedFront.WF_MsgCntr = 0;chassis_info_.WheelSpeedFront.WhlspdFL = 0;
      chassis_info_.WheelSpeedFront.WhlspdFLdir = 0;chassis_info_.WheelSpeedFront.WhlspdFLsts = 0;chassis_info_.WheelSpeedFront.WhlspdFR = 0;
      chassis_info_.WheelSpeedFront.WhlspdFRdir = 0;chassis_info_.WheelSpeedFront.WhlspdFRsts = 0;
      chassis_info_.WheelSpeedRear.WR_CRC = 0;chassis_info_.WheelSpeedRear.WR_MsgCntr = 0;chassis_info_.WheelSpeedRear.WhlspdRL = 0;
      chassis_info_.WheelSpeedRear.WhlspdRLdir = 0;chassis_info_.WheelSpeedRear.WhlspdRLsts = 0;chassis_info_.WheelSpeedRear.WhlspdRR = 0;
      chassis_info_.WheelSpeedRear.WhlspdRRdir = 0;chassis_info_.WheelSpeedRear.WhlspdRRsts = 0;
    }

    void ZlgCanDbcAion::RecordSourceChassisInfo(ChassisInfo& chassis_info_tmp ){
      AINFO << __FUNCTION__ << ": LatitudeAcc=" << chassis_info_tmp.LatitudeAcc << ", LongitudeAcc=" << chassis_info_tmp.LongitudeAcc
      << ", YawRate=" << chassis_info_tmp.YawRate << ", CDDSActv=" << chassis_info_tmp.CDDSActv
      << ", Vehspd=" << chassis_info_tmp.Vehspd << ", BrkPedPst=" << chassis_info_tmp.BrkPedPst
      << ", EPS_Steer_angledir=" << chassis_info_tmp.EPS_Steer_angledir << ", EPS_Steeragspddir=" << chassis_info_tmp.EPS_Steeragspddir
      << ", EPS_SteeringAngle=" << chassis_info_tmp.EPS_SteeringAngle << ", EPS_FaultSts=" << chassis_info_tmp.EPS_FaultSts
      << ", EPS_ToqCtrlAbortFeedback=" << chassis_info_tmp.EPS_ToqCtrlAbortFeedback << ", EPS_LatCtrlMode=" << chassis_info_tmp.EPS_LatCtrlMode
      << ", EPS_SteeringAngleSpd=" << chassis_info_tmp.EPS_SteeringAngleSpd << ", EPS_AngCtrlAbortFeedback=" << chassis_info_tmp.EPS_AngCtrlAbortFeedback
      << ", VCU_CrntGearLvl=" << chassis_info_tmp.VCU_CrntGearLvl << ", VCU_ActVehWheelTorq=" << chassis_info_tmp.VCU_ActVehWheelTorq
      << ", WhlspdRLdir=" << chassis_info_tmp.WheelSpeedRear.WhlspdRLdir << ", WhlspdRLsts=" << chassis_info_tmp.WheelSpeedRear.WhlspdRLsts
      << ", WhlspdRL=" << chassis_info_tmp.WheelSpeedRear.WhlspdRL << ", WhlspdRRdir=" << chassis_info_tmp.WheelSpeedRear.WhlspdRRdir
      << ", WhlspdRRsts=" << chassis_info_tmp.WheelSpeedRear.WhlspdRRsts << ", WhlspdRR=" << chassis_info_tmp.WheelSpeedRear.WhlspdRR;
    }

    void ZlgCanDbcAion::RecordDealChassisInfo(ChassisInfoDeal& chassis_info_deal){
      AINFO << __FUNCTION__ << ": velocity=" << chassis_info_deal.velocity << ", lon_acceleration=" << chassis_info_deal.lon_acceleration
      << ", lat_acceleration=" << chassis_info_deal.lat_acceleration << ", yaw_rate=" << chassis_info_deal.yaw_rate
      << ", brake_system_status=" << chassis_info_deal.brake_system_status << ", brake_padal_position=" << chassis_info_deal.brake_padal_position
      << ", eps_steer_angle=" << chassis_info_deal.eps_steer_angle << ", eps_steer_angle_speed=" << chassis_info_deal.eps_steer_angle_speed
      << ", eps_fault_status=" << chassis_info_deal.eps_fault_status << ", eps_lat_ctl_mode=" << chassis_info_deal.eps_lat_ctl_mode
      << ", vcu_current_gear_level=" << chassis_info_deal.vcu_current_gear_level << ", vcu_actual_vehicle_wheel_torque=" << chassis_info_deal.vcu_actual_vehicle_wheel_torque
      << ", bcm_turn_lamp_status=" << chassis_info_deal.bcm_turn_lamp_status << ", wheel_speed_front_left=" << chassis_info_deal.wheel_speed_front_left
      << ", wheel_speed_front_right=" << chassis_info_deal.wheel_speed_front_right << ", wheel_speed_rear_left=" << chassis_info_deal.wheel_speed_rear_left
      << ", wheel_speed_rear_left=" << chassis_info_deal.wheel_speed_rear_left << ", wheel_speed_rear_right=" << chassis_info_deal.wheel_speed_rear_right
      << ", speed=" << chassis_info_deal.speed<< ", lon_req_status=" << static_cast<uint32_t>(lon_req_status_)<< ", eps_req_status=" << static_cast<uint32_t>(eps_req_status_);
    }


    uint32_t ZlgCanDbcAion::AionGearToCommonGear(uint32_t gear_info){
      uint32_t res = 0;
      if(static_cast<uint32_t>(VCU_CrntGearLvl::Neutral) == gear_info)
      {res = static_cast<uint32_t>(CommonGearPosition::NEUTRAL);}
      else if(static_cast<uint32_t>(VCU_CrntGearLvl::Drive) == gear_info)
      {res = static_cast<uint32_t>(CommonGearPosition::DRIVING);}
      else if(static_cast<uint32_t>(VCU_CrntGearLvl::Reversed) == gear_info)
      {res = static_cast<uint32_t>(CommonGearPosition::REVERSE);}
      else if(static_cast<uint32_t>(VCU_CrntGearLvl::Park) == gear_info)
      {res = static_cast<uint32_t>(CommonGearPosition::PARKING);}
      else if(static_cast<uint32_t>(VCU_CrntGearLvl::Invalid) == gear_info)
      {res = static_cast<uint32_t>(CommonGearPosition::NONE);}
      return res;
    };

    uint32_t ZlgCanDbcAion::CommonGearToAionGear(uint32_t common_gear){
      uint32_t res = 0;
      if(static_cast<uint32_t>(CommonGearPosition::NEUTRAL) == common_gear)
      {res = static_cast<uint32_t>(VCU_CrntGearLvl::Neutral);}
      else if(static_cast<uint32_t>(CommonGearPosition::DRIVING) == common_gear)
      {res = static_cast<uint32_t>(VCU_CrntGearLvl::Drive);}
      else if(static_cast<uint32_t>(CommonGearPosition::REVERSE) == common_gear)
      {res = static_cast<uint32_t>(VCU_CrntGearLvl::Reversed);}
      else if(static_cast<uint32_t>(CommonGearPosition::PARKING) == common_gear)
      {res = static_cast<uint32_t>(VCU_CrntGearLvl::Park);}
      else if(static_cast<uint32_t>(CommonGearPosition::NONE) == common_gear)
      {res = static_cast<uint32_t>(VCU_CrntGearLvl::Invalid);}
      return res;
    }

    void ZlgCanDbcAion::ShakeHandStateInfoPub(StruOutputDataShakeHand& output_data_shake_hand){
      if(0 == shakehand_valid_)return;
      ShakeHandStateInfo sh_cmd;
      static int64_t seq=0;
      auto header = sh_cmd.mutable_header();
      header->set_timestamp(getTimeS(nullptr));
      header->set_module_name(nodeCfg.getName());
      header->set_sequence_num(seq++);
      header->set_version(0);
      sh_cmd.set_lat_active(output_data_shake_hand.lat_active);
      sh_cmd.set_lat_err_type(output_data_shake_hand.lat_shakehand_err_type);
      sh_cmd.set_lat_err_reason(output_data_shake_hand.lat_shakehand_err_reason);
      sh_cmd.set_long_active(output_data_shake_hand.long_active);
      sh_cmd.set_long_err_type(output_data_shake_hand.long_shakehand_err_type);
      sh_cmd.set_long_err_reason(output_data_shake_hand.long_shakehand_err_reason);
      pWriterShakeHandStateInfo->Write(sh_cmd);
      AINFO << __FUNCTION__ << ": lat_active=" << static_cast<int>(output_data_shake_hand.lat_active)
                      <<",  long_active="<<static_cast<int>(output_data_shake_hand.long_active)<<",";
      shakehand_valid_ = 0;
    }

    void ZlgCanDbcAion::ChassisPub(StruChassis& chassis){
        Chassis cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        cmd.set_driving_mode((DrivingMode)(chassis.driving_mode));
        cmd.set_gear_locationb((CommonGearPosition::Enum)AionGearToCommonGear(chassis.gear_locationb));

        cmd.mutable_vehicle_signal()->set_turn_signal((TurnSignal)(chassis.vehicle_light.turn_signal));
        cmd.mutable_vehicle_signal()->set_high_beam(chassis.vehicle_light.high_beam);
        cmd.mutable_vehicle_signal()->set_low_beam(chassis.vehicle_light.low_beam);
        cmd.mutable_vehicle_signal()->set_horn(chassis.vehicle_light.horn);
        cmd.mutable_vehicle_signal()->set_emergency_light(chassis.vehicle_light.emergency_light);

        cmd.mutable_vehicle_speed()->set_vehicle_speed(chassis.vehicle_speed.vehicle_speed);
        cmd.mutable_vehicle_speed()->set_wheel_speed_fl(chassis.vehicle_speed.wheel_speed_fl);
        cmd.mutable_vehicle_speed()->set_wheel_speed_fr(chassis.vehicle_speed.wheel_speed_fr);
        cmd.mutable_vehicle_speed()->set_wheel_speed_rl(chassis.vehicle_speed.wheel_speed_rl);
        cmd.mutable_vehicle_speed()->set_wheel_speed_rr(chassis.vehicle_speed.wheel_speed_rr);
        cmd.mutable_vehicle_speed()->set_instrument_vehicle_speed(chassis.vehicle_speed.instrument_vehicle_speed);

        cmd.set_maximum_user_speed(chassis.maximum_user_speed);
        cmd.set_throttle_perceptage(chassis.throttle_perceptage);
        cmd.set_brake_percentage(chassis.brake_percentage);

        cmd.mutable_acceleration()->set_longitudinal(chassis.acceleration.longitudinal);
        cmd.mutable_acceleration()->set_lateral(chassis.acceleration.lateral);

        cmd.set_yaw_rate(chassis.yaw_rate);
        cmd.mutable_steering_system()->set_steering_wheel_angle(chassis.steering_system.steering_wheel_angle);
        cmd.mutable_steering_system()->set_steering_wheel_rate(chassis.steering_system.steering_wheel_rate);
        cmd.mutable_steering_system()->set_steering_wheel_torque(chassis.steering_system.steering_wheel_torque);
        cmd.mutable_steering_system()->set_steering_angle(chassis.steering_system.steering_angle);

        cmd.set_time_gap(chassis.time_gap);
        cmd.set_cur_torque(chassis.cur_torque);

        pWriterChassis->Write(cmd);
    }

}

#ifndef CHASSIS_INFO_H_
#define CHASSIS_INFO_H_

template <typename T> 
class ZeroStruct 
{ 
public: 
    ZeroStruct() 
    { 
        memset(this,0,sizeof(T)); 
    } 
};

struct AcmBody
{
    uint ACM_CRC;
    uint ACM_MsgCntr;
    double LatitudeAcc;//
    double YawRate;//
    double LongitudeAcc;//
};

//四门开关状态
struct BodyStatus
{
    uint BCM_CLOSURE_CRC;
    uint BCM_CLOSURE_MsgCntr;
    uint DoorAjarFrntRiSts;//
    uint DoorAjarReRiSts;//
    uint DoorAjarFrntLeSts;//
    uint DoorAjarReLeSts;//
    double VehSpd;//
};

struct BrkSymSts
{
    bool CDDSAvl;
    bool CDDSActv; //减速度激活标志位,ACC及AEB减速度标志位反馈,以此判断减速度是否受控
};

struct DriverStatus//油门override
{
    bool VCUAccrPedlOvrd;
};

struct EPB_02//EPBStatus
{
    uint EPB_SwitchSt;
    uint BrkPedlSts;
    bool EPB_FailSt;
    double Vehspd;
    bool Vehspdsts;
    double BrkPedPst;//制动踏板百分比
    bool BrkPedPstVD;
    uint Brk_Msgcntr;
};

struct EPSSts//EPSStatus01 
{
    uint EPS_D_CRC;
    bool EPS_Steer_angleandspdvalid;
    bool EPS_Steer_angledir;//
    bool EPS_Steeragspddir;//
    uint EPS_D_MsgCntr;
    double EPS_SteeringAngle;//
    bool EPS_FaultSts;//
    uint EPS_ToqCtrlAbortFeedback;//
    uint EPS_LatCtrlMode;//
    uint EPS_SteeringAngleSpd;//deg/sec (5,0) 0-2550
    uint EPS_AngCtrlAbortFeedback;//
};

struct  EPSStatus02
{
    uint EPS_02_CRC;
    uint EPS_PinionAgValid;
    uint EPS_02_MsgCntr;
    double EPS_PinionAg;
    double EPS_MotorTq;
    uint EPS_MotorTqValid;
};

struct SCMsts//ACC按键状态
{
    uint SCM_CRC;
    uint AccTimeGapIncSwtSts;
    uint SCM_MsgCntr;
    uint ModeTogDecSwtSts;
    uint AccSetSwtSts;
    uint MenuPushSwtSts;
    uint AccSpdDecSwtSts;
    uint FogLiPushSwtSts;
    uint HiLowBeamPushSwtSts;
    uint SCMFailSts;
    uint ModeTogIncSwtSts;
    uint TurnIndcrSwtSts;
    uint MenuRiPushSwtSts;
    uint MenuOrVolUpPushSwtSts;
    uint AccTimeGapDecSwtSts;
    uint MenuOrVolDwnPushSwtSts;
    uint SWCFailSts;
    uint AccSpdIncOrResuSwtSts;
    uint MenuLePushSwtSts;
    uint CCPushSwtSts;
};

struct VCU_13
{
    uint VCU_13_CRC;
    uint VCU_13_MsgCntr;
    bool Accpedacttravelvalid;
    double VCU_ACCPedalAcPst;
    uint VCU_CrntGearLvl;//
    uint VCU_ActVehWheelTorq;//
};

struct WHLspdFront
{
    uint WF_CRC;
    uint WF_MsgCntr;
    uint WhlspdFLdir;
    uint WhlspdFLsts;
    double WhlspdFR;
    uint WhlspdFRdir;
    uint WhlspdFRsts;
    double WhlspdFL;
};

struct WHLspdRear
{
    uint WR_CRC;
    uint WR_MsgCntr;
    uint WhlspdRLdir;
    uint WhlspdRLsts;
    double WhlspdRR;
    uint WhlspdRRdir;
    uint WhlspdRRsts;
    double WhlspdRL;
};

struct VehicleLights
{
    uint BCM_FrontFogLampSt;
    uint BCM_RearFogLampSt;
    uint FrntwiperSts;
    uint BCM_TurnLampSt;
    uint BCM_BeamSt;
    uint BCM_BrkLightSts;
};

struct CAM_2
{
    /* data */
};

struct CAM_3
{
    /* data */
};

struct ChassisInfo
{
    //AcmBody
    double LatitudeAcc;
    double YawRate;
    double LongitudeAcc;
    //BrkSymSts
    bool CDDSActv;
    //EPB_02
    double Vehspd;
    double BrkPedPst;
    //EPSSts
    bool EPS_Steer_angledir;
    bool EPS_Steeragspddir;
    double EPS_SteeringAngle;
    bool EPS_FaultSts;
    uint EPS_ToqCtrlAbortFeedback;
    uint EPS_LatCtrlMode;
    uint EPS_SteeringAngleSpd;//(5,0) [0|2550] "Deg/sec"
    uint EPS_AngCtrlAbortFeedback;
    //VCU_13
    uint VCU_CrntGearLvl;
    double VCU_ACCPedalAcPst;
    uint VCU_ActVehWheelTorq;
    //WHLspdFront WHLspdRear
    WHLspdFront WheelSpeedFront;
    WHLspdRear WheelSpeedRear;
    //VehicleLights
    uint BCM_TurnLampSt;
};

struct ChassisInfoDeal:ZeroStruct<ChassisInfoDeal>
{
    double velocity;

    double lon_acceleration;
    double lat_acceleration;
    double yaw_rate;

    bool brake_system_status;
    double brake_padal_position;

    double eps_steer_angle;
    uint eps_steer_angle_speed;
    bool eps_fault_status;
    uint eps_lat_ctl_mode;

    uint vcu_current_gear_level;
    uint vcu_actual_vehicle_wheel_torque;

    uint bcm_turn_lamp_status;

    double wheel_speed_front_left;
    double wheel_speed_front_right;
    double wheel_speed_rear_left;
    double wheel_speed_rear_right;
    double speed;
};

enum class AccMode{
    OFF = 0,
    PASSIVE = 1,
    STANDBY = 2,
    ACTIVE = 3
};

enum class LngCtrlReq{
    INACTIVE = 0,
    ACTIVE = 1
};

enum class AccDecelReqSts{
    NOREQUEST = 0,
    REQUEST = 1
};

enum class ADCLatCtrlReq{
    NOREQUEST = 0,
    READY = 1,
    TOQREQUEST = 2,
    ANGREQUEST =3
};

enum class ADCEpsSpdDown{
    DEFULT = 0,
    NORMAL = 1,
    SPEEDZERO = 2
};

enum class ADCGearReqVD{
    INVALID = 0,
    VALID =1
};

enum class ADCGearReq{
    NOREQUEST = 0,
    P = 1,
    R = 2,
    D = 3
};

enum class ADC_APAGearLvlReq{
    NOREQUEST = 0,
    P = 1,
    R = 2,
    D = 3
};

enum class VCU_CrntGearLvl{
    Invalid = 0,
    Drive = 1,
    Neutral = 2,
    Reversed = 3,
    Park = 4
};

enum class EPSAngleReqStatus{
    DEFAULT = 0,
    INITDONE = 1,
    SPDDOWNINIT = 2,
    SPDDOWNZERO = 3,
    SETLATCTRLMODE = 4,
    SETLATCTRLMODEDONE = 5,
    QUITLATCTRLMODE = 6
};

enum class ShakeHandStatus:uint32_t{
    DEFAULT=0,
    COUNTERPARTSTATUS=1,
    SHAKEHANDREQ=2,
    SHAKEHANDSUS=3,
    SHAKEHANDFAILED=4,
    INTERRUPT=5,
    SHAKEHANDREQQUIT=6,
    SHAKEHANDQUIT=7,
    SHAKEHANDREQ2=8
};

struct ADC_EPSReq
{
    uint32_t ADC_MsgCntr;

    uint32_t ADC_EpsSpdDown;

    uint32_t ADC_LatCtrlReq;
    double ADC_SteerAngReq;
    
    double ADC_SteerTorqReq;

    void reset(){
        this->ADC_EpsSpdDown = 2;
        this->ADC_LatCtrlReq = 0;
        this->ADC_SteerAngReq = 0.0;
        this->ADC_SteerTorqReq = 0.0;
    }
};

struct ADC_AccLongReq
{
    uint32_t ADC_MsgCntr;

    uint32_t ADC_GearReqVD;
    uint32_t ADC_GearReq;

    uint32_t Acc_Mode;
    uint32_t ADC_LngCtrlReq;
    double ADC_VehTrqReq;
    double Acc_Decel_Req;
    uint32_t Acc_Decel_ReqSts;

    void reset(){
        this->ADC_GearReqVD = 0;
        this->ADC_GearReq = 0;
        this->Acc_Mode = 0;
        this->ADC_LngCtrlReq = 0;
        this->ADC_VehTrqReq = 0.0;
        this->Acc_Decel_Req = 0.0;
        this->Acc_Decel_ReqSts = 0;
    }
};

struct SCM_Req{
    uint32_t SCM_TurnLeftLightReq;
    uint32_t SCM_TurnRightLightReq;

    void reset(){
        this->SCM_TurnLeftLightReq = 0;
        this->SCM_TurnRightLightReq = 0;
    }
};

    //acc
struct StruVehicleSignal{
    uint32_t turn_signal;
    bool high_beam;
    bool low_beam;
    bool horn;
    bool emergency_light;
};
struct StruVehicleSpeed{
    double vehicle_speed;
    double wheel_speed_fl;
    double wheel_speed_fr;
    double wheel_speed_rl;
    double wheel_speed_rr;
    double instrument_vehicle_speed;
};
struct StruAcceleration{
    double longitudinal;
    double lateral;
};
struct StruSteeringSystem{
    double steering_wheel_angle;
    double steering_wheel_rate;
    double steering_wheel_torque;
    double steering_angle;  
};
struct StruChassis:ZeroStruct<StruChassis>
{
    uint32_t driving_mode;
    uint32_t gear_locationb;
    StruVehicleSignal vehicle_light;
    StruVehicleSpeed vehicle_speed;
    double maximum_user_speed;
    // 
    double throttle_perceptage;
    double brake_percentage;
    StruAcceleration acceleration;
    double yaw_rate;
    StruSteeringSystem steering_system;
    uint32_t time_gap;
    uint32_t cur_torque;
};

#endif

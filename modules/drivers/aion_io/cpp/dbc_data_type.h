#ifndef DBC_DATA_TYPE_H_
#define DBC_DATA_TYPE_H_

#include <iostream>
#include <vector>

typedef struct Signals {
    std::string name;
    unsigned int raw;
    double physical;
    bool use_raw;
    Signals(const std::string name, const unsigned int raw) :
        name(name), raw(raw), use_raw(true) {}
    Signals(const std::string name, const double physical) :
        name(name), physical(physical), use_raw(false) {}
    Signals(const std::string name, const unsigned int raw, const double physical) :
        name(name), raw(raw), physical(physical) {}
    Signals(const std::string name, const unsigned int raw, const double physical, const bool use_raw) :
        name(name), raw(raw), physical(physical), use_raw(use_raw) {}
} Signal;

typedef struct CanMessage {
    uint64_t timestamp;
    std::string name;
    std::vector<Signals> signals;
    ~CanMessage(){}
    CanMessage(const uint64_t timestamp, const std::string name) :
        timestamp(timestamp),name(name) {}
} Message;

#endif
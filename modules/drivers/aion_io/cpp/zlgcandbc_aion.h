#ifndef ZLGCANDBC_AION_H_
#define ZLGCANDBC_AION_H_

#include "cyber/cyber.h"
#include "cyber/node/writer.h"
#include "cyber/timer/timer.h"

#include <google/protobuf/util/json_util.h>
#include "base/util/errno.h"
#include "base/util/module_base.hpp"
#include "base/util/config_parser.hpp"
#include "base/util/app_template.hpp"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_chassis.pb.h"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_control.pb.h"
#include "zlgcandbc.h"
#include "chassis_info.h"
#include <atomic>
#include <cstdlib>
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/acc_planning_input.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"
#include "common/file.h"
#include "modules/control/acc_control/proto/control_conf.pb.h"
#include "thirdparty/common-algorithm/pnc_math/basic_math/math_utils.h"

#include "thirdparty/recommend_protocols/aeb/proto/aeb.pb.h"

using rainbowdash::common::CommonBool;

using apollo::cyber::Node;
using apollo::cyber::Writer;
using CanMessage = zlgcandbc::Message;
using Signals = zlgcandbc::Signal;
using aion_io::chassisinfo::AionChassisInfo;
using rainbowdash::control_by_wire::WheelSpeedInfo;
using rainbowdash::common::CommonGearPosition;
using rainbowdash::control_by_wire::LatAndLongShakeHandCmd;
using rainbowdash::control_by_wire::ShakeHandStateInfo;
using rainbowdash::control_by_wire::LatAndLongControlCmd;
using rainbowdash::control_by_wire::TorqueControlData;
using rainbowdash::control_by_wire::LatShakeHandErrReason;
using rainbowdash::control_by_wire::LonShakeHandErrReason;
using rainbowdash::common::CommonErrorType;

// l2
using rainbowdash::control_by_wire::DrivingMode;
using rainbowdash::control_by_wire::Acceleration;
using rainbowdash::control_by_wire::SteeringSystem;
using rainbowdash::control_by_wire::TurnSignal;
using rainbowdash::control_by_wire::VehicleSignal;
using rainbowdash::control_by_wire::Chassis;
using rainbowdash::planning::ACCState;
using rainbowdash::planning::LCCState;
using rainbowdash::planning::HwpDecisionToPlanningUpdateMsg;

using mega::control::ControlConf;

#define G 9.8

// External outputs (root outports fed by signals with default storage)
struct ExtY_AEBController_T {
  float Deceleration;                 // '<Root>/Deceleration '
  uint32_t AEBStatus;                   // '<Root>/AEB Status'
  uint32_t FCWActivate;                 // '<Root>/FCW Activate'
  uint32_t EgoCarStop;                // '<Root>/Ego Car Stop'
};

namespace zlgcandbc_aion
{

    struct StruOutputDataShakeHand
    {
        uint32_t is_update;
        CommonBool::Enum lat_active;
        CommonErrorType::Enum lat_shakehand_err_type;
        LatShakeHandErrReason::Enum lat_shakehand_err_reason;
        CommonBool::Enum long_active;
        CommonErrorType::Enum long_shakehand_err_type;
        LonShakeHandErrReason::Enum long_shakehand_err_reason;
    };

    class ZlgCanDbcAion: public ModuleBase
    {
    private:
        /* data */
    public:
        explicit ZlgCanDbcAion(std::shared_ptr <Node> node, NodeCfg& nodecfg, const char* dbc_path);
        ~ZlgCanDbcAion();

        bool dataMonitor();

    private:
        void StartDBC(const char *dbc_path);
        void StopDBC();
        Status ClearBuffer();
        Status Send(CanMessage& msg);

        void InitSub();
        void InitPub();
        bool InitConfigure();

        void ControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& msg);
        void ControlTakeoveCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& msg);
        void HwpDecisionToPlanningUpdateMsgCallback(const std::shared_ptr <HwpDecisionToPlanningUpdateMsg>& msg);
        void DecisionACCStateCallback(const std::shared_ptr <ACCState>& msg);
        void DecisionLCCStateCallback(const std::shared_ptr <LCCState>& msg);

        void ShakeHandCmdHandle(const LatAndLongShakeHandCmd& aion_takeover_cmd_tmp,ADC_EPSReq& adc_eps_req);
        void ControlCmdHandle(const LatAndLongControlCmd& aion_control_cmd_tmp,ADC_EPSReq& adc_eps_req,
                                                            ADC_AccLongReq& adc_acc_long_req);
        void LampControl(const LatAndLongControlCmd& aion_control_cmd_tmp,SCM_Req& scm_req);
        void sendCanMessage();
        void setCanMessage(CanMessage& eps_can_mesage,CanMessage& longitudinalCommand,CanMessage& lamp_can_mesage);

        void InitTimer();
        void IOChassisInfoRecv();
        void IOControlCmdSend();
        void IOChassisInfoSend(ChassisInfo& chassis_info);

        void resetCanMsg();
        void resetControlCmd();
        void IOExceptionHandle();

        Status UpdateChassisInfo();
        Status DealChassisInfo();
        Status DealChassisInfo(ChassisInfoDeal& chassis_info_deal);
        Status PraseAcmBody(std::shared_ptr<CanMessage>& canmsg);
        Status PraseBrkSymSts(std::shared_ptr<CanMessage>& canmsg);
        Status PraseEPB_02(std::shared_ptr<CanMessage>& canmsg);
        Status PraseEPSSts(std::shared_ptr<CanMessage>& canmsg);
        Status PraseVCU_13(std::shared_ptr<CanMessage>& canmsg);
        Status PraseWHLspdFront(std::shared_ptr<CanMessage>& canmsg);
        Status PraseWHLspdRear(std::shared_ptr<CanMessage>& canmsg);
        Status PraseVehicleLights(std::shared_ptr<CanMessage>& canmsg);

        void ChassisInfoReset();
        void RecordSourceChassisInfo(ChassisInfo& chassis_info_tmp);
        void RecordDealChassisInfo(ChassisInfoDeal& chassis_info_deal);

        uint32_t CommonGearToAionGear(uint32_t common_gear);
        uint32_t AionGearToCommonGear(uint32_t gear_info);
        void ShakeHandStateInfoPub(StruOutputDataShakeHand& output_data_shake_hand);
        void ChassisPub(StruChassis& chassis);

    private:
        std::shared_ptr <Node> pNode = nullptr;
        NodeCfg nodeCfg;
        zlgcandbc::ZlgcanDbc dbc_;
        ChassisInfo chassis_info_;
        ChassisInfoDeal chassis_info_deal_;
        std::shared_ptr <apollo::cyber::Timer> pTimerIOChassisInfoRecv = nullptr;//2ms
        std::shared_ptr <apollo::cyber::Timer> pTimerIOControlCmdSend = nullptr;//10ms
        std::atomic<bool> chassis_info_ready_{false};
        ShakeHandStatus eps_req_status_ = ShakeHandStatus::DEFAULT;
        ShakeHandStatus lon_req_status_ = ShakeHandStatus::DEFAULT;
        std::shared_ptr<Writer <AionChassisInfo>> pWriterChassisInfo = nullptr;
        std::shared_ptr<Writer <Chassis>> pWriterChassis = nullptr;
        std::shared_ptr<Writer <WheelSpeedInfo>> pWriterWheelSpeed = nullptr;
        std::shared_ptr< Writer<ShakeHandStateInfo> > pWriterShakeHandStateInfo = nullptr;
        std::mutex control_mux_;
        LatAndLongControlCmd aion_control_cmd_;
        LatAndLongShakeHandCmd aion_takeover_;
        double steering_angle_limit_ = 9.0;
        double cmd_angle_pre_ = 0.0;
        ADC_EPSReq adc_eps_req_;
        ADC_AccLongReq adc_acc_long_req_;
        SCM_Req scm_req_;
        StruOutputDataShakeHand output_data_shake_hand_;
        StruChassis chassis_;
        std::atomic<uint32_t> eps_fault_type_{0};
        std::atomic<uint32_t> shakehand_valid_{0};
        uint32_t pub_sero_speed_times_ = 0;

        ControlConf control_conf_;

        void updateAebController(const std::shared_ptr <rainbowdash::aeb::AEBController>& msg);
        std::vector<std::pair<double, double>> steer_angle_linear_interpolation_;
        ExtY_AEBController_T aeb_ctrl_;
        bool is_frist_lat_sh_success = false;
    };

} // namespace zlgcandbc_aion

#endif
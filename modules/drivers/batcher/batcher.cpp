#include <chrono>
#include <map>
#include <mutex>
#include <thread>

#include "batcher_utils.h"

std::mutex channel_mutex_;
std::map<std::string, std::vector<double>> channel_times_;
const std::map<std::string, int> FREQ_THRH_ = {
    {"ad_front", 5},
    {"ad_rear", 5},
    {"ad_left_front", 5},
    {"ad_left_rear", 5},
    {"ad_right_front", 5},
    {"ad_right_rear", 5},
    {"avm_front", 5},
    {"avm_rear", 5},
    {"avm_left", 5},
    {"avm_right", 5},
    {"channel/rawimu", 50},
    {"channel/pointcloud", 5},
    {"io/wheelspeed", 20}};

bool all_channels_ready_ = false;
FILE *freq_file;

void freq_check() {
  pthread_setname_np(pthread_self(), "freq_check");
  while (true) {
    {
      std::lock_guard<std::mutex> lock(channel_mutex_);
      if (all_channels_ready_) {
        for (auto item : FREQ_THRH_) {
          auto found = channel_times_.find(item.first);
          int size = found == channel_times_.end() ? 0 : found->second.size();
          if (size < item.second) {
            printf("frequency check failed for channel %s:%d\n", item.first.c_str(), size);
            fprintf(freq_file, "%s:", item.first.c_str());
            if(found != channel_times_.end()) {
              for(auto time: found->second) {
                fprintf(freq_file, " %f", time);
              }
            }
            fprintf(freq_file, "\n");
            fflush(freq_file);
          }
        }
      } else {
        all_channels_ready_ = true;
        for (auto item : FREQ_THRH_) {
          auto found = channel_times_.find(item.first);
          if (found == channel_times_.end() || found->second.size() < static_cast<unsigned int>(item.second)) {
            all_channels_ready_ = false;
            break;
          }
        }
        if (all_channels_ready_) {
          printf("all channels are ready\n");
          for (auto item : channel_times_) {
            printf("%s:%ld\n", item.first.c_str(), item.second.size());
          }
        }
      }

      for (auto item : channel_times_) {
        item.second.clear();
      }
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  }
}

int main(int argc, char *argv[]) {
  if (argc < 2) {
    printf("Usage: %s <output_dir>\n", argv[0]);
    return 0;
  }
  string node_name = string(argv[0]) + to_string(getpid());
  const char *output_dir = argv[1];

  apollo::cyber::Init(node_name.c_str());
  auto node = apollo::cyber::CreateNode(node_name);

  std::thread t_check(freq_check);

  const char *channel_names[] = {"ad_left_front", "ad_front", "ad_right_front", "ad_left_rear", "ad_rear", "ad_right_rear", "avm_left", "avm_front", "avm_right", "avm_rear", "channel/pointcloud", "channel/imu", "io/wheelspeed"};

  int PC_IDX = 10, IMU_IDX = 11, WS_IDX = 12;
  std::vector<std::string> img_channels(channel_names, channel_names + PC_IDX);
  std::string pointcloud_channel(channel_names[PC_IDX]);
  std::string rawimu_channel(channel_names[IMU_IDX]);
  std::string ws_channel(channel_names[WS_IDX]);

  string path(output_dir);
  if (create_dir(output_dir) != 0) {
    return -1;
  }

  int channel_size = (sizeof(channel_names) / sizeof(*channel_names));
  vector<string> dirs;
  for (int i = 0; i < channel_size; i++) {
    string channel_dir = string(channel_names[i]);
    std::replace(channel_dir.begin(), channel_dir.end(), '/', '_');  // replace all '/' with '_'
    string s = path + "/" + channel_dir;
    if (create_dir(s.c_str()) != 0) {
      perror("create dir");
      return -1;
    }
    dirs.push_back(s);
  }

  int img_size = img_channels.size();
  auto batcher = std::make_shared<MixedBatcher>(img_size, 1000);

  FILE *pic_list = fopen((string(output_dir) + "/" + string("list.txt")).c_str(), "w");
  FILE *imu_file = fopen((dirs[IMU_IDX] + "/" + string("imu.txt")).c_str(), "w");
  FILE *ws_file = fopen((dirs[WS_IDX] + "/" + string("wheelspeed.txt")).c_str(), "w");
  freq_file = fopen((string(output_dir) + "/" + string("freq_check.txt")).c_str(), "w");

  // create img readers
  for (int i = 0; i < img_size; i++) {
    node->CreateReader<CompressedImage>(
        img_channels[i],
        [=](const std::shared_ptr<CompressedImage> &data) -> void {
          std::lock_guard<std::mutex> lock(channel_mutex_);
          channel_times_[img_channels[i]].push_back(data->measurement_time());
          printf("%-20s:%f, %f\n", img_channels[i].c_str(), data->measurement_time(), data->header().timestamp());
          batcher->AddImg(i, data);
        });
  }

  // create pointcloud reader
  node->CreateReader<PointCloud>(
      pointcloud_channel,
      [=](const std::shared_ptr<PointCloud> &data) -> void {
        std::lock_guard<std::mutex> lock(channel_mutex_);
        channel_times_[pointcloud_channel].push_back(data->measurement_time());
        print_pointcloud_info(pointcloud_channel, data);
        batcher->AddPointCloud(data);
      });

  // create imu reader
  node->CreateReader<Imu>(
      rawimu_channel,
      [=](const std::shared_ptr<Imu> &data) -> void {
        std::lock_guard<std::mutex> lock(channel_mutex_);
        channel_times_[rawimu_channel].push_back(data->measurement_time());
        batcher->AddImu(data);
        // print_imu_info(data);
      });

  // create wheelspeed reader
  node->CreateReader<WheelSpeed>(
      ws_channel,
      [=](const std::shared_ptr<WheelSpeed> &data) -> void {
        std::lock_guard<std::mutex> lock(channel_mutex_);
        channel_times_[ws_channel].push_back(data->measure_time());
        write_ws_info(data, ws_file);
      });

  int batch_seq = 0;
  char file_name[256];
  std::vector<std::string> timestamps(img_size+1); // 10 imgs + 1 lidar
  while (true) {
    auto batch = batcher->getBatch();
    auto start_time = std::chrono::system_clock::now();
    if (!batch) {
      printf("all batch processed, exiting...\n");
      apollo::cyber::Clear();
      break;
    }

    printf("get batch %d, time:%f, candidates size:%d\n", batch_seq, batch->pointcloud_->measurement_time(), batcher->GetCandidateSize());
    std::string time_in_us = std::to_string(long(batch->pointcloud_->header().timestamp() * 1000000));
    timestamps[img_size] = time_in_us;
    sprintf(file_name, "%s/%05d_%s.bin", dirs[PC_IDX].c_str(), batch_seq, time_in_us.c_str());
    std::string lidar_file(file_name);
    write_pointcloud(lidar_file, batch->pointcloud_);

    for(auto it = batch->imgs_.begin(); it != batch->imgs_.end(); it++) {
      int img_id = it->first;
      auto img = it->second;
      std::string time_in_us = std::to_string(long(img->header().timestamp() * 1000000));
      timestamps[img_id] = time_in_us;
      sprintf(file_name, "%s/%05d_%s.jpg", dirs[img_id].c_str(), batch_seq, time_in_us.c_str());
      FILE *f = fopen(file_name, "wb");
      fwrite(img->data().c_str(), 1, img->data().size(), f);
      fclose(f);
    }

    for (unsigned int i = 0; i < batch->imus_.size(); i++) {
      auto imu = batch->imus_[i];
      fprintf(imu_file, "image_index:%4d %f %f %f %f %f %f %f\n", batch_seq, imu->header().timestamp(),
              imu->linear_acceleration().x(),
              imu->linear_acceleration().y(),
              imu->linear_acceleration().z(),
              imu->angular_velocity().x(),
              imu->angular_velocity().y(),
              imu->angular_velocity().z());
    }

    write_list_info(pic_list, batch_seq, timestamps);
    fflush(pic_list);
    fflush(imu_file);
    fflush(ws_file);
    fflush(stdout);
    batcher->freeBatch(batch);
    batch_seq++;
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start_time);
    printf("data persistent time: %ld\n", duration.count());
  }
  fclose(pic_list);
  fclose(imu_file);
  fclose(ws_file);
  fclose(freq_file);
  printf("batcher processing done!\n");
  fflush(stdout);
}

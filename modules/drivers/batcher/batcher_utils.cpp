#include "batcher_utils.h"

// create point cloud buffer of maxlenght
int LIDAR_POINT_MAX_NUM = 1000*1000; 
double* pc_buffer_ = new double[LIDAR_POINT_MAX_NUM*5]; // 最多100w个五元组

int create_dir(const char *dir_name) {
  DIR *dir = opendir(dir_name);
  if (dir == NULL) {
    if (errno == ENOENT) {
      if (mkdir(dir_name, S_IRWXU | S_IRWXO) != 0) {
        perror("mkdir");
        printf("dirname %s\n", dir_name);
        return -1;
      } else
        return 0;
    } else {
      perror("opendir");
      printf("dirname %s\n", dir_name);
      return -1;
    }
  } else {
    return closedir(dir);
  }
  return -1;
}

std::string Point3d2Str(Point3D point) {
  std::ostringstream ss;
  ss << "(" << point.x() << "," << point.y() << "," << point.z() << ")";
  return ss.str();
}

void print_imu_info(const shared_ptr<Imu> &data) {
  // printf("%-20s:%f, %f\n", "rawimu", data->measurement_time(), data->header().timestamp());
  printf("imu time:%f, linear:%s, angular:%s, location:%s, rotation:%s\n", data->header().timestamp(),
         Point3d2Str(data->linear_acceleration()).c_str(),
         Point3d2Str(data->angular_velocity()).c_str(),
         Point3d2Str(data->location()).c_str(),
         Point3d2Str(data->rotation()).c_str());
}

void write_list_info(FILE* file, int batch_seq, std::vector<std::string>& timestamps) {
  if(timestamps.size() == 0) {
    printf("invalid timestamps for seq %d, skip...\n", batch_seq);
    return;
  }
  std::string time_str = std::accumulate(timestamps.begin(), timestamps.end(), std::string(""), [](std::string &ss, std::string& s) {
        return ss.empty() ? s : ss + "," + s;
      });
  fprintf(file, "%05d,%s\n", batch_seq, time_str.c_str());
}

void write_ws_info(const shared_ptr<WheelSpeed> &data, FILE* file) {
  double fl = data->wheelspeed_fl();
  double fr = data->wheelspeed_fr();
  double rl = data->wheelspeed_rl();
  double rr = data->wheelspeed_rr();
  long time = data->measure_time();
  fprintf(file, "%f %f %f %f %ld\n", fl, fr, rl, rr, time);
  // printf("wheel speed: %f %f %f %f %ld\n", fl, fr, rl, rr, time);
}


const int STRIDE = 5;
double* parse_pcl_pointcloud(const void* buf, int size) {
  PPointCloud cld;
  cld.points.resize(size);
  memcpy((char *)cld.points.data(), buf, sizeof(PPoint) * size);
  for (int i = 0; i < size; i++) {
    pc_buffer_[i * STRIDE + 0] = cld[i].x;
    pc_buffer_[i * STRIDE + 1] = cld[i].y;
    pc_buffer_[i * STRIDE + 2] = cld[i].z;
    pc_buffer_[i * STRIDE + 3] = cld[i].intensity;
    if(STRIDE > 4) {
      pc_buffer_[i * STRIDE + 4] = cld[i].timestamp;
    }
  }

  return pc_buffer_;
}

double* parse_fbs_pointcloud(const void* buf, int size) {
  auto pointcloud = GetFPointCloud(buf);
  if(pointcloud->points()->size() != static_cast<unsigned int>(size)) {
    printf("size mismatch, msg size:%d, fbs size:%d\n", size, pointcloud->points()->size());
  }
  // double max_timestamp = 0;
  for (int i = 0; i < size; i++) {
    auto point = pointcloud->points()->Get(i);
    pc_buffer_[i * STRIDE + 0] = point->x();
    pc_buffer_[i * STRIDE + 1] = point->y();
    pc_buffer_[i * STRIDE + 2] = point->z();
    pc_buffer_[i * STRIDE + 3] = point->intensity();
    if(STRIDE > 4) {
      pc_buffer_[i * STRIDE + 4] = point->timestamp();
    }
  }

  return pc_buffer_;
}


void print_pointcloud_info(const std::string& channel_name, const shared_ptr<PointCloud> &data) {
  printf("%-20s:%f, %f\n", channel_name.c_str(), data->measurement_time(), data->header().timestamp());

  double max_time = 0, min_time = std::numeric_limits<double>::max();
  int max_idx = 0, min_idx = 0;
  float max_x = 0, max_y = 0, max_z = 0;
  float min_x = 100, min_y = 100, min_z = 100;

  int point_size = data->width() * data->height();

  double *point_arr = data->binary_points().size() > 0 ? parse_pcl_pointcloud(data->binary_points().data(), point_size) : parse_fbs_pointcloud(data->flatbuf_points().data(), point_size);
  for(int i=0; i< point_size; i++){
    float x = point_arr[i*STRIDE], y = point_arr[i*STRIDE+1], z = point_arr[i*STRIDE+2];
    double timestamp = point_arr[i*STRIDE+4];
    if (x > max_x) max_x = x;
    if (y > max_y) max_y = y;
    if (z > max_z) max_z = z;
    if (x < min_x) min_x = x;
    if (y < min_y) min_y = y;
    if (z < min_z) min_z = z;
    if (timestamp > max_time) {
      max_time = timestamp;
      max_idx = i;
    } else if (timestamp < min_time) {
      min_time = timestamp;
      min_idx = i;
    }
  }

  printf("max_time:%f, min_time:%f, max_idx:%d, min_idx:%d, size:%d\n", max_time, min_time, max_idx, min_idx, point_size);
  printf("binary_points size:%ld, flatbuf_points size:%ld\n", data->binary_points().size(), data->flatbuf_points().size());
  printf("max_x:%f, max_y:%f, max_z:%f, min_x:%f, min_y:%f, min_z:%f\n", max_x, max_y, max_z, min_x, min_y, min_z);
}

double max_time(double* arr, int size) {
  double ret = 0.0;
  int arr_size = size * STRIDE;
  for (int i = STRIDE - 1; i < arr_size; i += STRIDE) {
    if(ret < arr[i]) {
      ret = arr[i];
    }
  }
  return ret;
}

void write_pointcloud(std::string& output_path, const shared_ptr<PointCloud> &data) {
  if(!data) {
    printf("point cloud is null, please check!\n");
    return;
  }

  int point_size = data->width() * data->height();
  if(point_size > LIDAR_POINT_MAX_NUM) {
    printf("%d exceeds max num %d, resizing pc buffer...\n", point_size, LIDAR_POINT_MAX_NUM);
    delete[] pc_buffer_;
    pc_buffer_ = new double[point_size];
    LIDAR_POINT_MAX_NUM = point_size;
  }

  double *point_arr = data->binary_points().size() > 0 ? parse_pcl_pointcloud(data->binary_points().data(), point_size) : parse_fbs_pointcloud(data->flatbuf_points().data(), point_size);

  if (STRIDE == 5) {
    double max_timestamp = max_time(point_arr, point_size);
    double diff = data->header().timestamp() - max_timestamp;
    if (diff > 10) {
      // 说明激光时光戳没有同步，需要额外加上diff
      // printf("header time:%f, max point time:%f\n", data->header().timestamp(), max_timestamp);
      for (int i = STRIDE - 1; i < point_size * STRIDE; i += STRIDE) {
        point_arr[i] += diff;
      }
    }
  }

  // printf("time: %lf, point size:%d\n", data->measurement_time(), size);
  FILE *f = fopen(output_path.c_str(), "wb");
  fwrite(point_arr, sizeof(double), point_size * STRIDE, f);
  fclose(f);
}
#include "batcher.h"

#include <sstream>
#include <dirent.h>
#include <sys/types.h>
#include <limits.h>

#include "thirdparty/recommend_protocols/drivers/flatbuf/pointcloud_generated.h"
#include "pandarGeneral_sdk/pandarGeneral_sdk.h"

#include "cyber/cyber.h"

using namespace std;

using apollo::cyber::Node;
using apollo::cyber::Reader;

using rainbowdash::common::Point3D;

using namespace Flatbuf::drivers;

int create_dir(const char *dir_name);

std::string Point3d2Str(Point3D point);

void print_imu_info(const shared_ptr<Imu> &data);

void write_ws_info(const shared_ptr<WheelSpeed> &data, FILE *file);
void write_list_info(FILE *file, int batch_seq, std::vector<std::string> &timestamps);

double *parse_pcl_pointcloud(const void *buf, int size);

double *parse_fbs_pointcloud(const void *buf, int size);

void print_pointcloud_info(const std::string &channel_name, const shared_ptr<PointCloud> &data);

double max_time(double *arr, int size);

void write_pointcloud(std::string &output_path, const shared_ptr<PointCloud> &data);

#pragma once

#include <assert.h>
#include <math.h>

#include <chrono>
#include <condition_variable>
#include <iostream>
#include <list>
#include <memory>
#include <mutex>
#include <thread>
#include <utility>
#include <vector>

#include "thirdparty/recommend_protocols/drivers/proto/camera.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/imu.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/pointcloud.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/zlgcandbc_io.pb.h"

using rainbowdash::common::Header;
using rainbowdash::drivers::CompressedImage;
using rainbowdash::drivers::Imu;
using rainbowdash::drivers::PointCloud;
using rainbowdash::drivers::WheelSpeed;

using namespace std;

const float MAX_IMG_LIDAR_DIFF_TIME = 0.1;

class MixedBatch {
 public:
  MixedBatch(int batch_size) : batch_size_(batch_size) {}

  bool AddImg(int id, const shared_ptr<CompressedImage> &pdata) {
    if (ImgsReady()) return false;
    if (img_measurement_time_ < 0) {
      img_measurement_time_ = pdata->measurement_time();
    }
    if (fabs(pdata->measurement_time() - img_measurement_time_) < img_time_range_) {
      // img_ids.push_back(id);
      // imgs.push_back(pdata);
      imgs_[id] = pdata;
      // printf("add img %f...\n", pdata->measurement_time());
      double header_time = pdata->header().timestamp();
      if (header_time < img_min_time_) {
        img_min_time_ = header_time;
      } else if (header_time > img_max_time_) {
        img_max_time_ = header_time;
      }
      return true;
    }
    return false;
  };

  bool ImgsReady() {
    return static_cast<int>(imgs_.size()) == batch_size_;
  }

  bool AddPointCloud(const shared_ptr<PointCloud> &pc) {
    double measurement_time = pc->measurement_time();
    double haeder_time = pc->header().timestamp();
    // 图片的measurement_time由于和诺瓦泰不同步，可能会存在较大偏差故弃用，转而使用header_time做判断
    bool header_time_match = (img_min_time_ < haeder_time) && (haeder_time - img_min_time_ < 0.1);
    if (header_time_match) {
      printf("found match of imgs %f(%f) & lidar %f(%f), diff:%f\n", img_min_time_, img_measurement_time_, haeder_time, measurement_time, haeder_time - img_min_time_);
      pointcloud_ = pc;
      return true;
    }
    return false;
  }

  bool AddImus(list<shared_ptr<Imu>> &imu_buf) {
    imus_.clear();
    // cout << "original imu size: " << imu_buf.size() << endl;
    auto p = imu_buf.begin();
    while (p != imu_buf.end()) {
      auto imu = (*p);
      // if (imu->header().timestamp() > measurement_time()) {
      //   break;
      // }
      // imu改为使用measurement_time和lidar做对齐
      if (imu->measurement_time() > pointcloud_->measurement_time()) {
        break;
      }
      imus_.push_back(imu);
      p = imu_buf.erase(p);
    }
    // cout << imus.size() << " imus added to batch, remaining size: " << imu_buf.size() << endl;
    return true;
  }

  void Reset() {
    // img_ids.clear();
    imgs_.clear();
    imus_.clear();
    pointcloud_.reset();
    img_measurement_time_ = -1.0;
    img_min_time_ = std::numeric_limits<double>::max();
    img_max_time_ = 0;
  }

  double measurement_time() { return img_min_time_; }

 public:
  double img_measurement_time_ = -1.0;  // time in seconds
  double img_min_time_ = std::numeric_limits<double>::max();
  double img_max_time_ = 0;
  vector<shared_ptr<Imu>> imus_;
  map<int, shared_ptr<CompressedImage>> imgs_;
  shared_ptr<PointCloud> pointcloud_;
  int batch_size_;
  double img_time_range_ = 0.01;
};

class MixedBatcher {
 public:
  typedef list<shared_ptr<MixedBatch>> BatchBuffer;

  MixedBatcher(int batch_size, int buffer_size = 10) {
    for (int i = 0; i < buffer_size; i++) {
      free_buf.push_back(make_shared<MixedBatch>(batch_size));
    }
    _batch_size = batch_size;
    _image_buffer_size = buffer_size;
    _lidar_buffer_size = _image_buffer_size * 2;
    _imu_buffer_size = _image_buffer_size * IMUS_PER_IMG;
  }
  ~MixedBatcher() {
  }

  // TODO: add timetout parameter
  shared_ptr<MixedBatch> getBatch() {
    unique_lock<mutex> lock(_m);
    while (ready_buf.size() <= 0) {
      if(_cv.wait_for(lock, std::chrono::seconds(10)) == std::cv_status::timeout) {
        printf("wait timeout for 10 seconds, return null...\n");
        return nullptr;
      }
    }
      
    auto batch = ready_buf.front();
    ready_buf.pop_front();
    return batch;
  }

  int GetCandidateSize() {
    return candidate_buf.size();
  }

  void freeBatch(shared_ptr<MixedBatch> batch) {
    unique_lock<mutex> lock(_m);
    free_buf.push_back(batch);
  }

  void AddPointCloud(const shared_ptr<PointCloud> &pdata) {
    unique_lock<mutex> lock(_m);
    Add2Buf(pointcloud_buf, pdata, _lidar_buffer_size);
  }

  void AddImu(const shared_ptr<Imu> &pdata) {
    unique_lock<mutex> lock(_m);
    double cur_time = pdata->measurement_time();
    const double EPS = 1e-8;
    if(std::fabs(cur_time - last_imu_timestamp_) < EPS) {
      printf("found imu with same timestamp %f, ignore...\n", cur_time);
      return;
    }

    Add2Buf(imu_buf, pdata, _imu_buffer_size);
    last_imu_timestamp_ = cur_time;
  }

  template <class T>
  inline void Add2Buf(list<shared_ptr<T>> &buf, const shared_ptr<T> &pdata, int size) {
    if (buf.size() >= static_cast<unsigned int>(size)) {
      auto fnt = buf.front();
      printf("buf size %ld is larger than %d, time:%f, dropping...\n", buf.size(), size, fnt->measurement_time());
      buf.pop_front();
    }
    auto p = buf.begin();
    while (p != buf.end()) {
      if (pdata->measurement_time() < (*p)->measurement_time()) {
        buf.insert(p, pdata);
        return;
      }
      p++;
    }

    buf.push_back(pdata);
  }

  bool AbleToPublish(shared_ptr<MixedBatch> candidate) {
    // 发布条件：图片数据完整，有lidar数据或等待lidar超时
    if (!candidate->ImgsReady()) {
      return false;
    }
    // check lidar
    for (auto p = pointcloud_buf.begin(); p != pointcloud_buf.end(); p++) {
      auto pointcloud = (*p);
      double pc_header_time = pointcloud->header().timestamp();
      if (pc_header_time - candidate->measurement_time() > MAX_IMG_LIDAR_DIFF_TIME) {
        break;
      }
      if (candidate->AddPointCloud(pointcloud)) {
        pointcloud_buf.erase(p);
        candidate->AddImus(imu_buf);
        return true;
      }
    }

    // check delay time
    // double delay = last_img_timestamp_ - candidate->measurement_time();
    // if (delay > MAX_WAIT_TIME_4_LIDAR) {
    //   cout << "publish imgs without lidar, timestamp: " << candidate->measurement_time() << endl;
    //   candidate->AddImus(imu_buf);
    //   return true;
    // }
    return false;
  }

  void AddImg(int id, const shared_ptr<CompressedImage> &pdata) {
    unique_lock<mutex> lock(_m);
    double timestamp = pdata->header().timestamp();
    if (timestamp > last_img_timestamp_) {
      last_img_timestamp_ = timestamp;
    }
    shared_ptr<CompressedImage> data = pdata;
    // cout<<"add data timestamp:"<<timestamp<<endl;
    bool inserted = false;
    auto c = candidate_buf.begin();
    while(c != candidate_buf.end()) {
      auto candidate = *c;
      inserted = candidate->AddImg(id, data);

      if (AbleToPublish(candidate)) {
        c = candidate_buf.erase(c);
        ready_buf.push_back(candidate);
        _cv.notify_one();
      } else {
        c++;
      }

      if (inserted) {
        return;
      }
    }

    shared_ptr<MixedBatch> new_batch;
    if (free_buf.size() > 0) {
      new_batch = free_buf.front();
      free_buf.pop_front();
    } else {
      // In most cases, old data is at front.
      if (candidate_buf.size() >= _image_buffer_size) {
        new_batch = candidate_buf.front();
        candidate_buf.pop_front();
        printf("drop old candidate, size:%ld, timestamp:%f, cur_time:%f\n", new_batch->imgs_.size(), new_batch->measurement_time(), last_img_timestamp_);
      } else if (ready_buf.size() >= _image_buffer_size) {
        new_batch = ready_buf.front();
        ready_buf.pop_front();
        printf("drop ready batch, size:%ld, timestamp:%f, cur_time:%f\n", new_batch->imgs_.size(), new_batch->measurement_time(), last_img_timestamp_);
      } else {
        // cerr << "warning: batch buffer is not enough" << endl;
        new_batch = make_shared<MixedBatch>(_batch_size);
      }
    }
    new_batch->Reset();
    new_batch->AddImg(id, data);

    Add2Buf(candidate_buf, new_batch, _image_buffer_size);
    // candidate_buf.push_back(new_batch);
  }

 private:
  BatchBuffer free_buf;
  BatchBuffer candidate_buf;
  BatchBuffer ready_buf;
  list<shared_ptr<PointCloud>> pointcloud_buf;
  list<shared_ptr<Imu>> imu_buf;
  unsigned int _batch_size;
  condition_variable _cv;
  mutex _m;
  double last_img_timestamp_ = 0.0;
  double last_imu_timestamp_ = 0.0;
  unsigned int _image_buffer_size;
  unsigned int _lidar_buffer_size;
  unsigned int _imu_buffer_size;
  const int IMUS_PER_IMG = 10;
  const float MAX_WAIT_TIME_4_LIDAR = 2.0;
};
set(HESAI_SDK "thirdparty/hesailidar_general_sdk/")
set(BATCHER "modules/drivers/batcher/")
find_package(PCL REQUIRED QUIET)
#find_package(Boost REQUIRED COMPONENTS thread)
add_executable(batcher_server ${BATCHER}/batcher.cpp ${BATCHER}/batcher_utils.cpp ${PROTO_CC_FILES}  ${FLATBUF_GEN_FILES})
target_include_directories(batcher_server PRIVATE ${BATCHER} PRIVATE "${HESAI_SDK}/include" PRIVATE "${HESAI_SDK}/src/PandarGeneralRaw/include" PRIVATE ${BOOST_INCLUDE_DIRS} ${PCL_INCLUDE_DIRS})
target_link_libraries(batcher_server PandarGeneralSDK cyber protobuf protobuf-lite atomic uuid fastrtps fastcdr pthread m z rt gflags glog ${PCL_IO_LIBRARIES} ${BOOST_LIBRARIES})

/*
 * Copyright (C) 2013-2019 <PERSON>.
 * Contact: <EMAIL>
 *
 * This file is part of <PERSON>'s Toolkit.
 *
 * Commercial License Usage
 * Licensees holding valid commercial licenses may use this file in
 * accordance with the commercial license agreement provided with the
 * Software or, alternatively, in accordance with the terms contained in
 * a written agreement between you and <PERSON>.
 *
 * GNU General Public License 3.0 Usage
 * Alternatively, this file may be used under the terms of the GNU
 * General Public License version 3.0 as published by the Free Software
 * Foundation and appearing in the file LICENSE.GPL included in the
 * packaging of this file.  Please review the following information to
 * ensure the GNU General Public License version 3.0 requirements will be
 * met: http://www.gnu.org/copyleft/gpl.html.
 */

#include <Vector/DBC/SignalType.h>

namespace Vector {
namespace DBC {

std::ostream & operator<<(std::ostream & os, const SignalType & signalType) {
    os << "SGTYPE_ " << signalType.name;
    os << " : " << signalType.size;
    os << '@' << char(signalType.byteOrder);
    os << ' ' << char(signalType.valueType);
    os << ' ' << signalType.defaultValue;
    os << ", " << signalType.valueTable;
    os << ';' << endl;

    return os;
}

}
}

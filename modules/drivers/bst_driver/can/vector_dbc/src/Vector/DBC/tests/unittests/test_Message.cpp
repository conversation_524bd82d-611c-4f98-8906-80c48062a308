#define BOOST_TEST_MODULE Message
#if !defined(WIN32)
#define BOOST_TEST_DYN_LINK
#endif
#include <boost/test/unit_test.hpp>

#include <cstdint>
#include <fstream>
#include <string>
#include <vector>
#include <boost/filesystem.hpp>

#include <Vector/DBC.h>

BOOST_AUTO_TEST_CASE(Message) {
    Vector::DBC::Network network;

    /* load database file */
    boost::filesystem::path infile(CMAKE_CURRENT_SOURCE_DIR "/data/Database.dbc");
    const std::string & infilename = infile.string();
    std::ifstream ifs(infilename);
    ifs >> network;
    BOOST_REQUIRE(network.successfullyParsed);

    /* define message data */
    std::vector<uint8_t> messageData;
    messageData.push_back(0x00);
    messageData.push_back(0x00);
    messageData.push_back(0x00);
    messageData.push_back(0x00);
    messageData.push_back(0x00);
    messageData.push_back(0x00);
    messageData.push_back(0x00);
    messageData.push_back(0x00);

    /* get message */
    Vector::DBC::Message & message = network.messages[0xC0000000];
    BOOST_CHECK_EQUAL(message.id, 0xC0000000);
    BOOST_CHECK_EQUAL(message.name, "VECTOR__INDEPENDENT_SIG_MSG");
    BOOST_CHECK_EQUAL(message.size, 0);
    BOOST_CHECK_EQUAL(message.transmitter, "");
    BOOST_CHECK_EQUAL(message.comment, "This is a message for not used signals, created by Vector CANdb++ DBC OLE DB Provider.");

    /* check signals */
    Vector::DBC::Signal & signal = message.signals["Signal_8_Motorola_Unsigned"];
    uint64_t rawValue = signal.decode(messageData);
    BOOST_CHECK_EQUAL(rawValue, 0x00000000);

    /* check transmitters */
    BOOST_CHECK_EQUAL(message.transmitters.size(), 0);

    /* check signal groups */
    BOOST_CHECK_EQUAL(message.signalGroups.size(), 0);

    /* check attribute values */
    BOOST_CHECK_EQUAL(message.attributeValues.size(), 0);

    /* check message transmitters */
    message = network.messages[1];
    BOOST_CHECK_EQUAL(message.transmitters.size(), 2);
    BOOST_CHECK_EQUAL(message.transmitters.count("Node_1"), 1);
    BOOST_CHECK_EQUAL(message.transmitters.count("Node_2"), 1);
}

/*
 * This tests checks different variants on message seperation in the input file.
 * Even if this is not generated by original tools, it is commonly found.
 */
BOOST_AUTO_TEST_CASE(MessageSeparation) {
    Vector::DBC::Network network;

    /* load database file */
    boost::filesystem::path infile(CMAKE_CURRENT_SOURCE_DIR "/data/MessageSeparation.dbc");
    const std::string & infilename = infile.string();
    std::ifstream ifs(infilename);
    ifs >> network;
    BOOST_REQUIRE(network.successfullyParsed);

    Vector::DBC::Message & message = network.messages[1];
    BOOST_REQUIRE(message.id == 1);
    BOOST_CHECK(message.name == "Message_1");
    Vector::DBC::Signal & signal = message.signals["Signal_1"];
    BOOST_CHECK(signal.name == "Signal_1");

    /*
     * The second message is separated by a line with one space.
     * In previous versions this wasn't interpreted as message separator.
     * Hence the signals of the second message were appended to the first message.
     * chomp was modified to also chop away spaces and tabs.
     */
    message = network.messages[2];
    BOOST_REQUIRE(message.id == 2);
    BOOST_CHECK(message.name == "Message_2");
    signal = message.signals["Signal_2"];
    BOOST_CHECK(signal.name == "Signal_2");

    /*
     * The third message is not separated by a line at all.
     * In previous versions this wasn't interpreted as message separator.
     * Hence the following message was assumed to be a signal too.
     * The file::load method was modified to cope with this.
     */
    message = network.messages[3];
    BOOST_REQUIRE(message.id == 3);
    BOOST_CHECK(message.name == "Message_3");
    signal = message.signals["Signal_3"];
    BOOST_CHECK(signal.name == "Signal_3");

    /*
     * In Value Descriptions a description with leading space let to undefined behavior.
     * Example: 1 " Error B" 0 " Error A"
     * The algorithm to evaluate these value/description pairs was rewritten.
     */
    message = network.messages[2];
    signal = message.signals["Signal_2"];
    Vector::DBC::ValueDescriptions & valueDescriptions = signal.valueDescriptions;
    BOOST_CHECK(valueDescriptions[0] == " Error A");
    BOOST_CHECK(valueDescriptions[1] == " Error B");
}

/*
 * Checks multi-line comments, where the last sign is a line-break.
 * Before the fix this resulting in an string access at position -1.
 */
BOOST_AUTO_TEST_CASE(MultiLineComments) {
    Vector::DBC::Network network;

    /* load database file */
    boost::filesystem::path infile(CMAKE_CURRENT_SOURCE_DIR "/data/MessageSeparation.dbc");
    const std::string & infilename = infile.string();
    std::cout << "Input file: " << infilename << std::endl;
    std::ifstream ifs(infilename);
    ifs >> network;
    BOOST_REQUIRE(network.successfullyParsed);

    /* get message and signal */
    Vector::DBC::Message & message = network.messages[1];
    BOOST_REQUIRE_EQUAL(message.id, 1);
    Vector::DBC::Signal & signal = message.signals["Signal_1"];
    BOOST_REQUIRE_EQUAL(signal.name, "Signal_1");

    /* check if the comment is correctly parsed */
    BOOST_CHECK_EQUAL(signal.comment, "Comment\r\n");
}

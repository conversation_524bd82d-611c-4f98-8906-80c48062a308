cmake_minimum_required(VERSION 3.10)
include(GNUInstallDirs)
project(can_hal_test)

set(CMAKE_CXX_FLAGS " -std=c++17")

if (NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release")
endif()

add_subdirectory(${PROJECT_SOURCE_DIR}/vector_dbc)

add_compile_options("-O2")

# test executable
add_executable(can_hal_test main.cpp)
target_link_libraries(can_hal_test can_hal can_hal_config_proto ads_log)

add_executable(candrive candrive.cpp header.pb.cc aion_control.pb.cc aion_chassis.pb.cc)
target_link_libraries(candrive can_hal can_hal_config_proto ads_log)
target_link_libraries(candrive  Vector_DBC autoplt cyber cyber_proto fastrtps protobuf pthread )
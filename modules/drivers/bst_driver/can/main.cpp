#include <iostream>

#include "cyber/common/file.h"
#include "modules/sensor_hal/can_hal/can_hal.h"

// using namespace std;
using namespace autoplt::sensor_manager::hal;

class CanCbTest : public CanCbIf {
   public:
    CanCbTest() {}

    void callback_fun(CanDev_t can_channel,
                      const hal_can_frame_t& hal_can_frame_inst) override {
        int delta_can_frame_time =
            hal_can_frame_inst.timestamp_nano / 1e6 -
            m_last_can_frame_time;
        m_last_can_frame_time =
            static_cast<long>(hal_can_frame_inst.timestamp_nano / 1e6);
        std::cout << "[CAN_HAL_TEST] can channel " << dec << can_channel
                  << " recieved data" << std::endl;
        std::cout << "[CAN_HAL_TEST] can frame counter " << dec
                  << m_counter++ << std::endl;
        std::cout << "[CAN_HAL_TEST] can ID: "
                  << "0x" << hex << hal_can_frame_inst.can_id << std::endl;
        std::cout << "[CAN_HAL_TEST] can dlc: " << dec
                  << (int)hal_can_frame_inst.dlc << std::endl;
        std::cout << "[CAN_HAL_TEST] can delta timestamp ms: " << dec
                  << delta_can_frame_time << std::endl;
        std::cout << "[CAN_HAL_TEST] can data: ";
        for (int i = 0; i < hal_can_frame_inst.dlc; i++) {
            std::cout << "0x" << hex << (int)hal_can_frame_inst.data_buf[i]
                      << " ";
        }
        std::cout << std::endl;
    }

   private:
    long m_last_can_frame_time = 0;
    uint64_t m_counter = 0;
};

/**
 * @brief 使用方法
 *
 * @return int
 */
int main(int, char**) {
    CanHal& can_instance = CanHal::GetInstance();
    std::string can_config_path = "./can_hal_config.pb.txt";
    std::shared_ptr<CanCbTest> can_cb_test_1 = make_shared<CanCbTest>();
    std::shared_ptr<CanCbTest> can_cb_test_2 = make_shared<CanCbTest>();
    uint64_t counter = 0;
    CanCbId_t can_cb_test_1_id = 0;
    CanCbId_t can_cb_test_2_id = 0;

    /* 初始化CAN总线 */
    if (can_instance.CanChannelInit(can_config_path)) {
        std::cout << "[CAN_HAL_TEST] can init success!" << std::endl;
    }

    can_cb_test_1_id =
        can_instance.CanRegisterCallback(CAN_DEV_0, can_cb_test_1);
    can_cb_test_2_id =
        can_instance.CanRegisterCallback(CAN_DEV_1, can_cb_test_2);
    std::cout << "[CAN_HAL_TEST] wating for can frame..." << std::endl;
    std::cout << "can_cb_test_1_id : " << can_cb_test_1_id << "  can_cb_test_2_id : "<< can_cb_test_2_id << std::endl;
    uint8_t can_data_buf[8] = {0, 1, 2, 3, 4, 5, 6, 7};
    while (1) {
        counter++;
        sleep(1);

        // /*
        //  Test CanUnregisterCallback & CanRegisterCallback
        // */
        // if (counter == 5) {
        //     can_instance.CanUnregisterCallback(CAN_DEV_0,
        //                                        can_cb_test_1_id);
        // }
        // if (counter == 10) {
        //     can_instance.CanRegisterCallback(CAN_DEV_0, can_cb_test_1);
        // }
        /* Test CanUnregisterCallback & CanRegisterCallback Finished */
        can_instance.Write(CAN_DEV_1, 0x100, 8, can_data_buf);
    }
    return 0;
}

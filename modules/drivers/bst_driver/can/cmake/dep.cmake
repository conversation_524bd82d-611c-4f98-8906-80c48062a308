set(BST_CAN "${CMAKE_SOURCE_DIR}/modules/drivers/bst_driver/can")
if( ${BUILD_BST_COMPILE})
    include(GNUInstallDirs)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")

    if (NOT CMAKE_BUILD_TYPE)
        set(CMAKE_BUILD_TYPE "Release")
    endif()

    add_compile_options("-O2")

    # test executable
    add_executable(can_hal_test ${BST_CAN}/main.cpp)
    target_link_libraries(can_hal_test can_hal can_hal_config_proto ads_log)

    add_executable(candrive ${BST_CAN}/candrive.cpp ${PROTO_CC_FILES})
    target_link_libraries(candrive can_hal can_hal_config_proto ads_log)
    target_link_libraries(candrive  Vector_DBC autoplt cyber cyber_proto fastrtps protobuf pthread )
endif()
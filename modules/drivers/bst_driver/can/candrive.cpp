#include <iostream>
#include <thread>             // std::thread
#include <mutex>              // std::mutex, std::unique_lock
#include <condition_variable> // std::condition_variable
#include <autoplt/include/ADSNode.h>
#include <autoplt/include/ADSTime.h>
#include "cyber/common/file.h"
#include "modules/sensor_hal/can_hal/can_hal.h"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_chassis.pb.h"
#include "thirdparty/recommend_protocols/drivers/aion_io/proto/aion_control.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"
#include "chassis_info.h"
#include <Vector/DBC.h>

// using namespace std;
using namespace autoplt;
using namespace autoplt::sensor_manager::hal;
using namespace aion_io::chassisinfo;
using namespace rainbowdash::control_by_wire;

#define G 9.8

Vector::DBC::Network dbc_network;
std::shared_ptr<ADSNode> can_node = nullptr;
std::shared_ptr<Writer<AionWheelSpeed>> pWriterAionWheelSpeed = nullptr;
std::shared_ptr<Writer<WheelSpeedInfo>> pWriterWheelSpeed = nullptr;
std::shared_ptr<Writer<AionChassisInfo>> pWriterChassisInfo = nullptr;
std::atomic<bool> chassis_info_ready_;
ChassisInfo chassis_info_;
std::mutex mtx;
std::condition_variable cv;
std::atomic_bool send_exit(false);

typedef struct Signal {
    std::string name;
    unsigned int raw;
    double physical;
    bool use_raw;
    Signal(const std::string name, const unsigned int raw) :
      name(name), raw(raw), use_raw(true) {}
    Signal(const std::string name, const double physical) :
      name(name), physical(physical), use_raw(false) {}
    Signal(const std::string name, const unsigned int raw, const double physical) :
      name(name), raw(raw), physical(physical) {}
    Signal(const std::string name, const unsigned int raw, const double physical, const bool use_raw) :
      name(name), raw(raw), physical(physical), use_raw(use_raw) {}
  } Signal;

  typedef struct Message {
    uint64_t timestamp;
    std::string name;
    std::vector<Signal> signals;
    ~Message(){}
    Message(const uint64_t timestamp, const std::string name) :
      timestamp(timestamp),name(name) {}
  } Message;

  enum class Status
{
    OK = 0,
    ERROR = 1,

};

std::shared_ptr<Message> decoded_message = nullptr;
  std::shared_ptr<Message> decodeMessage(uint64_t timestamp, unsigned int canIdentifier, 
                                       std::vector<std::uint8_t>& canData) {
    auto& messages = dbc_network.messages;
    auto search = messages.find(canIdentifier);
    if (search == messages.end()) {
      std::cout << "Not found: " << canIdentifier << std::endl;
      return nullptr;
    }

    Vector::DBC::Message& message = messages[canIdentifier];

    std::shared_ptr<Message> output(new Message(timestamp, message.name));

    for (const auto& signal : message.signals) {
      std::cout << "  Signal " << signal.second.name << std::endl;
      unsigned int rawValue = signal.second.decode(canData);
      std::cout << "    Raw Value: 0x" << rawValue << std::endl;
      double physicalValue = signal.second.rawToPhysicalValue(rawValue);
      std::cout << "    Physical Value: " << physicalValue << std::endl;

      Signal temp(signal.second.name, rawValue, physicalValue);
      output->signals.push_back(temp);
    }

    return output;
  }

  void writewheelspeed(std::shared_ptr<Message> decoded_message){

  }

  void writechassisinfo(std::shared_ptr<Message> decoded_message){

  }
class CanCbTest : public CanCbIf {
   public:
    CanCbTest() {}

    void callback_fun(CanDev_t can_channel,
                      const hal_can_frame_t& hal_can_frame_inst) override {
        std::cout << "----------------------------------------" << std::endl;
        int delta_can_frame_time =
            hal_can_frame_inst.timestamp_nano / 1e6 -
            m_last_can_frame_time;
        m_last_can_frame_time =
            static_cast<long>(hal_can_frame_inst.timestamp_nano / 1e6);
        std::cout << "[CAN_HAL_TEST] can channel " << dec << can_channel
                  << " recieved data" << std::endl;
        std::cout << "[CAN_HAL_TEST] can frame counter " << dec
                  << m_counter++ << std::endl;
        std::cout << "[CAN_HAL_TEST] can ID: "
                  << "0x" << hex << hal_can_frame_inst.can_id << std::endl;
        std::cout << "[CAN_HAL_TEST] can dlc: " << dec
                  << (int)hal_can_frame_inst.dlc << std::endl;
        std::cout << "[CAN_HAL_TEST] can delta timestamp ms: " << dec
                  << delta_can_frame_time << std::endl;
        std::vector<std::uint8_t> can_data(hal_can_frame_inst.data_buf, hal_can_frame_inst.data_buf + hal_can_frame_inst.dlc);
        std::cout << "[CAN_HAL_TEST] can data: ";
        for (int i = 0; i < hal_can_frame_inst.dlc; i++) {
            std::cout << "0x" << hex << (int)hal_can_frame_inst.data_buf[i]
                      << " ";
        }
        std::cout << std::endl;
        std::unique_lock<std::mutex> lck(mtx);
        decoded_message = decodeMessage(m_last_can_frame_time, hal_can_frame_inst.can_id, can_data);
        cv.notify_one();
        std::cout << "----------------------------------------" << std::endl;
    }

   private:
    long m_last_can_frame_time = 0;
    uint64_t m_counter = 0;
};

Status PraseAcmBody(std::shared_ptr<Message>& canmsg){
    auto signals = canmsg->signals;
    for(auto s:signals){
    if("LatitudeAcc" == s.name){
        chassis_info_.LatitudeAcc = s.physical*G;
        AINFO << __FUNCTION__ << ": Cur_LatitudeAcc=" << s.physical;
    }
    else if("LatitudeAcc" == s.name){
        chassis_info_.YawRate = s.physical;
        AINFO << __FUNCTION__ << ": Cur_YawRate=" << s.physical;
    }
    else if("LatitudeAcc" == s.name){
        chassis_info_.LongitudeAcc = s.physical*G;
        AINFO << __FUNCTION__ << ": Cur_LongitudeAcc=" << s.physical;
    }
    else{
        AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
    }
    return Status::OK;
}

Status PraseBrkSymSts(std::shared_ptr<Message>& canmsg){
auto signals = canmsg->signals;
for(auto s:signals){
    if("CDDSActv" == s.name){
    chassis_info_.CDDSActv = s.physical;
    AINFO << __FUNCTION__ << ": Cur_CDDSActv=" << s.physical;
    }
    else{
    AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
}
return Status::OK;
}

Status PraseEPB_02(std::shared_ptr<Message>& canmsg){
auto signals = canmsg->signals;
for(auto s:signals){
    if("Vehspd" == s.name){
    chassis_info_.Vehspd = s.physical;
    AINFO << __FUNCTION__ << ": Cur_Vehspd=" << s.physical;
    }
    else if("BrkPedPst" == s.name){
    chassis_info_.BrkPedPst = s.physical;
    AINFO << __FUNCTION__ << ": Cur_BrkPedPst=" << s.physical;
    }
    else{
    AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
}
return Status::OK;
}

Status PraseEPSSts(std::shared_ptr<Message>& canmsg){
    auto signals = canmsg->signals;
    for(auto s:signals){
    if("EPS_Steer_angledir" == s.name){
        chassis_info_.EPS_Steer_angledir = s.physical;
        AINFO << __FUNCTION__ << ": Cur_EPS_Steer_angledir=" << s.physical;
    }
    else if("EPS_Steeragspddir" == s.name){
        chassis_info_.EPS_Steeragspddir = s.physical;
        AINFO << __FUNCTION__ << ": Cur_EPS_Steeragspddir=" << s.physical;
    }
    else if("EPS_SteeringAngle" == s.name){
        chassis_info_.EPS_SteeringAngle = s.physical;
        AINFO << __FUNCTION__ << ": Cur_EPS_SteeringAngle=" << s.physical;
    }
    else if("EPS_FaultSts" == s.name){
        chassis_info_.EPS_FaultSts = s.physical;
        AINFO << __FUNCTION__ << ": Cur_EPS_FaultSts=" << s.physical;
    }
    else if("EPS_LatCtrlMode" == s.name){
        chassis_info_.EPS_LatCtrlMode = s.physical;
        AINFO << __FUNCTION__ << ": Cur_EPS_LatCtrlMode=" << s.physical;
    }
    else if("EPS_SteeringAngleSpd" == s.name){
        chassis_info_.EPS_SteeringAngleSpd = s.physical;
        AINFO << __FUNCTION__ << ": Cur_EPS_SteeringAngleSpd=" << s.physical;
    }
    else{
        AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
    }
    return Status::OK;
}

Status PraseVCU_13(std::shared_ptr<Message>& canmsg){
    auto signals = canmsg->signals;
    for(auto s:signals){
    if("VCU_CrntGearLvl" == s.name){
        chassis_info_.VCU_CrntGearLvl = s.physical;
        AINFO << __FUNCTION__ << ": Cur_VCU_CrntGearLvl=" << s.physical;
    }
    else{
        AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
    }
    return Status::OK;
}

Status PraseWHLspdFront(std::shared_ptr<Message>& canmsg){
    auto signals = canmsg->signals;
    for(auto s:signals){
    if("WhlspdFLdir" == s.name){
        chassis_info_.WheelSpeedFront.WhlspdFLdir = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdFLdir=" << s.physical;
    }
    else if("WhlspdFLsts" == s.name){
        chassis_info_.WheelSpeedFront.WhlspdFLsts = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdFLsts=" << s.physical;
    }
    else if("WhlspdFR" == s.name){
        chassis_info_.WheelSpeedFront.WhlspdFR = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdFR=" << s.physical;
    }
    else if("WhlspdFRdir" == s.name){
        chassis_info_.WheelSpeedFront.WhlspdFRdir = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdFRdir=" << s.physical;
    }
    else if("WhlspdFRsts" == s.name){
        chassis_info_.WheelSpeedFront.WhlspdFRsts = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdFRsts=" << s.physical;
    }
    else if("WhlspdFL" == s.name){
        chassis_info_.WheelSpeedFront.WhlspdFL = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdFL=" << s.physical;
    }
    else{
        AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
    }
    return Status::OK;
}

Status PraseWHLspdRear(std::shared_ptr<Message>& canmsg){
    auto signals = canmsg->signals;
    for(auto s:signals){
    if("WhlspdRLdir" == s.name){
        chassis_info_.WheelSpeedRear.WhlspdRLdir = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdRLdir=" << s.physical;
    }
    else if("WhlspdRLsts" == s.name){
        chassis_info_.WheelSpeedRear.WhlspdRLsts = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdRLsts=" << s.physical;
    }
    else if("WhlspdRR" == s.name){
        chassis_info_.WheelSpeedRear.WhlspdRR = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdRR=" << s.physical;
    }
    else if("WhlspdRRdir" == s.name){
        chassis_info_.WheelSpeedRear.WhlspdRRdir = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdRRdir=" << s.physical;
    }
    else if("WhlspdRRsts" == s.name){
        chassis_info_.WheelSpeedRear.WhlspdRRsts = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdRRsts=" << s.physical;
    }
    else if("WhlspdRL" == s.name){
        chassis_info_.WheelSpeedRear.WhlspdRL = s.physical;
        AINFO << __FUNCTION__ << ": Cur_WhlspdRL=" << s.physical;
    }
    else{
        AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
    }
    return Status::OK;
}

Status PraseVehicleLights(std::shared_ptr<Message>& canmsg){
    auto signals = canmsg->signals;
    for(auto s:signals){
    if("BCM_TurnLampSt" == s.name){
        chassis_info_.BCM_TurnLampSt = s.physical;
        AINFO << __FUNCTION__ << ": Cur_BCM_TurnLampSt=" << s.physical;
    }
    else{
        AINFO << __FUNCTION__ << ": Cur_PraseOtherSignal -> "<< s.name;
    }
    }
    return Status::OK;
}

Status UpdateChassisInfo(){

    AINFO << __FUNCTION__ << ": dbc_recv_start.";
    auto can_msg = decoded_message;
    // auto can_msg = dbc_.Recv();
    // auto can_msgs = decoded_message;
    // AINFO << __FUNCTION__ << ": dbc_recv_finish.";

    // if (can_msgs.empty()) {
    // AINFO << __FUNCTION__ << ": get empty can_msg.";
    // return Status::ERROR;
    // }
    // else{
    // AINFO << __FUNCTION__ << ": get "<<can_msgs.size()<<" can_msg...";
    // // std::cout<< __FUNCTION__ << ": receive "<<can_msgs.size()<<" can_msg..."<<std::endl;
    // }

    // int can_msgs_size = can_msgs.size();
    // for(int i=0;i<can_msgs_size;i++){
    // // std::cout<<"i = "<<i<<std::endl;
    // auto can_msg = can_msgs.at(i);
    // if(can_msg == nullptr){
    //     continue;
    // }
    //AINFO << __FUNCTION__ << ": can_msg["<<i<<"] timestamp is "<<can_msg->timestamp;
    // std::cout<<"i = "<<i<<", can_msg->name = "<<can_msg->name<<std::endl;
    if(can_msg == nullptr){
        return Status::ERROR;
    }
    if("AcmBody" == can_msg->name){
        AINFO << __FUNCTION__ << ": praseAcmBody.";
        PraseAcmBody(can_msg);
        chassis_info_ready_ = true;
    }
    else if("BrkSymSts" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseBrkSymSts.";
        chassis_info_ready_ = true;
        PraseBrkSymSts(can_msg);
    }
    else if("EPB_02" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseEPB_02.";
        chassis_info_ready_ = true;
        PraseEPB_02(can_msg);
    }
    else if("EPSSts" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseEPSSts.";
        chassis_info_ready_ = true;
        PraseEPSSts(can_msg);
    }
    else if("VCU_13" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseVCU_13.";
        chassis_info_ready_ = true;
        PraseVCU_13(can_msg);
    }
    else if("WHLspdFront" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseWHLspdFront.";
        chassis_info_ready_ = true;
        PraseWHLspdFront(can_msg);
    }
    else if("WHLspdRear" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseWHLspdRear.";
        chassis_info_ready_ = true;
        PraseWHLspdRear(can_msg);
    }
    else if("VehicleLights" == can_msg->name){
        AINFO << __FUNCTION__ << ": PraseVehicleLights.";
        chassis_info_ready_ = true;
        PraseVehicleLights(can_msg);
    }
    else{
        AINFO << __FUNCTION__ << ": PraseOtherMsg -> "<< can_msg->name;
    }
    //}

    if(false == chassis_info_ready_){
    return Status::ERROR;
    }
    return Status::OK;
}

Status DealChassisInfo(ChassisInfoDeal& chassis_info_deal){
      // ChassisInfoDeal chassis_info_deal;
      static double last_eps_steer_angle = 0.0;
      static bool first_set_steer_angle_flag = false;
      static bool steer_angle_speed_dir = false;
      chassis_info_deal.velocity = chassis_info_.Vehspd;
      chassis_info_deal.lon_acceleration = chassis_info_.LongitudeAcc;
      chassis_info_deal.lat_acceleration = chassis_info_.LatitudeAcc;
      chassis_info_deal.yaw_rate = chassis_info_.YawRate;
      chassis_info_deal.brake_system_status = chassis_info_.CDDSActv;
      chassis_info_deal.brake_padal_position = chassis_info_.BrkPedPst;
      chassis_info_deal.eps_steer_angle = (chassis_info_.EPS_Steer_angledir == 0)?(-chassis_info_.EPS_SteeringAngle):(chassis_info_.EPS_SteeringAngle);
      if(false == first_set_steer_angle_flag){
        first_set_steer_angle_flag = true;
        last_eps_steer_angle = chassis_info_deal.eps_steer_angle;
      }
      // bool steer_angle_speed_dir = static_cast<bool>(chassis_info_deal.eps_steer_angle - last_eps_steer_angle > 0);
      if(chassis_info_deal.eps_steer_angle - last_eps_steer_angle > 0){
        steer_angle_speed_dir = true;
      }
      else if(chassis_info_deal.eps_steer_angle - last_eps_steer_angle < 0){
        steer_angle_speed_dir = false;
      }
      chassis_info_deal.eps_steer_angle_speed = (steer_angle_speed_dir == false)?(chassis_info_.EPS_SteeringAngleSpd):(-chassis_info_.EPS_SteeringAngleSpd);
      chassis_info_deal.eps_fault_status = chassis_info_.EPS_FaultSts;
      chassis_info_deal.eps_lat_ctl_mode = chassis_info_.EPS_LatCtrlMode;
      chassis_info_deal.vcu_current_gear_level = chassis_info_.VCU_CrntGearLvl;
      chassis_info_deal.vcu_actual_vehicle_wheel_torque = chassis_info_.VCU_ActVehWheelTorq;
      chassis_info_deal.bcm_turn_lamp_status = chassis_info_.BCM_TurnLampSt;
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        chassis_info_deal.wheel_speed_front_left = chassis_info_.WheelSpeedFront.WhlspdFL;
      }
      else if(1 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        chassis_info_deal.wheel_speed_front_left = -chassis_info_.WheelSpeedFront.WhlspdFL;
      }
      if(0 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        chassis_info_deal.wheel_speed_front_right = chassis_info_.WheelSpeedFront.WhlspdFR;
      }
      else if(1 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        chassis_info_deal.wheel_speed_front_right = -chassis_info_.WheelSpeedFront.WhlspdFR;
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        chassis_info_deal.wheel_speed_rear_left = chassis_info_.WheelSpeedRear.WhlspdRL;
      }
      else if(1 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        chassis_info_deal.wheel_speed_rear_left = -chassis_info_.WheelSpeedRear.WhlspdRL;
      }
      if(0 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        chassis_info_deal.wheel_speed_rear_right = chassis_info_.WheelSpeedRear.WhlspdRR;
      }
      else if(1 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        chassis_info_deal.wheel_speed_rear_right = chassis_info_.WheelSpeedRear.WhlspdRR;
      }
      chassis_info_deal.speed = (chassis_info_deal.wheel_speed_rear_right+chassis_info_deal.wheel_speed_rear_left)/2.0;

      last_eps_steer_angle = chassis_info_deal.eps_steer_angle;
      return Status::OK;
    }

void IOChassisInfoSend(ChassisInfo& chassis_info){
    static uint seq = 0;

    ChassisInfoDeal chassis_info_deal;
    if(Status::ERROR == DealChassisInfo(chassis_info_deal)){
    AINFO << __FUNCTION__ << ": deal chassis info failed.";
    }

    AionChassisInfo aion_chassis_info;
    WheelSpeedInfo wheelspeedinfo;
    auto chasisheader = aion_chassis_info.mutable_header();
    auto time = apollo::cyber::Time::Now().ToNanosecond()/1000000000.0;
    aion_chassis_info.set_measurement_time(time);
    chasisheader->set_timestamp(time);
    chasisheader->set_module_name("aion_io_node");
    chasisheader->set_sequence_num(seq);
    chasisheader->set_version(0);

    auto header = wheelspeedinfo.mutable_header();
    wheelspeedinfo.set_measurement_time(time);
    header->set_timestamp(time);
    header->set_module_name("aion_io_node");
    header->set_sequence_num(seq);
    header->set_version(0);
    seq++;

    aion_chassis_info.set_velocity(chassis_info_deal.velocity);

    aion_chassis_info.set_lon_acceleration(chassis_info_deal.lon_acceleration);
    aion_chassis_info.set_lat_acceleration(chassis_info_deal.lat_acceleration);
    aion_chassis_info.set_yaw_rate(chassis_info_deal.yaw_rate);

    aion_chassis_info.set_brake_system_status(chassis_info_deal.brake_system_status);
    aion_chassis_info.set_brake_padal_position(chassis_info_deal.brake_padal_position);

    aion_chassis_info.set_eps_steer_angle(chassis_info_deal.eps_steer_angle);
    aion_chassis_info.set_eps_steer_angle_speed(chassis_info_deal.eps_steer_angle_speed);
    aion_chassis_info.set_eps_fault_status(chassis_info_deal.eps_fault_status);
    aion_chassis_info.set_eps_lat_ctl_mode(chassis_info_deal.eps_lat_ctl_mode);

    aion_chassis_info.set_vcu_current_gear_level(chassis_info_deal.vcu_current_gear_level);
    aion_chassis_info.set_vcu_actual_vehicle_wheel_torque(chassis_info_deal.vcu_actual_vehicle_wheel_torque);

    aion_chassis_info.set_bcm_turn_lamp_status(chassis_info_deal.bcm_turn_lamp_status);

    aion_chassis_info.set_wheel_speed_front_left(chassis_info_deal.wheel_speed_front_left);
    aion_chassis_info.set_wheel_speed_front_right(chassis_info_deal.wheel_speed_front_right);
    aion_chassis_info.set_wheel_speed_rear_left(chassis_info_deal.wheel_speed_rear_left);
    aion_chassis_info.set_wheel_speed_rear_right(chassis_info_deal.wheel_speed_rear_right);
    aion_chassis_info.set_speed(chassis_info_deal.speed);
//   aion_chassis_info.set_takeover_status(takeover_status_);
    if(0 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        wheelspeedinfo.set_wheelspeed_fl(chassis_info_.WheelSpeedFront.WhlspdFL/3.6);
        wheelspeedinfo.set_wheelsign_fl(1);
    }else if(1 == chassis_info_.WheelSpeedFront.WhlspdFLdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFLsts){
        wheelspeedinfo.set_wheelspeed_fl(chassis_info_.WheelSpeedFront.WhlspdFL/3.6);
        wheelspeedinfo.set_wheelsign_fl(-1);
    }
    if(0 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        wheelspeedinfo.set_wheelspeed_fr(chassis_info_.WheelSpeedFront.WhlspdFR/3.6);
        wheelspeedinfo.set_wheelsign_fr(1);
    }else if(1 == chassis_info_.WheelSpeedFront.WhlspdFRdir && 1 == chassis_info_.WheelSpeedFront.WhlspdFRsts){
        wheelspeedinfo.set_wheelspeed_fr(chassis_info_.WheelSpeedFront.WhlspdFR/3.6);
        wheelspeedinfo.set_wheelsign_fr(-1);
    }
    if(0 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        wheelspeedinfo.set_wheelspeed_rl(chassis_info_.WheelSpeedRear.WhlspdRL/3.6);
        wheelspeedinfo.set_wheelsign_rl(1);
    }else if(1 == chassis_info_.WheelSpeedRear.WhlspdRLdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRLsts){
        wheelspeedinfo.set_wheelspeed_rl(chassis_info_.WheelSpeedRear.WhlspdRL/3.6);
        wheelspeedinfo.set_wheelsign_rl(-1);
    }
    if(0 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        wheelspeedinfo.set_wheelspeed_rr(chassis_info_.WheelSpeedRear.WhlspdRR/3.6);
        wheelspeedinfo.set_wheelsign_rr(1);
    }else if(1 == chassis_info_.WheelSpeedRear.WhlspdRRdir && 1 == chassis_info_.WheelSpeedRear.WhlspdRRsts){
        wheelspeedinfo.set_wheelspeed_rr(chassis_info_.WheelSpeedRear.WhlspdRR/3.6);
        wheelspeedinfo.set_wheelsign_rr(-1);
    }
    //pWriterChassisInfo->Write(aion_chassis_info);
    pWriterWheelSpeed->Write(wheelspeedinfo);


    //   std::string json_string;
    //   google::protobuf::util::MessageToJsonString(aion_chassis_info, &json_string);
    //   AINFO << __FUNCTION__ << json_string;
    }

void ChassisInfoSend(){
    while (!send_exit)
    {
        std::unique_lock<std::mutex> lck(mtx);
        cv.wait(lck);
        if(Status::ERROR == UpdateChassisInfo()){
        AINFO << __FUNCTION__ << ": update chassis info failed.";
      }
      //deal and send chassis info
      IOChassisInfoSend(chassis_info_);
    }
    
}


/**
 * @brief 使用方法
 *
 * @return int
 */
int main(int argc, char *argv[]) {
    CanHal& can_instance = CanHal::GetInstance();
    std::string can_config_path = "./can_hal_config.pb.txt";
    std::string dbc_path = "./Client_1210.dbc";
    std::shared_ptr<CanCbTest> can_cb = make_shared<CanCbTest>();
    uint64_t counter = 0;
    CanCbId_t can_cb_id = 0;


    // init dbc
    std::ifstream ifs(dbc_path);
    std::cout << "dbc_path: " << dbc_path << std::endl;
    if (!ifs.is_open()) {
      std::cout << "Unable to open dbc file" << std::endl;
    }
    ifs >> dbc_network;
    if (!dbc_network.successfullyParsed) {
      std::cout << "Unable to parse dbc file" << std::endl;
    }
  
    /* loop over messages */
    for (const auto & message : dbc_network.messages) {
      std::cout << "Message id: " << message.second.id << std::endl;
      std::cout << "Message name: " << message.second.name << std::endl;
      std::cout << "Message size: " << message.second.size << std::endl;
  
      /* loop over signals of this messages */
      for (const auto & signal : message.second.signals) {
        std::cout << "  Signal name: " << signal.second.name << std::endl;
        std::cout << "  Signal comment: " << signal.second.comment << std::endl;
      }
    }

    // 初始化通信框架
    ADSNode::Init(argv[0]);
    // 创建Node
    if (can_node == nullptr)
	{
		can_node = std::make_shared<ADSNode>("can_talker");
	}

    if(pWriterAionWheelSpeed == nullptr)
    {
        std::string aion_wheel_speed_writer_name = "/aion/WheelSpeed";
        pWriterAionWheelSpeed = can_node->CreateWriter<AionWheelSpeed>(aion_wheel_speed_writer_name);
        std::cout << "channel : " << aion_wheel_speed_writer_name << std::endl;
    }

    if(pWriterWheelSpeed == nullptr)
    {
        std::string wheel_speed_writer_name = "channel/wheelspeed";
        pWriterWheelSpeed = can_node->CreateWriter<WheelSpeedInfo>(wheel_speed_writer_name);
        std::cout << "channel : " << wheel_speed_writer_name << std::endl;
    }

    if(pWriterChassisInfo == nullptr)
    {	
        std::string chassis_info_writer_name = "/aion/ChassisInfo";
        pWriterChassisInfo = can_node->CreateWriter<AionChassisInfo>(chassis_info_writer_name);
        std::cout << "channel : " << chassis_info_writer_name << std::endl;
    }

    /* 初始化CAN总线 */
    if (can_instance.CanChannelInit(can_config_path)) {
        std::cout << "[CAN_HAL_TEST] can init success!" << std::endl;
    }

    can_cb_id =
        can_instance.CanRegisterCallback(CAN_DEV_1, can_cb);
    std::cout << "[CAN_HAL_TEST] wating for can frame..." << std::endl;

    std::thread writeChassis(ChassisInfoSend);

    // uint8_t can_data_buf[8] = {0, 1, 2, 3, 4, 5, 6, 7};
    // while (1) {
    //     counter++;
    //     sleep(1);


    //     can_instance.Write(CAN_DEV_1, 0x100, 8, can_data_buf);
    // }
    std::cout << "Init done. Enjoy." << std::endl;
    apollo::cyber::WaitForShutdown();
    send_exit  = true;
    if (writeChassis.joinable())
    {
        writeChassis.join();
    }
    
    return 0;
}

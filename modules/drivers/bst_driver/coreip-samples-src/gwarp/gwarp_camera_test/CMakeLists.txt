cmake_minimum_required(VERSION 2.8)

add_executable(gwarp_camera_test ./gwarp_camera.cpp)
add_executable(scaler_camera_test ./scaler_camera.cpp)

target_link_libraries(gwarp_camera_test bstgwarp bstisp)
target_link_libraries(scaler_camera_test bstgwarp bstisp)

install(TARGETS gwarp_camera_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/gwarp)

install(TARGETS scaler_camera_test
RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/gwarp)


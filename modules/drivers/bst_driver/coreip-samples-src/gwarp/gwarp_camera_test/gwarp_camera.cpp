/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include "bst_gwarp_sdk_api.h"
#include "bst_isp_sdk_api.h"

#define BUFFER_NUM 4
#define GWARP_VIDEO_BASE_NODE 30

using namespace bvideo;

int ter = 0;
static void sigint_handler(int arg) { ter = 1; }

struct optparam {
    int input_width;
    int input_height;
    int output_width;
    int output_height;
    char const *dst_yuv;
    int debug_print;
    int gwarp_id;
    int camera_id;
};

void optparam_init(struct optparam *arg)
{
    memset(arg, 0, sizeof(struct optparam));

    arg->input_width = 1280;
    arg->input_height = 720;
    arg->output_width = 1280;
    arg->output_height = 720;
    arg->dst_yuv = "./gdc_camera.yuv";
    arg->gwarp_id = 0;
    arg->camera_id = 8;
}

int optparam_option(int argc, char *argv[], struct optparam *arg)
{
    int ch;
    int ret = 0;
    struct optparam *param = arg;

    optparam_init(param);

    if (argc < 2) {
        printf("camera_id: /dev/video8\n");
        printf("gwarp_id : /dev/video30\n");
        printf("dst_yuv_file = ./gdc_camera.yuv\n");
        printf("input width = 1280\n");
        printf("input height = 720\n");
        printf("output width = 1280\n");
        printf("output height = 720\n");
    } else {
        while ((ch = getopt(argc, argv, "d:g:f:h:w:v:L")) != -1) {
            switch (ch) {
                case 'd':
                    param->camera_id = atoi(optarg);
                    printf("camera_id = /dev/video%d\n", param->camera_id);
                    break;
                case 'g':
                    param->gwarp_id = atoi(optarg);
                    printf("gwarp_id = /dev/video%d\n",
                           param->gwarp_id + GWARP_VIDEO_BASE_NODE);
                    break;
                case 'h':
                    param->output_height = atoi(optarg);
                    printf("gwarp output height = %d\n", param->output_height);
                    break;
                case 'w':
                    param->output_width = atoi(optarg);
                    printf("gwarp output width = %d\n", param->output_width);
                    break;
                case 'L':
                    param->debug_print = 1;
                    break;
                case 'v':
                    param->dst_yuv = optarg;
                    printf("dst_yuv_file = %s\n", param->dst_yuv);
                    break;
                case '?':
                    printf("Unknow option character\n");
                    ret = -1;
                    break;
                case ':':
                    printf("option: %c missing argument\n", ch);
                    ret = -1;
                    break;
                default:
                    printf("input failed\n");
                    ret = -1;
                    break;
            }
        }
    }

    return ret;
}

int main(int argc, char *argv[])
{
    int ret;
    int i;

    struct optparam param;
    image_rect_t input_rect;
    image_rect_t output_rect;

    const char *version_str;

    signal(SIGINT, sigint_handler);

    version_str = gwarp_get_sdk_version();
    printf("gwarp sdk version = %s\n", version_str);

    // argument init
    ret = optparam_option(argc, argv, &param);
    if (ret < 0) {
        printf("optparm init failed\n");
        return ret;
    }

    // open dst file
    int dst_fd;
    dst_fd = open(param.dst_yuv, O_RDWR | O_CREAT);
    if (dst_fd < 0) {
        printf("open dst_file : %s failed\n", param.dst_yuv);
        return -1;
    }

    // camera_init
    handle_t cam_handle;
    int dmafd[BUFFER_NUM];

    cam_handle = isp_open_video(param.camera_id, ISP_MODE_VIEWS);
    if (!cam_handle) {
        printf("isp_open_video failed\n");
        return -1;
    }

    view_format_t view_format;
    int view_type;
    int view_index;
    for (int i = 0; i < MAX_VIEW_NUM; i++) {
        if (isp_get_view_format(cam_handle, i, &view_format)) {
            printf("Get view format failed!\n");
            return RET_ERR_COMMON;
        }
        if (param.input_height == view_format.height &&
            param.input_width == view_format.width) {
            switch (i) {
                case 0:
                    view_type = ISP_VIEW0;
                    view_index = 0;
                    break;
                case 1:
                    view_type = ISP_VIEW1;
                    view_index = 1;
                    break;
                default:
                    break;
            }
        }
    }
    printf("view_type using view %d, view_index = %d\n", view_type, view_index);
    ret = isp_init_video(cam_handle, (view_type_t)view_type, BUFFER_NUM);
    if (ret) {
        printf("isp_init_video failed\n");
        return ret;
    }

    // get camera video buf dmafd
    for (i = 0; i < BUFFER_NUM; i++) {
        dmafd[i] = isp_get_view_buf_fd(cam_handle, i, view_index);
        printf("dma[%d] = %d\n", i, dmafd[i]);
    }

    // open gwarp device
    handle_t gwarp_handle;
    gwarp_handle = gwarp_open_video(param.gwarp_id, 0);

    // gwarp_init_buf for input
    memset(&input_rect, 0, sizeof(input_rect));
    input_rect.width = param.input_width;
    input_rect.height = param.input_height;
    input_rect.wstride = param.input_width;
    input_rect.hstride = param.input_height;
    input_rect.format = BST_PIXEL_FORMAT_NV12;

    buf_req_t req_in;
    video_buf_t input_buf[BUFFER_NUM];
    req_in.type = MEM_TYPE_DMAFD;
    req_in.count = BUFFER_NUM;
    for (i = 0; i < BUFFER_NUM; i++) {
        input_buf[i].buf_index = i;
        input_buf[i].type = MEM_TYPE_DMAFD;
        input_buf[i].fd = dmafd[i];
    }

    gwarp_init_buf(gwarp_handle, &req_in, &input_rect, VIDEO_INPUT, input_buf);

    // gwarp_init_buf for output
    memset(&output_rect, 0, sizeof(output_rect));
    output_rect.wstride = param.output_width;
    output_rect.hstride = param.output_height;
    output_rect.width = param.output_width;
    output_rect.height = param.output_height;
    output_rect.format = BST_PIXEL_FORMAT_NV12;

    buf_req_t req_out;
    video_buf_t output_buf[BUFFER_NUM];
    req_out.type = MEM_TYPE_MMAP;
    req_out.count = BUFFER_NUM;

    gwarp_init_buf(gwarp_handle, &req_out, &output_rect, VIDEO_OUTPUT,
                   output_buf);
    for (i = 0; i < BUFFER_NUM; i++) {
        printf("i = %d, start = 0x%x, length = 0x%x, fd = %d\n", i,
               output_buf[i].start, output_buf[i].length, output_buf[i].fd);
    }

    FILE *init_fp;
    int init_config_size;

    // init file config
    init_fp = fopen("./gwarp_init0.bin", "rb");
    if (init_fp == NULL) {
        printf("fopen init file failed\n");
        return -1;
    }
    if (fseek(init_fp, 0, SEEK_END) != 0) {
        printf("fseek error\n");
        return -1;
    }
    init_config_size = ftell(init_fp);
    if (init_config_size < 0) {
        printf("error get file size\n");
        return -1;
    }

    if (fseek(init_fp, 0, SEEK_SET) != 0) {
        printf("fseek error\n");
        return -1;
    }

    void *init_config_addr;
    init_config_addr = malloc(init_config_size);
    if (init_config_addr == NULL) {
        printf("malloc failed\n");
        return -1;
    }

    ret = fread(init_config_addr, init_config_size, 1, init_fp);
    if (ret == 0) {
        printf("fread error\n");
        return -1;
    }

    fclose(init_fp);

    // table file config

    FILE *table_fp;
    int table_config_size;
    table_fp = fopen("./gwarp_table0.bin", "rb");
    if (table_fp == NULL) {
        printf("fopen table file failed\n");
        return -1;
    }
    if (fseek(table_fp, 0, SEEK_END) != 0) {
        printf("fseek error\n");
        return -1;
    }
    table_config_size = ftell(table_fp);
    if (table_config_size < 0) {
        printf("error get file size\n");
        return -1;
    }

    if (fseek(table_fp, 0, SEEK_SET) != 0) {
        printf("fseek error\n");
        return -1;
    }

    void *table_config_addr;
    table_config_addr = malloc(table_config_size);
    if (table_config_addr == NULL) {
        printf("malloc failed\n");
        return -1;
    }

    ret = fread(table_config_addr, table_config_size, 1, table_fp);
    if (ret == 0) {
        printf("fread error\n");
        return -1;
    }
    fclose(table_fp);

    // gwarp config gdc
    gwarp_config_gdc(gwarp_handle, init_config_addr, init_config_size,
                     table_config_addr, table_config_size);

    // start camera stream
    ret = isp_start_capture(cam_handle);
    if (ret) {
        printf("isp_start_capture failed\n");
        return ret;
    }

    // loop process
    isp_buf_t cam_buf;
    video_buf_t out_buf;

    while (1) {
        // fill data to input port from camera
        while (1) {
            ret = isp_get_frame_buf(cam_handle, &cam_buf, -1);
            if (ret == 0) {
                break;
            } else if (ret == RET_TRYAGAIN || ret == RET_TIMEOUT) {
                continue;
            } else {
                printf("cam get frame failed\n");
                return ret;
            }
        }

        if (param.debug_print)
            printf("cam_buf.index = %d\n", cam_buf.buf_index);

        // input_buf[cam_buf.buf_index].buf_index = cam_buf.buf_index;
        // input_buf[cam_buf.buf_index].type = MEM_TYPE_DMAFD;
        // input_buf[cam_buf.buf_index].fd = dmafd[cam_buf.buf_index][0];
        gwarp_gdc(gwarp_handle, &input_buf[cam_buf.buf_index], &out_buf);

        // data process
        if (ter == 1) {
            int origin_fd;

            origin_fd = open("./before_gdc.yuv", O_RDWR | O_CREAT);
            if (origin_fd < 0) {
                printf("open before_gdc.yuv failed\n");
            } else {
                write(origin_fd, cam_buf.views[0].start,
                      cam_buf.views[0].length);
                close(origin_fd);
            }

            printf("/********************************/\n");
            write(dst_fd, out_buf.start, out_buf.length);
            break;
        }

        // put output buf
        gwarp_put_buf(gwarp_handle, &out_buf, VIDEO_OUTPUT);
        // put camera buf
        ret = isp_put_frame_buf(cam_handle, &cam_buf);
        if (ret) {
            printf("isp_put_frame_buf failed\n");
            return ret;
        }
    }
    close(dst_fd);
    // camera streamoff
    ret = isp_stop_capture(cam_handle);
    if (ret) {
        printf("isp_stop_capture failed\n");
        return ret;
    }

    // camera close
    isp_close_video(cam_handle);

    // close gwarp device
    ret = gwarp_close_video(gwarp_handle);
    if (ret < 0) {
        printf("gwarp video close failed\n");
        return ret;
    }

    return 0;
}

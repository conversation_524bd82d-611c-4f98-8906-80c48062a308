cmake_minimum_required(VERSION 2.8)

add_executable(gwarp_file_test ./gdc_file.cpp)
add_executable(scaler_file_test ./scaler_file.cpp)
add_executable(crop_file_test ./crop_file.cpp)

target_link_libraries(gwarp_file_test bstgwarp)
target_link_libraries(scaler_file_test bstgwarp)
target_link_libraries(crop_file_test bstgwarp)

install(TARGETS gwarp_file_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/gwarp)

install(TARGETS scaler_file_test
RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/gwarp)

install(TARGETS crop_file_test
RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/gwarp)

install(FILES gwarp_test.yuv
        DESTINATION ${COREIP_SAMPLE_DIR}/../install/gwarp)


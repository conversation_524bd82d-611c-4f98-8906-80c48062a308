/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>

#include "bst_gwarp_sdk_api.h"

#define BUFFER_NUM 1
#define GWARP_VIDEO_BASE_NODE 30
using namespace bvideo;

struct optparam {
    int input_width;
    int input_height;
    int output_width;
    int output_height;
    char const *src_yuv;
    char const *dst_yuv;
    int dstfile_flags;
    int debug_print;
    int gwarp_id;
    bool pitch_enable;
};

void optparam_init(struct optparam *arg)
{
    memset(arg, 0, sizeof(struct optparam));

    arg->input_width = 1280;
    arg->input_height = 720;
    arg->output_width = 1280;
    arg->output_height = 720;
    arg->src_yuv = "./gwarp_test.yuv";
    arg->dst_yuv = "./gdc_file.yuv";
    arg->gwarp_id = 0;
    arg->debug_print = 0;
    arg->pitch_enable = false;
}

int optparam_option(int argc, char *argv[], struct optparam *arg)
{
    int ch;
    int ret = 0;
    struct optparam *param = arg;

    optparam_init(param);

    if (argc < 2) {
        printf("gwarp_id : /dev/video30\n");
        printf("src_yuv_file = ./gwarp_test.yuv\n");
        printf("dst_yuv_file = ./gdc_file.yuv\n");
        printf("input width = 1280\n");
        printf("input height = 720\n");
        printf("output width = 1280\n");
        printf("output height = 720\n");
    } else {
        while ((ch = getopt(argc, argv, "g:f::h:w:v:pL")) != -1) {
            switch (ch) {
                case 'g':
                    param->gwarp_id = atoi(optarg);
                    printf("gwarp_id = /dev/video%d\n",
                           param->gwarp_id + GWARP_VIDEO_BASE_NODE);
                    break;
                case 'f':
                    if (optarg) {
                        param->src_yuv = optarg;
                    } else {
                        printf("src_yuv_file = %s\n", param->src_yuv);
                    }
                    break;
                case 'h':
                    param->output_height = atoi(optarg);
                    printf("gwarp output height = %d\n", param->output_height);
                    break;
                case 'w':
                    param->output_width = atoi(optarg);
                    printf("gwarp output width = %d\n", param->output_width);
                    break;
                case 'L':
                    param->debug_print = 1;
                    break;
                case 'v':
                    param->dst_yuv = optarg;
                    printf("dst_yuv_file = %s\n", param->dst_yuv);
                    break;
                case '?':
                    printf("Unknow option character\n");
                    ret = -1;
                    break;
                case 'p':
                    param->pitch_enable = true;
                    break;
                default:
                    printf("input failed\n");
                    ret = -1;
                    break;
            }
        }
    }

    return ret;
}

int main(int argc, char *argv[])
{
    int ret;
    int i;
    struct optparam param;
    image_rect_t input_rect;
    image_rect_t output_rect;

    const char *version_str;

    version_str = gwarp_get_sdk_version();
    printf("gwarp sdk version = %s\n", version_str);

    // argument init
    ret = optparam_option(argc, argv, &param);
    if (ret < 0) {
        printf("optparam init failed\n");
        return ret;
    }

    // open src and dst file
    int src_fd;
    int dst_fd;

    src_fd = open(param.src_yuv, O_RDONLY);
    if (src_fd < 0) {
        printf("open src_yuv file %s failed\n", param.src_yuv);
        return -1;
    }

    dst_fd = open(param.dst_yuv, O_RDWR | O_CREAT);
    if (dst_fd < 0) {
        printf("open dst_file : %s failed\n", param.dst_yuv);
        return -1;
    }

    // open gwarp device

    handle_t gwarp_handle;

    gwarp_handle = gwarp_open_video(param.gwarp_id, 0);

    // gwarp_init_buf for input
    memset(&input_rect, 0, sizeof(input_rect));
    input_rect.width = param.input_width;
    input_rect.height = param.input_height;
    input_rect.wstride = param.input_width;
    input_rect.hstride = param.input_height;
    input_rect.format = BST_PIXEL_FORMAT_NV12;

    buf_req_t req_in;
    video_buf_t input_buf[BUFFER_NUM];
    req_in.type = MEM_TYPE_MMAP;
    req_in.count = BUFFER_NUM;

    gwarp_init_buf(gwarp_handle, &req_in, &input_rect, VIDEO_INPUT, input_buf);

    for (i = 0; i < BUFFER_NUM; i++) {
        printf("i = %d, start = 0x%x, length = 0x%x\n", i, input_buf[i].start,
               input_buf[i].length);
    }

    // gwarp_init_buf for output
    memset(&output_rect, 0, sizeof(output_rect));
    output_rect.wstride = param.output_width;
    output_rect.hstride = param.output_height;
    output_rect.width = param.output_width;
    output_rect.height = param.output_height;
    output_rect.format = BST_PIXEL_FORMAT_NV12;

    buf_req_t req_out;
    video_buf_t output_buf[BUFFER_NUM];
    req_out.type = MEM_TYPE_MMAP;
    req_out.count = BUFFER_NUM;

    gwarp_init_buf(gwarp_handle, &req_out, &output_rect, VIDEO_OUTPUT,
                   output_buf);
    for (i = 0; i < BUFFER_NUM; i++) {
        printf("i = %d, start = 0x%x, length = 0x%x, fd = %d\n", i,
               output_buf[i].start, output_buf[i].length, output_buf[i].fd);
    }

    FILE *init_fp;
    int init_config_size;

    // init file config
    init_fp = fopen("./gwarp_init0.bin", "rb");
    if (init_fp == NULL) {
        printf("fopen init file failed\n");
        return -1;
    }
    if (fseek(init_fp, 0, SEEK_END) != 0) {
        printf("fseek error\n");
        return -1;
    }
    init_config_size = ftell(init_fp);
    if (init_config_size < 0) {
        printf("error get file size\n");
        return -1;
    }

    if (fseek(init_fp, 0, SEEK_SET) != 0) {
        printf("fseek error\n");
        return -1;
    }

    void *init_config_addr;
    init_config_addr = malloc(init_config_size);
    if (init_config_addr == NULL) {
        printf("malloc failed\n");
        return -1;
    }

    ret = fread(init_config_addr, init_config_size, 1, init_fp);
    if (ret == 0) {
        printf("fread error\n");
        return -1;
    }

    fclose(init_fp);

    // table file config

    FILE *table_fp;
    int table_config_size;
    table_fp = fopen("./gwarp_table0.bin", "rb");
    if (table_fp == NULL) {
        printf("fopen table file failed\n");
        return -1;
    }
    if (fseek(table_fp, 0, SEEK_END) != 0) {
        printf("fseek error\n");
        return -1;
    }
    table_config_size = ftell(table_fp);
    if (table_config_size < 0) {
        printf("error get file size\n");
        return -1;
    }

    if (fseek(table_fp, 0, SEEK_SET) != 0) {
        printf("fseek error\n");
        return -1;
    }

    void *table_config_addr;
    table_config_addr = malloc(table_config_size);
    if (table_config_addr == NULL) {
        printf("malloc failed\n");
        return -1;
    }

    ret = fread(table_config_addr, table_config_size, 1, table_fp);
    if (ret == 0) {
        printf("fread error\n");
        return -1;
    }
    fclose(table_fp);

    // gwarp config gdc
    gwarp_config_gdc(gwarp_handle, init_config_addr, init_config_size,
                     table_config_addr, table_config_size);

    // fill data to input port from file
    for (i = 0; i < BUFFER_NUM; i++) {
        read(src_fd, input_buf[i].start,
             param.input_width * param.input_height * 3 / 2);
    }

    if (param.pitch_enable) {
        gwarp_set_buf_pitch(gwarp_handle, GWARP_OUTPUT_BUF_PITCH);
    }

    // loop progress
    video_buf_t out_buf;

    for (i = 0; i < BUFFER_NUM; i++) {
        gwarp_gdc(gwarp_handle, &input_buf[i], &out_buf);
        if (param.debug_print) {
            printf("buf_index = %d\n", out_buf.buf_index);
        }
        // data process
        write(dst_fd, out_buf.start, out_buf.length);
        gwarp_put_buf(gwarp_handle, &out_buf, VIDEO_OUTPUT);
    }

    // close gwarp device
    ret = gwarp_close_video(gwarp_handle);
    if (ret < 0) {
        printf("gwarp video close failed\n");
        return ret;
    }

    close(src_fd);
    close(dst_fd);

    return 0;
}


/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#ifndef BST_MSG_QUEUE_H
#define BST_MSG_QUEUE_H
#include <string>
#include "Msg.hpp"
#include "Queue.hpp"

template <typename dataType>
class bstMsgQueue {
   public:
    bstMsgQueue(std::string sQueueName = "bst_queue",
                unsigned int maxObject = 1)
        : m_message(0), m_queueName(sQueueName), m_maxObject(maxObject)
    {
        m_queue.setQueueSize(maxObject);
    }

    ~bstMsgQueue() {}

    void setParam(std::string queueName, unsigned int maxObject)
    {
        m_queueName = queueName;
        m_maxObject = maxObject;
        m_queue.setQueueSize(maxObject);
    }

    void put(dataType& Data)
    {
        m_queue.put(PolyM::DataMsg<dataType>(m_message++, Data));
    }

    void get(dataType& data, int timeoutMillis = 0)
    {
        auto Message = m_queue.get();
        auto& dMessage = dynamic_cast<PolyM::DataMsg<dataType>&>(*Message);
        data = std::move(dMessage.getPayload());
    }

    int size() { return m_queue.size(); }

    bool empty() { return m_queue.empty(); }

   private:
    PolyM::Queue m_queue;
    long long m_message;
    std::string m_queueName;
    int m_maxObject;
};

#endif

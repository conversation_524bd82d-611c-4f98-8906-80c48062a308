
/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include "image_utils.h"

#define min(a, b) ((a < b) ? a : b)
int nv12img_relocate(uint8_t *src_buf, int src_w, int src_h, uint8_t *des_buf,
                     int des_w, int des_h, int des_x, int des_y)
{
    // XXX only support even number now
    des_x = des_x / 2 * 2;
    des_y = des_y / 2 * 2;

    // copy y
    for (int i = 0; i < min(src_h, (des_h - des_y)); i++) {
        if ((des_y + i) >= 0) {
            for (int j = 0; j < min(src_w, (des_w - des_x)); j++) {
                if ((des_x + j) >= 0) {
                    *(des_buf + (des_y + i) * des_w + des_x + j) =
                        *(src_buf + i * src_w + j);
                }
            }
        }
    }

    // copy uv
    for (int i = 0; i < min(src_h, (des_h - des_y)) / 2; i++) {
        if ((des_y / 2 + i) >= 0) {
            for (int j = 0; j < min(src_w, (des_w - des_x)); j++) {
                if ((des_x + j) >= 0) {
                    *(des_buf + des_h * des_w + (des_y / 2 + i) * des_w +
                      des_x + j) = *(src_buf + src_h * src_w + i * src_w + j);
                }
            }
        }
    }

    return 0;
}

int argbimg_relocate(uint8_t *src_buf, int src_w, int src_h, uint8_t *des_buf,
                     int des_w, int des_h, int des_x, int des_y)
{
    // copy argb
    for (int i = 0; i < min(src_h, (des_h - des_y)); i++) {
        if ((des_y + i) >= 0) {
            for (int j = 0; j < min(src_w, (des_w - des_x)) * 4; j++) {
                if ((des_x * 4 + j) >= 0) {
                    *(des_buf + (des_y + i) * des_w * 4 + des_x * 4 + j) =
                        *(src_buf + i * src_w * 4 + j);
                }
            }
        }
    }

    return 0;
}

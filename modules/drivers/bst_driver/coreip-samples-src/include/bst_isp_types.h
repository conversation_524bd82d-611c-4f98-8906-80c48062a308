/*******************************************************************************
 *  This file contains proprietary information that is the sole intellectual
 *  property of Black Sesame Technologies, Inc. and its affiliates.
 *  No portions of this material may be reproduced in any form without the
 *  written permission of:
 *
 *  Black Sesame Technologies, Inc. and its affiliates
 *  Rm 1102-1108, Building 36, No.2777, East Jinxiu Rd,
 *  Pudong District, Shanghai, China
 *
 *  Copyright \@2020: all right RETerved.
 *  For distribution or production, further Software License Agreement is
 *  required.
 *
 ******************************************************************************/

#ifndef _BST_ISP_TYPES_H_
#define _BST_ISP_TYPES_H_

#include <stddef.h>
#include <stdint.h>
#include <vector>

#include "bst_video_common.h"

namespace bvideo {

#define MAX_VIEW_NUM 3
#define MAX_ISP_VIDEO_NUM 16
#define MAX_CAMERA_PER_GROUP 8
#define MAX_CAMERA_NAME_LEN 32
#define MAX_ISP_CHANNEL 12
#define MAX_FRAME_CYCLE 120

#define YDNS_MAX 10
#define YDNS_MIN 0
#define UVDNS_MAX 10
#define UVDNS_MIN 0
#define SHARPNESS_MAX 10
#define SHARPNESS_MIN 0
#define BRIGHTNESS_MAX 255
#define BRIGHTNESS_MIN 0
#define CONTRAST_MAX 255
#define CONTRAST_MIN 0
#define SATURATION_MAX 255
#define SATURATION_MIN 0
#define HUE_MAX 255
#define HUE_MIN 0

#define YUV_STAT_OFFSET 320

#define MAX_EMBEDDED_ZONE_NUM 3
#define MAX_2A_FRAME_NUM 5

#define SAFETY_ERR_REG_GROUP (32)

#define MAX_I2C_READ_GROUP (32)
#define ISP_MAX_I2C_GROUP_NUM (8)
enum {
    ISP_MODE_VIEWS = 1,
    ISP_MODE_RAW = 2,
};

enum {
    VIDEO_NO_READABLE_BUF = 0,
    VIDEO_HAS_READABLE_BUF = 1,
};

enum {
    INTERNAL_TRIGGER_MODE = 0,
    EXTERNAL_TRIGGER_MODE = 1,
};

enum {
    ISP_BUFFER_TYPE_NULL = 0,
    ISP_BUFFER_TYPE_MMAP = 1,
    ISP_BUFFER_TYPE_DMABUF = 2,
    ISP_BUFFER_TYPE_USERPTR = 3,
};

enum { GROUP_FLAG_UNIFIED_CAMERA = 0, GROUP_FLAG_DIFFERENT_CAMERA };
/*apply gamma 1.8~2.6*/
typedef enum {
    ISP_GAMMA_1_8 = 0,
    ISP_GAMMA_1_9 = 1,
    ISP_GAMMA_2_0 = 2,
    ISP_GAMMA_2_1 = 3,
    ISP_GAMMA_2_2 = 4,
    ISP_GAMMA_2_3 = 5,
    ISP_GAMMA_2_4 = 6,
    ISP_GAMMA_2_5 = 7,
    ISP_GAMMA_2_6 = 8,
} gamma_value_t;

typedef enum {
    ISP_VIEW_INVALID = 0,
    ISP_VIEW0 = 1,
    ISP_VIEW1 = 2,
    ISP_VIEW0_VIEW1 = 3,
    ISP_VIEWY = 4,
    ISP_VIEW0_VIEWY = 5,
    ISP_VIEW1_VIEWY = 6,
    ISP_VIEW0_VIEW1_VIEWY = 7,
} view_type_t;

typedef struct _embedded_info_t {
    uint32_t emd_zone_num;
    struct {
        void *start;
        uint32_t size;
    } emd_zone[MAX_EMBEDDED_ZONE_NUM];
} embedded_info_t;

typedef struct tagTune2AInfo {
    uint32_t tune_pGainBuf_debug[3];
    uint32_t AECAGC_MEANY_L;
    unsigned short tune_pExpBuf[3];
    unsigned short tune_pGainBuf[3];
    unsigned short tune_pAWBGainBuf[3][3];
    unsigned short tune_pCamGainBuf[3];
    uint32_t FrameNum;
    unsigned short tune_pDigiGainBuf[1];
    unsigned short rsv;
} Tune2AInfo_t;

typedef struct _isp_buf_t {
    video_buf_t views[MAX_VIEW_NUM];
    int buf_index;  // same to view buf's index
    int y_statistic;
    embedded_info_t embedded_info;
    Tune2AInfo_t
        *tune2AInfoStart[MAX_2A_FRAME_NUM];  // 5 2A data start address, 2A data
                                             // struct Tune2AInfo_t
} isp_buf_t;

typedef struct _dma_buf_t{
    uint64_t dma_buffer_fd;
    uint64_t user_addr;
    uint64_t rsv[2];
} dma_buf_t;

typedef struct _view_format_t {
    int color_fmt;
    int width;
    int height;
} view_format_t;

typedef struct _camera_info_t {
    char camera_name[MAX_CAMERA_NAME_LEN];
    int camera_id;
    int is_streaming;
    uint16_t camera_data_type;
    uint16_t camera_fps;
    uint16_t camera_raw_width;
    uint16_t camera_raw_height;
} camera_info_t;

struct trigger_info {
    int trigger_mode;
    int mipi_id;
    int internal_trigger_fps;
    int camera_trigger_gpio_port;
    int deser_trigger_gpio_port;
};

typedef struct isp_i2c_info {
    uint16_t device_addr;
    uint8_t addr_width;
    uint8_t data_width;
    uint8_t reg_num;
    uint16_t reg_addr[MAX_I2C_READ_GROUP];
    uint16_t reg_data[MAX_I2C_READ_GROUP];
    uint32_t error_bits;
} isp_i2c_info_t;

typedef struct isp_misc_device {
    camera_info_t cam_info[MAX_ISP_CHANNEL];
    unsigned int camera_num;
} isp_misc_device_t;

typedef struct isp_ctrl {
    uint32_t value;  // isp_set_iqinfo_t  iqVal  ,if ctrl_addr == 0  ; use value
    uint16_t aecManualExp[3];
    uint16_t aecManualGain[3];
    uint16_t manualAWBGain[3][3];  // set mannual white balance value
} isp_ctrl_t;

typedef struct mec_param {
    int longexp;
    int midexp;
    int shortexp;
    int longgain;
    int midgain;
    int shortgain;
} mec_param_t;

typedef enum abnormal_item {
    ABN_INVALID,
    ABN_QUEUE_IRQ,  // DDR NOC queue abnormal interrupt for low queue(offline
                    // mode) or high queue(online mode)
    ABN_RAW2DDR_WR_OVERFLOW,           // RAW to DDR write overflow
    ABN_ISP2DDR_PDNS2DDR_WR_OVERFLOW,  // ISP/PDNS to DDR write overflow
    ABN_HW_QUEUE_IRQ,  // DDR NOC queue abnormal interrupt for low queue(offline
                       // mode) or high queue(online mode)
    ABN_HW_RAW2DDR_WR_OVERFLOW,           // RAW to DDR write overflow
    ABN_HW_ISP2DDR_PDNS2DDR_WR_OVERFLOW,  // ISP/PDNS to DDR write overflow

    ABN_SW_RAWRDYLST_OVERFLOW = 0x80,
    ABN_SW_NO_SYNC_FRAME_DATA,
    ABN_SW_GRP_RAWRDYLST_OVERFLOW,
    ABN_SW_PDNS_P0RDYLST_OVERFLOW,
    ABN_SW_PDNS_P1RDYLST_OVERFLOW,
    ABN_SW_DVP_FRAMEBUF_OVERFLOW,
    ABN_SW_SRAM_ALLOC_OVERFLOW,
    ABN_SW_RAW_BADFRAME,
    ABN_SW_CAMERA_PLUG_OUT,
} abnormal_item_e;

typedef struct abnormal_info {
    uint8_t abnormal_id;
    uint8_t abnormal_type;
    uint32_t last_good_sequence;
    uint32_t total_bad_frames;
    uint32_t total_frames;
} __attribute__((packed)) abnormal_info_t;

typedef struct scale_size {
    uint16_t width;
    uint16_t height;
} scale_size_t;

typedef struct crop_size {
    uint16_t topCropBefore;
    uint16_t botCropBefore;
    uint16_t lefCropBefore;
    uint16_t rigCropBefore;
    uint16_t topCropAfter;
    uint16_t botCropAfter;
    uint16_t lefCropAfter;
    uint16_t rigCropAfter;
} crop_size_t;

typedef struct resolution_resize {
    crop_size_t crop_size;
    scale_size_t scale_size;
    uint8_t view_id;
} resolution_resize_t;

struct crop_rect {
    uint16_t left;
    uint16_t top;
    uint16_t width;
    uint16_t height;
};

typedef struct resize_info {
    struct crop_rect crop_before;
    struct crop_rect crop_after;
    struct scale_size scale;
    uint8_t view_id;
} resize_info_t;

enum i2c_bus_ctrl_t {
    I2C_BUS_ISP = 0,  // isp controls i2c bus
    I2C_BUS_ARM = 1,  // arm controls i2c bus
};

struct i2c_ctrl_info {
    int video_id;
    enum i2c_bus_ctrl_t bus_ctrler;
};

/* Feed function */
typedef struct feed_drv_buf {
    uint32_t video_id;
    isp_buf_t *isp_buf;
} feed_drv_buf_t;

/* Applications should not touch other fields not described */
typedef struct feed_fw_reqbuffers {
    uint32_t count;    /* Required for request */
    uint32_t video_id; /* Required for request */
    uint32_t size;     /* Required for request */
    uint32_t memory;
    uint32_t fmt;
    uint32_t width;
    uint32_t height;
    uint32_t rsv[5];
} feed_fw_reqbuffers_t;

/* Applications should not touch other fields not described */
typedef struct feed_fw_buf {
    uint32_t video_id;  /* Required for query */
    uint32_t index;     /* Required for query */
    uint32_t chn_id;    /* Filled by SDK, applications should not touch */
    uint32_t bytesused; /* Filled by SDK, don't fill buffer exceed this size */
    uint32_t length;
    uint32_t sequence;
    uint32_t memory;
    union {
        uint64_t offset;
        uint64_t paddr;
    };
    int32_t fd;      /* Filled by SDK, use this in other module */
    uint64_t handle; /* Filled by SDK, use this in other module */
    union {
        uint64_t value64;
        void *vaddr; /* Filled by SDK, use this in other module */
    };
    uint32_t rsv[4];
} feed_fw_buf_t;

/* Applications should not touch other fields not described */
typedef struct feed_fw_control {
    feed_fw_buf_t bufs[MAX_ISP_CHANNEL]; /* Required to feed */
    uint32_t rsv[4];
} feed_fw_control_t;

// diag
enum diag_module {
    DIAG_MODULE_INVALID,
    DIAG_MODULE_DESER,
    DIAG_MODULE_SENSOR,
};

enum deser_diag_item { DESER_DIAG_ALL, DESER_DIAG_ERR_PIN, DESER_DIAG_ERR_CFG };

enum sensor_diag_item {
    SENSOR_DIAG_ALL,
    SENSOR_DIAG_ERR_PIN,
    SENSOR_DIAG_ERR_CFG,
    SENSOR_DIAG_PMIC_PG,
};

typedef struct err_cfg {
    int16_t reg;
    int16_t val;
} err_cfg_t;

typedef struct diag_info {
    uint32_t diag_module;
    uint32_t diag_item;
    uint32_t module_id;
    int32_t err_pin;
    int32_t pmic_pg;
    err_cfg_t err_cfgs[SAFETY_ERR_REG_GROUP];
} diag_info_t;
}  // namespace bvideo

typedef enum _dataType_e {
    DT_UYVY = 0x1e,
    DT_YUYV = 0x5e,
    DT_RAW10 = 0x2b,
    DT_RAW12 = 0x2c,
    DT_RAW14 = 0x2d,
    DT_RAW16 = 0x2e,
} dataType_e;

#endif  //_BST_VIDEO_TYPES_H_

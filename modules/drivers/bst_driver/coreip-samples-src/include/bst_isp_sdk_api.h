#ifndef __BST_ISP_SDK_API_H__
#define __BST_ISP_SDK_API_H__

/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include "bst_isp_types.h"

#define EXPORT_API __attribute__((visibility("default")))

namespace bvideo {

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief
 * @retval
 *
 **/
EXPORT_API const char *isp_get_sdk_version(void);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API handle_t isp_open_video(int video_id, int mode);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API handle_t isp_open_device();

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_close_video(handle_t handle);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_close_device(handle_t handle);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_init_video(handle_t handle, view_type_t view_type,
                              int buf_count);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_init_video_import_buf(handle_t handle, view_type_t view_type,
                                         int buf_count, int dma_buf_fd[]);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_init_video_import_dma_buf(handle_t handle, view_type_t view_type,
                                         int buf_count, dma_buf_t *import_dma_buf);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_enable_embedded_data(handle_t handle, view_type_t view_type);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_get_camera_info(handle_t handle,
                                   camera_info_t *pcamera_info);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_start_capture(handle_t handle);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_stop_capture(handle_t handle);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_get_frame_buf(handle_t handle, isp_buf_t *buf_info,
                                 int timeout_ms);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_set_frame_drop_num(handle_t handle, int frame_cycle,
                                      int drop_num);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_get_expected_frame(handle_t handle, isp_buf_t *buf_info,
                                      int timeout_ms);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_put_frame_buf(handle_t handle, isp_buf_t *buf_info);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_put_frame_buf_by_index(handle_t handle, int buf_index);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_get_view_buf_fd(handle_t handle, int buf_index,
                                   int view_index);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API uint64_t isp_get_view_buf_hdl(handle_t handle, int buf_index,
                                         int view_index);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_get_view_format(handle_t handle, int view_id,
                                   view_format_t *format);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_wait_for_data(handle_t *phandle, int *pbuf_flag,
                                        int camera_count,
                                        bst_timeval_t *ptimeout);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_tick(handle_t handle, uint64_t *ptick);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_brightness(handle_t handle, int value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_brightness(handle_t handle, int *pvalue);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_contrast(handle_t handle, int value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_contrast(handle_t handle, int *pvalue);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_hue(handle_t handle, int value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_hue(handle_t handle, int *pvalue);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_saturation(handle_t handle, int value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_saturation(handle_t handle, int *pvalue);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_sharpness(handle_t handle, int value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_sharpness(handle_t handle, int *pvalue);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_denoise(handle_t handle, int y_value,
                                      int uv_value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_denoise(handle_t handle, int *py_value,
                                      int *puv_value);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_aec(handle_t handle, bool is_enable);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_aec(handle_t handle, bool *pis_enable);

/**
 * @brief Set exposure manually
 * @param
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_mec(handle_t handle, mec_param_t mec_param);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_awb(handle_t handle, bool is_enable);

/**
 * @brief  Set white balance manually
 * @param r_gain Red gain value
 * @param g_gain green gain value
 * @param b_gain blue gain value
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_mwb(handle_t handle, int r_gain, int g_gain,
                                  int b_gain);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_awb(handle_t handle, bool *pis_enable);

/**
 * @brief
 * @param [in]
 * @retval
 *
 **/
EXPORT_API int isp_camera_set_gamma(handle_t handle, gamma_value_t gamma_value);

/**
 * @brief
 * @param [in]
 *
 * @retval
 *
 **/
EXPORT_API int isp_camera_get_gamma(handle_t handle,
                                    gamma_value_t *pgamma_value);

EXPORT_API int isp_camera_get_abnormal_info(handle_t handle,
                                            abnormal_info_t *abnormal_info);

EXPORT_API int isp_resize_resolution(handle_t handle,
                                     resize_info_t resize_info);

EXPORT_API int isp_camera_get_native_fd(handle_t handle, long *pfd);

/**
 * @brief
 * @param [in]
 *
 * @retval
 *
 **/
EXPORT_API int isp_get_valid_camera_count(int *pcount);

/**
 * @brief
 * @param [in]
 *
 * @retval
 *
 **/
EXPORT_API int isp_get_valid_camera_info(camera_info_t *pinfo, int count,
                                         int *valied_camera_num);

EXPORT_API int isp_set_i2c_ctrl(int video_id, i2c_bus_ctrl_t i2c_bus_ctrler);

EXPORT_API int isp_i2c_read_reg(handle_t handle, int bus_id, uint16_t dev_addr,
                                uint16_t reg_addr, int addr_width,
                                uint8_t *reg_value, int data_width);

EXPORT_API int isp_i2c_write_reg(handle_t handle, int bus_id, uint16_t dev_addr,
                                 uint16_t reg_addr, int addr_width,
                                 uint16_t reg_value, int data_width);

EXPORT_API int isp_i2c_read_reg_group(handle_t handle, int bus_id, isp_i2c_info_t *group_info, int group_num);

EXPORT_API int isp_set_internal_trigger(handle_t handle, int deser_id,
                                        int camera_gpio, int fps);

EXPORT_API int isp_set_external_trigger(handle_t handle, int deser_id,
                                        int camera_gpio, int deser_gpio);

EXPORT_API int isp_create_empty_video_group(handle_t *group_handle, int flag,
                                            char *group_name, int camera_count);

EXPORT_API int isp_create_video_group(handle_t *group_handle, int flag,
                                      char *group_name, int *camera_id,
                                      int camera_count);

EXPORT_API int isp_get_single_camera_handle(handle_t group_handle,
                                            unsigned int camera_id,
                                            handle_t *video_handle);

EXPORT_API int isp_group_init_video(handle_t group_handle,
                                    view_type_t view_type, int buf_count);

EXPORT_API int isp_group_get_video_info(handle_t group_handle, int camera_id,
                                        camera_info_t *cam_info);

// EXPORT_API int isp_group_set_view_format(handle_t group_handle, int view_id,
// view_format_t format);

// EXPORT_API int isp_group_get_view_format(handle_t group_handle, int view_id,
// view_format_t* pformat);

EXPORT_API int isp_group_set_internal_trigger(handle_t group_handle,
                                              int deser_id, int camera_gpio,
                                              int fps);

EXPORT_API int isp_group_set_external_trigger(handle_t group_handle,
                                              int deser_id, int camera_gpio,
                                              int deser_gpio);

EXPORT_API int isp_group_get_video_handle(handle_t group_handle, int camera_id,
                                          handle_t *camera_handle);

EXPORT_API int isp_group_open_video(handle_t group_handle, int mode,
                                    int *video_status);

EXPORT_API int isp_group_close_video(handle_t group_handle);

EXPORT_API int isp_group_start_captrue(handle_t group_handle);

EXPORT_API int isp_group_stop_captrue(handle_t group_handle);

/* Feed function */
EXPORT_API int isp_query_feed_drv_free_buf(handle_t handle, uint32_t video_id);
EXPORT_API int isp_query_feed_drv_free_buf_timeout(handle_t handle,
                                                   uint32_t video_id,
                                                   int32_t timeout_ms);
EXPORT_API int isp_query_feed_drv_bufs(handle_t handle, uint32_t video_id,
                                       uint32_t buf_num, isp_buf_t bufs[]);
EXPORT_API int isp_do_feed_drv(handle_t handle, uint32_t num,
                               feed_drv_buf_t bufs[]);
EXPORT_API int isp_init_feed_fw_buf(handle_t handle, feed_fw_reqbuffers_t req,
                                    feed_fw_buf_t bufs[]);
EXPORT_API int isp_do_feed_fw(handle_t handle, feed_fw_control_t feed_ctrl);
EXPORT_API int isp_deinit_feed_fw_buf(handle_t handle,
                                      feed_fw_reqbuffers_t req);

/* diag */
EXPORT_API int isp_get_diag_info(handle_t handle, diag_info_t *diag_info);
EXPORT_API int isp_wait_err_info(handle_t handle, diag_info_t *diag_info,
                                 int32_t timeout_ms);

// EXPORT_API int isp_group_get_sync_frame(handle_t group_handle,
//                                         isp_buf_t *pbuf_info, int buf_num,
//                                         int timeout_us);
// /*check buf_num*/
// EXPORT_API int isp_group_put_frame(handle_t group_handle, isp_buf_t
// *pbuf_info,
//                                    int buf_num);

#ifdef __cplusplus
}
#endif

}  // namespace bvideo

#endif

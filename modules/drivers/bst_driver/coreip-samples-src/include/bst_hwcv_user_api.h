#ifndef _CV_USER_API_H
#define _CV_USER_API_H
#include <stdint.h>
namespace bhwcv {

#ifdef __cplusplus
extern "C" {
#endif
#define EXPORT_API __attribute__((visibility("default")))

typedef enum pxiel_format {
    HWCV_FMT_RGB888 = 0,
    HWCV_FMT_RGB888_PLANAR,
    HWCV_FMT_YUV422_YUYV,
    HWCV_FMT_YUV422_UYVY,
    HWCV_FMT_NV12,
    HWCV_FMT_NV21,
    HWCV_FMT_YUV420_PLANAR,
    HWCV_FMT_YUV422_PLANAR,
    HWCV_FMT_NV12_SEPARATION,
    HWCV_FMT_NV21_SEPARATION
} cv_pixel_format_e;

typedef enum interp_type {
    HWCV_INTERPOLATION_BICUBIC,
    HWCV_INTERPOLATION_BILINEAR,
} interp_warp_e;

typedef enum {
    HWCV_SCALER_POLYPHASE,
    HWCV_SCALER_PYRAMID_LAYER_0,
    HWCV_SCALER_PYRAMID_LAYER_0_1,
    HWCV_SCALER_PYRAMID_LAYER_0_1_2,
    HWCV_SCALER_GAUSS_PYRAMID_LAYER_0,
    HWCV_SCALER_GAUSS_PYRAMID_LAYER_0_1,
    HWCV_SCALER_GAUSS_PYRAMID_LAYER_0_1_2,
} scaler_mode_e;

typedef enum mem_type { HWCV_MEM_USER, HWCV_MEM_DMA } cv_mem_type_t;

typedef struct cv_img_rect {
    int xoffset;
    int yoffset;
    int width;
    int height;
    int wstride;
    int hstride;
    int pixel_format;
} cv_img_rect_t;

typedef struct bst_hwcv_buffer {
    void *uaddr;
    uint32_t mem_type;
    uint32_t buf_index;
    uint32_t fd;
    uint32_t bus_addr;
    uint32_t byte_used;
    uint64_t handle;
    uint32_t reserve[1];
} hwcv_buffer_t;

enum IMPORT_DMA_BUF_TYPE { IN_BUF, OUT_BUF };
enum IMPORT_DMA_BUF_PLANAR { Y_PLANAR, U_PLANAR, V_PLANAR };
enum IMPORT_DMA_BUF_LAYER { LAYER_0, LAYER_1, LAYER_2 };

typedef enum {
    HWCV_SUCC = 0,                      //!< execution success.
    HWCV_ERR_FAIL = -1,                 //!< execution failure.
    HWCV_ERR_DEVICE_UNAVAILABLE = -2,   //!< device unavailable.
    HWCV_ERR_FREE_FAIL = -3,            //!< dma buffer free failure.
    HWCV_ERR_MALLOC_FAIL = -4,          //!< dma buffer malloc failure.
    HWCV_ERR_PARAM_INVALID = -5,        //!< invalid input parameter.
    HWCV_ERR_LUT_INVALID = -6,          //!< invalid model.
    HWCV_ERR_DMA_BUF_IMPORT_FAIL = -7,  //!< dma-buf import failure.
    HWCV_ERR_DMA_BUF_EXPORT_FAIL = -8,  //!< dma-buf import failure.
    HWCV_ERR_INTR_TIMEOUT = -9,
    HWCV_ERR_DMA_BUF_FREE_FAIL = -10
} cv_ret_t;

typedef struct planar {
    int cnt;
    int size[3];
} planar_t;

typedef unsigned long handle_t;

/*------------------- obsolete data structure -------------------*/
typedef struct {
    cv_mem_type_t in_mem_type;        //!< input and output mem type
    cv_mem_type_t out_mem_type;       //!< input and output mem type
    cv_pixel_format_e input_format;   //!< pixel format
    cv_pixel_format_e output_format;  //!< pixel format
    uint8_t is_out_dma_fd;            //!< whether user output dmafd
    uint32_t input_q_idx;             //!< the index handle of the input
    uint32_t in_dma_fd;               //!< dma fd for HWCV_MEM_DMA
    uint32_t out_dma_fd;              //!< dma fd for HWCV_MEM_DMA
    void *lut_start;
    uint32_t lut_size;
    uint32_t frame_wp_src_bus_addr;  //!< gwarp buffers for HWCV_MEM_USER
    uint32_t frame_sc_src_bus_addr;  //!< scaler buffers for HWCV_MEM_USER
    uint32_t frame_wp_dst_bus_addr;  //!< gwarp buffers for HWCV_MEM_USER
    uint32_t frame_sc_dst_bus_addr;  //!< scaler buffers for HWCV_MEM_USER
} cv_user_input_t;

typedef struct bst_cv_req_buffer {
    uint32_t fd;         //!< DMA buf fd of input tensor buffers
    uint32_t size;       //!< the buffer size
    uint32_t align;      //!< the buffer alignment
    uint64_t user_addr;  //!< the buffer user address
    uint32_t bus_addr;   //!< the buffer bus address
} cv_req_buffer_t;

typedef struct bst_cv_user_buffer {
    uint32_t index;
    uint32_t pixel_format;
    uint32_t byte_used;
    uint32_t fd;
    uint64_t ts_usec;
    void *uaddr;
    uint32_t reserve[4];
} bst_cv_user_buffer_t;

#ifdef __cplusplus
}
#endif

EXPORT_API cv_ret_t bst_hwcv_open(handle_t *bstcv_handler);
EXPORT_API cv_ret_t bst_hwcv_close(handle_t bstcv_handler);
EXPORT_API cv_ret_t bst_hwcv_request_cv_buffers(handle_t handle,
                                                cv_mem_type_t mem_type,
                                                int buffer_num, int buffer_size,
                                                hwcv_buffer_t *bufs);
EXPORT_API cv_ret_t bst_hwcv_free_cv_buffer(handle_t handle,
                                            hwcv_buffer_t *bufs);
EXPORT_API cv_ret_t bst_hwcv_import_buf_fds(handle_t handle, int count,
                                            int *fds);
EXPORT_API cv_ret_t bst_hwcv_import_outbuf_fds(handle_t handle, int count,
                                               int *fds);
EXPORT_API cv_ret_t
bst_hwcv_import_dmabuf_handles(handle_t handle, int count, uint64_t *handles,
                               enum IMPORT_DMA_BUF_TYPE type);
EXPORT_API cv_ret_t bst_hwcv_import_dmabuf_handles_common(
    handle_t handle, int count, uint64_t *handles,
    enum IMPORT_DMA_BUF_TYPE type, enum IMPORT_DMA_BUF_PLANAR planar,
    enum IMPORT_DMA_BUF_LAYER layer, uint32_t offset);
EXPORT_API cv_ret_t bst_hwcv_load_gwarp_table(handle_t handle, void *lut_start,
                                              int lut_size);
EXPORT_API cv_ret_t bst_hwcv_load_dma_gwarp_table(handle_t handle,
                                                  uint64_t dma_handle);
EXPORT_API cv_ret_t bst_hwcv_unload_gwarp_table(handle_t handle);
EXPORT_API cv_ret_t bst_hwcv_do_warp(handle_t handle, interp_warp_e inter_type,
                                     cv_img_rect_t input_img,
                                     cv_img_rect_t output_img,
                                     int in_planar_num, hwcv_buffer_t *in_buf,
                                     int out_planar_num,
                                     hwcv_buffer_t *out_buf);
EXPORT_API cv_ret_t bst_hwcv_do_scaler(handle_t handle, scaler_mode_e mode,
                                       cv_img_rect_t input_img,
                                       cv_img_rect_t output_img,
                                       int in_planar_num, hwcv_buffer_t *in_buf,
                                       int out_planar_num,
                                       hwcv_buffer_t *out_buf);
EXPORT_API cv_ret_t bst_hwcv_do_color_convert(
    handle_t handle, cv_img_rect_t input_img, cv_img_rect_t output_img,
    int in_planar_num, hwcv_buffer_t *in_buf, int out_planar_num,
    hwcv_buffer_t *out_buf);
EXPORT_API cv_ret_t bst_get_format_planar_attr(cv_pixel_format_e format,
                                               int w_stride, int h_stride,
                                               planar_t *planar);

/*------------------- obsolete API -------------------*/
EXPORT_API cv_ret_t bst_hwcv_request_dma_buffers(handle_t handle,
                                                 cv_req_buffer_t *cv_buffer,
                                                 int buffer_size,
                                                 int buffer_num,
                                                 bool export_fd);
EXPORT_API cv_ret_t bst_hwcv_free_buffer(handle_t handle,
                                         cv_req_buffer_t *cv_buffer);
EXPORT_API cv_ret_t bst_hwcv_warp(handle_t handle, interp_warp_e inter_type,
                                  const cv_user_input_t in_info,
                                  cv_img_rect_t input_img,
                                  bst_cv_user_buffer_t *out_buf,
                                  cv_img_rect_t output_img);
EXPORT_API cv_ret_t bst_hwcv_scaler(handle_t handle, scaler_mode_e mode,
                                    const cv_user_input_t in_info,
                                    cv_img_rect_t input_img,
                                    bst_cv_user_buffer_t *out_buf,
                                    cv_img_rect_t output_img);
}  // namespace bhwcv
#endif

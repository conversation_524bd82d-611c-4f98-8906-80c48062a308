#include <stdint.h>
#include <time.h>
#include <stdio.h>

#define min(a, b) ((a < b) ? a : b)

static uint64_t tick_ms()
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (ts.tv_sec * 1000 + ts.tv_nsec / 1000000);
}

float print_fps_per_interval(int32_t cameraId, uint64_t timeInterval)
{
    static uint64_t lastTime = tick_ms();
    static int frameCount = 0;
    float fps = 0;

    ++frameCount;
    uint64_t curTime = tick_ms();
    if (curTime - lastTime > timeInterval * 1000) {
        fps = (float)(frameCount * 1000) / (curTime - lastTime);
        frameCount = 0;
        lastTime = curTime;
        if (cameraId >= 0) {
            printf("HWCV Camera[%d]:: fps = %0.1f\n", cameraId, fps);
        } else {
            printf("Performance:: fps = %0.1f\n", fps);
        }

        return fps;
    }
    return 0;
}

int nv12img_relocate(uint8_t *src_buf, int src_w, int src_h, uint8_t *des_buf,
                     int des_w, int des_h, int des_x, int des_y)
{
    // XXX only support even number now
    des_x = des_x / 2 * 2;
    des_y = des_y / 2 * 2;

    // copy y
    for (int i = 0; i < min(src_h, (des_h - des_y)); i++) {
        if ((des_y + i) >= 0) {
            for (int j = 0; j < min(src_w, (des_w - des_x)); j++) {
                if ((des_x + j) >= 0) {
                    *(des_buf + (des_y + i) * des_w + des_x + j) =
                        *(src_buf + i * src_w + j);
                }
            }
        }
    }

    // copy uv
    for (int i = 0; i < min(src_h, (des_h - des_y)) / 2; i++) {
        if ((des_y / 2 + i) >= 0) {
            for (int j = 0; j < min(src_w, (des_w - des_x)); j++) {
                if ((des_x + j) >= 0) {
                    *(des_buf + des_h * des_w + (des_y / 2 + i) * des_w +
                      des_x + j) = *(src_buf + src_h * src_w + i * src_w + j);
                }
            }
        }
    }

    return 0;
}
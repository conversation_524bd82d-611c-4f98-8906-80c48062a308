cmake_minimum_required(VERSION 2.8)

add_definitions(-std=c++11)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/inc)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)
find_package(OpenCV REQUIRED)

#bmpp
list(APPEND CMAKE_MODULE_PATH "$ENV{PKG_CONFIG_SYSROOT_DIR}/usr/share/cmake/Modules")
find_package(fidl REQUIRED MODULE)
set(bmpp_idl_file_path "$ENV{PKG_CONFIG_SYSROOT_DIR}/usr/include/autoplt/bmpp_idl/bmpp.fdepl")
idlGeneratorAndInstall("NO" "lib" "${CMAKE_CURRENT_BINARY_DIR}" "${bmpp_idl_file_path}" "${CMAKE_INSTALL_INCLUDEDIR}/autoplt" "")
include_directories(${CMAKE_CURRENT_BINARY_DIR}/src-gen)

# add_executable(io_test ioctl_test.c)
# add_executable(cv_sample cv_sample.c)

add_executable(hwcv_gwarp_file_test src/hwcv_gwarp_file_test.cpp)
target_link_libraries(hwcv_gwarp_file_test bstcv_gwarp_scaler)

install(TARGETS hwcv_gwarp_file_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

add_executable(hwcv_scaler_file_test src/hwcv_scaler_file_test.cpp)
target_link_libraries(hwcv_scaler_file_test bstcv_gwarp_scaler)

install(TARGETS hwcv_scaler_file_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

add_executable(hwcv_convert_file_test src/hwcv_convert_file_test.cpp)
target_link_libraries(hwcv_convert_file_test bstcv_gwarp_scaler)

install(TARGETS hwcv_convert_file_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

add_executable(hwcv_scaler_camera_test src/hwcv_scaler_camera_test.cpp)
target_link_libraries(hwcv_scaler_camera_test bstcv_gwarp_scaler bstisp drmshow)

install(TARGETS hwcv_scaler_camera_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

add_executable(camdriver src/camdriver.cpp ${PROTO_CC_FILES})
target_link_libraries(camdriver bstcv_gwarp_scaler bstisp drmshow ads_log autoplt cyber cyber_proto fastrtps bmpp protobuf pthread ${OpenCV_LIBS})

add_executable(hwcv_gwarp_camera_test src/hwcv_gwarp_camera_test.cpp)
target_link_libraries(hwcv_gwarp_camera_test bstcv_gwarp_scaler bstisp drmshow)

install(TARGETS hwcv_gwarp_camera_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)


include_directories(${CMAKE_CURRENT_SOURCE_DIR}/third-party/include)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/third-party/lib)

add_executable(hwcv_dma_scaler_file_test src/hwcv_dma_scaler_file_test.cpp)
target_link_libraries(hwcv_dma_scaler_file_test bstcv_gwarp_scaler rcall xrp-linux-native pthread ion)

install(TARGETS hwcv_dma_scaler_file_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

add_executable(hwcv_dma_gwarp_file_test src/hwcv_dma_gwarp_file_test.cpp)
target_link_libraries(hwcv_dma_gwarp_file_test bstcv_gwarp_scaler rcall xrp-linux-native pthread ion)

install(TARGETS hwcv_dma_gwarp_file_test
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

add_executable(hwcv_four_grip_splicing src/hwcv_four_grip_splicing.cpp)
target_link_libraries(hwcv_four_grip_splicing bstcv_gwarp_scaler)

install(TARGETS hwcv_four_grip_splicing
        RUNTIME DESTINATION ${COREIP_SAMPLE_DIR}/../install/cv_gwarp_scaler)

#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include "bst_hwcv_user_api.h"
#include "hwcv_util.hpp"

using namespace bhwcv;

#define MAX_PLANAR_NUM 3

/* format: "w h f F" */
static int input_width;
static int input_height;
static cv_pixel_format_e input_format = HWCV_FMT_RGB888;
static cv_pixel_format_e output_format = HWCV_FMT_RGB888;

/* file: "x y z X Y Z" */
static const char *input_file[MAX_PLANAR_NUM] = {
    "input_planar1.bin", "input_planar2.bin", "input_planar3.bin"};
static const char *output_file[MAX_PLANAR_NUM] = {
    "output_planar1.bin", "output_planar2.bin", "output_planar3.bin"};

/* ROI and pitch: "r s p q R S P Q" */
static int input_x_offset;
static int input_y_offset;
static int output_x_offset;
static int output_y_offset;
static int input_w_stride;
static int input_h_stride;
static int output_w_stride;
static int output_h_stride;

/* fps test: l m */
static int loop = 1;
static int gloop = 1;

/* debug: L */
static bool is_debug;

/* singal */
static volatile sig_atomic_t g_run_flag = 1;
static void signal_handler(int sig) { g_run_flag = 0; }

static void parse_option(int argc, char* argv[])
{
    int ch;

    if (argc == 1) {
        printf(
            "\n\
        usage: hwcv_convert_file_test [options]\n\
        options:\n\
        -w [int]    input width\n\
        -h [int]    input height\n\
        -f [int]    input format\n\
                    0: RGB888[default]\n\
                    1: RGB888_PLANAR\n\
                    2: YUV422_YUYV\n\
                    3: YUV422_UYVY\n\
                    4: NV12\n\
                    5: NV21\n\
                    6: YUV420_PLANAR\n\
                    7: YUV422_PLANAR\n\
                    8: NV12_PLANAR\n\
                    9: NV21_PLANAR\n\
        -F [int]    output format\n\
                    0: RGB888[default]\n\
                    1: RGB888_PLANAR\n\
                    2: YUV422_YUYV\n\
                    3: YUV422_UYVY\n\
                    4: NV12\n\
                    5: NV21\n\
                    6: YUV420_PLANAR\n\
                    7: YUV422_PLANAR\n\
                    8: NV12_PLANAR\n\
                    9: NV21_PLANAR\n\
        -x [string] input planar1 file path\n\
        -y [string] input planar2 file path\n\
        -z [string] input planar3 file path\n\
        -X [string] output planar1 file path\n\
        -Y [string] output planar2 file path\n\
        -Z [string] output planar3 file path\n\
        -r [int]    input x offset\n\
        -s [int]    input y offset\n\
        -p [int]    output x offset\n\
        -q [int]    output y offset\n\
        -R [int]    input w stride\n\
        -S [int]    input h stride\n\
        -P [int]    output w stride\n\
        -Q [int]    output h stride\n\
        -l [int]    loop cnt, default is 1\n\
        -m [int]    gloop cnt, default is 1\n\
        -L          enable print, default is disable\n");

        exit(1);
    }

    while ((ch = getopt(argc, argv,
                        "w:h:f:F:x:y:z:X:Y:Z:r:s:p:q:R:S:P:Q:l:m:L")) != -1) {
        switch (ch) {
            case 'w':
                input_width = atoi(optarg);
                break;
            case 'h':
                input_height = atoi(optarg);
                break;
            case 'f':
                input_format = (cv_pixel_format_e)atoi(optarg);
                break;
            case 'F':
                output_format = (cv_pixel_format_e)atoi(optarg);
                break;
            case 'x':
                input_file[0] = optarg;
                break;
            case 'y':
                input_file[1] = optarg;
                break;
            case 'z':
                input_file[2] = optarg;
                break;
            case 'X':
                output_file[0] = optarg;
                break;
            case 'Y':
                output_file[1] = optarg;
                break;
            case 'Z':
                output_file[2] = optarg;
                break;
            case 'r':
                input_x_offset = atoi(optarg);
                break;
            case 's':
                input_y_offset = atoi(optarg);
                break;
            case 'p':
                output_x_offset = atoi(optarg);
                break;
            case 'q':
                output_y_offset = atoi(optarg);
                break;
            case 'R':
                input_w_stride = atoi(optarg);
                break;
            case 'S':
                input_h_stride = atoi(optarg);
                break;
            case 'P':
                output_w_stride = atoi(optarg);
                break;
            case 'Q':
                output_h_stride = atoi(optarg);
                break;
            case 'l':
                loop = atoi(optarg);
                break;
            case 'm':
                gloop = atoi(optarg);
                break;
            case 'L':
                is_debug = true;
                break;
            default:
                break;
        }
    }

    printf("============Param list============\n");
    printf("Input Size: ");
    printf("(w,h) = (%d, %d)\n", input_width, input_height);

    printf("Input Format: ");
    if (input_format == HWCV_FMT_RGB888) {
        printf("RGB888\n");
    } else if (input_format == HWCV_FMT_RGB888_PLANAR) {
        printf("RGB888_PLANAR\n");
    } else if (input_format == HWCV_FMT_YUV422_YUYV) {
        printf("YUV422_YUYV\n");
    } else if (input_format == HWCV_FMT_YUV422_UYVY) {
        printf("YUV422_UYVY\n");
    } else if (input_format == HWCV_FMT_NV12) {
        printf("NV12\n");
    } else if (input_format == HWCV_FMT_NV21) {
        printf("NV21\n");
    } else if (input_format == HWCV_FMT_YUV420_PLANAR) {
        printf("YUV420_PLANAR\n");
    } else if (input_format == HWCV_FMT_YUV422_PLANAR) {
        printf("YUV422_PLANAR\n");
    } else if (input_format == HWCV_FMT_NV12_SEPARATION) {
        printf("NV12_PLANAR\n");
    } else if (input_format == HWCV_FMT_NV21_SEPARATION) {
        printf("NV21_PLANAR\n");
    } else {
        printf("\n");
    }

    printf("Output Format: ");
    if (output_format == HWCV_FMT_RGB888) {
        printf("RGB888\n");
    } else if (output_format == HWCV_FMT_RGB888_PLANAR) {
        printf("RGB888_PLANAR\n");
    } else if (output_format == HWCV_FMT_YUV422_YUYV) {
        printf("YUV422_YUYV\n");
    } else if (output_format == HWCV_FMT_YUV422_UYVY) {
        printf("YUV422_UYVY\n");
    } else if (output_format == HWCV_FMT_NV12) {
        printf("NV12\n");
    } else if (output_format == HWCV_FMT_NV21) {
        printf("NV21\n");
    } else if (output_format == HWCV_FMT_YUV420_PLANAR) {
        printf("YUV420_PLANAR\n");
    } else if (output_format == HWCV_FMT_YUV422_PLANAR) {
        printf("YUV422_PLANAR\n");
    } else if (output_format == HWCV_FMT_NV12_SEPARATION) {
        printf("NV12_PLANAR\n");
    } else if (output_format == HWCV_FMT_NV21_SEPARATION) {
        printf("NV21_PLANAR\n");
    } else {
        printf("\n");
    }

    printf("Input Offset: ");
    printf("(x,y) = (%d, %d)\n", input_x_offset, input_y_offset);

    printf("Output Offset: ");
    printf("(x,y) = (%d, %d)\n", output_x_offset, output_y_offset);

    printf("Input Stride: ");
    printf("(w,h) = (%d, %d)\n", input_w_stride, input_h_stride);

    printf("Output Stride: ");
    printf("(w,h) = (%d, %d)\n", output_w_stride, output_h_stride);

    printf("Input Planar 1 file: %s\n", input_file[0]);
    printf("Input Planar 2 file: %s\n", input_file[1]);
    printf("Input Planar 3 file: %s\n", input_file[2]);
    printf("Output Planar 1 file: %s\n", output_file[0]);
    printf("Output Planar 2 file: %s\n", output_file[1]);
    printf("Output Planar 3 file: %s\n", output_file[2]);

    printf("Loop: %d\n", loop);
    printf("Gloop: %d\n", gloop);

    printf("Use debug: %d\n", is_debug);
    printf("==================================\n");
}

int main(int argc, char const* argv[])
{
    int ret;
    int fd;
    handle_t handle;
    hwcv_buffer_t input_buffer[MAX_PLANAR_NUM];
    hwcv_buffer_t output_buffer[MAX_PLANAR_NUM];
    cv_img_rect_t input_img;
    cv_img_rect_t output_img;
    planar_t input_planar;
    planar_t output_planar;
    struct timespec ts;
    struct timespec te;
    double interval;

    signal(SIGINT, signal_handler);

    /* parse option */
    parse_option(argc, (char**)argv);

    if (!input_w_stride) input_w_stride = input_width;
    if (!input_h_stride) input_h_stride = input_height;
    if (!output_w_stride) output_w_stride = input_width;
    if (!output_h_stride) output_h_stride = input_height;

    /* Open device */
    ret = bst_hwcv_open(&handle);
    if (ret) {
        printf("Open hwcv device error\n");
        return -1;
    }

    /* Alloc buffer */
    memset(input_buffer, 0, sizeof(input_buffer));
    memset(output_buffer, 0, sizeof(output_buffer));

    bst_get_format_planar_attr(input_format, input_w_stride, input_h_stride,
                               &input_planar);
    bst_get_format_planar_attr(output_format, output_w_stride, output_h_stride,
                               &output_planar);

    for (int i = 0; i < input_planar.cnt; i++) {
        ret = bst_hwcv_request_cv_buffers(
            handle, HWCV_MEM_USER, 1, input_planar.size[i], &input_buffer[i]);
        if (ret) {
            printf(
                "bst_hwcv_request_cv_buffers failed, planar num = %d, size = "
                "%d\n",
                i, input_planar.size[i]);
            return ret;
        }
        if (is_debug) {
            printf(
                "Input planar %d alloc success, paddr = 0x%x, uaddr = 0x%p, "
                "size = %d\n",
                i, input_buffer[i].bus_addr, input_buffer[i].uaddr,
                input_buffer[i].byte_used);
        }
        memset((void*)input_buffer[i].uaddr, 0, input_buffer[i].byte_used);
    }
    for (int i = 0; i < output_planar.cnt; i++) {
        ret = bst_hwcv_request_cv_buffers(
            handle, HWCV_MEM_USER, 1, output_planar.size[i], &output_buffer[i]);
        if (ret) {
            printf(
                "bst_hwcv_request_cv_buffers failed, planar num = %d, size = "
                "%d\n",
                i, input_planar.size[i]);
            return ret;
        }
        if (is_debug) {
            printf(
                "Output planar %d alloc success, paddr = 0x%x, uaddr = 0x%p, "
                "size = %d\n",
                i, output_buffer[i].bus_addr, output_buffer[i].uaddr,
                output_buffer[i].byte_used);
        }
        memset((void*)output_buffer[i].uaddr, 0, output_buffer[i].byte_used);
    }

    /* Convert loop */
    while (loop-- && g_run_flag) {
        /* Fill input buffer */
        clock_gettime(CLOCK_MONOTONIC, &ts);

        for (int i = 0; i < input_planar.cnt; i++) {
            fd = open(input_file[i], O_RDONLY);
            if (fd < 0) {
                printf("open file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            ret = read(fd, (char*)input_buffer[i].uaddr, input_planar.size[i]);
            if (ret < 0) {
                printf("read file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            ret = close(fd);
            if (ret < 0) {
                printf("close file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            input_buffer[i].mem_type = HWCV_MEM_USER;
        }

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Read cost %lfs\n", interval);

        /* Fill output buffer */
        for (int i = 0; i < input_planar.cnt; i++) {
            output_buffer[i].mem_type = HWCV_MEM_USER;
        }

        /* Fill img */
        memset(&input_img, 0, sizeof(input_img));
        memset(&output_img, 0, sizeof(output_img));

        input_img.height = input_height;
        input_img.width = input_width;
        input_img.pixel_format = input_format;
        input_img.xoffset = input_x_offset;
        input_img.yoffset = input_y_offset;
        input_img.wstride = input_w_stride;
        input_img.hstride = input_h_stride;

        output_img.height = input_height;
        output_img.width = input_width;
        output_img.pixel_format = output_format;
        output_img.xoffset = output_x_offset;
        output_img.yoffset = output_y_offset;
        output_img.wstride = output_w_stride;
        output_img.hstride = output_h_stride;

        /* Convert internal loop */
        int tmp = gloop;
        while (tmp-- && g_run_flag) {
            clock_gettime(CLOCK_MONOTONIC, &ts);

            ret = bst_hwcv_do_color_convert(handle, input_img, output_img,
                                   input_planar.cnt, input_buffer,
                                   output_planar.cnt, output_buffer);
            if (ret) {
                printf("Gwarp error, ret = %d\n", ret);
                goto exit;
            }

            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("Convert cost %lfs\n", interval);

            print_fps_per_interval(-1, 1);
        }

        /* save output */
        clock_gettime(CLOCK_MONOTONIC, &ts);

        for (int i = 0; i < output_planar.cnt; i++) {
            if (is_debug)
                printf("Save to file \"%s\", len = %d\n", output_file[i],
                       output_planar.size[i]);
            fd = open(output_file[i], O_CREAT | O_RDWR);
            if (fd < 0) {
                printf("open file %s error: %s\n", output_file[i],
                       strerror(errno));
                goto exit;
            }

            ret =
                write(fd, (char*)output_buffer[i].uaddr, output_planar.size[i]);
            if (ret < 0) {
                printf("write file %s error: %s, uaddr = 0x%p, len = %d\n",
                       output_file[i], strerror(errno), output_buffer[i].uaddr,
                       output_buffer[i].byte_used);
                goto exit;
            }

            ret = close(fd);
            if (ret < 0) {
                printf("close file %s error: %s\n", output_file[i],
                       strerror(errno));
                goto exit;
            }
        }

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Write cost %lfs\n", interval);
    }
    /* free buffer */
exit:
    for (int i = 0; i < input_planar.cnt; i++) {
        bst_hwcv_free_cv_buffer(handle, &input_buffer[i]);
    }

    for (int i = 0; i < output_planar.cnt; i++) {
        bst_hwcv_free_cv_buffer(handle, &output_buffer[i]);
    }

    bst_hwcv_close(handle);

    return 0;
}
#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include "bst_hwcv_user_api.h"
#include "hwcv_util.hpp"

using namespace bhwcv;

#define MAX_PLANAR_NUM 3
#define MAX_LAYER_NUM 3

/* mode: "a" */
static scaler_mode_e scaler_mode;

/* format: "w h f W H" */
static int input_width;
static int input_height;
static cv_pixel_format_e input_format;
static int output_width;
static int output_height;

/* file: "x y z X Y Z" */
static char input_file[MAX_PLANAR_NUM][30] = {"in_1.bin", "in_2.bin",
                                              "in_3.bin"};
static char output_file[MAX_LAYER_NUM][MAX_PLANAR_NUM][30] = {
    {"out_1_1.bin", "out_1_2.bin", "out_1_3.bin"},
    {"out_2_1.bin", "out_2_2.bin", "out_2_3.bin"},
    {"out_3_1.bin", "out_3_2.bin", "out_3_3.bin"}};

/* ROI and pitch: "r s p q R S P Q" */
static int input_x_offset;
static int input_y_offset;
static int output_x_offset;
static int output_y_offset;
static int input_w_stride;
static int input_h_stride;
static int output_w_stride;
static int output_h_stride;

/* fps test: l m */
static int loop = 1;
static int sloop = 1;

/* debug: L */
static bool is_debug;

/* pyramid */
static bool pyramid_enable = false;
static uint8_t layer_num = 0;

/* singal */
static volatile sig_atomic_t g_run_flag = 1;
static void signal_handler(int sig) { g_run_flag = 0; }

static void parse_option(int argc, char *argv[])
{
    int ch;
    if (argc == 1) {
        printf(
            "\n\
        usage: hwcv_scaler_file_test [options]\n\
        options:\n\
        -a [int]    interpolation algorithm\n\
                    0: POLYPHASE[default]\n\
                    1: PYRAMID_LAYER_0\n\
                    2: PYRAMID_LAYER_0_1\n\
                    3: PYRAMID_LAYER_0_1_2\n\
                    4: PYRAMID_GAUSS_LAYER_0\n\
                    5: PYRAMID_GAUSS_LAYER_0_1\n\
                    6: PYRAMID_GAUSS_LAYER_0_1_2\n\
        -w [int]    input width\n\
        -h [int]    input height\n\
        -f [int]    input format\n\
                    0: RGB888[default]\n\
                    1: RGB888_PLANAR\n\
                    2: YUV422_YUYV\n\
                    3: YUV422_UYVY\n\
                    4: NV12\n\
                    5: NV21\n\
                    6: YUV420_PLANAR\n\
                    7: NOT SUPPORT\n\
                    8: NV12_PLANAR\n\
                    9: NV21_PLANAR\n\
        -W [int]    output width\n\
        -H [int]    output height\n\
        -x [string] input planar1 file path\n\
        -y [string] input planar2 file path\n\
        -z [string] input planar3 file path\n\
        -X [string] output planar1 file path\n\
        -Y [string] output planar2 file path\n\
        -Z [string] output planar3 file path\n\
        -r [int]    input x offset\n\
        -s [int]    input y offset\n\
        -p [int]    output x offset\n\
        -q [int]    output y offset\n\
        -R [int]    input w stride\n\
        -S [int]    input h stride\n\
        -P [int]    output w stride\n\
        -Q [int]    output h stride\n\
        -l [int]    loop cnt, default is 1\n\
        -m [int]    sloop cnt, default is 1\n\
        -L          enable print, default is disable\n");
        exit(1);
    }

    while ((ch = getopt(argc, argv,
                        "a:w:h:f:W:H:x:y:z:X:Y:Z:r:s:p:q:R:S:P:Q:l:m:L")) != -1) {
        switch (ch) {
            case 'a':
                scaler_mode = (scaler_mode_e)atoi(optarg);
                break;
            case 'w':
                input_width = atoi(optarg);
                break;
            case 'h':
                input_height = atoi(optarg);
                break;
            case 'f':
                input_format = (cv_pixel_format_e)atoi(optarg);
                break;
            case 'W':
                output_width = atoi(optarg);
                break;
            case 'H':
                output_height = atoi(optarg);
                break;
            case 'x':
                strcpy(input_file[0], optarg);
                break;
            case 'y':
                strcpy(input_file[1], optarg);
                break;
            case 'z':
                strcpy(input_file[2], optarg);
                break;
            case 'X':
                strcpy(output_file[0][0], optarg);
                sprintf(output_file[1][0], "%s_%s", "lev1", optarg);
                sprintf(output_file[2][0], "%s_%s", "lev2", optarg);
                break;
            case 'Y':
                strcpy(output_file[0][1], optarg);
                sprintf(output_file[1][1], "%s_%s", "lev1", optarg);
                sprintf(output_file[2][1], "%s_%s", "lev2", optarg);
                break;
            case 'Z':
                strcpy(output_file[0][2], optarg);
                sprintf(output_file[1][2], "%s_%s", "lev1", optarg);
                sprintf(output_file[2][2], "%s_%s", "lev2", optarg);
                break;
            case 'r':
                input_x_offset = atoi(optarg);
                break;
            case 's':
                input_y_offset = atoi(optarg);
                break;
            case 'p':
                output_x_offset = atoi(optarg);
                break;
            case 'q':
                output_y_offset = atoi(optarg);
                break;
            case 'R':
                input_w_stride = atoi(optarg);
                break;
            case 'S':
                input_h_stride = atoi(optarg);
                break;
            case 'P':
                output_w_stride = atoi(optarg);
                break;
            case 'Q':
                output_h_stride = atoi(optarg);
                break;
            case 'l':
                loop = atoi(optarg);
                break;
            case 'm':
                sloop = atoi(optarg);
                break;
            case 'L':
                is_debug = true;
                break;
            default:
                break;
        }
    }

    printf("============Param list============\n");
    if (scaler_mode == 0) {
        layer_num = 1;
        pyramid_enable = false;
    } else if (scaler_mode < HWCV_SCALER_GAUSS_PYRAMID_LAYER_0) {
        layer_num = scaler_mode;
        pyramid_enable = true;
    } else {
        layer_num = scaler_mode - 3;
        pyramid_enable = true;
    }
    printf("Scaler mode: %d\n", scaler_mode);
    printf("Pyramid_enable: %d\n", pyramid_enable);
    printf("Layer_num: %d\n", layer_num);

    printf("Input Size: ");
    printf("(w,h) = (%d, %d)\n", input_width, input_height);

    printf("Input Format: ");
    if (input_format == HWCV_FMT_RGB888) {
        printf("RGB888\n");
    } else if (input_format == HWCV_FMT_RGB888_PLANAR) {
        printf("RGB888_PLANAR\n");
    } else if (input_format == HWCV_FMT_YUV422_YUYV) {
        printf("YUV422_YUYV\n");
    } else if (input_format == HWCV_FMT_YUV422_UYVY) {
        printf("YUV422_UYVY\n");
    } else if (input_format == HWCV_FMT_NV12) {
        printf("NV12\n");
    } else if (input_format == HWCV_FMT_NV21) {
        printf("NV21\n");
    } else if (input_format == HWCV_FMT_YUV420_PLANAR) {
        printf("YUV420_PLANAR\n");
    } else if (input_format == HWCV_FMT_NV12_SEPARATION) {
        printf("NV12_PLANAR\n");
    } else if (input_format == HWCV_FMT_NV21_SEPARATION) {
        printf("NV21_PLANAR\n");
    } else {
        printf("\n");
    }

    printf("Output Size: ");
    printf("(w,h) = (%d, %d)\n", output_width, output_height);

    printf("Input Offset: ");
    printf("(x,y) = (%d, %d)\n", input_x_offset, input_y_offset);

    printf("Output Offset: ");
    printf("(x,y) = (%d, %d)\n", output_x_offset, output_y_offset);

    printf("Input Stride: ");
    printf("(w,h) = (%d, %d)\n", input_w_stride, input_h_stride);

    printf("Output Stride: ");
    printf("(w,h) = (%d, %d)\n", output_w_stride, output_h_stride);

    printf("Input Planar 1 file: %s\n", input_file[0]);
    printf("Input Planar 2 file: %s\n", input_file[1]);
    printf("Input Planar 3 file: %s\n", input_file[2]);
    printf("Output Planar 1 file: %s\n", output_file[0][0]);
    printf("Output Planar 2 file: %s\n", output_file[0][1]);
    printf("Output Planar 3 file: %s\n", output_file[0][2]);
    if (layer_num > 1) {
        printf("Layer1 Output Planar 1 file: %s\n", output_file[1][0]);
        printf("Layer1 Output Planar 2 file: %s\n", output_file[1][1]);
        printf("Layer1 Output Planar 3 file: %s\n", output_file[1][2]);
    }
    if (layer_num > 2) {
        printf("Layer2 Output Planar 1 file: %s\n", output_file[2][0]);
        printf("Layer2 Output Planar 2 file: %s\n", output_file[2][1]);
        printf("Layer2 Output Planar 3 file: %s\n", output_file[2][2]);
    }

    printf("Loop: %d\n", loop);
    printf("Sloop: %d\n", sloop);
    printf("Use debug: %d\n", is_debug);
    printf("==================================\n");
}

int main(int argc, char const *argv[])
{
    int ret;
    int fd;
    handle_t handle;
    planar_t input_planar;
    planar_t output_planar[MAX_LAYER_NUM];
    hwcv_buffer_t input_buffer[MAX_PLANAR_NUM];
    hwcv_buffer_t output_buffer[MAX_LAYER_NUM][MAX_PLANAR_NUM];
    cv_img_rect_t input_img;
    cv_img_rect_t output_img;
    struct timespec ts;
    struct timespec te;
    double interval;

    signal(SIGINT, signal_handler);

    /* parse option */
    parse_option(argc, (char **)argv);

    if (!input_w_stride) input_w_stride = input_width;
    if (!input_h_stride) input_h_stride = input_height;
    if (!output_w_stride) output_w_stride = output_width;
    if (!output_h_stride) output_h_stride = output_height;

    /* Open device */
    ret = bst_hwcv_open(&handle);
    if (ret) {
        printf("Open hwcv device error\n");
        return -1;
    }

    /* Alloc buffer */
    memset(input_buffer, 0, sizeof(input_buffer));
    memset(output_buffer, 0, sizeof(output_buffer));

    /* Alloc input buffer */
    bst_get_format_planar_attr(input_format, input_w_stride, input_h_stride,
                               &input_planar);
    for (int i = 0; i < input_planar.cnt; i++) {
        ret = bst_hwcv_request_cv_buffers(
            handle, HWCV_MEM_USER, 1, input_planar.size[i], &input_buffer[i]);
        if (ret) {
            printf(
                "bst_hwcv_request_cv_buffers failed, planar num = %d, size = "
                "%d\n",
                i, input_planar.size[i]);
            return ret;
        }
        if (is_debug) {
            printf(
                "input planar-%d alloc success, paddr = 0x%x, uaddr = 0x%p, "
                "size = %d\n",
                i, input_buffer[i].bus_addr, input_buffer[i].uaddr,
                input_buffer[i].byte_used);
        }
        memset((void *)input_buffer[i].uaddr, 0, input_buffer[i].byte_used);
    }

    /* Alloc output buffer */
    for (int i = 0; i < layer_num; i++) {
        if (pyramid_enable) {
            bst_get_format_planar_attr(input_format, output_w_stride,
                                       input_height >> (i + 1),
                                       &output_planar[i]);
        } else {
            bst_get_format_planar_attr(input_format, output_w_stride,
                                       output_h_stride, &output_planar[i]);
        }

        for (int j = 0; j < output_planar[i].cnt; j++) {
            ret = bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1,
                                              output_planar[i].size[j],
                                              &output_buffer[i][j]);
            if (ret) {
                printf(
                    "bst_hwcv_request_cv_buffers failed, planar num = %d, size "
                    "= %d\n",
                    i, output_planar[i].size[j]);
                return ret;
            }
            if (is_debug) {
                printf(
                    "output layer-%d planar-%d alloc success, paddr = 0x%x, "
                    "uaddr = 0x%p, size = %d\n",
                    i, j, output_buffer[i][j].bus_addr,
                    output_buffer[i][j].uaddr, output_buffer[i][j].byte_used);
            }
            memset((void *)output_buffer[i][j].uaddr, 0,
                   output_buffer[i][j].byte_used);
        }
    }

    /* Scaler loop */
    while (loop-- && g_run_flag) {
        /* Fill input buffer */
        clock_gettime(CLOCK_MONOTONIC, &ts);

        for (int i = 0; i < input_planar.cnt; i++) {
            fd = open(input_file[i], O_RDONLY);
            if (fd < 0) {
                printf("open file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            ret = read(fd, (char *)input_buffer[i].uaddr, input_planar.size[i]);
            if (ret < 0) {
                printf("read file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            ret = close(fd);
            if (ret < 0) {
                printf("close file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            input_buffer[i].mem_type = HWCV_MEM_USER;
        }

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Read cost %lfs\n", interval);

        /* Fill output buffer */
        for (int i = 0; i < layer_num; i++) {
            for (int j = 0; j < output_planar[i].cnt; i++) {
                output_buffer[i][j].mem_type = HWCV_MEM_USER;
            }
        }

        /* Fill img */
        memset(&input_img, 0, sizeof(input_img));
        memset(&output_img, 0, sizeof(output_img));

        input_img.height = input_height;
        input_img.width = input_width;
        input_img.pixel_format = input_format;
        input_img.xoffset = input_x_offset;
        input_img.yoffset = input_y_offset;
        input_img.wstride = input_w_stride;
        input_img.hstride = input_h_stride;

        output_img.height = output_height;
        output_img.width = output_width;
        output_img.pixel_format = input_format;
        output_img.xoffset = output_x_offset;
        output_img.yoffset = output_y_offset;
        output_img.wstride = output_w_stride;
        output_img.hstride = output_h_stride;

        /* Scaler internal loop */
        int tmp = sloop;
        while (tmp-- && g_run_flag) {
            clock_gettime(CLOCK_MONOTONIC, &ts);

            ret = bst_hwcv_do_scaler(
                handle, scaler_mode, input_img, output_img, input_planar.cnt,
                input_buffer, output_planar[0].cnt, &output_buffer[0][0]);
            if (ret) {
                printf("Scaler error, ret = %d\n", ret);
                goto exit;
            }

            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("Scaler cost %lfs\n", interval);

            print_fps_per_interval(-1, 1);
        }

        /* save output */
        clock_gettime(CLOCK_MONOTONIC, &ts);
        for (int i = 0; i < layer_num; i++) {
            for (int j = 0; j < output_planar[i].cnt; j++) {
                fd = open(output_file[i][j], O_CREAT | O_RDWR);
                if (fd < 0) {
                    printf("open file %s error: %s\n", output_file[i][j],
                           strerror(errno));
                    goto exit;
                }

                ret = write(fd, (char *)output_buffer[i][j].uaddr,
                            output_planar[i].size[j]);
                if (ret < 0) {
                    printf("write file %s error: %s, uaddr = 0x%p, len = %d\n",
                           output_file[i][j], strerror(errno),
                           output_buffer[i][j].uaddr, output_planar[i].size[j]);
                    goto exit;
                }

                ret = close(fd);
                if (ret < 0) {
                    printf("close file %s error: %s\n", output_file[i][j],
                           strerror(errno));
                    goto exit;
                }
            }
        }

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Write cost %lfs\n", interval);
    }
exit:
    /* free buffer */
    if (is_debug) printf("Free buffer\n");
    for (int i = 0; i < input_planar.cnt; i++) {
        bst_hwcv_free_cv_buffer(handle, &input_buffer[i]);
    }

    for (int i = 0; i < layer_num; i++) {
        for (int j = 0; j < output_planar[i].cnt; j++) {
            bst_hwcv_free_cv_buffer(handle, &output_buffer[i][j]);
        }
    }

    bst_hwcv_close(handle);

    return 0;
}
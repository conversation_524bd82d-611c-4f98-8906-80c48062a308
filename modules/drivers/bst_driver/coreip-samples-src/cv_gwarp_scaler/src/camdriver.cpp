#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include <stdlib.h>
#include <iostream>
#include <iomanip>
#include "autoplt/include/ADSNode.h"
#include "autoplt/include/ADSTime.h"
#include <ads_log/logger.h>
#include "thirdparty/recommend_protocols/drivers/proto/camera.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"
#include <opencv2/opencv.hpp>

#include "drmshow.h"
#include "bst_hwcv_user_api.h"
#include "bst_isp_sdk_api.h"
#include "hwcv_util.hpp"

#include "v1/autoplt/bmpp/BmppProxy.hpp"
using namespace v1::autoplt::bmpp;

using namespace bhwcv;
using namespace bvideo;

using namespace std;
using namespace autoplt;
using rainbowdash::drivers::Image;
using rainbowdash::drivers::CompressedImage;

#define MAX_PLANAR_CNT 3
#define CAMERA_BUFFER_NUM 3
#define PRINT_FPS_PER_S 30
#define NV12_LEN(width, height) ((width) * (height)*3 / 2)

static interp_warp_e gwarp_algo = HWCV_INTERPOLATION_BILINEAR;
static scaler_mode_e scaler_mode = HWCV_SCALER_POLYPHASE;
static int camera_id = 0;
static int view_id = 0;
static int output_width = 1920;
static int output_height = 1080;
static bool is_debug = true;
static bool is_display;
static const char *lut_file = "/userdata/8m_1920_1080_gwarp_table.bin";
static int display_width = 1920;
static int display_height = 1080;
static int scaler_width = 1920;
static int scaler_height = 1024;
static const char *output_file = "camera_dewarp_results.bin";

std::shared_ptr<BmppProxy> BmppImplObj;

static volatile sig_atomic_t g_run_flag = 1;
static void signal_handler(int sig) { g_run_flag = 0; }

bool InitEncodeObj()
{
    std::cout << "InitEncodeObj"<< std::endl;
    BmppImplObj = std::make_shared<BmppProxy>();
    BmppTypes::EncoderParam param;
    param.encodeType = BmppTypes::VideoType::MJPEG;
    param.yuvFormat  = BmppTypes::YUVType::YUV420SP;
    param.width      = 1920;
    param.height     = 1024;
    param.frameRate  = 30;

    BmppTypes::ErrorCode error_code;
    BmppImplObj->InitEncoder(param, error_code);
    if (error_code != BmppTypes::ErrorCode::SUCCESS)
        return false;
    return true;
}


static void parse_option(int argc, char *argv[])
{
    int ch;

    if (argc == 1) {
        printf(
            "\n\
        usage: hwcv_gwarp_camera_test [options]\n\
        options:\n\
        -a [int]    interpolation algorithm\n\
                    0: bicubic\n\
                    1: bilinear[default]\n\
        -v [int]    view id\n\
                    0: view 0[default]\n\
                    1: view 1\n\
        -i [int]    camera id\n\
                    default is 0\n\
        -W [int]    output width\n\
                    default is 0\n\
        -H [int]    output height\n\
                    default is 0\n\
        -t [string] input lut table file path\n\
        -p [int]    is print\n\
                    0: disable[default]\n\
                    1: enable\n\
        -d [int]    is display\n\
                    0: disable[default]\n\
                    1: enable\n\
        -s [int]    is save\n\
                    0: disable[default]\n\
                    1: enable\n\
        -x [int]    display width\n\
                    1920[defaut]\n\
        -y [int]    display height\n\
                    1080[default]\n");
        exit(1);
    }

    while ((ch = getopt(argc, argv, "a:v:i:W:H:t:l:x:y:pds")) != -1) {
        switch (ch) {
            case 'a':
                gwarp_algo = (interp_warp_e)atoi(optarg);
                break;
            case 'v':
                view_id = atoi(optarg);
                break;
            case 'i':
                camera_id = atoi(optarg);
                break;
            case 'W':
                output_width = atoi(optarg);
                break;
            case 'H':
                output_height = atoi(optarg);
                break;
            case 't':
                lut_file = optarg;
                break;
            case 'p':
                is_debug = true;
                break;
            case 'd':
                is_display = true;
                break;
            case 'x':
                display_width = atoi(optarg);
                break;
            case 'y':
                display_height = atoi(optarg);
                break;
            default:
                break;
        }
    }

    printf("============Param list============\n");
    printf("Gwarp Algorithm: ");
    if (gwarp_algo == HWCV_INTERPOLATION_BICUBIC) {
        printf("Bicubic\n");
    } else if (gwarp_algo == HWCV_INTERPOLATION_BILINEAR) {
        printf("Bilinear\n");
    } else {
        printf("\n");
    }

    printf("view id: %d\n", view_id);
    printf("Camera id: %d\n", camera_id);

    printf("Output Size: ");
    printf("(w,h) = (%d, %d)\n", output_width, output_height);

    printf("Display Size: ");
    printf("(w,h) = (%d, %d)\n", display_width, display_height);

    printf("Use debug: %d\n", is_debug);
    printf("Use display: %d\n", is_display);
    printf("==================================\n");
}

cv::Mat getMatFromNV12(char * buf, int width, int height) {
	cv::Mat yuvImg;
	cv::Mat rgbImg(height, width,  CV_8UC3);
	yuvImg.create(height * 3 / 2, width, CV_8UC1);
	memcpy(yuvImg.data, buf, height * 3 / 2 * width);
	cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_NV12);

	return rgbImg;
}

int main(int argc, char const *argv[])
{

  // 初始化通信框架
  ADSNode::Init(argv[0]);
  // 创建Node
  std::shared_ptr<ADSNode> talker_node = std::make_shared<ADSNode>("cam_test_talker");

  std::string channel_name = "/mipi_camera/cam0";
  // 创建Talker
  auto talker = talker_node->CreateWriter<CompressedImage>( channel_name );
  if(talker==nullptr)
  {
    AERROR << "[Example talker] talker create failed";
    return 0;
  }


    int ret = 0;
    int save_fd = 0;
    int lut_fd = 0;
    char *lut_buffer = NULL;
    int lut_len = 0;

    /* for hwcv */
    hwcv_buffer_t input_buffer;
    hwcv_buffer_t output_buffer;
    hwcv_buffer_t output_scaler_buffer;
    cv_img_rect_t input_img;
    cv_img_rect_t output_img;
    cv_img_rect_t output_scaler_img;
    bhwcv::handle_t handle;

    /* for camera */
    bvideo::handle_t cam_handle;
    int camera_dma_fds[CAMERA_BUFFER_NUM];
    view_format_t view_format;
    isp_buf_t cam_buf;
    view_type_t view_type;

    /* for display */
    uint8_t *primary_buf;
    int primary_size;

    struct timespec ts;
    struct timespec te;
    double interval;
        int count = 0;
    signal(SIGINT, signal_handler);

    /* parse option */
    //parse_option(argc, (char **)argv);

    /* Open camera */
    if (is_debug) printf("Open camera\n");
    cam_handle = isp_open_video(camera_id, ISP_MODE_VIEWS);
    if (!cam_handle) {
        printf("Open camera failed, camera id = %d\n", camera_id);
        return -1;
    }

    /* Init camera */
    if (is_debug) printf("Init camera\n");
    view_type = (view_id == 0) ? ISP_VIEW0 : ISP_VIEW1;
    if (isp_init_video(cam_handle, view_type, CAMERA_BUFFER_NUM)) {
        printf("Init cameara failed, camera id = %d\n", camera_id);
        return -1;
    }

    /* Get camera video buf dmafd */
    if (is_debug) printf("Get camera handle\n");
    for (int i = 0; i < CAMERA_BUFFER_NUM; i++) {
        camera_dma_fds[i] = isp_get_view_buf_fd(cam_handle, i, view_id);
        if (is_debug)
            printf("camera_buff_fds[%d] = %lu\n", i, camera_dma_fds[i]);
    }

    /* Get camera view format */
    if (is_debug) printf("Get camera format\n");
    isp_get_view_format(cam_handle, view_id, &view_format);
    printf("Camera view %d input size: (w,h) = (%d, %d)\n", view_id,
           view_format.width, view_format.height);

    /* Start Cameara */
    if (is_debug) printf("Start capture\n");
    if (isp_start_capture(cam_handle)) {
        printf("Start camera failed, camera id = %d\n", camera_id);
        return -1;
    }

    /* Open hwcv device */
    if (is_debug) printf("Open hwcv\n");

    ret = bst_hwcv_open(&handle);
    if (ret) {
        printf("Open hwcv device failed\n");
        goto isp_exit;
    }

    /* Load lut table */
    if (is_debug) printf("Load lut table\n");
    lut_fd = open(lut_file, O_RDONLY);
    if (lut_fd < 0) {
        printf("open file %s error\n", lut_file);
        goto isp_exit;
    }

    lut_len = lseek(lut_fd, 0, SEEK_END);
    lseek(lut_fd, 0, SEEK_SET);
    lut_buffer = (char *)malloc(lut_len);

    ret = read(lut_fd, lut_buffer, lut_len);
    if (ret < 0) {
        printf("read file %s error\n", lut_file);
        goto isp_exit;
    }

    ret = close(lut_fd);
    if (ret < 0) {
        printf("close file %s error\n", lut_file);
        goto isp_exit;
    }

    ret = bst_hwcv_load_gwarp_table(handle, lut_buffer, lut_len);
    if (ret) {
        printf("load lut error, ret = %d\n", ret);
        goto isp_exit;
    }

    free(lut_buffer);

    /* Fill img */
    if (is_debug) printf("Fill in/out img\n");
    memset(&input_img, 0, sizeof(input_img));
    memset(&output_img, 0, sizeof(output_img));
    memset(&output_scaler_img, 0, sizeof(output_scaler_img));
    input_img.height = view_format.height;
    input_img.width = view_format.width;
    input_img.pixel_format = HWCV_FMT_NV12;
    output_img.height = output_height;
    output_img.width = output_width;
    output_img.pixel_format = HWCV_FMT_NV12;
    output_scaler_img.height = scaler_height;
    output_scaler_img.width = scaler_width;
    output_scaler_img.pixel_format = HWCV_FMT_NV12;

    /* Alloc output buffer */
    if (is_debug) printf("Alloc output buffer\n");
    memset(&output_buffer, 0, sizeof(hwcv_buffer_t));
    bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1,
                                NV12_LEN(output_width, output_height),
                                &output_buffer);
    output_buffer.mem_type = HWCV_MEM_USER;

    memset(&output_scaler_buffer, 0, sizeof(hwcv_buffer_t));
    bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1,
                                NV12_LEN(scaler_width, scaler_height),
                                &output_scaler_buffer);
    output_scaler_buffer.mem_type = HWCV_MEM_USER;

    /* Fill input buffer */
    if (is_debug) printf("Fill input buffer\n");
    input_buffer.mem_type = HWCV_MEM_DMA;
    input_buffer.byte_used = NV12_LEN(input_img.width, input_img.height);

    if (is_debug) printf("import buf fds\n");
    ret = bst_hwcv_import_buf_fds(handle, CAMERA_BUFFER_NUM, camera_dma_fds);
    if (ret) {
        printf("bst_hwcv_import_buf_fds failed, ret = %d\n", ret);
        goto exit;
    }

    /*init bmpp*/
    if (!InitEncodeObj())
    {
       goto exit;
    }
    
    

    /* Gwarp Loop */
    while (g_run_flag) {
        clock_gettime(CLOCK_MONOTONIC, &ts);
        ret = isp_get_frame_buf(cam_handle, &cam_buf, -1);

        // gwarp
        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Get frame cost %lf s\n", interval);

        if (ret == 0) {
            input_buffer.buf_index = cam_buf.buf_index;

            clock_gettime(CLOCK_MONOTONIC, &ts);

            ret = bst_hwcv_do_warp(handle, gwarp_algo, input_img, output_img, 1,
                                   &input_buffer, 1, &output_buffer);
            if (ret) {
                printf("gwarp error, ret = %d\n", ret);
                goto exit;
            }

            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("Gwarp cost %lf s\n", interval);

            // scaler
            clock_gettime(CLOCK_MONOTONIC, &ts);
            ret = bst_hwcv_do_scaler(handle, scaler_mode, output_img, output_scaler_img,
                                     1, &output_buffer, 1, &output_scaler_buffer);
            if (ret) {
                printf("scaler error, ret = %d\n", ret);
                goto exit;
            }
            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("Scaler cost %lf s\n", interval);


            //bmpp            
            clock_gettime(CLOCK_MONOTONIC, &ts);

            BmppTypes::FrameBuffer frame_buffer;
            frame_buffer.data = (uint8_t *)output_scaler_buffer.uaddr;
            frame_buffer.size = output_scaler_buffer.byte_used;
            frame_buffer.flag = 0;
            BmppTypes::ErrorCode error_code;
            BmppImplObj->PutFrameToEncoderByUsrBuff(frame_buffer, error_code);
            BmppTypes::BmppPacket stream_packet;
            BmppImplObj->GetPacketFromEncoder(stream_packet, error_code);
            if (error_code == BmppTypes::ErrorCode::TERMINATE) break;
            else if (error_code == BmppTypes::ErrorCode::RETRY)
            {
            usleep(60000);
            continue;
            }
            if (error_code == BmppTypes::ErrorCode::SUCCESS)
            {
                auto msg = std::make_shared<CompressedImage>();
                auto header = msg->mutable_header();

                header->set_timestamp(apollo::cyber::Time::Now().ToNanosecond());
                header->set_module_name("cam_node");
                header->set_version(0);
                msg->set_format("jpeg");
                msg->set_data((char *)stream_packet.data,  stream_packet.size);
                talker->Write(msg);

                // count++;
                // if (count % 50 == 0)
                // {                
                //     std::string file_name = "bmpp"+std::to_string(count) +"_bmpp.jpg" ;
                // 	std::ofstream File(file_name);

                // 	// Write to the file
                // 	//File << "Files can be tricky, but it is fun enough!";
                // 	File.write((char *)stream_packet.data,  stream_packet.size);

                // 	// Close the file
                // 	File.close();
                // }
            }

            BmppImplObj->ReleasePacketToEncoder(stream_packet, error_code);

            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("bmpp cost %lf s\n", interval);


        } else if (ret == bvideo::RET_TRYAGAIN || ret == bvideo::RET_TIMEOUT) {
            printf("Get frame failed, try again, ret = %d\n", ret);
            continue;
        } else {
            printf("Get frame error, ret = %d\n", ret);
            goto exit;
        }

        /* isp put frame buf--- */
        ret = isp_put_frame_buf(cam_handle, &cam_buf);
        if (ret) {
            printf("isp put frame_buf failed\n");
            goto exit;
        }

        /* printf fps */
        print_fps_per_interval(camera_id, PRINT_FPS_PER_S);
    }
exit:
    if (is_debug) printf("Free buffer\n");
    bst_hwcv_free_cv_buffer(handle, &output_buffer);
    bst_hwcv_close(handle);

    if (is_debug) printf("Free bmpp\n");
    BmppTypes::ErrorCode error_code;
    BmppImplObj->DestoryEncoder(error_code);

isp_exit:
    /* Stop capture */
    ret = isp_stop_capture(cam_handle);
    if (ret) {
        printf("isp_stop_capture failed\n");
    }

    ret = isp_close_video(cam_handle);
    if (ret) {
        printf("isp_close_video failed\n");
    }

    return ret;
}
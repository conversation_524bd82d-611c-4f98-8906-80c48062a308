#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include <stdlib.h>
#include <iostream>
#include <iomanip>
#include <csignal>
#include <unistd.h>
#include "autoplt/include/ADSNode.h"
#include "autoplt/include/ADSTime.h"
#include <ads_log/logger.h>
#include "modules/drivers/proto/camera.pb.h"
#include "modules/common/proto/header.pb.h"
#include <opencv2/opencv.hpp>

#include "drmshow.h"
#include "bst_hwcv_user_api.h"
#include "bst_isp_sdk_api.h"

#include "v1/autoplt/bmpp/BmppProxy.hpp"
using namespace v1::autoplt::bmpp;
using rainbowdash::drivers::CompressedImage;


using namespace bhwcv;
using namespace bvideo;

using namespace std;
using namespace autoplt;

#define MAX_PLANAR_CNT 3
#define CAMERA_BUFFER_NUM 3
#define PRINT_FPS_PER_S 30
#define NV12_LEN(width, height) ((width) * (height)*3 / 2)

static interp_warp_e gwarp_algo = HWCV_INTERPOLATION_BILINEAR;
static scaler_mode_e scaler_mode = HWCV_SCALER_POLYPHASE;
static int camera_id = 0;
static int view_id = 0;
static int output_width = 1920;
static int output_height = 1080;
static bool is_debug = true;
static bool is_display;
static const char *lut_file = "/userdata/8m_1920_1080_gwarp_table.bin";
static int display_width = 1920;
static int display_height = 1080;
static int scaler_width = 1920;
static int scaler_height = 1024;
static const char *output_file = "camera_dewarp_results.bin";


static volatile sig_atomic_t g_run_flag = 1;
static void signal_handler(int sig) { g_run_flag = 0; }

cv::Mat getMatFromNV12(char * buf, int width, int height) {
	cv::Mat yuvImg;
	cv::Mat rgbImg(height, width,  CV_8UC3);
	yuvImg.create(height * 3 / 2, width, CV_8UC1);
	memcpy(yuvImg.data, buf, height * 3 / 2 * width);
	cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_NV12);
	return rgbImg;
}

namespace drive_perception
{
    class MipiCameraServer
    {
        private:

            int ret = 0;
            int lut_fd = 0;
            char *lut_buffer = NULL;
            int lut_len = 0;

            /* for hwcv */
            hwcv_buffer_t input_buffer;
            hwcv_buffer_t output_buffer;
            cv_img_rect_t input_img;
            cv_img_rect_t output_img;
            cv_img_rect_t output_scaler_img;
            hwcv_buffer_t output_scaler_buffer;
            bhwcv::handle_t handle;

            /* for camera */
            bvideo::handle_t cam_handle;
            int camera_dma_fds[CAMERA_BUFFER_NUM];
            view_format_t view_format;
            isp_buf_t cam_buf;
            view_type_t view_type;

            /* for bmpp */
            std::shared_ptr<BmppProxy> BmppImplObj;
            BmppTypes::BmppPacket stream_packet;

            /* for display */
            uint8_t *primary_buf;
            int primary_size;

            struct timespec ts;
            struct timespec te;
            double interval;
            int count = 0;
            // signal(SIGINT, signal_handler);

            bool startCameraIsp()
            {
                /* Open camera */
                printf("Open camera\n");
                cam_handle = isp_open_video(camera_id, ISP_MODE_VIEWS);
                if (!cam_handle) {
                    printf("Open camera failed, camera id = %d\n", camera_id);
                    return -1;
                }

                /* Init camera */
                printf("Init camera\n");
                view_type = (view_id == 0) ? ISP_VIEW0 : ISP_VIEW1;
                if (isp_init_video(cam_handle, view_type, CAMERA_BUFFER_NUM)) {
                    printf("Init cameara failed, camera id = %d\n", camera_id);
                    return -1;
                }

                /* Get camera video buf dmafd */
                printf("Get camera handle\n");
                for (int i = 0; i < CAMERA_BUFFER_NUM; i++) {
                    camera_dma_fds[i] = isp_get_view_buf_fd(cam_handle, i, view_id);
                    printf("camera_buff_fds[%d] = %lu\n", i, camera_dma_fds[i]);
                }

                /* Get camera view format */
                printf("Get camera format\n");
                isp_get_view_format(cam_handle, view_id, &view_format);
                printf("Camera view %d input size: (w,h) = (%d, %d)\n", view_id,
                    view_format.width, view_format.height);

                /* Start Cameara */
                printf("Start capture\n");
                if (isp_start_capture(cam_handle)) {
                    printf("Start camera failed, camera id = %d\n", camera_id);
                    return -1;
                }
                return 1;
            }

            void initHWCV()
            {
                /* Open hwcv device */
                printf("Open hwcv\n");
                ret = bst_hwcv_open(&handle);
                if (ret) {
                    printf("Open hwcv device failed\n");
                    this->isp_exit();
                }

                /* Load lut table */
                if (is_debug) printf("Load lut table\n");
                lut_fd = open(lut_file, O_RDONLY);
                if (lut_fd < 0) {
                    printf("open file %s error\n", lut_file);
                    this->isp_exit();
                }

                lut_len = lseek(lut_fd, 0, SEEK_END);
                lseek(lut_fd, 0, SEEK_SET);
                lut_buffer = (char *)malloc(lut_len);

                ret = read(lut_fd, lut_buffer, lut_len);
                if (ret < 0) {
                    printf("read file %s error\n", lut_file);
                    this->isp_exit();
                }

                ret = close(lut_fd);
                if (ret < 0) {
                    printf("close file %s error\n", lut_file);
                    this->isp_exit();
                }

                ret = bst_hwcv_load_gwarp_table(handle, lut_buffer, lut_len);
                if (ret) {
                    printf("load lut error, ret = %d\n", ret);
                    this->isp_exit();
                }

                free(lut_buffer);
            }

            void initConfig()
            {
                /* Fill img */
                if (is_debug) printf("Fill in/out img\n");
                memset(&input_img, 0, sizeof(input_img));
                memset(&output_img, 0, sizeof(output_img));
                memset(&output_scaler_img, 0, sizeof(output_scaler_img));
                input_img.height = view_format.height;
                input_img.width = view_format.width;
                input_img.pixel_format = HWCV_FMT_NV12;
                output_img.height = output_height;
                output_img.width = output_width;
                output_img.pixel_format = HWCV_FMT_NV12;
                output_scaler_img.height = scaler_height;
                output_scaler_img.width = scaler_width;
                output_scaler_img.pixel_format = HWCV_FMT_NV12;

                /* Alloc output buffer */
                if (is_debug) printf("Alloc output buffer\n");
                memset(&output_buffer, 0, sizeof(hwcv_buffer_t));
                bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1,
                                            NV12_LEN(output_width, output_height),
                                            &output_buffer);
                output_buffer.mem_type = HWCV_MEM_USER;

                memset(&output_scaler_buffer, 0, sizeof(hwcv_buffer_t));
                bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1,
                                            NV12_LEN(scaler_width, scaler_height),
                                            &output_scaler_buffer);
                output_scaler_buffer.mem_type = HWCV_MEM_USER;

                /* Fill input buffer */
                if (is_debug) printf("Fill input buffer\n");
                input_buffer.mem_type = HWCV_MEM_DMA;
                input_buffer.byte_used = NV12_LEN(input_img.width, input_img.height);

                if (is_debug) printf("import buf fds\n");
                ret = bst_hwcv_import_buf_fds(handle, CAMERA_BUFFER_NUM, camera_dma_fds);
                if (ret) {
                    printf("bst_hwcv_import_buf_fds failed, ret = %d\n", ret);
                    this->exit();
                }
            }

            bool initBmpp()
            {
                std::cout << "InitEncodeObj"<< std::endl;
                BmppImplObj = std::make_shared<BmppProxy>();
                BmppTypes::EncoderParam param;
                param.encodeType = BmppTypes::VideoType::MJPEG;
                param.yuvFormat  = BmppTypes::YUVType::YUV420SP;
                param.width      = 1920;
                param.height     = 1024;
                param.frameRate  = 30;

                BmppTypes::ErrorCode error_code;
                BmppImplObj->InitEncoder(param, error_code);
                if (error_code != BmppTypes::ErrorCode::SUCCESS)
                {
                    this->exit();
                    return false;
                }
                return true;
            }
         
        public:
            MipiCameraServer() {
            }

            ~ MipiCameraServer(){
                this->exit();
                this->isp_exit();
            }

            bool initComponent()
            {
                this->startCameraIsp();
                this->initHWCV();
                this->initConfig();
                this->initBmpp();
                return 1;
            }

            void updateOutputFromISP()
            {
                clock_gettime(CLOCK_MONOTONIC, &ts);
                ret = isp_get_frame_buf(cam_handle, &cam_buf, -1);

                // gwarp
                clock_gettime(CLOCK_MONOTONIC, &te);
                interval = (te.tv_sec - ts.tv_sec) +
                        (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
                if (is_debug) printf("Get frame cost %lf s\n", interval);

                if (ret == 0) {
                    input_buffer.buf_index = cam_buf.buf_index;

                    clock_gettime(CLOCK_MONOTONIC, &ts);

                    ret = bst_hwcv_do_warp(handle, gwarp_algo, input_img, output_img, 1,
                                        &input_buffer, 1, &output_buffer);
                    if (ret) {
                        printf("gwarp error, ret = %d\n", ret);
                        this->exit();
                        
                    }

                    clock_gettime(CLOCK_MONOTONIC, &te);
                    interval = (te.tv_sec - ts.tv_sec) +
                            (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
                    if (is_debug) printf("Gwarp cost %lf s\n", interval);

                    // scaler
                    clock_gettime(CLOCK_MONOTONIC, &ts);
                    ret = bst_hwcv_do_scaler(handle, scaler_mode, output_img, output_scaler_img,
                                            1, &output_buffer, 1, &output_scaler_buffer);
                    if (ret) {
                        printf("scaler error, ret = %d\n", ret);
                        this->exit();
                        
                    }
                    clock_gettime(CLOCK_MONOTONIC, &te);
                    interval = (te.tv_sec - ts.tv_sec) +
                            (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
                    if (is_debug) printf("Scaler cost %lf s\n", interval);

                } else if (ret == bvideo::RET_TRYAGAIN || ret == bvideo::RET_TIMEOUT) {
                    printf("Get frame failed, try again, ret = %d\n", ret);
                    
                } else {
                    printf("Get frame error, ret = %d\n", ret);
                    this->exit();
                }
                    
                /* isp put frame buf--- */
                ret = isp_put_frame_buf(cam_handle, &cam_buf);
                if (ret) {
                    printf("isp put frame_buf failed\n");
                    this->exit();
                }
            }
            
            int32_t bmppEncodeBuffer(std::shared_ptr<CompressedImage> msg)
            {
                //bmpp            
                clock_gettime(CLOCK_MONOTONIC, &ts);

                BmppTypes::FrameBuffer frame_buffer;
                frame_buffer.data = (uint8_t *)output_scaler_buffer.uaddr;
                frame_buffer.size = output_scaler_buffer.byte_used;
                std::cout << "byte_used:" << output_scaler_buffer.byte_used << std::endl;
                frame_buffer.flag = 0;
                BmppTypes::ErrorCode error_code;
                BmppImplObj->PutFrameToEncoderByUsrBuff(frame_buffer, error_code);
                // BmppTypes::BmppPacket stream_packet;
                BmppImplObj->GetPacketFromEncoder(stream_packet, error_code);
                
                if (error_code == BmppTypes::ErrorCode::SUCCESS)
                {
                    printf("bmpp success");
                    auto header = msg->mutable_header();

                    header->set_timestamp(apollo::cyber::Time::Now().ToNanosecond());
                    header->set_module_name("cam_node");
                    header->set_version(0);
                    msg->set_format("jpeg");
                    msg->set_data((char *)stream_packet.data,  stream_packet.size);
                    // talker->Write(msg);
                    // memcpy(encoded_data, (char *)stream_packet.data, stream_packet.size);
                    return 1;

                }else if (error_code == BmppTypes::ErrorCode::TERMINATE)
                {
                    printf("bmpp errorcode terminate.");
                    return -1;
                } else if (error_code == BmppTypes::ErrorCode::RETRY)
                {
                    usleep(60000);
                    printf("bmpp errorcode retry.");
                    return -1;
                }else if (error_code == BmppTypes::ErrorCode::MEMORY_ERROR)
                {
                    printf("bmpp errorcode memoryerror.");
                    return -1;
                }else if (error_code == BmppTypes::ErrorCode::INVALID)
                {
                    printf("bmpp errorcode INVALID.");
                    return -1;
                }else if (error_code == BmppTypes::ErrorCode::NULL_PTR)
                {
                    printf("bmpp errorcode NULL_PTR.");
                    return -1;
                }else if (error_code == BmppTypes::ErrorCode::NON_UPDATE)
                {
                    printf("bmpp errorcode NON_UPDATE.");
                    return -1;
                }
            }

            void releaseEncoder()
            {
                BmppTypes::ErrorCode error_code;
                BmppImplObj->ReleasePacketToEncoder(stream_packet, error_code);
            }

            uint8_t* getOutputGwarpScalerBuffer()
            {
                return (uint8_t *)output_scaler_buffer.uaddr;
            }

            size_t getOutputGwarpScalerBufferSize()
            {
                return (size_t)output_scaler_buffer.byte_used;
            }

            cv::Mat getRgbMatByOutputBuffer()
            {
                return getMatFromNV12(reinterpret_cast<char*>(output_scaler_buffer.uaddr), 1920, 1024);
            }

            void exit()
            {
                if (is_debug) printf("Free buffer\n");
                bst_hwcv_free_cv_buffer(handle, &output_buffer);
                bst_hwcv_close(handle);

                if (is_debug) printf("Free bmpp\n");
                BmppTypes::ErrorCode error_code;
                BmppImplObj->DestoryEncoder(error_code);
            }

            int isp_exit()
            {
                /* Stop capture */
                ret = isp_stop_capture(cam_handle);
                if (ret) {
                    printf("isp_stop_capture failed\n");
                }

                ret = isp_close_video(cam_handle);
                if (ret) {
                    printf("isp_close_video failed\n");
                }

                return ret;
            }
    };
}
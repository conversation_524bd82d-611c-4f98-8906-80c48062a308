#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include "bst_hwcv_user_api.h"
#include "hwcv_util.hpp"
#include "buildin.h"
#include "type.h"

using namespace bhwcv;

#define USE_DMA_GWARP_TABLE 1
#define MAX_PLANAR_NUM 3

/* mode: "a" */
static interp_warp_e gwarp_algo = HWCV_INTERPOLATION_BILINEAR;

/* format: "w h f F W H" */
static int input_width;
static int input_height;
static cv_pixel_format_e input_format = HWCV_FMT_RGB888;
static int output_width;
static int output_height;
static cv_pixel_format_e output_format = HWCV_FMT_RGB888;

/* file: "x y z X Y Z" */
static const char* input_file[MAX_PLANAR_NUM] = {
    "input_planar1.bin", "input_planar2.bin", "input_planar3.bin"};
static const char* output_file[MAX_PLANAR_NUM] = {
    "output_planar1.bin", "output_planar2.bin", "output_planar3.bin"};
static const char* lut_file = "lut.bin";

/* ROI and pitch: "r s p q R S P Q" */
static int input_x_offset;
static int input_y_offset;
static int output_x_offset;
static int output_y_offset;
static int input_w_stride;
static int input_h_stride;
static int output_w_stride;
static int output_h_stride;

/* fps test: l m */
static int loop = 1;
static int gloop = 1;

/* debug: L */
static bool is_debug;

/* singal */
static volatile sig_atomic_t g_run_flag = 1;
static void signal_handler(int sig) { g_run_flag = 0; }

static void parse_option(int argc, char* argv[])
{
    int ch;

    if (argc == 1) {
        printf(
            "\n\
        usage: hwcv_gwarp_file_test [options]\n\
        options:\n\
        -a [int]    interpolation algorithm\n\
                    0: bicubic\n\
                    1: bilinear[default]\n\
        -w [int]    input width\n\
        -h [int]    input height\n\
        -f [int]    input format\n\
                    0: RGB888[default]\n\
                    1: RGB888_PLANAR\n\
                    2: YUV422_YUYV\n\
                    3: YUV422_UYVY\n\
                    4: NV12\n\
                    5: NV21\n\
                    6: YUV420_PLANAR\n\
                    7: YUV422_PLANAR\n\
                    8: NV12_PLANAR\n\
                    9: NV21_PLANAR\n\
        -W [int]    output width\n\
        -H [int]    output height\n\
        -F [int]    output format\n\
                    0: RGB888[default]\n\
                    1: RGB888_PLANAR\n\
                    2: YUV422_YUYV\n\
                    3: YUV422_UYVY\n\
                    4: NV12\n\
                    5: NV21\n\
                    6: YUV420_PLANAR\n\
                    7: YUV422_PLANAR\n\
                    8: NV12_PLANAR\n\
                    9: NV21_PLANAR\n\
        -x [string] input planar1 file path\n\
        -y [string] input planar2 file path\n\
        -z [string] input planar3 file path\n\
        -X [string] output planar1 file path\n\
        -Y [string] output planar2 file path\n\
        -Z [string] output planar3 file path\n\
        -r [int]    input x offset\n\
        -s [int]    input y offset\n\
        -p [int]    output x offset\n\
        -q [int]    output y offset\n\
        -R [int]    input w stride\n\
        -S [int]    input h stride\n\
        -P [int]    output w stride\n\
        -Q [int]    output h stride\n\
        -t [string] input lut table file path\n\
        -l [int]    loop cnt, default is 1\n\
        -m [int]    gloop cnt, default is 1\n\
        -L          enable print, default is disable\n");

        exit(1);
    }

    while ((ch = getopt(argc, argv,
                        "a:w:h:f:W:H:F:x:y:z:X:Y:Z:t:r:s:p:q:R:S:P:Q:l:m:L")) != -1) {
        switch (ch) {
            case 'a':
                gwarp_algo = (interp_warp_e)atoi(optarg);
                break;
            case 'w':
                input_width = atoi(optarg);
                break;
            case 'h':
                input_height = atoi(optarg);
                break;
            case 'f':
                input_format = (cv_pixel_format_e)atoi(optarg);
                break;
            case 'W':
                output_width = atoi(optarg);
                break;
            case 'H':
                output_height = atoi(optarg);
                break;
            case 'F':
                output_format = (cv_pixel_format_e)atoi(optarg);
                break;
            case 'x':
                input_file[0] = optarg;
                break;
            case 'y':
                input_file[1] = optarg;
                break;
            case 'z':
                input_file[2] = optarg;
                break;
            case 'X':
                output_file[0] = optarg;
                break;
            case 'Y':
                output_file[1] = optarg;
                break;
            case 'Z':
                output_file[2] = optarg;
                break;
            case 't':
                lut_file = optarg;
                break;
            case 'r':
                input_x_offset = atoi(optarg);
                break;
            case 's':
                input_y_offset = atoi(optarg);
                break;
            case 'p':
                output_x_offset = atoi(optarg);
                break;
            case 'q':
                output_y_offset = atoi(optarg);
                break;
            case 'R':
                input_w_stride = atoi(optarg);
                break;
            case 'S':
                input_h_stride = atoi(optarg);
                break;
            case 'P':
                output_w_stride = atoi(optarg);
                break;
            case 'Q':
                output_h_stride = atoi(optarg);
                break;
            case 'l':
                loop = atoi(optarg);
                break;
            case 'm':
                gloop = atoi(optarg);
                break;
            case 'L':
                is_debug = true;
                break;
            default:
                break;
        }
    }

    printf("============Param list============\n");
    printf("Gwarp Algorithm: ");
    if (gwarp_algo == HWCV_INTERPOLATION_BICUBIC) {
        printf("Bicubic\n");
    } else if (gwarp_algo == HWCV_INTERPOLATION_BILINEAR) {
        printf("Bilinear\n");
    } else {
        printf("\n");
    }

    printf("Input Size: ");
    printf("(w,h) = (%d, %d)\n", input_width, input_height);

    printf("Input Format: ");
    if (input_format == HWCV_FMT_RGB888) {
        printf("RGB888\n");
    } else if (input_format == HWCV_FMT_RGB888_PLANAR) {
        printf("RGB888_PLANAR\n");
    } else if (input_format == HWCV_FMT_YUV422_YUYV) {
        printf("YUV422_YUYV\n");
    } else if (input_format == HWCV_FMT_YUV422_UYVY) {
        printf("YUV422_UYVY\n");
    } else if (input_format == HWCV_FMT_NV12) {
        printf("NV12\n");
    } else if (input_format == HWCV_FMT_NV21) {
        printf("NV21\n");
    } else if (input_format == HWCV_FMT_YUV420_PLANAR) {
        printf("YUV420_PLANAR\n");
    } else if (input_format == HWCV_FMT_YUV422_PLANAR) {
        printf("YUV422_PLANAR\n");
    } else if (input_format == HWCV_FMT_NV12_SEPARATION) {
        printf("NV12_PLANAR\n");
    } else if (input_format == HWCV_FMT_NV21_SEPARATION) {
        printf("NV21_PLANAR\n");
    } else {
        printf("\n");
    }

    printf("Output Size: ");
    printf("(w,h) = (%d, %d)\n", output_width, output_height);

    printf("Output Format: ");
    if (output_format == HWCV_FMT_RGB888) {
        printf("RGB888\n");
    } else if (output_format == HWCV_FMT_RGB888_PLANAR) {
        printf("RGB888_PLANAR\n");
    } else if (output_format == HWCV_FMT_YUV422_YUYV) {
        printf("YUV422_YUYV\n");
    } else if (output_format == HWCV_FMT_YUV422_UYVY) {
        printf("YUV422_UYVY\n");
    } else if (output_format == HWCV_FMT_NV12) {
        printf("NV12\n");
    } else if (output_format == HWCV_FMT_NV21) {
        printf("NV21\n");
    } else if (output_format == HWCV_FMT_YUV420_PLANAR) {
        printf("YUV420_PLANAR\n");
    } else if (output_format == HWCV_FMT_YUV422_PLANAR) {
        printf("YUV422_PLANAR\n");
    } else if (output_format == HWCV_FMT_NV12_SEPARATION) {
        printf("NV12_PLANAR\n");
    } else if (output_format == HWCV_FMT_NV21_SEPARATION) {
        printf("NV21_PLANAR\n");
    } else {
        printf("\n");
    }

    printf("Input Offset: ");
    printf("(x,y) = (%d, %d)\n", input_x_offset, input_y_offset);

    printf("Output Offset: ");
    printf("(x,y) = (%d, %d)\n", output_x_offset, output_y_offset);

    printf("Input Stride: ");
    printf("(w,h) = (%d, %d)\n", input_w_stride, input_h_stride);

    printf("Output Stride: ");
    printf("(w,h) = (%d, %d)\n", output_w_stride, output_h_stride);

    printf("Input Planar 1 file: %s\n", input_file[0]);
    printf("Input Planar 2 file: %s\n", input_file[1]);
    printf("Input Planar 3 file: %s\n", input_file[2]);
    printf("Output Planar 1 file: %s\n", output_file[0]);
    printf("Output Planar 2 file: %s\n", output_file[1]);
    printf("Output Planar 3 file: %s\n", output_file[2]);

    printf("Lut file: %s\n", lut_file);

    printf("Loop: %d\n", loop);
    printf("Gloop: %d\n", gloop);

    printf("Use debug: %d\n", is_debug);
    printf("==================================\n");
}

int main(int argc, char const* argv[])
{
    int ret;
    int fd;
    char* lut_buffer;
    int lut_len;
    handle_t handle;
    hwcv_buffer_t input_buffer[MAX_PLANAR_NUM];
    hwcv_buffer_t output_buffer[MAX_PLANAR_NUM];
    uint64_t dma_in_fd[MAX_PLANAR_NUM];
    uint64_t dma_out_fd[MAX_PLANAR_NUM];
    cv_img_rect_t input_img;
    cv_img_rect_t output_img;
    planar_t input_planar;
    planar_t output_planar;
    struct timespec ts;
    struct timespec te;
    double interval;
    sv_pContext pCtx_in = rcContext();
    sv_pContext pCtx_out = rcContext();
    sv_pContext pCtx_lut = rcContext();
    bst_ion_t bst_ion_in;
    bst_ion_t bst_ion_out;
    bst_ion_t bst_ion_lut;
    void* pIn = nullptr;
    void* pOut = nullptr;
    void* pLut = nullptr;

    signal(SIGINT, signal_handler);

    /* parse option */
    parse_option(argc, (char**)argv);

    if (!input_w_stride) input_w_stride = input_width;
    if (!input_h_stride) input_h_stride = input_height;
    if (!output_w_stride) output_w_stride = output_width;
    if (!output_h_stride) output_h_stride = output_height;

    /* Open device */
    ret = bst_hwcv_open(&handle);
    if (ret) {
        printf("Open hwcv device error\n");
        return -1;
    }

    /* Alloc buffer */
    memset(input_buffer, 0, sizeof(input_buffer));
    memset(output_buffer, 0, sizeof(output_buffer));

    bst_get_format_planar_attr(input_format, input_w_stride, input_h_stride,
                               &input_planar);
    bst_get_format_planar_attr(output_format, output_w_stride, output_h_stride,
                               &output_planar);

    for (int i = 0; i < input_planar.cnt; i++) {
        pIn = cma_allocate(pCtx_in, input_planar.size[i], &bst_ion_in.ion_fd[i],
                           &bst_ion_in.ion_buf_fd[i],
                           &bst_ion_in.buf_phy_addr[i]);
        printf("cma alloc: size: %d, fd: %d, paddr: 0x%x, uaddr: 0x%p\n",
               input_planar.size[i], bst_ion_in.ion_buf_fd[i],
               bst_ion_in.buf_phy_addr[i], pIn);

        dma_in_fd[i] = bst_ion_in.ion_buf_fd[i];
        input_buffer[i].uaddr = pIn;
        input_buffer[i].byte_used = input_planar.size[i];
        memset(input_buffer[i].uaddr, 0, input_planar.size[i]);

        ret = bst_hwcv_import_dmabuf_handles_common(
            handle, 1, &dma_in_fd[i], IN_BUF, (enum IMPORT_DMA_BUF_PLANAR)i,
            (enum IMPORT_DMA_BUF_LAYER)0, 0);
        if (ret) {
            printf("bst_hwcv_import_dmabuf_handles_common\n");
            return -1;
        }
    }

    for (int i = 0; i < output_planar.cnt; i++) {
        pOut = cma_allocate(pCtx_out, output_planar.size[i],
                            &bst_ion_out.ion_fd[i], &bst_ion_out.ion_buf_fd[i],
                            &bst_ion_out.buf_phy_addr[i]);
        printf("cma alloc: size: %d, fd: %d, paddr: 0x%x, uaddr: 0x%p\n",
               output_planar.size[i], bst_ion_out.ion_buf_fd[i],
               bst_ion_out.buf_phy_addr[i], pOut);

        dma_out_fd[i] = bst_ion_out.ion_buf_fd[i];
        output_buffer[i].uaddr = pOut;
        output_buffer[i].byte_used = output_planar.size[i];
        memset(output_buffer[i].uaddr, 0, output_planar.size[i]);

        ret = bst_hwcv_import_dmabuf_handles_common(
            handle, 1, &dma_out_fd[i], OUT_BUF, (enum IMPORT_DMA_BUF_PLANAR)i,
            (enum IMPORT_DMA_BUF_LAYER)0, 0);
        if (ret) {
            printf("bst_hwcv_import_dmabuf_handles_common\n");
            return -1;
        }
    }

    /* Load lut table */
    clock_gettime(CLOCK_MONOTONIC, &ts);

    fd = open(lut_file, O_RDONLY);
    if (fd < 0) {
        printf("open file %s error: %s\n", lut_file, strerror(errno));
        goto exit;
    }

    lut_len = lseek(fd, 0, SEEK_END);
    lseek(fd, 0, SEEK_SET);

#ifdef USE_DMA_GWARP_TABLE
    pLut =
        cma_allocate(pCtx_lut, lut_len, &bst_ion_lut.ion_fd[0],
                     &bst_ion_lut.ion_buf_fd[0], &bst_ion_lut.buf_phy_addr[0]);
    printf("cma alloc: size: %d, fd: %d, paddr: 0x%x, uaddr: 0x%p\n", lut_len,
           bst_ion_lut.ion_buf_fd[0], bst_ion_lut.buf_phy_addr[0], pLut);

    ret = read(fd, pLut, lut_len);
    if (ret < 0) {
        printf("read file %s error: %s\n", lut_file, strerror(errno));
        goto exit;
    }

    ret = bst_hwcv_load_dma_gwarp_table(handle, bst_ion_lut.ion_buf_fd[0]);
    if (ret) {
        printf("load dma lut error, ret = %d\n", ret);
        goto exit;
    }
#else
    lut_buffer = (char*)malloc(lut_len);

    ret = read(fd, lut_buffer, lut_len);
    if (ret < 0) {
        printf("read file %s error: %s\n", lut_file, strerror(errno));
        goto exit;
    }

    ret = bst_hwcv_load_gwarp_table(handle, lut_buffer, lut_len);
    if (ret) {
        printf("load lut error, ret = %d\n", ret);
        goto exit;
    }

    free(lut_buffer);
#endif

    ret = close(fd);
    if (ret < 0) {
        printf("close file %s error: %s\n", lut_file, strerror(errno));
        goto exit;
    }

    clock_gettime(CLOCK_MONOTONIC, &te);
    interval = (te.tv_sec - ts.tv_sec) +
               (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
    if (is_debug) printf("Load table cost %lfs\n", interval);

    /* Gwarp loop */
    while (loop-- && g_run_flag) {
        /* Fill input buffer */
        clock_gettime(CLOCK_MONOTONIC, &ts);

        for (int i = 0; i < input_planar.cnt; i++) {
            fd = open(input_file[i], O_RDONLY);
            if (fd < 0) {
                printf("open file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            ret = read(fd, (char*)input_buffer[i].uaddr, input_planar.size[i]);
            if (ret < 0) {
                printf("read file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }

            ret = close(fd);
            if (ret < 0) {
                printf("close file %s error: %s\n", input_file[i],
                       strerror(errno));
                goto exit;
            }
            input_buffer[i].mem_type = HWCV_MEM_DMA;
            input_buffer[i].buf_index = 0;
        }

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Read cost %lfs\n", interval);

        /* Fill output buffer */
        for (int i = 0; i < input_planar.cnt; i++) {
            output_buffer[i].buf_index = 0;
            output_buffer[i].mem_type = HWCV_MEM_DMA;
        }

        /* Fill img */
        memset(&input_img, 0, sizeof(input_img));
        memset(&output_img, 0, sizeof(output_img));

        input_img.height = input_height;
        input_img.width = input_width;
        input_img.pixel_format = input_format;
        input_img.xoffset = input_x_offset;
        input_img.yoffset = input_y_offset;
        input_img.wstride = input_w_stride;
        input_img.hstride = input_h_stride;

        output_img.height = output_height;
        output_img.width = output_width;
        output_img.pixel_format = output_format;
        output_img.xoffset = output_x_offset;
        output_img.yoffset = output_y_offset;
        output_img.wstride = output_w_stride;
        output_img.hstride = output_h_stride;

        /* Gwarp internal loop */
        int tmp = gloop;
        while (tmp-- && g_run_flag) {
            clock_gettime(CLOCK_MONOTONIC, &ts);

            ret = bst_hwcv_do_warp(handle, gwarp_algo, input_img, output_img,
                                   input_planar.cnt, input_buffer,
                                   output_planar.cnt, output_buffer);
            if (ret) {
                printf("Gwarp error, ret = %d\n", ret);
                goto exit;
            }

            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("Gwarp cost %lfs\n", interval);

            print_fps_per_interval(-1, 1);
        }

        /* save output */
        clock_gettime(CLOCK_MONOTONIC, &ts);

        for (int i = 0; i < output_planar.cnt; i++) {
            if (is_debug)
                printf("Save to file \"%s\", len = %d\n", output_file[i],
                       output_planar.size[i]);
            fd = open(output_file[i], O_CREAT | O_RDWR);
            if (fd < 0) {
                printf("open file %s error: %s\n", output_file[i],
                       strerror(errno));
                goto exit;
            }

            ret =
                write(fd, (char*)output_buffer[i].uaddr, output_planar.size[i]);
            if (ret < 0) {
                printf("write file %s error: %s, uaddr = 0x%p, len = %d\n",
                       output_file[i], strerror(errno), output_buffer[i].uaddr,
                       output_buffer[i].byte_used);
                goto exit;
            }

            ret = close(fd);
            if (ret < 0) {
                printf("close file %s error: %s\n", output_file[i],
                       strerror(errno));
                goto exit;
            }
        }

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Write cost %lfs\n", interval);
    }
    /* free buffer */
exit:
    for (int i = 0; i < input_planar.cnt; i++) {
        cma_deallocate(pCtx_in, &bst_ion_in.ion_buf_fd[i],
                       &bst_ion_in.ion_fd[i], input_buffer[i].byte_used,
                       input_buffer[i].uaddr);
    }

    for (int i = 0; i < output_planar.cnt; i++) {
        cma_deallocate(pCtx_out, &bst_ion_out.ion_buf_fd[i],
                       &bst_ion_out.ion_fd[i], output_buffer[i].byte_used,
                       output_buffer[i].uaddr);
    }

    rcFree(pCtx_in);
    rcFree(pCtx_out);

#ifdef USE_DMA_GWARP_TABLE
    cma_deallocate(pCtx_lut, &bst_ion_lut.ion_buf_fd[0], &bst_ion_lut.ion_fd[0],
                   lut_len, pLut);
    rcFree(pCtx_lut);
#endif

    bst_hwcv_close(handle);

    return 0;
}
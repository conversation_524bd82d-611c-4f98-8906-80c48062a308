#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include "drmshow.h"
#include "bst_hwcv_user_api.h"
#include "bst_isp_sdk_api.h"
#include "hwcv_util.hpp"

using namespace bhwcv;
using namespace bvideo;

#define MAX_PLANAR_CNT 3
#define CAMERA_BUFFER_NUM 3
#define PRINT_FPS_PER_S 30
#define NV12_LEN(width, height) ((width) * (height)*3 / 2)

static interp_warp_e gwarp_algo = HWCV_INTERPOLATION_BILINEAR;
static int camera_id;
static int view_id;
static int output_width;
static int output_height;
static int loop;
static bool is_debug;
static bool is_display;
static bool is_save;
static const char *lut_file;
static int display_width = 1920;
static int display_height = 1080;
static const char *output_file = "camera_dewarp_results.bin";

static volatile sig_atomic_t g_run_flag = 1;
static void signal_handler(int sig) { g_run_flag = 0; }

static void parse_option(int argc, char *argv[])
{
    int ch;

    if (argc == 1) {
        printf(
            "\n\
        usage: hwcv_gwarp_camera_test [options]\n\
        options:\n\
        -a [int]    interpolation algorithm\n\
                    0: bicubic\n\
                    1: bilinear[default]\n\
        -v [int]    view id\n\
                    0: view 0[default]\n\
                    1: view 1\n\
        -i [int]    camera id\n\
                    default is 0\n\
        -W [int]    output width\n\
                    default is 0\n\
        -H [int]    output height\n\
                    default is 0\n\
        -t [string] input lut table file path\n\
        -l [int]    loop cnts\n\
                    default is 0\n\
        -p [int]    is print\n\
                    0: disable[default]\n\
                    1: enable\n\
        -d [int]    is display\n\
                    0: disable[default]\n\
                    1: enable\n\
        -s [int]    is save\n\
                    0: disable[default]\n\
                    1: enable\n\
        -x [int]    display width\n\
                    1920[defaut]\n\
        -y [int]    display height\n\
                    1080[default]\n");
        exit(1);
    }

    while ((ch = getopt(argc, argv, "a:v:i:W:H:t:l:x:y:pds")) != -1) {
        switch (ch) {
            case 'a':
                gwarp_algo = (interp_warp_e)atoi(optarg);
                break;
            case 'v':
                view_id = atoi(optarg);
                break;
            case 'i':
                camera_id = atoi(optarg);
                break;
            case 'W':
                output_width = atoi(optarg);
                break;
            case 'H':
                output_height = atoi(optarg);
                break;
            case 't':
                lut_file = optarg;
                break;
            case 'l':
                loop = atoi(optarg);
                break;
            case 'p':
                is_debug = true;
                break;
            case 'd':
                is_display = true;
                break;
            case 's':
                is_save = true;
                break;
            case 'x':
                display_width = atoi(optarg);
                break;
            case 'y':
                display_height = atoi(optarg);
                break;
            default:
                break;
        }
    }

    printf("============Param list============\n");
    printf("Gwarp Algorithm: ");
    if (gwarp_algo == HWCV_INTERPOLATION_BICUBIC) {
        printf("Bicubic\n");
    } else if (gwarp_algo == HWCV_INTERPOLATION_BILINEAR) {
        printf("Bilinear\n");
    } else {
        printf("\n");
    }

    printf("view id: %d\n", view_id);
    printf("Camera id: %d\n", camera_id);

    printf("Output Size: ");
    printf("(w,h) = (%d, %d)\n", output_width, output_height);

    printf("Display Size: ");
    printf("(w,h) = (%d, %d)\n", display_width, display_height);

    printf("Loop cnt: %d\n", loop);

    printf("Use debug: %d\n", is_debug);
    printf("Use display: %d\n", is_display);
    printf("Use save: %d\n", is_save);
    printf("==================================\n");
}

int main(int argc, char const *argv[])
{
    int ret = 0;
    int save_fd = 0;
    int lut_fd = 0;
    char *lut_buffer = NULL;
    int lut_len = 0;

    /* for hwcv */
    hwcv_buffer_t input_buffer;
    hwcv_buffer_t output_buffer;
    cv_img_rect_t input_img;
    cv_img_rect_t output_img;
    bhwcv::handle_t handle;

    /* for camera */
    bvideo::handle_t cam_handle;
    int camera_dma_fds[CAMERA_BUFFER_NUM];
    view_format_t view_format;
    isp_buf_t cam_buf;
    view_type_t view_type;

    /* for display */
    uint8_t *primary_buf;
    int primary_size;

    struct timespec ts;
    struct timespec te;
    double interval;

    signal(SIGINT, signal_handler);

    /* parse option */
    parse_option(argc, (char **)argv);

    /* Open camera */
    if (is_debug) printf("Open camera\n");
    cam_handle = isp_open_video(camera_id, ISP_MODE_VIEWS);
    if (!cam_handle) {
        printf("Open camera failed, camera id = %d\n", camera_id);
        return -1;
    }

    /* Init camera */
    if (is_debug) printf("Init camera\n");
    view_type = (view_id == 0) ? ISP_VIEW0 : ISP_VIEW1;
    if (isp_init_video(cam_handle, view_type, CAMERA_BUFFER_NUM)) {
        printf("Init cameara failed, camera id = %d\n", camera_id);
        return -1;
    }

    /* Get camera video buf dmafd */
    if (is_debug) printf("Get camera handle\n");
    for (int i = 0; i < CAMERA_BUFFER_NUM; i++) {
        camera_dma_fds[i] = isp_get_view_buf_fd(cam_handle, i, view_id);
        if (is_debug)
            printf("camera_buff_fds[%d] = %lu\n", i, camera_dma_fds[i]);
    }

    /* Get camera view format */
    if (is_debug) printf("Get camera format\n");
    isp_get_view_format(cam_handle, view_id, &view_format);
    printf("Camera view %d input size: (w,h) = (%d, %d)\n", view_id,
           view_format.width, view_format.height);

    /* Start Cameara */
    if (is_debug) printf("Start capture\n");
    if (isp_start_capture(cam_handle)) {
        printf("Start camera failed, camera id = %d\n", camera_id);
        return -1;
    }

    /* Create display */
    if (is_display) {
        display_init_by_internal(display_width, display_height);
    }

    /*  Create Save fd */
    if (is_save) {
        if (is_debug) printf("Save output to %s\n", output_file);
        save_fd = open(output_file, O_RDWR | O_TRUNC | O_CREAT | O_APPEND);
        if (save_fd < 0) {
            printf("Open %s error: %s\n", output_file, strerror(errno));
            goto vout_isp_exit;
        }
    }

    /* Open hwcv device */
    if (is_debug) printf("Open hwcv\n");

    ret = bst_hwcv_open(&handle);
    if (ret) {
        printf("Open hwcv device failed\n");
        goto vout_isp_exit;
    }

    /* Load lut table */
    if (is_debug) printf("Load lut table\n");
    lut_fd = open(lut_file, O_RDONLY);
    if (lut_fd < 0) {
        printf("open file %s error\n", lut_file);
        goto vout_isp_exit;
    }

    lut_len = lseek(lut_fd, 0, SEEK_END);
    lseek(lut_fd, 0, SEEK_SET);
    lut_buffer = (char *)malloc(lut_len);

    ret = read(lut_fd, lut_buffer, lut_len);
    if (ret < 0) {
        printf("read file %s error\n", lut_file);
        goto vout_isp_exit;
    }

    ret = close(lut_fd);
    if (ret < 0) {
        printf("close file %s error\n", lut_file);
        goto vout_isp_exit;
    }

    ret = bst_hwcv_load_gwarp_table(handle, lut_buffer, lut_len);
    if (ret) {
        printf("load lut error, ret = %d\n", ret);
        goto vout_isp_exit;
    }

    free(lut_buffer);

    /* Fill img */
    if (is_debug) printf("Fill in/out img\n");
    memset(&input_img, 0, sizeof(input_img));
    memset(&output_img, 0, sizeof(output_img));
    input_img.height = view_format.height;
    input_img.width = view_format.width;
    input_img.pixel_format = HWCV_FMT_NV12;
    output_img.height = output_height;
    output_img.width = output_width;
    output_img.pixel_format = HWCV_FMT_NV12;

    /* Alloc output buffer */
    if (is_debug) printf("Alloc output buffer\n");
    memset(&output_buffer, 0, sizeof(hwcv_buffer_t));
    bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1,
                                NV12_LEN(output_width, output_height),
                                &output_buffer);
    output_buffer.mem_type = HWCV_MEM_USER;

    /* Fill input buffer */
    if (is_debug) printf("Fill input buffer\n");
    input_buffer.mem_type = HWCV_MEM_DMA;
    input_buffer.byte_used = NV12_LEN(input_img.width, input_img.height);

    if (is_debug) printf("import buf fds\n");
    ret = bst_hwcv_import_buf_fds(handle, CAMERA_BUFFER_NUM, camera_dma_fds);
    if (ret) {
        printf("bst_hwcv_import_buf_fds failed, ret = %d\n", ret);
        goto exit;
    }

    /* Gwarp Loop */
    while (loop-- && g_run_flag) {
        clock_gettime(CLOCK_MONOTONIC, &ts);
        ret = isp_get_frame_buf(cam_handle, &cam_buf, -1);

        clock_gettime(CLOCK_MONOTONIC, &te);
        interval = (te.tv_sec - ts.tv_sec) +
                   (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
        if (is_debug) printf("Get frame cost %lf s\n", interval);

        if (ret == 0) {
            input_buffer.buf_index = cam_buf.buf_index;

            clock_gettime(CLOCK_MONOTONIC, &ts);

            ret = bst_hwcv_do_warp(handle, gwarp_algo, input_img, output_img, 1,
                                   &input_buffer, 1, &output_buffer);
            if (ret) {
                printf("gwarp error, ret = %d\n", ret);
                goto exit;
            }

            clock_gettime(CLOCK_MONOTONIC, &te);
            interval = (te.tv_sec - ts.tv_sec) +
                       (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
            if (is_debug) printf("Gwarp cost %lf s\n", interval);
            if (is_save) {
                /* save output */
                clock_gettime(CLOCK_MONOTONIC, &ts);

                ret = write(save_fd, (char *)output_buffer.uaddr,
                            output_buffer.byte_used);
                if (ret < 0) {
                    printf("write file error:%s, uaddr = 0x%p, len = %d\n",
                           strerror(errno), output_buffer.uaddr,
                           output_buffer.byte_used);
                    goto exit;
                }

                clock_gettime(CLOCK_MONOTONIC, &te);
                interval =
                    (te.tv_sec - ts.tv_sec) +
                    (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
                if (is_debug) printf("Write cost %lf s\n", interval);
            }

            if (is_display) {
                clock_gettime(CLOCK_MONOTONIC, &ts);

                get_primary_plane_next_buf(&primary_buf, &primary_size);
                memset(primary_buf, 128, primary_size);
                nv12img_relocate((uint8_t *)output_buffer.uaddr, output_width,
                                 output_height, primary_buf, display_width,
                                 display_height, 0, 0);
                display_commit_by_buf(primary_buf, NULL);

                clock_gettime(CLOCK_MONOTONIC, &te);
                interval =
                    (te.tv_sec - ts.tv_sec) +
                    (double)(te.tv_nsec - ts.tv_nsec) / double(1000000000);
                if (is_debug) printf("Display cost %lf s\n", interval);
            }
        } else if (ret == bvideo::RET_TRYAGAIN || ret == bvideo::RET_TIMEOUT) {
            printf("Get frame failed, try again, ret = %d\n", ret);
            continue;
        } else {
            printf("Get frame error, ret = %d\n", ret);
            goto exit;
        }

        /* isp put frame buf--- */
        ret = isp_put_frame_buf(cam_handle, &cam_buf);
        if (ret) {
            printf("isp put frame_buf failed\n");
            goto exit;
        }

        /* printf fps */
        print_fps_per_interval(camera_id, PRINT_FPS_PER_S);
    }
exit:
    if (is_debug) printf("Free buffer\n");
    bst_hwcv_free_cv_buffer(handle, &output_buffer);
    bst_hwcv_close(handle);

    if (is_save) {
        ret = close(save_fd);
        if (ret < 0) {
            printf("close %s error: %s\n", output_file, strerror(errno));
        }
    }

vout_isp_exit:
    if (is_display) {
        display_deinit();
    }

isp_exit:
    /* Stop capture */
    ret = isp_stop_capture(cam_handle);
    if (ret) {
        printf("isp_stop_capture failed\n");
    }

    ret = isp_close_video(cam_handle);
    if (ret) {
        printf("isp_close_video failed\n");
    }

    return ret;
}
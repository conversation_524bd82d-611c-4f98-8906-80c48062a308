#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <errno.h>

#include "bst_hwcv_user_api.h"
#include "hwcv_util.hpp"

/* This case only shows 4 1080p nv12 images input */
using namespace bhwcv;

int main(int argc, char const *argv[])
{
    int fd;
    int ret;
    char name[20];
    handle_t handle;
    cv_img_rect_t input_img;
    cv_img_rect_t output_img;
    hwcv_buffer_t input_buffer;
    hwcv_buffer_t output_buffer;

    memset(&input_img, 0, sizeof(input_img));
    memset(&output_img, 0, sizeof(output_img));
    memset(&input_buffer, 0, sizeof(input_buffer));
    memset(&output_buffer, 0, sizeof(output_buffer));

    ret = bst_hwcv_open(&handle);
    if (ret) {
        printf("Open hwcv device error\n");
        return -1;
    }

    ret = bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1, 1920 * 1080 * 3 / 2, &input_buffer);
    if (ret) {
        printf("bst_hwcv_request_cv_buffers failed, ret = %d\n", ret);
        return ret;
    }

    ret = bst_hwcv_request_cv_buffers(handle, HWCV_MEM_USER, 1, 1280 * 720 * 3 * 4 / 2, &output_buffer);
    if (ret) {
        printf("bst_hwcv_request_cv_buffers failed, ret = %d\n", ret);
        return ret;
    }

    input_img.width = 1920;
    input_img.height = 1080;
    input_img.pixel_format = HWCV_FMT_NV12;
    input_img.xoffset = 0;
    input_img.yoffset = 0;
    input_img.wstride = 1920;
    input_img.hstride = 1080;

    output_img.width = 1280;
    output_img.height = 720;
    output_img.pixel_format = HWCV_FMT_NV12;
    output_img.wstride = 1280 * 2;
    output_img.hstride = 720 * 2;

    input_buffer.mem_type = HWCV_MEM_USER;
    output_buffer.mem_type = HWCV_MEM_USER;

    for (int i = 0; i < 4; i++) {
        output_img.xoffset = 1280 * (i % 2);
        output_img.yoffset = 720 * (i / 2);

        snprintf(name, sizeof(name), "%d.bin", (i+1));
        fd = open(name, O_RDONLY);
        if (fd < 0) {
            printf("open file %s error: %s\n", name, strerror(errno));
            return -1;
        }

        ret = read(fd, (char *)input_buffer.uaddr, 1920 * 1080 * 3 / 2);
        if (ret < 0) {
            printf("read file %s error: %s\n", name, strerror(errno));
            return -1;
        }

        ret = close(fd);
        if (ret < 0) {
            printf("close file %s error: %s\n", name, strerror(errno));
            return -1;
        }

        ret = bst_hwcv_do_scaler(
            handle, HWCV_SCALER_POLYPHASE,
            input_img, output_img, 
            1, &input_buffer,
            1, &output_buffer);
        if (ret) {
            printf("Scaler error, ret = %d\n", ret);
            return -1;
        }
    }

    fd = open("result.bin", O_CREAT | O_RDWR);
    if (fd < 0) {
        printf("open file %s error: %s\n", "result.bin", strerror(errno));
        return -1;
    }

    ret = write(fd, (char *)output_buffer.uaddr, 1280 * 720 * 3 * 4 / 2);
    if (ret < 0) {
        printf("write file %s error: %s\n", "result.bin", strerror(errno));
        return -1;
    }

    ret = close(fd);
    if (ret < 0) {
        printf("close file %s error: %s\n", "result.bin", strerror(errno));
        return -1;
    }

    bst_hwcv_free_cv_buffer(handle, &input_buffer);
    bst_hwcv_free_cv_buffer(handle, &output_buffer);
}
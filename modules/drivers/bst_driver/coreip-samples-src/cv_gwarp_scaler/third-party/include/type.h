
/**********************************************************
 * A standard copyright header for the RTL code and software 
 * code to protect companies¡¯ technology

 * This file contains proprietary information that is the sole 
 * intellectual property of Black Sesame Technologies, Inc. and 
 * its affiliates.? No portions of this material may be reproduced 
 * in any form without the written permission of:

 * Black Sesame Technologies, Inc. and its affiliates
 * 2255 Martin Ave. Suite D
 * Santa Clara, CA? 95050
 * Copyright @2016: all right reserved.
 * *********************************************************
*/

#ifndef TYPE_H
#define TYPE_H

#include <time.h>

typedef struct bst_ion{
    uint32_t len[16];
    int ion_fd[16];
    int ion_buf_fd[16]; 
    int long buf_phy_addr[16];
}bst_ion_t;

long long pTime_driver[8];
long long *pTime_host;
int time_cnt_core[4];   
int time_cnt;                             

#endif

#ifndef  RCALL_ADAPTOR_H
#define  RCALL_ADAPTOR_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define ALIGN_128(X) (((X + 127) >> 7) << 7)

typedef enum {
    SV_BUFSTAT_READY = 0,
    SV_BUFSTAT_WAITING
}sv_eBuferStat;


typedef enum svPackFormat
{
    SV_FMT_RGB888 = 0,    /* Scaler: AXI pitch: 192 */
    SV_FMT_RGB888_planar, /* Scaler: AXI pitch: 64, */
    SV_FMT_YUV422_YUYV,   /* Scaler: AXI pitch: 128, */
    SV_FMT_YUV422_UYVY,   /* Scaler: AXI pitch: 128, */
    SV_FMT_NV12,          /* Scaler: AXI pitch:  : 64, */
    SV_FMT_NV21,          /* Scaler: AXI pitch:  : 64, */
    SV_FMT_YUV420_planar, /* Scaler: AXI pitch:  : 64, */
    SV_FMT_YUV422_planar, /* Scaler: AXI pitch:  : 64 */
}sv_ePackFmt;

typedef enum {
  SV_ST_FUNCNON = -2,
	SV_ST_FAIL = -1,
	SV_ST_SUCCESS = 0,
}svRcallSt;

typedef enum svStatus
{
    SV_STAT_SUCCEED,
    SV_STAT_FAILURE,
    SV_STAT_PENDING,
}sv_eStatus;


typedef void* svHandler;

typedef struct svContextStruct {
    /*! \brief The abstact root handler pointer */
    svHandler      device;      /*! shared memory io device */
    svHandler      queue;       /*! message queue for host-device command */

    svHandler      shmem_base;  /*! shared memory pool, a linked table */
    svHandler      shmem_last;
    int            shmem_num;
    int            shmem_size;

    svHandler      pFuncs;
	svHandler      pLastFunc;

}sv_Context, *sv_pContext;

typedef struct svImageStruct
{
    int32_t  frameWidth;
    int32_t  frameHeight;
    int32_t  framePitch;
    sv_ePackFmt  pixelPackFormat;
    uint32_t img_addr[4];
} sv_image, *sv_pImage;

typedef struct svLUTStruct
{
    uint32_t data_len;        /* byte length */
    uint32_t data_addr_phy;   /* physical address for binary data */
    uint32_t data_stride;        /* byte stride */
}sv_bin, *sv_pBin;


/** @brief creat RCALL context     

    @return         pointer of RCALL context
  */
sv_pContext rcContext(void);

/** @brief free RCALL resource

    @param[in]      pCtx    	pointer of RCALL context
    @return         status
	  @retval SV_STAT_SUCCEED = success
	  @retval SV_STAT_FAILURE = fail
  */
sv_eStatus rcFree(sv_pContext pCtx);

/** @brief creat function context

    @param[in]      pCtx    	pointer of RCALL context
    @return         handler of function context
  */
svHandler rcCreateFuncCtx(sv_pContext pCtx);

/** @brief add uint32_t member to argument list

    @param[in]      pFunc_    	handler of function context
    @param[in]      val    	    value of uint32_t member
    @param[in]      prevArg    	handler of previous argument list
    @return         handler of argument list
  */
svHandler rcAddOneArgu_uint32(svHandler pFunc_, uint32_t val, svHandler prevArg);

/** @brief add int32_t member to argument list

    @param[in]      pFunc_    	handler of function context
    @param[in]      val    	    value of int32_t member
    @param[in]      prevArg    	handler of previous argument list
    @return         handler of argument list
  */
svHandler rcAddOneArgu_int32(svHandler pFunc_, int32_t val, svHandler prevArg);

/** @brief add sv_pBin member to argument list

    @param[in]      pFunc_    	handler of function context
    @param[in]      pBin    	pointer of sv_pBin member
    @param[in]      prevArg    	handler of previous argument list
    @return         handler of argument list
  */
svHandler rcAddOneArgu_binary(svHandler pFunc_, sv_pBin pBin, svHandler prevArg);

/** @brief add sv_pImage member to argument list

    @param[in]      pFunc_    	handler of function context
    @param[in]      pImg    	pointer of sv_pImage member
    @param[in]      prevArg    	handler of previous argument list
    @return         handler of argument list
  */
svHandler rcAddOneArgu_image(svHandler pFunc_, sv_pImage pImg, svHandler prevArg);

/** @brief get physical address of argument list header

    @param[in]      pFunc_    	handler of function context
    @return         physical address of argument list header
  */
uint32_t rcGetFuncArg_phy(svHandler pFunc_);

/** @brief pack argument list

    @param[in]      pFunc_    	handler of function context
    @param[in]      arg_phy    	physical address of argument list header
    @param[in]      pArgTop    	handler of argument list header
    @param[in]      pArgBot    	handler of argument list tail
    @return         
  */
void rcSetFuncArg_phy(svHandler pFunc_, uint32_t arg_phy, svHandler pArgTop, svHandler pArgBot);

/** @brief  allocate RCALL memory

    @param[in]      pCtx    	pointer of RCALL context
    @param[in]      size    	Size of the memory
    @return         handler of RCALL memory
  */
svHandler rcAllocShmem(sv_pContext pCtx, uint32_t size);

/** @brief get address of RCALL memory

    @param[in]      pShmem    	handler of RCALL memory
    @return         address of RCALL memory
  */
void* rcGetShmemPtr(svHandler pShmem);

/** @brief get physical address of RCALL memory

    @param[in]      pShmem    	handler of RCALL memory
    @return         physical address of RCALL memory
  */
uint32_t rcGetShmemAddrPhy(svHandler pShmem);

/** @brief get the handler of argument list member(binary/image)

    @param[in]      pFunc    	handler of function context
    @param[in]      n_arg    	order of argument list (start from 0)
    @param[in]      argc    	count of argument list
    @return         handler of argument list member(binary/image)
  */
svHandler rcGetArgPtrBuf(svHandler pFunc, int n_arg, int argc);

/** @brief update the physical address of binary member in argument list

    @param[in]      ptr    	    handler of argument list member(binary)
    @param[in]      pData_phy   physical address of binary
    @return         status
	  @retval SV_STAT_SUCCEED = success
	  @retval SV_STAT_FAILURE = fail
  */
sv_eStatus rcUpdateBinAddr(svHandler ptr, uint32_t pData_phy);

/** @brief update the physical address of image member in argument list

    @param[in]      ptr    	    handler of argument list member(image)
    @param[in]      pData_phy   physical address of image plane
    @return         status
	  @retval SV_STAT_SUCCEED = success
	  @retval SV_STAT_FAILURE = fail
  */
sv_eStatus rcUpdateImageAddr(svHandler ptr, uint32_t pData_phy[4]);

/** @brief get handler of function command

    @param[in]      pFunc    	handler of function context
    @return         handler of function command
  */
svHandler rcGetFuncCmd(svHandler pFunc);

/** @brief update the physical address of image member in argument list

    @param[in]      func_id    	function id
    @param[in]      argc    	count of argument list
    @param[in]      args_phy    physical address of argument list header
    @param[in]      _pCmd    	handler of function command
    @return         status
	  @retval SV_STAT_SUCCEED = success
	  @retval SV_STAT_FAILURE = fail
  */
sv_eStatus rcCreateCmd(int func_id, int argc, uint32_t args_phy, svHandler _pCmd);

/** @brief update the physical address of image member in argument list

    @param[in]          pFunc_    	handler of function context
    @param[in]          pCmd_    	handler of function command
    @param[in,out]      out         return value of DSP
    @return             status
	  @retval SV_STAT_SUCCEED = success
	  @retval SV_STAT_FAILURE = fail
  */
sv_eStatus rcExecFunc(svHandler pFunc_, svHandler pCmd_, uint32_t *out);

long long get_time_stamp();

void WriteMailBox(uint32_t value);

sv_eStatus rcExecFunc_coreAffinity(svHandler pFunc_, svHandler pCmd_, uint32_t *out, int8_t coreID);

void updateImageBuffer_64align(sv_pImage img, void *pData, const char *path, int src_pitch);
void color_convert_init(sv_pImage pImgIn, sv_pImage pImgOut, int thr_num);
void SetImageBufferAddr_thr(sv_pContext pCtx, sv_pImage pImgIn, sv_pImage pImgOut, int thr_num);
void rccolor_convert_create_thr(sv_pContext pCtx, int thr_num);
int rccolor_convert_opt(int thr_num);
svHandler rcIntergral_create(sv_pContext pCtx, sv_pImage pImgIn, sv_pImage pImgOut);
sv_eStatus rcIntergral_coreAffinity(svHandler pFunc, sv_pImage src, sv_pImage dst, int8_t coreID);
svHandler rcgamma_create(sv_pContext pCtx, sv_pImage pImgIn, sv_pImage pImgOut);
sv_eStatus rcgamma_coreAffinity(svHandler pFunc, sv_pImage src, sv_pImage dst, int8_t coreID);
svHandler rcIntergral_Gamma_create(sv_pContext pCtx, sv_pImage pImgIn, sv_pImage pImgOut, sv_pImage pImgOut_gm);
sv_eStatus rcIntergral_Gamma(svHandler pFunc, sv_pImage src, sv_pImage dst, sv_pImage dst_gm);

#ifdef __cplusplus
}
#endif

#endif


/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <iostream>
#include "drmshow.h"
// #include "time_utils.h"

#define SCREEN_WIDTH 1920
#define SCREEN_HEIGHT 1080

#define IMG_NV12_SIZE (SCREEN_WIDTH * SCREEN_HEIGHT * 3 / 2)
#define IMG_ARGB_SIZE (SCREEN_WIDTH * SCREEN_HEIGHT * 4)

static unsigned char imgnv12[IMG_NV12_SIZE];
static unsigned char imgargb[IMG_ARGB_SIZE];

static int ter = 0;
static void sigint_handler(int arg) { ter = 1; }

const int max_file_name = 26;

struct cmd_config_t {
    bool single_layer = false;
    bool single_osd_layer = false;

    bool if_primary = false;
    bool if_osd = false;
    char primary_name[max_file_name];
    char osd_name[max_file_name];
};

void process_image(const char *nv12path, const char *argbpath)
{
    int file_size;
    int buf_count;
    int nbuf_count;
    int ret;
    int real_count = 0;
    int i;
    uint8_t *osd_buf;
    int osd_size;
    uint8_t *primary_buf;
    int primary_size;

    FILE *fp = fopen(argbpath, "r");
    if (fp == NULL) {
        printf("ERROR! Unable to open argb video\n");
        return;
    }
    fread(imgargb, IMG_ARGB_SIZE, 1, fp);
    fclose(fp);

    fp = fopen(nv12path, "r");
    if (fp == NULL) {
        printf("ERROR! Unable to open nv12 video\n");
        return;
    }

    //循环每一帧读取显示
    while (!ter) {
        if (feof(fp)) rewind(fp);
        ret = fread(imgnv12, IMG_NV12_SIZE, 1, fp);
        if (ret <= 0) continue;

        get_osd_plane_next_buf(&osd_buf, &osd_size);
        memcpy(osd_buf, imgargb, osd_size);

        get_primary_plane_next_buf(&primary_buf, &primary_size);
        memcpy(primary_buf, imgnv12, primary_size);
        display_commit_by_buf(primary_buf, osd_buf);
    }
    fclose(fp);
}

void primary_nv12_set(uint8_t *primary_nv12_buf, int width, int height)
{
    uint8_t red_y = 255 * 0.299;
    uint8_t red_u = 255 * -0.1687 + 128;
    uint8_t red_v = 255 * 0.5 + 128;
    uint8_t green_y = 255 * 0.587;
    uint8_t green_u = 255 * -0.3313 + 128;
    uint8_t green_v = 255 * -0.4187 + 128;
    uint8_t blue_y = 255 * 0.114;
    uint8_t blue_u = 255 * 0.5 + 128;
    uint8_t blue_v = 255 * -0.0813 + 128;

    for (int i = 0; i < 12; i++) {
        if (i % 3 == 0) {
            for (int j = 0; j < 12; j++) {
                memset((primary_nv12_buf + (i * (height / 12)) * width), red_y,
                       width * (height / 12));
            }
        } else if (i % 3 == 1) {
            for (int j = 0; j < 12; j++) {
                memset((primary_nv12_buf + (i * (height / 12)) * width),
                       green_y, width * (height / 12));
            }
        } else {
            for (int j = 0; j < 12; j++) {
                memset((primary_nv12_buf + (i * (height / 12)) * width), blue_y,
                       width * (height / 12));
            }
        }
    }

    uint8_t *nv12_uv;
    nv12_uv = &primary_nv12_buf[width * height];
    for (int i = 0; i < 12; i++) {
        if (i % 3 == 0) {
            for (int h = 0; h < (height / 12 / 2); h++) {
                for (int j = 0; j < width; j += 2) {
                    nv12_uv[(i * (height / 12 / 2) + h) * width + j] = red_u;
                    nv12_uv[(i * (height / 12 / 2) + h) * width + j + 1] =
                        red_v;
                }
            }
        } else if (i % 3 == 1) {
            for (int h = 0; h < (height / 12 / 2); h++) {
                for (int j = 0; j < width; j += 2) {
                    nv12_uv[(i * (height / 12 / 2) + h) * width + j] = green_u;
                    nv12_uv[(i * (height / 12 / 2) + h) * width + j + 1] =
                        green_v;
                }
            }
        } else {
            for (int h = 0; h < (height / 12 / 2); h++) {
                for (int j = 0; j < width; j += 2) {
                    nv12_uv[(i * (height / 12 / 2) + h) * width + j] = blue_u;
                    nv12_uv[(i * (height / 12 / 2) + h) * width + j + 1] =
                        blue_v;
                }
            }
        }
    }
}

void osd_set_purple(uint8_t *argb_start_buf, int lane_size)
{
    for (int i = 0; i < lane_size * 4; i++) {
        argb_start_buf[i] = 128;
        argb_start_buf[i + 1] = 255;
        argb_start_buf[i + 2] = 0;
        argb_start_buf[i + 3] = 255;
    }
}

void osd_set_red_line(uint8_t *argb_start_buf, int lane_size)
{
    for (int i = 0; i < lane_size * 4; i++) {
        argb_start_buf[i] = 0;
        argb_start_buf[i + 1] = 255;
        argb_start_buf[i + 2] = 0;
        argb_start_buf[i + 3] = 0;
    }
}

void osd_set_green_line(uint8_t *argb_start_buf, int lane_size)
{
    for (int i = 0; i < lane_size * 4; i++) {
        argb_start_buf[i] = 0;
        argb_start_buf[i + 1] = 0;
        argb_start_buf[i + 2] = 255;
        argb_start_buf[i + 3] = 0;
    }
}

void osd_set_blue_line(uint8_t *argb_start_buf, int lane_size)
{
    for (int i = 0; i < lane_size * 4; i++) {
        argb_start_buf[i] = 0;
        argb_start_buf[i + 1] = 0;
        argb_start_buf[i + 2] = 0;
        argb_start_buf[i + 3] = 255;
    }
}

void set_osd_single_layer(uint8_t *primary_argb_buf, int width, int height)
{
    // uint8_t *pdest;
    for (int i = 0; i < height / 12; i++) {
        if (i % 3 == 0) {
            for (int j = 0; j < 12; j++) {
                osd_set_blue_line(primary_argb_buf + (i * 12 + j) * width,
                                  width);
            }
        } else if (i % 3 == 1) {
            for (int j = 0; j < 12; j++) {
                osd_set_green_line(primary_argb_buf + (i * 12 + j) * width,
                                   width);
            }
        } else {
            for (int j = 0; j < 12; j++) {
                osd_set_red_line(primary_argb_buf + (i * 12 + j) * width,
                                 width);
            }
        }
    }
}

void primary_argb_set(uint8_t *primary_argb_buf, int rgb_size)
{
    memset(primary_argb_buf, 0, rgb_size);
    uint8_t *pdest;

    for (int i = 0; i < 240; i++) {
        pdest = &primary_argb_buf[220 * 4 + (4 * 1920 * (140 + i))];
        osd_set_purple(pdest, 360);
    }
    for (int i = 0; i < 540; i++) {
        pdest = &primary_argb_buf[1420 * 4 + (4 * 1920 * (100 + i))];
        osd_set_purple(pdest, 400);
    }
    for (int i = 0; i < 256; i++) {
        pdest = &primary_argb_buf[900 * 4 + (4 * 1920 * (330 + i))];
        osd_set_purple(pdest, 360);
    }
    for (int i = 0; i < 360; i++) {
        pdest = &primary_argb_buf[420 * 4 + (4 * 1920 * (660 + i))];
        osd_set_purple(pdest, 420);
    }
    for (int i = 0; i < 280; i++) {
        pdest = &primary_argb_buf[1300 * 4 + (4 * 1920 * (780 + i))];
        osd_set_purple(pdest, 480);
    }
}

// static uint64_t getTick()
// {
//     struct timeval tv;
//     gettimeofday(&tv, NULL);
//     return (tv.tv_sec * 1000 + tv.tv_usec / 1000);
// }

// float fps()
// {
//     static uint64_t lastTime = getTick();  // ms
//     static int frameCount = 0;
//     float fps = 0;

//     ++frameCount;
//     uint64_t curTime = getTick();
//     if (curTime - lastTime > 10 * 1000)  // inteval 10s
//     {
//         fps = (float)(frameCount * 1000) / (curTime - lastTime);
//         frameCount = 0;
//         lastTime = curTime;

//         printf("Performance:: fps = %0.1f\n", fps);

//         return fps;
//     }
//     return 0;
// }

void non_image_process(cmd_config_t cmd_parse)
{
    int nv12_size;
    int rgb_size;
    uint8_t *primary_nv12_buf = new uint8_t[1920 * 1080 * 3 / 2];
    uint8_t *primary_argb_buf = new uint8_t[1920 * 1080 * 4];
    while (!ter) {
        get_osd_plane_next_buf(&primary_argb_buf, &rgb_size);
        get_primary_plane_next_buf(&primary_nv12_buf, &nv12_size);
        // memset(primary_rgb_buf, 0, rgb_size);
        // std::cout << "rgb_size = " << rgb_size << ", nv12_size = " <<
        // nv12_size
        // << std::endl;
        if (cmd_parse.single_layer == false) {
            primary_argb_set(primary_argb_buf, rgb_size);
            primary_nv12_set(primary_nv12_buf, 1920, 1080);
        } else {
            if (cmd_parse.single_osd_layer == false) {
                memset(primary_argb_buf, 0, rgb_size);
                primary_nv12_set(primary_nv12_buf, 1920, 1080);
            } else {
                set_osd_single_layer(primary_argb_buf, 1920, 1080);
                memset(primary_nv12_buf, 0, nv12_size);
            }
        }
        display_commit_by_buf(primary_nv12_buf, primary_argb_buf);
    }
}

void half_image_process(const char *nv12path)
{
    FILE *fp = fopen(nv12path, "r");
    if (fp == NULL) {
        printf("ERROR! Unable to open nv12 video\n");
        return;
    }
    int nv12_size;
    int rgb_size;
    uint8_t *primary_nv12_buf = new uint8_t[1920 * 1080 * 3 / 2];
    uint8_t *primary_argb_buf = new uint8_t[1920 * 1080 * 4];
    while (!ter) {
        if (feof(fp)) rewind(fp);
        int ret = fread(imgnv12, IMG_NV12_SIZE, 1, fp);
        if (ret <= 0) continue;
        get_osd_plane_next_buf(&primary_argb_buf, &rgb_size);
        get_primary_plane_next_buf(&primary_nv12_buf, &nv12_size);
        // memset(primary_rgb_buf, 0, rgb_size);
        primary_argb_set(primary_argb_buf, rgb_size);
        memcpy(primary_nv12_buf, imgnv12, nv12_size);
        display_commit_by_buf(primary_nv12_buf, primary_argb_buf);
    }
}

void main_process(cmd_config_t cmd_parse)
{
    if (cmd_parse.if_primary == false) {
        if (cmd_parse.if_osd == false) {
            non_image_process(cmd_parse);
        } else {
            std::cout << "wrong argument input!\n";
            return;
        }
    } else {
        if (cmd_parse.if_osd == false) {
            half_image_process(cmd_parse.primary_name);
        } else {
            process_image(cmd_parse.primary_name, cmd_parse.osd_name);
        }
    }
}

int main(int argc, char **argv)
{
    // if (argc < 3) {
    //     printf("drmtest %dx%d usage: ./drmtest_1080p nv12file argbfile\n",
    //     SCREEN_WIDTH, SCREEN_HEIGHT); return 0;
    // }

    int opt;
    int select;

    cmd_config_t cmd_parse;

    while ((opt = getopt(argc, argv, "p:o:s:")) != -1) {
        switch (opt) {
            case 'p':
                memcpy(cmd_parse.primary_name, optarg, max_file_name - 1);
                cmd_parse.if_primary = true;
                break;
            case 'o':
                memcpy(cmd_parse.osd_name, optarg, max_file_name - 1);
                cmd_parse.if_osd = true;
                break;
            case 's':
                cmd_parse.single_layer = true;
                select = atoi(optarg);
                if (select == 0) {
                    cmd_parse.single_osd_layer = true;
                }
                break;
            default: /* '?' */
                exit(EXIT_FAILURE);
                break;
        }
    }

    signal(SIGINT, sigint_handler);
    display_init_by_internal(SCREEN_WIDTH, SCREEN_HEIGHT);
    main_process(cmd_parse);
    // non_image_process();
    // process_image(argv[1], argv[2]);
    // half_image_process(argv[1]);

    return 0;
}

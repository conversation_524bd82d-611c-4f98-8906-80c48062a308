
/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#ifndef __CMD_OPTION_PARSER_H__
#define __CMD_OPTION_PARSER_H__

#include <iostream>
#include "bst_isp_sdk_api.h"
#include <semaphore.h>

using namespace bvideo;

#define ISP_VIEW_MASK(i) (1 << i)

#define CMD_OPEN_LOG    ("openlog.sh")
#define CMD_LOG_DUMP    ("logdump -l")
#define CMD_GREP_DUMP   ("ps -ef | grep ")
#define CMD_KILL_DUMP   (" | grep -v grep | awk '{print $2}'| xargs --no-run-if-empty kill -9")
#define CMD_MKDIR       ("mkdir -p")
#define CMD_CLEAR_FILE  ("rm -r")

#define MSEC_USEC_BASE  (1000)

enum {
    RET_CMD_OK = 0,
    RET_CMD_ERR_SCREEN_FLAG = -1,
    RET_CMD_ERR_VIEW_TYPE = -2,
};

const int origin_width = 1920;
const int origin_height = 1080;
const int downscale_width = 1280;
const int downscale_height = 720;
const int origin_screen_resolution = 1080;
const int downscale_screen_resolution = 720;

typedef struct {
    bool save_flag = false;
    bool continusly_save_flag = false;
    bool display_flag = false;
    bool log_flag = false;
    bool using_zero_copy = false;
    bool display_timeval = false;
    bool embed_enable = false;
    bool embed_analysis = false;
    bool dump_log = false;
    bool dump_mono_utc_time = false;

    bool dump_running = false;

    int camera_id = 0;
    int isp_width = origin_width;
    int isp_height = origin_height;
    int screen_flag = origin_screen_resolution;
    int screen_width = origin_width;
    int screen_height = origin_height;
    int view_type = ISP_VIEW0;
    int view_index = 0;  // 0-view0; 1-view1; 2-viewy
    int buf_type =0;     // 0-invalid_type; 1-MMAP; 2-DMA buffer 3-USERPTR
    int timestamp_check_interval = 52;
    int pic_fmt_base = 3;
    int dump_log_time = 60;

    char save_filename[100];
    char continusly_save_filename[100];
    char dump_log_filename[100];

    sem_t sema_dump;

} CmdParserConfig_t;

extern CmdParserConfig_t cmd_config;

extern void printf_version(void);

extern int parse_cmd_options(int argc, char *const argv[]);

#endif  //__CMD_OPTION_PARSER_H__

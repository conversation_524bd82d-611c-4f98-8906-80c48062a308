
/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#ifndef __TIME_UTILS_H__
#define __TIME_UTILS_H__

#include <sys/time.h>
#include <time.h>

extern bool using_timecount;

#define REPORT_INTERVAL 10
#define THOUSAND 1000
#define MILLION 1000000

#define timecount_a(f)             \
    struct timeval A##f;           \
    struct timeval B##f;           \
    if (using_timecount) {         \
        gettimeofday(&A##f, NULL); \
    }

#define timecount_b(f)                                                   \
    static long long ITV##f;                                             \
    static long long CNT##f;                                             \
    if (using_timecount) {                                               \
        gettimeofday(&B##f, NULL);                                       \
        ITV##f += (B##f.tv_sec - A##f.tv_sec) * MILLION + B##f.tv_usec - \
                  A##f.tv_usec;                                          \
        if (++CNT##f == REPORT_INTERVAL) {                               \
            printf("Performance:: %s %lldms\n", #f,                      \
                   ITV##f / THOUSAND / REPORT_INTERVAL);                 \
            ITV##f = 0;                                                  \
            CNT##f = 0;                                                  \
        }                                                                \
    }

float fps();
long long gettime_ms(void);

#endif

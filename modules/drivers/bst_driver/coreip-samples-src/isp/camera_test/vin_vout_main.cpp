
/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include <errno.h>
#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include "bst_isp_types.h"
#include "cmd_option_parser.h"

#define SIGINT 2 /* Interactive attention signal.  */

using namespace std;
using namespace bvideo;

#include "capture.h"
#include "drmshow.h"
#include "time_utils.h"

int ter = 0;

static void sigint_handler(int arg)
{
    ter = 1;
    sem_post(&cmd_config.sema_dump);
}
extern void vin_init(void);

static void ImageCutCopy(uint8_t *display_buf, isp_buf_t *pbuf_info)
{
    unsigned char *ptr_source;
    unsigned char *ptr_dest;
    unsigned char *ptr_save_file;
    int image_width;
    int image_height;
    int loop;

    // y plane
    ptr_source = (unsigned char *)pbuf_info->views[cmd_config.view_index].start;
    ptr_dest = display_buf;
    image_width = (cmd_config.isp_width > cmd_config.screen_width)
                      ? cmd_config.screen_width
                      : cmd_config.isp_width;
    image_height = (cmd_config.isp_height > cmd_config.screen_height)
                       ? cmd_config.screen_height
                       : cmd_config.isp_height;
    for (loop = 0; loop < image_height; loop++) {
        memcpy(ptr_dest, ptr_source, image_width);
        ptr_dest += cmd_config.screen_width;
        ptr_source += cmd_config.isp_width;
    }

    // uv plane
    ptr_dest = (unsigned char *)display_buf +
               cmd_config.screen_width * cmd_config.screen_height;
    ptr_source =
        (unsigned char *)pbuf_info->views[cmd_config.view_index].start +
        cmd_config.isp_width * cmd_config.isp_height;

    // default background black
    memset(ptr_dest, 0x80,
           cmd_config.screen_width * cmd_config.screen_height / 2);

    // for view2, do nothing
    if (cmd_config.view_type == ISP_VIEWY) {
        return;
    }

    // for view0/view1, copy uv plane data
    for (loop = 0; loop < image_height / 2; loop++) {
        memcpy(ptr_dest, ptr_source, image_width);
        ptr_dest += cmd_config.screen_width;
        ptr_source += cmd_config.isp_width;
    }

    return;
}

int process_image(isp_buf_t *pbuf_info)
{
    int ret;
#ifdef CAPTURE
    static int count;
#endif

    if (!cmd_config.display_flag) {
        return 0;
    }

    if (cmd_config.using_zero_copy) {
        display_commit_by_fd(pbuf_info->buf_index, NULL);
    } else {
        uint8_t *primary_buf;
        int primary_size;

        get_primary_plane_next_buf(&primary_buf, &primary_size);
        ImageCutCopy(primary_buf, pbuf_info);
        display_commit_by_buf(primary_buf, NULL);
    }

    if (ter)
        return -1;
    else
        return 0;
}

int main(int argc, char **argv)
{
    uint32_t ret;

    printf_version();

    signal(SIGINT, sigint_handler);

    ret = parse_cmd_options(argc, argv);

    if (RET_CMD_OK != ret) {
        return -1;
    }

    vin_init();

    return 0;
}

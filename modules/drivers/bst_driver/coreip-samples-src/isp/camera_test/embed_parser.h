#include <dirent.h>
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <limits.h>
#include <pthread.h>
#include <semaphore.h>
#include <signal.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>
#include <cstdint>
#include <iostream>

#define EMBED_BYTES_USED 128
#define CAMERA_RAW_14 14
#define CAMERA_RAW_12 12
#define RAW_DATA_BYTE_PER_BLOCK 16
#define BITS_PER_BYTE 8
#define EMBD_CONFIG_ARRAY_SIZE 9
#define EMBD_FRAME_COUNT_DATA1_OFFSET 19
#define EMBD_FRAME_COUNT_DATA2_OFFSET 23
#define EMBD_FRAME_COUNT_DATA3_OFFSET 27
#define EMBD_FRAME_COUNT_DATA4_OFFSET 31
#define EMBD_VALID_DATA_SIZE 16
#define EMBD_MCRC_VALUE_OFFSET 12
#define EMBD_VALID_DATA_BLOCK_NUM 3
#define EMBD_CRC_DATA1_OFFSET 52
#define EMBD_CRC_DATA2_OFFSET 53
#define EMBD_CRC_DATA3_OFFSET 54
#define EMBD_CRC_DATA4_OFFSET 55
#define EMBD_DATA_BYTE_PER_REG 4
#define EMBD_CHIP_ID_DATA1_OFFSET 3
#define EMBD_CHIP_ID_DATA2_OFFSET 7
#define EMBD_CHIP_ID_DATA3_OFFSET 11
#define EMBD_CHIP_ID_DATA4_OFFSET 15
#define EMBD_TMPRTURE_DATA1_OFFSET 35
#define EMBD_TMPRTURE_DATA2_OFFSET 39
#define EMBD_TMPRTURE_DATA3_OFFSET 43
#define EMBD_TMPRTURE_DATA4_OFFSET 47
#define EMBD_MCRC_DATA1_OFFSET 48
#define EMBD_MCRC_DATA2_OFFSET 49
#define EMBD_MCRC_DATA3_OFFSET 50
#define EMBD_MCRC_DATA4_OFFSET 51

using namespace std;

namespace utils {
class BitArray {
   public:
    uint8_t *data;

    uint32_t arraysize = 0;

   public:
    BitArray(size_t cnt)
    {
        arraysize = cnt;

        data = (uint8_t *)malloc(arraysize + 1);
    }
    ~BitArray()
    {
        free(data);
        data = NULL;
    }

   public:
    uint8_t get(size_t index)
    {
        int off = index / (8);
        int bit = index % (8);
        uint8_t res = (data[off] & (0x01 << bit)) >> bit;
        return res;
    }

    void get(size_t index, int loop, uint8_t *value)
    {
        uint16_t value_tmp = 0;
        for (int i = 0; i < loop; i++) {
            value_tmp = value_tmp << 1;
            value_tmp += get(index - i);
        }
        if (14 == loop) {
            *value = (value_tmp >> 6);
        } else if (12 == loop) {
            *value = (value_tmp >> 4);
        }
    }
    void clear() { memset(data, 0, arraysize); }
};
}  // namespace utils


/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include "time_utils.h"
#include <stdint.h>
#include <stdio.h>

bool using_timecount = true;

static uint64_t getTick()
{
    struct timespec ts{};
    clock_gettime(CLOCK_MONOTONIC_RAW, &ts);
    return (ts.tv_sec * 1000 + ts.tv_nsec / 1000000);
}

float fps(int index = -1)
{
    static uint64_t lastTime = getTick();  // ms
    static int frameCount = 0;
    float fps = 0;

    ++frameCount;
    uint64_t curTime = getTick();
    if (curTime - lastTime > 10 * 1000)  // inteval 10s
    {
        fps = (float)(frameCount * 1000) / (curTime - lastTime);

        int frame_cnt = frameCount;
        uint64_t time_gap = (curTime - lastTime);
        frameCount = 0;
        lastTime = curTime;
        if (index >= 0) {
            printf("video%d:: fps = %0.1f\n", index, fps);
        } else {
            printf("Performance:: fps = %0.1f\n", fps);
        }

        return fps;
    }
    return 0;
}

// return ms from epoch
long long gettime_ms(void)
{
    struct timeval now;
    gettimeofday(&now, NULL);
    return (now.tv_sec * 1000 + now.tv_usec / 1000);
}

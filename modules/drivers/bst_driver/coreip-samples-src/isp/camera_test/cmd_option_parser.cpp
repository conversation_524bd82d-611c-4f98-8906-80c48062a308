
/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include "cmd_option_parser.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "test_common.h"

using namespace std;

CmdParserConfig_t cmd_config;
static const char *CMD_OPTSTR = "p:i:h:w:v:s:c:b:D:d:g:m:xLteaHT";

void printf_version(void)
{
    printf("current version : %d.%d.%d\n", TEST_VER_MAJOR, TEST_VER_MINOR,
           TEST_VER_PATCH);
}

int parse_cmd_options(int argc, char *const argv[])
{
    printf("start parsing cmd options...\n");
    cmd_config.view_type = -1;

    int optchar;
    do {
        optchar = getopt(argc, argv, CMD_OPTSTR);
        if (-1 == optchar) break;
        switch (optchar) {
            case 'i':
                cmd_config.camera_id = strtol(optarg, NULL, 0);
                break;
            case 'w':
                cmd_config.isp_width = strtol(optarg, NULL, 0);
                break;
            case 'h':
                cmd_config.isp_height = strtol(optarg, NULL, 0);
                break;
            case 's':
                cmd_config.save_flag = true;
                memcpy(cmd_config.save_filename, optarg, 100);
                break;
            case 'c':
                cmd_config.continusly_save_flag = true;
                memcpy(cmd_config.continusly_save_filename, optarg, 100);
                break;
            case 'x':
                cmd_config.display_flag = true;
                break;
            case 'L':
                cmd_config.log_flag = true;
                break;
            case 't':
                cmd_config.display_timeval = true;
                break;
            case 'p':
                cmd_config.screen_flag = strtol(optarg, NULL, 0);
                break;
            case 'T':
                cmd_config.dump_mono_utc_time = true;
                break;
            case 'b':
                cmd_config.buf_type = strtol(optarg, NULL, 0);
                break;
            case 'v':
                cmd_config.view_index = strtol(optarg, NULL, 0);
                if (cmd_config.view_index < 0 || cmd_config.view_index > 4) {
                    printf("invalid view index!\n");
                    return RET_CMD_ERR_VIEW_TYPE;
                }
                
                if (cmd_config.view_index > 2) {
                    cmd_config.view_type = 3;
                    cmd_config.view_index -= 3;
                    printf("view_type = %d, view_index = %d\n", cmd_config.view_type, cmd_config.view_index);
                } else {
                    cmd_config.view_type = (1 << cmd_config.view_index);
                }

                break;
            case 'e':
                cmd_config.embed_enable = true;
                break;
            case 'a':
                cmd_config.embed_analysis = true;
                break;
            case 'D':
                memcpy(cmd_config.dump_log_filename, optarg, 100);
                cmd_config.dump_log = true;
                break;
            case 'd':
                cmd_config.dump_log_time = strtol(optarg, NULL, 0);
                break;
            case 'm':
                cmd_config.pic_fmt_base = strtol(optarg, NULL, 0);
                break;
            case 'g':
                cmd_config.timestamp_check_interval = strtol(optarg, NULL, 0);
                break;
            case 'H':
                printf("show help infos as follows and exit.\n");
            default:
                printf("cmd_argvs to input are as follows:\n");
                printf("======================================================\n");
                printf("-i CAM_ID; camera device to open and test, default as 0, ex. -i 0 for open /dev/video0\n");
                printf("-w WIDTH; camera width, default as 1920, ex. -w 1920 for camera which width is 1920\n");
                printf("-h HEIGHT; camera height, default as 1080, ex. -h 1080 for camera which width is 1080\n");
                printf("-v VIEW_INDEX; set camera view channel which ranges from 0 to 2, confilcted to -w and -h\n");
                printf("-s OUTPUT_FILE_NAME; save one frame into one file every 20 frames\n");
                printf("-c OUTPUT_FILE_NAME; save continuely frame into one file, it save at most 150 frames\n");
                printf("-g MAX_FRAME_INTERVAL; timestamp check interval, default as 52 ms\n");
                printf("-p DOWNSCALE_SCREEN_RESOLUTION; set display buffer, support 1080 and 720\n");
                printf("-b BUFFER_TYPE; set buffer type of input, 1 for mmp, 2 for dma\n");
                printf("-D LOG_SAVE_FOLDER; open firmware logdump and while drop frame dump fw log under target folder\n");
                printf("-d DUMP_LOG_TIME; under -D mode, set time interval for logdump, default save 60s while drop frame\n");
                printf("-m PICTURE_FORMAT_BASE; set picture save base for size calculate as width*height*PICTURE_FORMAT_BASE/2\n");
                printf("-x ; display a 1920x1080 nv12 format picture\n");
                printf("-L ; show frame informations while gets a frame\n");
                printf("-t ; show time interval from EOF to get_frame\n");
                printf("-T ; show Monotonic and UTC timestamp of getting video buf\n");
                printf("-e ; enable embeded data\n");
                printf("-a ; enable embeded data analysis\n");
                printf("======================================================\n");
                exit(EXIT_FAILURE);
                break;
        }
    } while (optchar != -1);

    if (cmd_config.screen_flag == origin_screen_resolution) {
        cmd_config.screen_width = origin_width;
        cmd_config.screen_height = origin_height;
    } else if (cmd_config.screen_flag == downscale_screen_resolution) {
        cmd_config.screen_width = downscale_width;
        cmd_config.screen_height = downscale_height;
    }

    cout << "video device is : /dev/video" << cmd_config.camera_id << endl;
    cout << "camera width is : " << cmd_config.isp_width << endl;
    cout << "camera height is : " << cmd_config.isp_height << endl;
    cout << "save_flag is : " << cmd_config.save_flag << endl;
    cout << "display flag is : " << cmd_config.display_flag << endl;
    cout << "log_flag is : " << cmd_config.log_flag << endl;
    cout << "buf_type is : " << cmd_config.buf_type << endl;

    return RET_CMD_OK;
}

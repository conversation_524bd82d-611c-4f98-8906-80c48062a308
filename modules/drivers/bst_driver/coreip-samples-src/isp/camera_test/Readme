this code is for test ov2311 camera

fw :with driver_test/firmware/ov2311/peking_show

kernel:http://************/bstos/linux_kernel/tree/mipi_test

fw will output 2 kinds of image:
1>1600*1300 NV12
2>1328*1080 NV12

could choose when app run:

eg.1 : camera_test -d /dev/video0 -w 1600 -h 1300
eg.2 : camera_test -d /dev/video0 -w 1328 -h 1080

also ,you could save source frame use below cmd,the image frame will locate at /nv12 of board
camera_test -d /dev/video0 -w 1600 -h 1300 -s
camera_test -d /dev/video0 -w 1328 -h 1080 -s


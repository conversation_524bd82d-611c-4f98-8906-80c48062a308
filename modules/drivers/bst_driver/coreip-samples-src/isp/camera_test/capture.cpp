
/*******************************************************************************
 * Copyright(c) Black Sesame Technologies Inc., All right reserved.
 *
 * No part of this file may be distributed, duplicated or transmitted in any
 *form or by any means, without prior consent of Black Sesame Technologies Inc.
 *
 * This file is  property. It contains BST's trade secret, proprietary
 * and confidential information.
 *
 *********************************************************************************/

#include "capture.h"
#include <assert.h>
#include <errno.h>
#include <fcntl.h>  /* low-level i/o */
#include <getopt.h> /* getopt_long() */
#include <linux/videodev2.h>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <thread>
#include <dirent.h>
#include <cctype>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <algorithm>
#include "bst_isp_sdk_api.h"
#include "capture.h"
#include "cmd_option_parser.h"
#include "drmshow.h"
#include "bst_video_common.h"
#include "bst_hwcv_user_api.h"
#include "embed_parser.h"

#define MAX_CMD_LEN     128
#define MAX_PROCESSES   256
#define PROCESS_NAME    "logdump"

#define CLEAR(x) memset(&(x), 0, sizeof(x))
#define MAX_CAMERA_NUM 12
#define ISP_VIEW_MASK(i) (1 << i)

#define EMBED_DATA_BUF_RESERVE_SIZE (64*1024)
#define YUV_STAT_BUF_EXPAND (1024)
#define EMBED_LINE_NUM_USED 2

using namespace bvideo;
using namespace bhwcv;

extern float fps(int index);
extern int process_image(isp_buf_t *p);
extern int ter;

int frame_fd;
int cons_frame_fd;
long capture_frame_count = 0;

handle_t handle;

uint64_t pre_timestamp = 0;
uint32_t pre_sequence = 0;

cv_req_buffer_t dma_buf_pool[MAX_VIEW_NUM][MAX_BUF_NUM];
dma_buf_t isp_dma_buf[MAX_VIEW_NUM * MAX_BUF_NUM];

long int src_len;
camera_info_t camera_info;

uint32_t crc32_emd(uint8_t *data, uint16_t length, bool xorout)
{
    uint8_t i;
    uint32_t crc = 0xffffffff;  // Initial value
    uint32_t crc_tmp = 0;
    while (length--) {
        crc ^= *data++;
        for (i = 0; i < 8; ++i) {
            if (crc & 1)
                crc =
                    (crc >> 1) ^ 0x82F63B78;  // 0x82F63B78= reverse 0x1EDC6F41
            else
                crc = (crc >> 1);
        }
    }
    if (true == xorout) {
        for (int j = 0; j < 4; j++) {
            crc_tmp += (((~crc) >> (j * 8)) & 0x000000FF);
            if (j < 3) crc_tmp = crc_tmp << 8;
        }
        return crc_tmp;
    } else {
        return crc;
    }
}

static void embed_data_parser(isp_buf_t *buf_info, camera_info_t *pcamera_info)
{
    int pixel_bits = 0;
    int embed_size_used = EMBED_BYTES_USED;
    if (!(buf_info->embedded_info.emd_zone_num)) {
        printf("Error: embedded data line empty! \n");
        return;
    }

    if (DT_RAW14 == pcamera_info->camera_data_type) {
        pixel_bits = CAMERA_RAW_14;
    } else if (DT_RAW12 == pcamera_info->camera_data_type) {
        pixel_bits = CAMERA_RAW_12;
    } else {
        pixel_bits = CAMERA_RAW_14;
    }

    utils::BitArray datain(embed_size_used);
    utils::BitArray dataout(embed_size_used);
    datain.clear();
    dataout.clear();

    // only use front 2 embedded line (emd_zone[0])
    memcpy(datain.data, buf_info->embedded_info.emd_zone[0].start,
           embed_size_used);

    int line_block_cnt = (embed_size_used + RAW_DATA_BYTE_PER_BLOCK - 1) / RAW_DATA_BYTE_PER_BLOCK;
    int block_pixel_cnt = EMBED_BYTES_USED / pixel_bits;

    for (int i = 0; i < line_block_cnt; i++) {
        for (int j = 0; j < block_pixel_cnt; j++) {
            datain.get(RAW_DATA_BYTE_PER_BLOCK * BITS_PER_BYTE * (i + 1) - 1 - j * pixel_bits, pixel_bits,
                       &dataout.data[i * block_pixel_cnt + j]);
            embed_size_used--;
            if (embed_size_used == 0) {
                break;
            }
        }
    }

    static uint32_t frame_count_cur = 0;
    static uint32_t frame_count_pre = 0;

    frame_count_cur = (*(dataout.data + EMBD_FRAME_COUNT_DATA1_OFFSET) << 24) +
                      (*(dataout.data + EMBD_FRAME_COUNT_DATA2_OFFSET) << 16) +
                      (*(dataout.data + EMBD_FRAME_COUNT_DATA3_OFFSET) << 8) + (*(dataout.data + EMBD_FRAME_COUNT_DATA4_OFFSET));

    uint32_t crc_resault;
    // embedded config in sensor setting, it`s config named MCRC.
    static uint8_t mcrc_source[EMBD_CONFIG_ARRAY_SIZE] = {0x30, 0x0A, 0x04, 0x46, 0x20, 0x04, 0x4D, 0x56, 0x04};
    crc_resault = crc32_emd(mcrc_source, EMBD_CONFIG_ARRAY_SIZE, false);

    uint8_t datacrc_source[EMBD_VALID_DATA_SIZE];
    for (int i = 0; i < EMBD_VALID_DATA_SIZE; i++) {
        if (i < EMBD_MCRC_VALUE_OFFSET) datacrc_source[i] = *(dataout.data + (i + 1) * EMBD_DATA_BYTE_PER_REG - 1);
        if (i >= EMBD_MCRC_VALUE_OFFSET) datacrc_source[i] = *(dataout.data + RAW_DATA_BYTE_PER_BLOCK * EMBD_VALID_DATA_BLOCK_NUM + (i-EMBD_MCRC_VALUE_OFFSET));
    }

    crc_resault = crc32_emd(datacrc_source, EMBD_VALID_DATA_SIZE, true);

    static uint32_t crc_recv = 0;
    crc_recv = (*(dataout.data + EMBD_CRC_DATA1_OFFSET) << 24) + (*(dataout.data + EMBD_CRC_DATA2_OFFSET) << 16) +
               (*(dataout.data + EMBD_CRC_DATA3_OFFSET) << 8) + (*(dataout.data + EMBD_CRC_DATA4_OFFSET));

    printf("\n");

    if (frame_count_pre != 0) {
        if (((frame_count_cur - frame_count_pre) != 1) && (crc_recv != crc_resault)) {
            if (crc_recv != crc_resault) {
                printf("camera [%d], Data CRC not match !, crc_recv 0x%08x\n", cmd_config.camera_id,
                    crc_recv);
            }
            if((frame_count_cur - frame_count_pre) != 1) {
                printf("camera [%d], Sensor frame count error, frame_count_pre %d, "
                "frame_count_cur %d\n",
                cmd_config.camera_id, frame_count_pre, frame_count_cur);
            }

            printf("\n");
            printf("embedded data after perser:\n");
            for(int i=0; i<64; i++) {
                if( 0 == i%16 )
                    printf("\n");
                printf("%02x ", *((char *)dataout.data + i));
            }

            printf("\n");
            printf("camera [%d], Embed data parser info: \n", cmd_config.camera_id);
            printf("camera [%d], Sensor chip ID: 0x%02x 0x%02x 0x%02x 0x%02x \n",
                cmd_config.camera_id, *(dataout.data + EMBD_CHIP_ID_DATA1_OFFSET), *(dataout.data + EMBD_CHIP_ID_DATA2_OFFSET),
                *(dataout.data + EMBD_CHIP_ID_DATA3_OFFSET), *(dataout.data + EMBD_CHIP_ID_DATA4_OFFSET));
            printf("camera [%d], Sensor frame count: 0x%02x 0x%02x 0x%02x 0x%02x \n",
                cmd_config.camera_id, *(dataout.data + EMBD_FRAME_COUNT_DATA1_OFFSET), *(dataout.data + EMBD_FRAME_COUNT_DATA2_OFFSET),
                *(dataout.data + EMBD_FRAME_COUNT_DATA3_OFFSET), *(dataout.data + EMBD_FRAME_COUNT_DATA4_OFFSET));
            printf("camera [%d], Sensor temperature: 0x%02x 0x%02x 0x%02x 0x%02x \n",
                cmd_config.camera_id, *(dataout.data + EMBD_TMPRTURE_DATA1_OFFSET), *(dataout.data + EMBD_TMPRTURE_DATA2_OFFSET),
                *(dataout.data + EMBD_TMPRTURE_DATA3_OFFSET), *(dataout.data + EMBD_TMPRTURE_DATA4_OFFSET));
            printf("camera [%d], Sensor MCRC: 0x%02x 0x%02x 0x%02x 0x%02x \n", cmd_config.camera_id,
                *(dataout.data + EMBD_MCRC_DATA1_OFFSET), *(dataout.data + EMBD_MCRC_DATA2_OFFSET), *(dataout.data + EMBD_MCRC_DATA3_OFFSET),
                *(dataout.data + EMBD_MCRC_DATA4_OFFSET));
            printf("camera [%d], Sensor data CRC: 0x%02x 0x%02x 0x%02x 0x%02x \n",
                cmd_config.camera_id, *(dataout.data + EMBD_CRC_DATA1_OFFSET), *(dataout.data + EMBD_CRC_DATA2_OFFSET),
                *(dataout.data + EMBD_CRC_DATA3_OFFSET), *(dataout.data + EMBD_CRC_DATA4_OFFSET));
        }
    }

    frame_count_pre = frame_count_cur;

    printf("camera [%d], Sensor frame count: 0x%02x 0x%02x 0x%02x 0x%02x \n",
        cmd_config.camera_id, *(dataout.data + EMBD_FRAME_COUNT_DATA1_OFFSET), *(dataout.data + EMBD_FRAME_COUNT_DATA2_OFFSET),
        *(dataout.data + EMBD_FRAME_COUNT_DATA3_OFFSET), *(dataout.data + EMBD_FRAME_COUNT_DATA4_OFFSET));

    return;

}

int read_frame(isp_buf_t *pbuf_info)
{
    // get one frame
    int ret;
    while (true) {
        int ret = isp_get_frame_buf(handle, pbuf_info, -1);

        if (ret == 0) {  // success
            break;
        } else if (ret == RET_TRYAGAIN || ret == RET_TIMEOUT) {  // not ready
            continue;
        } else {  // error
            printf("GetFrame failed\n");
            return RET_ERR_COMMON;
        }
    }

    if (cmd_config.display_timeval) {
        struct timespec get_buf_time;
        clock_gettime(CLOCK_MONOTONIC, &get_buf_time);
        uint64_t buf_time = get_buf_time.tv_sec * 1000 * 1000 +
                            (uint64_t)round(get_buf_time.tv_nsec / 1000.0);
        uint64_t timeval =
            buf_time - pbuf_info->views[cmd_config.view_index].ts_usec;
        printf("video%d:: timeval from EOF to get_frame is : %lld us.\n", cmd_config.camera_id, timeval);
    }

    if (cmd_config.log_flag) {
        if(cmd_config.dump_mono_utc_time) {
        printf("video%d:: get a buffer[%d] %u at %lld usec, exposure_time = %u us, y_statistic-%d, roi brightness: %d %d %d %d\n"
                "buf_monotonic_ts: %lld usec, buf_utc_ts: %lld usec \n",
               cmd_config.camera_id,
               pbuf_info->views[cmd_config.view_index].buf_index,
               pbuf_info->views[cmd_config.view_index].sequence,
               pbuf_info->views[cmd_config.view_index].ts_usec,
               pbuf_info->views[cmd_config.view_index].exposure_time,
               pbuf_info->y_statistic,
               pbuf_info->views[cmd_config.view_index].roi_value[0],
               pbuf_info->views[cmd_config.view_index].roi_value[1],
               pbuf_info->views[cmd_config.view_index].roi_value[2],
               pbuf_info->views[cmd_config.view_index].roi_value[3],
               pbuf_info->views[cmd_config.view_index].buf_monotonic_usec,
               pbuf_info->views[cmd_config.view_index].buf_utc_usec);
        } else {
            printf("video%d:: get a buffer[%d] %u at %lld usec, exposure_time = %u us, y_statistic-%d, roi brightness: %d %d %d %d \n",
               cmd_config.camera_id,
               pbuf_info->views[cmd_config.view_index].buf_index,
               pbuf_info->views[cmd_config.view_index].sequence,
               pbuf_info->views[cmd_config.view_index].ts_usec,
               pbuf_info->views[cmd_config.view_index].exposure_time,
               pbuf_info->y_statistic,
               pbuf_info->views[cmd_config.view_index].roi_value[0],
               pbuf_info->views[cmd_config.view_index].roi_value[1],
               pbuf_info->views[cmd_config.view_index].roi_value[2],
               pbuf_info->views[cmd_config.view_index].roi_value[3]);
        }
    }

    bool start_dump = false;

    if (pbuf_info->views[cmd_config.view_index].sequence - pre_sequence != 1 &&
        pre_sequence != 0) 
    {
        printf("video%d:: get frame sequence error detected, pre_sequence = %d, cur_sequence = %d\n", 
                cmd_config.camera_id, pre_sequence, pbuf_info->views[cmd_config.view_index].sequence);
        if(cmd_config.dump_log)
        {
            start_dump = true;
        }
    }

    if ((pbuf_info->views[cmd_config.view_index].ts_usec < pre_timestamp ||
         pbuf_info->views[cmd_config.view_index].ts_usec - pre_timestamp >
             cmd_config.timestamp_check_interval * MSEC_USEC_BASE) &&
        pre_timestamp != 0) 
    {
        printf("video%d:: get frame timestamp error detected, pre_timestamp = %llu, cur_timestamp = %llu\n", 
                cmd_config.camera_id, pre_timestamp, pbuf_info->views[cmd_config.view_index].ts_usec);
        if(cmd_config.dump_log)
        {
            start_dump = true;
        }
    }

    pre_sequence = pbuf_info->views[cmd_config.view_index].sequence;
    pre_timestamp = pbuf_info->views[cmd_config.view_index].ts_usec;

    if(cmd_config.dump_log)
    {
        if(start_dump)
        {
            if(!cmd_config.dump_running)
            {
                cmd_config.dump_running = true;
                sem_post(&cmd_config.sema_dump);
            }
        }
    }

    // display
    if (cmd_config.display_flag) {
        ret = process_image(pbuf_info);
        if (ret < 0) return -1;
    }

    fps(cmd_config.camera_id);

    if (true == cmd_config.embed_enable && true == cmd_config.embed_analysis) {
        embed_data_parser(pbuf_info, &camera_info);
    }

    int save_length = pbuf_info->views[cmd_config.view_index].length;
    if(cmd_config.pic_fmt_base != 0)
    {
        save_length = cmd_config.isp_width * cmd_config.isp_height * cmd_config.pic_fmt_base / 2;
    }

    capture_frame_count++;
    if (cmd_config.save_flag && capture_frame_count % 20 == 0) {
        write(frame_fd, (void *)pbuf_info->views[cmd_config.view_index].start,
                save_length);

    }

    if (cmd_config.continusly_save_flag && capture_frame_count <= 150) {
        write(cons_frame_fd,
              (void *)pbuf_info->views[cmd_config.view_index].start,
              save_length);
    }

    return 0;
}

int put_frame(isp_buf_t *pbuf_info)
{
    if (isp_put_frame_buf(handle, pbuf_info)) {
        printf("PutFrame failed\n");
        return RET_ERR_COMMON;
    }
    return RET_OK;
}

// Function to get an array of process IDs (PIDs) by process name
int get_pids_by_name(const char *proc_name, pid_t *pids, size_t max_pids) {
    DIR *dir = opendir("/proc");
    struct dirent *entry;
    int num_pids = 0;

    if (dir == NULL) {
        perror("opendir");
        return -1;
    }

    while ((entry = readdir(dir)) != NULL && num_pids < max_pids) {
        // Skip entries that are not directories (process folders are directories)
        if (entry->d_type != DT_DIR)
            continue;

        // Skip entries that are not numeric (process folder names are numeric PIDs)
        if (!isdigit(entry->d_name[0]))
            continue;

        // Read the command name from /proc/[pid]/comm
        char comm_path[256];
        snprintf(comm_path, sizeof(comm_path), "/proc/%s/comm", entry->d_name);
        FILE *comm_file = fopen(comm_path, "r");
        if (comm_file == NULL) {
            continue;
        }

        char comm[256];
        if (fgets(comm, sizeof(comm), comm_file) != NULL) {
            // Remove newline character from the command name
            comm[strlen(comm) - 1] = '\0';

            // Compare the command name with the target process name
            if (strcmp(proc_name, comm) == 0) {
                pids[num_pids++] = atoi(entry->d_name);
            }
        }

        fclose(comm_file);
    }

    closedir(dir);
    return num_pids;
}

void log_dump_func()
{
    char cmd_dump[MAX_CMD_LEN];
    char cmd_kill[MAX_CMD_LEN];
    char cmd_clear[MAX_CMD_LEN];
    char cmd_mkdir[MAX_CMD_LEN];
    char dump_tile_name[MAX_CMD_LEN];
    char cmd_keep_one_process[MAX_CMD_LEN];
    snprintf(cmd_clear, MAX_CMD_LEN, "%s %s", CMD_CLEAR_FILE, cmd_config.dump_log_filename);
    system(cmd_clear);

    system(CMD_OPEN_LOG);

    snprintf(cmd_mkdir, MAX_CMD_LEN, "%s %s", CMD_MKDIR, cmd_config.dump_log_filename);
    system(cmd_mkdir);

    int dump_cnt = 0;
    pid_t pids[MAX_PROCESSES];

    while(!ter)
    {
        sem_wait(&cmd_config.sema_dump);
        if(ter)
        {
            break;
        }

        //skip loop if logdump started
        if(get_pids_by_name(PROCESS_NAME, pids, MAX_PROCESSES) > 0)
        {
            continue;
        }
        memset(pids, 0, MAX_PROCESSES);

        snprintf(cmd_dump, MAX_CMD_LEN, "%s > %s/%s_%d.log &", CMD_LOG_DUMP, cmd_config.dump_log_filename, "logdump_save", dump_cnt);
        printf("logdump capture log for %d seconds/n", cmd_config.dump_log_time);
        dump_cnt++;
        system(cmd_dump);

        int proc_num = get_pids_by_name(PROCESS_NAME, pids, MAX_PROCESSES);
        for(int i = proc_num - 1; i > 0; i--)
        {
            snprintf(cmd_keep_one_process, MAX_CMD_LEN, "kill -9 %d", pids[i]);
            system(cmd_keep_one_process);
            pids[i] = 0;
        }

        sleep(cmd_config.dump_log_time);
        snprintf(cmd_kill, MAX_CMD_LEN, "kill -9 %d", pids[0]);
        system(cmd_kill);
        pids[0] = 0;
        cmd_config.dump_running = false;
    }
    
}

void mainloop(void)
{
    int ret = 0;
    isp_buf_t buf_info;

    std::thread* dump_thread;

    if(cmd_config.dump_log)
    {
        dump_thread = new std::thread(log_dump_func);
    }

    while (!ter) {
        ret = read_frame(&buf_info);
        if (ret < 0) {
            if(cmd_config.dump_log)
            {
                if(!cmd_config.dump_running)
                {
                    cmd_config.dump_running = true;
                    sem_post(&cmd_config.sema_dump);
                }
            }

            abnormal_info_t abnormal_info;
            if (!isp_camera_get_abnormal_info(handle, &abnormal_info)) {
                printf(
                    "Abnormal Frame Info: ID: 0x%02X, type: 0x%02X, "
                    "last_good_sequence: %u, total_bad_frames: %u, "
                    "total_frames: %u\n",
                    abnormal_info.abnormal_id, abnormal_info.abnormal_type,
                    abnormal_info.last_good_sequence,
                    abnormal_info.total_bad_frames, abnormal_info.total_frames);
                continue;
            } else {
                fprintf(stderr,
                        "Failed to read frame and can't get abnormal "
                        "information\n");
                break;
            }
        }
        ret = put_frame(&buf_info);
        if (ret < 0) {
            break;
        }
    }

    return;
}

int check_formats(handle_t handle)
{
    view_format_t format;

    if (cmd_config.view_type >= 0) {
        isp_get_view_format(handle, cmd_config.view_index, &format);
        cmd_config.isp_width = format.width;
        cmd_config.isp_height = format.height;
        printf("view %d: width %d, height %d\n", cmd_config.view_index,
               format.width, format.height);
        return RET_OK;
    }

    for (int i = 0; i < MAX_VIEW_NUM; i++) {
        if (isp_get_view_format(handle, i, &format)) {
            printf("GetFormat Failed\n");
            return RET_ERR_COMMON;
        }
        printf("view %d: width %d, height %d\n", i, format.width,
               format.height);
        if (format.width == cmd_config.isp_width &&
            format.height == cmd_config.isp_height) {
            switch (i) {
                case 0:
                    cmd_config.view_type = ISP_VIEW0;
                    cmd_config.view_index = 0;
                    break;
                case 1:
                    cmd_config.view_type = ISP_VIEW1;
                    cmd_config.view_index = 1;
                    break;
                case 2:
                    cmd_config.view_type = ISP_VIEWY;
                    cmd_config.view_index = 2;
                    break;
                default:
                    break;
            }
            break;
        }
    }

    if (cmd_config.view_type < 0) {
        printf("input format %d x %d not matched\n", cmd_config.isp_width,
               cmd_config.isp_height);
        return -1;
    }

    return RET_OK;
}

void vin_init(void)
{
    int ret = 0;

    bhwcv::handle_t handle_dma;
    if (cmd_config.save_flag) {
        frame_fd = open(cmd_config.save_filename,
                        O_RDWR | O_TRUNC | O_CREAT | O_APPEND);
    }

    if (cmd_config.continusly_save_flag) {
        cons_frame_fd = open(cmd_config.continusly_save_filename,
                             O_RDWR | O_TRUNC | O_CREAT | O_APPEND);
    }

    handle = isp_open_video(cmd_config.camera_id, ISP_MODE_VIEWS);

    if (!handle) {
        printf("OpenVideo Failed\n");
        return;
    }

    if (check_formats(handle)) {
        isp_close_video(handle);
        return;
    }

    isp_get_camera_info(handle, &camera_info);
        printf("Get camera raw info, camera_raw_width %d,camera_raw_height %d\n",
                camera_info.camera_raw_width, camera_info.camera_raw_height);

    if ( ISP_BUFFER_TYPE_DMABUF == cmd_config.buf_type ) {

        ret = bst_hwcv_open(&handle_dma);
        if (ret)
        {
            printf("open error\n");
            return ;
        }

        int i = 0;
        int n = 0;
        int view_count = 0;
        for (n = 0; n < MAX_VIEW_NUM; n++) {
            if (((view_type_t)cmd_config.view_type) & ISP_VIEW_MASK(n)) {
                view_format_t view_format;
                isp_get_view_format(handle, n, &view_format);
                // Then length of DMA buffer must add embedded size expand and reserve memory expand.
                src_len = (view_format.width * view_format.height * 3/2) + YUV_STAT_BUF_EXPAND +
                    EMBED_DATA_BUF_RESERVE_SIZE + EMBED_LINE_NUM_USED * camera_info.camera_raw_width;
                printf("DMA buffer src_len %d, view %d, width %d, height %d\n",
                src_len, n, view_format.width, view_format.height);
                //buffers request
                ret = bst_hwcv_request_dma_buffers(handle_dma,
                                    dma_buf_pool[n],
                                    src_len,
                                    MAX_BUF_NUM, true);
                if (ret)
                {
                    printf("bst_hwcv_request_dma_buffers error\n");
                    return ;
                }
                for(i=0; i<MAX_BUF_NUM; i++)
                {
                    isp_dma_buf[view_count * MAX_BUF_NUM + i].dma_buffer_fd = dma_buf_pool[n][i].fd;
                    isp_dma_buf[view_count * MAX_BUF_NUM + i].user_addr= dma_buf_pool[n][i].user_addr;
                }
                view_count++;
            }
        }
        if (isp_init_video_import_dma_buf(handle, (view_type_t)cmd_config.view_type, MAX_BUF_NUM, isp_dma_buf)) {
            printf("InitVideo Failed\n");
            isp_close_video(handle);
            return;
        }
    } else {
        if (isp_init_video(handle, (view_type_t)cmd_config.view_type,
                       MAX_BUF_NUM)) {
            printf("InitVideo Failed\n");
            isp_close_video(handle);
            return;
        }
    }

    if (true == cmd_config.embed_enable) {
        isp_enable_embedded_data(handle, (view_type_t)cmd_config.view_type);
    }

    if (cmd_config.display_flag) {
        int view_fds[MAX_BUF_NUM];
        int i;

        if (cmd_config.screen_width == cmd_config.isp_width &&
            cmd_config.screen_height == cmd_config.isp_height) {
            cmd_config.using_zero_copy = true;
        }
        printf("using_zero_copy:  %d\n", cmd_config.using_zero_copy);

        for(i=0; i<MAX_BUF_NUM; i++)
        {
            if ( ISP_BUFFER_TYPE_DMABUF == cmd_config.buf_type ) {
                view_fds[i] = dma_buf_pool[cmd_config.view_index][i].fd;
            }else {
                view_fds[i] = isp_get_view_buf_fd(handle, i, cmd_config.view_index);
            }
        }
        if (cmd_config.using_zero_copy) {
            display_init_by_fd(cmd_config.screen_width,
                               cmd_config.screen_height, view_fds, MAX_BUF_NUM);
        } else {
            display_init_by_internal(cmd_config.screen_width,
                                     cmd_config.screen_height);
        }
    }

    if (isp_start_capture(handle)) {
        printf("StartCapture Failed\n");
        goto exit_capture;
    }

    pre_timestamp = 0;
    pre_sequence = 0;

    if(cmd_config.dump_log)
    {
        if (sem_init(&cmd_config.sema_dump, 0, 0))
        {
            printf("Init sema_dump FAILED\n");
        }
    }

    mainloop();

exit_capture:
    isp_stop_capture(handle);
    isp_close_video(handle);

    if (cmd_config.display_flag) {
        display_deinit();
    }

    if(cmd_config.dump_log)
    {
        if (sem_destroy(&cmd_config.sema_dump))
        {
            printf("Destroy sema_dump FAILED\n");
        }
    }

    if (cmd_config.save_flag) {
        fdatasync(frame_fd);
        close(frame_fd);
    }
    if (cmd_config.continusly_save_flag) {
        fdatasync(cons_frame_fd);
        close(cons_frame_fd);
    }
    fprintf(stderr, "\n");
}

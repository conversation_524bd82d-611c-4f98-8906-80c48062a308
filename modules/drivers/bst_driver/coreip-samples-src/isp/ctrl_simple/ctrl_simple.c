#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <linux/videodev2.h>
#include <pthread.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <termios.h>
#include <unistd.h>
#include "drmshow.h"

/*
   this test uses ar0231-raw(video6) as testing camera
   only view0(1920x1080, nv12) is used
 */

#define VIDEO_DEVICE "/dev/video6"
#define BUFF_COUNT 5
#define VIEW_INDEX 0

struct view_buff {
    int index;
    int length;
    void *start;
} vbuffs[BUFF_COUNT];

static int fd;
static int fdcom;
static int bri_val = 128;
static int sat_val = 128;
static int con_val = 128;
static int hue_val = 128;
static int gamma_val = 4;
static int sharpness_val = 0;
static int ydns_val = 0;
static int uvdns_val = 0;

static int xioctl(int fh, int request, void *arg)
{
    int ret;
    do {
        ret = ioctl(fh, request, arg);
    } while (-1 == ret && EINTR == errno);
    return ret;
}

static void usage_print()
{
    printf("welcome to use simple ctrl test\n");
    printf("with keyinput, v4l2 ctrl values can be adjusted\n");
    printf("brightness: e+, q-\n");
    printf("contrast:   d+, a-\n");
    printf("saturation: w+, s-\n");
    printf("hue:        c+, z-\n");
    printf("ext test:   u, i\n");
}

static int video_init(void)
{
    /* open */
    fd = open(VIDEO_DEVICE, O_RDWR | O_NONBLOCK);
    if (fd < 0) {
        printf("%s, open failed\n");
        exit(0);
    }

    /* set input */
    int view = (1 << VIEW_INDEX);
    if (xioctl(fd, VIDIOC_S_INPUT, &view) == -1) {
        printf("set view input failed\n");
        exit(0);
    }

    /* set format */
    struct v4l2_format fmt;
    memset(&fmt, 0, sizeof(fmt));
    fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    fmt.fmt.pix_mp.num_planes = 0;
    fmt.fmt.pix_mp.width = 1920;
    fmt.fmt.pix_mp.height = 1080;
    fmt.fmt.pix_mp.pixelformat = V4L2_PIX_FMT_NV12;
    fmt.fmt.pix_mp.field = V4L2_FIELD_ANY;
    if (xioctl(fd, VIDIOC_S_FMT, &fmt) == -1) {
        printf("set view0 format failed\n");
        exit(0);
    }

    /* req buf */
    struct v4l2_requestbuffers req;
    memset(&req, 0, sizeof(req));
    req.count = BUFF_COUNT;
    req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    req.memory = V4L2_MEMORY_MMAP;
    if (xioctl(fd, VIDIOC_REQBUFS, &req) == -1) {
        printf("request buffer failed\n");
        exit(0);
    }

    /* map buf */
    for (int i = 0; i < req.count; i++) {
        struct v4l2_buffer buf;
        struct v4l2_plane planes;
        memset(&buf, 0, sizeof(buf));
        buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
        buf.memory = V4L2_MEMORY_MMAP;
        buf.index = i;
        buf.length = 1;
        buf.m.planes = &planes;

        if (xioctl(fd, VIDIOC_QUERYBUF, &buf) == -1) {
            printf("query buff %d failed\n", i);
            exit(0);
        }

        vbuffs[i].index = i;
        vbuffs[i].length = buf.m.planes[0].length;
        vbuffs[i].start =
            mmap(NULL, buf.m.planes[0].length, PROT_READ | PROT_WRITE,
                 MAP_SHARED, fd, buf.m.planes[0].m.mem_offset);
        if (vbuffs[i].start == MAP_FAILED) {
            printf("mmap buff %d failed\n", i);
            exit(0);
        }
    }

    return 0;
}

static int capture_start(void)
{
    /* queue buffs */
    struct v4l2_plane planes;
    for (int i = 0; i < BUFF_COUNT; ++i) {
        struct v4l2_buffer buf;
        memset(&buf, 0, sizeof(buf));
        buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
        buf.memory = V4L2_MEMORY_MMAP;
        buf.index = i;
        buf.length = 1;
        buf.m.planes = &planes;
        if (xioctl(fd, VIDIOC_QBUF, &buf) == -1) {
            printf("queue buff %d failed\n", i);
            return -1;
        }
    }

    /* stream on */
    enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    if (xioctl(fd, VIDIOC_STREAMON, &type) == -1) {
        printf("stream on failed\n");
        return -1;
    }

    return 0;
}

static int capture_stop(void)
{
    /* stream off */
    enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    if (xioctl(fd, VIDIOC_STREAMOFF, &type) == -1) {
        printf("stream on failed\n");
        return -1;
    }

    return 0;
}

static int frame_get(int *index)
{
    if (!index) return -1;

    struct v4l2_plane planes;
    struct v4l2_buffer buf;
    memset(&buf, 0, sizeof(buf));
    buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    buf.memory = V4L2_MEMORY_MMAP;
    buf.m.planes = &planes;
    buf.length = 1;

    if (xioctl(fd, VIDIOC_DQBUF, &buf) == -1) {
        printf("dequeue buff failed");
        return -1;
    }

    *index = buf.index;

    return 0;
}

static int frame_put(int index)
{
    struct v4l2_plane planes;
    struct v4l2_buffer buf;
    memset(&buf, 0, sizeof(buf));
    buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
    buf.memory = V4L2_MEMORY_MMAP;
    buf.m.planes = &planes;
    buf.length = 1;
    buf.index = index;

    if (xioctl(fd, VIDIOC_QBUF, &buf) == -1) {
        printf("dequeue buff failed");
        return -1;
    }

    return 0;
}

static void capture_loop(void)
{
    while (1) {
        fd_set fds;
        FD_ZERO(&fds);
        FD_SET(fd, &fds);

        struct timeval tv;
        tv.tv_sec = 10;
        tv.tv_usec = 0;

        int r = select(fd + 1, &fds, NULL, NULL, &tv);
        if (-1 == r && EINTR == errno) {
            continue;
        }
        if (0 == r) {
            printf("select timeout\n");
            return;
        }

        int index;
        int primary_size;
        uint8_t *primary_buf;
        frame_get(&index);
        get_primary_plane_next_buf(&primary_buf, &primary_size);
        memcpy(primary_buf, vbuffs[index].start, 1920 * 1080 * 3 / 2);
        display_commit_by_buf(primary_buf, NULL);
        frame_put(index);
    }
}

static void video_run()
{
    capture_start();
    capture_loop();
    capture_stop();
}

static void video_deinit()
{
    for (int i = 0; i < BUFF_COUNT; ++i) {
        munmap(vbuffs[i].start, vbuffs[i].length);
    }

    close(fd);
}

#define ctrl_brightness_add() ctrl_brightness(bri_val + 1)
#define ctrl_brightness_sub() ctrl_brightness(bri_val - 1)
static int ctrl_brightness(int value)
{
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_BRIGHTNESS;
    ctrl.value = value;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    if (value > 255) bri_val = 255;
    if (value < 0) bri_val = 0;
    bri_val = value;
    return 0;
}

static int ctrl_get_brightness(void)
{
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_BRIGHTNESS;
    if (ioctl(fd, VIDIOC_G_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    // if (value > 255)
    //     bri_val = 255;
    // if (value < 0)
    //     bri_val = 0;
    // bri_val = value;
    printf("current brightness is %d\n", ctrl.value);
    return ctrl.value;
}

#define ctrl_contrast_add() ctrl_contrast(con_val + 1)
#define ctrl_contrast_sub() ctrl_contrast(con_val - 1)

#define ctrl_gamma_add() ctrl_gamma(gamma_val + 1)
#define ctrl_gamma_sub() ctrl_gamma(gamma_val - 1)

static int ctrl_contrast(int value)
{
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_CONTRAST;
    ctrl.value = value;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    if (value > 255) con_val = 255;
    if (value < 0) con_val = 0;
    con_val = value;
    return 0;
}

#define ctrl_saturation_add() ctrl_saturation(sat_val + 1)
#define ctrl_saturation_sub() ctrl_saturation(sat_val - 1)
static int ctrl_saturation(int value)
{
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_SATURATION;
    ctrl.value = value;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    if (value > 255) sat_val = 255;
    if (value < 0) sat_val = 0;
    sat_val = value;
    return 0;
}

#define ctrl_hue_add() ctrl_hue(hue_val + 1)
#define ctrl_hue_sub() ctrl_hue(hue_val - 1)
static int ctrl_hue(int value)
{
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_HUE;
    ctrl.value = value;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    if (value > 255) hue_val = 255;
    if (value < 0) hue_val = 0;
    hue_val = value;
    return 0;
}

/* The base for isp v4l2 controls. */
#define V4L2_CID_USER_ISP_BASE (V4L2_CID_USER_BASE + 0x1090)
#define V4L2_CID_ISP_TEST (V4L2_CID_USER_ISP_BASE + 0)
#define V4L2_CID_ISP_MANUAL_WB (V4L2_CID_USER_ISP_BASE + 1)
#define V4L2_CID_ISP_MANUAL_EXPOSURE (V4L2_CID_USER_ISP_BASE + 2)
#define V4L2_CID_ISP_YDNS (V4L2_CID_USER_ISP_BASE + 3)
#define V4L2_CID_ISP_UVDNS (V4L2_CID_USER_ISP_BASE + 4)

struct isp_aec_ctrl {
    uint16_t aecManualExp[3];
    uint16_t aecManualGain[3];
};
typedef struct isp_ctrl {
    uint32_t value;  // isp_set_iqinfo_t  iqVal  ,if ctrl_addr == 0  ; use value
    uint16_t aecManualExp[3];
    uint16_t aecManualGain[3];
    uint16_t manualAWBGain[3][3];  // set mannual white balance value
} isp_ctrl_t;

static int ctrl_test1(void)
{
    int i;
    struct isp_ctrl ic;
    ic.value = 113;
    for (i = 0; i < 9; i++) {
        ic.manualAWBGain[i / 3][i % 3] = i;
    }
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_TEST;
    ctrl.size = sizeof(struct isp_ctrl);
    ctrl.ptr = &ic;

    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;

    if (ioctl(fd, VIDIOC_S_EXT_CTRLS, &ctrls)) {
        printf("set ext ctrl failed\n");
        return -1;
    }

    return 0;
}

static int ctrl_awb(int val)
{
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_AUTO_WHITE_BALANCE;
    ctrl.value = val;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    return 0;
}

static int ctrl_mwb(int val)
{
    printf("%s :%d\n", __func__);
    int i;
    struct isp_ctrl ic;
    ic.value = 1;
    for (i = 0; i < 9; i++) {
        ic.manualAWBGain[i / 3][i % 3] = i * val;
    }
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_MANUAL_WB;
    ctrl.size = sizeof(struct isp_ctrl);
    ctrl.ptr = &ic;

    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;

    if (ioctl(fd, VIDIOC_S_EXT_CTRLS, &ctrls)) {
        printf("set ext ctrl failed\n");
        return -1;
    }
    return 0;
}

static int ctrl_mec(int val)
{
    int i;
    struct isp_ctrl ic;
    for (i = 0; i < 3; i++) {
        ic.aecManualExp[i] = i * val;
        ic.aecManualGain[i] = i * val;
    }
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_MANUAL_EXPOSURE;
    ctrl.size = sizeof(struct isp_ctrl);
    ctrl.ptr = &ic;

    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;

    if (ioctl(fd, VIDIOC_S_EXT_CTRLS, &ctrls)) {
        printf("set ext ctrl failed\n");
        return -1;
    }
    return 0;
}

#define ctrl_ydns_add() ctrl_ydns(ydns_val + 1)
#define ctrl_ydns_sub() ctrl_ydns(ydns_val - 1)

static int ctrl_ydns(int val)
{
    printf("%s :%d\n", __func__, val);
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_YDNS;
    ctrl.size = sizeof(__s32);
    ctrl.value = val;
    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;
    if (ioctl(fd, VIDIOC_S_EXT_CTRLS, &ctrls)) {
        printf("set ext ctrl failed\n");
        return -1;
    }
    ydns_val = val;
    return 0;
}

static int ctrl_get_ydns(void)
{
    printf("%s\n", __func__);
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_YDNS;
    ctrl.size = sizeof(__s32);
    ctrl.value = 0;

    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;
    if (ioctl(fd, VIDIOC_G_EXT_CTRLS, &ctrls)) {
        printf("get ext ctrl failed\n");
        return -1;
    }
    printf("%s\n", "recived ydns ctrls");
    int val = ctrls.controls->value;
    printf("current uvdns value is %d\n", val);
    return 0;
}

#define ctrl_uvdns_add() ctrl_uvdns(uvdns_val + 1)
#define ctrl_uvdns_sub() ctrl_uvdns(uvdns_val - 1)

static int ctrl_uvdns(int val)
{
    printf("%s :%d\n", __func__, val);
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_UVDNS;
    ctrl.size = sizeof(__s32);
    ctrl.value = val;
    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;
    if (ioctl(fd, VIDIOC_S_EXT_CTRLS, &ctrls)) {
        printf("set ext ctrl failed\n");
        return -1;
    }
    uvdns_val = val;
    return 0;
}

static int ctrl_get_uvdns(void)
{
    printf("%s\n", __func__);
    struct v4l2_ext_control ctrl;
    ctrl.id = V4L2_CID_ISP_UVDNS;
    ctrl.size = sizeof(__s32);
    ctrl.value = 0;

    struct v4l2_ext_controls ctrls;
    ctrls.ctrl_class = V4L2_CTRL_CLASS_USER;
    ctrls.count = 1;
    ctrls.controls = &ctrl;
    if (ioctl(fd, VIDIOC_G_EXT_CTRLS, &ctrls)) {
        printf("get ext ctrl failed\n");
        return -1;
    }
    printf("%s\n", "recived ydns ctrls");
    int val = ctrls.controls->value;
    printf("current uvdns value is %d\n", val);
    return 0;
}

static int ctrl_aec(void)
{
    printf("%s", __func__);
    struct v4l2_control control;
    control.value = 1;
    control.id = V4L2_CID_EXPOSURE_AUTO;
    return xioctl(fd, VIDIOC_S_CTRL, &control);
}

#define ctrl_sharpness_add() ctrl_sharpness(sharpness_val + 1)
#define ctrl_sharpness_sub() ctrl_sharpness(sharpness_val - 1)
static int ctrl_sharpness(int val)
{
    printf("%s\n", __func__);

    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_SHARPNESS;
    ctrl.value = val;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    sharpness_val = val;
    return 0;
}
static int ctrl_get_gamma()
{
    printf("%s\n", __func__);
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_GAMMA;
    if (ioctl(fd, VIDIOC_G_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    printf("get gamma value is %d\n", ctrl.value);
    gamma_val = ctrl.value;
    return 0;
}
static int ctrl_gamma(int val)
{
    printf("%s\n", __func__);

    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_GAMMA;
    ctrl.value = val;
    if (ioctl(fd, VIDIOC_S_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    gamma_val = val;
    return 0;
}

static int ctrl_get_sharpness()
{
    printf("%s\n", __func__);
    struct v4l2_control ctrl;
    ctrl.id = V4L2_CID_SHARPNESS;
    if (ioctl(fd, VIDIOC_G_CTRL, &ctrl)) {
        printf("set ctrl failed\n");
        return -1;
    }
    printf("current sharpness is %d\n", ctrl.value);
    return 0;
}

static void *ctrl_listen(void *arg)
{
    fdcom = open("/dev/tty", O_RDONLY);
    if (fdcom < 0) {
        printf("open /dev/tty failed\n");
        return NULL;
    }
    struct termios newt, oldt;
    tcgetattr(fdcom, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(fdcom, TCSANOW, &newt);
    int mwb_val = 1;
    int mec_val = 1;
    while (1) {
        char ch;
        read(fdcom, &ch, 2);
        switch (ch) {
            case 'q':
                printf("brightness -\n");
                ctrl_brightness_sub();
                break;
            case 'e':
                printf("brightness +\n");
                ctrl_brightness_add();
                break;
            case 'r':
                printf("get brightness\n");
                ctrl_get_brightness();
                break;
            case 'a':
                printf("contrast -\n");
                ctrl_contrast_sub();
                break;
            case 'd':
                printf("contrast +\n");
                ctrl_contrast_add();
                break;
            case 'w':
                printf("saturation +\n");
                ctrl_saturation_add();
                break;
            case 's':
                printf("saturation -\n");
                ctrl_saturation_sub();
                break;
            case 'z':
                printf("hue -\n");
                ctrl_hue_sub();
                break;
            case 'c':
                printf("hue +\n");
                ctrl_hue_add();
                break;
            case 'u':
                printf("test 1\n");
                ctrl_test1();
                break;
            case 'i':
                printf("get cur gamma\n");
                ctrl_get_gamma();
                break;
            case 'o':
                printf("gamma ++\n");
                ctrl_gamma_add();
                break;
            case '0':
                printf("gamma --\n");
                ctrl_gamma_sub();
                break;
            case 'p':
                printf("awb 1\n");
                ctrl_awb(1);
                break;
            case '[':
                printf("awb 0\n");
                ctrl_awb(0);
                break;
            case '1':
                printf("mwb manual white balance\n");
                ctrl_mwb(mwb_val);
                mwb_val++;
                break;
            case '2':
                printf("mec manual exposure \n");
                ctrl_mec(mec_val);
                mec_val++;
                break;
            case 'g':
                printf("set ydns++\n");
                ctrl_ydns_add();
                break;
            case 't':
                printf("set ydns--\n");
                ctrl_ydns_sub();
                break;
            case 'n':
                printf("get ydns\n");
                ctrl_get_ydns();
                break;
            case 'm':
                printf("get uvdns\n");
                ctrl_get_uvdns();
                break;
            case 'h':
                printf("set uvdns ++\n");
                ctrl_uvdns_add();
                break;
            case 'b':
                printf("set uvdns --\n");
                ctrl_uvdns_sub();
                break;
            case 'j':
                printf("aec\n");
                ctrl_aec();
                break;
            case 'l':
                printf("set sharpness --\n");
                ctrl_sharpness_sub();
                break;
            case 'k':
                printf("set sharpness ++\n");
                ctrl_sharpness_add();
                break;
            case ';':
                printf("get sharpness\n");
                ctrl_get_sharpness();
                break;
            default:
                break;
        }
    }

    tcsetattr(fdcom, TCSANOW, &oldt);
    close(fdcom);

    return NULL;
}

int main(int argc, char **argv)
{
    usage_print();
    display_init_by_internal(1920, 1080);
    video_init();
    pthread_t listener;
    pthread_create(&listener, NULL, &ctrl_listen, NULL);
    video_run();
    video_deinit();
    display_deinit();
    pthread_join(listener, NULL);

    return 0;
}

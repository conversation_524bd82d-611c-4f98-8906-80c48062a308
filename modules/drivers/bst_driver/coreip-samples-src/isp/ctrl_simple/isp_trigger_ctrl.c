#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <linux/videodev2.h>
#include <pthread.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <termios.h>
#include <unistd.h>

#define MAX_ISP_CHANNEL 12
#define MAX_CAMERA_NAME_LEN 32
#define IOC_GET_VAILED_CAMERA_INFO \
    _IOR('y', 0x41, int)                        /* Get vailed camera info */
#define IOC_ECHO_ISP_MISC _IOWR('y', 0x42, int) /* echo */
#define IOC_SET_MIPI_TRIGGER_MODE \
    _IOR('y', 0x43, int) /* Get vailed camera info */

struct trigger_info {
    int trigger_mode;
    int mipi_id;
    int internal_trigger_fps;
    int camera_trigger_gpio_port;
    int deser_trigger_gpio_port;
    int external_freq;
    int target_freq;
    int fsync_in;
    int fsync_out;
    int pulse_width;
    int fsync_source;
    int fsync_sdk;
};

struct camera_info {
    char camera_name[MAX_CAMERA_NAME_LEN];
    int camera_id;
    int is_streaming;
};

typedef struct isp_misc_device {
    struct camera_info cam_info[MAX_ISP_CHANNEL];
    unsigned int camera_num;
} isp_misc_device_t;

enum { INTERNAL_TRIGGER_MODE = 0, EXTERNAL_TRIGGER_MODE };

void readme()
{
    printf("-----Internal Example--------\n");
    printf("param 1: internal trigger mode: 0---\n");
    printf("param 2: mipi_id \n");
    printf("param 3: camera_trigger_gpio_port\n");
    printf("param 4: internal_trigger_fps\n");
    printf("ar0231 Internal Example : isp_trigger_sample 0 0 1 22\n");
    printf("imx390 Internal Example : isp_trigger_sample 0 1 0 30\n");
    printf("-----External mode:--------\n");
    printf("param 1: mipi_id External\n");
    printf("param 2: mipi_id \n");
    printf("param 3: camera_trigger_gpio_port \n");
    printf("param 4: deser_trigger_gpio_port \n");
    printf("param 5: external_freq \n");
    printf("param 6: target_freq \n");
    printf("param 7: fsync_in \n");
    printf("param 8: fsync_out \n");

    // printf("param 5: internal_trigger_fps \n");
    printf("ar0231 External Example : isp_trigger_sample 1 0 1 0\n");
}

int main(int argc, char **argv)
{
    int i;
    isp_misc_device_t misc_device;
    int fd = open("/dev/isp_misc", O_RDWR);
    if (fd == -1) {
        perror("open error\n");
        return -2;
    }
    /*--------------camera_info---------------------*/
    isp_misc_device_t recv_misc_device;
    if (-1 == ioctl(fd, IOC_GET_VAILED_CAMERA_INFO, &recv_misc_device)) {
        printf("ioctl GET_VAILED_CAMERA_INFO failed! \n");
    } else {
        printf("ioctl GET_VAILED_CAMERA_INFO success! \n");
        for (i = 0; i < recv_misc_device.camera_num; i++) {
            printf("video%d : id: %d, camera_name: %s , is_streaming: %d\n", i,
                   recv_misc_device.cam_info[i].camera_id,
                   recv_misc_device.cam_info[i].camera_name,
                   recv_misc_device.cam_info[i].is_streaming);
        }
    }
    readme();
    //     if(argc <= 1)
    //     {
    //         printf("param number vailed ! \n");
    //         readme();
    //         return 0;
    //     }

    /*--------------internal trigger---------------------*/
    if (atoi(argv[1]) == 0) {
        struct trigger_info t_info;
        /*ar0231*/
        t_info.mipi_id = atoi(argv[2]);
        t_info.camera_trigger_gpio_port = atoi(argv[3]);
        t_info.internal_trigger_fps = atoi(argv[4]);
        t_info.trigger_mode = INTERNAL_TRIGGER_MODE;

        if (-1 == ioctl(fd, IOC_SET_MIPI_TRIGGER_MODE, &t_info)) {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE failed! \n");
        } else {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE success! \n");
        }
    }
    /*--------------external trigger---ar0231------------------*/
    if (atoi(argv[1]) == 1) {
        struct trigger_info ext_info;

        ext_info.mipi_id = atoi(argv[2]);
        ext_info.camera_trigger_gpio_port = atoi(argv[3]);
        ext_info.deser_trigger_gpio_port = atoi(argv[4]);
        ext_info.external_freq = atoi(argv[5]);
        ext_info.target_freq = atoi(argv[6]);
        ext_info.fsync_in = atoi(argv[7]);
        ext_info.fsync_out = atoi(argv[8]);
        ext_info.pulse_width = atoi(argv[9]);
        ext_info.fsync_source = atoi(argv[10]);
        ext_info.trigger_mode = EXTERNAL_TRIGGER_MODE;
        ext_info.fsync_sdk = atoi(argv[11]);
        if (ext_info.external_freq > ext_info.target_freq &&
            (ext_info.external_freq % ext_info.target_freq != 0)) {
            printf("ext_info.external_freq % ext_info.target_freq != 0\n");
            return -1;
        }
        if (-1 == ioctl(fd, IOC_SET_MIPI_TRIGGER_MODE, &ext_info)) {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE failed! \n");
        } else {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE success! \n");
        }
    }

    return 0;
}

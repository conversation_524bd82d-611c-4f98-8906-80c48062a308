#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <linux/videodev2.h>
#include <pthread.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <termios.h>
#include <unistd.h>

#define MAX_ISP_CHANNEL 12
#define MAX_CAMERA_NAME_LEN 32
#define IOC_GET_VAILED_CAMERA_INFO \
    _IOR('y', 0x41, int)                        /* Get vailed camera info */
#define IOC_ECHO_ISP_MISC _IOWR('y', 0x42, int) /* echo */
#define IOC_SET_MIPI_TRIGGER_MODE \
    _IOR('y', 0x43, int) /* Get vailed camera info */

struct trigger_info {
    int trigger_mode;
    int mipi_id;
    int internal_trigger_fps;
    int camera_trigger_gpio_port;
    int deser_trigger_gpio_port;
    int external_freq;
    int target_freq;
    int fsync_in;
    int fsync_out;
};

struct camera_info {
    char camera_name[MAX_CAMERA_NAME_LEN];
    int camera_id;
    int is_streaming;
};

typedef struct isp_misc_device {
    struct camera_info cam_info[MAX_ISP_CHANNEL];
    unsigned int camera_num;
} isp_misc_device_t;

enum { INTERNAL_TRIGGER_MODE = 0, EXTERNAL_TRIGGER_MODE };

int main(int argc, char **argv)
{
    int i;
    isp_misc_device_t misc_device;
    int fd = open("/dev/isp_misc", O_RDWR);
    if (fd == -1) {
        perror("open error\n");
        return -2;
    }
    printf("open success! \n");

    misc_device.camera_num = 10;
    if (-1 == ioctl(fd, IOC_ECHO_ISP_MISC, &misc_device)) {
        printf("ioctl ECHO failed! \n");
    }
    /*-----------------------------------*/
    isp_misc_device_t recv_misc_device;
    if (-1 == ioctl(fd, IOC_GET_VAILED_CAMERA_INFO, &recv_misc_device)) {
        printf("ioctl GET_VAILED_CAMERA_INFO failed! \n");
    } else {
        printf("ioctl GET_VAILED_CAMERA_INFO success! \n");
    }

    /*--------------internal trigger---------------------*/
    if (atoi(argv[1]) == 0) {
        struct trigger_info t_info;
        t_info.mipi_id = 2;
        t_info.camera_trigger_gpio_port = 0;
        t_info.internal_trigger_fps = 30;
        t_info.trigger_mode = INTERNAL_TRIGGER_MODE;

        if (-1 == ioctl(fd, IOC_SET_MIPI_TRIGGER_MODE, &t_info)) {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE failed! \n");
        } else {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE success! \n");
        }
    }
    /*--------------external trigger---ar0231------------------*/
    if (atoi(argv[1]) == 1) {
        struct trigger_info ext_info;
        ext_info.mipi_id = 2;
        ext_info.camera_trigger_gpio_port = 1;
        ext_info.deser_trigger_gpio_port = 0;
        ext_info.internal_trigger_fps = 22;
        ext_info.trigger_mode = EXTERNAL_TRIGGER_MODE;

        if (-1 == ioctl(fd, IOC_SET_MIPI_TRIGGER_MODE, &ext_info)) {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE failed! \n");
        } else {
            printf("ioctl IOC_SET_MIPI_TRIGGER_MODE success! \n");
        }
    }

    for (i = 0; i < recv_misc_device.camera_num; i++) {
        printf("video%d : id: %d, camera_name: %s , is_streaming: %d\n", i,
               recv_misc_device.cam_info[i].camera_id,
               recv_misc_device.cam_info[i].camera_name,
               recv_misc_device.cam_info[i].is_streaming);
    }

    return 0;
}
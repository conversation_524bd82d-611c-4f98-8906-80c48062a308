#include <iostream>
#include <fstream> 
#include <thread>
#include <vector>
#include <unistd.h>
#include <unordered_map>
#include <stdio.h>

#include <opencv2/opencv.hpp>

#include <autoplt/include/ADSNode.h>
#include <autoplt/include/ADSTime.h>

//#include <modules/sensor_hal/can_hal/can_hal.h>

#include <modules/sensor/sensor_abstraction/sensor_abstraction.h>

#include <modules/sensor/sensor_manager/config/ss_request.pb.h>
#include <modules/sensor/sensor_manager/config/sm_service_config.pb.h>
#include <modules/sensor/sensor_manager/config/sm_general_config.pb.h>
#include <modules/sensor/sensor_manager/config/ads_gnssconfig.pb.h>

#include <modules/sensor/sensor_manager/tools/sm_tools.h>

// 包含所有支持的 Interface
#include <modules/sensor_hal/interface/IpcInterface.h>

#include <modules/sensor/sensor_manager/tools/sm_tools.h>

using namespace std;
using namespace apollo;
using namespace apollo::cyber;
using namespace autoplt;
using namespace autoplt::sensorservice;

using namespace autoplt::sensor_manager::hal;

bool bRecived = false;

int saved = 0;
int delta = 0;

using namespace chrono;

std::chrono::time_point<std::chrono::steady_clock> last_time;

std::string ipcamera_channel_name = "/ipcamera/front/1";
std::string gnss_channel_name = "/ipcamera/front/1";

autoplt::tzc::TZCHelper<osi3::CameraSensorView> * tzcHelper; //(ipcamera_channel_name);

void CamMessageCallback(const std::shared_ptr<osi3::CameraSensorView> &msg) {
	// auto id = msg->mutable_view_configuration()->sensor_id().value();
	// std::cout << "sensor id " << id << std::endl;
    std::cout << "/ipcamera/front/1\n";
	unsigned int width = msg->mutable_view_configuration()->number_of_pixels_horizontal();
	std::cout << "client width " << width << std::endl;
	unsigned int height = msg->mutable_view_configuration()->number_of_pixels_vertical();
	std::cout << "client height " << height << std::endl;


    if(msg->shm_size() > 0) {
		char* msg_addr = nullptr;
		// 调用tzc接口，获取数据所在地址
		msg_addr = tzcHelper->GetDataPtrFromTZC(msg);

		std::cout << "tzc " << msg->shm_size() << std::endl;

        if(saved < 100 && saved % 10 == 0) {
            cv::Mat yuvImg;
            cv::Mat rgbImg(height, width,  CV_8UC3);
            yuvImg.create(height * 3 / 2, width, CV_8UC1);
            memcpy(yuvImg.data, const_cast<char *>(msg_addr), height * 3 / 2 * width);
            cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_I420);

            string filename = "hai_" + std::to_string(saved) + ".jpg";
            cv::imwrite(filename,  rgbImg);		
        }  

		tzcHelper->ReleaseReadLock();
    } else {
        if(saved < 100 && saved % 10 == 0) {
            cv::Mat yuvImg;
            cv::Mat rgbImg(height, width,  CV_8UC3);
            yuvImg.create(height * 3 / 2, width, CV_8UC1);
            memcpy(yuvImg.data, const_cast<char *>(msg->image_data().data()), height * 3 / 2 * width);
            cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_I420);

            string filename = "hai_" + std::to_string(saved) + ".jpg";
            cv::imwrite(filename,  rgbImg);		
        }        
    }

   
    auto now = std::chrono::steady_clock::now();    	
    auto tick = duration_cast<chrono::milliseconds>(now - last_time).count();
    if(tick >= 1000) {
        printf("fps[%.1f]\n", delta/(tick/1000.0));
        delta = 0;
        last_time = now;
    }

    saved++;
    delta++;

	bRecived = true;
}
void CamMessageCallback2(const std::shared_ptr<osi3::CameraSensorView> &msg) {
	// auto id = msg->mutable_view_configuration()->sensor_id().value();
	// std::cout << "sensor id " << id << std::endl;
    std::cout << "/ipcamera/front/2";
	unsigned int width = msg->mutable_view_configuration()->number_of_pixels_horizontal();
	std::cout << "client width " << width << std::endl;
	unsigned int height = msg->mutable_view_configuration()->number_of_pixels_vertical();

	// cv::Mat yuvImg;
	// cv::Mat rgbImg(height, width,  CV_8UC3);
	// yuvImg.create(height * 3 / 2, width, CV_8UC1);
	// memcpy(yuvImg.data, const_cast<char *>(msg->image_data().data()), height * 3 / 2 * width);
	// cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_I420);
	// if(saved < 10000 && saved % 20 == 0) {
	// 	string filename = std::to_string(id) + "_" + std::to_string(saved) + ".jpg";
	// 	cv::imwrite(filename,  rgbImg);		
	// }	
}

void GnssMessageCallback(const std::shared_ptr<autoplt::GnssInsLocation> &msg){
    cout << "[GNSS]timestamp: second = " << msg->timestamp().seconds() << " nanos = " << msg->timestamp().nanos() << endl;
    cout << "[GNSS]positioning status: " << msg->gnssposstatus() << endl;
    cout << "[GNSS]longitude: " << std::setprecision(12) << msg->longitude() << endl;
    cout << "[GNSS]latitude: " << std::setprecision(12) << msg->latitude() << endl;
}

//int main(int argc, char * argv[]) {
int main(int argc, char **argv) {

    ADSNode::Init("SensorManagerServiceTest");


    using namespace autoplt::sensor_manager;
    using autoplt::sensor_manager::getSensorServiceName;

    auto serverName = getSensorServiceName();
    if(serverName.empty()) {
        std::cout << ("FATAL ERROR: cannot get server name");
        return -1;
    } else {
        std::cout << "get server name " << serverName << std::endl;
    }

    auto nodeName = "Test_Node"+std::to_string(apollo::cyber::Time::Now().ToNanosecond());

    auto sm_node_ = std::make_shared<autoplt::ADSNode>(nodeName);

    std::cout << "create node\n ";


    auto client = sm_node_->CreateClient<SensorServiceRequest, SensorServiceResponse>(serverName);

    // if(!client->WaitForService(std::chrono::duration<int64_t, std::milli>(1000*10))) {
    //     std::cout << "client: service may not ready\n ";
    //     return -1;
    // }

    auto driver_msg = std::make_shared<SensorServiceRequest>();
    driver_msg->set_config_path("/usr/bin/sensor_all_in_one.pb.txt");

    std::cout << "create CreateClient\n ";


    std::cout << "SendRequest\n ";

    int i = 0;
    std::shared_ptr<SensorServiceResponse> res;
    for(; i < 10; i++) {
        res = client->SendRequest(driver_msg);

        if (res != nullptr) {
            std::cout << "client: response: " << res->ShortDebugString() << " \n";
            std::cout << res->parse_status() << std::endl;
            break;
        } else {
            AINFO << "client: service may not ready.";
            std::cout << "client: service may not ready\n ";
        }
    }
    if(i == 10) {
        return -1;
    }

    last_time = std::chrono::steady_clock::now();

    cout << "Init TZC: " << res->results(0).channel() << endl;

	tzcHelper = new autoplt::tzc::TZCHelper<osi3::CameraSensorView>(res->results(0).channel());
    // 初始化TZC对象
	tzcHelper->InitTZC(32);

	auto listener = sm_node_->CreateReader<osi3::CameraSensorView>(
	 		        res->results(0).channel(), CamMessageCallback);

	// sm_node_->CreateReader<osi3::CameraSensorView>(
	//  		        "/ipcamera/front/2", CamMessageCallback2);   

    cout << "result.1: channel" << res->results(1).channel() << endl;                  

    sm_node_->CreateReader<autoplt::GnssInsLocation>(
	 		        res->results(1).channel(), GnssMessageCallback); 

    std::this_thread::sleep_for(std::chrono::milliseconds(10 * 1000));

    return 0;
}

#ifndef DECODER_UTILITY_H_
#define DECODER_UTILITY_H_

#include <memory>
#include <sys/time.h>

extern "C"
{
#include <libavformat/avformat.h>
}

int FindStartcode3(unsigned char *buffer);
int FindStartcode4(unsigned char *buffer);
int FindFirstNALU(unsigned char * buffer, int packetSize, int* startcodeLength);
int ParseVideoType(const char* url);
uint64_t ConvertStr2uint_64(const unsigned char* StrNumber);
void ReleaseFormatCtx(AVFormatContext** ifmt_ctx);

#endif
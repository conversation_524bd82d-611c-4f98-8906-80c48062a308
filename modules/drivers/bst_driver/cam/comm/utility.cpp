#include "utility.h"

int FindStartcode3(unsigned char *buffer)
{
	return (buffer[0]==0 && buffer[1]==0 && buffer[2]==1);
}

int FindStartcode4(unsigned char *buffer)
{
	return (buffer[0]==0 && buffer[1]==0 && buffer[2]==0 && buffer[3]==1);
}

int FindFirstNALU(unsigned char * buffer, int packetSize, int* startcodeLength) {
	int pos = 0;
	int found = 0;
	
    int info2 = 0;
    int info3 = 0;
	
	int startcode_len = 0;
	
	while (!found) {
		if(pos + 4 <= packetSize) {
			info3 = FindStartcode4(&buffer[pos]);//判断是否为0x00000001
		}
        if(info3 != 1)
        {
            info2 = FindStartcode3(&buffer[pos]);//判断是否为0x000001
            if (info2)
            {
                startcode_len = 3;
            }
        }
        else
        {
            startcode_len = 4;
        }

        found = (info2 == 1 || info3 == 1);
        if (pos+4 > packetSize)  return -1;
	
        if (!found) pos++;		
	}
	
    if (startcodeLength != NULL)   *startcodeLength = startcode_len;

    return pos;	
}

uint64_t ConvertStr2uint_64(const unsigned char* StrNumber)
{
    if (StrNumber == NULL) return 0;

    uint64_t number = 0;
    while ((*StrNumber) != '\0')
    {
        number = number * 10 + ((*StrNumber) - '0');
        StrNumber++;
    }

    return number;
}

int ParseVideoType(const char* url)
{
    AVFormatContext* m_fmtctx=NULL;
    int ret = avformat_open_input(&m_fmtctx, url, NULL, NULL);
    if (ret != 0)
    {
        char buf[1024] = "";
        av_strerror(ret, buf, 1024);
        printf("Couldn't open file %s, ret: %d, erro message: %s\n", url, ret, buf);
        return -1;
    }

    av_dump_format(m_fmtctx, 0, url, 0);

    int m_videoStream = -1;
    for (unsigned int i = 0; i < m_fmtctx->nb_streams; i++)
    {
        if (m_fmtctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
        {
            m_videoStream = i;
            break;
        }
    }
    if (m_videoStream == -1)
    {
        printf("no video stream.\n");
        if (m_fmtctx != NULL) avformat_close_input(&m_fmtctx);
        return -1;
    }

    enum AVCodecID CurCodeID = m_fmtctx->streams[m_videoStream]->codecpar->codec_id;
    printf("m_fmtctx->codec_id: %d.\n", CurCodeID);

    if (m_fmtctx != NULL) avformat_close_input(&m_fmtctx);

    if (CurCodeID == AV_CODEC_ID_H264)       return 264;
    else if (CurCodeID == AV_CODEC_ID_HEVC)  return 265;
    else if (CurCodeID == AV_CODEC_ID_MJPEG) return 7;
    else
    {
        printf("stream type is not right, it should be one of the following choice: h264, h265 or mjpeg.\n");
        return -1;
    }
}

void ReleaseFormatCtx(AVFormatContext** ifmt_ctx)
{
    avformat_close_input(ifmt_ctx);
    avformat_free_context(*ifmt_ctx);
    avformat_network_deinit();
}
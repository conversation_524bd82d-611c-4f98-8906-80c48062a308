#include <iostream>
#include <fstream>
#include <thread>
#include <vector>
#include <unistd.h>
#include <unordered_map>
#include <stdio.h>

#include <autoplt/include/ADSNode.h>
#include <autoplt/include/ADSTime.h>
#include <autoplt/include/tzc_helper.h>
#include "thirdparty/recommend_protocols/drivers/proto/camera.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"

//#include <modules/sensor_hal/can_hal/can_hal.h>
#include <modules/sensor/sensor_abstraction/sensor_abstraction.h>

#include <modules/sensor/sensor_manager/config/ss_request.pb.h>
#include <modules/sensor/sensor_manager/config/sm_service_config.pb.h>
#include <modules/sensor/sensor_manager/config/sm_general_config.pb.h>
#include <modules/sensor/sensor_manager/config/ads_gnssconfig.pb.h>

#include <modules/sensor/sensor_manager/tools/sm_tools.h>

// 包含所有支持的 Interface
#include <modules/sensor_hal/interface/IpcInterface.h>

#include <opencv2/opencv.hpp>

using namespace std;
using namespace std;
using namespace apollo;
using namespace apollo::cyber;
using namespace autoplt;
using namespace autoplt::sensorservice;

using namespace autoplt::sensor_manager::hal;

bool bRecived = false;
int saved = 0;
using rainbowdash::drivers::Image;

std::string channel_name = "/mipi_camera/front/1";
std::shared_ptr<ADSNode> cam_talker_node = nullptr;
std::shared_ptr <Writer<Image>> pWriterImage = nullptr;

autoplt::tzc::TZCHelper<osi3::CameraSensorView> * tzcHelper; //(channel_name);

cv::Mat getMatFromNV12(char * buf, int width, int height) {
	cv::Mat yuvImg;
	cv::Mat rgbImg(height, width,  CV_8UC3);
	yuvImg.create(height * 3 / 2, width, CV_8UC1);
	memcpy(yuvImg.data, buf, height * 3 / 2 * width);
	cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_NV12);

	return rgbImg;
}

void CamMessageCallback(const std::shared_ptr<osi3::CameraSensorView> &msg) {
	auto id = msg->mutable_view_configuration()->sensor_id().value();
	//std::cout << "sensor id " << id << std::endl;
	std::cout << "/mipi_camera/front/1\n";
	unsigned int width = msg->mutable_view_configuration()->number_of_pixels_horizontal();
	std::cout << "client width " << width << std::endl;
	unsigned int height = msg->mutable_view_configuration()->number_of_pixels_vertical();
	std::cout << "client height " << height << std::endl;

	auto imgmsg = std::make_shared<Image>();
	std::cout << "init cyber image msg" << std::endl;
	imgmsg->set_height(height);
	imgmsg->set_width(width);
	std::cout << "set w h done!" << std::endl;
	if (pWriterImage == nullptr)
	{
		std::cout << "nullptr!" << std::endl;
	}
	
	if(!pWriterImage->Write(imgmsg))
	{
	AWARN  << "[Example intra com] Write msg failed.";
	}
	std::cout << "write cam through cyber" << std::endl;

	
	if(msg->shm_size() > 0) {
		char* msg_addr = nullptr;
		// 调用tzc接口，获取数据所在地址
		msg_addr = tzcHelper->GetDataPtrFromTZC(msg);

		std::cout << "tzc " << msg->shm_size() << std::endl;
		cv::Mat rgbImg = getMatFromNV12(msg_addr, width, height);

		if (10<=saved && saved<=170 && saved % 10 == 0) {
			string filename = "/userdata/" + std::to_string(id) + "_" + std::to_string(saved) + ".jpg";
			cv::imwrite(filename, rgbImg);	  

			bRecived = true;
		}
		saved++;

		tzcHelper->ReleaseReadLock();
	} else {

		cv::Mat rgbImg = getMatFromNV12(const_cast<char *>(msg->image_data().data()), width, height);

		if (10<=saved && saved<=170 && saved % 10 == 0) {
			string filename = "/userdata/" + std::to_string(id) + "_" + std::to_string(saved) + ".jpg";
			cv::imwrite(filename, rgbImg);	  

			bRecived = true;
		}
		saved++;
	}

}

//int main(int argc, char * argv[]) {
int main(int argc, char **argv) {

	ADSNode::Init("SensorManagerServiceTest");

	using autoplt::sensor_manager::getSensorServiceName;

	auto serverName = getSensorServiceName();
	if(serverName.empty()) {
		std::cout << ("FATAL ERROR: cannot get server name");
		return -1;
	}
	std::cout << "serverName: " << serverName << std::endl;

	auto sm_node_ = std::make_shared<autoplt::ADSNode>("Test_Node"+std::to_string(apollo::cyber::Time::Now().ToNanosecond()));

	auto client = sm_node_->CreateClient<SensorServiceRequest, SensorServiceResponse>(serverName);
	auto driver_msg = std::make_shared<SensorServiceRequest>();
	driver_msg->set_config_path("/usr/bin/mipi_camera_config_test.pb.txt");

    int i = 0;
    std::shared_ptr<SensorServiceResponse> res;
	for(; i < 10; i++) {
		res = client->SendRequest(driver_msg);

		if (res != nullptr) {
			std::cout << "client: response: " << res->ShortDebugString() << " \n";
			std::cout << res->parse_status() << std::endl;
			break;
		} else {
			std::cout << "client: service may not ready.\n";
		}		
	}
    if(i == 10) {
        return -1;
    }


	sleep(1);


	// 初始化TZC对象
	tzcHelper = new autoplt::tzc::TZCHelper<osi3::CameraSensorView>(res->results(0).channel());
	tzcHelper->InitTZC(32);

	if (cam_talker_node == nullptr)
	{
		cam_talker_node = std::make_shared<ADSNode>("cam_talker");
	}

	if (pWriterImage == nullptr)
	{	
		std::cout << "pWriterImage == nullptr" << std::endl;
		std::string img_writer_name = "/mipi_camera/camtaker";
		pWriterImage = cam_talker_node->CreateWriter<Image>(img_writer_name);
	}
	

	auto listener = sm_node_->CreateReader<osi3::CameraSensorView>(
					res->results(0).channel(), CamMessageCallback);

	std::this_thread::sleep_for(std::chrono::milliseconds(1000*20));

	using autoplt::sensor_manager::getSensorCloseServiceName;
	auto closeServerName = getSensorCloseServiceName();
	auto clientClose = sm_node_->CreateClient<SensorServiceCloseRequest, SensorServiceCloseResponse>(closeServerName);

	std::cout << "closeServerName " << closeServerName << " \n";

	// 关闭打开过的传感器
	auto close_driver_msg = std::make_shared<SensorServiceCloseRequest>();
	for(int i = 0; i < res->results_size(); i++) {
		const auto & msg = res->results(i);
		std::cout << " msg.channel() is " << msg.channel() << std::endl;
		close_driver_msg->set_channel(msg.channel());
		clientClose->SendRequest(close_driver_msg);
	}

	return 0;
}


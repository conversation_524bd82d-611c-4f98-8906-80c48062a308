#include <iostream>
#include <fstream>
#include <thread>
#include <vector>
#include <unistd.h>
#include <unordered_map>
#include <stdio.h>
#include <functional>

#include <autoplt/include/ADSNode.h>
#include <autoplt/include/ADSTime.h>
#include <autoplt/include/tzc_helper.h>
#include "thirdparty/recommend_protocols/drivers/proto/camera.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"

//#include <modules/sensor_hal/can_hal/can_hal.h>
#include <modules/sensor/sensor_abstraction/sensor_abstraction.h>

#include <modules/sensor/sensor_manager/config/ss_request.pb.h>
#include <modules/sensor/sensor_manager/config/sm_service_config.pb.h>
#include <modules/sensor/sensor_manager/config/sm_general_config.pb.h>
#include <modules/sensor/sensor_manager/config/ads_gnssconfig.pb.h>

#include <modules/sensor/sensor_manager/tools/sm_tools.h>

// 包含所有支持的 Interface
#include <modules/sensor_hal/interface/IpcInterface.h>

#include <opencv2/opencv.hpp>


#include <unistd.h>
#include <sys/mman.h>
#include <filesystem>
#include <algorithm>
#include <umm/mem_allocator.h>
#include <umm/ion_allocator.h>
#include "utility.h"
#include "v0/hanhai/autoplt/bmpp/BmppProxy.hpp"
#include "v0/hanhai/autoplt/bmpp/BmppTypes.hpp"


using namespace std;
using namespace apollo;
using namespace apollo::cyber;
using namespace autoplt;
using namespace autoplt::sensorservice;

using namespace autoplt::sensor_manager::hal;
using rainbowdash::drivers::Image;
using rainbowdash::drivers::CompressedImage;

bool bRecived = false;
int saved = 0;
int FrameWidth = 3840;
int FrameHeight = 2160;
bool cam_terminate = false;
const int CAM_NUM = 1;

using namespace v0::hanhai::autoplt::bmpp;
// static std::vector<std::shared_ptr<BmppProxy>> BmppImplObj;
// std::vector<std::shared_ptr<Writer<CompressedImage>>> pWriterImage;
std::shared_ptr<ADSNode> cam_talker_node = nullptr;


struct CamDriver
{
	std::shared_ptr<Writer<CompressedImage>> pWriterImage = nullptr;
	std::shared_ptr<BmppProxy> BmppImplObj;
	autoplt::tzc::TZCHelper<osi3::CameraSensorView> * tzcHelper;
	uint64_t writesqe = 0;
	uint64_t readseq = 0;
};

static CamDriver Cam[CAM_NUM];


cv::Mat getMatFromNV12(char * buf, int width, int height) {
	cv::Mat yuvImg;
	cv::Mat rgbImg(height, width,  CV_8UC3);
	yuvImg.create(height * 3 / 2, width, CV_8UC1);
	memcpy(yuvImg.data, buf, height * 3 / 2 * width);
	cv::cvtColor(yuvImg, rgbImg, cv::COLOR_YUV2BGR_NV12);

	return rgbImg;
}

void CamMessageCallback(const std::shared_ptr<osi3::CameraSensorView> &msg, int i) {
	auto id = msg->mutable_view_configuration()->sensor_id().value();
	//std::cout << "sensor id : " << id << " i : " << i << std::endl;
	unsigned int width = msg->mutable_view_configuration()->number_of_pixels_horizontal();
	unsigned int height = msg->mutable_view_configuration()->number_of_pixels_vertical();
	//std::cout << "client width " << width << " client height " << height << std::endl;
	Cam[i].readseq ++;
	if(Cam[i].readseq % 15 != 0){
		return;
	}
	
	if(msg->shm_size() > 0) {
		char* msg_addr = nullptr;
		// 调用tzc接口，获取数据所在地址
		msg_addr = Cam[i].tzcHelper->GetDataPtrFromTZC(msg);

		//std::cout << "tzc " << msg->shm_size() << std::endl;
		//printf("push yuv image by user-defined buffer\n");

		BmppTypes::FrameBuffer frame_buffer;
		BmppTypes::RetCode ret = BmppTypes::RetCode::BST_OK;
		size_t frame_size = FrameWidth * FrameHeight * 3 / 2;

		frame_buffer.data = (uint8_t*)msg_addr;
		frame_buffer.size = frame_size;
		Cam[i].BmppImplObj->EncodePutFrameByUsrBuff(frame_buffer, ret);

		//printf("encoding done\n");
		Cam[i].tzcHelper->ReleaseReadLock();
	} else {

		cv::Mat rgbImg = getMatFromNV12(const_cast<char *>(msg->image_data().data()), width, height);

		if (10<=saved && saved<=170 && saved % 10 == 0) {
			string filename = "/userdata/" + std::to_string(id) + "_" + std::to_string(saved) + ".jpg";
			cv::imwrite(filename, rgbImg);	  

			bRecived = true;
		}
		saved++;
	}

}

void WriteEncodePacket(int i)
{
	auto msg = std::make_shared<CompressedImage>();
  	auto header = msg->mutable_header();

	header->set_timestamp(apollo::cyber::Time::Now().ToNanosecond());
	header->set_module_name("cam_node");
	header->set_version(0);
	msg->set_format("jpeg");

	
    BmppTypes::BmppPacket stream_packet;
    BmppTypes::RetCode ret;
    uint64_t FrameCounter = 0;
    while(!cam_terminate)
    {
        Cam[i].BmppImplObj->EncodeGetPacket(stream_packet, ret);
        if (ret == BmppTypes::RetCode::BST_ERR_TERMINATE) break;
        else if (ret == BmppTypes::RetCode::BST_ERR_RETRY)
        {
            usleep(1);
            continue;
        }

        if (ret == BmppTypes::RetCode::BST_OK)
        {
            FrameCounter++;
			msg->set_data(stream_packet.data, stream_packet.size);
			std::cout  << " cam_id : " << i << " writesqe : " <<  Cam[i].writesqe << "  jpeg size : " << stream_packet.size << " time " << apollo::cyber::Time::Now().ToNanosecond() << std::endl;
            Cam[i].BmppImplObj->EncodeReleasePacket(stream_packet, ret);


			// if ( Cam[i].writesqe % 100 == 0)
			// {
			//     std::string file_name = std::to_string(i) +"_"+ std::to_string( Cam[i].writesqe)+ "bmpp.jpg" ;
			// 	std::ofstream File(file_name);

			// 	// Write to the file
			// 	//File << "Files can be tricky, but it is fun enough!";
			// 	File.write((char *)stream_packet.data,  stream_packet.size);

			// 	// Close the file
			// 	File.close();
			// }

            //printf("Success to release output buffer: %p, %d\n", stream_packet.data, (int)ret);

			if(!Cam[i].pWriterImage->Write(msg))
			{
			AWARN  << "[Example intra com] Write msg failed.";
			}
			Cam[i].writesqe++;
			//std::cout << "write cam through cyber" << std::endl;
        }
        usleep(1);
    }
}

bool InitEncodeObj()
{
    BmppTypes::EncodeParam param;
    param.encodeType = BmppTypes::CodecType::BST_CODEC_MJPEG;
    param.yuvFormat  = BmppTypes::YUVType::BST_FORMAT_YUV420SP;
    param.width      = FrameWidth;
    param.height     = FrameHeight;
    param.frameRate  = 30;

	for (size_t i = 0; i < CAM_NUM; i++)
	{
		BmppTypes::RetCode ret;
		Cam[i].BmppImplObj = std::make_shared<BmppProxy>();
		Cam[i].BmppImplObj->EncodeInit(param, ret);
		if (ret != BmppTypes::RetCode::BST_OK) return false;
	}

    return true;
}

void CamMessageCallback1(const std::shared_ptr<osi3::CameraSensorView> &msg) {
	auto id = msg->mutable_view_configuration()->sensor_id().value();
	std::cout << "sensor id " << id << std::endl;
	unsigned int width = msg->mutable_view_configuration()->number_of_pixels_horizontal();
	std::cout << "client width " << width << std::endl;
	unsigned int height = msg->mutable_view_configuration()->number_of_pixels_vertical();
	std::cout << "client height " << height << std::endl;

}

//int main(int argc, char * argv[]) {
int main(int argc, char **argv) {

	ADSNode::Init("SensorManagerServiceTest");

	using autoplt::sensor_manager::getSensorServiceName;

	auto serverName = getSensorServiceName();
	if(serverName.empty()) {
		std::cout << ("FATAL ERROR: cannot get server name");
		return -1;
	}
	std::cout << "serverName: " << serverName << std::endl;

	auto sm_node_ = std::make_shared<autoplt::ADSNode>("Test_Node"+std::to_string(apollo::cyber::Time::Now().ToNanosecond()));

	auto client = sm_node_->CreateClient<SensorServiceRequest, SensorServiceResponse>(serverName);
	auto driver_msg = std::make_shared<SensorServiceRequest>();
	driver_msg->set_config_path("/usr/bin/mega_mipi_camera_config.pb.txt");

    int i = 0;
    std::shared_ptr<SensorServiceResponse> res;
	for(; i < 10; i++) {
		res = client->SendRequest(driver_msg);

		if (res != nullptr) {
			std::cout << "client: response: " << res->ShortDebugString() << " \n";
			std::cout << res->parse_status() << std::endl;
			break;
		} else {
			std::cout << "client: service may not ready.\n";
		}		
	}
    if(i == 10) {
        return -1;
    }


	sleep(1);

	for (size_t i = 0; i < CAM_NUM; i++)
	{
		// 初始化TZC对象
		Cam[i].tzcHelper = new autoplt::tzc::TZCHelper<osi3::CameraSensorView>(res->results(i).channel());
		Cam[i].tzcHelper->InitTZC(32);
	}

	if (cam_talker_node == nullptr)
	{
		cam_talker_node = std::make_shared<ADSNode>("cam_talker");
	}

	for (size_t i = 0; i < CAM_NUM; i++)
	{
		if (Cam[i].pWriterImage == nullptr)
		{	
			std::string img_writer_name = "/mipi_camera/cam"+std::to_string(i);
			Cam[i].pWriterImage = cam_talker_node->CreateWriter<CompressedImage>(img_writer_name);
			std::cout << "channel : " << img_writer_name << std::endl;
		}
	}
	

	
	if (!InitEncodeObj()){
		std::cout << "InitEncodeObj failed!" << std::endl;
	}
    printf("Success to init encode object\n");

	std::thread get_data_thread[CAM_NUM];
	for (size_t i = 0; i < CAM_NUM; i++)
	{
		get_data_thread[i] = std::thread(WriteEncodePacket,i);
	}
	
	std::shared_ptr<Reader<osi3::CameraSensorView>> readers[CAM_NUM];
	
	for (size_t i = 0; i < CAM_NUM; i++)
	{
		auto CamCallback = std::bind(CamMessageCallback, std::placeholders::_1, i);
		readers[i] = sm_node_->CreateReader<osi3::CameraSensorView>(
					res->results(i).channel(), CamCallback);
		// readers[i] = sm_node_->CreateReader<osi3::CameraSensorView>(
		// 	res->results(i).channel(), CamMessageCallback1);
		std::cout << "create reader for  : " << res->results(i).channel() << std::endl;
	}
	
	// auto listener = sm_node_->CreateReader<osi3::CameraSensorView>(
	// 				res->results(0).channel(), CamMessageCallback);

	apollo::cyber::WaitForShutdown();

	using autoplt::sensor_manager::getSensorCloseServiceName;
	auto closeServerName = getSensorCloseServiceName();
	auto clientClose = sm_node_->CreateClient<SensorServiceCloseRequest, SensorServiceCloseResponse>(closeServerName);

	std::cout << "closeServerName " << closeServerName << " \n";

	// 关闭打开过的传感器
	auto close_driver_msg = std::make_shared<SensorServiceCloseRequest>();
	for(int i = 0; i < res->results_size(); i++) {
		const auto & msg = res->results(i);
		std::cout << " msg.channel() is " << msg.channel() << std::endl;
		close_driver_msg->set_channel(msg.channel());
		clientClose->SendRequest(close_driver_msg);
	}

	cam_terminate = true;
	for (size_t i = 0; i < CAM_NUM; i++)
	{
		if (get_data_thread[i].joinable())
		{
			get_data_thread[i].join();
		}
	}
    printf("encode image complete\n");

	return 0;
}


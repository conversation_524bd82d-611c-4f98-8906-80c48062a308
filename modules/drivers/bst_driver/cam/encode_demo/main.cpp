#include <unistd.h>
#include <string.h>
#include <iostream>
#include <unordered_map>
#include "v0/hanhai/autoplt/bmpp/BmppTypes.hpp"

using namespace v0::hanhai::autoplt::bmpp;

extern void MainLoop();

void ShowUsage()
{
    std::cout << "encode usage: \n";
    std::cout << "-i input_path\n";
    std::cout << "-o output_format(h264/h265/MJPEG)\n";
    std::cout << "-c input YUV format(YUV420/YUV420SP/YVU420SP)\n";
    std::cout << "-w input_image_width\n";
    std::cout << "-h input_image_height\n";
    std::cout << "-t input_buffer_type(0/1/2 means user_buffer/codec_buffer/user_dma_buffer)\n";
}

char InputPath[256] = "/userdata/encode/intput_image/";
char SavePath[256]  = "/userdata/encode/output_video/";
BmppTypes::YUVType InputFmt   = BmppTypes::YUVType::BST_FORMAT_YUV420SP;
BmppTypes::CodecType OutputFmt = BmppTypes::CodecType::BST_CODEC_HEVC;
int FrameWidth  = 0;
int FrameHeight = 0;
int BuffType    = 0;

const char CmdOption[] = "i:o:c:w:h:t:";

const std::unordered_map<int, std::string> BuffTypeMap = {{0, "user buffer"}, {1, "codec buffer"}, {2, "user dma buffer"}};

extern const std::unordered_map<std::string, BmppTypes::YUVType>    YuvTypeMap = {{"YUV420",   BmppTypes::YUVType::BST_FORMAT_YUV420P},
                                                                                  {"YUV420SP", BmppTypes::YUVType::BST_FORMAT_YUV420SP},
                                                                                  {"YVU420SP", BmppTypes::YUVType::BST_FORMAT_YVU420SP},
                                                                                 };

extern const std::unordered_map<std::string, BmppTypes::CodecType>  VideoTypeMap = {{"h264",  BmppTypes::CodecType::BST_CODEC_H264},
                                                                                    {"h265",  BmppTypes::CodecType::BST_CODEC_HEVC},
                                                                                    {"MJPEG", BmppTypes::CodecType::BST_CODEC_MJPEG},
                                                                                   };

extern const std::unordered_map<BmppTypes::CodecType, std::string>  FileExtMap = {{BmppTypes::CodecType::BST_CODEC_H264,  ".h264"},
                                                                                  {BmppTypes::CodecType::BST_CODEC_HEVC,  ".h265"},
                                                                                  {BmppTypes::CodecType::BST_CODEC_MJPEG, ".jpg"}
                                                                                 };

bool ParseCmdOption(int argc, char** argv)
{
    int ch = 0;
    char output_fmt[16];
    char yuv_type[16];
    while (ch != -1)
    {
        ch = getopt(argc, argv, CmdOption);
        if (ch == -1) break;

        switch (ch)
        {
        case 'i':
            strncpy(InputPath, optarg, sizeof(InputPath)/sizeof(char)-1);
            break;
        case 'o':
            strncpy(output_fmt, optarg, sizeof(output_fmt)/sizeof(char)-1);
            if (VideoTypeMap.find(optarg) == VideoTypeMap.end()) return false;
            else OutputFmt = VideoTypeMap.at(optarg);
            break;            
        case 'c':
            strncpy(yuv_type, optarg, sizeof(yuv_type)/sizeof(char)-1);
            if (YuvTypeMap.find(optarg) == YuvTypeMap.end()) return false;
            else InputFmt = YuvTypeMap.at(optarg);
            break;
        case 'w':
            FrameWidth = std::stoi(optarg);
            break;
        case 'h':
            FrameHeight = std::stoi(optarg);
            break;
        case 't':
            BuffType = std::stoi(optarg);
            break;
        default:
            return false;
        }
    }
    std::cout << "Input path: "    << InputPath  << "\n";
    std::cout << "Output format: " << output_fmt << ", video save path: " << SavePath << "\n";;
    std::cout << "Input image yuv type format: " << yuv_type << "\n";
    std::cout << "Input image width: "  << FrameWidth  << "\n";
    std::cout << "Input image height: " << FrameHeight << "\n";
    std::cout << "Buffer type : "       << BuffTypeMap.at(BuffType) << "\n";

    return true;
}


int main(int argc, char**argv)
{
    if ((argc<13) || !ParseCmdOption(argc, argv))
    {
        ShowUsage();
        return -1;
    }

    MainLoop();

    return 0;
}
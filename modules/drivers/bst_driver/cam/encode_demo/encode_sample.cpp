#include <stdio.h>
#include <unistd.h>
#include <sys/mman.h>
#include <vector>
#include <filesystem>
#include <algorithm>
#include <thread>
#include <umm/mem_allocator.h>
#include <umm/ion_allocator.h>
#include "utility.h"
#include "v0/hanhai/autoplt/bmpp/BmppProxy.hpp"

using namespace v0::hanhai::autoplt::bmpp;

extern char InputPath[256];
extern char SavePath[256];
extern BmppTypes::YUVType InputFmt;
extern BmppTypes::CodecType OutputFmt;
extern int FrameWidth;
extern int FrameHeight;
extern int BuffType;
extern const std::unordered_map<BmppTypes::CodecType, std::string>  FileExtMap;

const int DmaBuffNum = 6;
int DmaBuffs[DmaBuffNum] = {0};

static std::shared_ptr<BmppProxy> BmppImplObj;

void *encode_obj;

std::vector<std::string> GetAndSortFileList(const char *filePath)
{
    std::vector<std::string> file_list;
    for (const auto &cur_file : std::filesystem::directory_iterator(filePath))
    {
        file_list.push_back(cur_file.path());
    }

    std::sort(file_list.begin(), file_list.end());
    return file_list;
}

bool InitEncodeObj()
{
    BmppTypes::EncodeParam param;
    param.encodeType = OutputFmt;
    param.yuvFormat  = InputFmt;
    param.width      = FrameWidth;
    param.height     = FrameHeight;
    param.frameRate  = 30;

    if (BuffType == 2)
    {
        std::unique_ptr<autoplt::memory::MemAllocator> allocator_ = std::make_unique<autoplt::memory::IonAllocator>();
        allocator_->Init();

        size_t buf_size = FrameWidth * FrameHeight * 3 / 2;
        autoplt::memory::AllocParam alloc_param{buf_size, 0, true, 1, "reserved"};
        for (int i=0; i<DmaBuffNum; ++i)
        {
            DmaBuffs[i] = allocator_->Allocate(alloc_param);
        }

        param.fdNum   = DmaBuffNum;
        param.fdBuffs = DmaBuffs;
    }

    BmppTypes::RetCode ret;
    BmppImplObj = std::make_shared<BmppProxy>();
    BmppImplObj->EncodeInit(param, ret);
    if (ret != BmppTypes::RetCode::BST_OK) return false;

    return true;
}

void PushYuvDataByCodecBuffer()
{
    printf("push yuv image by codec buffer\n");

    BmppTypes::FrameBuffer frame_buffer;
    BmppTypes::RetCode ret = BmppTypes::RetCode::BST_OK;
    size_t image_counter = 0;
    size_t frame_size = FrameWidth * FrameHeight * 3 / 2;

    std::vector<std::string> file_list = GetAndSortFileList(InputPath);
    std::vector<std::string>::iterator file_it = file_list.begin();
    while (file_it != file_list.end())
    {
        if (!std::filesystem::is_regular_file(*file_it)) continue;

        BmppImplObj->EncodeGetCodecInputBuff(frame_buffer, ret);
        if (ret == BmppTypes::RetCode::BST_OK)
        {
            FILE *pFile = fopen(file_it->c_str(), "r");
            fread(frame_buffer.data, frame_size, 1, pFile);
            fclose(pFile);

            image_counter++;
            frame_buffer.size = frame_size;
            frame_buffer.flag = (image_counter==file_list.size()) ? (0x00000001) : 0;
            BmppImplObj->EncodePutFrameByCodecBuff(frame_buffer, ret);

            file_it++;
        }
    }

    printf("image_counter:%ld\n", image_counter);
}

void PushYuvDataByUsrBuffer()
{
    printf("push yuv image by user-defined buffer\n");

    BmppTypes::FrameBuffer frame_buffer;
    BmppTypes::RetCode ret = BmppTypes::RetCode::BST_OK;
    size_t image_counter = 0;
    size_t frame_size = FrameWidth * FrameHeight * 3 / 2;
    char *pframe_data_buff =(char*) malloc(FrameWidth * FrameHeight * 3 / 2);

    std::vector<std::string> file_list = GetAndSortFileList(InputPath);
    std::vector<std::string>::iterator file_it = file_list.begin();
    while (file_it != file_list.end())
    {
        if (!std::filesystem::is_regular_file(*file_it)) continue;

        if (ret == BmppTypes::RetCode::BST_OK)
        {
            FILE *pFile = fopen(file_it->c_str(), "r");
            fread(pframe_data_buff, frame_size, 1, pFile);
            fclose(pFile);

            image_counter++;
        }

        frame_buffer.data = (uint8_t*)pframe_data_buff;
        frame_buffer.size = frame_size;
        frame_buffer.flag = (image_counter==file_list.size()) ? (0x00000001) : 0;
        BmppImplObj->EncodePutFrameByUsrBuff(frame_buffer, ret);

        if (ret == BmppTypes::RetCode::BST_OK) file_it++;
    }

    delete[] pframe_data_buff;
    printf("image_counter:%ld\n", image_counter);
}

void PushYuvDataByDmaBuffer()
{
    printf("push yuv image by dma buffer\n");

    BmppTypes::FrameBuffer frame_buffer;
    BmppTypes::RetCode ret = BmppTypes::RetCode::BST_OK;
    size_t image_counter = 0;
    size_t frame_size = FrameWidth * FrameHeight * 3 / 2;
    int fd_index = 0;

    std::vector<std::string> file_list = GetAndSortFileList(InputPath);
    std::vector<std::string>::iterator file_it = file_list.begin();
    while (file_it != file_list.end())
    {
        if (!std::filesystem::is_regular_file(*file_it)) continue;

        if (ret == BmppTypes::RetCode::BST_OK)
        {
            printf("fd index:%d, fd: %d\n", fd_index, DmaBuffs[fd_index]);
            void *pframe_data_buff = (char *)mmap(NULL, frame_size, PROT_WRITE, MAP_SHARED, DmaBuffs[fd_index], 0);

            FILE *pFile = fopen(file_it->c_str(), "r");
            fread(pframe_data_buff, frame_size, 1, pFile);
            fclose(pFile);

            munmap(pframe_data_buff, frame_size);

            image_counter++;
        }

        frame_buffer.fd      = DmaBuffs[fd_index];
        frame_buffer.fdIndex = fd_index;
        frame_buffer.size    = frame_size;
        frame_buffer.flag    = (image_counter==file_list.size()) ? (0x00000001) : 0;

        BmppImplObj->EncodePutFrameByDmaBuff(frame_buffer, ret);
        if (ret != BmppTypes::RetCode::BST_OK) continue;

        if (image_counter < DmaBuffNum) fd_index++;
        else
        {
            BmppImplObj->EncodeReclaimDmaBuff(frame_buffer, ret);
            while (ret != BmppTypes::RetCode::BST_OK)
            {
                usleep(30);
                BmppImplObj->EncodeReclaimDmaBuff(frame_buffer, ret);
            }
            fd_index = frame_buffer.fdIndex;
        }
        file_it++;
    }

    printf("image_counter:%ld\n", image_counter);
}

void SaveEncodePacket()
{
    if (!std::filesystem::is_directory(SavePath))
    {
        if (!std::filesystem::create_directories(SavePath))
        {
            printf("fail to create save path\n");
            return;
        }
    }
    
    char file_name[1025];
    snprintf((char*)file_name, 1024, "%s/Video-%dx%d%s", SavePath, FrameWidth, FrameHeight, FileExtMap.at(OutputFmt).c_str());

    FILE *pFile = fopen(file_name, "w");
    BmppTypes::BmppPacket stream_packet;
    BmppTypes::RetCode ret;
    uint64_t FrameCounter = 0;
    char* tmp_data_buff =(char*) malloc(FrameWidth * FrameHeight * 3 / 2);
    while(true)
    {
        BmppImplObj->EncodeGetPacket(stream_packet, ret);
        if (ret == BmppTypes::RetCode::BST_ERR_TERMINATE) break;
        else if (ret == BmppTypes::RetCode::BST_ERR_RETRY)
        {
            usleep(60000);
            continue;
        }

        if (ret == BmppTypes::RetCode::BST_OK)
        {
            FrameCounter++;
            memcpy(tmp_data_buff, stream_packet.data, stream_packet.size);
            fwrite(tmp_data_buff, stream_packet.size, 1, pFile);
            fflush(pFile);
            BmppImplObj->EncodeReleasePacket(stream_packet, ret);
            printf("Success to release output buffer: %p, %d\n", stream_packet.data, (int)ret);
        }
        usleep(30);
    }

    printf("encode frame number: %" PRIu64 "\n", FrameCounter);
    fclose(pFile);
    free(tmp_data_buff);
}

void MainLoop()
{
    if (!InitEncodeObj()) return;
    printf("Success to init encode object\n");

    std::thread push_data_thread;
    if (BuffType == 0)      push_data_thread = std::thread(PushYuvDataByUsrBuffer);
    else if (BuffType == 1) push_data_thread = std::thread(PushYuvDataByCodecBuffer);
    else                    push_data_thread = std::thread(PushYuvDataByDmaBuffer);

    std::thread get_data_thread(SaveEncodePacket);

    push_data_thread.join();
    printf("push yuv data complete\n");

    get_data_thread.join();
    printf("encode image complete\n");

    BmppTypes::RetCode ret;
    BmppImplObj->EncodeDestory(ret);
    printf("finish encode\n");
}
set(BST_CAM "${CMAKE_SOURCE_DIR}/modules/drivers/bst_driver/cam")
if( ${BUILD_BST_COMPILE})
    include(GNUInstallDirs)
    include_directories(${BST_CAM})
    find_package(OpenCV REQUIRED)

    add_executable(MipiDemo  ${BST_CAM}/MipiDemo.cpp ${PROTO_CC_FILES})
    target_link_libraries(MipiDemo ads_log autoplt  cyber cyber_proto fastrtps protobuf pthread)
    target_link_libraries(MipiDemo  protobuf protobuf-lite fastrtps fastcdr pthread m z rt gflags glog atomic uuid)
    target_include_directories(MipiDemo PRIVATE ${BST_CAM})

    if (CMAKE_SYSTEM_NAME MATCHES QNX)
        target_link_libraries(MipiDemo ${OpenCV_LIBS} sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto protobuf NanoLog)
    else()
        target_link_libraries(MipiDemo ads_log ${OpenCV_LIBS} can_hal sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto pthread protobuf ads_log)
    endif()

    install(FILES mipi_camera_config_test.pb.txt DESTINATION /usr/bin)
    install(FILES mipi_test.pb.txt DESTINATION /usr/bin)

    install(FILES ip_camera_config.pb.txt DESTINATION /usr/bin)
    install(FILES sensor_all_in_one.pb.txt DESTINATION /usr/bin)

    install(FILES ip_camera_bstCodec.pb.txt DESTINATION /usr/bin)
    install(FILES BstCode.pb.txt DESTINATION /usr/bin)

    install(TARGETS MipiDemo RUNTIME DESTINATION /usr/bin/examples/)

    set(CMAKE_CXX_STANDARD 17)
    add_compile_options("-O2")
    add_compile_options( -fstack-protector-strong)

    EXEC_PROGRAM(which
        ARGS "bst_idl_code_gen_adsp"
        OUTPUT_VARIABLE idl_tool_path
    )

    set(idl_file_path "${BST_CAM}/../bmpp_idl/bmpp.fdepl")
    if (NOT EXISTS ${idl_file_path})
        set(idl_file_path "$ENV{SDKTARGETSYSROOT}/usr/include/hanhai/autoplt/bmpp_idl/bmpp.fdepl")
    endif()
    execute_process(
        COMMAND ${idl_tool_path} -t lib -d ./ ${idl_file_path}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    )

    include_directories(${BST_CAM}/comm ${CMAKE_CURRENT_BINARY_DIR}/bmpp/src-gen)

    file(GLOB COMM_SRCS_LIST ${BST_CAM}/comm/*.cpp)


    set(encode_sample_path ${BST_CAM}/encode_demo)
    file(GLOB ENCODESRCS_LIST ${encode_sample_path}/*.cpp)
    add_executable(bmpp_encode_demo ${ENCODESRCS_LIST} ${COMM_SRCS_LIST})


    TARGET_LINK_LIBRARIES(bmpp_encode_demo
        avcodec avutil avformat avfilter swscale
        pthread
        bmpp
        stdc++fs
        mem_allocator
    )

    if (${CMAKE_CXX_COMPILER} MATCHES "aarch64")
        TARGET_LINK_LIBRARIES(bmpp_encode_demo
        mveomx selfie
    )
    endif()

    install(TARGETS bmpp_encode_demo DESTINATION ${CMAKE_INSTALL_BINDIR}/examples/bmpp-demo)




    add_executable(CamDriver  ${BST_CAM}/CamDriver.cpp ${PROTO_CC_FILES} ${COMM_SRCS_LIST})
    target_include_directories(CamDriver PRIVATE ${PROJECT_SOURCE_DIR})
    TARGET_LINK_LIBRARIES(CamDriver
        avcodec avutil avformat avfilter swscale
        pthread
        bmpp
        stdc++fs
        mem_allocator
    )

    if (${CMAKE_CXX_COMPILER} MATCHES "aarch64")
        TARGET_LINK_LIBRARIES(CamDriver
        mveomx selfie
    )
    endif()

    if (CMAKE_SYSTEM_NAME MATCHES QNX)
        target_link_libraries(CamDriver ${OpenCV_LIBS} sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto protobuf NanoLog)
    else()
        target_link_libraries(CamDriver ads_log ${OpenCV_LIBS} can_hal sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto pthread protobuf ads_log)
    endif()

endif()
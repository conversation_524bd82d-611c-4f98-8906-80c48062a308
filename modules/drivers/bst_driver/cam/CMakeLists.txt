include(GNUInstallDirs)
include_directories(${PROJECT_SOURCE_DIR})
find_package(OpenCV REQUIRED)

if (CMAKE_EXTRA_SYSTEM MATCHES QNX)
	add_definitions(-DQNX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O2 -ggdb -g -no-pie -stdlib=libstdc++ -D_GLIBCXX_USE_C99 -std=c++11  -fPIC")
else()
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
    add_compile_options("-O2")
endif()

add_executable(MipiDemo MipiDemo.cpp camera.pb.cc header.pb.cc)
target_include_directories(MipiDemo PRIVATE ${PROJECT_SOURCE_DIR})


if (CMAKE_EXTRA_SYSTEM MATCHES QNX)
    target_link_libraries(MipiDemo ${OpenCV_LIBS} sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto protobuf NanoLog)
else()
    target_link_libraries(MipiDemo ads_log ${OpenCV_LIBS} can_hal sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto pthread protobuf ads_log)
endif()

install(FILES mipi_camera_config_test.pb.txt DESTINATION /usr/bin)
install(FILES mipi_test.pb.txt DESTINATION /usr/bin)

install(FILES ip_camera_config.pb.txt DESTINATION /usr/bin)
install(FILES sensor_all_in_one.pb.txt DESTINATION /usr/bin)

install(FILES ip_camera_bstCodec.pb.txt DESTINATION /usr/bin)
install(FILES BstCode.pb.txt DESTINATION /usr/bin)

install(TARGETS MipiDemo RUNTIME DESTINATION /usr/bin/examples/)



set(CMAKE_CXX_STANDARD 17)
add_compile_options("-O2")
add_compile_options(-Wall -Werror -fstack-protector-strong)

EXEC_PROGRAM(which
    ARGS "bst_idl_code_gen_adsp"
    OUTPUT_VARIABLE idl_tool_path
)

set(idl_file_path "${CMAKE_CURRENT_SOURCE_DIR}/../bmpp_idl/bmpp.fdepl")
if (NOT EXISTS ${idl_file_path})
    set(idl_file_path "$ENV{SDKTARGETSYSROOT}/usr/include/hanhai/autoplt/bmpp_idl/bmpp.fdepl")
endif()
execute_process(
    COMMAND ${idl_tool_path} -t lib -d ./ ${idl_file_path}
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/comm ${CMAKE_CURRENT_BINARY_DIR}/bmpp/src-gen)

file(GLOB COMM_SRCS_LIST ${CMAKE_CURRENT_SOURCE_DIR}/comm/*.cpp)


set(encode_sample_path ${CMAKE_CURRENT_SOURCE_DIR}/encode_demo)
file(GLOB ENCODESRCS_LIST ${encode_sample_path}/*.cpp)
add_executable(bmpp_encode_demo ${ENCODESRCS_LIST} ${COMM_SRCS_LIST})


TARGET_LINK_LIBRARIES(bmpp_encode_demo
    avcodec avutil avformat avfilter swscale
    pthread
    bmpp
    stdc++fs
    mem_allocator
)

if (${CMAKE_CXX_COMPILER} MATCHES "aarch64")
    TARGET_LINK_LIBRARIES(bmpp_encode_demo
    mveomx selfie
)
endif()

install(TARGETS bmpp_encode_demo DESTINATION ${CMAKE_INSTALL_BINDIR}/examples/bmpp-demo)




add_executable(CamDriver CamDriver.cpp camera.pb.cc header.pb.cc ${COMM_SRCS_LIST})
target_include_directories(CamDriver PRIVATE ${PROJECT_SOURCE_DIR})
TARGET_LINK_LIBRARIES(CamDriver
    avcodec avutil avformat avfilter swscale
    pthread
    bmpp
    stdc++fs
    mem_allocator
)

if (${CMAKE_CXX_COMPILER} MATCHES "aarch64")
    TARGET_LINK_LIBRARIES(CamDriver
    mveomx selfie
)
endif()

if (CMAKE_EXTRA_SYSTEM MATCHES QNX)
    target_link_libraries(CamDriver ${OpenCV_LIBS} sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto protobuf NanoLog)
else()
    target_link_libraries(CamDriver ads_log ${OpenCV_LIBS} can_hal sm_tools sensor_abstraction_proto cyber cyber_proto fastrtps autoplt sm_service_config_proto pthread protobuf ads_log)
endif()

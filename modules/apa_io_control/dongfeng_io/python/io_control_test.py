import time
import math
import random

# import numpy as np
import sys
from random import *
import logging as log
from cyber.python.cyber_py3 import cyber
from modules.apa_io_control.proto.io_proto_pb2 import IOControl
from modules.apa_io_control.proto.io_proto_pb2 import IOTakeover


class IOControlTest():

  def __init__(self):
    print("start io control test.")
    self.io_contol_node = cyber.Node("io_control")

    self.io_control_writer = self.io_contol_node.create_writer("/changan/io/control",IOControl,6)
    self.io_takeover_writer = self.io_contol_node.create_writer("/changan/io/takeover",IOTakeover,6)
    pass

  def pub_takeover(self,apa,eps,esp,vcu,gear):
    cmd = IOTakeover()
    cmd.header.timestamp = time.time()
    cmd.apa_takeover = apa
    cmd.eps_takeover = eps
    cmd.esp_takeover = esp
    cmd.vcu_takeover = vcu
    cmd.gear_takeover = gear
    self.io_takeover_writer.write(cmd)
    pass

  def pub_iocontrol(self,is_braking,tar_deceleration,is_driving,tar_torque,is_steering,tar_steer_angle,is_gear,tar_gear):
    cmd = IOControl()
    cmd.header.timestamp = time.time()
    cmd.is_braking = is_braking
    cmd.tar_deceleration = tar_deceleration
    cmd.is_driving = is_driving
    cmd.tar_torque = tar_torque
    cmd.is_steering = is_steering
    cmd.tar_steer_angle = tar_steer_angle
    cmd.is_gear = is_gear
    cmd.tar_gear = tar_gear
    self.io_control_writer.write(cmd)
    pass 

  def auto_run(self):
    while True:
      k = input('[Prompt] continue test or not?  [q] for Quit:  ')
      if (k == 'q'):
        return
      elif k == 'w':
        self.pub_takeover(0,1,0,0,0)
        print(k)
      elif k == 'e':
        self.pub_takeover(1,0,0,0,0)
        print(k)
      elif k == 'r':
        self.pub_iocontrol(0,0,0,0,1,10,0,0)
        print(k)
      else:
        print("other key")




# ==============================================================================
# -- main() --------------------------------------------------------------------
# ==============================================================================


def main():
  """
  Main function
  """
  cyber.init()
 
  io_control_test = IOControlTest()
  try:
    io_control_test.auto_run()
    cyber.shutdown()
    
  finally:
    if io_control_test is not None:
      pass


if __name__ == '__main__':

    main()

    

{"DBC_INIT": {"IVI_160": {"HAD_APA_LongitudinalCtrlReq": 0, "HAD_APA_SystemSt": 1, "HAD_APA_ReqToStopDst": 0, "HAD_APA_ReqtTargetGearPosSt": 0, "HAD_APA_FailureBrakeModeSt": 0, "HAD_APA_BrakeTargetMaxSpeed": 0.0, "HAD_APA_EmergencySt": 0, "HAD_APA_Emergency_Valid": 0, "HAD_APA_ESC_Funmode": 0, "HAD_APA_ReqEPSTargetAngleReq": 0, "RollingCounter160": 0, "HAD_APA_ControlEPSReq": 0, "HAD_EPS_FuncModeReq": 0, "CheckSum160": 0}, "IVI_4C1": {}, "IVI_35A": {"HAD_APA_AutomaticParkingPodeSt": 0, "HAD_APA_TurnLightsCommandReq": 0, "HAD_APA_LightControlValid": 0, "HAD_APA_OnOffDisp": 0, "HAD_APA_UPAErrorDisp": 0, "HAD_APA_MPAPArkNoticeDisp": 0, "HAD_APA_MPAStFeedbackSt": 0, "HAD_APA_MPAReadyFbSt": 0, "RollingCounter35A": 0, "CheckSum35A": 0}}, "Pub_Topic": {"topic_160": "IVI_160", "topic_4c1": "IVI_4C1", "topic_35a": "IVI_35A", "pub_topic": "APA/SettingInfo"}, "Sub_Topic": {"sub_apa_topic": "APA/MessageInfo", "sub_imu_topic": "ImuSensor/resp"}, "Param": {"steering_rate_limit": 14.0, "ibc_pedal_travel": 10, "use_heart_beat_check": 0, "use_exception_function": 1, "use_resend_function": 0, "use_imu_whlspd_monitor": 0, "overtime_span_warn": 200, "overtime_span_error": 5000, "warnning_rate": 5, "filter_window_size": 15, "filter_pickup_index": 2, "uss_valid_distance_cm": 500.0, "eps_tk_torque_threshold": 1.8, "eps_tk_keep_time": 300, "eco_pub_speed_threshold": 30.0, "eco_no_pub_speed_threshold": 34.0, "radar_handle_type": 1, "slope_mean_filter_ws": 40, "slope_mean_ws": 25, "slope_ws": 80, "slope_debug": 1, "real_mass": 2650.0, "mass_friction": 0.0, "mass_a": 0.666, "mass_b": -2.066, "mass_c": 197.458, "mass_offset": 0.0, "mass_min_speed_limited": 0.5, "mass_max_speed_limited": 7.0, "mass_acc_limited": 0.05, "mass_wheel_radius": 0.3937, "mass_slope_limited": 3.0, "mass_eps_limited": 30.0, "mass_debug": 1, "use_system_identification": 1}, "ErrorLevel": {"EPSErrorLevel": {"EPSErrorLevel3": [[5, 5], [6, 6], [7, 7]], "EPSErrorLevel2": [[1, 1], [2, 2], [3, 3], [4, 4]], "EPSErrorLevel1": [], "EPSErrorLevel0": [[0, 0]]}, "IBCErrorLevel": {"IBCErrorLevel3": [], "IBCErrorLevel2": [[1, 1], [2, 2], [3, 3], [5, 5], [6, 6], [7, 7], [8, 8], [10, 10], [11, 11], [12, 12]], "IBCErrorLevel1": [[4, 4], [9, 9], [13, 13], [14, 14], [15, 15], [16, 16]], "IBCErrorLevel0": [[0, 0]]}}}
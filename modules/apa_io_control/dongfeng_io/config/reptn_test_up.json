{"INS_17F": {"bindSigNames": ["ICMSG_652/INS_GPS_Time", "ICMSG_652/FL_wheel_vel_for_IPC", "ICMSG_652/INS_Current_Pos_Heading", "ICMSG_652/INS_Current_Pos_Heading_Accuracy", "ICMSG_652/L_wheel_factor", "ICMSG_652/R_wheel_factor", "ICMSG_652/INS_Current_Pos_Pitch_Accuracy", "ICMSG_652/INS_Current_Pos_Roll_Accuracy", "ICMSG_652/INS_Current_Pos_Pitch", "ICMSG_652/INS_Current_Pos_Roll", "ICMSG_652/FR_wheel_vel_for_IPC", "ICMSG_652/RL_wheel_vel_for_IPC", "ICMSG_652/RR_wheel_vel_for_IPC", "ICMSG_652/INS_Current_Pos_Pitch_Confidence", "ICMSG_652/INS_Current_Pos_Heading_Confiden", "ICMSG_652/INS_Current_Pos_Roll_Confidence", "ICMSG_652/INS_TiStamp", "ICMSG_652/INS_TiOut", "ICMSG_652/INS_TiLeap", "ICMSG_652/INS_TiBas", "ICMSG_652/INS_RollingCounter_17F", "ICMSG_652/INS_CRCCheck_17F"]}, "INS_146": {"bindSigNames": ["ICMSG_650/INS_Current_Pos_X_Accel", "ICMSG_650/INS_Current_Pos_Y_Accel", "ICMSG_650/INS_Pos_X_Accel_Bias", "ICMSG_650/INS_Pos_Y_Accel_Bias", "ICMSG_650/INS_Current_Pos_Z_Accel", "ICMSG_650/INS_Pos_Z_Accel_Bias", "ICMSG_650/INS_Current_Pos_X_Rate", "ICMSG_650/INS_Pos_X_Rate_Bias", "ICMSG_650/INS_Current_Pos_Y_Rate", "ICMSG_650/INS_Pos_Y_Rate_Bias", "ICMSG_650/INS_Current_Pos_Z_Rate", "ICMSG_650/INS_Pos_Z_Rate_Bias", "ICMSG_650/INS_Quaternion_X", "ICMSG_650/INS_Quaternion_Y", "ICMSG_650/INS_Quaternion_Z", "ICMSG_650/INS_Quaternion_W", "ICMSG_650/INS_IMU_Time", "ICMSG_650/INS_TiOut", "ICMSG_650/INS_TiLeap", "ICMSG_650/INS_TiBas", "ICMSG_650/INS_IMU_Valid", "ICMSG_650/INS_Roll_Mis_Angle_to_Veh", "ICMSG_650/INS_Pitch_Mis_Angle_to_Veh", "ICMSG_650/INS_Yaw_Mis_Angle_to_Veh", "ICMSG_650/INS_TiStamp", "ICMSG_650/INS_RollingCounter_146", "ICMSG_650/INS_CRCCheck_146"]}, "ICMSG_653/INS_Wheel_Scale_factor": "INS_5B3", "VCU_161": {"bindSigNames": ["ICMSG_651/VCUShiftPostionValid", "ICMSG_651/VCUAPARequestEnable", "ICMSG_651/VCUAPAdriverInterruption", "ICMSG_651/VCUAccPedShield", "ICMSG_651/VcuAccrPedlPosn", "ICMSG_651/VcuCalcnAccrPedlPosn", "ICMSG_651/VcuAPATorqRequestAvailable", "ICMSG_651/VcuAccrPedlPosnVld", "ICMSG_651/VcuCalcnAccrPedlPosnVld", "ICMSG_651/VcuEnyRecyclMod", "ICMSG_651/VcuComFltSts", "ICMSG_651/VcuSimnEpbSwtSts", "ICMSG_651/VcuSimnEpbSwtStsVld", "ICMSG_651/VcuGearPosn", "ICMSG_651/VcuPtTqReqAvl", "ICMSG_651/VcuPtTqRealVld", "ICMSG_651/VcuPtTqLimMinVld", "ICMSG_651/VcuPtTqLimMaxVld", "ICMSG_651/VcuOvrdModReq", "ICMSG_651/VcuPtTqLimMax", "ICMSG_651/VcuPtTqLimMin", "ICMSG_651/VcuShiftLvlPosn", "ICMSG_651/VcuPtTqReal", "ICMSG_651/VcuVehWhlReqTqVld", "ICMSG_651/VcuVehGearPosnVld", "ICMSG_651/VcuRdySts", "ICMSG_651/VcuVehWhlReqTq", "ICMSG_651/GW_ADSSecOCVerifyFailureFlag", "ICMSG_651/VcuCycCntr161", "ICMSG_651/VcuCrcChk161"]}, "GW_1C2": {"bindSigNames": ["ICMSG_652/EspReWhlIncTarTqActv", "ICMSG_652/EspReWhlDecTarTqActv", "ICMSG_652/EspTcsActvSts", "ICMSG_652/EspTcsFailr", "ICMSG_652/EspFctOpenSts", "ICMSG_652/EspActvSts", "ICMSG_652/EspAbsActv", "ICMSG_652/EspAbsFailr", "ICMSG_652/EspEbdFailr", "ICMSG_652/EspReWhlDecTarTq", "ICMSG_652/EspReWhlIncTarTq", "ICMSG_652/EspVehStandstill", "ICMSG_652/EspAutoHoldActvSts", "ICMSG_652/EspVdcActvSts", "ICMSG_652/IBCU_ADCActiveState", "ICMSG_652/IBCU_CommunicationInvalid", "ICMSG_652/IBCU_ADCFullFuncAvail", "ICMSG_652/IBCU_ADCReducedFuncAvail"]}, "GW_20B": {"bindSigNames": ["ICMSG_653/WhlSpdRiFrntData", "ICMSG_653/WhlSpdRiFrntDir", "ICMSG_653/WhlSpdRiFrntDataVld", "ICMSG_653/WhlSpdLeFrntData", "ICMSG_653/WhlSpdLeFrntDir", "ICMSG_653/WhlSpdLeFrntDataVld", "ICMSG_653/IBCU_APCActiveStatus", "ICMSG_653/WhlSpdRiReData", "ICMSG_653/WhlSpdRiReDir", "ICMSG_653/WhlSpdRiReDataVld", "ICMSG_653/WhlSpdLeReData", "ICMSG_653/WhlSpdLeReDir", "ICMSG_653/WhlSpdLeReDataVld", "ICMSG_653/ESP_APA_DriverOverride", "ICMSG_653/WhlSpdFrntLePls", "ICMSG_653/WhlSpdFrntRiPls", "ICMSG_653/WhlSpdReRiPls", "ICMSG_653/WhlSpdReLePls", "ICMSG_653/ESP_PrefillAvailable", "ICMSG_653/ESP_PrefillActive", "ICMSG_653/ESP_BrakeForce", "ICMSG_653/IBCU_APCReducedFuncAvail", "ICMSG_653/IBCU_APCFullFuncAvail", "ICMSG_653/IbcuCycCntr20B", "ICMSG_653/IBCU_ADCRampOffSuspendState", "ICMSG_653/IbcuCrcChk20B"]}, "GW_17A": {"bindSigNames": ["ICMSG_652/EspVehSpd", "ICMSG_652/EspVehSpdVld", "ICMSG_652/EPB_APArequest_Available", "ICMSG_652/EPB_AchievedClampForce_Primary", "ICMSG_652/EPB_AchievedClampForce", "ICMSG_652/EPB_FailStatuss_Primary", "ICMSG_652/ESP_RollingCounter_17A", "ICMSG_652/ESP_CRCCheck_17A"]}, "GW_3C2": {"bindSigNames": ["ICMSG_653/EpbFailrSts", "ICMSG_653/EpbSts", "ICMSG_653/EspEpbReqAvl", "ICMSG_653/EpbStsPrimary", "ICMSG_653/EspCycCntr3C2", "ICMSG_653/EspCrcChk3C2"]}, "GW_2C2": {"bindSigNames": ["ICMSG_653/IbBrkPedlStsGb", "ICMSG_653/IbBrkPedlStsGbVld", "ICMSG_653/IBCU_PFSBrakePressure", "ICMSG_653/IBCU_PlungerBrakePressure", "ICMSG_653/IbCycCntr2C2", "ICMSG_653/IbCrcChk2C2"]}, "GW_24F": {"bindSigNames": ["ICMSG_653/EPS_ElectPowerConsumption", "ICMSG_653/EPS_APA_EpasFAILED", "ICMSG_653/EPS_APA_Abortfeedback", "ICMSG_653/EPS_SteeringTorque", "ICMSG_653/EPS_IACC_abortreason", "ICMSG_653/EPS_APA_ControlFeedback", "ICMSG_653/EPS_ADASActiveMode", "ICMSG_653/EPS_ADS_ControlFeedback", "ICMSG_653/EPS_SystemSt", "ICMSG_653/EPS_RollingCounter_24F", "ICMSG_653/EPS_ConcussAvailabilityStatus", "ICMSG_653/EPS_CRCCheck_24F"]}, "GW_17E": {"bindSigNames": ["ICMSG_652/EPS_RollingCounter_17E", "ICMSG_652/EPS_LatCtrlAvailabilityStatus", "ICMSG_652/EPS_LatCtrlActive", "ICMSG_652/EPS_CRCCheck_17E"]}, "GW_170": {"bindSigNames": ["ICMSG_651/EPS_RollingCounter_170", "ICMSG_651/EPS_fault_state"]}, "GW_180": {"bindSigNames": ["ICMSG_651/EpsSasSteerAg", "ICMSG_651/EpsSteerAgRate", "ICMSG_651/EpsSasCalSts", "ICMSG_651/EpsSteerAgSensFilr", "ICMSG_651/EpsSasSteerAgVld", "ICMSG_651/EpsCycCntr180", "ICMSG_651/EpsCrcChk180"]}, "INS_271": {"bindSigNames": ["ICMSG_651/INS_TiStamp", "ICMSG_651/INS_TiOut", "ICMSG_651/INS_TiLeap", "ICMSG_651/INS_TiBas", "ICMSG_651/INS_TIleap_Difference", "ICMSG_651/INS_RollingCounter_271", "ICMSG_651/INS_CRCCheck_271"]}, "USS_611": {"bindSigNames": ["ICMSG_654/Ult_Probe_info1", "ICMSG_654/Ult_Probe_info2", "ICMSG_654/Ult_Probe_info3", "ICMSG_654/Ult_Probe_info4"]}, "USS_612": {"bindSigNames": ["ICMSG_654/Ult_Probe_info5", "ICMSG_654/Ult_Probe_info6", "ICMSG_654/Ult_Probe_info7", "ICMSG_654/Ult_Probe_info8"]}, "USS_613": {"bindSigNames": ["ICMSG_654/Ult_Probe_info9", "ICMSG_654/Ult_Probe_info10", "ICMSG_654/Ult_Probe_info11", "ICMSG_654/Ult_Probe_info12"]}}
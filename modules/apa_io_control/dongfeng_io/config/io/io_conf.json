{
    "DBC_INIT":{
        "IVI_160":{
            "HAD_APA_LongitudinalCtrlReq":0,
            "HAD_APA_SystemSt": 1,
            "HAD_APA_ReqToStopDst": 0,
            "HAD_APA_ReqtTargetGearPosSt": 0,
            "HAD_APA_FailureBrakeModeSt": 0,
            "HAD_APA_BrakeTargetMaxSpeed": 0.0,
            "HAD_APA_EmergencySt": 0,
            "HAD_APA_Emergency_Valid": 0,
            "HAD_APA_ESC_Funmode": 0,
            "HAD_APA_ReqEPSTargetAngleReq": 0,
            "RollingCounter160": 0,
            "HAD_APA_ControlEPSReq": 0,
            "HAD_EPS_FuncModeReq": 0,
            "CheckSum160": 0
        },
        "IVI_4C1":{

        },
        "IVI_35A":{
            "HAD_APA_AutomaticParkingPodeSt": 0,
            "HAD_APA_TurnLightsCommandReq": 0,
            "HAD_APA_LightControlValid": 0,
            "HAD_APA_OnOffDisp": 0,
            "HAD_APA_UPAErrorDisp": 0,
            "HAD_APA_MPAPArkNoticeDisp": 0,
            "HAD_APA_MPAStFeedbackSt": 0,
            "HAD_APA_MPAReadyFbSt": 0,
            "RollingCounter35A": 0,
            "CheckSum35A": 0
        }
    },
    "Pub_Topic":{
        "topic_160": "IVI_160",
        "topic_4c1": "IVI_4C1",
        "topic_35a": "IVI_35A",
        "pub_topic": "APA/SettingInfo"
    },
    "Sub_Topic":{
        "sub_topic": "APA/MessageInfo"
    },
    "Param":{
        "steering_rate_limit":9.0,
        "filter_window_size" : 15,
        "filter_pickup_index" : 2,
        "uss_valid_distance_cm": 500.0
    },
    "ErrorLevel":{
        "EPSErrorLevel":{
            "EPSErrorLevel3":
            [
                [5,5],        #EPSFailure
                [6,6],        #DAEthermalSafetyCatch
                [7,7]        #OtherFault
            ],
            "EPSErrorLevel2":[
                [1,1],        #PickUpDriver
                [2,2],        #VehicleSpeedTooHigh
                [3,3],        #AngularErrorTooHigh
                [4,4]        #ObstacleAtTheWheel
            ],
            "EPSErrorLevel1":[
                
            ],
            "EPSErrorLevel0":[ 
                [0,0]        #NoControlInterruption
            ]
        },
        "IBCErrorLevel":{
            "IBCErrorLevel3":
            [
            ],
            "IBCErrorLevel2":[
                [1,1],        #VehIVIleBlocked  
                [2,2],         #UnexpectedGearPosition  
                [3,3],         #UnexpectedEPB_Action 
                [5,5],         #UnexpectedSpeedLimitation  
                [6,6],         #UnexpectedGearIntervention  
                [7,7],         #UnexpectedDriverAcceleration  
                [8,8],         #UnexpectedEngineFailure  
                [10,10],         #UnexpectedAPAFailure  
                [11,11],         #StatIVISlopeOutofRange  
                [12,12]         #ESCFailure
            ],
            "IBCErrorLevel1":[
                [4,4],        #UnexpectedTravelDistance   
                [9,9],         #UnexpectedTCUFailure  
                [13,13],         #TimeOut  
                [14,14],         #DriverUnpresent  
                [15,15],         #Reserved  
                [16,16]          #TakeOver
            ],
            "IBCErrorLevel0":[
                [0,0]        #NoError
            ]
        }
    }
}

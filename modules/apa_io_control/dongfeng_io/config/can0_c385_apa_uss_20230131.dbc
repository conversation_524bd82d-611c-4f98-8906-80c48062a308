VERSION "HIPBNYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY/4/%%%/4/'%**4YYY///"


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: GW ADC TBOX INS VCU CDC LVSM USS DVR APA ADS BDC SRS EPS RRS FLR RLR FRR RRR


BO_ 759 TBOX_2F7: 64 TBOX
 SG_ TboxLocalTiYear : 7|8@0+ (1,2000) [2000|2255] "year"  TBOX,CDC,LVSM
 SG_ TboxLocalTiMth : 11|4@0+ (1,0) [0|15] "month"  TBOX,CDC,LVSM
 SG_ TboxLocalTiDate : 20|5@0+ (1,0) [0|31] "day"  TBOX,CDC,LVSM
 SG_ TboxLocalTiHr : 28|5@0+ (1,0) [0|31] "hour"  TBOX,CDC,LVSM
 SG_ TboxLocalTiMins : 37|6@0+ (1,0) [0|63] "minute"  TBOX,CDC,LVSM
 SG_ TboxLocalTiSec : 45|6@0+ (1,0) [0|63] "second"  TBOX,CDC,LVSM
 SG_ TboxLocalTiVld : 46|1@0+ (1,0) [0|1] ""  TBOX,CDC,LVSM
 SG_ TboxSwVers0 : 231|8@0+ (1,0) [0|255] ""  TBOX,CDC
 SG_ TboxSwVers1 : 239|8@0+ (1,0) [0|255] ""  TBOX,CDC
 SG_ TboxSwVers2 : 247|8@0+ (1,0) [0|255] ""  TBOX,CDC
 SG_ TboxSwVers3 : 255|8@0+ (1,0) [0|255] ""  TBOX,CDC
 SG_ TboxSwVers4 : 263|16@0+ (1,0) [0|65535] ""  TBOX,CDC
 SG_ TboxSwVers5 : 279|8@0+ (1,0) [0|255] ""  TBOX,CDC
 SG_ TboxSwVers6 : 287|8@0+ (1,0) [0|255] ""  TBOX,CDC
 SG_ HU_CurrentLocationLongitude : 343|29@0+ (1E-006,-268.435455) [-180|180] "degree"  TBOX,DVR,APA,ADS,CDC,LVSM
 SG_ HU_CurrentLocationLatitude : 362|28@0+ (1E-006,-134.217727) [-90|90] "degree"  TBOX,DVR,APA,ADS,CDC,LVSM
 SG_ TBOX_SignalStrength : 394|3@0+ (1,0) [0|7] "/"  TBOX,CDC,LVSM
 SG_ TBOX_RemoteDisReq : 395|1@0+ (1,0) [0|1] "/"  TBOX,CDC,LVSM
 SG_ TBOX_RemoteDetectReq : 397|2@0+ (1,0) [0|3] "/"  TBOX,CDC,LVSM
 SG_ HU_CurrentLocationValid : 398|1@0+ (1,0) [0|1] "/"  TBOX,DVR,APA,CDC,LVSM
 SG_ TBOX_APAFunctionReq : 402|3@0+ (1,0) [0|7] ""  TBOX,CDC,APA,LVSM
 SG_ TBOX_VehicleVedioDetectReq : 404|2@0+ (1,0) [0|3] "/"  TBOX,CDC,LVSM
 SG_ TBOX_SignalType : 407|3@0+ (1,0) [0|7] "/"  TBOX,CDC,LVSM
 SG_ TBOX_APAControlEnable : 413|5@0+ (1,0) [0|31] ""  TBOX,CDC,APA,LVSM
 SG_ TBOX_RemoteStartReq : 415|2@0+ (1,0) [0|3] ""  TBOX,CDC,APA,LVSM
 SG_ TBOX_APP_Xlevel : 423|16@0+ (1,0) [0|1920] ""  TBOX,CDC,APA,LVSM
 SG_ TBOX_APP_Ylevel : 439|16@0+ (1,0) [0|720] "/"  TBOX,CDC,APA,LVSM
 SG_ TBOX_APA_SignalDelay : 471|4@0+ (1,0) [0|15] "/"  TBOX,CDC,APA,LVSM

BO_ 1459 INS_5B3: 64 INS
 SG_ INS_Wheel_Scale_factor : 7|20@0+ (1E-006,-0.5) [-0.5|0.5] "/"  INS,CDC
 SG_ INS_IMU_Temp : 19|16@0+ (0.01,0) [0|655] "K"  INS,CDC
 SG_ INS_CorrelationSystem_failure_st : 447|16@0+ (1,0) [0|65535] "/"  INS,CDC
 SG_ INS_Sts : 457|4@0+ (1,0) [0|7] "/"  INS,CDC
 SG_ INS_Latitude_Hemisphere : 462|1@0+ (1,0) [0|1] ""  INS,CDC
 SG_ INS_Longitude_Hemisphere : 463|1@0+ (1,0) [0|1] ""  INS,CDC
 SG_ INS_RollingCounter_5B3 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_5B3 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 383 INS_17F: 64 INS
 SG_ INS_GPS_Week : 7|16@0+ (1,0) [0|65500] "/"  INS,CDC
 SG_ INS_GPS_Time : 23|32@0+ (1,0) [0|650000000] "ms"  INS,CDC
 SG_ INS_Current_Pos_Long_GNSS : 55|32@0+ (1E-007,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Current_Pos_Lat_GNSS : 87|32@0+ (1E-007,-180) [-90|90] "°"  INS,CDC
 SG_ INS_Current_Pos_Long_GNSS_Err : 119|16@0+ (1,0) [0|65535] "mm"  INS,CDC
 SG_ INS_Current_Pos_Lat_GNSS_Err : 135|16@0+ (1,0) [0|65535] "mm"  INS,CDC
 SG_ FL_wheel_vel_for_IPC : 151|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_Current_Pos_Heading : 167|20@0+ (0.001,0) [0|360] "°"  INS,CDC
 SG_ INS_GNSS_Mode : 179|4@0+ (1,0) [0|7] ""  INS,CDC
 SG_ INS_Current_Pos_Heading_Accuracy : 191|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_GNSS_Height : 207|24@0+ (0.001,-1000) [-1000|7500] "m"  INS,CDC
 SG_ INS_GNSS_Height_Err : 231|16@0+ (1,0) [0|65535] "mm"  INS,CDC
 SG_ L_wheel_factor : 247|16@0+ (0.0001,0) [0|1] "m/m"  INS,CDC
 SG_ R_wheel_factor : 263|16@0+ (0.0001,0) [0|1] "m/m"  INS,CDC
 SG_ INS_Current_Pos_Pitch_Accuracy : 279|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Roll_Accuracy : 295|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Pitch : 311|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Current_Pos_Roll : 323|20@0+ (0.001,-90) [-90|90] "°"  INS,CDC
 SG_ FR_wheel_vel_for_IPC : 351|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ RL_wheel_vel_for_IPC : 367|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ RR_wheel_vel_for_IPC : 383|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_Current_Pos_Pitch_Confidence : 395|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Heading_Confiden : 399|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Long_Confidence : 403|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Roll_Confidence : 407|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Height_Confidenc : 411|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Lat_Confidence : 415|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_TiStamp : 423|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_Initiation_Mark : 456|1@0+ (1,0) [0|1] "/"  INS,CDC
 SG_ INS_TiOut : 457|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 458|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 459|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_RollingCounter_17F : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_POS_Match_RTK_POS_Mark : 492|1@0+ (1,0) [0|1] "/"  INS,CDC
 SG_ INS_CRCCheck_17F : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 326 INS_146: 64 INS
 SG_ INS_Current_Pos_X_Accel : 7|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_Y_Accel : 19|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Pos_X_Accel_Bias : 47|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Pos_Y_Accel_Bias : 63|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_Z_Accel : 79|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Pos_Z_Accel_Bias : 91|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_X_Rate : 107|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_X_Rate_Bias : 135|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Current_Pos_Y_Rate : 151|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_Y_Rate_Bias : 163|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Current_Pos_Z_Rate : 179|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_Z_Rate_Bias : 207|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Quaternion_X : 223|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_Y : 247|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_Z : 271|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_W : 295|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_IMU_Time : 319|32@0+ (1,0) [0|4294967295] "ms"  INS,CDC
 SG_ INS_TiOut : 348|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 349|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 350|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_IMU_Valid : 351|1@0+ (1,0) [0|1] "ENUM"  INS,CDC
 SG_ INS_Roll_Mis_Angle_to_Veh : 359|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Pitch_Mis_Angle_to_Veh : 371|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Yaw_Mis_Angle_to_Veh : 399|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_TiStamp : 411|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_RollingCounter_146 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_146 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 795 INS_31B: 64 INS
 SG_ INS_GNSS_Time_Year : 7|12@0+ (1,2000) [2000|3000] "year"  INS,CDC
 SG_ INS_GNSS_Time_Month : 11|4@0+ (1,0) [1|12] "month"  INS,CDC
 SG_ INS_GNSS_Time_Date : 23|8@0+ (1,0) [1|31] "day"  INS,CDC
 SG_ INS_GNSS_Time_Hour : 31|8@0+ (1,0) [0|23] "hour"  INS,CDC
 SG_ INS_GNSS_Time_Minute : 39|8@0+ (1,0) [0|59] "minute"  INS,CDC
 SG_ INS_GNSS_Time_Second : 47|8@0+ (1,0) [0|59] "second"  INS,CDC
 SG_ INS_GNSS_Time_mSecond : 55|12@0+ (1,0) [0|999] "ms"  INS,CDC
 SG_ INS_GNSS_Time_Valid : 59|1@0+ (1,0) [0|1] "ENUM"  INS,CDC
 SG_ GNSS_Speed : 71|20@0+ (0.001,-100) [-100|100] "m/s"  INS,CDC
 SG_ GNSS_Speed_Err : 83|16@0+ (1,0) [0|65535] "mm/s"  INS,CDC
 SG_ INS_GNSS_Speed_North : 99|20@0+ (0.001,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_GNSS_Speed_East : 127|20@0+ (0.001,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_GNSS_Speed_Earth : 139|20@0+ (0.001,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_GNSS_Speed_North_Err : 167|16@0+ (1,0) [0|65535] "mm/s"  INS,CDC
 SG_ INS_GNSS_Speed_East_Err : 183|16@0+ (1,0) [0|65535] "mm/s"  INS,CDC
 SG_ INS_GNSS_Speed_Earth_Err : 199|16@0+ (1,0) [0|65535] "mm/s"  INS,CDC
 SG_ INS_GNSS_Original_Height : 215|24@0+ (0.001,-1000) [-1000|7500] "m"  INS,CDC
 SG_ INS_GPS_Flight_Path_Angle : 251|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_GNSS_GDOP : 279|20@0+ (0.01,0) [0|1000] ""  INS,CDC
 SG_ INS_GNSS_PDOP : 291|20@0+ (0.01,0) [0|1000] ""  INS,CDC
 SG_ INS_GNSS_HDOP : 319|20@0+ (0.01,0) [0|1000] ""  INS,CDC
 SG_ INS_GNSS_VDOP : 331|20@0+ (0.01,0) [0|1000] ""  INS,CDC
 SG_ INS_GNSS_TDOP : 359|20@0+ (0.01,0) [0|1000] ""  INS,CDC
 SG_ INS_GNSS_Speed_North_Confidence : 371|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_GNSS_Speed_Earth_Confidence : 379|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_GNSS_Speed_East_Confidence : 383|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_UsedSV : 391|8@0+ (1,0) [0|100] "/"  INS,CDC
 SG_ INS_GNSS_Difference_delay : 399|20@0+ (0.001,0) [0|255] "s"  INS,CDC
 SG_ INS_WatchedSV : 411|8@0+ (1,0) [0|100] "/"  INS,CDC
 SG_ INS_Current_Original_Lat : 431|32@0+ (1E-007,-180) [-180|90] "°"  INS,CDC
 SG_ INS_Current_Original_Lon : 463|32@0+ (1E-007,-180) [-180|180] "°"  INS,CDC
 SG_ INS_RollingCounter_31B : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_31B : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 906 VCU_38A: 8 VCU
 SG_ VcuCrsSwtDiagInfo : 3|3@0+ (1,0) [0|7] ""  VCU,CDC
 SG_ VcuCrsResuSwtSts : 5|2@0+ (1,0) [0|3] "/"  VCU,CDC
 SG_ VcuCrsSetSwtSts : 7|2@0+ (1,0) [0|3] "/"  VCU,CDC
 SG_ VcuCrsDstSwtPlusSts : 9|2@0+ (1,0) [0|3] "/"  VCU,CDC
 SG_ VcuCrsDstSwtReduceSts : 11|2@0+ (1,0) [0|3] "/"  VCU,CDC
 SG_ VcuRollingCounter_38A : 51|4@0+ (1,0) [0|15] "/"  VCU,CDC
 SG_ VcuCRCCheck_38A : 63|8@0+ (1,0) [0|255] "/"  VCU,CDC

BO_ 353 VCU_161: 64 VCU
 SG_ VcuAccrMod : 1|3@0+ (1,0) [0|7] ""  VCU,APA,CDC
 SG_ VCUShiftPostionValid : 2|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VCUAPARequestEnable : 4|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VCUAPAdriverInterruption : 6|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VCUAccPedShield : 7|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuAccrPedlPosn : 14|10@0+ (0.1,0) [0|102.3] "%"  VCU,APA,CDC
 SG_ VcuCalcnAccrPedlPosn : 18|10@0+ (0.1,0) [0|102.3] "%"  VCU,APA,CDC
 SG_ VcuAPATorqRequestAvailable : 19|1@0+ (1,0) [0|1] "/"  VCU,APA,CDC
 SG_ VcuAccrPedlPosnVld : 20|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuCalcnAccrPedlPosnVld : 24|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuEnyRecyclMod : 37|7@0+ (1,0) [0|127] ""  VCU,APA,CDC
 SG_ VcuComFltSts : 39|2@0+ (1,0) [0|3] "/"  VCU,APA,CDC
 SG_ VcuSimnEpbSwtSts : 45|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VcuSimnEpbSwtStsVld : 46|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuGearPosn : 65|3@0+ (1,0) [0|7] ""  VCU,APA,CDC,INS,LVSM
 SG_ VcuPtTqReqAvl : 74|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VcuPtTqRealVld : 75|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMinVld : 76|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMaxVld : 77|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuOvrdModReq : 78|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMax : 87|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuPtTqLimMin : 103|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuShiftLvlPosn : 119|4@0+ (1,0) [0|15] "/"  VCU,APA,CDC
 SG_ VcuPtTqReal : 135|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuVehWhlReqTqVld : 149|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuVehGearPosnVld : 150|1@0+ (1,0) [0|1] ""  VCU,APA,CDC,INS,LVSM
 SG_ VcuRdySts : 151|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuVehWhlReqTq : 159|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuBattlowU : 175|8@0+ (0.1,0) [0|25.5] "V"  VCU,APA,CDC,INS
 SG_ VcuResiMilg : 199|11@0+ (1,0) [0|2047] "km"  VCU,CDC
 SG_ GW_ADSSecOCVerifyFailureFlag : 204|1@0+ (1,0) [0|1] "/"  VCU,CDC
 SG_ VcuSwVers0 : 263|8@0+ (1,0) [0|255] ""  VCU,CDC
 SG_ VcuSwVers1 : 271|8@0+ (1,0) [0|255] ""  VCU,CDC
 SG_ VcuSwVers2 : 279|8@0+ (1,0) [0|255] ""  VCU,CDC
 SG_ VcuSwVers3 : 287|8@0+ (1,0) [0|255] ""  VCU,CDC
 SG_ VcuSwVers4 : 295|16@0+ (1,0) [0|65535] ""  VCU,CDC
 SG_ VcuSwVers5 : 311|8@0+ (1,0) [0|255] ""  VCU,CDC
 SG_ VcuSwVers6 : 319|8@0+ (1,0) [0|255] ""  VCU,CDC
 SG_ VcuCycCntr161 : 491|4@0+ (1,0) [0|15] ""  VCU,APA,CDC,INS
 SG_ VcuCrcChk161 : 503|16@0+ (1,0) [0|65535] ""  VCU,APA,CDC,INS

BO_ 1527 CDC_5F7: 16 CDC
 SG_ ADCSwVers0 : 7|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCSwVers1 : 15|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCSwVers2 : 23|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCSwVers3 : 31|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCSwVers4 : 39|16@0+ (1,0) [0|65535] ""  CDC
 SG_ ADCSwVers5 : 55|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCSwVers6 : 63|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCHwVers0 : 71|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCHwVers1 : 79|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCHwVers2 : 87|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCHwVers3 : 95|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCHwVers4 : 103|16@0+ (1,0) [0|65535] ""  CDC
 SG_ ADCHwVers5 : 119|8@0+ (1,0) [0|255] ""  CDC
 SG_ ADCHwVers6 : 127|8@0+ (1,0) [0|255] ""  CDC

BO_ 786 CDC_312: 64 CDC
 SG_ ADS_UDLCVoiceRecStatus : 93|3@0+ (1,0) [0|7] "/"  CDC,BDC
 SG_ ADS_ALCStatus : 479|8@0+ (1,0) [0|255] ""  CDC

BO_ 580 CDC_244: 64 CDC
 SG_ ACC_ACCTargetAcceleration : 7|8@0+ (0.05,-10) [-10|2.75] "m/s2"  CDC,GW
 SG_ ACC_LDWVibrationWarningReq : 11|2@0+ (1,0) [0|3] "/"  CDC,GW
 SG_ ADCReqMode : 17|2@0+ (1,0) [0|3] ""  CDC,GW
 SG_ ACC_LDWShakeLevStatus : 21|2@0+ (1,0) [0|3] "/"  CDC,GW
 SG_ ACC_DecToStop : 31|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_CDDActive : 44|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_RollingCounter_24E : 51|4@0+ (1,0) [0|15] "/"  CDC,VCU
 SG_ ACC_ACCMode : 54|3@0+ (1,0) [0|7] "/"  CDC,GW,VCU,APA
 SG_ ACC_Driveoff_Request : 55|1@0+ (1,0) [0|1] "/"  CDC,GW,VCU
 SG_ ACC_CRCCheck_24E : 63|8@0+ (1,0) [0|255] "/"  CDC,VCU
 SG_ ACC_AEBTargetDeceleration : 71|16@0+ (0.0005,-16) [-16|16] "m/s2"  CDC,GW
 SG_ ACC_AWBlevel : 83|4@0+ (1,0) [0|15] "/"  CDC,GW
 SG_ ACC_ABAActive : 84|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_ABAlevel : 86|2@0+ (1,0) [0|3] "/"  CDC,GW
 SG_ ACC_AEBActive : 87|1@0+ (1,0) [0|1] "/"  CDC,GW,VCU,APA
 SG_ ACC_AccTrqReq : 89|15@0+ (1,-16384) [-16384|16383] "Nm"  CDC,VCU
 SG_ ACC_AEBVehilceHoldReq : 90|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_PrefillActive : 94|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_AWBActive : 95|1@0+ (1,0) [0|1] "/"  CDC,GW,APA
 SG_ ACC_AEBCtrlType : 104|4@0+ (1,0) [0|15] "/"  CDC,GW,APA
 SG_ ACC_AccTrqReqActive : 106|1@0+ (1,0) [0|1] "/"  CDC,VCU
 SG_ ACC_RollingCounter_25E : 115|4@0+ (1,0) [0|15] "/"  CDC,VCU
 SG_ ACC_CRCCheck_25E : 127|8@0+ (1,0) [0|255] "/"  CDC,VCU
 SG_ ADS_DDSASafetyStopCallReq : 153|2@0+ (1,0) [0|3] "-"  CDC,TBOX
 SG_ ADS_RollingCounter_244 : 427|4@0+ (1,0) [0|15] "/"  CDC,GW,VCU
 SG_ ADC_DDSAStatus : 430|3@0+ (1,0) [0|7] "/"  CDC,TBOX
 SG_ ADS_CRCCheck_244 : 439|16@0+ (1,0) [0|65535] "/"  CDC,GW,VCU

BO_ 775 CDC_307: 64 CDC
 SG_ ACC_ACCEPBrequest : 23|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_SaftyBeltVibrationReq : 42|1@0+ (1,0) [0|1] "/"  CDC,BDC,SRS
 SG_ ACC_LngTakeOverReqReason : 481|5@0+ (1,0) [0|31] "/"  CDC,TBOX
 SG_ ACC_RollingCounter_307 : 491|4@0+ (1,0) [0|15] "/"  CDC,GW,APA
 SG_ ACC_CRCCheck_307 : 503|16@0+ (1,0) [0|65535] "/"  CDC,GW,APA

BO_ 1785 CDC_6F9: 8 CDC
 SG_ ADS_DTC1_HighByte : 7|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC1_MiddByte : 15|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC1_LowByte : 23|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC1_Status : 31|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC2_HighByte : 39|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC2_MiddByte : 47|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC2_LowByte : 55|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ ADS_DTC2_Status : 63|8@0+ (1,0) [0|255] "/"  CDC,TBOX

BO_ 583 CDC_247: 32 CDC
 SG_ APA_SteeringAngleReqProtection : 1|2@0+ (1,0) [0|3] "/"  CDC,APA,GW,TBOX
 SG_ APA_ErrorStatus : 2|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_indication : 5|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ APA_APAOnOff : 6|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_EmergenceBrake : 7|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_SteeringAngleReq : 15|16@0- (0.1,0) [-780|780] "degree"  CDC,APA,GW,TBOX
 SG_ APA_RemoteOnOff : 25|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_ButtonPress : 26|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_IncreasePressureReq : 27|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_TurnLightsCommand : 29|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_ParkNotice_4 : 32|6@0+ (1,0) [0|63] "/"  CDC,APA,TBOX
 SG_ APA_ParkNotice : 39|6@0+ (1,0) [0|63] "/"  CDC,APA,TBOX
 SG_ APA_ParkingPercentage : 42|7@0+ (1,0) [0|100] "%"  CDC,APA,TBOX
 SG_ APA_RollingCounter_264 : 51|4@0+ (1,0) [0|15] "/"  CDC,APA
 SG_ APA_CRCCheck_264 : 63|8@0+ (1,0) [0|255] "/"  CDC,APA
 SG_ APA_EPBrequest : 65|2@0+ (1,0) [0|3] "/"  CDC,APA,GW,TBOX
 SG_ APA_EPBrequestValid : 66|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_TargetAccelerationValid : 67|1@0+ (1,0) [0|1] "NA"  CDC,APA,GW,TBOX
 SG_ APA_TransPRNDShiftRequest : 70|3@0+ (1,0) [0|7] "/"  CDC,APA,VCU,TBOX
 SG_ APA_TransPRNDShiftReqValid : 71|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_TargetAcceleration : 79|8@0+ (0.05,-5) [-5|7.75] "m/s2"  CDC,APA,GW,TBOX
 SG_ APA_EngTorqReq : 87|10@0+ (0.097847,0) [0|99.999634] "%"  CDC,APA,TBOX
 SG_ APA_Activation_Status : 89|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ APA_TransPRNDShiftEnable : 90|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_LSCAction : 91|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX,VCU
 SG_ APA_HSAHDforbidden : 92|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_EngineTrqReqEnable : 93|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_AccPedShieldReq : 97|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_ESPDecompressionModel : 98|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_PrefillReq : 104|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_DynamicSlotWarning : 105|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_SlotNotice : 109|4@0+ (1,0) [0|15] "/"  CDC,APA,TBOX
 SG_ APA_TCUClutchCombinationReq : 110|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_TrqHoldForTCUCl : 111|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_RollingCounter_26C : 115|4@0+ (1,0) [0|15] ""  CDC,APA,VCU
 SG_ APA_CRCCheck_26C : 127|8@0+ (1,0) [0|15] "/"  CDC,APA,VCU
 SG_ APA_PtTorqReq : 135|16@0+ (1,-32768) [-32768|32767] "Nm"  CDC,APA,TBOX,VCU,GW
 SG_ APA_ESPDistToStop : 147|12@0+ (1,0) [0|4095] ""  CDC,APA,GW,TBOX
 SG_ APA_ESP_BrakeFunctionMode : 150|3@0+ (1,0) [0|7] ""  CDC,APA,GW,TBOX
 SG_ APA_PtTrqReqValid : 151|1@0+ (1,0) [0|1] ""  CDC,APA,GW,VCU,TBOX
 SG_ APA_VCUReadyReq : 166|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU
 SG_ APA_ESP_StandstillRequest : 167|1@0+ (1,0) [0|1] ""  CDC,APA,GW,TBOX
 SG_ APA_RollingCounter_236 : 179|4@0+ (1,0) [0|15] ""  CDC,APA,VCU
 SG_ APA_CRC_Checksum_236 : 191|8@0+ (1,0) [0|255] ""  CDC,APA,VCU
 SG_ APA_RollingCounter_247 : 235|4@0+ (1,0) [0|15] "/"  CDC,APA,TBOX,GW,VCU
 SG_ APA_CRCCheck_247 : 247|16@0+ (1,0) [0|65535] "/"  CDC,APA,TBOX,GW,VCU

BO_ 710 CDC_2C6: 64 CDC
 SG_ APA_ParkNotice_5 : 70|7@0+ (1,0) [0|127] "/"  CDC,APA,TBOX
 SG_ APA_LAEBReq : 71|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_LAEBStatus : 78|3@0+ (1,0) [0|7] "/"  CDC,APA,VCU,TBOX
 SG_ APA_BLEConnectionRemind : 79|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_LAEBNotice : 82|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ APA_RemoteParkingUsingRemind : 83|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_ASPAvailableStatus : 84|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_ASPStatus : 86|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_CrossModeSelectReq : 87|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_BCMHornCommand : 97|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_vehicleFrontdetect : 99|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_ReleasePressureReq : 100|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_PEPS_EngineOffLockRequest : 101|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_RADSNotice : 108|6@0+ (1,0) [0|63] "/"  CDC,APA,TBOX
 SG_ APA_RollingCounter_2D4 : 115|4@0+ (1,0) [0|15] "/"  CDC,APA,VCU
 SG_ APA_PEPS_EngineOffRequest : 116|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_CRCCheck_2D4 : 127|8@0+ (1,0) [0|255] "/"  CDC,APA,VCU
 SG_ APA_RollingCounter_2C6 : 491|4@0+ (1,0) [0|15] "/"  CDC,APA,VCU,GW
 SG_ APA_CRCCheck_2C6 : 503|16@0+ (1,0) [0|65535] "/"  CDC,APA,VCU,GW

BO_ 1760 CDC_6E0: 8 CDC
 SG_ APA_AuthenticationStatus : 7|64@0+ (1,0) [0|1.84467440737096E+019] "/"  CDC,APA,VCU,TBOX

BO_ 798 CDC_31E: 64 CDC
 SG_ RRS_RRSDistance : 37|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ RRS_SideZoneStatus_Front : 39|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ RRS_WarningFrequency : 41|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ RRS_SideZoneStatus_Rear : 43|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ RRS_WarningType : 47|4@0+ (1,0) [0|15] "/"  CDC,APA,TBOX
 SG_ RRS_SwitchIndicatorError : 60|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_SystemFailureFlag : 284|5@0+ (1,0) [0|31] "/"  CDC,APA,TBOX

BO_ 442 CDC_1BA: 32 CDC
 SG_ ACC_MotorTorqueMaxLimitRequest : 7|11@0+ (0.02,-20.48) [-20.48|20.44] "Nm"  CDC,GW
 SG_ ACC_MotorTorqueMinLimitRequest : 12|11@0+ (0.02,-20.48) [-20.48|20.44] "Nm"  CDC,GW
 SG_ ACC_LatAngReq : 17|14@0+ (0.1,-720) [-720|720] "degree"  CDC,GW,BDC
 SG_ ADS_ESSActive : 34|1@0+ (1,0) [0|1] "/"  CDC,GW
 SG_ ACC_LatAngReqActive : 35|1@0+ (1,0) [0|1] "/"  CDC,GW,BDC
 SG_ ACC_RollingCounter_1BA_0 : 51|4@0+ (1,0) [0|15] "/"  CDC,GW,BDC
 SG_ ACC_CRCCheck_1BA_0 : 63|8@0+ (1,0) [0|255] "/"  CDC,GW,BDC
 SG_ ADS_ErrorStatus : 72|1@0+ (1,0) [0|1] ""  CDC,VCU,GW
 SG_ ACC_ADCReqType : 84|2@0+ (1,0) [0|3] "/"  CDC,GW
 SG_ ADS_Reqmode : 87|3@0+ (1,0) [0|7] "/"  CDC,GW
 SG_ ACC_RollingCounter_1BA_1 : 115|4@0+ (1,0) [0|15] "/"  CDC,GW,EPS
 SG_ ACC_CRCCheck_1BA_1 : 127|8@0+ (1,0) [0|255] "/"  CDC,GW,EPS

BO_ 839 GW_347: 8 GW
 SG_ TpmsLeFrntTireP : 15|8@0+ (1.373,0) [0|350] "KPa"  GW,CDC
 SG_ TpmsLeFrntTirePWarn : 47|3@0+ (1,0) [0|7] "/"  GW,CDC

BO_ 680 GW_2A8: 64 GW
 SG_ TmsActPwr : 18|11@0+ (0.01,0) [0|20.47] "kW"  GW,ADS,CDC,APA
 SG_ TmsAcEnvtlT : 39|8@0+ (0.5,-40) [-40|87.5] "℃"  GW,RRS,APA,CDC
 SG_ TmsFrntBlowMod : 186|3@0+ (1,0) [0|7] ""  GW,RRS,APA,CDC
 SG_ TmsAcEnvtlTVld : 432|1@0+ (1,0) [0|1] "/"  GW,RRS,APA,CDC
 SG_ TmsCycCntr2A8 : 491|4@0+ (1,0) [0|15] ""  GW,RRS,APA,CDC
 SG_ TmsCrcChk2A8 : 503|16@0+ (1,0) [0|65535] ""  GW,RRS,APA,CDC

BO_ 450 GW_1C2: 64 GW
 SG_ EspReWhlIncTarTqActv : 37|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspReWhlDecTarTqActv : 38|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspTcsActvSts : 52|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspTcsFailr : 53|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspFctOpenSts : 54|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspActvSts : 55|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspAbsActv : 72|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspAbsFailr : 73|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspEbdFailr : 87|1@0+ (1,0) [0|1] ""  GW,CDC
 SG_ EspReWhlDecTarTq : 271|16@0+ (1,-32768) [-32768|32767] "Nm"  GW,APA,CDC
 SG_ EspReWhlIncTarTq : 335|16@0+ (1,-32768) [-32768|32767] "Nm"  GW,APA,CDC
 SG_ EspVehStandstill : 351|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspAutoHoldActvSts : 353|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ IBCU_BrakeDiscHighTempWarning : 390|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EspBrkLiOnReq : 397|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ MbRgnTarWhlQlfr : 413|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EspVdcActvSts : 414|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ MbRgnTarWhl : 423|16@0+ (1,-32768) [-32768|32766] "Nm"  GW,APA,CDC
 SG_ IBCU_ADCActiveState : 481|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_CommunicationInvalid : 483|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_ADCFullFuncAvail : 485|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_ADCReducedFuncAvail : 487|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EspCycCntr1C2 : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ EspCrcChk1C2 : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC

BO_ 523 GW_20B: 64 GW
 SG_ WhlSpdRiFrntData : 4|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdRiFrntDir : 6|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiFrntDataVld : 7|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdLeFrntData : 20|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdLeFrntDir : 22|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdLeFrntDataVld : 23|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ IBCU_APCActiveStatus : 33|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiReData : 68|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdRiReDir : 70|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiReDataVld : 71|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdLeReData : 84|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdLeReDir : 86|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdLeReDataVld : 87|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ ESP_UrgencyBrakeAlarm : 118|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_APA_DriverOverride : 119|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ WhlSpdFrntLePls : 215|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdFrntRiPls : 223|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdReRiPls : 231|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdReLePls : 239|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ ESP_AWBactive : 256|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_AEBdecActive : 257|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_AEBAvailable : 258|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_PrefillAvailable : 259|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_PrefillActive : 260|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_ABAavailable : 261|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_ABAactive : 262|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_BrakeForce : 263|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_BrakeOverHeatBrakeDiscTemp : 270|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ESP_AWBavailable : 271|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ IBCU_APCReducedFuncAvail : 276|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_QDCACC : 279|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_APCFullFuncAvail : 281|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ IbcuCycCntr20B : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC,INS
 SG_ IBCU_ADCRampOffSuspendState : 493|2@0+ (1,0) [0|3] ""  GW,CDC
 SG_ IbcuCrcChk20B : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC,INS

BO_ 378 GW_17A: 64 GW
 SG_ EspVehSpd : 36|13@0+ (0.05625,0) [0|460.74375] "km/h"  GW,APA,CDC,INS,LVSM
 SG_ EspVehSpdVld : 37|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS,LVSM
 SG_ EPB_APArequest_Available : 64|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPB_AchievedClampForce_Primary : 69|5@0+ (1,0) [0|31] "KN"  GW,APA,CDC
 SG_ EPB_AchievedClampForce : 85|5@0+ (1,0) [0|31] "KN"  GW,APA,CDC
 SG_ EPB_FailStatuss_Primary : 87|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ WheelPressureFrontRight_Model : 95|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ WheelPressureRearLeft_Model : 99|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ WheelPressureRearRight_Model : 119|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ WheelPressureFrontLeft_Model : 467|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ ESP_RollingCounter_17A : 491|4@0+ (1,0) [0|15] "/"  GW,APA,CDC,LVSM,INS
 SG_ ESP_CRCCheck_17A : 503|16@0+ (1,0) [0|65535] "/"  GW,APA,CDC,LVSM,INS

BO_ 962 GW_3C2: 64 GW
 SG_ EpbFailrSts : 1|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ EpbSts : 7|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EpbFctLamp : 13|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EpbFailrLamp : 15|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EspEpbReqAvl : 28|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EpbFctLamp_Primary : 65|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EpbFailrLamp_Primary : 71|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EpbStsPrimary : 79|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EspCycCntr3C2 : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ EspCrcChk3C2 : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC

BO_ 706 GW_2C2: 64 GW
 SG_ IbBrkPedlStsGb : 7|8@0+ (1,0) [0|255] "%"  GW,APA,CDC
 SG_ IbBrkPedlTrvlDrvr : 11|12@0+ (0.015625,-5) [-5|47] "mm"  GW,CDC
 SG_ IbBrkPedlStsGbVld : 15|2@0+ (1,0) [0|2] ""  GW,APA,CDC
 SG_ IbBrkPedlTrvlDrvrVld : 31|2@0+ (1,0) [0|2] ""  GW,CDC
 SG_ IBBrkPedlModSts : 52|8@0+ (1,0) [0|255] ""  GW,APA,CDC
 SG_ IBBrkPedlModSwtAvl : 53|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ IBCU_PFSBrakePressure : 159|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ IBCU_PFSBrakePressureValid : 163|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ IBCU_PlungerBrakePressure : 175|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ IBCU_PlungerBrakePressureValid : 179|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ IbCycCntr2C2 : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ IbCrcChk2C2 : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC

BO_ 555 GW_22B: 64 GW
 SG_ RbmWheel_Speed_FR_Data : 4|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC
 SG_ RbmWheel_Speed_FR_Direction : 6|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_FR_Valid_Data : 7|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_FL_Data : 20|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC
 SG_ RbmWheel_Speed_FL_Direction : 22|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ RbmWheel_Speed_FL_Valid_Data : 23|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ RbmVehStandstill : 36|1@0+ (1,0) [0|1] ""  GW,CDC
 SG_ RbmWheel_Speed_RR_Data : 68|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC
 SG_ RbmWheel_Speed_RR_Direction : 70|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_RR_Valid_Data : 71|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_RL_Data : 84|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC
 SG_ RbmWheel_Speed_RL_Direction : 86|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_RL_Valid_Data : 87|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EngineTrqLimit : 135|16@0- (1,0) [-30000|30000] "Nm"  GW,CDC
 SG_ EngineTrqLimitEna : 151|1@0+ (1,0) [0|1] ""  GW,CDC
 SG_ RbmESP_BrakeLightOnRequest : 197|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ RbmWheel_Speed_FL_Pulse : 215|8@0+ (1,0) [0|254] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_FR_Pulse : 223|8@0+ (1,0) [0|254] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_RR_Pulse : 231|8@0+ (1,0) [0|254] ""  GW,APA,CDC
 SG_ RbmWheel_Speed_RL_Pulse : 239|8@0+ (1,0) [0|254] ""  GW,APA,CDC
 SG_ RbmESP_QDCACC : 279|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ RbmEpbFctLamp_Secondary : 410|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ RbmSBSFailr : 411|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ RbmEspVehSpdVld : 453|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ RbmSBSActv : 455|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ RbmEPB_FailStatuss_Secondary : 465|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ RBM_FullFuncAvail : 473|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ RbmEpbFailrLamp_Secondary : 476|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ RBM_ADCActiveState : 485|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ RBM_CommunicationInvalid : 487|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ RbmCycCntr22B : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ RbmCrcChk22B : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC

BO_ 382 GW_17E: 8 GW
 SG_ EPS_MeasuredTorsionBarTorque : 7|12@0+ (0.01,-20.48) [-20.48|20.46] "Nm"  GW,CDC
 SG_ EPS_ADS_Abortfeedback : 11|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_Pinionang : 21|14@0+ (0.1,-720) [-720|720] "degree"  GW,CDC
 SG_ EPS_Pinionang_Valid : 22|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EPS_Handwheel_Relang : 39|15@0+ (0.1,-1080) [-1080|1080] "degree"  GW,CDC
 SG_ EPS_MeasuredTorsionBarTorqValid : 40|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EPS_RollingCounter_17E : 51|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_LatCtrlAvailabilityStatus : 53|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_LatCtrlActive : 54|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EPS_Handwheel_Relang_Valid : 55|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EPS_CRCCheck_17E : 63|8@0+ (1,0) [0|255] "/"  GW,CDC

BO_ 368 GW_170: 8 GW
 SG_ EPS_ActualMotorTorq : 7|12@0+ (0.01,-20.48) [-20.48|20.46] "Nm"  GW,CDC
 SG_ EPS_MaxSafetyTorsionBarTorq : 8|11@0+ (0.02,-20.48) [-20.48|20.44] "Nm"  GW,CDC
 SG_ EPS_MinSafetyTorsionBarTorq : 29|11@0+ (0.02,-20.48) [-20.48|20.44] "Nm"  GW,CDC
 SG_ EPS_ActualTorsionBarTorq : 34|11@0+ (0.02,-20.48) [-20.48|20.44] "Nm"  GW,CDC
 SG_ EPS_RollingCounter_170 : 51|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_fault_state : 55|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_CRCCheck_170 : 63|8@0+ (1,0) [0|255] "/"  GW,CDC

BO_ 591 GW_24F: 8 GW
 SG_ EPS_ElectPowerConsumption : 7|8@0+ (0.5,0) [0|127] "A"  GW,APA,CDC
 SG_ EPS_TorqSensorStatus : 8|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_APA_EpasFAILED : 9|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_ModeSwitchSt : 10|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_APA_Abortfeedback : 14|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_SteeringTorque : 23|8@0+ (0.1794,-22.78) [-22.78|22.78] "Nm"  GW,APA,CDC
 SG_ EPS_IACC_abortreason : 28|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_APA_ControlFeedback : 29|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_LDW_ShakeLevStatus : 31|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_ADASActiveMode : 33|3@0+ (1,0) [0|7] "/"  GW,CDC
 SG_ EPS_ADS_ControlFeedback : 36|3@0+ (1,0) [0|7] "/"  GW,CDC
 SG_ EpsFaild : 42|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_SystemSt : 46|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_RollingCounter_24F : 51|4@0+ (1,0) [0|15] "/"  GW,CDC,APA
 SG_ EPS_ConcussAvailabilityStatus : 53|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EpsSteerModFb : 55|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_CRCCheck_24F : 63|8@0+ (1,0) [0|255] "/"  GW,CDC,APA

BO_ 384 GW_180: 8 GW
 SG_ EpsSasSteerAg : 7|16@0- (0.1,0) [-780|780] "degree"  GW,APA,CDC,LVSM
 SG_ EpsSteerAgRate : 23|8@0+ (4,0) [0|1016] "deg/s"  GW,APA,CDC
 SG_ EpsSasCalSts : 29|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EpsSteerAgSensFilr : 30|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EpsSasSteerAgVld : 31|1@0+ (1,0) [0|1] "/"  GW,APA,CDC,LVSM
 SG_ EpsCycCntr180 : 51|4@0+ (1,0) [0|15] "/"  GW,APA,CDC,LVSM
 SG_ EpsCrcChk180 : 63|8@0+ (1,0) [0|255] "/"  GW,APA,CDC,LVSM

BO_ 1683 CDC_693: 8 CDC
 SG_ FR_DTC1_HighByte : 7|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC1_MiddByte : 15|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC1_LowByte : 23|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC1_Status : 31|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC2_HighByte : 39|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC2_MiddByte : 47|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC2_LowByte : 55|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FR_DTC2_Status : 63|8@0+ (1,0) [0|255] "/"  CDC,TBOX

BO_ 1763 CDC_6E3: 8 CDC
 SG_ FLR_DTC1_HighByte : 7|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC1_MiddByte : 15|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC1_LowByte : 23|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC1_Status : 31|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC2_HighByte : 39|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC2_MiddByte : 47|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC2_LowByte : 55|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FLR_DTC2_Status : 63|8@0+ (1,0) [0|255] "/"  CDC,TBOX

BO_ 1687 CDC_697: 8 CDC
 SG_ RLR_DTC1_HighByte : 7|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC1_MiddByte : 15|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC1_LowByte : 23|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC1_Status : 31|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC2_HighByte : 39|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC2_MiddByte : 47|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC2_LowByte : 55|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RLR_DTC2_Status : 63|8@0+ (1,0) [0|255] "/"  CDC,TBOX

BO_ 1776 CDC_6F0: 8 CDC
 SG_ FRR_DTC1_HighByte : 7|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC1_MiddByte : 15|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC1_LowByte : 23|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC1_Status : 31|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC2_HighByte : 39|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC2_MiddByte : 47|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC2_LowByte : 55|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ FRR_DTC2_Status : 63|8@0+ (1,0) [0|255] "/"  CDC,TBOX

BO_ 1755 CDC_6DB: 8 CDC
 SG_ RRR_DTC1_HighByte : 7|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC1_MiddByte : 15|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC1_LowByte : 23|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC1_Status : 31|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC2_HighByte : 39|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC2_MiddByte : 47|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC2_LowByte : 55|8@0+ (1,0) [0|255] "/"  CDC,TBOX
 SG_ RRR_DTC2_Status : 63|8@0+ (1,0) [0|255] "/"  CDC,TBOX

BO_ 697 CDC_2B9: 64 CDC
 SG_ APA_FunctionOnOffSts : 4|6@0+ (1,0) [0|63] "/"  CDC,VCU,TBOX
 SG_ APA_Condition_Notice : 8|5@0+ (1,0) [0|31] ""  CDC,TBOX
 SG_ APA_TouchInfOnOffRes : 11|3@0+ (1,0) [0|7] ""  CDC,TBOX
 SG_ APA_AVP_Notice : 19|6@0+ (1,0) [0|63] ""  CDC,TBOX
 SG_ APA_HZP_Notice : 29|6@0+ (1,0) [0|63] ""  CDC,TBOX
 SG_ APA_ViewActual : 33|5@0+ (1,0) [0|31] ""  CDC,APA,TBOX
 SG_ APA_Summon_Notice : 39|6@0+ (1,0) [0|63] ""  CDC,TBOX
 SG_ APA_ActivationSts : 42|3@0+ (1,0) [0|7] ""  CDC,APA,TBOX
 SG_ APA_ReadySts : 44|2@0+ (1,0) [0|3] ""  CDC,APA,TBOX
 SG_ APA_ParkingSlot_ExtraFeature : 67|4@0+ (1,0) [0|15] "/"  CDC,TBOX
 SG_ APA_ParkingSlot_Type : 71|4@0+ (1,0) [0|15] "/"  CDC,TBOX
 SG_ APA_TurnOnMode : 82|4@0+ (1,0) [0|15] "/"  CDC,TBOX

BO_ 625 INS_271: 64 INS
 SG_ INS_TiStamp : 7|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_TiOut : 37|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 38|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 39|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TIleap_Difference : 47|32@0+ (1,-10000000) [-10000000|100000000] "us"  INS,CDC
 SG_ INS_RollingCounter_271 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_271 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 1474 INS_5C2: 64 INS
 SG_ Odometer_Value_First_Fault : 7|24@0+ (0.1,0) [0|999999] "Km"  INS,CDC
 SG_ Odometer_Value_Last_Fault : 31|24@0+ (0.1,0) [0|999999] "Km"  INS,CDC
 SG_ BatteryVoltage_Record_Fault : 71|8@0+ (0.1,0) [0|25.4] "V"  INS,CDC

BO_ 646 CDC_286: 16 CDC
 SG_ ADS_SYNC_Type : 7|8@0+ (1,0) [0|255] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_CRC : 15|8@0+ (1,0) [0|255] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_SequenceCnt : 19|4@0+ (1,0) [0|15] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_TimeDomain : 23|4@0+ (1,0) [0|15] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_OVS_SGW : 31|8@0+ (1,0) [0|255] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_SyncTime : 39|32@0+ (1,0) [0|4294967295] "-"  CDC,INS,LVSM

BO_ 1394 CDC_572: 16 CDC
 SG_ FRSwVers0 : 7|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRSwVers1 : 15|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRSwVers2 : 23|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRSwVers3 : 31|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRSwVers4 : 39|16@0+ (1,0) [0|65535] ""  CDC
 SG_ FRSwVers5 : 55|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRSwVers6 : 63|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRHwVers0 : 71|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRHwVers1 : 79|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRHwVers2 : 87|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRHwVers3 : 95|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRHwVers4 : 103|16@0+ (1,0) [0|65535] ""  CDC
 SG_ FRHwVers5 : 119|8@0+ (1,0) [0|255] ""  CDC
 SG_ FRHwVers6 : 127|8@0+ (1,0) [0|255] ""  CDC

BO_ 1395 CDC_573: 16 CDC
 SG_ FLRSwVers0 : 7|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRSwVers1 : 15|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRSwVers2 : 23|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRSwVers3 : 31|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRSwVers4 : 39|16@0+ (1,0) [0|65535] ""  CDC,FLR
 SG_ FLRSwVers5 : 55|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRSwVers6 : 63|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRHwVers0 : 71|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRHwVers1 : 79|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRHwVers2 : 87|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRHwVers3 : 95|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRHwVers4 : 103|16@0+ (1,0) [0|65535] ""  CDC,FLR
 SG_ FLRHwVers5 : 119|8@0+ (1,0) [0|255] ""  CDC,FLR
 SG_ FLRHwVers6 : 127|8@0+ (1,0) [0|255] ""  CDC,FLR

BO_ 1397 CDC_575: 16 CDC
 SG_ RLRSwVers0 : 7|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRSwVers1 : 15|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRSwVers2 : 23|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRSwVers3 : 31|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRSwVers4 : 39|16@0+ (1,0) [0|65535] ""  CDC,RLR
 SG_ RLRSwVers5 : 55|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRSwVers6 : 63|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRHwVers0 : 71|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRHwVers1 : 79|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRHwVers2 : 87|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRHwVers3 : 95|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRHwVers4 : 103|16@0+ (1,0) [0|65535] ""  CDC,RLR
 SG_ RLRHwVers5 : 119|8@0+ (1,0) [0|255] ""  CDC,RLR
 SG_ RLRHwVers6 : 127|8@0+ (1,0) [0|255] ""  CDC,RLR

BO_ 1396 CDC_574: 16 CDC
 SG_ FRRSwVers0 : 7|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRSwVers1 : 15|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRSwVers2 : 23|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRSwVers3 : 31|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRSwVers4 : 39|16@0+ (1,0) [0|65535] ""  CDC,FRR
 SG_ FRRSwVers5 : 55|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRSwVers6 : 63|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRHwVers0 : 71|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRHwVers1 : 79|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRHwVers2 : 87|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRHwVers3 : 95|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRHwVers4 : 103|16@0+ (1,0) [0|65535] ""  CDC,FRR
 SG_ FRRHwVers5 : 119|8@0+ (1,0) [0|255] ""  CDC,FRR
 SG_ FRRHwVers6 : 127|8@0+ (1,0) [0|255] ""  CDC,FRR

BO_ 1409 CDC_581: 16 CDC
 SG_ RRRSwVers0 : 7|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRSwVers1 : 15|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRSwVers2 : 23|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRSwVers3 : 31|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRSwVers4 : 39|16@0+ (1,0) [0|65535] ""  CDC,RRR
 SG_ RRRSwVers5 : 55|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRSwVers6 : 63|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRHwVers0 : 71|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRHwVers1 : 79|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRHwVers2 : 87|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRHwVers3 : 95|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRHwVers4 : 103|16@0+ (1,0) [0|65535] ""  CDC,RRR
 SG_ RRRHwVers5 : 119|8@0+ (1,0) [0|255] ""  CDC,RRR
 SG_ RRRHwVers6 : 127|8@0+ (1,0) [0|255] ""  CDC,RRR

BO_ 547 GW_223: 8 GW
 SG_ PmuMainLoopUBeforeSwtOff : 39|8@0+ (0.1,0) [0|25] "V "  GW,CDC
 SG_ PmuSubsidaryLoopUBeforeSwtOff : 47|8@0+ (0.1,0) [0|25] "V "  GW,CDC
 SG_ PMU_RollingCounter_223 : 51|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ Pmufailurestatus : 55|3@0+ (1,0) [0|7] ""  GW,CDC
 SG_ PMU_CRCCheck_223 : 63|8@0+ (1,0) [0|255] "/"  GW,CDC

BO_ 794 CDC_31A: 64 CDC
 SG_ ACC_LatTakeoverReqReason : 481|5@0+ (1,0) [0|31] "/"  CDC,TBOX

BO_ 1337 LVSM_539: 8 LVSM
 SG_ ADC_BlindZoneFunSts : 7|2@0+ (1,0) [0|1] ""  LVSM,GW,CDC

BO_ 1428 CDC_594: 8 CDC
 SG_ FR_WakeUp_BYTE4BIT0 : 32|1@0+ (1,0) [0|1] "/"  CDC
 SG_ FR_WakeUp_BYTE4BIT1 : 33|1@0+ (1,0) [0|1] "/"  CDC
 SG_ FR_NotSleep_BYTE7BIT0 : 56|1@0+ (1,0) [0|1] "/"  CDC
 SG_ FR_NotSleep_BYTE7BIT1 : 57|1@0+ (1,0) [0|1] "/"  CDC

BO_ 1432 CDC_598: 8 CDC
 SG_ FRR_WakeUp_BYTE4BIT0 : 32|1@0+ (1,0) [0|1] "/"  CDC,FRR
 SG_ FRR_WakeUp_BYTE4BIT1 : 33|1@0+ (1,0) [0|1] "/"  CDC,FRR
 SG_ FRR_NotSleep_BYTE7BIT0 : 56|1@0+ (1,0) [0|1] "/"  CDC,FRR
 SG_ FRR_NotSleep_BYTE7BIT1 : 57|1@0+ (1,0) [0|1] "/"  CDC,FRR

BO_ 1442 CDC_5A2: 8 CDC
 SG_ FLR_WakeUp_BYTE4BIT0 : 32|1@0+ (1,0) [0|1] "/"  CDC,FLR
 SG_ FLR_WakeUp_BYTE4BIT1 : 33|1@0+ (1,0) [0|1] "/"  CDC,FLR
 SG_ FLR_NotSleep_BYTE7BIT0 : 56|1@0+ (1,0) [0|1] "/"  CDC,FLR
 SG_ FLR_NotSleep_BYTE7BIT1 : 57|1@0+ (1,0) [0|1] "/"  CDC,FLR

BO_ 1430 CDC_596: 8 CDC
 SG_ RLR_WakeUp_BYTE4BIT0 : 32|1@0+ (1,0) [0|1] "/"  CDC,RLR
 SG_ RLR_WakeUp_BYTE4BIT1 : 33|1@0+ (1,0) [0|1] "/"  CDC,RLR
 SG_ RLR_NotSleep_BYTE7BIT0 : 56|1@0+ (1,0) [0|1] "/"  CDC,RLR
 SG_ RLR_NotSleep_BYTE7BIT1 : 57|1@0+ (1,0) [0|1] "/"  CDC,RLR

BO_ 1431 CDC_597: 8 CDC
 SG_ RRR_WakeUp_BYTE4BIT0 : 32|1@0+ (1,0) [0|1] "/"  CDC,RRR
 SG_ RRR_WakeUp_BYTE4BIT1 : 33|1@0+ (1,0) [0|1] "/"  CDC,RRR
 SG_ RRR_NotSleep_BYTE7BIT0 : 56|1@0+ (1,0) [0|1] "/"  CDC,RRR
 SG_ RRR_NotSleep_BYTE7BIT1 : 57|1@0+ (1,0) [0|1] "/"  CDC,RRR

BO_ 1423 CDC_58F: 8 CDC
 SG_ ADC_WakeUp_BYTE4BIT0 : 32|1@0+ (1,0) [0|1] "/"  CDC
 SG_ ADC_WakeUp_BYTE4BIT1 : 33|1@0+ (1,0) [0|1] "/"  CDC
 SG_ ADC_NotSleep_BYTE7BIT0 : 56|1@0+ (1,0) [0|1] "/"  CDC
 SG_ ADC_NotSleep_BYTE7BIT1 : 57|1@0+ (1,0) [0|1] "/"  CDC

BO_ 757 LVSM_2F5: 64 LVSM
 SG_ LVSM_TiStamp : 7|32@0+ (0.0001,0) [0|429496.7295] "s"  LVSM,CDC
 SG_ LVSM_TiOut : 37|1@0+ (1,0) [0|1] "-"  LVSM,CDC
 SG_ LVSM_TiLeap : 38|1@0+ (1,0) [0|1] "-"  LVSM,CDC
 SG_ LVSM_TiBas : 39|1@0+ (1,0) [0|1] "-"  LVSM,CDC
 SG_ LVSM_TimeDifference : 47|8@0+ (1,0) [0|255] ""  LVSM,CDC
 SG_ LvsmCycCntr2F5 : 491|4@0+ (1,0) [0|15] ""  LVSM,CDC
 SG_ LvsmCrcChk2F5 : 503|16@0+ (1,0) [0|65535] ""  LVSM,CDC

BO_ 805 CDC_325: 8 CDC
 SG_ ADC_UTCTiYear : 7|8@0+ (1,2000) [2000|2255] "year"  CDC,LVSM
 SG_ ADC_UTCTiDate : 11|5@0+ (1,0) [0|31] "day"  CDC,LVSM
 SG_ ADC_UTCTiMth : 15|4@0+ (1,0) [0|15] "month"  CDC,LVSM
 SG_ ADC_UTCTiMins : 17|6@0+ (1,0) [0|63] "minute"  CDC,LVSM
 SG_ ADC_UTCTiHr : 22|5@0+ (1,0) [0|31] "hour"  CDC,LVSM
 SG_ ADC_UTCTiSec : 27|6@0+ (1,0) [0|63] "second"  CDC,LVSM
 SG_ ADC_UTCTiVld : 37|1@0+ (1,0) [0|1] ""  CDC,LVSM

BO_ 821 LVSM_335: 8 LVSM
 SG_ LVSM_UTCTiYear : 7|8@0+ (1,2000) [2000|2255] "year"  LVSM,CDC
 SG_ LVSM_UTCTiDate : 11|5@0+ (1,0) [0|31] "day"  LVSM,CDC
 SG_ LVSM_UTCTiMth : 15|4@0+ (1,0) [0|15] "month"  LVSM,CDC
 SG_ LVSM_UTCTiMins : 17|6@0+ (1,0) [0|63] "minute"  LVSM,CDC
 SG_ LVSM_UTCTiHr : 22|5@0+ (1,0) [0|31] "hour"  LVSM,CDC
 SG_ LVSM_UTCTiSec : 27|6@0+ (1,0) [0|63] "second"  LVSM,CDC
 SG_ LVSM_UTCTiVld : 37|1@0+ (1,0) [0|1] ""  LVSM,CDC

BO_ 1317 CDC_525: 8 CDC
 SG_ TC397_TimeSyncInit : 0|1@0+ (1,0) [0|1] ""  CDC,INS,LVSM

BO_ 771 CDC_303: 8 CDC
 SG_ ADC_PowerSwitchFlag : 7|2@0+ (1,0) [0|3] "/"  CDC,LVSM,INS

BO_ 1537 CDC_601: 8 CDC
 SG_ VCU_Distance_CMD : 7|8@0+ (1,0) [0|255] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM9 : 8|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM10 : 9|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM11 : 10|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM12 : 11|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_Machine_NUM : 15|4@0+ (1,0) [0|15] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM1 : 16|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM2 : 17|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM3 : 18|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM4 : 19|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM5 : 20|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM6 : 21|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM7 : 22|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM8 : 23|1@0+ (1,0) [0|1] ""  CDC,USS

BO_ 1553 USS_611: 8 USS
 SG_ Ult_Probe_info1 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info2 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info3 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info4 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS

BO_ 1554 USS_612: 8 USS
 SG_ Ult_Probe_info5 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info6 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info7 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info8 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS

BO_ 1555 USS_613: 8 USS
 SG_ Ult_Probe_info9 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info10 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info11 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info12 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS



BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 100000000000;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 100000;
BA_DEF_ SG_  "NWM - WakeupAllowed" ENUM  "no","Yes";
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","Cyclic","NotUsed","IfActive","NoMsgSendType","NotUsed","vector_leerstring";
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 999999;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 1000;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 50000;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 50000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "no","Yes";
BA_DEF_ BO_  "NmMessage" ENUM  "no","Yes";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 65535;
BA_DEF_ BU_  "NmStationAddress" INT 0 63;
BA_DEF_ BU_  "NmNode" ENUM  "no","Yes";
BA_DEF_  "NmBaseAddress" HEX 1024 1087;
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ SG_  "SystemSignalLongSymbol" STRING ;
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigSendType" "NoSigSendType";
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "NWM - WakeupAllowed" "";
BA_DEF_DEF_  "GenMsgSendType" "NoMsgSendType";
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "NmMessage" "";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmNode" "";
BA_DEF_DEF_  "NmBaseAddress" 1024;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_DEF_DEF_  "SystemSignalLongSymbol" "";
BA_ "BusType" "CAN FD";
BA_ "DBName" "CAN";
BA_ "GenMsgCycleTimeFast" BO_ 759 100;
BA_ "GenMsgCycleTime" BO_ 759 100;
BA_ "GenMsgSendType" BO_ 759 0;
BA_ "VFrameFormat" BO_ 759 14;
BA_ "GenMsgCycleTimeFast" BO_ 1459 40;
BA_ "GenMsgCycleTime" BO_ 1459 40;
BA_ "GenMsgSendType" BO_ 1459 0;
BA_ "VFrameFormat" BO_ 1459 14;
BA_ "GenMsgCycleTimeFast" BO_ 383 10;
BA_ "GenMsgCycleTime" BO_ 383 10;
BA_ "GenMsgSendType" BO_ 383 0;
BA_ "VFrameFormat" BO_ 383 14;
BA_ "GenMsgCycleTimeFast" BO_ 326 10;
BA_ "GenMsgCycleTime" BO_ 326 10;
BA_ "GenMsgSendType" BO_ 326 0;
BA_ "VFrameFormat" BO_ 326 14;
BA_ "GenMsgCycleTimeFast" BO_ 795 100;
BA_ "GenMsgCycleTime" BO_ 795 100;
BA_ "GenMsgSendType" BO_ 795 0;
BA_ "VFrameFormat" BO_ 795 14;
BA_ "GenMsgCycleTimeFast" BO_ 906 100;
BA_ "GenMsgCycleTime" BO_ 906 100;
BA_ "GenMsgSendType" BO_ 906 0;
BA_ "VFrameFormat" BO_ 906 0;
BA_ "GenMsgCycleTimeFast" BO_ 353 10;
BA_ "GenMsgCycleTime" BO_ 353 10;
BA_ "GenMsgSendType" BO_ 353 0;
BA_ "VFrameFormat" BO_ 353 14;
BA_ "GenMsgCycleTimeFast" BO_ 1527 1000;
BA_ "GenMsgCycleTime" BO_ 1527 1000;
BA_ "GenMsgSendType" BO_ 1527 0;
BA_ "VFrameFormat" BO_ 1527 14;
BA_ "GenMsgCycleTimeFast" BO_ 786 100;
BA_ "GenMsgCycleTime" BO_ 786 100;
BA_ "GenMsgSendType" BO_ 786 0;
BA_ "VFrameFormat" BO_ 786 14;
BA_ "GenMsgCycleTimeFast" BO_ 580 20;
BA_ "GenMsgCycleTime" BO_ 580 20;
BA_ "GenMsgSendType" BO_ 580 0;
BA_ "VFrameFormat" BO_ 580 14;
BA_ "GenMsgCycleTimeFast" BO_ 775 100;
BA_ "GenMsgCycleTime" BO_ 775 100;
BA_ "GenMsgSendType" BO_ 775 0;
BA_ "VFrameFormat" BO_ 775 14;
BA_ "GenMsgCycleTimeFast" BO_ 1785 500;
BA_ "GenMsgCycleTime" BO_ 1785 500;
BA_ "GenMsgSendType" BO_ 1785 0;
BA_ "VFrameFormat" BO_ 1785 0;
BA_ "GenMsgCycleTimeFast" BO_ 583 20;
BA_ "GenMsgCycleTime" BO_ 583 20;
BA_ "GenMsgSendType" BO_ 583 0;
BA_ "VFrameFormat" BO_ 583 14;
BA_ "GenMsgCycleTimeFast" BO_ 710 40;
BA_ "GenMsgCycleTime" BO_ 710 40;
BA_ "GenMsgSendType" BO_ 710 0;
BA_ "VFrameFormat" BO_ 710 14;
BA_ "GenMsgSendType" BO_ 1760 8;
BA_ "VFrameFormat" BO_ 1760 0;
BA_ "GenMsgCycleTimeFast" BO_ 798 100;
BA_ "GenMsgCycleTime" BO_ 798 100;
BA_ "GenMsgSendType" BO_ 798 0;
BA_ "VFrameFormat" BO_ 798 14;
BA_ "GenMsgCycleTimeFast" BO_ 442 10;
BA_ "GenMsgCycleTime" BO_ 442 10;
BA_ "GenMsgSendType" BO_ 442 0;
BA_ "VFrameFormat" BO_ 442 14;
BA_ "GenMsgCycleTimeFast" BO_ 839 500;
BA_ "GenMsgCycleTime" BO_ 839 500;
BA_ "GenMsgSendType" BO_ 839 0;
BA_ "VFrameFormat" BO_ 839 0;
BA_ "GenMsgCycleTimeFast" BO_ 680 100;
BA_ "GenMsgCycleTime" BO_ 680 100;
BA_ "GenMsgSendType" BO_ 680 0;
BA_ "VFrameFormat" BO_ 680 14;
BA_ "GenMsgCycleTimeFast" BO_ 450 10;
BA_ "GenMsgCycleTime" BO_ 450 10;
BA_ "GenMsgSendType" BO_ 450 0;
BA_ "VFrameFormat" BO_ 450 14;
BA_ "GenMsgCycleTimeFast" BO_ 523 10;
BA_ "GenMsgCycleTime" BO_ 523 10;
BA_ "GenMsgSendType" BO_ 523 0;
BA_ "VFrameFormat" BO_ 523 14;
BA_ "GenMsgCycleTimeFast" BO_ 378 10;
BA_ "GenMsgCycleTime" BO_ 378 10;
BA_ "GenMsgSendType" BO_ 378 0;
BA_ "VFrameFormat" BO_ 378 14;
BA_ "GenMsgCycleTimeFast" BO_ 962 100;
BA_ "GenMsgCycleTime" BO_ 962 100;
BA_ "GenMsgSendType" BO_ 962 0;
BA_ "VFrameFormat" BO_ 962 14;
BA_ "GenMsgCycleTimeFast" BO_ 706 10;
BA_ "GenMsgCycleTime" BO_ 706 10;
BA_ "GenMsgSendType" BO_ 706 0;
BA_ "VFrameFormat" BO_ 706 14;
BA_ "GenMsgCycleTimeFast" BO_ 555 10;
BA_ "GenMsgCycleTime" BO_ 555 10;
BA_ "GenMsgSendType" BO_ 555 0;
BA_ "VFrameFormat" BO_ 555 14;
BA_ "GenMsgCycleTimeFast" BO_ 382 10;
BA_ "GenMsgCycleTime" BO_ 382 10;
BA_ "GenMsgSendType" BO_ 382 0;
BA_ "VFrameFormat" BO_ 382 0;
BA_ "GenMsgCycleTimeFast" BO_ 368 10;
BA_ "GenMsgCycleTime" BO_ 368 10;
BA_ "GenMsgSendType" BO_ 368 0;
BA_ "VFrameFormat" BO_ 368 0;
BA_ "GenMsgCycleTimeFast" BO_ 591 20;
BA_ "GenMsgCycleTime" BO_ 591 20;
BA_ "GenMsgSendType" BO_ 591 0;
BA_ "VFrameFormat" BO_ 591 0;
BA_ "GenMsgCycleTimeFast" BO_ 384 10;
BA_ "GenMsgCycleTime" BO_ 384 10;
BA_ "GenMsgSendType" BO_ 384 0;
BA_ "VFrameFormat" BO_ 384 0;
BA_ "GenMsgCycleTimeFast" BO_ 1683 500;
BA_ "GenMsgCycleTime" BO_ 1683 500;
BA_ "GenMsgSendType" BO_ 1683 0;
BA_ "VFrameFormat" BO_ 1683 0;
BA_ "GenMsgCycleTimeFast" BO_ 1763 500;
BA_ "GenMsgCycleTime" BO_ 1763 500;
BA_ "GenMsgSendType" BO_ 1763 0;
BA_ "VFrameFormat" BO_ 1763 0;
BA_ "GenMsgCycleTimeFast" BO_ 1687 500;
BA_ "GenMsgCycleTime" BO_ 1687 500;
BA_ "GenMsgSendType" BO_ 1687 0;
BA_ "VFrameFormat" BO_ 1687 0;
BA_ "GenMsgCycleTimeFast" BO_ 1776 500;
BA_ "GenMsgCycleTime" BO_ 1776 500;
BA_ "GenMsgSendType" BO_ 1776 0;
BA_ "VFrameFormat" BO_ 1776 0;
BA_ "GenMsgCycleTimeFast" BO_ 1755 500;
BA_ "GenMsgCycleTime" BO_ 1755 500;
BA_ "GenMsgSendType" BO_ 1755 0;
BA_ "VFrameFormat" BO_ 1755 0;
BA_ "GenMsgCycleTimeFast" BO_ 697 50;
BA_ "GenMsgCycleTime" BO_ 697 50;
BA_ "GenMsgSendType" BO_ 697 0;
BA_ "VFrameFormat" BO_ 697 14;
BA_ "GenMsgCycleTimeFast" BO_ 625 10;
BA_ "GenMsgCycleTime" BO_ 625 10;
BA_ "GenMsgSendType" BO_ 625 0;
BA_ "VFrameFormat" BO_ 625 14;
BA_ "GenMsgCycleTimeFast" BO_ 1474 1000;
BA_ "GenMsgSendType" BO_ 1474 8;
BA_ "VFrameFormat" BO_ 1474 14;
BA_ "GenMsgCycleTimeFast" BO_ 646 40;
BA_ "GenMsgCycleTime" BO_ 646 500;
BA_ "GenMsgSendType" BO_ 646 8;
BA_ "VFrameFormat" BO_ 646 14;
BA_ "GenMsgCycleTimeFast" BO_ 1394 1000;
BA_ "GenMsgCycleTime" BO_ 1394 1000;
BA_ "GenMsgSendType" BO_ 1394 0;
BA_ "VFrameFormat" BO_ 1394 14;
BA_ "GenMsgCycleTimeFast" BO_ 1395 1000;
BA_ "GenMsgCycleTime" BO_ 1395 1000;
BA_ "GenMsgSendType" BO_ 1395 0;
BA_ "VFrameFormat" BO_ 1395 14;
BA_ "GenMsgCycleTimeFast" BO_ 1397 1000;
BA_ "GenMsgCycleTime" BO_ 1397 1000;
BA_ "GenMsgSendType" BO_ 1397 0;
BA_ "VFrameFormat" BO_ 1397 14;
BA_ "GenMsgCycleTimeFast" BO_ 1396 1000;
BA_ "GenMsgCycleTime" BO_ 1396 1000;
BA_ "GenMsgSendType" BO_ 1396 0;
BA_ "VFrameFormat" BO_ 1396 14;
BA_ "GenMsgCycleTimeFast" BO_ 1409 1000;
BA_ "GenMsgCycleTime" BO_ 1409 1000;
BA_ "GenMsgSendType" BO_ 1409 0;
BA_ "VFrameFormat" BO_ 1409 14;
BA_ "GenMsgCycleTimeFast" BO_ 547 100;
BA_ "GenMsgCycleTime" BO_ 547 100;
BA_ "GenMsgSendType" BO_ 547 0;
BA_ "VFrameFormat" BO_ 547 0;
BA_ "GenMsgCycleTimeFast" BO_ 794 100;
BA_ "GenMsgCycleTime" BO_ 794 100;
BA_ "GenMsgSendType" BO_ 794 0;
BA_ "VFrameFormat" BO_ 794 14;
BA_ "GenMsgCycleTimeFast" BO_ 1337 100;
BA_ "GenMsgCycleTime" BO_ 1337 100;
BA_ "GenMsgSendType" BO_ 1337 0;
BA_ "VFrameFormat" BO_ 1337 0;
BA_ "GenMsgCycleTimeFast" BO_ 1428 1000;
BA_ "GenMsgCycleTime" BO_ 1428 1000;
BA_ "GenMsgSendType" BO_ 1428 0;
BA_ "VFrameFormat" BO_ 1428 0;
BA_ "GenMsgCycleTimeFast" BO_ 1432 1000;
BA_ "GenMsgCycleTime" BO_ 1432 1000;
BA_ "GenMsgSendType" BO_ 1432 0;
BA_ "VFrameFormat" BO_ 1432 0;
BA_ "GenMsgCycleTimeFast" BO_ 1442 1000;
BA_ "GenMsgCycleTime" BO_ 1442 1000;
BA_ "GenMsgSendType" BO_ 1442 0;
BA_ "VFrameFormat" BO_ 1442 0;
BA_ "GenMsgCycleTimeFast" BO_ 1430 1000;
BA_ "GenMsgCycleTime" BO_ 1430 1000;
BA_ "GenMsgSendType" BO_ 1430 0;
BA_ "VFrameFormat" BO_ 1430 0;
BA_ "GenMsgCycleTimeFast" BO_ 1431 1000;
BA_ "GenMsgCycleTime" BO_ 1431 1000;
BA_ "GenMsgSendType" BO_ 1431 0;
BA_ "VFrameFormat" BO_ 1431 0;
BA_ "GenMsgCycleTimeFast" BO_ 1423 1000;
BA_ "GenMsgCycleTime" BO_ 1423 1000;
BA_ "GenMsgSendType" BO_ 1423 0;
BA_ "VFrameFormat" BO_ 1423 0;
BA_ "GenMsgCycleTimeFast" BO_ 757 50;
BA_ "GenMsgCycleTime" BO_ 757 50;
BA_ "GenMsgSendType" BO_ 757 0;
BA_ "VFrameFormat" BO_ 757 14;
BA_ "GenMsgCycleTimeFast" BO_ 805 100;
BA_ "GenMsgCycleTime" BO_ 805 100;
BA_ "GenMsgSendType" BO_ 805 0;
BA_ "VFrameFormat" BO_ 805 0;
BA_ "GenMsgCycleTimeFast" BO_ 821 100;
BA_ "GenMsgCycleTime" BO_ 821 100;
BA_ "GenMsgSendType" BO_ 821 0;
BA_ "VFrameFormat" BO_ 821 0;
BA_ "GenMsgCycleTimeFast" BO_ 1317 100;
BA_ "GenMsgCycleTime" BO_ 1317 100;
BA_ "GenMsgSendType" BO_ 1317 8;
BA_ "VFrameFormat" BO_ 1317 0;
BA_ "GenMsgCycleTimeFast" BO_ 771 500;
BA_ "GenMsgCycleTime" BO_ 771 500;
BA_ "GenMsgSendType" BO_ 771 0;
BA_ "VFrameFormat" BO_ 771 0;
BA_ "GenMsgCycleTimeFast" BO_ 1537 100;
BA_ "GenMsgCycleTime" BO_ 1537 100;
BA_ "GenMsgSendType" BO_ 1537 0;
BA_ "VFrameFormat" BO_ 1537 0;
BA_ "GenMsgCycleTimeFast" BO_ 1553 100;
BA_ "GenMsgCycleTime" BO_ 1553 100;
BA_ "GenMsgSendType" BO_ 1553 0;
BA_ "VFrameFormat" BO_ 1553 0;
BA_ "GenMsgCycleTimeFast" BO_ 1554 100;
BA_ "GenMsgCycleTime" BO_ 1554 100;
BA_ "GenMsgSendType" BO_ 1554 0;
BA_ "VFrameFormat" BO_ 1554 0;
BA_ "GenMsgCycleTimeFast" BO_ 1555 100;
BA_ "GenMsgCycleTime" BO_ 1555 100;
BA_ "GenMsgSendType" BO_ 1555 0;
BA_ "VFrameFormat" BO_ 1555 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiYear 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiMth 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiDate 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiHr 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiMins 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiSec 0;
BA_ "GenSigStartValue" SG_ 759 TboxLocalTiVld 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers0 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers1 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers2 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers3 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers4 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers5 0;
BA_ "GenSigStartValue" SG_ 759 TboxSwVers6 0;
BA_ "GenSigStartValue" SG_ 759 HU_CurrentLocationLongitude 0;
BA_ "GenSigStartValue" SG_ 759 HU_CurrentLocationLatitude 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_SignalStrength 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_RemoteDisReq 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_RemoteDetectReq 0;
BA_ "GenSigStartValue" SG_ 759 HU_CurrentLocationValid 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_APAFunctionReq 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_VehicleVedioDetectReq 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_SignalType 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_APAControlEnable 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_RemoteStartReq 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_APP_Xlevel 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_APP_Ylevel 0;
BA_ "GenSigStartValue" SG_ 759 TBOX_APA_SignalDelay 0;
BA_ "GenSigStartValue" SG_ 1459 INS_Wheel_Scale_factor 500000;
BA_ "GenSigStartValue" SG_ 1459 INS_IMU_Temp 0;
BA_ "GenSigStartValue" SG_ 1459 INS_CorrelationSystem_failure_st 0;
BA_ "SystemSignalLongSymbol" SG_ 1459 INS_CorrelationSystem_failure_st "INS_CorrelationSystem_failure_status";
BA_ "GenSigStartValue" SG_ 1459 INS_Sts 0;
BA_ "GenSigStartValue" SG_ 1459 INS_Latitude_Hemisphere 0;
BA_ "GenSigStartValue" SG_ 1459 INS_Longitude_Hemisphere 0;
BA_ "GenSigStartValue" SG_ 1459 INS_RollingCounter_5B3 0;
BA_ "GenSigStartValue" SG_ 1459 INS_CRCCheck_5B3 0;
BA_ "GenSigStartValue" SG_ 383 INS_GPS_Week 0;
BA_ "GenSigStartValue" SG_ 383 INS_GPS_Time 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Long_GNSS 1800000000;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Lat_GNSS 900000000;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Long_GNSS_Err 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Lat_GNSS_Err 0;
BA_ "GenSigStartValue" SG_ 383 FL_wheel_vel_for_IPC 100000;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Heading 18000;
BA_ "GenSigStartValue" SG_ 383 INS_GNSS_Mode 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Heading_Accuracy 0;
BA_ "GenSigStartValue" SG_ 383 INS_GNSS_Height 1000000;
BA_ "GenSigStartValue" SG_ 383 INS_GNSS_Height_Err 0;
BA_ "GenSigStartValue" SG_ 383 L_wheel_factor 0;
BA_ "GenSigStartValue" SG_ 383 R_wheel_factor 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Pitch_Accuracy 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Roll_Accuracy 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Pitch 18000;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Roll 18000;
BA_ "GenSigStartValue" SG_ 383 FR_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 RL_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 RR_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Pitch_Confidence 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Heading_Confiden 0;
BA_ "SystemSignalLongSymbol" SG_ 383 INS_Current_Pos_Heading_Confiden "INS_Current_Pos_Heading_Confidence";
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Long_Confidence 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Roll_Confidence 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Height_Confidenc 0;
BA_ "SystemSignalLongSymbol" SG_ 383 INS_Current_Pos_Height_Confidenc "INS_Current_Pos_Height_Confidence";
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Lat_Confidence 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 383 INS_Initiation_Mark 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 383 INS_RollingCounter_17F 0;
BA_ "GenSigStartValue" SG_ 383 INS_POS_Match_RTK_POS_Mark 0;
BA_ "GenSigStartValue" SG_ 383 INS_CRCCheck_17F 0;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_X_Accel 40000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Y_Accel 40000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_X_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Y_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Z_Accel 40000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Z_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_X_Rate 250000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_X_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Y_Rate 250000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Y_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Z_Rate 250000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Z_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_X 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_Y 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_Z 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_W 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_IMU_Time 0;
BA_ "GenSigStartValue" SG_ 326 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 326 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 326 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 326 INS_IMU_Valid 0;
BA_ "GenSigStartValue" SG_ 326 INS_Roll_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 326 INS_Pitch_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 326 INS_Yaw_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 326 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 326 INS_RollingCounter_146 0;
BA_ "GenSigStartValue" SG_ 326 INS_CRCCheck_146 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Year 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Month 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Date 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Hour 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Minute 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Second 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_mSecond 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Time_Valid 0;
BA_ "GenSigStartValue" SG_ 795 GNSS_Speed 100000;
BA_ "GenSigStartValue" SG_ 795 GNSS_Speed_Err 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_North 100000;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_East 100000;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_Earth 100000;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_North_Err 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_East_Err 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_Earth_Err 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Original_Height 1000000;
BA_ "GenSigStartValue" SG_ 795 INS_GPS_Flight_Path_Angle 18000;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_GDOP 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_PDOP 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_HDOP 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_VDOP 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_TDOP 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_North_Confidence 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_Earth_Confidence 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Speed_East_Confidence 0;
BA_ "GenSigStartValue" SG_ 795 INS_UsedSV 0;
BA_ "GenSigStartValue" SG_ 795 INS_GNSS_Difference_delay 0;
BA_ "GenSigStartValue" SG_ 795 INS_WatchedSV 0;
BA_ "GenSigStartValue" SG_ 795 INS_Current_Original_Lat 900000000;
BA_ "GenSigStartValue" SG_ 795 INS_Current_Original_Lon 900000000;
BA_ "GenSigStartValue" SG_ 795 INS_RollingCounter_31B 0;
BA_ "GenSigStartValue" SG_ 795 INS_CRCCheck_31B 0;
BA_ "GenSigStartValue" SG_ 906 VcuCrsSwtDiagInfo 0;
BA_ "GenSigStartValue" SG_ 906 VcuCrsResuSwtSts 0;
BA_ "GenSigStartValue" SG_ 906 VcuCrsSetSwtSts 0;
BA_ "GenSigStartValue" SG_ 906 VcuCrsDstSwtPlusSts 0;
BA_ "GenSigStartValue" SG_ 906 VcuCrsDstSwtReduceSts 0;
BA_ "GenSigStartValue" SG_ 906 VcuRollingCounter_38A 0;
BA_ "GenSigStartValue" SG_ 906 VcuCRCCheck_38A 0;
BA_ "GenSigStartValue" SG_ 353 VcuAccrMod 0;
BA_ "GenSigStartValue" SG_ 353 VCUShiftPostionValid 0;
BA_ "GenSigStartValue" SG_ 353 VCUAPARequestEnable 0;
BA_ "GenSigStartValue" SG_ 353 VCUAPAdriverInterruption 0;
BA_ "GenSigStartValue" SG_ 353 VCUAccPedShield 0;
BA_ "GenSigStartValue" SG_ 353 VcuAccrPedlPosn 0;
BA_ "GenSigStartValue" SG_ 353 VcuCalcnAccrPedlPosn 0;
BA_ "GenSigStartValue" SG_ 353 VcuAPATorqRequestAvailable 0;
BA_ "GenSigStartValue" SG_ 353 VcuAccrPedlPosnVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuCalcnAccrPedlPosnVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuEnyRecyclMod 0;
BA_ "GenSigStartValue" SG_ 353 VcuComFltSts 0;
BA_ "GenSigStartValue" SG_ 353 VcuSimnEpbSwtSts 0;
BA_ "GenSigStartValue" SG_ 353 VcuSimnEpbSwtStsVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuGearPosn 3;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqReqAvl 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqRealVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMinVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMaxVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuOvrdModReq 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMax 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMin 0;
BA_ "GenSigStartValue" SG_ 353 VcuShiftLvlPosn 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqReal 0;
BA_ "GenSigStartValue" SG_ 353 VcuVehWhlReqTqVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuVehGearPosnVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuRdySts 0;
BA_ "GenSigStartValue" SG_ 353 VcuVehWhlReqTq 0;
BA_ "GenSigStartValue" SG_ 353 VcuBattlowU 0;
BA_ "GenSigStartValue" SG_ 353 VcuResiMilg 0;
BA_ "GenSigStartValue" SG_ 353 GW_ADSSecOCVerifyFailureFlag 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers0 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers1 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers2 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers3 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers4 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers5 0;
BA_ "GenSigStartValue" SG_ 353 VcuSwVers6 0;
BA_ "GenSigStartValue" SG_ 353 VcuCycCntr161 0;
BA_ "GenSigStartValue" SG_ 353 VcuCrcChk161 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers0 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers1 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers2 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers3 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers4 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers5 0;
BA_ "GenSigStartValue" SG_ 1527 ADCSwVers6 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers0 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers1 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers2 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers3 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers4 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers5 0;
BA_ "GenSigStartValue" SG_ 1527 ADCHwVers6 0;
BA_ "GenSigStartValue" SG_ 786 ADS_UDLCVoiceRecStatus 0;
BA_ "GenSigStartValue" SG_ 786 ADS_ALCStatus 0;
BA_ "GenSigStartValue" SG_ 580 ACC_ACCTargetAcceleration 100;
BA_ "GenSigStartValue" SG_ 580 ACC_LDWVibrationWarningReq 0;
BA_ "GenSigStartValue" SG_ 580 ADCReqMode 0;
BA_ "GenSigStartValue" SG_ 580 ACC_LDWShakeLevStatus 2;
BA_ "GenSigStartValue" SG_ 580 ACC_DecToStop 0;
BA_ "GenSigStartValue" SG_ 580 ACC_CDDActive 0;
BA_ "GenSigStartValue" SG_ 580 ACC_RollingCounter_24E 0;
BA_ "GenSigStartValue" SG_ 580 ACC_ACCMode 0;
BA_ "GenSigStartValue" SG_ 580 ACC_Driveoff_Request 0;
BA_ "GenSigStartValue" SG_ 580 ACC_CRCCheck_24E 0;
BA_ "GenSigStartValue" SG_ 580 ACC_AEBTargetDeceleration 32000;
BA_ "GenSigStartValue" SG_ 580 ACC_AWBlevel 0;
BA_ "GenSigStartValue" SG_ 580 ACC_ABAActive 0;
BA_ "GenSigStartValue" SG_ 580 ACC_ABAlevel 3;
BA_ "GenSigStartValue" SG_ 580 ACC_AEBActive 0;
BA_ "GenSigStartValue" SG_ 580 ACC_AccTrqReq 0;
BA_ "GenSigStartValue" SG_ 580 ACC_AEBVehilceHoldReq 0;
BA_ "GenSigStartValue" SG_ 580 ACC_PrefillActive 0;
BA_ "GenSigStartValue" SG_ 580 ACC_AWBActive 0;
BA_ "GenSigStartValue" SG_ 580 ACC_AEBCtrlType 0;
BA_ "GenSigStartValue" SG_ 580 ACC_AccTrqReqActive 0;
BA_ "GenSigStartValue" SG_ 580 ACC_RollingCounter_25E 0;
BA_ "GenSigStartValue" SG_ 580 ACC_CRCCheck_25E 0;
BA_ "GenSigStartValue" SG_ 580 ADS_DDSASafetyStopCallReq 0;
BA_ "GenSigStartValue" SG_ 580 ADS_RollingCounter_244 0;
BA_ "GenSigStartValue" SG_ 580 ADC_DDSAStatus 7;
BA_ "GenSigStartValue" SG_ 580 ADS_CRCCheck_244 0;
BA_ "GenSigStartValue" SG_ 775 ACC_ACCEPBrequest 0;
BA_ "GenSigStartValue" SG_ 775 ACC_SaftyBeltVibrationReq 0;
BA_ "GenSigStartValue" SG_ 775 ACC_LngTakeOverReqReason 0;
BA_ "GenSigStartValue" SG_ 775 ACC_RollingCounter_307 0;
BA_ "GenSigStartValue" SG_ 775 ACC_CRCCheck_307 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC1_HighByte 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC1_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC1_LowByte 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC1_Status 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC2_HighByte 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC2_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC2_LowByte 0;
BA_ "GenSigStartValue" SG_ 1785 ADS_DTC2_Status 0;
BA_ "GenSigStartValue" SG_ 583 APA_SteeringAngleReqProtection 0;
BA_ "GenSigStartValue" SG_ 583 APA_ErrorStatus 0;
BA_ "GenSigStartValue" SG_ 583 APA_indication 0;
BA_ "GenSigStartValue" SG_ 583 APA_APAOnOff 0;
BA_ "GenSigStartValue" SG_ 583 APA_EmergenceBrake 0;
BA_ "GenSigStartValue" SG_ 583 APA_SteeringAngleReq 32767;
BA_ "GenSigStartValue" SG_ 583 APA_RemoteOnOff 0;
BA_ "GenSigStartValue" SG_ 583 APA_ButtonPress 0;
BA_ "GenSigStartValue" SG_ 583 APA_IncreasePressureReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_TurnLightsCommand 0;
BA_ "GenSigStartValue" SG_ 583 APA_ParkNotice_4 0;
BA_ "GenSigStartValue" SG_ 583 APA_ParkNotice 0;
BA_ "GenSigStartValue" SG_ 583 APA_ParkingPercentage 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_264 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRCCheck_264 0;
BA_ "GenSigStartValue" SG_ 583 APA_EPBrequest 0;
BA_ "GenSigStartValue" SG_ 583 APA_EPBrequestValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_TargetAccelerationValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_TransPRNDShiftRequest 0;
BA_ "GenSigStartValue" SG_ 583 APA_TransPRNDShiftReqValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_TargetAcceleration 100;
BA_ "GenSigStartValue" SG_ 583 APA_EngTorqReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_Activation_Status 0;
BA_ "GenSigStartValue" SG_ 583 APA_TransPRNDShiftEnable 0;
BA_ "GenSigStartValue" SG_ 583 APA_LSCAction 0;
BA_ "GenSigStartValue" SG_ 583 APA_HSAHDforbidden 0;
BA_ "GenSigStartValue" SG_ 583 APA_EngineTrqReqEnable 0;
BA_ "GenSigStartValue" SG_ 583 APA_AccPedShieldReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_ESPDecompressionModel 0;
BA_ "GenSigStartValue" SG_ 583 APA_PrefillReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_DynamicSlotWarning 0;
BA_ "GenSigStartValue" SG_ 583 APA_SlotNotice 0;
BA_ "GenSigStartValue" SG_ 583 APA_TCUClutchCombinationReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_TrqHoldForTCUCl 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_26C 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRCCheck_26C 0;
BA_ "GenSigStartValue" SG_ 583 APA_PtTorqReq 32768;
BA_ "GenSigStartValue" SG_ 583 APA_ESPDistToStop 0;
BA_ "GenSigStartValue" SG_ 583 APA_ESP_BrakeFunctionMode 0;
BA_ "GenSigStartValue" SG_ 583 APA_PtTrqReqValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_VCUReadyReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_ESP_StandstillRequest 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_236 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRC_Checksum_236 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_247 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRCCheck_247 0;
BA_ "GenSigStartValue" SG_ 710 APA_ParkNotice_5 0;
BA_ "GenSigStartValue" SG_ 710 APA_LAEBReq 0;
BA_ "GenSigStartValue" SG_ 710 APA_LAEBStatus 0;
BA_ "GenSigStartValue" SG_ 710 APA_BLEConnectionRemind 0;
BA_ "GenSigStartValue" SG_ 710 APA_LAEBNotice 0;
BA_ "GenSigStartValue" SG_ 710 APA_RemoteParkingUsingRemind 0;
BA_ "GenSigStartValue" SG_ 710 APA_ASPAvailableStatus 0;
BA_ "GenSigStartValue" SG_ 710 APA_ASPStatus 0;
BA_ "GenSigStartValue" SG_ 710 APA_CrossModeSelectReq 0;
BA_ "GenSigStartValue" SG_ 710 APA_BCMHornCommand 0;
BA_ "GenSigStartValue" SG_ 710 APA_vehicleFrontdetect 0;
BA_ "GenSigStartValue" SG_ 710 APA_ReleasePressureReq 0;
BA_ "GenSigStartValue" SG_ 710 APA_PEPS_EngineOffLockRequest 0;
BA_ "GenSigStartValue" SG_ 710 APA_RADSNotice 0;
BA_ "GenSigStartValue" SG_ 710 APA_RollingCounter_2D4 0;
BA_ "GenSigStartValue" SG_ 710 APA_PEPS_EngineOffRequest 0;
BA_ "GenSigStartValue" SG_ 710 APA_CRCCheck_2D4 0;
BA_ "GenSigStartValue" SG_ 710 APA_RollingCounter_2C6 0;
BA_ "GenSigStartValue" SG_ 710 APA_CRCCheck_2C6 0;
BA_ "GenSigSendType" SG_ 1760 APA_AuthenticationStatus 1;
BA_ "GenSigStartValue" SG_ 1760 APA_AuthenticationStatus 0;
BA_ "GenSigStartValue" SG_ 798 RRS_RRSDistance 0;
BA_ "GenSigStartValue" SG_ 798 RRS_SideZoneStatus_Front 0;
BA_ "GenSigStartValue" SG_ 798 RRS_WarningFrequency 0;
BA_ "GenSigStartValue" SG_ 798 RRS_SideZoneStatus_Rear 0;
BA_ "GenSigStartValue" SG_ 798 RRS_WarningType 0;
BA_ "GenSigStartValue" SG_ 798 RRS_SwitchIndicatorError 0;
BA_ "GenSigStartValue" SG_ 798 APA_SystemFailureFlag 0;
BA_ "GenSigStartValue" SG_ 442 ACC_MotorTorqueMaxLimitRequest 1024;
BA_ "GenSigStartValue" SG_ 442 ACC_MotorTorqueMinLimitRequest 1024;
BA_ "GenSigStartValue" SG_ 442 ACC_LatAngReq 7200;
BA_ "GenSigStartValue" SG_ 442 ADS_ESSActive 0;
BA_ "GenSigStartValue" SG_ 442 ACC_LatAngReqActive 0;
BA_ "GenSigStartValue" SG_ 442 ACC_RollingCounter_1BA_0 0;
BA_ "GenSigStartValue" SG_ 442 ACC_CRCCheck_1BA_0 0;
BA_ "GenSigStartValue" SG_ 442 ADS_ErrorStatus 0;
BA_ "GenSigStartValue" SG_ 442 ACC_ADCReqType 0;
BA_ "GenSigStartValue" SG_ 442 ADS_Reqmode 0;
BA_ "GenSigStartValue" SG_ 442 ACC_RollingCounter_1BA_1 0;
BA_ "GenSigStartValue" SG_ 442 ACC_CRCCheck_1BA_1 0;
BA_ "GenSigStartValue" SG_ 839 TpmsLeFrntTireP 0;
BA_ "GenSigStartValue" SG_ 839 TpmsLeFrntTirePWarn 0;
BA_ "GenSigStartValue" SG_ 680 TmsActPwr 0;
BA_ "GenSigStartValue" SG_ 680 TmsAcEnvtlT 255;
BA_ "GenSigStartValue" SG_ 680 TmsFrntBlowMod 0;
BA_ "GenSigStartValue" SG_ 680 TmsAcEnvtlTVld 0;
BA_ "GenSigStartValue" SG_ 680 TmsCycCntr2A8 0;
BA_ "GenSigStartValue" SG_ 680 TmsCrcChk2A8 0;
BA_ "GenSigStartValue" SG_ 450 EspReWhlIncTarTqActv 0;
BA_ "GenSigStartValue" SG_ 450 EspReWhlDecTarTqActv 0;
BA_ "GenSigStartValue" SG_ 450 EspTcsActvSts 0;
BA_ "GenSigStartValue" SG_ 450 EspTcsFailr 0;
BA_ "GenSigStartValue" SG_ 450 EspFctOpenSts 0;
BA_ "GenSigStartValue" SG_ 450 EspActvSts 0;
BA_ "GenSigStartValue" SG_ 450 EspAbsActv 0;
BA_ "GenSigStartValue" SG_ 450 EspAbsFailr 0;
BA_ "GenSigStartValue" SG_ 450 EspEbdFailr 0;
BA_ "GenSigStartValue" SG_ 450 EspReWhlDecTarTq 0;
BA_ "GenSigStartValue" SG_ 450 EspReWhlIncTarTq 0;
BA_ "GenSigStartValue" SG_ 450 EspVehStandstill 0;
BA_ "GenSigStartValue" SG_ 450 EspAutoHoldActvSts 0;
BA_ "GenSigStartValue" SG_ 450 IBCU_BrakeDiscHighTempWarning 0;
BA_ "GenSigStartValue" SG_ 450 EspBrkLiOnReq 0;
BA_ "GenSigStartValue" SG_ 450 MbRgnTarWhlQlfr 0;
BA_ "GenSigStartValue" SG_ 450 EspVdcActvSts 0;
BA_ "GenSigStartValue" SG_ 450 MbRgnTarWhl 32768;
BA_ "GenSigStartValue" SG_ 450 IBCU_ADCActiveState 0;
BA_ "GenSigStartValue" SG_ 450 IBCU_CommunicationInvalid 0;
BA_ "GenSigStartValue" SG_ 450 IBCU_ADCFullFuncAvail 0;
BA_ "GenSigStartValue" SG_ 450 IBCU_ADCReducedFuncAvail 0;
BA_ "GenSigStartValue" SG_ 450 EspCycCntr1C2 0;
BA_ "GenSigStartValue" SG_ 450 EspCrcChk1C2 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiFrntData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiFrntDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiFrntDataVld 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeFrntData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeFrntDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeFrntDataVld 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_APCActiveStatus 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiReData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiReDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiReDataVld 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeReData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeReDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeReDataVld 0;
BA_ "GenSigStartValue" SG_ 523 ESP_UrgencyBrakeAlarm 0;
BA_ "GenSigStartValue" SG_ 523 ESP_APA_DriverOverride 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdFrntLePls 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdFrntRiPls 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdReRiPls 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdReLePls 0;
BA_ "GenSigStartValue" SG_ 523 ESP_AWBactive 0;
BA_ "GenSigStartValue" SG_ 523 ESP_AEBdecActive 0;
BA_ "GenSigStartValue" SG_ 523 ESP_AEBAvailable 0;
BA_ "GenSigStartValue" SG_ 523 ESP_PrefillAvailable 0;
BA_ "GenSigStartValue" SG_ 523 ESP_PrefillActive 0;
BA_ "GenSigStartValue" SG_ 523 ESP_ABAavailable 0;
BA_ "GenSigStartValue" SG_ 523 ESP_ABAactive 0;
BA_ "GenSigStartValue" SG_ 523 ESP_BrakeForce 0;
BA_ "GenSigStartValue" SG_ 523 ESP_BrakeOverHeatBrakeDiscTemp 0;
BA_ "GenSigStartValue" SG_ 523 ESP_AWBavailable 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_APCReducedFuncAvail 0;
BA_ "GenSigStartValue" SG_ 523 ESP_QDCACC 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_APCFullFuncAvail 0;
BA_ "GenSigStartValue" SG_ 523 IbcuCycCntr20B 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_ADCRampOffSuspendState 0;
BA_ "GenSigStartValue" SG_ 523 IbcuCrcChk20B 0;
BA_ "GenSigStartValue" SG_ 378 EspVehSpd 0;
BA_ "GenSigStartValue" SG_ 378 EspVehSpdVld 0;
BA_ "GenSigStartValue" SG_ 378 EPB_APArequest_Available 0;
BA_ "GenSigStartValue" SG_ 378 EPB_AchievedClampForce_Primary 0;
BA_ "GenSigStartValue" SG_ 378 EPB_AchievedClampForce 0;
BA_ "GenSigStartValue" SG_ 378 EPB_FailStatuss_Primary 0;
BA_ "GenSigStartValue" SG_ 378 WheelPressureFrontRight_Model 0;
BA_ "GenSigStartValue" SG_ 378 WheelPressureRearLeft_Model 0;
BA_ "GenSigStartValue" SG_ 378 WheelPressureRearRight_Model 0;
BA_ "GenSigStartValue" SG_ 378 WheelPressureFrontLeft_Model 0;
BA_ "GenSigStartValue" SG_ 378 ESP_RollingCounter_17A 0;
BA_ "GenSigStartValue" SG_ 378 ESP_CRCCheck_17A 0;
BA_ "GenSigStartValue" SG_ 962 EpbFailrSts 0;
BA_ "GenSigStartValue" SG_ 962 EpbSts 0;
BA_ "GenSigStartValue" SG_ 962 EpbFctLamp 0;
BA_ "GenSigStartValue" SG_ 962 EpbFailrLamp 0;
BA_ "GenSigStartValue" SG_ 962 EspEpbReqAvl 0;
BA_ "GenSigStartValue" SG_ 962 EpbFctLamp_Primary 0;
BA_ "GenSigStartValue" SG_ 962 EpbFailrLamp_Primary 0;
BA_ "GenSigStartValue" SG_ 962 EpbStsPrimary 0;
BA_ "GenSigStartValue" SG_ 962 EspCycCntr3C2 0;
BA_ "GenSigStartValue" SG_ 962 EspCrcChk3C2 0;
BA_ "GenSigStartValue" SG_ 706 IbBrkPedlStsGb 0;
BA_ "GenSigStartValue" SG_ 706 IbBrkPedlTrvlDrvr 320;
BA_ "GenSigStartValue" SG_ 706 IbBrkPedlStsGbVld 0;
BA_ "GenSigStartValue" SG_ 706 IbBrkPedlTrvlDrvrVld 0;
BA_ "GenSigStartValue" SG_ 706 IBBrkPedlModSts 0;
BA_ "GenSigStartValue" SG_ 706 IBBrkPedlModSwtAvl 0;
BA_ "GenSigStartValue" SG_ 706 IBCU_PFSBrakePressure 0;
BA_ "GenSigStartValue" SG_ 706 IBCU_PFSBrakePressureValid 0;
BA_ "GenSigStartValue" SG_ 706 IBCU_PlungerBrakePressure 0;
BA_ "GenSigStartValue" SG_ 706 IBCU_PlungerBrakePressureValid 0;
BA_ "GenSigStartValue" SG_ 706 IbCycCntr2C2 0;
BA_ "GenSigStartValue" SG_ 706 IbCrcChk2C2 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FR_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FR_Direction 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FR_Valid_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FL_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FL_Direction 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FL_Valid_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmVehStandstill 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RR_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RR_Direction 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RR_Valid_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RL_Data 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RL_Direction 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RL_Valid_Data 0;
BA_ "GenSigStartValue" SG_ 555 EngineTrqLimit 0;
BA_ "GenSigStartValue" SG_ 555 EngineTrqLimitEna 0;
BA_ "GenSigStartValue" SG_ 555 RbmESP_BrakeLightOnRequest 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FL_Pulse 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_FR_Pulse 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RR_Pulse 0;
BA_ "GenSigStartValue" SG_ 555 RbmWheel_Speed_RL_Pulse 0;
BA_ "GenSigStartValue" SG_ 555 RbmESP_QDCACC 0;
BA_ "GenSigStartValue" SG_ 555 RbmEpbFctLamp_Secondary 0;
BA_ "GenSigStartValue" SG_ 555 RbmSBSFailr 0;
BA_ "GenSigStartValue" SG_ 555 RbmEspVehSpdVld 0;
BA_ "GenSigStartValue" SG_ 555 RbmSBSActv 0;
BA_ "GenSigStartValue" SG_ 555 RbmEPB_FailStatuss_Secondary 0;
BA_ "GenSigStartValue" SG_ 555 RBM_FullFuncAvail 0;
BA_ "GenSigStartValue" SG_ 555 RbmEpbFailrLamp_Secondary 0;
BA_ "GenSigStartValue" SG_ 555 RBM_ADCActiveState 0;
BA_ "GenSigStartValue" SG_ 555 RBM_CommunicationInvalid 0;
BA_ "GenSigStartValue" SG_ 555 RbmCycCntr22B 0;
BA_ "GenSigStartValue" SG_ 555 RbmCrcChk22B 0;
BA_ "GenSigStartValue" SG_ 382 EPS_MeasuredTorsionBarTorque 2048;
BA_ "GenSigStartValue" SG_ 382 EPS_ADS_Abortfeedback 0;
BA_ "GenSigStartValue" SG_ 382 EPS_Pinionang 7200;
BA_ "GenSigStartValue" SG_ 382 EPS_Pinionang_Valid 0;
BA_ "GenSigStartValue" SG_ 382 EPS_Handwheel_Relang 10800;
BA_ "GenSigStartValue" SG_ 382 EPS_MeasuredTorsionBarTorqValid 0;
BA_ "GenSigStartValue" SG_ 382 EPS_RollingCounter_17E 0;
BA_ "GenSigStartValue" SG_ 382 EPS_LatCtrlAvailabilityStatus 0;
BA_ "GenSigStartValue" SG_ 382 EPS_LatCtrlActive 0;
BA_ "GenSigStartValue" SG_ 382 EPS_Handwheel_Relang_Valid 0;
BA_ "GenSigStartValue" SG_ 382 EPS_CRCCheck_17E 0;
BA_ "GenSigStartValue" SG_ 368 EPS_ActualMotorTorq 2048;
BA_ "GenSigStartValue" SG_ 368 EPS_MaxSafetyTorsionBarTorq 1024;
BA_ "GenSigStartValue" SG_ 368 EPS_MinSafetyTorsionBarTorq 1024;
BA_ "GenSigStartValue" SG_ 368 EPS_ActualTorsionBarTorq 1024;
BA_ "GenSigStartValue" SG_ 368 EPS_RollingCounter_170 0;
BA_ "GenSigStartValue" SG_ 368 EPS_fault_state 0;
BA_ "GenSigStartValue" SG_ 368 EPS_CRCCheck_170 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ElectPowerConsumption 0;
BA_ "GenSigStartValue" SG_ 591 EPS_TorqSensorStatus 0;
BA_ "GenSigStartValue" SG_ 591 EPS_APA_EpasFAILED 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ModeSwitchSt 0;
BA_ "GenSigStartValue" SG_ 591 EPS_APA_Abortfeedback 0;
BA_ "GenSigStartValue" SG_ 591 EPS_SteeringTorque 127;
BA_ "GenSigStartValue" SG_ 591 EPS_IACC_abortreason 0;
BA_ "GenSigStartValue" SG_ 591 EPS_APA_ControlFeedback 0;
BA_ "GenSigStartValue" SG_ 591 EPS_LDW_ShakeLevStatus 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ADASActiveMode 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ADS_ControlFeedback 0;
BA_ "GenSigStartValue" SG_ 591 EpsFaild 0;
BA_ "GenSigStartValue" SG_ 591 EPS_SystemSt 0;
BA_ "GenSigStartValue" SG_ 591 EPS_RollingCounter_24F 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ConcussAvailabilityStatus 0;
BA_ "GenSigStartValue" SG_ 591 EpsSteerModFb 3;
BA_ "GenSigStartValue" SG_ 591 EPS_CRCCheck_24F 0;
BA_ "GenSigStartValue" SG_ 384 EpsSasSteerAg 0;
BA_ "GenSigStartValue" SG_ 384 EpsSteerAgRate 0;
BA_ "GenSigStartValue" SG_ 384 EpsSasCalSts 0;
BA_ "GenSigStartValue" SG_ 384 EpsSteerAgSensFilr 0;
BA_ "GenSigStartValue" SG_ 384 EpsSasSteerAgVld 0;
BA_ "GenSigStartValue" SG_ 384 EpsCycCntr180 0;
BA_ "GenSigStartValue" SG_ 384 EpsCrcChk180 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC1_HighByte 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC1_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC1_LowByte 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC1_Status 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC2_HighByte 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC2_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC2_LowByte 0;
BA_ "GenSigStartValue" SG_ 1683 FR_DTC2_Status 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC1_HighByte 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC1_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC1_LowByte 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC1_Status 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC2_HighByte 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC2_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC2_LowByte 0;
BA_ "GenSigStartValue" SG_ 1763 FLR_DTC2_Status 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC1_HighByte 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC1_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC1_LowByte 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC1_Status 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC2_HighByte 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC2_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC2_LowByte 0;
BA_ "GenSigStartValue" SG_ 1687 RLR_DTC2_Status 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC1_HighByte 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC1_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC1_LowByte 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC1_Status 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC2_HighByte 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC2_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC2_LowByte 0;
BA_ "GenSigStartValue" SG_ 1776 FRR_DTC2_Status 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC1_HighByte 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC1_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC1_LowByte 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC1_Status 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC2_HighByte 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC2_MiddByte 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC2_LowByte 0;
BA_ "GenSigStartValue" SG_ 1755 RRR_DTC2_Status 0;
BA_ "GenSigStartValue" SG_ 697 APA_FunctionOnOffSts 0;
BA_ "GenSigStartValue" SG_ 697 APA_Condition_Notice 0;
BA_ "GenSigStartValue" SG_ 697 APA_TouchInfOnOffRes 0;
BA_ "GenSigStartValue" SG_ 697 APA_AVP_Notice 0;
BA_ "GenSigStartValue" SG_ 697 APA_HZP_Notice 0;
BA_ "GenSigStartValue" SG_ 697 APA_ViewActual 0;
BA_ "GenSigStartValue" SG_ 697 APA_Summon_Notice 0;
BA_ "GenSigStartValue" SG_ 697 APA_ActivationSts 0;
BA_ "GenSigStartValue" SG_ 697 APA_ReadySts 0;
BA_ "GenSigStartValue" SG_ 697 APA_ParkingSlot_ExtraFeature 0;
BA_ "GenSigStartValue" SG_ 697 APA_ParkingSlot_Type 0;
BA_ "GenSigStartValue" SG_ 697 APA_TurnOnMode 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 625 INS_TIleap_Difference 0;
BA_ "GenSigStartValue" SG_ 625 INS_RollingCounter_271 0;
BA_ "GenSigStartValue" SG_ 625 INS_CRCCheck_271 0;
BA_ "GenSigSendType" SG_ 1474 Odometer_Value_First_Fault 1;
BA_ "GenSigStartValue" SG_ 1474 Odometer_Value_First_Fault 0;
BA_ "GenSigSendType" SG_ 1474 Odometer_Value_Last_Fault 1;
BA_ "GenSigStartValue" SG_ 1474 Odometer_Value_Last_Fault 0;
BA_ "GenSigSendType" SG_ 1474 BatteryVoltage_Record_Fault 1;
BA_ "GenSigStartValue" SG_ 1474 BatteryVoltage_Record_Fault 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_Type 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_Type 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_CRC 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_CRC 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_SequenceCnt 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_SequenceCnt 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_TimeDomain 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_TimeDomain 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_OVS_SGW 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_OVS_SGW 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_SyncTime 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_SyncTime 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers0 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers1 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers2 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers3 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers4 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers5 0;
BA_ "GenSigStartValue" SG_ 1394 FRSwVers6 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers0 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers1 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers2 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers3 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers4 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers5 0;
BA_ "GenSigStartValue" SG_ 1394 FRHwVers6 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers0 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers1 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers2 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers3 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers4 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers5 0;
BA_ "GenSigStartValue" SG_ 1395 FLRSwVers6 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers0 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers1 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers2 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers3 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers4 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers5 0;
BA_ "GenSigStartValue" SG_ 1395 FLRHwVers6 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers0 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers1 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers2 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers3 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers4 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers5 0;
BA_ "GenSigStartValue" SG_ 1397 RLRSwVers6 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers0 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers1 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers2 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers3 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers4 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers5 0;
BA_ "GenSigStartValue" SG_ 1397 RLRHwVers6 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers0 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers1 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers2 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers3 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers4 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers5 0;
BA_ "GenSigStartValue" SG_ 1396 FRRSwVers6 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers0 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers1 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers2 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers3 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers4 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers5 0;
BA_ "GenSigStartValue" SG_ 1396 FRRHwVers6 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers0 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers1 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers2 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers3 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers4 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers5 0;
BA_ "GenSigStartValue" SG_ 1409 RRRSwVers6 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers0 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers1 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers2 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers3 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers4 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers5 0;
BA_ "GenSigStartValue" SG_ 1409 RRRHwVers6 0;
BA_ "GenSigStartValue" SG_ 547 PmuMainLoopUBeforeSwtOff 0;
BA_ "GenSigStartValue" SG_ 547 PmuSubsidaryLoopUBeforeSwtOff 0;
BA_ "GenSigStartValue" SG_ 547 PMU_RollingCounter_223 0;
BA_ "GenSigStartValue" SG_ 547 Pmufailurestatus 0;
BA_ "GenSigStartValue" SG_ 547 PMU_CRCCheck_223 0;
BA_ "GenSigStartValue" SG_ 794 ACC_LatTakeoverReqReason 0;
BA_ "GenSigStartValue" SG_ 1337 ADC_BlindZoneFunSts 0;
BA_ "GenSigStartValue" SG_ 1428 FR_WakeUp_BYTE4BIT0 0;
BA_ "GenSigStartValue" SG_ 1428 FR_WakeUp_BYTE4BIT1 0;
BA_ "GenSigStartValue" SG_ 1428 FR_NotSleep_BYTE7BIT0 0;
BA_ "GenSigStartValue" SG_ 1428 FR_NotSleep_BYTE7BIT1 0;
BA_ "GenSigStartValue" SG_ 1432 FRR_WakeUp_BYTE4BIT0 0;
BA_ "GenSigStartValue" SG_ 1432 FRR_WakeUp_BYTE4BIT1 0;
BA_ "GenSigStartValue" SG_ 1432 FRR_NotSleep_BYTE7BIT0 0;
BA_ "GenSigStartValue" SG_ 1432 FRR_NotSleep_BYTE7BIT1 0;
BA_ "GenSigStartValue" SG_ 1442 FLR_WakeUp_BYTE4BIT0 0;
BA_ "GenSigStartValue" SG_ 1442 FLR_WakeUp_BYTE4BIT1 0;
BA_ "GenSigStartValue" SG_ 1442 FLR_NotSleep_BYTE7BIT0 0;
BA_ "GenSigStartValue" SG_ 1442 FLR_NotSleep_BYTE7BIT1 0;
BA_ "GenSigStartValue" SG_ 1430 RLR_WakeUp_BYTE4BIT0 0;
BA_ "GenSigStartValue" SG_ 1430 RLR_WakeUp_BYTE4BIT1 0;
BA_ "GenSigStartValue" SG_ 1430 RLR_NotSleep_BYTE7BIT0 0;
BA_ "GenSigStartValue" SG_ 1430 RLR_NotSleep_BYTE7BIT1 0;
BA_ "GenSigStartValue" SG_ 1431 RRR_WakeUp_BYTE4BIT0 0;
BA_ "GenSigStartValue" SG_ 1431 RRR_WakeUp_BYTE4BIT1 0;
BA_ "GenSigStartValue" SG_ 1431 RRR_NotSleep_BYTE7BIT0 0;
BA_ "GenSigStartValue" SG_ 1431 RRR_NotSleep_BYTE7BIT1 0;
BA_ "GenSigStartValue" SG_ 1423 ADC_WakeUp_BYTE4BIT0 0;
BA_ "GenSigStartValue" SG_ 1423 ADC_WakeUp_BYTE4BIT1 0;
BA_ "GenSigStartValue" SG_ 1423 ADC_NotSleep_BYTE7BIT0 0;
BA_ "GenSigStartValue" SG_ 1423 ADC_NotSleep_BYTE7BIT1 0;
BA_ "GenSigStartValue" SG_ 757 LVSM_TiStamp 0;
BA_ "GenSigStartValue" SG_ 757 LVSM_TiOut 0;
BA_ "GenSigStartValue" SG_ 757 LVSM_TiLeap 0;
BA_ "GenSigStartValue" SG_ 757 LVSM_TiBas 0;
BA_ "GenSigStartValue" SG_ 757 LVSM_TimeDifference 0;
BA_ "GenSigStartValue" SG_ 757 LvsmCycCntr2F5 0;
BA_ "GenSigStartValue" SG_ 757 LvsmCrcChk2F5 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiYear 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiDate 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiMth 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiMins 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiHr 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiSec 0;
BA_ "GenSigStartValue" SG_ 805 ADC_UTCTiVld 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiYear 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiDate 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiMth 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiMins 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiHr 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiSec 0;
BA_ "GenSigStartValue" SG_ 821 LVSM_UTCTiVld 0;
BA_ "GenSigSendType" SG_ 1317 TC397_TimeSyncInit 1;
BA_ "GenSigStartValue" SG_ 1317 TC397_TimeSyncInit 0;
BA_ "GenSigStartValue" SG_ 771 ADC_PowerSwitchFlag 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_Distance_CMD 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM9 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM10 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM11 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM12 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_Machine_NUM 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM1 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM2 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM3 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM4 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM5 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM6 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM7 0;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM8 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info1 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info2 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info3 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info4 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info5 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info6 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info7 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info8 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info9 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info10 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info11 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info12 0;
VAL_ 759 TboxLocalTiVld 0 "Valid" 1 "Invalid " ;
VAL_ 759 TboxSwVers0 83 "S" ;
VAL_ 759 TboxSwVers1 87 "W" ;
VAL_ 759 TboxSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 759 TboxSwVers3 46 "." ;
VAL_ 759 TboxSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 759 TboxSwVers5 46 "." ;
VAL_ 759 TboxSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 759 TBOX_RemoteDisReq 0 "Inactive" 1 "Active" ;
VAL_ 759 TBOX_RemoteDetectReq 0 "InActive" 1 "Active" 2 "Reserved" 3 "Invalid" ;
VAL_ 759 HU_CurrentLocationValid 0 "Valid" 1 "Not Valid" ;
VAL_ 759 TBOX_VehicleVedioDetectReq 0 "InActive" 1 "Active" 2 "Reserved" 3 "Invalid" ;
VAL_ 1459 INS_Latitude_Hemisphere 0 "'E'" 1 "'W'" ;
VAL_ 1459 INS_Longitude_Hemisphere 0 "'N'" 1 "'S'" ;
VAL_ 383 INS_TiOut 0 "no timeout" 1 "timeout" ;
VAL_ 326 INS_TiOut 0 "no timeout" 1 "timeout" ;
VAL_ 326 INS_IMU_Valid 0 "Not Valid" 1 "Valid" ;
VAL_ 353 VcuAccrMod 0 "invalid" 1 "Normal" 2 "Fast" 3 "Slow" ;
VAL_ 353 VCUShiftPostionValid 0 "Valid" 1 "Invalid" ;
VAL_ 353 VCUAPARequestEnable 0 "norequest" 1 "Controlenabled" 2 "Control disable" 3 "invalid" ;
VAL_ 353 VCUAPAdriverInterruption 0 "No Interruption" 1 "Interruption" 2 "Reserved" 3 "invalid" ;
VAL_ 353 VCUAccPedShield 0 "Unshield" 1 "Shield" ;
VAL_ 353 VcuAPATorqRequestAvailable 0 "not available" 1 "available" ;
VAL_ 353 VcuAccrPedlPosnVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuCalcnAccrPedlPosnVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuSimnEpbSwtSts 0 "no request" 1 "apply" ;
VAL_ 353 VcuSimnEpbSwtStsVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuGearPosn 0 "Invalid" 1 "P" 2 "R" 3 "N" 4 "D" 5 "reserved" ;
VAL_ 353 VcuPtTqReqAvl 0 "not available" 1 "available" 2 "available degraded" ;
VAL_ 353 VcuPtTqRealVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuPtTqLimMinVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuPtTqLimMaxVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuOvrdModReq 0 "Not override" 1 "Override" ;
VAL_ 353 VcuShiftLvlPosn 0 "Center" 1 "Up" 2 "Down" 3 "Up,Up" 4 "Down,Down" 15 "Invalid" ;
VAL_ 353 VcuVehWhlReqTqVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuVehGearPosnVld 0 "Valid" 1 "Invalid " ;
VAL_ 353 VcuRdySts 0 "no Ready" 1 "Ready" ;
VAL_ 353 GW_ADSSecOCVerifyFailureFlag 0 "Not Failure" 1 "Failure" ;
VAL_ 353 VcuSwVers0 83 "S" ;
VAL_ 353 VcuSwVers1 87 "W" ;
VAL_ 353 VcuSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 353 VcuSwVers3 46 "." ;
VAL_ 353 VcuSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 353 VcuSwVers5 46 "." ;
VAL_ 353 VcuSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1527 ADCSwVers0 83 "S" ;
VAL_ 1527 ADCSwVers1 87 "W" ;
VAL_ 1527 ADCSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1527 ADCSwVers3 46 "." ;
VAL_ 1527 ADCSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1527 ADCSwVers5 46 "." ;
VAL_ 1527 ADCSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1527 ADCHwVers0 72 "H" ;
VAL_ 1527 ADCHwVers1 87 "W" ;
VAL_ 1527 ADCHwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1527 ADCHwVers3 46 "." ;
VAL_ 1527 ADCHwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1527 ADCHwVers5 46 "." ;
VAL_ 1527 ADCHwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 580 ADCReqMode 0 "ADC_None" 1 "ReducedFunction" 2 "FullFunction" 3 "invalid" ;
VAL_ 580 ACC_LDWShakeLevStatus 0 "reserved" 1 "low" 2 "normal" 3 "high" ;
VAL_ 580 ACC_DecToStop 0 "no demand" 1 "demand" ;
VAL_ 580 ACC_CDDActive 0 "not active" 1 "Active" ;
VAL_ 580 ACC_ACCMode 0 "OFF mode" 1 "Passive mode" 2 "StandBy mode" 3 "Active Control mode" 4 "Brake Only mode" 5 "Override" 6 "Stand Wait mode" 7 "Failure mode" ;
VAL_ 580 ACC_Driveoff_Request 0 "No request" 1 "request" ;
VAL_ 580 ACC_ABAActive 0 "not Active" 1 "Active" ;
VAL_ 580 ACC_ABAlevel 0 "level 0" 1 "level 1" 2 "level 2" 3 "level 3" ;
VAL_ 580 ACC_AEBActive 0 "not Active" 1 "Active." ;
VAL_ 580 ACC_AEBVehilceHoldReq 0 "No hold" 1 "hold" ;
VAL_ 580 ACC_PrefillActive 0 "not Active" 1 "Active" ;
VAL_ 580 ACC_AWBActive 0 "not Active" 1 "Active" ;
VAL_ 580 ACC_AccTrqReqActive 0 "not Active" 1 "Active." ;
VAL_ 775 ACC_ACCEPBrequest 0 "no request" 1 "request" ;
VAL_ 775 ACC_SaftyBeltVibrationReq 0 "No Request" 1 "Request" ;
VAL_ 583 APA_SteeringAngleReqProtection 0 "No Request" 1 "Request EPS control" 2 "EPS control active" ;
VAL_ 583 APA_ErrorStatus 0 "No Error" 1 "Error" ;
VAL_ 583 APA_indication 0 "No function is active" 1 "pPSC is active" 2 "cPSC is active" 3 "POC is active" 4 "ePSC is active" 5 "Invalid" ;
VAL_ 583 APA_APAOnOff 0 "OFF" 1 "ON" ;
VAL_ 583 APA_EmergenceBrake 0 "Not request" 1 "Request" ;
VAL_ 583 APA_RemoteOnOff 0 "OFF" 1 "On" 2 "Not used" 3 "Invalid " ;
VAL_ 583 APA_ButtonPress 0 "PSC button idle" 1 "PSC button pressed" ;
VAL_ 583 APA_IncreasePressureReq 0 "Not request" 1 "Request" ;
VAL_ 583 APA_TurnLightsCommand 0 "No Request" 1 "LeftLight On" 2 "RightLight On" 3 "DoubleLights On  " ;
VAL_ 583 APA_ParkNotice 0 "IDLE" 1 "pPSC_Search_R" 2 "pPSC_Search_L" 3 "cPSC_Search_R" 4 "cPSC_Search_L" 5 "pPSC_PS_OK_Pos_NOK_R" 6 "pPSC_PS_OK_Pos_NOK_L" 7 "cPSC_PS_OK_Pos_NOK_R" 8 "cPSC_PS_OK_Pos_NOK_L" 9 "pPSC_PS_OK_Stop_R" 10 "pPSC_PS_OK_Stop_L" 11 "cPSC_PS_OK_Stop_R" 12 "cPSC_PS_OK_Stop_L" 13 "pPSC_PS_OK_R_Gear_R" 14 "pPSC_PS_OK_R_Gear_L" 15 "cPSC_PS_OK_R_Gear_R" 16 "cPSC_PS_OK_R_Gear_L" 17 "PSC_PS_OK_Reactive" 18 "ePSC_PS_OK_R_Gear_R" 19 "POC_OK_R" 20 "ePSC_PS_OK_R_Gear_L" 21 "POC_Turn_ON_Indicator" 22 "pPSC_move_bwd_R" 23 "pPSC_move_bwd_L" 24 "cPSC_move_bwd_R" 25 "cPSC_move_bwd_L" 26 "POC_move_bwd_R" 27 "POC_move_bwd_L" 28 "pPSC_move_fwd_R" 29 "pPSC_move_fwd_L" 30 "cPSC_move_fwd_R" 31 "cPSC_move_fwd_L" 32 "POC_move_fwd_R" 33 "POC_move_fwd_L" 34 "Guidance_Insert_D_Gear" 35 "Guidance_Insert_R_Gear" 36 "Guidance_Stop" 37 "Guidence_Wait_for_steeringcomplete" 38 "Guidence_Slow_Down" 39 "Parking maneuver completed" 40 "reserved" 41 "Abort_ESP Intervention" 42 "Abort_Speed too high" 43 "Abort_Hand on detect" 44 "Abort_max number of maneouvres reached" 45 "Abort_Trailor attached" 46 "Abort_ User deactivated" 47 "Failure_Sensors Blinded" 48 "APA internal Failure" 49 "APA external Failure" 50 "Searching_Slow_Down" 51 "Abort_GuidanceTimeout" 52 "Abort_OffTrack" 53 "Abort_PS too small" 54 "Abort_Other_reasons" 55 "POC_No front obstacle" 56 "Searching forPakingSlotRightEchelon" 57 "Searching forPakingSlotLeftEchelon" 58 "ParkingSpaceFoundRightEchelon" 59 "ParkingSpaceFoundLeftEchelon" ;
VAL_ 583 APA_EPBrequest 0 "No Request" 1 "RequestBrake" 2 "RequestRelase" ;
VAL_ 583 APA_EPBrequestValid 0 "Invalid" 1 "Valid" ;
VAL_ 583 APA_TargetAccelerationValid 0 "not enable" 1 "enable" ;
VAL_ 583 APA_TransPRNDShiftReqValid 0 "Invalid" 1 "Valid" ;
VAL_ 583 APA_Activation_Status 0 "APA_Off" 1 "APA_Nonactivated" 2 "APA_Actived" 3 "APA_GuidanceActive" 4 "APA_Completed" 5 "APA_Failure" 6 "APA_Terminated" 7 "Reverse" ;
VAL_ 583 APA_TransPRNDShiftEnable 0 "Not Enable" 1 "Enable" ;
VAL_ 583 APA_LSCAction 0 "OFF" 1 "ON" ;
VAL_ 583 APA_HSAHDforbidden 0 "Not request" 1 "Request" ;
VAL_ 583 APA_EngineTrqReqEnable 0 "Not Enable" 1 "Enable" ;
VAL_ 583 APA_AccPedShieldReq 0 "Unshield" 1 "Shield" ;
VAL_ 583 APA_ESPDecompressionModel 0 "Slow decompression" 1 "fast decompression" ;
VAL_ 583 APA_PrefillReq 0 "Not request" 1 "Request" ;
VAL_ 583 APA_DynamicSlotWarning 0 "No warning" 1 "Warning" ;
VAL_ 583 APA_TCUClutchCombinationReq 0 "No Request  " 1 "Request" ;
VAL_ 583 APA_TrqHoldForTCUCl 0 "Off" 1 "On" ;
VAL_ 583 APA_ESP_BrakeFunctionMode 0 "None" 1 "ReducedFunction" 2 "FullFunction" 3 "Invalid" ;
VAL_ 583 APA_PtTrqReqValid 0 "Invalid" 1 "Valid" ;
VAL_ 583 APA_VCUReadyReq 0 "no request" 1 "request" ;
VAL_ 583 APA_ESP_StandstillRequest 0 "Not request" 1 "Request" ;
VAL_ 710 APA_LAEBReq 0 "No Request  " 1 "Request" ;
VAL_ 710 APA_LAEBStatus 0 "Off" 1 "Standby" 2 "Unavailable" 3 "Active" 4 "Brake" 5 "ReFault" 6 "Fault" 7 "Reserve" ;
VAL_ 710 APA_BLEConnectionRemind 0 "No Request" 1 "Request " ;
VAL_ 710 APA_RemoteParkingUsingRemind 0 "No GetOff Request" 1 "GetOff Request" ;
VAL_ 710 APA_ASPAvailableStatus 0 "Not Available" 1 "Available" ;
VAL_ 710 APA_ASPStatus 0 "Off" 1 "On" 2 "Abort" 3 "Finish" 4 "Suspend" 5 "Failure" 6 "Reserved" 7 "Invalid" ;
VAL_ 710 APA_CrossModeSelectReq 0 "No Request" 1 "Request" ;
VAL_ 710 APA_BCMHornCommand 0 "No Request" 1 "Request" 2 "Reserved" 3 "Invalid" ;
VAL_ 710 APA_vehicleFrontdetect 0 "No Detect" 1 "Detect" 2 "Reserved" 3 "Invalid" ;
VAL_ 710 APA_ReleasePressureReq 0 "No Request" 1 "Request" ;
VAL_ 710 APA_PEPS_EngineOffLockRequest 0 "Not Request  " 1 "Request" ;
VAL_ 710 APA_PEPS_EngineOffRequest 0 "Not Request" 1 "Request" ;
VAL_ 798 RRS_SideZoneStatus_Front 0 "None" 1 "Left" 2 "Right" 3 "Left and Right" ;
VAL_ 798 RRS_SideZoneStatus_Rear 0 "None" 1 "Left" 2 "Right" 3 "Left and Right" ;
VAL_ 798 RRS_SwitchIndicatorError 0 "No Error" 1 "Error" ;
VAL_ 442 ADS_ESSActive 0 "not active" 1 "active" ;
VAL_ 442 ACC_LatAngReqActive 0 "not Active" 1 "Active" ;
VAL_ 442 ADS_ErrorStatus 0 "No Error" 1 "Error" ;
VAL_ 442 ACC_ADCReqType 0 " None" 1 " ADC_Comfort" 2 " ADC_Emergency" 3 " Invalid" ;
VAL_ 839 TpmsLeFrntTirePWarn 0 "Nowarning" 1 "Highpressurewarning" 2 "Lowpressurewarning" 3 "Quikleakage" 4 "LostSensor" 5 "Sensorbatterylow" 6 "Sensorfailure" 7 "Unlearning" ;
VAL_ 680 TmsFrntBlowMod 0 "blow face" 1 "blow face/blow feet" 2 "blow feet" 3 "blow fee/defroster" 4 "defroster" 7 "error" ;
VAL_ 680 TmsAcEnvtlTVld 0 "Valid" 1 "Invalid" ;
VAL_ 450 EspReWhlIncTarTqActv 0 "Not active" 1 "active" ;
VAL_ 450 EspReWhlDecTarTqActv 0 "Not active" 1 "active" ;
VAL_ 450 EspTcsActvSts 0 "Not active" 1 "active" ;
VAL_ 450 EspTcsFailr 0 "No error" 1 "Error" ;
VAL_ 450 EspFctOpenSts 0 "Off" 1 "ON" ;
VAL_ 450 EspActvSts 0 "Not active" 1 "active" ;
VAL_ 450 EspAbsActv 0 "Not active" 1 "active" ;
VAL_ 450 EspAbsFailr 0 "No error" 1 "Error" ;
VAL_ 450 EspEbdFailr 0 "No error" 1 "Error" ;
VAL_ 450 EspVehStandstill 0 "Not standstill" 1 "standstill" ;
VAL_ 450 EspAutoHoldActvSts 0 "Not active" 1 "active" ;
VAL_ 450 EspBrkLiOnReq 0 "Not request(brakelightOFF)" 1 "brake light ON" 2 "Not used" 3 "signal Not available" ;
VAL_ 450 MbRgnTarWhlQlfr 0 "NotIntialized" 1 "Normal" 2 "Faulty" ;
VAL_ 450 EspVdcActvSts 0 "Not active" 1 "active" ;
VAL_ 450 IBCU_ADCActiveState 0 "ADC_NotActive" 1 "ADC_ActiveReduce" 2 "ADC_ActiveFull" 3 "reserved" ;
VAL_ 450 IBCU_CommunicationInvalid 0 "noerror" 1 "error" 2 "reserved" 3 "reserved" ;
VAL_ 523 WhlSpdRiFrntDir 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 523 WhlSpdRiFrntDataVld 0 "Valid" 1 "Invalid " ;
VAL_ 523 WhlSpdLeFrntDir 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 523 WhlSpdLeFrntDataVld 0 "Valid" 1 "Invalid " ;
VAL_ 523 IBCU_APCActiveStatus 0 " Deactive" 1 " ReducedFunction Active" 2 " FullFunction Active" 3 " Reserved" ;
VAL_ 523 WhlSpdRiReDir 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 523 WhlSpdRiReDataVld 0 "Valid" 1 "Invalid " ;
VAL_ 523 WhlSpdLeReDir 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 523 WhlSpdLeReDataVld 0 "Valid" 1 "Invalid " ;
VAL_ 523 ESP_UrgencyBrakeAlarm 0 "NotActive" 1 "Active" ;
VAL_ 523 ESP_APA_DriverOverride 0 "Flase" 1 "True" ;
VAL_ 523 ESP_AWBactive 0 "notactive" 1 "active" ;
VAL_ 523 ESP_AEBdecActive 0 "notactive" 1 "active" ;
VAL_ 523 ESP_AEBAvailable 0 "notavailable" 1 "available" ;
VAL_ 523 ESP_PrefillAvailable 0 "notavailable" 1 "available" ;
VAL_ 523 ESP_PrefillActive 0 "notactive" 1 "active" ;
VAL_ 523 ESP_ABAavailable 0 "notavailable" 1 "available" ;
VAL_ 523 ESP_ABAactive 0 "notactive" 1 "active" ;
VAL_ 523 ESP_BrakeForce 0 "nobrakeforce" 1 "havebrakeforce" ;
VAL_ 523 ESP_BrakeOverHeatBrakeDiscTemp 0 "nothigh" 1 "temptoohigh" ;
VAL_ 523 ESP_AWBavailable 0 "notavailable" 1 "available" ;
VAL_ 523 IBCU_APCReducedFuncAvail 0 "notavailable" 1 "available" ;
VAL_ 523 ESP_QDCACC 0 "noerror" 1 "error" 2 "reserved" 3 "reserved" ;
VAL_ 523 IBCU_APCFullFuncAvail 0 "notavailable" 1 "available" 2 "testpending" ;
VAL_ 523 IBCU_ADCRampOffSuspendState 0 "ADC_Normal" 1 "ADC_SuspendByOtherBrake" 2 "ADC_SuspendByDriverPropulsion" 3 "ADC_RampOffByDriverBrake" ;
VAL_ 378 EspVehSpdVld 0 "Valid" 1 "Invalid" ;
VAL_ 378 EPB_APArequest_Available 0 "notavailable" 1 "available" ;
VAL_ 378 EPB_FailStatuss_Primary 0 "no error in EPB " 1 "not defined  " 2 "not defined" 3 "error in EPB " ;
VAL_ 962 EpbFailrSts 0 "No error" 1 "Not defined" 2 "Not defined" 3 "error" ;
VAL_ 962 EpbSts 0 "both brakes released" 1 "both brakes applied" 2 "both brakes Releasing" 3 "both brakes Locking" 4 "unknown" ;
VAL_ 962 EpbFctLamp 0 "Lamp Off" 1 "Lamp ON" 2 "Lamp Flashing" 3 "Invalid" ;
VAL_ 962 EpbFailrLamp 0 "Lamp Off" 1 "Lamp ON" 2 "Lamp Flashing" 3 "Invalid" ;
VAL_ 962 EspEpbReqAvl 0 "Not available" 1 "available" ;
VAL_ 962 EpbFctLamp_Primary 0 "Lamp Off" 1 "Lamp ON" 2 "Lamp Flashing" 3 "Invalid" ;
VAL_ 962 EpbFailrLamp_Primary 0 "Lamp Off" 1 "Lamp ON" 2 "Lamp Flashing" 3 "Invalid" ;
VAL_ 962 EpbStsPrimary 0 "released" 1 " ParkedApplied" 2 "releasing" 3 "Locking" 4 "unknown" 5 "HoldApplied" 6 " CompletelyReleased" 7 "HapPrepared" ;
VAL_ 706 IbBrkPedlStsGbVld 0 "NotInitialized" 1 "Normal" 2 "Invalid" ;
VAL_ 706 IbBrkPedlTrvlDrvrVld 0 "NotInitialized" 1 "Normal" 2 "Invalid" ;
VAL_ 706 IBBrkPedlModSwtAvl 0 "display" 1 "No display" ;
VAL_ 706 IBCU_PFSBrakePressureValid 0 "Invalid" 1 "Valid" ;
VAL_ 706 IBCU_PlungerBrakePressureValid 0 "Invalid" 1 "Valid" ;
VAL_ 555 RbmWheel_Speed_FR_Direction 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 555 RbmWheel_Speed_FR_Valid_Data 0 "Valid" 1 "Invalid " ;
VAL_ 555 RbmWheel_Speed_FL_Direction 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 555 RbmWheel_Speed_FL_Valid_Data 0 "Valid" 1 "Invalid " ;
VAL_ 555 RbmVehStandstill 0 "Not standstill" 1 "standstill" ;
VAL_ 555 RbmWheel_Speed_RR_Direction 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 555 RbmWheel_Speed_RR_Valid_Data 0 "Valid" 1 "Invalid " ;
VAL_ 555 RbmWheel_Speed_RL_Direction 0 "Forward" 1 "backward" 2 "Standstill" 3 "Invalid" ;
VAL_ 555 RbmWheel_Speed_RL_Valid_Data 0 "Valid" 1 "Invalid " ;
VAL_ 555 RbmESP_BrakeLightOnRequest 0 "brakelightOFF" 1 "brakelightON" 2 "notused" 3 "signalnotavailable" ;
VAL_ 555 RbmESP_QDCACC 0 "noerror" 1 "error" 2 "reserved" 3 "reserved" ;
VAL_ 555 RbmEpbFctLamp_Secondary 0 "Lamp Off" 1 "Lamp ON" 2 "Lamp Flashing" 3 "Invalid" ;
VAL_ 555 RbmSBSFailr 0 "No error" 1 "Error" ;
VAL_ 555 RbmEspVehSpdVld 0 "Valid" 1 "Invalid " ;
VAL_ 555 RbmSBSActv 0 "Not active" 1 "active" ;
VAL_ 555 RbmEPB_FailStatuss_Secondary 0 "no error in EPB " 1 "not defined  " 2 "not defined" 3 "error in EPB " ;
VAL_ 555 RbmEpbFailrLamp_Secondary 0 "Lamp Off" 1 "Lamp ON" 2 "Lamp Flashing" 3 "Invalid" ;
VAL_ 555 RBM_ADCActiveState 0 "ADC_NotActive" 1 "ADC_Active_Full" 2 "reserved" 3 "reserved" ;
VAL_ 555 RBM_CommunicationInvalid 0 "noerror" 1 "error" 2 "reserved" 3 "reserved" ;
VAL_ 382 EPS_ADS_Abortfeedback 0 "No Fault" 1 "Adas Speed Invalid" 2 "Adas Signal Invalid" 3 "State Mismatch" 4 "APA Interrupt" 5 "BUS Off" 6 "Both Channels CAN down" 7 "Adas Chassis CAN Down" 8 "ADAS Internal Error" 9 "Speed out of range" 10 "AgAmpErr" 11 "AgGrdtFlt" 12 "Driver Interrupt" 13 "ADAS Req Mode Invld" ;
VAL_ 382 EPS_Pinionang_Valid 0 "valid" 1 "invalid" ;
VAL_ 382 EPS_LatCtrlAvailabilityStatus 0 "not Availability" 1 "Availability" 2 "failure" 3 "reserved" ;
VAL_ 382 EPS_LatCtrlActive 0 "Not Actived" 1 "Actived" ;
VAL_ 382 EPS_Handwheel_Relang_Valid 0 "valid" 1 "invalid" ;
VAL_ 368 EPS_fault_state 0 "No Fault" 1 "Thermal Protection Fault" 2 "Power Limit Fault" 3 "IMC Result error" 4 "Torque Sensor Fault(recoverable with assist)" 5 "Angle Sensor Fault(recoverable with assist)" 6 "Sensorless Angle Fault(Non recoverable without assist)" 7 "Power Supply Fault(Non recoverable)" 8 "Motor Sensor Fault(Non recoverable)" 9 "Flash/RAM/NVM Fault(Non recoverable)" 10 "Battery Voltage Fault(Recoverable)" 11 "ECU ID Fault(Non recoverable)" 12 "Other Fault(Non recoverable)" ;
VAL_ 591 EPS_TorqSensorStatus 0 "Normal" 1 "Abnormal" ;
VAL_ 591 EPS_APA_EpasFAILED 0 "No Fail" 1 "Fail" ;
VAL_ 591 EPS_ModeSwitchSt 0 "No judgment" 1 "In judgment" ;
VAL_ 591 EPS_APA_Abortfeedback 0 "NO_DRIVING_INTERUPTION" 1 "DRIVER_RECOVERY" 2 "TOO_HIGH_VEHICLE_SPEED" 3 "TOO_IMPORTANT_ANGULAR_ERROR" 4 "TOO_IMPORTANT_ANGULAR_SPEED_ERROR" 5 "DAE_THERMAL_SECURITY" 6 "DAE_LIMIT_SECURITY" 7 "OTHER_DEFAULT" ;
VAL_ 591 EPS_IACC_abortreason 0 "NO_DRIVING_INTERUPTION" 1 "DRIVER_RECOVERY(Over ride)" 2 "VEHICLE_SPEED ERROR" 3 "REQUEST ERROR" 4 "DAE_THERMAL_SECURITY" 5 "OTHER_DEFAULT" 6 "ADAS MESSAGE FAULT" 7 "RESERVED" ;
VAL_ 591 EPS_APA_ControlFeedback 0 "Control disable" 1 "Control enabled" ;
VAL_ 591 EPS_LDW_ShakeLevStatus 0 "reserved" 1 "low" 2 "normal" 3 "high" ;
VAL_ 591 EPS_ADASActiveMode 0 "none" 1 "APALat" 2 "MainLat" 3 "SubLat" 4 "-0x7" ;
VAL_ 591 EpsSteerModFb 0 "Standar Mode" 1 "Comfort Mode" 2 "Sport Mode" 3 "unknow" ;
VAL_ 384 EpsSasCalSts 0 "Calibrated" 1 "Not Calibrated" ;
VAL_ 384 EpsSteerAgSensFilr 0 "Valid" 1 "Invalid" ;
VAL_ 384 EpsSasSteerAgVld 0 "Valid" 1 "Invalid" ;
VAL_ 697 APA_Condition_Notice 0 "No Problem" 1 "Speed too fast" 2 "Slope too high" 3 "Vehicle not start" 4 "vehicle setup  center" 5 "ACC not closed" 6 "The current area not available" 7 "Dark" 8 "Bad Weather" ;
VAL_ 625 INS_TiOut 0 "no timeout" 1 "timeout" ;
VAL_ 1394 FRSwVers0 83 "S" ;
VAL_ 1394 FRSwVers1 87 "W" ;
VAL_ 1394 FRSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1394 FRSwVers3 46 "." ;
VAL_ 1394 FRSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1394 FRSwVers5 46 "." ;
VAL_ 1394 FRSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1394 FRHwVers0 72 "H" ;
VAL_ 1394 FRHwVers1 87 "W" ;
VAL_ 1394 FRHwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1394 FRHwVers3 46 "." ;
VAL_ 1394 FRHwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1394 FRHwVers5 46 "." ;
VAL_ 1394 FRHwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1395 FLRSwVers0 83 "S" ;
VAL_ 1395 FLRSwVers1 87 "W" ;
VAL_ 1395 FLRSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1395 FLRSwVers3 46 "." ;
VAL_ 1395 FLRSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1395 FLRSwVers5 46 "." ;
VAL_ 1395 FLRSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1395 FLRHwVers0 72 "H" ;
VAL_ 1395 FLRHwVers1 87 "W" ;
VAL_ 1395 FLRHwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1395 FLRHwVers3 46 "." ;
VAL_ 1395 FLRHwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1395 FLRHwVers5 46 "." ;
VAL_ 1395 FLRHwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1397 RLRSwVers0 83 "S" ;
VAL_ 1397 RLRSwVers1 87 "W" ;
VAL_ 1397 RLRSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1397 RLRSwVers3 46 "." ;
VAL_ 1397 RLRSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1397 RLRSwVers5 46 "." ;
VAL_ 1397 RLRSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1397 RLRHwVers0 72 "H" ;
VAL_ 1397 RLRHwVers1 87 "W" ;
VAL_ 1397 RLRHwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1397 RLRHwVers3 46 "." ;
VAL_ 1397 RLRHwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1397 RLRHwVers5 46 "." ;
VAL_ 1397 RLRHwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1396 FRRSwVers0 83 "S" ;
VAL_ 1396 FRRSwVers1 87 "W" ;
VAL_ 1396 FRRSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1396 FRRSwVers3 46 "." ;
VAL_ 1396 FRRSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1396 FRRSwVers5 46 "." ;
VAL_ 1396 FRRSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1396 FRRHwVers0 72 "H" ;
VAL_ 1396 FRRHwVers1 87 "W" ;
VAL_ 1396 FRRHwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1396 FRRHwVers3 46 "." ;
VAL_ 1396 FRRHwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1396 FRRHwVers5 46 "." ;
VAL_ 1396 FRRHwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1409 RRRSwVers0 83 "S" ;
VAL_ 1409 RRRSwVers1 87 "W" ;
VAL_ 1409 RRRSwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1409 RRRSwVers3 46 "." ;
VAL_ 1409 RRRSwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1409 RRRSwVers5 46 "." ;
VAL_ 1409 RRRSwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1409 RRRHwVers0 72 "H" ;
VAL_ 1409 RRRHwVers1 87 "W" ;
VAL_ 1409 RRRHwVers2 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1409 RRRHwVers3 46 "." ;
VAL_ 1409 RRRHwVers4 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 1409 RRRHwVers5 46 "." ;
VAL_ 1409 RRRHwVers6 48 "0" 49 "1" 50 "2" 51 "3" 52 "4" 53 "5" 54 "6" 55 "7" 56 "8" 57 "9" 65 "A" 66 "B" 67 "C" 68 "D" 69 "E" 70 "F" 71 "G" 72 "H" 73 "I" 74 "J" 75 "K" 76 "L" 77 "M" 78 "N" 79 "O" 80 "P" 81 "Q" 82 "R" 83 "S" 84 "T" 85 "U" 86 "V" 87 "W" 88 "X" 89 "Y" 90 "Z" ;
VAL_ 757 LVSM_TiOut 0 "no timeout" 1 "timeout" ;
VAL_ 805 ADC_UTCTiVld 0 "Valid" 1 "Invalid " ;
VAL_ 821 LVSM_UTCTiVld 0 "Valid" 1 "Invalid " ;
VAL_ 1317 TC397_TimeSyncInit 0 "0x1" ;


{"AVAS_3F2/VspLoSpdWarnSts": "BodyInfo/LowSpeedPedestrianAlarmType", "CDC_356/CDCFaceAuthentSts": "DMS_356/CDCFaceAuthentSts", "CDC_356/DmsFaceAccountFb": "DMS_356/DmsFaceAccountFb", "CDC_356/DmsFaceDataID": "DMS_356/DmsFaceDataID", "CDC_356/DmsWarnSig": "DMS_356/DmsFaceSoundWarn", "CDC_356/DmsFaceRegistFctSts": "DMS_356/DmsFaceRegistFctSts", "CDC_356/DmsFaceRegistDispReq": "DMS_356/DmsFaceRegistDispReq", "CDC_356/DmsFaceRegistSoundReq": "DMS_356/DmsFaceRegistSoundReq", "CDC_356/DmsFaceDectSts": "DMS_356/DmsFaceDectSts", "GW_50/SrsDrvrBucSwtSts": {"VehicleSeat/SeatBelt/FrontLeft": {"valueMap": {"0": 1, "1": 0}, "defaultValueType": 3, "comments": "主驾安全带"}}, "GW_50/SrsPassBucSwtSts": {"VehicleSeat/SeatBelt/FrontRight": {"valueMap": {"0": 1, "1": 0}, "defaultValueType": 3, "comments": "副驾安全带"}}, "TBOX_2F7/TboxEmgyAcoustWarnReq": "SOS/EmergencyAssistanceAcoustReq", "GW_3B1/BcmTrDoorOpenDeg": "VehicleDoor/TrunkDoorDegree", "GW_1E5/TmsAcIntT": "Sensor/InsideTemperature", "VCU_309/VcuUnreleasedMilgInLowT": "EnergyInfo/FrozenPercent", "GW_31E/APA_Voice_Notice": "ADC_31E/APA_Voice_Notice", "VCU_309/VcuVehAvrgEgyCnse": "EnergyInfo/AverageEnergyConsumption", "VCU_309/VcuVehIncrMilg": "EnergyInfo/ChargeIncreasedRange", "GW_31A/ACC_Voiceinfo": "ADC_31A/ACC_Voiceinfo", "GW_2A4/LCDAR_BSD_LCAAlert": "ADC_2A4/LCDAR_BSD_LCAAlert", "GW_2A4/LCDAR_Left_BSD_LCAAlert": "ADC_2A4/LCDAR_Left_BSD_LCAAlert", "GW_382/ACC_LaneEquationC1": "ADC_382/ACC_LaneEquationC1", "GW_382/ACC_LaneEquationC2": "ADC_382/ACC_LaneEquationC2", "GW_382/ACC_LaneEquationC3": "ADC_382/ACC_LaneEquationC3", "GW_36F/ADS_PathShow_Status": "ADC_36F/ADS_PathShow_Status", "GW_244/ACC_FCWLatentWarning": "ADC_244/ACC_FCWLatentWarning", "GW_382/ACC_Target1Detection": "ADC_382/ACC_Target1Detection", "GW_382/ACC_Target1ID": "ADC_382/ACC_Target1ID", "GW_382/ACC_Target1Type": "ADC_382/ACC_Target1Type", "GW_382/ACC_Target1LngRange": "ADC_382/ACC_Target1LngRange", "GW_382/ACC_Target1LatRange": "ADC_382/ACC_Target1LatRange", "GW_382/ACC_Target1Direction": "ADC_382/ACC_Target1Direction", "GW_382/ACC_Target1HeadingAngle": "ADC_382/ACC_Target1HeadingAngle", "GW_382/ACC_Target1ZoneID": "ADC_382/ACC_Target1ZoneID", "GW_382/ACC_Target2Detection": "ADC_382/ACC_Target2Detection", "GW_382/ACC_Target2ID": "ADC_382/ACC_Target2ID", "GW_382/ACC_Target2Type": "ADC_382/ACC_Target2Type", "GW_382/ACC_Target2LngRange": "ADC_382/ACC_Target2LngRange", "GW_382/ACC_Target2LatRange": "ADC_382/ACC_Target2LatRange", "GW_382/ACC_Target2Direction": "ADC_382/ACC_Target2Direction", "GW_382/ACC_Target2HeadingAngle": "ADC_382/ACC_Target2HeadingAngle", "GW_382/ACC_Target2ZoneID": "ADC_382/ACC_Target2ZoneID", "GW_382/ACC_Target3Detection": "ADC_382/ACC_Target3Detection", "GW_382/ACC_Target3ID": "ADC_382/ACC_Target3ID", "GW_382/ACC_Target3Type": "ADC_382/ACC_Target3Type", "GW_382/ACC_Target3LngRange": "ADC_382/ACC_Target3LngRange", "GW_382/ACC_Target3LatRange": "ADC_382/ACC_Target3LatRange", "GW_382/ACC_Target3Direction": "ADC_382/ACC_Target3Direction", "GW_382/ACC_Target3HeadingAngle": "ADC_382/ACC_Target3HeadingAngle", "GW_382/ACC_Target3ZoneID": "ADC_382/ACC_Target3ZoneID", "GW_382/ACC_Target4Detection": "ADC_382/ACC_Target4Detection", "GW_382/ACC_Target4ID": "ADC_382/ACC_Target4ID", "GW_382/ACC_Targe4Type": "ADC_382/ACC_Target4Type", "GW_382/ACC_Target4LngRange": "ADC_382/ACC_Target4LngRange", "GW_382/ACC_Target4LatRange": "ADC_382/ACC_Target4LatRange", "GW_382/ACC_Target4Direction": "ADC_382/ACC_Target4Direction", "GW_382/ACC_Target4HeadingAngle": "ADC_382/ACC_Target4HeadingAngle", "GW_382/ACC_Target4ZoneID": "ADC_382/ACC_Target4ZoneID", "GW_382/ACC_Target5Detection": "ADC_382/ACC_Target5Detection", "GW_382/ACC_Target5ID": "ADC_382/ACC_Target5ID", "GW_382/ACC_Target5Type": "ADC_382/ACC_Target5Type", "GW_382/ACC_Target5LngRange": "ADC_382/ACC_Target5LngRange", "GW_382/ACC_Target5LatRange": "ADC_382/ACC_Target5LatRange", "GW_382/ACC_Target5Direction": "ADC_382/ACC_Target5Direction", "GW_382/ACC_Target5HeadingAngle": "ADC_382/ACC_Target5HeadingAngle", "GW_382/ACC_Target5ZoneID": "ADC_382/ACC_Target5ZoneID", "GW_307/ACC_Target6Detection": "ADC_307/ACC_Target6Detection", "GW_307/ACC_Target6ID": "ADC_307/ACC_Target6ID", "GW_307/ACC_Target6Type": "ADC_307/ACC_Target6Type", "GW_307/ACC_Target6LngRange": "ADC_307/ACC_Target6LngRange", "GW_307/ACC_Target6LatRange": "ADC_307/ACC_Target6LatRange", "GW_307/ACC_Target6Direction": "ADC_307/ACC_Target6Direction", "GW_307/ACC_Target6HeadingAngle": "ADC_307/ACC_Target6HeadingAngle", "GW_307/ACC_Target6ZoneID": "ADC_307/ACC_Target6ZoneID", "GW_307/ACC_Target7Detection": "ADC_307/ACC_Target7Detection", "GW_307/ACC_Target7ID": "ADC_307/ACC_Target7ID", "GW_307/ACC_Target7Type": "ADC_307/ACC_Target7Type", "GW_307/ACC_Target7LngRange": "ADC_307/ACC_Target7LngRange", "GW_307/ACC_Target7LatRange": "ADC_307/ACC_Target7LatRange", "GW_307/ACC_Target7Direction": "ADC_307/ACC_Target7Direction", "GW_307/ACC_Target7HeadingAngle": "ADC_307/ACC_Target7HeadingAngle", "GW_307/ACC_Target7ZoneID": "ADC_307/ACC_Target7ZoneID", "GW_307/ACC_LLLaneDis": "ADC_307/ACC_LLLaneDis", "GW_307/ACC_RRLaneDis": "ADC_307/ACC_RRLaneDis", "GW_307/ACC_LLaneDistanceFus": "ADC_307/ACC_LLaneDistanceFus", "GW_307/ACC_RLaneDistanceFus": "ADC_307/ACC_RLaneDistanceFus", "GW_307/ADS_DDSAS_Enable": "ADC_307/ADS_DDSAS_Enable", "GW_31A/ADC_CompanionAssitModeEnable": "ADC_31A/ADC_CompanionAssitModeEnable", "GW_307/ADS_Parking_Asistant_Syste_Enable": "ADC_307/ADS_Parking_Asistant_Syste_Enable", "GW_307/ADS_Automated_Valet_Parking_Enable": "ADC_307/ADS_Automated_Valet_Parking_Enable", "GW_307/ADS_Novice_Enable": "ADC_307/ADS_Novice_Enable", "GW_312/ADS_LaneChangeStyle": "ADC_312/ADS_LaneChangeStyle", "GW_31A/ACC_IACCHWAMode": "GW_31A/ACC_IACCHWAMode", "GW_244/ACC_ACCMode": "GW_244/ACC_ACCMode", "GW_31A/ACC_HandsOnReq": "GW_31A/ACC_HandsOnReq", "GW_307/ACC_LngTakeOverReq": "GW_307/ACC_LngTakeOverReq", "GW_31A/ACC_LatTakeoverReq": "GW_31A/ACC_LatTakeoverReq", "GW_244/ACC_LngTakeOverReq": "GW_244/ACC_LngTakeOverReq", "GW_244/ACC_LatTakeoverReq": "GW_244/ACC_LatTakeoverReq", "GW_244/ACC_FCWPreWarning": "ADC_244/ACC_FCWPreWarning", "GW_244/ACC_AWBActive": "ADC_244/ACC_AWBActive", "GW_244/ACC_AEBActive": "GW_244/ACC_AEBActive", "GW_2A4/LCDAR_Left_RCTAAlert": "GW_2A4/LCDAR_Left_RCTAAlert", "GW_2A4/LCDAR_RCTAAlert": "GW_2A4/LCDAR_RCTAAlert", "GW_29E/ADS_FRC_Left_FCTALAlert": "ADC_29E/ADS_FRC_Left_FCTALAlert", "GW_29E/ADS_FRC_Right_FCTARAlert": "ADC_29E/ADS_FRC_Right_FCTARAlert", "VCU_2A1/VcuAccrPedlPosn": "VCU_2A1/VcuAccrPedlPosn", "VCU_2A1/VcuAccrPedlPosnVld": "VCU_2A1/VcuAccrPedlPosnVld", "GW_384/BDC_FrtWipMistSW": "GW_384/BDC_FrtWipMistSW", "GW_244/ACC_EmergencyDataRrdReq": "GW_244/ACC_EmergencyDataRrdReq", "VCU_2A1/VcuBrkLiOnReq": "VCU_2A1/VcuBrkLiOnReq", "GW_2F3/IbBrkLiOnReq": "GW_2F3/IbBrkLiOnReq", "GW_2F3/RbmESP_BrakeLightOnRequest": "GW_2F3/RbmESP_BrakeLightOnRequest", "GW_1C2/EspBrkLiOnReq": "GW_1C2/EspBrkLiOnReq", "GW_288/BcmHiBeamSts": "GW_288/BcmHiBeamSts", "GW_288/BcmLoBeamSts": "GW_288/BcmLoBeamSts", "GW_288/BcmTurnIndcrLe": "GW_288/BcmTurnIndcrLe", "GW_288/BcmTurnIndcrRi": "GW_288/BcmTurnIndcrRi", "GW_2AD/LCDAL_SEAEnableStatus": "GW_2AD/LCDAL_SEAEnableStatus", "GW_312/ADS_NDAAudioWarEnableStatus": "GW_312/ADS_NDAAudioWarEnableStatus", "GW_312/ADS_ADSSafeVoiceEnableSts": "GW_312/ADS_ADSSafeVoiceEnableSts", "GW_312/ADS_ADSLaneChangeVoiceEnableSts": "GW_312/ADS_ADSLaneChangeVoiceEnableSts", "GW_312/ADS_ADSRoadVoiceEnableSts": "GW_312/ADS_ADSRoadVoiceEnableSts", "TBOX_2F7/TboxHeatReq": "TBOX_2F7/TboxHeatReq", "GW_307/ACC_SetSpeed": "GW_307/ACC_SetSpeed", "GW_36F/ADS_DisToRamp": "GW_36F/ADS_DisToRamp", "ADC_307/ACC_LngTakeOverReq": "ADC_307/ACC_LngTakeOverReq", "ADC_31A/ACC_LatTakeoverReq": "ADC_31A/ACC_LatTakeoverReq", "GW_36F/ADS_PathShow_C1": "GW_36F/ADS_PathShow_C1", "GW_36F/ADS_PathShow_C2": "GW_36F/ADS_PathShow_C2", "GW_36F/ADS_PathShow_C3": "GW_36F/ADS_PathShow_C3", "GW_36F/ADS_PathShowLngRange": "GW_36F/ADS_PathShowLngRange", "GW_36F/ADS_PathThroughRamp": "GW_36F/ADS_PathThroughRamp", "GW_36F/ADS_RampSide": "GW_36F/ADS_RampSide", "GW_36F/ADS_RampStatus": "GW_36F/ADS_RampStatus", "GW_312/ADS_ToTargetLatDistance": "GW_312/ADS_ToTargetLatDistance", "GW_36F/ADS_NDASetSpdModeStatus": "GW_36F/ADS_NDASetSpdModeStatus", "GW_36F/ADS_NDAStatus": "GW_36F/ADS_NDAStatus", "GW_36F/ADS_NDATextInfo": "GW_36F/ADS_NDATextInfo", "GW_244/ACC_ADCNotice": "GW_244/ACC_ADCNotice", "GW_1E5/EmsEngSts": "GW_1E5/EmsEngSts", "GW_1E5/TmsAcEnvtlT": "GW_1E5/TmsAcEnvtlT", "CDC_356/DmsFaceRegistSts": "DMS_356/DmsFaceRegistSts", "GW_612/AVM_DVRModeFeedback": "ADC_612/AVM_DVRModeFeedback", "CDC_356/DmsAttentionFunSts": "DMS_356/DmsAttentionFunSts", "GW_612/DVR_PrintScreenFeedback": "ADC_612/DVR_PrintScreenFeedback", "GW_612/DVR_FormatStatus": "ADC_612/DVR_FormatStatus", "GW_31E/RRS_WarningType": "GW_31E/RRS_WarningType", "CDC_356/DmsDrvrFatigueDetdSts": "DMS_356/DmsDrvrFatigueDetdSts", "CDC_356/DmsDrvrFatigueWarnReq": {"DMS_356/DmsDrvrFatigueWarnReq": {}, "DMS/DetectedStatus": {"valueMap": {"0": 0, "1": 2}, "defaultValueType": 3, "comments": "疲劳驾驶"}}, "GW_1C2/WhlSpd": {"bindSigNames": ["GW_1C2/WhlSpdReRiPls", "GW_1C2/WhlSpdFrntRiPls", "GW_1C2/WhlSpdReLePls", "GW_1C2/WhlSpdFrntLePls", "GW_1C2/WhlSpdRiReDataVld", "GW_1C2/WhlSpdRiReData", "GW_1C2/WhlSpdRiFrntDataVld", "GW_1C2/WhlSpdRiFrntData", "GW_1C2/WhlSpdLeReDataVld", "GW_1C2/WhlSpdLeReData", "GW_1C2/WhlSpdLeFrntDataVld", "GW_1C2/WhlSpdLeFrntData"]}, "Light/Turn": {"bindSigNames": ["GW_288/BcmTurnIndcrLe", "GW_288/BcmTurnIndcrRi"]}, "GW_3B5/WLCM_WirelessChargingSetStatus": "GW_3B5/WLCM_WirelessChargingSetStatus", "GW_5F1/BDC_IntLampFunSetSts": "GW_5F1/BDC_IntLampFunSetSts", "CDC_356/DmsSmokeModSetFb": {"bindSigNames": ["CDC_356/CdcSmokeACStartReq", "CDC_356/CdcSmokeOuterCircReq"]}, "GW_307/ACC_DistanceLevel": "GW_307/ACC_DistanceLevel", "GW_539/ADC_BlindZoneFunSts": "GW_539/ADC_BlindZoneFunSts", "GW_39A/LAS_TSRHostSpdLimit": "GW_39A/LAS_TSRHostSpdLimit", "CDC_356/DmsFaceIdentifyTextDispReq": "DMS_356/DmsFaceIdentifyTextDispReq", "GW_244/ACC_TakeoverReqAtIPFail": "ADAS/TakeoverReqAtIPFail", "GW_352/DsmTextDispReq": "GW_352/DsmTextDispReq", "IBCU_256/EPB_Status": "IBCU_256/EPB_Status", "IBCU_256/IbBrkPedlStsGb": "IBCU_256/IbBrkPedlStsGb", "IBCU_256/IbBrkPedlStsGbVld": "IBCU_256/IbBrkPedlStsGbVld", "RBM_1CC/RBM_VehicleSpeed": "RBM_1CC/RBM_VehicleSpeed", "GW_1E5/RmipuPlsHeatgSts": "GW_1E5/RmipuPlsHeatgSts", "GW_1E5/BcuPlsHeatTiRem": "GW_1E5/BcuPlsHeatTiRem", "GW_1E5/BcuBattPlsHeagStsFb": "GW_1E5/BcuBattPlsHeagStsFb", "GW_5FF/BdcFrntWiperSensitivity": {"GW_5FF/BdcFrntWiperSensitivity": {"valueMap": {"0": 1, "1": 2, "2": 3, "3": 4}, "defaultValueType": 3, "comments": "前雨刮灵敏度"}}, "GW_1E5/BcuBattNoFuChargReq": "GW_1E5/BcuBattNoFuChargReq", "GW_384/BdcReWiprSts": "GW_384/BdcReWiprSts", "GW_1C2/IBCU_CST_Status": "GW_1C2/IBCU_CST_Status", "TURN_54B/TurnCdcAngPos": "TURN_54B/TurnCdcAngPos", "TURN_54B/TurnCdcTvModSts": "TURN_54B/TurnCdcTvModSts", "Sunshade_LIN_1/Sunshade_Movement": "SunShade/Movement", "Sunshade_LIN_1/Sunshade_POS_VIT_FL": "SunShade/PosVitFl", "TBOX_2F7/TboxSetStandyMode": "TBOX_2F7/TboxSetStandyMode", "TBOX_2F7/TboxVehLocnDetectReq": "TBOX_2F7/TboxVehLocnDetectReq", "GW_244/ADCSentryModeErrL1": "ADC_244/ADCSentryModeErrL1", "VCU_2A1/VcuSentryModeErrL2": "VCU_2A1/VcuSentryModeErrL2", "GW_244/ADCSentryModeWarnL1": "ADC_244/ADCSentryModeWarnL1", "VCU_2A1/VcuSentryModeWarnL2": "VCU_2A1/VcuSentryModeWarnL2", "GW_288/BcmKeyAlrmSts": "GW_288/BcmKeyAlrmSts", "GW_3B7/AVM__Trigger_method_Feedback": "AVM/AVMTrigger", "GW_3B7/AVM_AVMReviewMirrorIconSts": "GW_3B7/AVM_AVMReviewMirrorIconSts", "ICMSG_650": {"bindSigNames": ["ICMSG_650/INS_Current_Pos_X_Accel", "ICMSG_650/INS_Current_Pos_X_Rate", "ICMSG_650/INS_Current_Pos_Y_Accel", "ICMSG_650/INS_Current_Pos_Y_Rate", "ICMSG_650/INS_Current_Pos_Z_Accel", "ICMSG_650/INS_Current_Pos_Z_Rate", "ICMSG_650/INS_IMU_Time", "ICMSG_650/INS_IMU_Valid", "ICMSG_650/INS_Pos_X_Accel_Bias", "ICMSG_650/INS_Pos_X_Rate_Bias", "ICMSG_650/INS_Pos_Y_Accel_Bias", "ICMSG_650/INS_Pos_Y_Rate_Bias", "ICMSG_650/INS_Pos_Z_Accel_Bias", "ICMSG_650/INS_Pos_Z_Rate_Bias", "ICMSG_650/INS_Quaternion_W", "ICMSG_650/INS_Quaternion_X", "ICMSG_650/INS_Quaternion_Y", "ICMSG_650/INS_Quaternion_Z", "ICMSG_650/INS_Roll_Mis_Angle_to_Veh", "ICMSG_650/INS_Pitch_Mis_Angle_to_Veh", "ICMSG_650/INS_Yaw_Mis_Angle_to_Veh", "ICMSG_650/INS_TiStamp", "ICMSG_650/ICMSG_TimeStamp", "ICMSG_650/INS_RollingCounter_146", "ICMSG_650/INS_TiOut", "ICMSG_650/INS_TiLeap", "ICMSG_650/INS_TiBas", "ICMSG_650/INS_CRCCheck_146"]}, "ICMSG_651": {"bindSigNames": ["ICMSG_651/VCUAPARequestEnable", "ICMSG_651/VCUAPAdriverInterruption", "ICMSG_651/VCUAccPedShield", "ICMSG_651/VcuAPATorqRequestAvailable", "ICMSG_651/VCUShiftPostionValid", "ICMSG_651/VcuCalcnAccrPedlPosn", "ICMSG_651/VcuEnyRecyclMod", "ICMSG_651/VcuGearPosn", "ICMSG_651/VcuPtTqLimMax", "ICMSG_651/VcuComFltSts", "ICMSG_651/VcuCalcnAccrPedlPosnVld", "ICMSG_651/VcuPtTqLimMaxVld", "ICMSG_651/VcuPtTqLimMin", "ICMSG_651/VcuPtTqReal", "ICMSG_651/VcuPtTqLimMinVld", "ICMSG_651/VcuPtTqReqAvl", "ICMSG_651/VcuPtTqRealVld", "ICMSG_651/VcuVehWhlReqTq", "ICMSG_651/VcuVehGearPosnVld", "ICMSG_651/VcuShiftLvlPosn", "ICMSG_651/VcuRdySts", "ICMSG_651/VcuVehWhlReqTqVld", "ICMSG_651/VcuCrcChk161", "ICMSG_651/VcuSimnEpbSwtStsVld", "ICMSG_651/VcuSimnEpbSwtSts", "ICMSG_651/VcuCycCntr161", "ICMSG_651/EPS_fault_state", "ICMSG_651/EPS_RollingCounter_170", "ICMSG_651/EpsCrcChk180", "ICMSG_651/EpsSasCalSts", "ICMSG_651/EpsCycCntr180", "ICMSG_651/EpsSasSteerAg", "ICMSG_651/EpsSasSteerAgVld", "ICMSG_651/EpsSteerAgRate", "ICMSG_651/EpsSteerAgSensFilr", "ICMSG_651/ICMSG_TimeStamp", "ICMSG_651/VcuAccrPedlPosn", "ICMSG_651/GW_ADSSecOCVerifyFailureFlag", "ICMSG_651/VcuOvrdModReq", "ICMSG_651/VcuAccrPedlPosnVld", "ICMSG_651/INS_TiStamp", "ICMSG_651/INS_TIleap_Difference", "ICMSG_651/INS_TiOut", "ICMSG_651/INS_TiLeap", "ICMSG_651/INS_TiBas", "ICMSG_651/INS_RollingCounter_271", "ICMSG_651/INS_CRCCheck_271"]}, "ICMSG_652": {"bindSigNames": ["ICMSG_652/ICMSG_TimeStamp", "ICMSG_652/EspAbsFailr", "ICMSG_652/EspAbsActv", "ICMSG_652/EspReWhlDecTarTq", "ICMSG_652/EspReWhlDecTarTqActv", "ICMSG_652/EspReWhlIncTarTq", "ICMSG_652/EspEbdFailr", "ICMSG_652/EspTcsActvSts", "ICMSG_652/EspVdcActvSts", "ICMSG_652/EspReWhlIncTarTqActv", "ICMSG_652/EspVehSpd", "ICMSG_652/EspVehSpdVld", "ICMSG_652/ESP_CRCCheck_17A", "ICMSG_652/ESP_RollingCounter_17A", "ICMSG_652/EPB_AchievedClampForce", "ICMSG_652/EPB_AchievedClampForce_Primary", "ICMSG_652/EPB_FailStatuss_Primary", "ICMSG_652/EPB_APArequest_Available", "ICMSG_652/EPS_CRCCheck_17E", "ICMSG_652/EPS_LatCtrlAvailabilityStatus", "ICMSG_652/EPS_LatCtrlActive", "ICMSG_652/INS_Current_Pos_Heading", "ICMSG_652/EPS_RollingCounter_17E", "ICMSG_652/INS_Current_Pos_Heading_Accuracy", "ICMSG_652/INS_Current_Pos_Pitch", "ICMSG_652/INS_Current_Pos_Heading_Con_0000", "ICMSG_652/INS_Current_Pos_Pitch_Accuracy", "ICMSG_652/INS_Current_Pos_Roll", "ICMSG_652/INS_Current_Pos_Pitch_Confidence", "ICMSG_652/INS_Current_Pos_Roll_Accuracy", "ICMSG_652/INS_Current_Pos_Roll_Confidence", "ICMSG_652/FL_wheel_vel_for_IPC", "ICMSG_652/FR_wheel_vel_for_IPC", "ICMSG_652/RL_wheel_vel_for_IPC", "ICMSG_652/RR_wheel_vel_for_IPC", "ICMSG_652/L_wheel_factor", "ICMSG_652/R_wheel_factor", "ICMSG_652/INS_TiStamp", "ICMSG_652/INS_RollingCounter_17F", "ICMSG_652/INS_TiOut", "ICMSG_652/INS_TiLeap", "ICMSG_652/INS_TiBas", "ICMSG_652/INS_CRCCheck_17F", "ICMSG_652/INS_GPS_Time", "ICMSG_652/EspAutoHoldActvSts", "ICMSG_652/EspTcsFailr", "ICMSG_652/EspFctOpenSts", "ICMSG_652/EspActvSts", "ICMSG_652/EspVehStandstill", "ICMSG_652/IBCU_ADCActiveState", "ICMSG_652/IBCU_CommunicationInvalid", "ICMSG_652/IBCU_ADCFullFuncAvail", "ICMSG_652/IBCU_ADCReducedFuncAvail"]}, "ICMSG_653": {"bindSigNames": ["ICMSG_653/ESP_BrakeForce", "ICMSG_653/EPS_SteeringTorque", "ICMSG_653/ESP_APA_DriverOverride", "ICMSG_653/ESP_PrefillAvailable", "ICMSG_653/ESP_PrefillActive", "ICMSG_653/WhlSpdFrntLePls", "ICMSG_653/WhlSpdFrntRiPls", "ICMSG_653/WhlSpdLeFrntData", "ICMSG_653/WhlSpdLeReData", "ICMSG_653/WhlSpdLeFrntDir", "ICMSG_653/WhlSpdLeFrntDataVld", "ICMSG_653/WhlSpdLeReDir", "ICMSG_653/WhlSpdLeReDataVld", "ICMSG_653/WhlSpdReLePls", "ICMSG_653/WhlSpdReRiPls", "ICMSG_653/WhlSpdRiFrntData", "ICMSG_653/WhlSpdRiReData", "ICMSG_653/WhlSpdRiFrntDir", "ICMSG_653/WhlSpdRiFrntDataVld", "ICMSG_653/WhlSpdRiReDir", "ICMSG_653/WhlSpdRiReDataVld", "ICMSG_653/IbcuCrcChk20B", "ICMSG_653/IbcuCycCntr20B", "ICMSG_653/IBCU_ADCRampOffSuspendState", "ICMSG_653/IBCU_APCActiveStatus", "ICMSG_653/IBCU_APCReducedFuncAvail", "ICMSG_653/IBCU_APCFullFuncAvail", "ICMSG_653/EPS_ConcussAvailabilityStatus", "ICMSG_653/EPS_APA_EpasFAILED", "ICMSG_653/EPS_APA_ControlFeedback", "ICMSG_653/EPS_APA_Abortfeedback", "ICMSG_653/EPS_CRCCheck_24F", "ICMSG_653/EPS_ElectPowerConsumption", "ICMSG_653/EPS_IACC_abortreason", "ICMSG_653/EPS_SystemSt", "ICMSG_653/EPS_RollingCounter_24F", "ICMSG_653/EPS_ADS_ControlFeedback", "ICMSG_653/EPS_ADASActiveMode", "ICMSG_653/IbBrkPedlStsGb", "ICMSG_653/IBCU_PFSBrakePressure", "ICMSG_653/IbBrkPedlStsGbVld", "ICMSG_653/IBCU_PlungerBrakePressure", "ICMSG_653/IBCU_PlungerBrakePressureValid", "ICMSG_653/IbCrcChk2C2", "ICMSG_653/IbCycCntr2C2", "ICMSG_653/EpbStsPrimary", "ICMSG_653/EpbSts", "ICMSG_653/EpbFailrSts", "ICMSG_653/EspCrcChk3C2", "ICMSG_653/EspCycCntr3C2", "ICMSG_653/INS_Wheel_Scale_factor", "ICMSG_653/EspEpbReqAvl", "ICMSG_653/ICMSG_TimeStamp"]}, "ICMSG_654": {"bindSigNames": ["ICMSG_654/Ult_Probe_info1", "ICMSG_654/Ult_Probe_info2", "ICMSG_654/Ult_Probe_info3", "ICMSG_654/Ult_Probe_info4", "ICMSG_654/Ult_Probe_info5", "ICMSG_654/Ult_Probe_info6", "ICMSG_654/Ult_Probe_info7", "ICMSG_654/Ult_Probe_info8", "ICMSG_654/Ult_Probe_info9", "ICMSG_654/Ult_Probe_info10", "ICMSG_654/Ult_Probe_info11", "ICMSG_654/Ult_Probe_info12", "ICMSG_653/ICMSG_TimeStamp"]}}
VERSION "HIPBNYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY/4/%%%/4/'%**4YYY///"


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_


BS_:

BU_: GW ADC SGW_C IBC PDCU EPS M_CAMERA HAD IVI


BO_ 838 SGW_C_346: 8 SGW_C
 SG_ VIUL_PEPS_RKECommand2 : 39|4@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 833 SGW_C_341: 8 SGW_C
 SG_ VIUL_BattVoltSt : 7|10@0+ (0.05,0) [0.0|28.0] "V"  IVI


BO_ 828 SGW_C_33C: 8 SGW_C
 SG_ VIUL_IgnitionSt : 1|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ VIUL_DoorLockSt : 2|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_FrontWiperSwSt : 15|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ VIUL_CHMSLSt : 45|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ VIUL_BrakeLampSt : 47|2@0+ (1,0) [0.0|3.0] "—"  IVI


BO_ 1242 SGW_C_4DA: 8 SGW_C
 SG_ TBOX_TimeMonth : 7|4@0+ (1,0) [1.0|12.0] "month"  IVI
 SG_ TBOX_TimeDate : 12|5@0+ (1,0) [1.0|31.0] "day"  IVI
 SG_ TBOX_TimeValid : 15|1@0+ (1,0) [0.0|1.0] "_"  IVI
 SG_ TBOX_TimeHour : 20|5@0+ (1,0) [0.0|23.0] "hour"  IVI
 SG_ TBOX_TimeMinute : 29|6@0+ (1,0) [0.0|59.0] "minute"  IVI
 SG_ TBOX_TimeSecond : 37|6@0+ (1,0) [0.0|59.0] "second"  IVI
 SG_ TBOX_TimeYear : 47|8@0+ (1,2000) [2000.0|2254.0] "year"  IVI


BO_ 131 SGW_C_83: 8 SGW_C
 SG_ EMS_EngSpd : 7|16@0+ (0.125,0) [0.0|8191.875] "rpm"  IVI


BO_ 162 IBC_A2: 8 IBC
 SG_ IBC_VehicleSpeed : 7|13@0+ (0.05625,0) [0.0|270.0] "km/h"  IVI
 SG_ IBC_VehicleSpeedValid : 10|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_VehicleDrivingDirection : 25|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_VehicleStandstillSt : 27|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_VehicleStandstillValid : 28|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_ModeSt : 29|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_BrakelightReq : 30|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_EBDFaultSt : 37|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_EBDActiveSt : 38|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_ESCoffSt : 39|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_ESCFaultSt : 40|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ RollingCounter0A2 : 51|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ IBC_VehicleHoldSt : 54|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ Checksum0A2 : 63|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 307 IBC_133: 8 IBC
 SG_ IBC_BrakePedalSt : 2|2@0+ (1,0) [0.0|3.0] ""  IVI


BO_ 285 IBC_11D: 8 IBC
 SG_ IBC_PlungerPressure : 11|12@0+ (0.3,-5) [-5.0|301.9] "bar"  IVI


BO_ 300 IBC_12C: 8 IBC
 SG_ IBC_EPBSt : 10|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ IBC_EPBSwValiditySt : 11|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ IBC_EPBSwSt : 13|2@0+ (1,0) [0.0|2.0] "—"  IVI
 SG_ IBC_Slope : 25|6@0+ (1,-30) [-30.0|30.0] "%"  IVI
 SG_ IBC_SlopeSt : 26|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_SlopHighWarn : 33|2@0+ (1,0) [0|3] ""  IVI
 SG_ IBC_EPBErrorSt : 35|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ RollingCounter31A : 51|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ Checksum31A : 63|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 257 IBC_101: 12 IBC
 SG_ IBC_wheelSpeedRL : 7|13@0+ (0.05625,0) [0.0|270.0] "km/h"  IVI
 SG_ IBC_wheelSpeedRLValid : 10|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_wheelSpeedRR : 23|13@0+ (0.05625,0) [0.0|270.0] "km/h"  IVI
 SG_ IBC_wheelSpeedRRValid : 26|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_wheelSpeedFL : 39|13@0+ (0.05625,0) [0.0|270.0] "km/h"  IVI
 SG_ IBC_wheelSpeedFLValid : 42|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_wheelSpeedFR : 55|13@0+ (0.05625,0) [0.0|270.0] "km/h"  IVI
 SG_ IBC_wheelSpeedFRValid : 58|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ RollingCounter101 : 83|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ Checksum101 : 95|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 370 IBC_172: 12 IBC
 SG_ IBC_SumOfEdgeFLWSS : 7|16@0+ (1,0) [0.0|65535.0] ""  IVI
 SG_ IBC_SumOfEdgeFRWSS : 23|16@0+ (1,0) [0.0|65535.0] ""  IVI
 SG_ IBC_WheelDirectionFRSt : 33|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_WheelDirectionFLSt : 35|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_WheelDirectionFRValid : 36|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_WheelDirectionFLValid : 37|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_SumOfEdgeFRWSSValid : 38|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_SumOfEdgeFLWSSValid : 39|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_SumOfEdgeRLWSS : 47|16@0+ (1,0) [0.0|65535.0] ""  IVI
 SG_ IBC_SumOfEdgeRRWSS : 63|16@0+ (1,0) [0.0|65535.0] ""  IVI
 SG_ IBC_WheelDirectionRRSt : 73|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_WheelDirectionRLSt : 75|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_WheelDirectionRRValid : 76|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_WheelDirectionRLValid : 77|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_SumOfEdgeRRWSSValid : 78|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_SumOfEdgeRLWSSValid : 79|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ RollingCounter172 : 83|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ Checksum172 : 95|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 323 IBC_143: 8 IBC
 SG_ IBC_APABrakeModeSt : 1|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ IBC_PDCUCtrlReq : 2|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_VlcInterTargetAx : 15|8@0+ (0.05,-7) [-7.0|5.7] "m/s2"  IVI
 SG_ IBC_LSMCtrlFaultSt : 19|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ IBC_APABrakSysLongictlSt : 23|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ IBC_WHEEL_TRQ_APATReq : 31|14@0+ (1,-2000) [-2000.0|8000.0] "N.m"  IVI
 SG_ IBC_APAGearReqActiveSt : 44|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ IBC_TargetGearReq : 47|3@0+ (1,0) [0.0|7.0] ""  IVI
 SG_ RollingCounter143 : 51|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ Checksum143 : 63|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 252 PDCU_FC: 32 PDCU
 SG_ PDCU_AccelPedalVirtualSt : 57|10@0+ (0.1,0) [0.0|100.0] "%"  IVI
 SG_ PDCU_AccPedalVirtlValid : 79|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_FAWhlTqRegenMax : 91|12@0+ (1,-4093) [-4093.0|0.0] "Nm"  IVI
 SG_ PDCU_FrontMotorRpm : 111|16@0+ (1,-30000) [-30000.0|30000.0] "RPM"  IVI
 SG_ PDCU_FAWhlTqAct : 143|14@0+ (1,-3000) [-3000.0|10000.0] "Nm"  IVI
 SG_ PDCU_frontMotorRpmValid : 144|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_FAWhlTqRegenMaxValid : 145|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_rearMotRpm : 159|16@0+ (1,-30000) [-30000.0|30000.0] "RPM"  IVI
 SG_ PDCU_RAWhlTqAct : 191|14@0+ (1,-3000) [-3000.0|10000.0] "Nm"  IVI
 SG_ PDCU_rearMotRpmValid : 192|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_RAWhlTqRegenMaxValid : 193|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_RAWhlTqRegenMax : 207|12@0+ (1,-4093) [-4093.0|0.0] "Nm"  IVI
 SG_ RollingCounter0FC : 243|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ Checksum0FC : 255|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 456 PDCU_1C8: 8 PDCU
 SG_ PDCU_AccelPedalSt : 1|10@0+ (0.1,0) [0.0|100.0] "%"  IVI
 SG_ PDCU_ActualGear : 5|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ PDCU_AccelPedalValid : 39|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ RollingCounter1C8 : 51|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ Checksum1C8 : 63|8@0+ (1,0) [0.0|255.0] ""  IVI


BO_ 254 PDCU_FE: 8 PDCU
 SG_ PDCU_DriveReadySt : 0|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ PDCU_BMS_SOCCalculate : 39|8@0+ (1,0) [0.0|100.0] "%"  IVI
 SG_ RollingCounter0FE : 51|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ Checksum0FE : 63|8@0+ (1,0) [0.0|255.0] "—"  IVI


BO_ 255 PDCU_FF: 12 PDCU
 SG_ EMS_PowertrainControlSt : 1|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ PDCU_ShiftQuitReason : 3|2@0+ (1,0) [0.0|3.0] "_"  IVI
 SG_ PDCU_ShiftAvaibleSt : 4|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_ACCFunIhibitionReq : 5|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ PDCU_DriveAsiSttACCSt : 7|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ PDCU_MaxVehicleWhlTq : 12|13@0+ (1,0) [0.0|8190.0] "N.m"  IVI
 SG_ PDCU_AxleTorqueSt : 13|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ PDCU_DriveAsiStt_APA : 15|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ PDCU_TargetDriverTorq : 31|13@0+ (1,-4096) [-4096.0|4094.0] "Nm"  IVI
 SG_ PDCU_ACCResponseSt : 34|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ PDCU_RealizedPowertrainWhlTq : 47|14@0+ (1,-4000) [-4000.0|11000.0] "N.m"  IVI
 SG_ PDCU_ACCcontrolAvailableSt : 48|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ PDCU_WhetherACCReqRealizedSt : 49|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ PDCU_MCUR_InverterWorkSt : 60|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ PDCU_MCUF_InverterWorkSt : 63|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ RollingCounter0FF : 83|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ Checksum0FF : 95|8@0+ (1,0) [0.0|255.0] "—"  IVI


BO_ 378 EPS_17A: 8 EPS
 SG_ EPS_SteeringHoldSt : 13|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ EPS_SteeringAngleFlag : 14|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ EPS_SteeringTorqueSensorSt : 15|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ EPS_SteeringAngle : 23|16@0- (0.1,0) [-1008.0|1008.0] "deg"  IVI
 SG_ EPS_SteeringTorqueValue : 47|12@0+ (0.01,-20.48) [-20.48|20.47] "Nm"  IVI
 SG_ EPS_RollingCount17A : 51|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ EPS_CheckSum17A : 63|8@0+ (1,0) [0.0|255.0] "—"  IVI


BO_ 165 EPS_A5: 8 EPS
 SG_ TAS_SAS_SteeringAngle : 7|16@0- (0.1,0) [-1008.0|1008.0] "deg"  IVI
 SG_ TAS_SAS_SteeringRotSpd : 23|8@0+ (4,0) [0.0|1016.0] "deg/s"  IVI
 SG_ TAS_SAS_TrimmingSt : 29|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ TAS_SAS_CalibratedSt : 30|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ TAS_SAS_SASFailureSt : 31|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ TAS_SAS_RollingCountA5 : 51|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ TAS_SAS_CheckSumA5 : 63|8@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 176 EPS_B0: 8 EPS
 SG_ EPS_APARollingCountB0 : 3|4@0+ (1,0) [0.0|15.0] ""  IVI
 SG_ EPS_APAChecksumB0 : 15|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ EPS_APAProhibitedResaon : 18|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ EPS_APAFuncModeSt : 20|2@0+ (1,0) [0.0|3.0] ""  IVI
 SG_ EPS_APASt : 22|2@0+ (1,0) [0.0|3.0] ""  IVI


BO_ 358 EPS_166: 8 EPS
 SG_ EPS_LKA_DriverOverrideSt : 19|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ EPS_LKA_TorqueLimitationSt : 20|1@0+ (1,0) [0.0|1.0] ""  IVI


BO_ 359 EPS_167: 8 EPS
 SG_ EPS_DriverInitializedESASt : 18|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ EPS_ESA_DriverOverrideSt : 19|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ EPS_ESA_TorqueLimitationSt : 20|1@0+ (1,0) [0.0|1.0] ""  IVI


BO_ 501 M_CAMERA_1F5: 8 M_CAMERA
 SG_ HAD_CAMERA_HMITargetDisp : 0|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ HAD_CAMERA_FDMSt : 3|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ACCSt : 7|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_ACCLongitudialDst : 15|8@0+ (1,0) [0.0|254.0] "m"  IVI
 SG_ HAD_CAMERA_ACCLateralDst : 23|8@0- (1,0) [-63.0|63.0] "m"  IVI
 SG_ HAD_CAMERA_TargetPosSt : 31|3@0+ (1,0) [0.0|7.0] "—"  IVI


BO_ 1144 M_CAMERA_478: 20 M_CAMERA
 SG_ HAD_CAMERA_ACCSetSpdDisp : 2|3@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ACCRVVTypeReq : 3|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ HAD_CAMERA_ACCHeadwayIconDisp : 4|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ HAD_CAMERA_ACCHeadwayDisp : 7|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ACCStIconDisp : 10|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ACCMessageRem : 15|5@0+ (1,0) [0.0|31.0] "—"  IVI
 SG_ HAD_CAMERA_ACCSetSpeed : 23|8@0+ (1,0) [0.0|254.0] "kp/h"  IVI
 SG_ HAD_CAMERA_AEBFaultSt : 25|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_AEBMassDisp : 28|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ISLCSt : 31|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_AEBAlertReq : 33|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_HBCHighBeamReq : 35|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ HAD_CAMERA_AEBSwSt : 37|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ACCdbRem : 39|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ICAMessageDisp : 44|5@0+ (1,0) [0.0|31.0] "—"  IVI
 SG_ HAD_CAMERA_ICAStIconDisp : 47|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OperatingSt : 51|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_HandsoffWarn : 53|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ISLCDriverRem : 55|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_LDWStDisp : 57|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_LKAStDisp : 59|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_LCAPopMessageDisp : 63|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_LKAPopupMessage : 67|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_LKAModeSwSt : 69|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_LDWWarnSt : 71|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_LCAStDisp : 73|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ISASignTypeSt : 76|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ISASt : 79|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ISAPopMessageDisp : 82|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_ISAValue : 87|5@0+ (1,0) [0.0|31.0] "km/h"  IVI
 SG_ HAD_CAMERA_TSRSignTypeSt : 95|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_VehicleLaneDisp : 100|5@0+ (1,0) [0.0|31.0] "—"  IVI
 SG_ HAD_CAMERA_IHBCFuctionSt : 102|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_JA_AlertReq : 103|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ FDM_LongitDstDisp : 111|14@0+ (0.025,-10) [-10.0|399.575] "m"  IVI
 SG_ HAD_CAMERA_FDMWarn : 113|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_FDMMessageDisp : 124|5@0+ (1,0) [0.0|31.0] "—"  IVI
 SG_ HAD_CAMERA_FDMStDisp : 127|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_LSSSwst : 128|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ HAD_CAMERA_TSRSwst : 131|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ISASwst : 133|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ISAModeSwst : 135|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ISASetSpeeddevSwst : 137|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_FCWSwst : 139|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_FCWSensitivitySwst : 141|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_FDMSwst : 143|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_ELKSwst : 145|2@0+ (1,0) [0.0|3.0] "—"  IVI


BO_ 1051 M_CAMERA_41B: 20 M_CAMERA
 SG_ HAD_CAMERA_L0LineTypeSt : 3|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_R0LineTypeSt : 7|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_L1LineTypeSt : 11|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_R1LineTypeSt : 15|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_LineCurveTypeSt : 17|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_L0C0Coef : 31|12@0- (0.01,0) [-20.48|20.47] "m"  IVI
 SG_ HAD_CAMERA_R0C0Coef : 35|12@0- (0.01,0) [-20.48|20.47] "m"  IVI
 SG_ HAD_CAMERA_L0C1Coef : 49|10@0- (0.000976563,0) [-0.357|0.357] "rad"  IVI
 SG_ HAD_CAMERA_FailSafeLineSt : 53|4@0+ (1,0) [0|15] ""  IVI
 SG_ HAD_CAMERA_ICALateralModeSt : 55|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_R0C1Coef : 65|10@0- (0.000976563,0) [-0.357|0.357] "—"  IVI
 SG_ HAD_CAMERA_L0LineQTSt : 69|2@0+ (1,0) [0|3] ""  IVI
 SG_ HAD_CAMERA_R0LineQTSt : 71|2@0+ (1,0) [0|3] ""  IVI
 SG_ HAD_CAMERA_L0C2Coef : 87|16@0- (0.000001,0) [-0.032768|0.032766] "m-1"  IVI
 SG_ HAD_CAMERA_R0C2Coef : 103|16@0- (0.000001,0) [-0.032768|0.032766] "m-1"  IVI
 SG_ HAD_CAMERA_L0C3Coef : 119|16@0- (0.000000004,0) [-0.000131072|0.000131068] "m-2"  IVI
 SG_ HAD_CAMERA_R0C3Coef : 135|16@0+ (0.000000004,0) [0.000131072|0.000131068] "m-2"  IVI
 SG_ MsgCounter41B : 153|2@0+ (1,0) [0.0|3.0] "—"  IVI


BO_ 1120 M_CAMERA_460: 8 M_CAMERA
 SG_ HAD_CAMERA_FrontObject01Zone : 3|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObj01Class : 6|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObject02Zone : 11|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObj02Class : 14|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObject03Zone : 19|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObj03Class : 22|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObject04Zone : 27|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObj04Class : 30|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObject05Zone : 35|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObj05Class : 38|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObject06Zone : 43|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_FrontObj06Class : 46|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_LRObject : 49|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_RRObject : 51|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ RollingCounter460 : 59|4@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 1136 M_CAMERA_470: 48 M_CAMERA
 SG_ HAD_CAMERA_OBJ1LatDst : 7|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_CAMERA_OBJ1Lane : 9|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ1Class : 12|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ1ID : 23|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ1LgtDst : 31|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_CAMERA_OBJ1LatPct : 33|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ2LatDst : 42|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_CAMERA_OBJ2ID : 63|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ2LgtDst : 71|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_CAMERA_OBJ2Lane : 73|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ2LatPct : 84|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ2Class : 87|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ3LgtDst : 93|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_CAMERA_OBJ3ID : 111|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ3LatDst : 119|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_CAMERA_OBJ3Lane : 121|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ3Class : 124|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ3LatPct : 135|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ4LatDst : 143|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_CAMERA_OBJ4Lane : 145|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ4Class : 148|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ4ID : 159|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ4LgtDst : 167|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_CAMERA_OBJ4LatPct : 169|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ5LatDst : 178|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_CAMERA_OBJ5ID : 199|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ5LgtDst : 207|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_CAMERA_OBJ5Lane : 209|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ5LatPct : 220|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ5Class : 223|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ6LgtDst : 229|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_CAMERA_OBJ6ID : 247|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ6LatDst : 255|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_CAMERA_OBJ6Lane : 257|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ6Class : 260|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ6LatPct : 271|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ OBJ1_MsgCount : 379|4@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 1137 HAD_471: 8 HAD
 SG_ HAD_P2P_OBJ9LatDst : 7|11@0+ (0.03,-30.72) [-30.72|30.69] "m"  IVI
 SG_ HAD_P2P_OBJ09Lane : 9|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ HAD_P2P_OBJ09Class : 12|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ HAD_P2P_OBJ09ID : 23|8@0+ (1,0) [0.0|255.0] "—"  IVI
 SG_ HAD_P2P_OBJ9LgtDst : 31|14@0+ (0.025,-100) [-100.0|309.575] "m"  IVI
 SG_ HAD_P2P_OBJ09LatPct : 33|7@0+ (1,0) [0.0|100.0] "—"  IVI
 SG_ OBJ2_MsgCount : 59|4@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 1140 M_CAMERA_474: 48 M_CAMERA
 SG_ HAD_CAMERA_OBJ01LatAccr : 7|12@0- (0.01,0) [-20.48|20.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ01LgtAccr : 11|12@0- (0.01,-10) [-30.48|10.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ01LgtSpeed : 31|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ01LatSpeed : 47|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ01MoveClass : 51|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ02LatAccr : 63|12@0- (0.01,0) [-20.48|20.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ02LgtAccr : 67|12@0- (0.01,-10) [-30.48|10.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ02LgtSpeed : 87|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ02LatSpeed : 103|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ02MoveClass : 107|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ03LatAccr : 119|12@0- (0.01,0) [-20.48|20.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ03LgtAccr : 123|12@0- (0.01,-10) [-30.48|10.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ03LgtSpeed : 143|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ03LatSpeed : 159|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ03MoveClass : 163|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ04LatAccr : 175|12@0- (0.01,0) [-20.48|20.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ04LgtAccr : 179|12@0- (0.01,-10) [-30.48|10.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ04LgtSpeed : 199|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ04LatSpeed : 215|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ04MoveClass : 219|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ05LatAccr : 231|12@0- (0.01,0) [-20.48|20.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ05LgtAccr : 235|12@0- (0.01,-10) [-30.48|10.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ05LgtSpeed : 255|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ05LatSpeed : 271|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ05MoveClass : 275|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_CAMERA_OBJ06LatAccr : 287|12@0- (0.01,0) [-20.48|20.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ06LgtAccr : 291|12@0- (0.01,-10) [-30.48|10.47] "m/s2"  IVI
 SG_ HAD_CAMERA_OBJ06LgtSpeed : 311|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ06LatSpeed : 327|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ HAD_CAMERA_OBJ06MoveClass : 331|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ HAD_P2P_OBJva1_MsgCount : 379|4@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 1143 M_CAMERA_477: 8 M_CAMERA
 SG_ AEB_ObjY : 7|14@0+ (0.025,-10) [-10.0|399.575] "m"  IVI
 SG_ AEB_ObjX : 9|10@0- (0.029325513,0) [-15.014662656|14.985337143] "m"  IVI
 SG_ AEB_ObjVy : 31|16@0- (0.01,0) [-327.68|327.67] "m/s"  IVI
 SG_ AEB_ObjVx : 47|11@0- (0.01,0) [-10.24|10.23] "m/s"  IVI
 SG_ AEB_ObjTyp : 52|3@0+ (1,0) [0.0|7.0] "—"  IVI
 SG_ AEB_ObjMoveClass : 63|4@0+ (1,0) [0.0|15.0] "—"  IVI


BO_ 858 IVI_35A: 8 IVI
 SG_ HAD_APA_AutomaticParkingPodeSt : 1|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_APA_TurnLightsCommandReq : 5|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_APA_LightControlValid : 6|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_APA_OnOffDisp : 7|1@0+ (1,0) [0.0|1.0] ""  Vector__XXX
 SG_ HAD_APA_UPAErrorDisp : 9|2@0+ (1,0) [0.0|3.0] ""  Vector__XXX
 SG_ HAD_APA_MPAPArkNoticeDisp : 15|6@0+ (1,0) [0.0|63.0] ""  Vector__XXX
 SG_ HAD_APA_MPAStFeedbackSt : 20|5@0+ (1,0) [0.0|31.0] ""  Vector__XXX
 SG_ HAD_APA_MPAReadyFbSt : 23|3@0+ (1,0) [0.0|7.0] ""  Vector__XXX
 SG_ RollingCounter35A : 51|4@0+ (1,0) [0.0|15.0] ""  Vector__XXX
 SG_ CheckSum35A : 63|8@0+ (1,0) [0.0|255.0] "—"  Vector__XXX


BO_ 352 IVI_160: 12 IVI
 SG_ HAD_APA_LongitudinalCtrlReq : 2|3@0+ (1,0) [0.0|7.0] ""  Vector__XXX
 SG_ HAD_APA_SystemSt : 5|3@0+ (1,0) [0.0|7.0] ""  Vector__XXX
 SG_ HAD_APA_ReqToStopDst : 17|10@0+ (1,0) [0.0|1023.0] "cm"  Vector__XXX
 SG_ HAD_APA_ReqtTargetGearPosSt : 20|3@0+ (1,0) [0.0|7.0] "—"  Vector__XXX
 SG_ HAD_APA_FailureBrakeModeSt : 22|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_APA_BrakeTargetMaxSpeed : 35|12@0+ (0.01,0) [0.0|20.05] "km/h"  Vector__XXX
 SG_ HAD_APA_EmergencySt : 36|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_APA_Emergency_Valid : 37|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_APA_ESC_Funmode : 39|2@0+ (1,0) [0.0|3.0] ""  Vector__XXX
 SG_ HAD_APA_ReqEPSTargetAngleReq : 71|16@0- (0.1,0) [-780.0|780.0] "degree"  Vector__XXX
 SG_ RollingCounter160 : 83|4@0+ (1,0) [0.0|15.0] "—"  Vector__XXX
 SG_ HAD_APA_ControlEPSReq : 85|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_EPS_FuncModeReq : 87|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ CheckSum160 : 95|8@0+ (1,0) [0.0|255.0] "—"  Vector__XXX


BO_ 1201 IVI_4B1: 8 IVI
 SG_ HAD_APA_AVMDispViewSt : 4|5@0+ (1,0) [0.0|31.0] "—"  Vector__XXX
 SG_ HAD_APA_AVMLogSt : 11|4@0+ (1,0) [0.0|15.0] "—"  Vector__XXX
 SG_ HAD_APA_AVMFaultRem : 23|8@0+ (1,0) [0.0|255.0] "—"  Vector__XXX


BO_ 859 IVI_35B: 8 IVI
 SG_ HAD_SRR_LF_FCTAWorkingSt : 1|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_SRR_LF_FCTBWorkingSt : 3|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_SRR_LF_AutoCaliResultSt : 6|3@0+ (1,0) [0.0|7.0] "—"  Vector__XXX
 SG_ HAD_SRR_LF_ErrorSt : 7|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_FCTBDeceleration : 8|9@0+ (0.05,-16) [-16.0|0.0] "m/s2"  Vector__XXX
 SG_ HAD_SRR_LF_FCTAWarn : 9|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_SRR_RF_FCTAWarn : 10|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_FCTBVaild : 11|1@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ HAD_FCTBCollisionDirectionSt : 13|2@0+ (1,0) [0.0|3.0] "—"  Vector__XXX
 SG_ HAD_FCTBActionSt : 15|2@0+ (1,0) [0.0|1.0] "—"  Vector__XXX
 SG_ RollingCount35B : 51|4@0+ (1,0) [0.0|15.0] "—"  Vector__XXX
 SG_ CheckSum35B : 63|8@0+ (1,0) [0.0|255.0] "—"  Vector__XXX


BO_ 782 IVI_30E: 8 IVI
 SG_ HAD_APA_ADASLeftLEDWarnSt : 9|2@0+ (1,0) [0.0|3.0] ""  Vector__XXX
 SG_ HAD_APA_ADASRighrLEDWarnSt : 11|2@0+ (1,0) [0.0|3.0] ""  Vector__XXX


BO_ 942 IVI_3AE: 48 IVI
 SG_ RollingCounter3AE : 371|4@0+ (1,0) [0.0|15.0] "—"  Vector__XXX
 SG_ CheckSum3AE : 383|8@0+ (1,0) [0.0|255.0] "—"  Vector__XXX


BO_ 570 SGW_C_23A: 8 SGW_C
 SG_ VIUL_TrunkSt : 0|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_LetfligthSt : 1|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_RightligthSt : 3|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_PosLampSt : 5|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_HazardLampSt : 6|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_HighBeamSt : 7|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_LowBeamSt : 8|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_LFDoorSwSt : 11|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_RFDoorSwSt : 12|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_RRDoorSwSt : 13|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_LRDoorSwSt : 14|1@0+ (1,0) [0.0|1.0] "—"  IVI
 SG_ VIUL_LeftTurnSwSt : 21|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ VIUL_RightTurnSwrSt : 23|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ VIUL_FrontWiperWorkSt : 41|2@0+ (1,0) [0.0|3.0] "—"  IVI
 SG_ VIUL_HazardSwrSt : 47|1@0+ (1,0) [0.0|1.0] "—"  IVI


BO_ 64 SGW_C_40: 2 SGW_C
 SG_ VIUL_PEPS_RKECommand : 7|4@0+ (1,0) [0.0|15.0] "—"  IVI
 SG_ VIUL_PEPS_DoorLockReq : 9|2@0+ (1,0) [0.0|3.0] "—"  IVI


BO_ 802 SGW_C_322: 8 SGW_C
 SG_ VIUR_AC_AmbTempSt : 7|8@0+ (0.5,-40) [-40.0|80.0] "℃"  IVI


BO_ 281 SGW_C_119: 8 SGW_C
 SG_ ACU_YawRateSt : 7|16@0+ (0.03,-120) [-120.0|120.0] "°/s"  IVI
 SG_ ACU_LateralAccelarationSt : 19|12@0+ (0.01,-20) [-20.0|20.0] "m/s²"  IVI
 SG_ ACU_LongitdAcclerValid : 20|1@0+ (1,0) [0.0|1.0] "_"  IVI
 SG_ ACU_LateralAccelareValid : 22|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ ACU_YawrateValiditySt : 23|1@0+ (1,0) [0.0|1.0] ""  IVI
 SG_ ACU_LongitudAccelerationSt : 47|12@0+ (0.01,-20) [-20.0|20.0] "m/s²"  IVI
 SG_ RollingCounter119 : 51|4@0+ (1,0) [0.0|15.0] "_"  IVI
 SG_ CheckSum119 : 63|8@0+ (1,0) [0.0|255.0] "_"  IVI




BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 100000000000;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 100000;
BA_DEF_ SG_  "NWM - WakeupAllowed" ENUM  "no","Yes";
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed","vector_leerstring";
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 999999;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 1000;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 50000;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 50000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM "no","Yes";
BA_DEF_ BO_  "NmMessage" ENUM  "no","Yes";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 65535;
BA_DEF_ BU_  "NmStationAddress" INT 0 63;
BA_DEF_ BU_  "NmNode" ENUM  "no","Yes";
BA_DEF_  "NmBaseAddress" HEX 1024 1087;
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING;
BA_DEF_  "BusType" STRING;
BA_DEF_ BU_  "NodeLayerModules" STRING;
BA_DEF_ BU_  "ECU" STRING;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin"  INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved", "reserved", "StandardCAN_FD", "ExtendedCAN_FD";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigSendType" "NoSigSendType";
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "NWM - WakeupAllowed" "";
BA_DEF_DEF_  "GenMsgSendType" "NoMsgSendType";
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "NmMessage" " no";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmNode" " no";
BA_DEF_DEF_  "NmBaseAddress" 1024;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_ "BusType" "CAN FD";
BA_ "DBName" "CAN";

BA_ "VFrameFormat" BO_ 838 14;
BA_ "VFrameFormat" BO_ 833 14;
BA_ "VFrameFormat" BO_ 828 14;
BA_ "VFrameFormat" BO_ 1242 14;
BA_ "VFrameFormat" BO_ 131 14;
BA_ "VFrameFormat" BO_ 162 14;
BA_ "VFrameFormat" BO_ 307 14;
BA_ "VFrameFormat" BO_ 285 14;
BA_ "VFrameFormat" BO_ 300 14;
BA_ "VFrameFormat" BO_ 257 14;
BA_ "VFrameFormat" BO_ 370 14;
BA_ "VFrameFormat" BO_ 323 14;
BA_ "VFrameFormat" BO_ 252 14;
BA_ "VFrameFormat" BO_ 456 14;
BA_ "VFrameFormat" BO_ 254 14;
BA_ "VFrameFormat" BO_ 255 14;
BA_ "VFrameFormat" BO_ 378 14;
BA_ "VFrameFormat" BO_ 165 14;
BA_ "VFrameFormat" BO_ 176 14;
BA_ "VFrameFormat" BO_ 358 14;
BA_ "VFrameFormat" BO_ 359 14;
BA_ "VFrameFormat" BO_ 501 14;
BA_ "VFrameFormat" BO_ 1144 14;
BA_ "VFrameFormat" BO_ 1051 14;
BA_ "VFrameFormat" BO_ 1120 14;
BA_ "VFrameFormat" BO_ 1136 14;
BA_ "VFrameFormat" BO_ 1137 14;
BA_ "VFrameFormat" BO_ 1140 14;
BA_ "VFrameFormat" BO_ 1143 14;
BA_ "VFrameFormat" BO_ 858 14;
BA_ "VFrameFormat" BO_ 352 14;
BA_ "VFrameFormat" BO_ 1201 14;
BA_ "VFrameFormat" BO_ 859 14;
BA_ "VFrameFormat" BO_ 782 14;
BA_ "VFrameFormat" BO_ 942 14;
BA_ "VFrameFormat" BO_ 570 14;
BA_ "VFrameFormat" BO_ 64 14;
BA_ "VFrameFormat" BO_ 802 14;
BA_ "VFrameFormat" BO_ 281 14;
BA_ "GenMsgSendType" BO_ 838 0;
BA_ "GenMsgSendType" BO_ 833 0;
BA_ "GenMsgSendType" BO_ 828 0;
BA_ "GenMsgSendType" BO_ 1242 0;
BA_ "GenMsgSendType" BO_ 131 0;
BA_ "GenMsgSendType" BO_ 162 0;
BA_ "GenMsgSendType" BO_ 307 0;
BA_ "GenMsgSendType" BO_ 285 0;
BA_ "GenMsgSendType" BO_ 300 0;
BA_ "GenMsgSendType" BO_ 257 0;
BA_ "GenMsgSendType" BO_ 370 0;
BA_ "GenMsgSendType" BO_ 323 0;
BA_ "GenMsgSendType" BO_ 252 0;
BA_ "GenMsgSendType" BO_ 456 0;
BA_ "GenMsgSendType" BO_ 254 0;
BA_ "GenMsgSendType" BO_ 255 0;
BA_ "GenMsgSendType" BO_ 378 0;
BA_ "GenMsgSendType" BO_ 165 0;
BA_ "GenMsgSendType" BO_ 176 0;
BA_ "GenMsgSendType" BO_ 358 0;
BA_ "GenMsgSendType" BO_ 359 0;
BA_ "GenMsgSendType" BO_ 501 0;
BA_ "GenMsgSendType" BO_ 1144 0;
BA_ "GenMsgSendType" BO_ 1051 0;
BA_ "GenMsgSendType" BO_ 1120 0;
BA_ "GenMsgSendType" BO_ 1136 0;
BA_ "GenMsgSendType" BO_ 1137 0;
BA_ "GenMsgSendType" BO_ 1140 0;
BA_ "GenMsgSendType" BO_ 1143 0;
BA_ "GenMsgSendType" BO_ 858 0;
BA_ "GenMsgSendType" BO_ 352 0;
BA_ "GenMsgSendType" BO_ 1201 0;
BA_ "GenMsgSendType" BO_ 859 0;
BA_ "GenMsgSendType" BO_ 782 0;
BA_ "GenMsgSendType" BO_ 942 0;
BA_ "GenMsgSendType" BO_ 570 0;
BA_ "GenMsgSendType" BO_ 64 8;
BA_ "GenMsgSendType" BO_ 802 0;
BA_ "GenMsgSendType" BO_ 281 0;
BA_ "GenMsgCycleTime" BO_ 838 100;
BA_ "GenMsgCycleTime" BO_ 833 100;
BA_ "GenMsgCycleTime" BO_ 828 100;
BA_ "GenMsgCycleTime" BO_ 1242 1000;
BA_ "GenMsgCycleTime" BO_ 131 10;
BA_ "GenMsgCycleTime" BO_ 162 20;
BA_ "GenMsgCycleTime" BO_ 307 20;
BA_ "GenMsgCycleTime" BO_ 285 20;
BA_ "GenMsgCycleTime" BO_ 300 20;
BA_ "GenMsgCycleTime" BO_ 257 20;
BA_ "GenMsgCycleTime" BO_ 370 20;
BA_ "GenMsgCycleTime" BO_ 323 20;
BA_ "GenMsgCycleTime" BO_ 252 10;
BA_ "GenMsgCycleTime" BO_ 456 20;
BA_ "GenMsgCycleTime" BO_ 254 20;
BA_ "GenMsgCycleTime" BO_ 255 10;
BA_ "GenMsgCycleTime" BO_ 378 20;
BA_ "GenMsgCycleTime" BO_ 165 10;
BA_ "GenMsgCycleTime" BO_ 176 10;
BA_ "GenMsgCycleTime" BO_ 358 20;
BA_ "GenMsgCycleTime" BO_ 359 20;
BA_ "GenMsgCycleTime" BO_ 501 20;
BA_ "GenMsgCycleTime" BO_ 1144 100;
BA_ "GenMsgCycleTime" BO_ 1051 100;
BA_ "GenMsgCycleTime" BO_ 1120 100;
BA_ "GenMsgCycleTime" BO_ 1136 100;
BA_ "GenMsgCycleTime" BO_ 1137 100;
BA_ "GenMsgCycleTime" BO_ 1140 100;
BA_ "GenMsgCycleTime" BO_ 1143 100;
BA_ "GenMsgCycleTime" BO_ 858 100;
BA_ "GenMsgCycleTime" BO_ 352 20;
BA_ "GenMsgCycleTime" BO_ 1201 500;
BA_ "GenMsgCycleTime" BO_ 859 100;
BA_ "GenMsgCycleTime" BO_ 782 100;
BA_ "GenMsgCycleTime" BO_ 942 100;
BA_ "GenMsgCycleTime" BO_ 570 40;
BA_ "GenMsgCycleTime" BO_ 802 100;
BA_ "GenMsgCycleTime" BO_ 281 20;
BA_ "GenMsgCycleTimeFast" BO_ 838 100;
BA_ "GenMsgCycleTimeFast" BO_ 833 100;
BA_ "GenMsgCycleTimeFast" BO_ 828 20;
BA_ "GenMsgCycleTimeFast" BO_ 1242 1000;
BA_ "GenMsgCycleTimeFast" BO_ 131 10;
BA_ "GenMsgCycleTimeFast" BO_ 162 20;
BA_ "GenMsgCycleTimeFast" BO_ 307 20;
BA_ "GenMsgCycleTimeFast" BO_ 285 20;
BA_ "GenMsgCycleTimeFast" BO_ 300 20;
BA_ "GenMsgCycleTimeFast" BO_ 257 20;
BA_ "GenMsgCycleTimeFast" BO_ 370 20;
BA_ "GenMsgCycleTimeFast" BO_ 323 20;
BA_ "GenMsgCycleTimeFast" BO_ 252 10;
BA_ "GenMsgCycleTimeFast" BO_ 456 20;
BA_ "GenMsgCycleTimeFast" BO_ 254 20;
BA_ "GenMsgCycleTimeFast" BO_ 255 10;
BA_ "GenMsgCycleTimeFast" BO_ 378 20;
BA_ "GenMsgCycleTimeFast" BO_ 165 10;
BA_ "GenMsgCycleTimeFast" BO_ 176 10;
BA_ "GenMsgCycleTimeFast" BO_ 358 20;
BA_ "GenMsgCycleTimeFast" BO_ 359 20;
BA_ "GenMsgCycleTimeFast" BO_ 501 20;
BA_ "GenMsgCycleTimeFast" BO_ 1144 100;
BA_ "GenMsgCycleTimeFast" BO_ 1051 100;
BA_ "GenMsgCycleTimeFast" BO_ 1120 100;
BA_ "GenMsgCycleTimeFast" BO_ 1136 100;
BA_ "GenMsgCycleTimeFast" BO_ 1137 100;
BA_ "GenMsgCycleTimeFast" BO_ 1140 100;
BA_ "GenMsgCycleTimeFast" BO_ 1143 100;
BA_ "GenMsgCycleTimeFast" BO_ 858 20;
BA_ "GenMsgCycleTimeFast" BO_ 352 20;
BA_ "GenMsgCycleTimeFast" BO_ 1201 20;
BA_ "GenMsgCycleTimeFast" BO_ 859 100;
BA_ "GenMsgCycleTimeFast" BO_ 782 20;
BA_ "GenMsgCycleTimeFast" BO_ 942 100;
BA_ "GenMsgCycleTimeFast" BO_ 570 20;
BA_ "GenMsgCycleTimeFast" BO_ 64 20;
BA_ "GenMsgCycleTimeFast" BO_ 802 100;
BA_ "GenMsgCycleTimeFast" BO_ 281 20;
BA_ "GenMsgNrOfRepetition" BO_ 858 3;
BA_ "GenMsgNrOfRepetition" BO_ 1201 3;
BA_ "GenMsgNrOfRepetition" BO_ 782 3;
BA_ "GenSigStartValue" SG_  838 VIUL_PEPS_RKECommand2 0;
BA_ "GenSigStartValue" SG_  833 VIUL_BattVoltSt 0;
BA_ "GenSigStartValue" SG_  828 VIUL_IgnitionSt 0;
BA_ "GenSigStartValue" SG_  828 VIUL_DoorLockSt 0;
BA_ "GenSigStartValue" SG_  828 VIUL_FrontWiperSwSt 0;
BA_ "GenSigStartValue" SG_  828 VIUL_CHMSLSt 0;
BA_ "GenSigStartValue" SG_  828 VIUL_BrakeLampSt 0;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeYear 0;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeMonth 1;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeDate 1;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeHour 0;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeMinute 0;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeSecond 0;
BA_ "GenSigStartValue" SG_  1242 TBOX_TimeValid 1;
BA_ "GenSigStartValue" SG_  131 EMS_EngSpd 0;
BA_ "GenSigStartValue" SG_  162 IBC_VehicleSpeed 8190;
BA_ "GenSigStartValue" SG_  162 IBC_VehicleSpeedValid 0;
BA_ "GenSigStartValue" SG_  162 IBC_VehicleStandstillSt 0;
BA_ "GenSigStartValue" SG_  162 IBC_VehicleStandstillValid 0;
BA_ "GenSigStartValue" SG_  162 IBC_VehicleDrivingDirection 0;
BA_ "GenSigStartValue" SG_  162 IBC_VehicleHoldSt 0;
BA_ "GenSigStartValue" SG_  162 IBC_ModeSt 0;
BA_ "GenSigStartValue" SG_  162 IBC_BrakelightReq 0;
BA_ "GenSigStartValue" SG_  162 IBC_EBDFaultSt 1;
BA_ "GenSigStartValue" SG_  162 IBC_EBDActiveSt 0;
BA_ "GenSigStartValue" SG_  162 IBC_ESCoffSt 1;
BA_ "GenSigStartValue" SG_  162 IBC_ESCFaultSt 1;
BA_ "GenSigStartValue" SG_  162 RollingCounter0A2 0;
BA_ "GenSigStartValue" SG_  162 Checksum0A2 0;
BA_ "GenSigStartValue" SG_  307 IBC_BrakePedalSt 0;
BA_ "GenSigStartValue" SG_  285 IBC_PlungerPressure 17;
BA_ "GenSigStartValue" SG_  300 IBC_EPBSwSt 0;
BA_ "GenSigStartValue" SG_  300 IBC_EPBSwValiditySt 0;
BA_ "GenSigStartValue" SG_  300 IBC_EPBSt 1;
BA_ "GenSigStartValue" SG_  300 IBC_EPBErrorSt 0;
BA_ "GenSigStartValue" SG_  300 IBC_Slope 62;
BA_ "GenSigStartValue" SG_  300 IBC_SlopeSt 0;
BA_ "GenSigStartValue" SG_  300 IBC_SlopHighWarn 0;
BA_ "GenSigStartValue" SG_  300 RollingCounter31A 0;
BA_ "GenSigStartValue" SG_  300 Checksum31A 0;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedRL 8190;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedRLValid 0;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedRR 8190;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedRRValid 0;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedFL 8190;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedFLValid 0;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedFR 8190;
BA_ "GenSigStartValue" SG_  257 IBC_wheelSpeedFRValid 0;
BA_ "GenSigStartValue" SG_  257 RollingCounter101 0;
BA_ "GenSigStartValue" SG_  257 Checksum101 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeFLWSS 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeFRWSS 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeFLWSSValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeFRWSSValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionFLValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionFRValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionFLSt 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionFRSt 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeRLWSS 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeRRWSS 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeRLWSSValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_SumOfEdgeRRWSSValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionRLValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionRRValid 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionRLSt 0;
BA_ "GenSigStartValue" SG_  370 IBC_WheelDirectionRRSt 0;
BA_ "GenSigStartValue" SG_  370 RollingCounter172 0;
BA_ "GenSigStartValue" SG_  370 Checksum172 0;
BA_ "GenSigStartValue" SG_  323 IBC_LSMCtrlFaultSt 0;
BA_ "GenSigStartValue" SG_  323 IBC_APABrakSysLongictlSt 0;
BA_ "GenSigStartValue" SG_  323 IBC_APABrakeModeSt 0;
BA_ "GenSigStartValue" SG_  323 IBC_APAGearReqActiveSt 0;
BA_ "GenSigStartValue" SG_  323 IBC_WHEEL_TRQ_APATReq 16382;
BA_ "GenSigStartValue" SG_  323 IBC_PDCUCtrlReq 0;
BA_ "GenSigStartValue" SG_  323 IBC_VlcInterTargetAx 255;
BA_ "GenSigStartValue" SG_  323 IBC_TargetGearReq 0;
BA_ "GenSigStartValue" SG_  323 RollingCounter143 0;
BA_ "GenSigStartValue" SG_  323 Checksum143 0;
BA_ "GenSigStartValue" SG_  252 PDCU_AccelPedalVirtualSt 1022;
BA_ "GenSigStartValue" SG_  252 PDCU_AccPedalVirtlValid 1;
BA_ "GenSigStartValue" SG_  252 PDCU_FrontMotorRpm 0;
BA_ "GenSigStartValue" SG_  252 PDCU_FAWhlTqAct 0;
BA_ "GenSigStartValue" SG_  252 PDCU_frontMotorRpmValid 0;
BA_ "GenSigStartValue" SG_  252 PDCU_FAWhlTqRegenMaxValid 0;
BA_ "GenSigStartValue" SG_  252 PDCU_FAWhlTqRegenMax 4094;
BA_ "GenSigStartValue" SG_  252 PDCU_rearMotRpm 0;
BA_ "GenSigStartValue" SG_  252 PDCU_RAWhlTqAct 0;
BA_ "GenSigStartValue" SG_  252 PDCU_rearMotRpmValid 0;
BA_ "GenSigStartValue" SG_  252 PDCU_RAWhlTqRegenMaxValid 0;
BA_ "GenSigStartValue" SG_  252 PDCU_RAWhlTqRegenMax 4094;
BA_ "GenSigStartValue" SG_  252 RollingCounter0FC 0;
BA_ "GenSigStartValue" SG_  252 Checksum0FC 0;
BA_ "GenSigStartValue" SG_  456 PDCU_ActualGear 0;
BA_ "GenSigStartValue" SG_  456 PDCU_AccelPedalSt 1022;
BA_ "GenSigStartValue" SG_  456 PDCU_AccelPedalValid 1;
BA_ "GenSigStartValue" SG_  456 RollingCounter1C8 0;
BA_ "GenSigStartValue" SG_  456 Checksum1C8 0;
BA_ "GenSigStartValue" SG_  254 PDCU_DriveReadySt 0;
BA_ "GenSigStartValue" SG_  254 PDCU_BMS_SOCCalculate 0;
BA_ "GenSigStartValue" SG_  254 RollingCounter0FE 0;
BA_ "GenSigStartValue" SG_  254 Checksum0FE 0;
BA_ "GenSigStartValue" SG_  255 EMS_PowertrainControlSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_ShiftQuitReason 0;
BA_ "GenSigStartValue" SG_  255 PDCU_ShiftAvaibleSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_ACCFunIhibitionReq 0;
BA_ "GenSigStartValue" SG_  255 PDCU_DriveAsiSttACCSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_DriveAsiStt_APA 0;
BA_ "GenSigStartValue" SG_  255 PDCU_AxleTorqueSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_MaxVehicleWhlTq 0;
BA_ "GenSigStartValue" SG_  255 PDCU_TargetDriverTorq 0;
BA_ "GenSigStartValue" SG_  255 PDCU_ACCResponseSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_RealizedPowertrainWhlTq 0;
BA_ "GenSigStartValue" SG_  255 PDCU_ACCcontrolAvailableSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_WhetherACCReqRealizedSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_MCUF_InverterWorkSt 0;
BA_ "GenSigStartValue" SG_  255 PDCU_MCUR_InverterWorkSt 0;
BA_ "GenSigStartValue" SG_  255 RollingCounter0FF 0;
BA_ "GenSigStartValue" SG_  255 Checksum0FF 0;
BA_ "GenSigStartValue" SG_  378 EPS_SteeringTorqueSensorSt 0;
BA_ "GenSigStartValue" SG_  378 EPS_SteeringHoldSt 0;
BA_ "GenSigStartValue" SG_  378 EPS_SteeringAngleFlag 0;
BA_ "GenSigStartValue" SG_  378 EPS_SteeringAngle 32767;
BA_ "GenSigStartValue" SG_  378 EPS_SteeringTorqueValue 2048;
BA_ "GenSigStartValue" SG_  378 EPS_RollingCount17A 0;
BA_ "GenSigStartValue" SG_  378 EPS_CheckSum17A 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_SteeringAngle 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_SteeringRotSpd 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_TrimmingSt 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_CalibratedSt 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_SASFailureSt 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_RollingCountA5 0;
BA_ "GenSigStartValue" SG_  165 TAS_SAS_CheckSumA5 0;
BA_ "GenSigStartValue" SG_  176 EPS_APARollingCountB0 0;
BA_ "GenSigStartValue" SG_  176 EPS_APAChecksumB0 0;
BA_ "GenSigStartValue" SG_  176 EPS_APAProhibitedResaon 0;
BA_ "GenSigStartValue" SG_  176 EPS_APAFuncModeSt 0;
BA_ "GenSigStartValue" SG_  176 EPS_APASt 0;
BA_ "GenSigStartValue" SG_  358 EPS_LKA_DriverOverrideSt 0;
BA_ "GenSigStartValue" SG_  358 EPS_LKA_TorqueLimitationSt 0;
BA_ "GenSigStartValue" SG_  359 EPS_ESA_DriverOverrideSt 0;
BA_ "GenSigStartValue" SG_  359 EPS_DriverInitializedESASt 0;
BA_ "GenSigStartValue" SG_  359 EPS_ESA_TorqueLimitationSt 0;
BA_ "GenSigStartValue" SG_  501 HAD_CAMERA_ACCSt 0;
BA_ "GenSigStartValue" SG_  501 HAD_CAMERA_HMITargetDisp 0;
BA_ "GenSigStartValue" SG_  501 HAD_CAMERA_ACCLongitudialDst 0;
BA_ "GenSigStartValue" SG_  501 HAD_CAMERA_ACCLateralDst 0;
BA_ "GenSigStartValue" SG_  501 HAD_CAMERA_TargetPosSt 4;
BA_ "GenSigStartValue" SG_  501 HAD_CAMERA_FDMSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCSetSpdDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCHeadwayIconDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCHeadwayDisp 3;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCRVVTypeReq 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCStIconDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCMessageRem 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCSetSpeed 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISLCSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_AEBFaultSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_AEBMassDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_AEBAlertReq 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_AEBSwSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ACCdbRem 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ICAStIconDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ICAMessageDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISLCDriverRem 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_HandsoffWarn 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_OperatingSt 9;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LDWStDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LKAStDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_VehicleLaneDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LKAPopupMessage 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LDWWarnSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LKAModeSwSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LCAStDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISASignTypeSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISASt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISAPopMessageDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISAValue 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_TSRSignTypeSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LCAPopMessageDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_HBCHighBeamReq 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_IHBCFuctionSt 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_JA_AlertReq 0;
BA_ "GenSigStartValue" SG_  1144 FDM_LongitDstDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_FDMWarn 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_FDMStDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_FDMMessageDisp 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_LSSSwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_TSRSwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISASwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISAModeSwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ISASetSpeeddevSwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_FCWSwst 2;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_FCWSensitivitySwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_FDMSwst 0;
BA_ "GenSigStartValue" SG_  1144 HAD_CAMERA_ELKSwst 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L0LineTypeSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R0LineTypeSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L1LineTypeSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R1LineTypeSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_LineCurveTypeSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L0C0Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R0C0Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L0C1Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_FailSafeLineSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_ICALateralModeSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L0LineQTSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R0LineQTSt 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R0C1Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L0C2Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R0C2Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_L0C3Coef 0;
BA_ "GenSigStartValue" SG_  1051 HAD_CAMERA_R0C3Coef 0;
BA_ "GenSigStartValue" SG_  1051 MsgCounter41B 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObject01Zone 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObj01Class 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObject02Zone 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObj02Class 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObject03Zone 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObj03Class 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObject04Zone 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObj04Class 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObject05Zone 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObj05Class 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObject06Zone 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_FrontObj06Class 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_LRObject 0;
BA_ "GenSigStartValue" SG_  1120 HAD_CAMERA_RRObject 0;
BA_ "GenSigStartValue" SG_  1120 RollingCounter460 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ1LatDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ1LgtDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ1Class 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ1ID 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ1Lane 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ1LatPct 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ2LatDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ2LgtDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ2Class 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ2ID 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ2Lane 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ2LatPct 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ3LatDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ3LgtDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ3Class 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ3ID 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ3Lane 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ3LatPct 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ4LatDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ4LgtDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ4Class 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ4ID 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ4Lane 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ4LatPct 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ5LatDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ5LgtDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ5Class 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ5ID 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ5Lane 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ5LatPct 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ6LatDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ6LgtDst 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ6Class 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ6ID 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ6Lane 0;
BA_ "GenSigStartValue" SG_  1136 HAD_CAMERA_OBJ6LatPct 0;
BA_ "GenSigStartValue" SG_  1136 OBJ1_MsgCount 0;
BA_ "GenSigStartValue" SG_  1137 HAD_P2P_OBJ9LatDst 0;
BA_ "GenSigStartValue" SG_  1137 HAD_P2P_OBJ9LgtDst 0;
BA_ "GenSigStartValue" SG_  1137 HAD_P2P_OBJ09Class 0;
BA_ "GenSigStartValue" SG_  1137 HAD_P2P_OBJ09ID 0;
BA_ "GenSigStartValue" SG_  1137 HAD_P2P_OBJ09Lane 0;
BA_ "GenSigStartValue" SG_  1137 HAD_P2P_OBJ09LatPct 0;
BA_ "GenSigStartValue" SG_  1137 OBJ2_MsgCount 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ01LatSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ01LgtSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ01LatAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ01LgtAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ01MoveClass 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ02LatSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ02LgtSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ02LatAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ02LgtAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ02MoveClass 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ03LatSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ03LgtSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ03LatAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ03LgtAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ03MoveClass 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ04LatSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ04LgtSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ04LatAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ04LgtAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ04MoveClass 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ05LatSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ05LgtSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ05LatAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ05LgtAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ05MoveClass 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ06LatSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ06LgtSpeed 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ06LatAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ06LgtAccr 0;
BA_ "GenSigStartValue" SG_  1140 HAD_CAMERA_OBJ06MoveClass 0;
BA_ "GenSigStartValue" SG_  1140 HAD_P2P_OBJva1_MsgCount 0;
BA_ "GenSigStartValue" SG_  1143 AEB_ObjY 0;
BA_ "GenSigStartValue" SG_  1143 AEB_ObjX 0;
BA_ "GenSigStartValue" SG_  1143 AEB_ObjVy 0;
BA_ "GenSigStartValue" SG_  1143 AEB_ObjVx 0;
BA_ "GenSigStartValue" SG_  1143 AEB_ObjTyp 0;
BA_ "GenSigStartValue" SG_  1143 AEB_ObjMoveClass 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_AutomaticParkingPodeSt 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_TurnLightsCommandReq 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_LightControlValid 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_OnOffDisp 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_MPAPArkNoticeDisp 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_UPAErrorDisp 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_MPAStFeedbackSt 0;
BA_ "GenSigStartValue" SG_  858 HAD_APA_MPAReadyFbSt 0;
BA_ "GenSigStartValue" SG_  858 RollingCounter35A 0;
BA_ "GenSigStartValue" SG_  858 CheckSum35A 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_LongitudinalCtrlReq 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_SystemSt 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_FailureBrakeModeSt 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_ReqtTargetGearPosSt 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_ReqToStopDst 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_EmergencySt 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_Emergency_Valid 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_ESC_Funmode 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_BrakeTargetMaxSpeed 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_ReqEPSTargetAngleReq 0;
BA_ "GenSigStartValue" SG_  352 HAD_APA_ControlEPSReq 0;
BA_ "GenSigStartValue" SG_  352 RollingCounter160 0;
BA_ "GenSigStartValue" SG_  352 CheckSum160 0;
BA_ "GenSigStartValue" SG_  352 HAD_EPS_FuncModeReq 0;
BA_ "GenSigStartValue" SG_  1201 HAD_APA_AVMDispViewSt 0;
BA_ "GenSigStartValue" SG_  1201 HAD_APA_AVMLogSt 0;
BA_ "GenSigStartValue" SG_  1201 HAD_APA_AVMFaultRem 0;
BA_ "GenSigStartValue" SG_  859 HAD_SRR_LF_FCTAWorkingSt 0;
BA_ "GenSigStartValue" SG_  859 HAD_SRR_LF_FCTBWorkingSt 0;
BA_ "GenSigStartValue" SG_  859 HAD_SRR_LF_AutoCaliResultSt 0;
BA_ "GenSigStartValue" SG_  859 HAD_SRR_LF_ErrorSt 0;
BA_ "GenSigStartValue" SG_  859 HAD_SRR_LF_FCTAWarn 0;
BA_ "GenSigStartValue" SG_  859 HAD_SRR_RF_FCTAWarn 0;
BA_ "GenSigStartValue" SG_  859 HAD_FCTBVaild 0;
BA_ "GenSigStartValue" SG_  859 HAD_FCTBCollisionDirectionSt 0;
BA_ "GenSigStartValue" SG_  859 HAD_FCTBActionSt 0;
BA_ "GenSigStartValue" SG_  859 HAD_FCTBDeceleration 511;
BA_ "GenSigStartValue" SG_  859 RollingCount35B 0;
BA_ "GenSigStartValue" SG_  859 CheckSum35B 0;
BA_ "GenSigStartValue" SG_  782 HAD_APA_ADASLeftLEDWarnSt 0;
BA_ "GenSigStartValue" SG_  782 HAD_APA_ADASRighrLEDWarnSt 0;
BA_ "GenSigStartValue" SG_  942 RollingCounter3AE 0;
BA_ "GenSigStartValue" SG_  942 CheckSum3AE 0;
BA_ "GenSigStartValue" SG_  570 VIUL_TrunkSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_LetfligthSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_RightligthSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_PosLampSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_HazardLampSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_HighBeamSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_LowBeamSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_LFDoorSwSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_RFDoorSwSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_RRDoorSwSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_LRDoorSwSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_LeftTurnSwSt 3;
BA_ "GenSigStartValue" SG_  570 VIUL_RightTurnSwrSt 3;
BA_ "GenSigStartValue" SG_  570 VIUL_FrontWiperWorkSt 0;
BA_ "GenSigStartValue" SG_  570 VIUL_HazardSwrSt 0;
BA_ "GenSigStartValue" SG_  64 VIUL_PEPS_RKECommand 0;
BA_ "GenSigStartValue" SG_  64 VIUL_PEPS_DoorLockReq 0;
BA_ "GenSigStartValue" SG_  802 VIUR_AC_AmbTempSt 0;
BA_ "GenSigStartValue" SG_  281 ACU_YawRateSt 0;
BA_ "GenSigStartValue" SG_  281 ACU_YawrateValiditySt 0;
BA_ "GenSigStartValue" SG_  281 ACU_LateralAccelarationSt 0;
BA_ "GenSigStartValue" SG_  281 ACU_LateralAccelareValid 0;
BA_ "GenSigStartValue" SG_  281 ACU_LongitdAcclerValid 0;
BA_ "GenSigStartValue" SG_  281 ACU_LongitudAccelerationSt 0;
BA_ "GenSigStartValue" SG_  281 RollingCounter119 0;
BA_ "GenSigStartValue" SG_  281 CheckSum119 0;
BA_ "GenSigSendType" SG_  838 VIUL_PEPS_RKECommand2 0;
BA_ "GenSigSendType" SG_  833 VIUL_BattVoltSt 0;
BA_ "GenSigSendType" SG_  828 VIUL_IgnitionSt 0;
BA_ "GenSigSendType" SG_  828 VIUL_DoorLockSt 0;
BA_ "GenSigSendType" SG_  828 VIUL_FrontWiperSwSt 0;
BA_ "GenSigSendType" SG_  828 VIUL_CHMSLSt 0;
BA_ "GenSigSendType" SG_  828 VIUL_BrakeLampSt 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeYear 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeMonth 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeDate 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeHour 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeMinute 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeSecond 0;
BA_ "GenSigSendType" SG_  1242 TBOX_TimeValid 0;
BA_ "GenSigSendType" SG_  131 EMS_EngSpd 0;
BA_ "GenSigSendType" SG_  162 IBC_VehicleSpeed 0;
BA_ "GenSigSendType" SG_  162 IBC_VehicleSpeedValid 0;
BA_ "GenSigSendType" SG_  162 IBC_VehicleStandstillSt 0;
BA_ "GenSigSendType" SG_  162 IBC_VehicleStandstillValid 0;
BA_ "GenSigSendType" SG_  162 IBC_VehicleDrivingDirection 0;
BA_ "GenSigSendType" SG_  162 IBC_VehicleHoldSt 0;
BA_ "GenSigSendType" SG_  162 IBC_ModeSt 0;
BA_ "GenSigSendType" SG_  162 IBC_BrakelightReq 0;
BA_ "GenSigSendType" SG_  162 IBC_EBDFaultSt 0;
BA_ "GenSigSendType" SG_  162 IBC_EBDActiveSt 0;
BA_ "GenSigSendType" SG_  162 IBC_ESCoffSt 0;
BA_ "GenSigSendType" SG_  162 IBC_ESCFaultSt 0;
BA_ "GenSigSendType" SG_  162 RollingCounter0A2 0;
BA_ "GenSigSendType" SG_  162 Checksum0A2 0;
BA_ "GenSigSendType" SG_  307 IBC_BrakePedalSt 0;
BA_ "GenSigSendType" SG_  285 IBC_PlungerPressure 0;
BA_ "GenSigSendType" SG_  300 IBC_EPBSwSt 0;
BA_ "GenSigSendType" SG_  300 IBC_EPBSwValiditySt 0;
BA_ "GenSigSendType" SG_  300 IBC_EPBSt 0;
BA_ "GenSigSendType" SG_  300 IBC_EPBErrorSt 0;
BA_ "GenSigSendType" SG_  300 IBC_Slope 0;
BA_ "GenSigSendType" SG_  300 IBC_SlopeSt 0;
BA_ "GenSigSendType" SG_  300 IBC_SlopHighWarn 0;
BA_ "GenSigSendType" SG_  300 RollingCounter31A 0;
BA_ "GenSigSendType" SG_  300 Checksum31A 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedRL 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedRLValid 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedRR 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedRRValid 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedFL 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedFLValid 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedFR 0;
BA_ "GenSigSendType" SG_  257 IBC_wheelSpeedFRValid 0;
BA_ "GenSigSendType" SG_  257 RollingCounter101 0;
BA_ "GenSigSendType" SG_  257 Checksum101 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeFLWSS 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeFRWSS 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeFLWSSValid 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeFRWSSValid 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionFLValid 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionFRValid 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionFLSt 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionFRSt 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeRLWSS 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeRRWSS 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeRLWSSValid 0;
BA_ "GenSigSendType" SG_  370 IBC_SumOfEdgeRRWSSValid 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionRLValid 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionRRValid 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionRLSt 0;
BA_ "GenSigSendType" SG_  370 IBC_WheelDirectionRRSt 0;
BA_ "GenSigSendType" SG_  370 RollingCounter172 0;
BA_ "GenSigSendType" SG_  370 Checksum172 0;
BA_ "GenSigSendType" SG_  323 IBC_LSMCtrlFaultSt 0;
BA_ "GenSigSendType" SG_  323 IBC_APABrakSysLongictlSt 0;
BA_ "GenSigSendType" SG_  323 IBC_APABrakeModeSt 0;
BA_ "GenSigSendType" SG_  323 IBC_APAGearReqActiveSt 0;
BA_ "GenSigSendType" SG_  323 IBC_WHEEL_TRQ_APATReq 0;
BA_ "GenSigSendType" SG_  323 IBC_PDCUCtrlReq 0;
BA_ "GenSigSendType" SG_  323 IBC_VlcInterTargetAx 0;
BA_ "GenSigSendType" SG_  323 IBC_TargetGearReq 0;
BA_ "GenSigSendType" SG_  323 RollingCounter143 0;
BA_ "GenSigSendType" SG_  323 Checksum143 0;
BA_ "GenSigSendType" SG_  252 PDCU_AccelPedalVirtualSt 0;
BA_ "GenSigSendType" SG_  252 PDCU_AccPedalVirtlValid 0;
BA_ "GenSigSendType" SG_  252 PDCU_FrontMotorRpm 0;
BA_ "GenSigSendType" SG_  252 PDCU_FAWhlTqAct 0;
BA_ "GenSigSendType" SG_  252 PDCU_frontMotorRpmValid 0;
BA_ "GenSigSendType" SG_  252 PDCU_FAWhlTqRegenMaxValid 0;
BA_ "GenSigSendType" SG_  252 PDCU_FAWhlTqRegenMax 0;
BA_ "GenSigSendType" SG_  252 PDCU_rearMotRpm 0;
BA_ "GenSigSendType" SG_  252 PDCU_RAWhlTqAct 0;
BA_ "GenSigSendType" SG_  252 PDCU_rearMotRpmValid 0;
BA_ "GenSigSendType" SG_  252 PDCU_RAWhlTqRegenMaxValid 0;
BA_ "GenSigSendType" SG_  252 PDCU_RAWhlTqRegenMax 0;
BA_ "GenSigSendType" SG_  252 RollingCounter0FC 0;
BA_ "GenSigSendType" SG_  252 Checksum0FC 0;
BA_ "GenSigSendType" SG_  456 PDCU_ActualGear 0;
BA_ "GenSigSendType" SG_  456 PDCU_AccelPedalSt 0;
BA_ "GenSigSendType" SG_  456 PDCU_AccelPedalValid 0;
BA_ "GenSigSendType" SG_  456 RollingCounter1C8 0;
BA_ "GenSigSendType" SG_  456 Checksum1C8 0;
BA_ "GenSigSendType" SG_  254 PDCU_DriveReadySt 0;
BA_ "GenSigSendType" SG_  254 PDCU_BMS_SOCCalculate 0;
BA_ "GenSigSendType" SG_  254 RollingCounter0FE 0;
BA_ "GenSigSendType" SG_  254 Checksum0FE 0;
BA_ "GenSigSendType" SG_  255 EMS_PowertrainControlSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_ShiftQuitReason 0;
BA_ "GenSigSendType" SG_  255 PDCU_ShiftAvaibleSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_ACCFunIhibitionReq 0;
BA_ "GenSigSendType" SG_  255 PDCU_DriveAsiSttACCSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_DriveAsiStt_APA 0;
BA_ "GenSigSendType" SG_  255 PDCU_AxleTorqueSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_MaxVehicleWhlTq 0;
BA_ "GenSigSendType" SG_  255 PDCU_TargetDriverTorq 0;
BA_ "GenSigSendType" SG_  255 PDCU_ACCResponseSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_RealizedPowertrainWhlTq 0;
BA_ "GenSigSendType" SG_  255 PDCU_ACCcontrolAvailableSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_WhetherACCReqRealizedSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_MCUF_InverterWorkSt 0;
BA_ "GenSigSendType" SG_  255 PDCU_MCUR_InverterWorkSt 0;
BA_ "GenSigSendType" SG_  255 RollingCounter0FF 0;
BA_ "GenSigSendType" SG_  255 Checksum0FF 0;
BA_ "GenSigSendType" SG_  378 EPS_SteeringTorqueSensorSt 0;
BA_ "GenSigSendType" SG_  378 EPS_SteeringHoldSt 0;
BA_ "GenSigSendType" SG_  378 EPS_SteeringAngleFlag 0;
BA_ "GenSigSendType" SG_  378 EPS_SteeringAngle 0;
BA_ "GenSigSendType" SG_  378 EPS_SteeringTorqueValue 0;
BA_ "GenSigSendType" SG_  378 EPS_RollingCount17A 0;
BA_ "GenSigSendType" SG_  378 EPS_CheckSum17A 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_SteeringAngle 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_SteeringRotSpd 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_TrimmingSt 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_CalibratedSt 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_SASFailureSt 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_RollingCountA5 0;
BA_ "GenSigSendType" SG_  165 TAS_SAS_CheckSumA5 0;
BA_ "GenSigSendType" SG_  176 EPS_APARollingCountB0 0;
BA_ "GenSigSendType" SG_  176 EPS_APAChecksumB0 0;
BA_ "GenSigSendType" SG_  176 EPS_APAProhibitedResaon 0;
BA_ "GenSigSendType" SG_  176 EPS_APAFuncModeSt 0;
BA_ "GenSigSendType" SG_  176 EPS_APASt 0;
BA_ "GenSigSendType" SG_  358 EPS_LKA_DriverOverrideSt 0;
BA_ "GenSigSendType" SG_  358 EPS_LKA_TorqueLimitationSt 0;
BA_ "GenSigSendType" SG_  359 EPS_ESA_DriverOverrideSt 0;
BA_ "GenSigSendType" SG_  359 EPS_DriverInitializedESASt 0;
BA_ "GenSigSendType" SG_  359 EPS_ESA_TorqueLimitationSt 0;
BA_ "GenSigSendType" SG_  501 HAD_CAMERA_ACCSt 0;
BA_ "GenSigSendType" SG_  501 HAD_CAMERA_HMITargetDisp 0;
BA_ "GenSigSendType" SG_  501 HAD_CAMERA_ACCLongitudialDst 0;
BA_ "GenSigSendType" SG_  501 HAD_CAMERA_ACCLateralDst 0;
BA_ "GenSigSendType" SG_  501 HAD_CAMERA_TargetPosSt 0;
BA_ "GenSigSendType" SG_  501 HAD_CAMERA_FDMSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCSetSpdDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCHeadwayIconDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCHeadwayDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCRVVTypeReq 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCStIconDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCMessageRem 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCSetSpeed 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISLCSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_AEBFaultSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_AEBMassDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_AEBAlertReq 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_AEBSwSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ACCdbRem 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ICAStIconDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ICAMessageDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISLCDriverRem 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_HandsoffWarn 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_OperatingSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LDWStDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LKAStDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_VehicleLaneDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LKAPopupMessage 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LDWWarnSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LKAModeSwSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LCAStDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISASignTypeSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISASt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISAPopMessageDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISAValue 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_TSRSignTypeSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LCAPopMessageDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_HBCHighBeamReq 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_IHBCFuctionSt 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_JA_AlertReq 0;
BA_ "GenSigSendType" SG_  1144 FDM_LongitDstDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_FDMWarn 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_FDMStDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_FDMMessageDisp 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_LSSSwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_TSRSwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISASwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISAModeSwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ISASetSpeeddevSwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_FCWSwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_FCWSensitivitySwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_FDMSwst 0;
BA_ "GenSigSendType" SG_  1144 HAD_CAMERA_ELKSwst 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L0LineTypeSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R0LineTypeSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L1LineTypeSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R1LineTypeSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_LineCurveTypeSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L0C0Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R0C0Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L0C1Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_FailSafeLineSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_ICALateralModeSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L0LineQTSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R0LineQTSt 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R0C1Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L0C2Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R0C2Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_L0C3Coef 0;
BA_ "GenSigSendType" SG_  1051 HAD_CAMERA_R0C3Coef 0;
BA_ "GenSigSendType" SG_  1051 MsgCounter41B 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObject01Zone 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObj01Class 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObject02Zone 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObj02Class 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObject03Zone 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObj03Class 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObject04Zone 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObj04Class 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObject05Zone 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObj05Class 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObject06Zone 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_FrontObj06Class 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_LRObject 0;
BA_ "GenSigSendType" SG_  1120 HAD_CAMERA_RRObject 0;
BA_ "GenSigSendType" SG_  1120 RollingCounter460 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ1LatDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ1LgtDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ1Class 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ1ID 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ1Lane 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ1LatPct 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ2LatDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ2LgtDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ2Class 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ2ID 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ2Lane 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ2LatPct 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ3LatDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ3LgtDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ3Class 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ3ID 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ3Lane 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ3LatPct 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ4LatDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ4LgtDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ4Class 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ4ID 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ4Lane 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ4LatPct 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ5LatDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ5LgtDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ5Class 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ5ID 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ5Lane 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ5LatPct 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ6LatDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ6LgtDst 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ6Class 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ6ID 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ6Lane 0;
BA_ "GenSigSendType" SG_  1136 HAD_CAMERA_OBJ6LatPct 0;
BA_ "GenSigSendType" SG_  1136 OBJ1_MsgCount 0;
BA_ "GenSigSendType" SG_  1137 HAD_P2P_OBJ9LatDst 0;
BA_ "GenSigSendType" SG_  1137 HAD_P2P_OBJ9LgtDst 0;
BA_ "GenSigSendType" SG_  1137 HAD_P2P_OBJ09Class 0;
BA_ "GenSigSendType" SG_  1137 HAD_P2P_OBJ09ID 0;
BA_ "GenSigSendType" SG_  1137 HAD_P2P_OBJ09Lane 0;
BA_ "GenSigSendType" SG_  1137 HAD_P2P_OBJ09LatPct 0;
BA_ "GenSigSendType" SG_  1137 OBJ2_MsgCount 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ01LatSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ01LgtSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ01LatAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ01LgtAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ01MoveClass 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ02LatSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ02LgtSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ02LatAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ02LgtAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ02MoveClass 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ03LatSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ03LgtSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ03LatAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ03LgtAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ03MoveClass 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ04LatSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ04LgtSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ04LatAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ04LgtAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ04MoveClass 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ05LatSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ05LgtSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ05LatAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ05LgtAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ05MoveClass 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ06LatSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ06LgtSpeed 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ06LatAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ06LgtAccr 0;
BA_ "GenSigSendType" SG_  1140 HAD_CAMERA_OBJ06MoveClass 0;
BA_ "GenSigSendType" SG_  1140 HAD_P2P_OBJva1_MsgCount 0;
BA_ "GenSigSendType" SG_  1143 AEB_ObjY 0;
BA_ "GenSigSendType" SG_  1143 AEB_ObjX 0;
BA_ "GenSigSendType" SG_  1143 AEB_ObjVy 0;
BA_ "GenSigSendType" SG_  1143 AEB_ObjVx 0;
BA_ "GenSigSendType" SG_  1143 AEB_ObjTyp 0;
BA_ "GenSigSendType" SG_  1143 AEB_ObjMoveClass 0;
BA_ "GenSigSendType" SG_  858 HAD_APA_AutomaticParkingPodeSt 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_TurnLightsCommandReq 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_LightControlValid 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_OnOffDisp 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_MPAPArkNoticeDisp 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_UPAErrorDisp 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_MPAStFeedbackSt 2;
BA_ "GenSigSendType" SG_  858 HAD_APA_MPAReadyFbSt 2;
BA_ "GenSigSendType" SG_  858 RollingCounter35A 2;
BA_ "GenSigSendType" SG_  858 CheckSum35A 2;
BA_ "GenSigSendType" SG_  352 HAD_APA_LongitudinalCtrlReq 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_SystemSt 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_FailureBrakeModeSt 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_ReqtTargetGearPosSt 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_ReqToStopDst 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_EmergencySt 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_Emergency_Valid 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_ESC_Funmode 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_BrakeTargetMaxSpeed 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_ReqEPSTargetAngleReq 0;
BA_ "GenSigSendType" SG_  352 HAD_APA_ControlEPSReq 0;
BA_ "GenSigSendType" SG_  352 RollingCounter160 0;
BA_ "GenSigSendType" SG_  352 CheckSum160 0;
BA_ "GenSigSendType" SG_  352 HAD_EPS_FuncModeReq 0;
BA_ "GenSigSendType" SG_  1201 HAD_APA_AVMDispViewSt 2;
BA_ "GenSigSendType" SG_  1201 HAD_APA_AVMLogSt 2;
BA_ "GenSigSendType" SG_  1201 HAD_APA_AVMFaultRem 2;
BA_ "GenSigSendType" SG_  859 HAD_SRR_LF_FCTAWorkingSt 0;
BA_ "GenSigSendType" SG_  859 HAD_SRR_LF_FCTBWorkingSt 0;
BA_ "GenSigSendType" SG_  859 HAD_SRR_LF_AutoCaliResultSt 0;
BA_ "GenSigSendType" SG_  859 HAD_SRR_LF_ErrorSt 0;
BA_ "GenSigSendType" SG_  859 HAD_SRR_LF_FCTAWarn 0;
BA_ "GenSigSendType" SG_  859 HAD_SRR_RF_FCTAWarn 0;
BA_ "GenSigSendType" SG_  859 HAD_FCTBVaild 0;
BA_ "GenSigSendType" SG_  859 HAD_FCTBCollisionDirectionSt 0;
BA_ "GenSigSendType" SG_  859 HAD_FCTBActionSt 0;
BA_ "GenSigSendType" SG_  859 HAD_FCTBDeceleration 0;
BA_ "GenSigSendType" SG_  859 RollingCount35B 0;
BA_ "GenSigSendType" SG_  859 CheckSum35B 0;
BA_ "GenSigSendType" SG_  782 HAD_APA_ADASLeftLEDWarnSt 2;
BA_ "GenSigSendType" SG_  782 HAD_APA_ADASRighrLEDWarnSt 2;
BA_ "GenSigSendType" SG_  942 RollingCounter3AE 0;
BA_ "GenSigSendType" SG_  942 CheckSum3AE 0;
BA_ "GenSigSendType" SG_  570 VIUL_TrunkSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_LetfligthSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_RightligthSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_PosLampSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_HazardLampSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_HighBeamSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_LowBeamSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_LFDoorSwSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_RFDoorSwSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_RRDoorSwSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_LRDoorSwSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_LeftTurnSwSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_RightTurnSwrSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_FrontWiperWorkSt 0;
BA_ "GenSigSendType" SG_  570 VIUL_HazardSwrSt 0;
BA_ "GenSigSendType" SG_  64 VIUL_PEPS_RKECommand 1;
BA_ "GenSigSendType" SG_  64 VIUL_PEPS_DoorLockReq 1;
BA_ "GenSigSendType" SG_  802 VIUR_AC_AmbTempSt 0;
BA_ "GenSigSendType" SG_  281 ACU_YawRateSt 0;
BA_ "GenSigSendType" SG_  281 ACU_YawrateValiditySt 0;
BA_ "GenSigSendType" SG_  281 ACU_LateralAccelarationSt 0;
BA_ "GenSigSendType" SG_  281 ACU_LateralAccelareValid 0;
BA_ "GenSigSendType" SG_  281 ACU_LongitdAcclerValid 0;
BA_ "GenSigSendType" SG_  281 ACU_LongitudAccelerationSt 0;
BA_ "GenSigSendType" SG_  281 RollingCounter119 0;
BA_ "GenSigSendType" SG_  281 CheckSum119 0;
VAL_ 828 VIUL_IgnitionSt 0 "OFF" 1 "ACC" 2 "ON" 3 "START";
VAL_ 828 VIUL_DoorLockSt 0 "1";
VAL_ 828 VIUL_CHMSLSt 0 "OFF" 1 "ON" 2 "3";
VAL_ 828 VIUL_BrakeLampSt 0 "OFF" 1 "ON" 2 "3";
VAL_ 300 IBC_EPBSwSt 0 "NotActive" 1 "Release" 2 "Apply";
VAL_ 300 IBC_EPBSwValiditySt 0 "Valid" 1 "Invalid";
VAL_ 323 IBC_PDCUCtrlReq 0 "Inactive" 1 "Active";
VAL_ 254 PDCU_DriveReadySt 0 "1";
VAL_ 255 EMS_PowertrainControlSt 0 "Unvalaible" 1 "Waiting" 2 "Active" 3 "Inactive";
VAL_ 255 PDCU_ShiftQuitReason 0 "1" 2 "3";
VAL_ 255 PDCU_ShiftAvaibleSt 0 " False" 1 " True";
VAL_ 255 PDCU_DriveAsiSttACCSt 0 "Unavailable" 1 "Waiting" 2 "Active" 3 "Emergency";
VAL_ 255 PDCU_DriveAsiStt_APA 0 "Unavailable" 1 "Waiting" 2 "Active" 3 "Emergency";
VAL_ 255 PDCU_AxleTorqueSt 0 "valid" 1 "invalid";
VAL_ 378 EPS_SteeringTorqueSensorSt 0 "1";
VAL_ 378 EPS_SteeringHoldSt 0 " no steering activity detected from the driver torque" 1 " steering activity detected from the driver torque";
VAL_ 378 EPS_SteeringAngleFlag 0 "Invalid" 1 "Valid";
VAL_ 165 TAS_SAS_CalibratedSt 0 "未标定（sensor not calibrated）" 1 "已标定（sensor calibrated）";
VAL_ 165 TAS_SAS_SASFailureSt 1 " 无故障（Sensor Information valid）" 0 " 故障（sensor information invalid, an internal sensor fault occurred）";
VAL_ 176 EPS_APAFuncModeSt 0 "Function Not Active " 1 "APA Mode" 2 "LAPA Mode " 3 "Invalid";
VAL_ 176 EPS_APASt 0 "Unavailable不可用" 1 "Standby" 2 "control in progress" 3 "Abort";
VAL_ 358 EPS_LKA_DriverOverrideSt 0 " No override" 1 " Override";
VAL_ 358 EPS_LKA_TorqueLimitationSt 0 " no saturation" 1 " Saturation effective";
VAL_ 359 EPS_ESA_DriverOverrideSt 0 " No override" 1 " Override";
VAL_ 359 EPS_DriverInitializedESASt 0 " Not active" 1 " Active";
VAL_ 359 EPS_ESA_TorqueLimitationSt 0 " no saturation" 1 " Saturation effective";
VAL_ 501 HAD_CAMERA_HMITargetDisp 0 "Off" 1 "On";
VAL_ 1144 HAD_CAMERA_ACCHeadwayIconDisp 0 "Off" 1 "On";
VAL_ 1144 HAD_CAMERA_AEBFaultSt 0 "No_Fault" 1 "FCW_Fault" 2 "AEB_Fault" 3 "AEB_FCW_Fault";
VAL_ 1144 HAD_CAMERA_LKAModeSwSt 0 "1" 2 "3";
VAL_ 1144 HAD_CAMERA_HBCHighBeamReq 0 "High beam OFF" 1 "High beam ON";
VAL_ 1144 HAD_CAMERA_IHBCFuctionSt 0 "OFF" 1 "ON" 2 "Fault" 3 "Reserved";
VAL_ 1144 HAD_CAMERA_JA_AlertReq 0 " No Req" 1 " JA_Alert_Req";
VAL_ 1144 HAD_CAMERA_FCWSwst 0 "1" 2 "3";
VAL_ 1144 HAD_CAMERA_ELKSwst 0 "1" 2 "3";
VAL_ 1051 HAD_CAMERA_ICALateralModeSt 0 " 无横向控制" 1 " 跟线控制" 2 " 跟车控制" 3 " 预留";
VAL_ 1051 HAD_CAMERA_L0LineQTSt 0 "无" 1 "低" 2 "中" 3 "高";
VAL_ 1051 HAD_CAMERA_R0LineQTSt 0 "无" 1 "低" 2 "中" 3 "高";
VAL_ 1136 HAD_CAMERA_OBJ1Lane 0 "1" 2 "3";
VAL_ 1136 HAD_CAMERA_OBJ2Lane 0 "1" 2 "3";
VAL_ 1136 HAD_CAMERA_OBJ3Lane 0 "1" 2 "3";
VAL_ 1136 HAD_CAMERA_OBJ4Lane 0 "1" 2 "3";
VAL_ 1136 HAD_CAMERA_OBJ5Lane 0 "1" 2 "3";
VAL_ 1136 HAD_CAMERA_OBJ6Lane 0 "1" 2 "3";
VAL_ 1137 HAD_P2P_OBJ09Lane 0 "1" 2 "3";
VAL_ 1143 AEB_ObjMoveClass 0 "1" 2 "3";
VAL_ 858 HAD_APA_LightControlValid 0 " 无效" 1 " 有效";
VAL_ 858 HAD_APA_OnOffDisp 0 "OFF" 1 "ON";
VAL_ 858 HAD_APA_UPAErrorDisp 0 "1" 2 "3";
VAL_ 858 HAD_APA_MPAStFeedbackSt 0 "Init" 1 "Standby" 2 "Fault" 3 "4" 5 "6" 7 "8" 9 "10" 11 "12" 13 "14" 15 "16" 17 "18" 19 "20" 21 "22" 23 "24" 31 "reserved";
VAL_ 352 HAD_APA_SystemSt 0 "Init" 1 "Standby" 2 "Active" 3 "ManeuverFinished" 4 "Suspend" 5 "Abort" 6 "Failure" 7 "Reserved";
VAL_ 352 HAD_APA_FailureBrakeModeSt 0 " Idle (No Braking) 不请求" 1 " Comfortable 缓慢刹车1（APA发送电刹+机械刹车）" 2 " Comfortable2 缓慢刹车2（LAPA发送-仅电刹车）" 3 " Emergency 急刹（APA/LAPA发送电刹+机械刹车）";
VAL_ 352 HAD_APA_EmergencySt 0 "ESC" 1 "ESC";
VAL_ 352 HAD_APA_Emergency_Valid 0 "1";
VAL_ 352 HAD_APA_ControlEPSReq 0 " No request " 1 " reserve " 2 " control request " 3 "  Invalid ";
VAL_ 352 HAD_EPS_FuncModeReq 0 "Function Not Active" 1 "APA Mode" 2 "LAPA Mode" 3 "reserve";
VAL_ 859 HAD_SRR_LF_FCTAWorkingSt 0 "default" 1 "OFF" 2 "ON" 3 "Reserved";
VAL_ 859 HAD_SRR_LF_FCTBWorkingSt 0 "default" 1 "OFF" 2 "ON" 3 "Reserved";
VAL_ 859 HAD_SRR_LF_AutoCaliResultSt 0 "1" 2 "3" 4 "5" 6 "7";
VAL_ 859 HAD_SRR_LF_FCTAWarn 0 "Inactive" 1 "Active";
VAL_ 859 HAD_SRR_RF_FCTAWarn 0 "Inactive" 1 "Active";
VAL_ 859 HAD_FCTBVaild 0 "1";
VAL_ 859 HAD_FCTBCollisionDirectionSt 0 "1" 2 "3";
VAL_ 859 HAD_FCTBActionSt 0 "inactive" 1 "active";
VAL_ 570 VIUL_TrunkSt 0 " 关闭" 1 "开启";
VAL_ 570 VIUL_LetfligthSt 0 " 灭" 1 " 闪";
VAL_ 570 VIUL_RightligthSt 0 " 灭" 1 " 闪";
VAL_ 570 VIUL_PosLampSt 0 " 灭" 1 " 亮";
VAL_ 570 VIUL_HazardLampSt 0 " 灭" 1 " 亮";
VAL_ 570 VIUL_HighBeamSt 0 " 灭" 1 " 亮";
VAL_ 570 VIUL_LowBeamSt 0 " 灭" 1 " 亮";
VAL_ 570 VIUL_LFDoorSwSt 0 " 关闭" 1 " 开启";
VAL_ 570 VIUL_RFDoorSwSt 0 " 关闭" 1 " 开启";
VAL_ 570 VIUL_RRDoorSwSt 0 " 关闭" 1 " 开启";
VAL_ 570 VIUL_LRDoorSwSt 0 " 关闭" 1 " 开启";
VAL_ 570 VIUL_LeftTurnSwSt 0 "OFF" 1 "ON" 2 "3";
VAL_ 570 VIUL_RightTurnSwrSt 0 "OFF" 1 "ON" 2 "3";
VAL_ 570 VIUL_FrontWiperWorkSt 0 "1" 2 "3";
VAL_ 570 VIUL_HazardSwrSt 0 " Not Active" 1 " Active";
VAL_ 281 ACU_YawrateValiditySt 0 "1";
VAL_ 281 ACU_LateralAccelareValid 0 "1";
VAL_ 281 ACU_LongitdAcclerValid 0 "1";
import cantools
import can
from socketcan import CanRawSocket, CanFrame, CanFdFrame
import time

interface = "can0"
can_socket = CanRawSocket(interface=interface)

blf_file = r'test3.blf'
dbc_file = r'CAN0_C385_V2.2.3_20220720.dbc'

# Load the .dbc file 
db = cantools.database.load_file(dbc_file)
decoded_msg = []

# Open the .blf file and create a log reader
with can.BLFReader(blf_file) as can_log:
    for msg in can_log:
        print(msg.arbitration_id)
        print(msg.data)
        # can_msg = can.Message(arbitration_id=msg.arbitration_id, data=msg.data, is_extended_id=False)
        can_frame = CanFdFrame(can_id=msg.arbitration_id, data=bytes(msg.data))
            
        can_socket.send(can_frame)
        time.sleep(0.1)
        #decoded_msg.append(
        #    db.decode_message(msg.arbitration_id, msg.data)
        #)
#print(decoded_msg)

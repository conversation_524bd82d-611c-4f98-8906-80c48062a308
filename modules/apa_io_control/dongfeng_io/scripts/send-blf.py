import can
from can.io.blf import B<PERSON><PERSON>eader
import cantools
import time

blf_file0 = "test3.blf"
blf_file1 = "TSMaster1_20230320.blf"
can_file0 = "CAN0_C385_V2.2.3_20220720.dbc"
can_file1 = "can0_c385_apa_uss_20230131.dbc"

db = cantools.database.load_file(can_file0)
bus0 = can.Bus(interface='socketcan',
              channel='can0',
              receive_own_messages=True, fd=True)

cnt = 0
invalid_cnt = 0
with BLFReader(blf_file1) as can_log:
    for blf_msg in can_log:
        # if cnt > 10000:
        #     break
        msg = can.Message(arbitration_id=blf_msg.arbitration_id,
                          data=blf_msg.data,
                          is_extended_id=blf_msg.is_extended_id,
                          is_fd=blf_msg.is_fd
                          )
        bus0.send(msg, timeout=0.2)
        try:
            msg = db.decode_message(blf_msg.arbitration_id, blf_msg.data)
            if cnt % 1000 == 0:
                print(f"{blf_msg.arbitration_id}:{msg}")
        except:
            # if cnt % 1000 == 0:
            print(f"invalid id: {blf_msg.arbitration_id}")
            invalid_cnt += 1
        time.sleep(0.001) # sleep 1ms
        cnt += 1
        
        
print(f"total cnt:{cnt}, invalid cnt:{invalid_cnt}")
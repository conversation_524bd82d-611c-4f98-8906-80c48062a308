  j_extension_2b9["CDC_2B9/APA_FunctionOnOffSts"] = cdc_2b9.APA_FunctionOnOffSts;
  j_extension_2b9["CDC_2B9/APA_Condition_Notice"] = cdc_2b9.APA_Condition_Notice;
  j_extension_2b9["CDC_2B9/APA_TouchInfOnOffRes"] = cdc_2b9.APA_TouchInfOnOffRes;
  j_extension_2b9["CDC_2B9/APA_AVP_Notice"] = cdc_2b9.APA_AVP_Notice;
  j_extension_2b9["CDC_2B9/APA_HZP_Notice"] = cdc_2b9.APA_HZP_Notice;
  j_extension_2b9["CDC_2B9/APA_ViewActual"] = cdc_2b9.APA_ViewActual;
  j_extension_2b9["CDC_2B9/APA_Summon_Notice"] = cdc_2b9.APA_Summon_Notice;
  j_extension_2b9["CDC_2B9/APA_ActivationSts"] = cdc_2b9.APA_ActivationSts;
  j_extension_2b9["CDC_2B9/APA_ReadySts"] = cdc_2b9.APA_ReadySts;
  j_extension_2b9["CDC_2B9/APA_ParkingSlot_ExtraFeature"] = cdc_2b9.APA_ParkingSlot_ExtraFeature;
  j_extension_2b9["CDC_2B9/APA_ParkingSlot_Type"] = cdc_2b9.APA_ParkingSlot_Type;
  j_extension_2b9["CDC_2B9/APA_TurnOnMode"] = cdc_2b9.APA_TurnOnMode;
    j_extension_601["CDC_601/VCU_Distance_CMD"] = cdc_601.VCU_Distance_CMD;
    
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM9"] = cdc_601.VCU_ProbeCMD_NUM9;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM10"] = cdc_601.VCU_ProbeCMD_NUM10;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM11"] = cdc_601.VCU_ProbeCMD_NUM11;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM12"] = cdc_601.VCU_ProbeCMD_NUM12;
  j_extension_601["CDC_601/VCU_Machine_NUM"] = cdc_601.VCU_Machine_NUM;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM1"] = cdc_601.VCU_ProbeCMD_NUM1;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM2"] = cdc_601.VCU_ProbeCMD_NUM2;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM3"] = cdc_601.VCU_ProbeCMD_NUM3;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM4"] = cdc_601.VCU_ProbeCMD_NUM4;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM5"] = cdc_601.VCU_ProbeCMD_NUM5;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM6"] = cdc_601.VCU_ProbeCMD_NUM6;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM7"] = cdc_601.VCU_ProbeCMD_NUM7;
  j_extension_601["CDC_601/VCU_ProbeCMD_NUM8"] = cdc_601.VCU_ProbeCMD_NUM8;
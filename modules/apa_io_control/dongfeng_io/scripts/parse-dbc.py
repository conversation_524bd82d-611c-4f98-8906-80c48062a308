import cantools

def print_dbc_content(file_name:str):
    print(f"-------------------------{file_name}-------------------------")
    db = cantools.database.load_file('can0_c385_apa_uss_20230131.dbc')
    signal_cnt = 0
    for msg in db.messages:
        print(f"{msg}: {len(msg.signals)}")
        # print(msg.signals)
        signal_cnt += len(msg.signals)
    print(f"msg cnt: {len(db.messages)}, signal cnt: {signal_cnt}")

can_file0 = "can0_c385_apa_uss_20230131.dbc"
can_file1 = "CAN0_C385_V2.2.3_20220720.dbc"

print_dbc_content(can_file1)
print_dbc_content(can_file0)
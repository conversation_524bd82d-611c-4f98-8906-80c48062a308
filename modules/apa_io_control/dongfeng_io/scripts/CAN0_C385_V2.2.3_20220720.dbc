VERSION "HIPBNYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY/4/%%%/4/'%**4YYY///"


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: GW ADC INS VCU CDC USS MCU APA LVSM TBOX ICMSG


BO_ 383 INS_17F: 64 INS
 SG_ INS_GPS_Time : 23|32@0+ (1,0) [0|650000000] "ms"  INS,CDC
 SG_ FL_wheel_vel_for_IPC : 151|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_Current_Pos_Heading : 167|20@0+ (0.001,0) [0|360] "°"  INS,CDC
 SG_ INS_Current_Pos_Heading_Accuracy : 191|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ L_wheel_factor : 247|16@0+ (0.0001,0) [0|1] "m/m"  INS,CDC
 SG_ R_wheel_factor : 263|16@0+ (0.0001,0) [0|1] "m/m"  INS,CDC
 SG_ INS_Current_Pos_Pitch_Accuracy : 279|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Roll_Accuracy : 295|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Pitch : 311|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Current_Pos_Roll : 323|20@0+ (0.001,-90) [-90|90] "°"  INS,CDC
 SG_ FR_wheel_vel_for_IPC : 351|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ RL_wheel_vel_for_IPC : 367|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ RR_wheel_vel_for_IPC : 383|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ INS_Current_Pos_Pitch_Confidence : 395|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Heading_Confiden : 399|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Roll_Confidence : 407|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_TiStamp : 423|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_TiOut : 457|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 458|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 459|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_RollingCounter_17F : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_17F : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 326 INS_146: 64 INS
 SG_ INS_Current_Pos_X_Accel : 7|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_Y_Accel : 19|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Pos_X_Accel_Bias : 47|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Pos_Y_Accel_Bias : 63|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_Z_Accel : 79|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Pos_Z_Accel_Bias : 91|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_X_Rate : 107|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_X_Rate_Bias : 135|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Current_Pos_Y_Rate : 151|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_Y_Rate_Bias : 163|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Current_Pos_Z_Rate : 179|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_Z_Rate_Bias : 207|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Quaternion_X : 223|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_Y : 247|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_Z : 271|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_W : 295|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_IMU_Time : 319|32@0+ (1,0) [0|4294967295] "ms"  INS,CDC
 SG_ INS_TiOut : 348|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 349|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 350|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_IMU_Valid : 351|1@0+ (1,0) [0|1] "ENUM"  INS,CDC
 SG_ INS_Roll_Mis_Angle_to_Veh : 359|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Pitch_Mis_Angle_to_Veh : 371|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Yaw_Mis_Angle_to_Veh : 399|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_TiStamp : 411|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_RollingCounter_146 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_146 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 1459 INS_5B3: 64 INS
 SG_ INS_Wheel_Scale_factor : 7|20@0+ (1E-006,-0.5) [-0.5|0.5] "/"  INS,CDC

BO_ 353 VCU_161: 64 VCU
 SG_ VCUShiftPostionValid : 2|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VCUAPARequestEnable : 4|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VCUAPAdriverInterruption : 6|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VCUAccPedShield : 7|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuAccrPedlPosn : 14|10@0+ (0.1,0) [0|102.3] "%"  VCU,APA,ADC
 SG_ VcuCalcnAccrPedlPosn : 18|10@0+ (0.1,0) [0|102.3] "%"  VCU,APA,CDC
 SG_ VcuAPATorqRequestAvailable : 19|1@0+ (1,0) [0|1] "/"  VCU,APA,CDC
 SG_ VcuAccrPedlPosnVld : 20|1@0+ (1,0) [0|1] ""  VCU,APA,ADC
 SG_ VcuCalcnAccrPedlPosnVld : 24|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuEnyRecyclMod : 37|7@0+ (1,0) [0|127] ""  VCU,APA,CDC
 SG_ VcuComFltSts : 39|2@0+ (1,0) [0|3] "/"  VCU,APA,CDC
 SG_ VcuSimnEpbSwtSts : 45|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VcuSimnEpbSwtStsVld : 46|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuGearPosn : 65|3@0+ (1,0) [0|7] ""  VCU,APA,CDC,INS,LVSM
 SG_ VcuPtTqReqAvl : 74|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VcuPtTqRealVld : 75|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMinVld : 76|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMaxVld : 77|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuOvrdModReq : 78|1@0+ (1,0) [0|1] ""  VCU,APA,ADC
 SG_ VcuPtTqLimMax : 87|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuPtTqLimMin : 103|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuShiftLvlPosn : 119|4@0+ (1,0) [0|15] "/"  VCU,APA,CDC
 SG_ VcuPtTqReal : 135|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuVehWhlReqTqVld : 149|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuVehGearPosnVld : 150|1@0+ (1,0) [0|1] ""  VCU,APA,CDC,INS,LVSM
 SG_ VcuRdySts : 151|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuVehWhlReqTq : 159|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ GW_ADSSecOCVerifyFailureFlag : 204|1@0+ (1,0) [0|1] "/"  VCU,ADC
 SG_ VcuCycCntr161 : 491|4@0+ (1,0) [0|15] ""  VCU,APA,CDC,INS
 SG_ VcuCrcChk161 : 503|16@0+ (1,0) [0|65535] ""  VCU,APA,CDC,INS

BO_ 583 CDC_247: 32 CDC
 SG_ APA_SteeringAngleReqProtection : 1|2@0+ (1,0) [0|3] "/"  CDC,APA,GW,TBOX
 SG_ APA_ErrorStatus : 2|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_indication : 5|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ APA_APAOnOff : 6|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_EmergenceBrake : 7|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_SteeringAngleReq : 15|16@0- (0.1,0) [-780|780] "degree"  CDC,APA,GW,TBOX
 SG_ APA_RemoteOnOff : 25|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_ButtonPress : 26|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_IncreasePressureReq : 27|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_TurnLightsCommand : 29|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_ParkNotice_4 : 32|6@0+ (1,0) [0|63] "/"  CDC,APA,TBOX
 SG_ APA_ParkNotice : 39|6@0+ (1,0) [0|63] "/"  CDC,APA,TBOX
 SG_ APA_ParkingPercentage : 42|7@0+ (1,0) [0|100] "%"  CDC,APA,TBOX
 SG_ APA_RollingCounter_264 : 51|4@0+ (1,0) [0|15] "/"  CDC,APA
 SG_ APA_CRCCheck_264 : 63|8@0+ (1,0) [0|255] "/"  CDC,APA
 SG_ APA_EPBrequest : 65|2@0+ (1,0) [0|3] "/"  CDC,APA,GW,TBOX
 SG_ APA_EPBrequestValid : 66|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_TargetAccelerationValid : 67|1@0+ (1,0) [0|1] "NA"  CDC,APA,GW,TBOX
 SG_ APA_TransPRNDShiftRequest : 70|3@0+ (1,0) [0|7] "/"  CDC,APA,VCU,TBOX
 SG_ APA_TransPRNDShiftReqValid : 71|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_TargetAcceleration : 79|8@0+ (0.05,-5) [-5|7.75] "m/s2"  CDC,APA,GW,TBOX
 SG_ APA_EngTorqReq : 87|10@0+ (0.097847,0) [0|99.999634] "%"  CDC,APA,TBOX
 SG_ APA_Activation_Status : 89|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ APA_TransPRNDShiftEnable : 90|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_LSCAction : 91|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX,VCU
 SG_ APA_HSAHDforbidden : 92|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_EngineTrqReqEnable : 93|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_AccPedShieldReq : 97|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_ESPDecompressionModel : 98|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_PrefillReq : 104|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_DynamicSlotWarning : 105|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_SlotNotice : 109|4@0+ (1,0) [0|15] "/"  CDC,APA,TBOX
 SG_ APA_TCUClutchCombinationReq : 110|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_TrqHoldForTCUCl : 111|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_RollingCounter_26C : 115|4@0+ (1,0) [0|15] ""  CDC,APA,VCU
 SG_ APA_CRCCheck_26C : 127|8@0+ (1,0) [0|15] "/"  CDC,APA,VCU
 SG_ APA_PtTorqReq : 135|16@0+ (1,-32768) [-32768|32767] "Nm"  CDC,APA,TBOX,VCU,GW
 SG_ APA_ESPDistToStop : 147|12@0+ (1,0) [0|4095] ""  CDC,APA,GW,TBOX
 SG_ APA_ESP_BrakeFunctionMode : 150|3@0+ (1,0) [0|7] ""  CDC,APA,GW,TBOX
 SG_ APA_PtTrqReqValid : 151|1@0+ (1,0) [0|1] ""  CDC,APA,GW,VCU,TBOX
 SG_ APA_VCUReadyReq : 166|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU
 SG_ APA_ESP_StandstillRequest : 167|1@0+ (1,0) [0|1] ""  CDC,APA,GW,TBOX
 SG_ APA_RollingCounter_236 : 179|4@0+ (1,0) [0|15] ""  CDC,APA,VCU
 SG_ APA_CRC_Checksum_236 : 191|8@0+ (1,0) [0|255] ""  CDC,APA,VCU
 SG_ APA_RollingCounter_247 : 235|4@0+ (1,0) [0|15] "/"  CDC,APA,TBOX,GW,VCU
 SG_ APA_CRCCheck_247 : 247|16@0+ (1,0) [0|65535] "/"  CDC,APA,TBOX,GW,VCU

BO_ 710 CDC_2C6: 64 CDC
 SG_ APA_ParkNotice_5 : 70|7@0+ (1,0) [0|127] "/"  CDC,APA,TBOX
 SG_ APA_LAEBReq : 71|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_LAEBStatus : 78|3@0+ (1,0) [0|7] "/"  CDC,APA,VCU,TBOX
 SG_ APA_BLEConnectionRemind : 79|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_LAEBNotice : 82|3@0+ (1,0) [0|7] "/"  CDC,APA,TBOX
 SG_ APA_RemoteParkingUsingRemind : 83|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_ASPAvailableStatus : 84|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_ASPStatus : 86|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_CrossModeSelectReq : 87|1@0+ (1,0) [0|1] "/"  CDC,APA,TBOX
 SG_ APA_BCMHornCommand : 97|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_vehicleFrontdetect : 99|2@0+ (1,0) [0|3] "/"  CDC,APA,TBOX
 SG_ APA_ReleasePressureReq : 100|1@0+ (1,0) [0|1] "/"  CDC,APA,GW,TBOX
 SG_ APA_PEPS_EngineOffLockRequest : 101|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_RADSNotice : 108|6@0+ (1,0) [0|63] "/"  CDC,APA,TBOX
 SG_ APA_RollingCounter_2D4 : 115|4@0+ (1,0) [0|15] "/"  CDC,APA,VCU
 SG_ APA_PEPS_EngineOffRequest : 116|1@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX
 SG_ APA_CRCCheck_2D4 : 127|8@0+ (1,0) [0|255] "/"  CDC,APA,VCU
 SG_ APA_RollingCounter_2C6 : 491|4@0+ (1,0) [0|15] "/"  CDC,APA,VCU,GW
 SG_ APA_CRCCheck_2C6 : 503|16@0+ (1,0) [0|65535] "/"  CDC,APA,VCU,GW

BO_ 1760 CDC_6E0: 8 CDC
 SG_ APA_AuthenticationStatus : 7|64@0+ (1,0) [0|1] "/"  CDC,APA,VCU,TBOX

BO_ 798 CDC_31E: 64 CDC
 SG_ APA_SystemFailureFlag : 284|5@0+ (1,0) [0|31] "/"  CDC,APA,TBOX

BO_ 523 GW_20B: 64 GW
 SG_ WhlSpdRiFrntData : 4|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdRiFrntDir : 6|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiFrntDataVld : 7|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdLeFrntData : 20|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdLeFrntDir : 22|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdLeFrntDataVld : 23|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ IBCU_APCActiveStatus : 33|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiReData : 68|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdRiReDir : 70|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiReDataVld : 71|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdLeReData : 84|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdLeReDir : 86|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdLeReDataVld : 87|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ ESP_APA_DriverOverride : 119|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ WhlSpdFrntLePls : 215|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdFrntRiPls : 223|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdReRiPls : 231|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdReLePls : 239|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ ESP_PrefillAvailable : 259|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_PrefillActive : 260|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_BrakeForce : 263|1@0+ (1,0) [0|1] "/"  GW,APA,ADC
 SG_ IBCU_APCReducedFuncAvail : 276|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ IBCU_APCFullFuncAvail : 281|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ IbcuCycCntr20B : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC,INS
 SG_ IBCU_ADCRampOffSuspendState : 493|2@0+ (1,0) [0|3] ""  GW,CDC
 SG_ IbcuCrcChk20B : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC,INS

BO_ 378 GW_17A: 64 GW
 SG_ EspVehSpd : 36|13@0+ (0.05625,0) [0|460.74375] "km/h"  GW,APA,CDC,INS,LVSM
 SG_ EspVehSpdVld : 37|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS,LVSM
 SG_ EPB_APArequest_Available : 64|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPB_AchievedClampForce_Primary : 69|5@0+ (1,0) [0|31] "KN"  GW,APA,CDC
 SG_ EPB_AchievedClampForce : 85|5@0+ (1,0) [0|31] "KN"  GW,APA,CDC
 SG_ EPB_FailStatuss_Primary : 87|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ ESP_RollingCounter_17A : 491|4@0+ (1,0) [0|15] "/"  GW,APA,CDC,LVSM,INS
 SG_ ESP_CRCCheck_17A : 503|16@0+ (1,0) [0|65535] "/"  GW,APA,CDC,LVSM,INS

BO_ 962 GW_3C2: 64 GW
 SG_ EpbFailrSts : 1|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ EpbSts : 7|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EspEpbReqAvl : 28|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EpbStsPrimary : 79|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EspCycCntr3C2 : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ EspCrcChk3C2 : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC

BO_ 706 GW_2C2: 64 GW
 SG_ IbBrkPedlStsGb : 7|8@0+ (1,0) [0|255] "%"  GW,APA,CDC
 SG_ IbBrkPedlStsGbVld : 15|2@0+ (1,0) [0|2] ""  GW,APA,CDC
 SG_ IBCU_PFSBrakePressure : 159|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ IBCU_PlungerBrakePressure : 175|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ IbCycCntr2C2 : 491|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ IbCrcChk2C2 : 503|16@0+ (1,0) [0|65535] ""  GW,APA,CDC

BO_ 591 GW_24F: 8 GW
 SG_ EPS_ElectPowerConsumption : 7|8@0+ (0.5,0) [0|127] "A"  GW,APA,CDC
 SG_ EPS_APA_EpasFAILED : 9|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_APA_Abortfeedback : 14|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_SteeringTorque : 23|8@0+ (0.1794,-22.78) [-22.78|22.78] "Nm"  GW,APA,ADC
 SG_ EPS_IACC_abortreason : 28|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_APA_ControlFeedback : 29|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_ADASActiveMode : 33|3@0+ (1,0) [0|7] "/"  GW,CDC
 SG_ EPS_ADS_ControlFeedback : 36|3@0+ (1,0) [0|7] "/"  GW,CDC
 SG_ EPS_SystemSt : 46|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_RollingCounter_24F : 51|4@0+ (1,0) [0|15] "/"  GW,CDC,APA
 SG_ EPS_ConcussAvailabilityStatus : 53|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_CRCCheck_24F : 63|8@0+ (1,0) [0|255] "/"  GW,CDC,APA

BO_ 382 GW_17E: 8 GW
 SG_ EPS_RollingCounter_17E : 51|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_LatCtrlAvailabilityStatus : 53|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_LatCtrlActive : 54|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EPS_CRCCheck_17E : 63|8@0+ (1,0) [0|255] "/"  GW,CDC

BO_ 368 GW_170: 8 GW
 SG_ EPS_RollingCounter_170 : 51|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_fault_state : 55|4@0+ (1,0) [0|15] "/"  GW,CDC

BO_ 384 GW_180: 8 GW
 SG_ EpsSasSteerAg : 7|16@0- (0.1,0) [-780|780] "degree"  GW,APA,CDC,LVSM
 SG_ EpsSteerAgRate : 23|8@0+ (4,0) [0|1016] "deg/s"  GW,APA,CDC
 SG_ EpsSasCalSts : 29|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EpsSteerAgSensFilr : 30|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ EpsSasSteerAgVld : 31|1@0+ (1,0) [0|1] "/"  GW,APA,CDC,LVSM
 SG_ EpsCycCntr180 : 51|4@0+ (1,0) [0|15] "/"  GW,APA,CDC,LVSM
 SG_ EpsCrcChk180 : 63|8@0+ (1,0) [0|255] "/"  GW,APA,CDC,LVSM

BO_ 697 CDC_2B9: 64 CDC
 SG_ APA_FunctionOnOffSts : 4|6@0+ (1,0) [0|63] "/"  CDC,VCU,TBOX
 SG_ APA_ActivationSts : 42|3@0+ (1,0) [0|7] ""  CDC,APA,TBOX
 SG_ APA_ReadySts : 44|2@0+ (1,0) [0|3] ""  CDC,APA,TBOX

BO_ 625 INS_271: 64 INS
 SG_ INS_TiStamp : 7|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_TiOut : 37|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 38|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 39|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TIleap_Difference : 47|32@0+ (1,-10000000) [-10000000|100000000] "us"  INS,CDC
 SG_ INS_RollingCounter_271 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_271 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 646 CDC_286: 16 CDC
 SG_ ADS_SYNC_Type : 7|8@0+ (1,0) [0|255] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_CRC : 15|8@0+ (1,0) [0|255] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_SequenceCnt : 19|4@0+ (1,0) [0|15] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_TimeDomain : 23|4@0+ (1,0) [0|15] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_OVS_SGW : 31|8@0+ (1,0) [0|255] "-"  CDC,INS,LVSM
 SG_ ADS_SYNC_SyncTime : 39|32@0+ (1,0) [0|4294967295] "-"  CDC,INS,LVSM

BO_ 1317 CDC_525: 8 CDC
 SG_ TC397_TimeSyncInit : 0|1@0+ (1,0) [0|1] ""  CDC,INS,LVSM

BO_ 1537 CDC_601: 8 CDC
 SG_ VCU_Distance_CMD : 7|8@0+ (1,0) [0|255] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM9 : 8|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM10 : 9|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM11 : 10|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM12 : 11|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_Machine_NUM : 15|4@0+ (1,0) [0|15] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM1 : 16|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM2 : 17|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM3 : 18|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM4 : 19|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM5 : 20|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM6 : 21|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM7 : 22|1@0+ (1,0) [0|1] ""  CDC,USS
 SG_ VCU_ProbeCMD_NUM8 : 23|1@0+ (1,0) [0|1] ""  CDC,USS

BO_ 1553 USS_611: 8 USS
 SG_ Ult_Probe_info1 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info2 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info3 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info4 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS

BO_ 1554 USS_612: 8 USS
 SG_ Ult_Probe_info5 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info6 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info7 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info8 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS

BO_ 1555 USS_613: 8 USS
 SG_ Ult_Probe_info9 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info10 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info11 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info12 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS

BO_ 1616 ICMSG_650: 64 ICMSG
 SG_ INS_Current_Pos_X_Accel : 7|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_Y_Accel : 19|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Pos_X_Accel_Bias : 47|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Pos_Y_Accel_Bias : 63|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_Z_Accel : 79|20@0+ (0.001,-40) [-40|40] "m/s2"  INS,CDC
 SG_ INS_Pos_Z_Accel_Bias : 91|16@0+ (0.001,-10) [-10|10] "m/s2"  INS,CDC
 SG_ INS_Current_Pos_X_Rate : 107|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_X_Rate_Bias : 135|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Current_Pos_Y_Rate : 151|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_Y_Rate_Bias : 163|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Current_Pos_Z_Rate : 179|20@0+ (2E-005,-4.363) [-4.363|4.363] "rad/s"  INS,CDC
 SG_ INS_Pos_Z_Rate_Bias : 207|16@0+ (2E-005,-0.17453) [-0.17453|0.17453] "rad/s"  INS,CDC
 SG_ INS_Quaternion_X : 223|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_Y : 247|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_Z : 271|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_Quaternion_W : 295|24@0+ (1E-006,-1) [-1|1] "/"  INS,CDC
 SG_ INS_IMU_Time : 319|32@0+ (1,0) [0|4294967295] "ms"  INS,CDC
 SG_ INS_TiOut : 348|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 349|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 350|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_IMU_Valid : 351|1@0+ (1,0) [0|1] "ENUM"  INS,CDC
 SG_ INS_Roll_Mis_Angle_to_Veh : 359|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Pitch_Mis_Angle_to_Veh : 371|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Yaw_Mis_Angle_to_Veh : 399|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ ICMSG_TimeStamp : 431|64@0+ (1,0) [0|4294967295] "ms"  CDC
 SG_ INS_RollingCounter_146 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_146 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 1617 ICMSG_651: 64 ICMSG
 SG_ VCUAPARequestEnable : 1|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VCUAPAdriverInterruption : 3|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VCUAccPedShield : 4|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuAPATorqRequestAvailable : 8|1@0+ (1,0) [0|1] "/"  VCU,APA,CDC
 SG_ VCUShiftPostionValid : 9|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuCalcnAccrPedlPosn : 25|10@0+ (0.1,0) [0|102.3] "%"  VCU,APA,CDC
 SG_ VcuEnyRecyclMod : 57|7@0+ (1,0) [0|127] ""  VCU,APA,CDC
 SG_ VcuGearPosn : 66|3@0+ (1,0) [0|7] ""  VCU,APA,CDC,INS,LVSM
 SG_ VcuPtTqLimMax : 72|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuComFltSts : 75|2@0+ (1,0) [0|3] "/"  VCU,APA,CDC
 SG_ VcuCalcnAccrPedlPosnVld : 76|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMaxVld : 88|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqLimMin : 103|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuPtTqReal : 114|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuPtTqLimMinVld : 115|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuPtTqReqAvl : 129|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VcuPtTqRealVld : 130|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuVehWhlReqTq : 136|16@0+ (1,-32768) [-32768|32767] "Nm"  VCU,APA,CDC
 SG_ VcuVehGearPosnVld : 137|1@0+ (1,0) [0|1] ""  VCU,APA,CDC,INS,LVSM
 SG_ VcuShiftLvlPosn : 141|4@0+ (1,0) [0|15] "/"  VCU,APA,CDC
 SG_ VcuRdySts : 142|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuVehWhlReqTqVld : 152|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuCrcChk161 : 167|16@0+ (1,0) [0|65535] ""  VCU,APA,CDC,INS
 SG_ VcuSimnEpbSwtStsVld : 184|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuSimnEpbSwtSts : 186|2@0+ (1,0) [0|3] ""  VCU,APA,CDC
 SG_ VcuCycCntr161 : 190|4@0+ (1,0) [0|15] ""  VCU,APA,CDC,INS
 SG_ EPS_fault_state : 195|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_RollingCounter_170 : 199|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EpsCrcChk180 : 207|8@0+ (1,0) [0|255] "/"  GW,APA,CDC,LVSM
 SG_ EpsSasCalSts : 216|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EpsCycCntr180 : 220|4@0+ (1,0) [0|15] "/"  GW,APA,CDC,LVSM
 SG_ EpsSasSteerAg : 224|16@0- (0.1,0) [-780|780] "degree"  GW,APA,CDC,LVSM
 SG_ EpsSasSteerAgVld : 240|1@0+ (1,0) [0|1] "/"  GW,APA,CDC,LVSM
 SG_ EpsSteerAgRate : 255|8@0+ (4,0) [0|1016] "deg/s"  GW,APA,CDC
 SG_ EpsSteerAgSensFilr : 260|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ ICMSG_TimeStamp : 343|64@0+ (1,0) [0|4294967295] "ms"  CDC
 SG_ VcuAccrPedlPosn : 401|10@0+ (0.1,0) [0|102.3] "%"  VCU,APA,CDC
 SG_ GW_ADSSecOCVerifyFailureFlag : 416|1@0+ (1,0) [0|1] "/"  VCU,CDC
 SG_ VcuOvrdModReq : 417|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ VcuAccrPedlPosnVld : 418|1@0+ (1,0) [0|1] ""  VCU,APA,CDC
 SG_ INS_TiStamp : 431|32@0+ (0.0001,0) [0|429496.7295] "s"  INS,CDC
 SG_ INS_TIleap_Difference : 459|32@0+ (1,-10000000) [-10000000|100000000] "us"  INS,CDC
 SG_ INS_TiOut : 460|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiLeap : 461|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_TiBas : 462|1@0+ (1,0) [0|1] "-"  INS,CDC
 SG_ INS_RollingCounter_271 : 491|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_CRCCheck_271 : 503|16@0+ (1,0) [0|65535] "/"  INS,CDC

BO_ 1618 ICMSG_652: 64 ICMSG
 SG_ EspAbsFailr : 48|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspAbsActv : 49|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspReWhlDecTarTq : 56|16@0+ (1,-32768) [-32768|32767] "Nm"  GW,APA,CDC
 SG_ EspReWhlDecTarTqActv : 72|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspReWhlIncTarTq : 87|16@0+ (1,-32768) [-32768|32767] "Nm"  GW,APA,CDC
 SG_ EspEbdFailr : 96|1@0+ (1,0) [0|1] ""  GW,CDC
 SG_ EspTcsActvSts : 97|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspVdcActvSts : 98|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspReWhlIncTarTqActv : 99|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspVehSpd : 109|13@0+ (0.05625,0) [0|460.74375] "km/h"  GW,APA,CDC,INS,LVSM
 SG_ EspVehSpdVld : 112|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS,LVSM
 SG_ ESP_CRCCheck_17A : 123|16@0+ (1,0) [0|65535] "/"  GW,APA,CDC,LVSM,INS
 SG_ ESP_RollingCounter_17A : 139|4@0+ (1,0) [0|15] "/"  GW,APA,CDC,LVSM,INS
 SG_ EPB_AchievedClampForce : 156|5@0+ (1,0) [0|31] "KN"  GW,APA,CDC
 SG_ EPB_AchievedClampForce_Primary : 164|5@0+ (1,0) [0|31] "KN"  GW,APA,CDC
 SG_ EPB_FailStatuss_Primary : 166|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ EPB_APArequest_Available : 167|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_CRCCheck_17E : 175|8@0+ (1,0) [0|255] "/"  GW,CDC
 SG_ EPS_LatCtrlAvailabilityStatus : 177|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_LatCtrlActive : 178|1@0+ (1,0) [0|1] "/"  GW,CDC
 SG_ INS_Current_Pos_Heading : 187|20@0+ (0.001,0) [0|360] "°"  INS,CDC
 SG_ EPS_RollingCounter_17E : 191|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ INS_Current_Pos_Heading_Accuracy : 215|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Pitch : 227|20@0+ (0.001,-180) [-180|180] "°"  INS,CDC
 SG_ INS_Current_Pos_Heading_Confiden : 231|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Pitch_Accuracy : 255|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Roll : 267|20@0+ (0.001,-90) [-90|90] "°"  INS,CDC
 SG_ INS_Current_Pos_Pitch_Confidence : 271|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ INS_Current_Pos_Roll_Accuracy : 291|16@0+ (0.001,0) [0|65.535] "°"  INS,CDC
 SG_ INS_Current_Pos_Roll_Confidence : 307|4@0+ (1,0) [0|15] "/"  INS,CDC
 SG_ FL_wheel_vel_for_IPC : 319|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ FR_wheel_vel_for_IPC : 335|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ RL_wheel_vel_for_IPC : 351|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ RR_wheel_vel_for_IPC : 367|16@0+ (0.015625,-100) [-100|100] "m/s"  INS,CDC
 SG_ L_wheel_factor : 383|16@0+ (0.0001,0) [0|1] "m/m"  INS,CDC
 SG_ R_wheel_factor : 399|16@0+ (0.0001,0) [0|1] "m/m"  INS,CDC
 SG_ ICMSG_TimeStamp : 439|64@0+ (1,0) [0|4294967295] "ms"  CDC
 SG_ EspAutoHoldActvSts : 497|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EspTcsFailr : 498|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspFctOpenSts : 499|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspActvSts : 500|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ EspVehStandstill : 501|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ IBCU_ADCActiveState : 505|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_CommunicationInvalid : 507|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_ADCFullFuncAvail : 509|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ IBCU_ADCReducedFuncAvail : 511|2@0+ (1,0) [0|3] "/"  GW,CDC

BO_ 1619 ICMSG_653: 64 ICMSG
 SG_ ESP_BrakeForce : 16|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_SteeringTorque : 31|8@0+ (0.1794,-22.78) [-22.78|22.78] "Nm"  GW,APA,CDC
 SG_ ESP_APA_DriverOverride : 32|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ ESP_PrefillAvailable : 33|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ ESP_PrefillActive : 34|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ WhlSpdFrntLePls : 47|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdFrntRiPls : 52|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdLeFrntData : 60|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdLeReData : 76|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdLeFrntDir : 78|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdLeFrntDataVld : 79|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdLeReDir : 89|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdLeReDataVld : 90|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdReLePls : 103|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdReRiPls : 108|8@0+ (1,0) [0|254] ""  GW,APA,CDC,INS
 SG_ WhlSpdRiFrntData : 116|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdRiReData : 132|13@0+ (0.05625,0) [0|360] "km/h"  GW,APA,CDC,INS
 SG_ WhlSpdRiFrntDir : 134|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiFrntDataVld : 135|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ WhlSpdRiReDir : 145|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ WhlSpdRiReDataVld : 146|1@0+ (1,0) [0|1] ""  GW,APA,CDC,INS
 SG_ IbcuCrcChk20B : 155|16@0+ (1,0) [0|65535] ""  GW,APA,CDC,INS
 SG_ IbcuCycCntr20B : 171|4@0+ (1,0) [0|15] ""  GW,APA,CDC,INS
 SG_ IBCU_ADCRampOffSuspendState : 177|2@0+ (1,0) [0|3] ""  GW,CDC
 SG_ IBCU_APCActiveStatus : 179|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ IBCU_APCReducedFuncAvail : 180|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ IBCU_APCFullFuncAvail : 182|2@0+ (1,0) [0|3] ""  GW,APA,CDC
 SG_ EPS_ConcussAvailabilityStatus : 185|2@0+ (1,0) [0|3] "/"  GW,CDC
 SG_ EPS_APA_EpasFAILED : 186|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_APA_ControlFeedback : 187|1@0+ (1,0) [0|1] "/"  GW,APA,CDC
 SG_ EPS_APA_Abortfeedback : 190|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_CRCCheck_24F : 199|8@0+ (1,0) [0|255] "/"  GW,CDC,APA
 SG_ EPS_ElectPowerConsumption : 202|8@0+ (0.5,0) [0|127] "A"  GW,APA,CDC
 SG_ EPS_IACC_abortreason : 210|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EPS_SystemSt : 219|4@0+ (1,0) [0|15] "/"  GW,CDC
 SG_ EPS_RollingCounter_24F : 223|4@0+ (1,0) [0|15] "/"  GW,CDC,APA
 SG_ EPS_ADS_ControlFeedback : 226|3@0+ (1,0) [0|7] "/"  GW,CDC
 SG_ EPS_ADASActiveMode : 229|3@0+ (1,0) [0|7] "/"  GW,CDC
 SG_ IbBrkPedlStsGb : 263|8@0+ (1,0) [0|255] "%"  GW,APA,CDC
 SG_ IBCU_PFSBrakePressure : 267|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ IbBrkPedlStsGbVld : 269|2@0+ (1,0) [0|2] ""  GW,APA,CDC
 SG_ IBCU_PlungerBrakePressure : 291|12@0+ (0.1,0) [0|250] "bar"  GW,APA,CDC
 SG_ IbCrcChk2C2 : 307|16@0+ (1,0) [0|65535] ""  GW,APA,CDC
 SG_ IbCycCntr2C2 : 323|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ EpbStsPrimary : 394|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EpbSts : 397|3@0+ (1,0) [0|7] "/"  GW,APA,CDC
 SG_ EpbFailrSts : 399|2@0+ (1,0) [0|3] "/"  GW,APA,CDC
 SG_ EspCrcChk3C2 : 407|16@0+ (1,0) [0|65535] ""  GW,APA,CDC
 SG_ EspCycCntr3C2 : 416|4@0+ (1,0) [0|15] ""  GW,APA,CDC
 SG_ INS_Wheel_Scale_factor : 427|20@0+ (1E-006,-0.5) [-0.5|0.5] "/"  INS,CDC
 SG_ EspEpbReqAvl : 428|1@0+ (1,0) [0|1] ""  GW,APA,CDC
 SG_ ICMSG_TimeStamp : 455|64@0+ (1,0) [0|4294967295] "ms"  CDC

BO_ 1620 ICMSG_654: 32 ICMSG
 SG_ Ult_Probe_info1 : 7|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info2 : 23|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info3 : 39|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info4 : 55|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info5 : 71|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info6 : 87|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info7 : 103|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info8 : 119|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info9 : 135|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info10 : 151|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info11 : 167|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ Ult_Probe_info12 : 183|16@0+ (1,0) [0|65535] ""  CDC,USS
 SG_ ICMSG_TimeStamp : 199|64@0+ (1,0) [0|4294967295] "ms"  CDC



BA_DEF_ SG_  "GenSigStartValue" FLOAT 0 100000000000;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 100000;
BA_DEF_ SG_  "NWM - WakeupAllowed" ENUM  "no","Yes";
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","Cyclic","NotUsed","IfActive","NoMsgSendType","NotUsed","vector_leerstring";
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 999999;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 1000;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 50000;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 50000;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "no","Yes";
BA_DEF_ BO_  "NmMessage" ENUM  "no","Yes";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 65535;
BA_DEF_ BU_  "NmStationAddress" INT 0 63;
BA_DEF_ BU_  "NmNode" ENUM  "no","Yes";
BA_DEF_  "NmBaseAddress" HEX 1024 1087;
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ SG_  "SystemSignalLongSymbol" STRING ;
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigSendType" "NoSigSendType";
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "NWM - WakeupAllowed" "";
BA_DEF_DEF_  "GenMsgSendType" "NoMsgSendType";
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgILSupport" "Yes";
BA_DEF_DEF_  "NmMessage" "";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmNode" "";
BA_DEF_DEF_  "NmBaseAddress" 1024;
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_DEF_DEF_  "SystemSignalLongSymbol" "";
BA_ "BusType" "CAN FD";
BA_ "DBName" "CAN";
BA_ "GenMsgCycleTimeFast" BO_ 383 10;
BA_ "GenMsgCycleTime" BO_ 383 10;
BA_ "GenMsgSendType" BO_ 383 0;
BA_ "VFrameFormat" BO_ 383 14;
BA_ "GenMsgCycleTimeFast" BO_ 326 10;
BA_ "GenMsgCycleTime" BO_ 326 10;
BA_ "GenMsgSendType" BO_ 326 0;
BA_ "VFrameFormat" BO_ 326 14;
BA_ "GenMsgCycleTimeFast" BO_ 1459 40;
BA_ "GenMsgCycleTime" BO_ 1459 40;
BA_ "GenMsgSendType" BO_ 1459 0;
BA_ "VFrameFormat" BO_ 1459 14;
BA_ "GenMsgCycleTimeFast" BO_ 353 10;
BA_ "GenMsgCycleTime" BO_ 353 10;
BA_ "GenMsgSendType" BO_ 353 0;
BA_ "VFrameFormat" BO_ 353 14;
BA_ "GenMsgCycleTimeFast" BO_ 583 20;
BA_ "GenMsgCycleTime" BO_ 583 20;
BA_ "GenMsgSendType" BO_ 583 0;
BA_ "VFrameFormat" BO_ 583 14;
BA_ "GenMsgCycleTimeFast" BO_ 710 40;
BA_ "GenMsgCycleTime" BO_ 710 40;
BA_ "GenMsgSendType" BO_ 710 0;
BA_ "VFrameFormat" BO_ 710 14;
BA_ "GenMsgSendType" BO_ 1760 8;
BA_ "VFrameFormat" BO_ 1760 0;
BA_ "GenMsgCycleTimeFast" BO_ 798 100;
BA_ "GenMsgCycleTime" BO_ 798 100;
BA_ "GenMsgSendType" BO_ 798 0;
BA_ "VFrameFormat" BO_ 798 14;
BA_ "GenMsgCycleTimeFast" BO_ 523 10;
BA_ "GenMsgCycleTime" BO_ 523 10;
BA_ "GenMsgSendType" BO_ 523 0;
BA_ "VFrameFormat" BO_ 523 14;
BA_ "GenMsgCycleTimeFast" BO_ 378 10;
BA_ "GenMsgCycleTime" BO_ 378 10;
BA_ "GenMsgSendType" BO_ 378 0;
BA_ "VFrameFormat" BO_ 378 14;
BA_ "GenMsgCycleTimeFast" BO_ 962 100;
BA_ "GenMsgCycleTime" BO_ 962 100;
BA_ "GenMsgSendType" BO_ 962 0;
BA_ "VFrameFormat" BO_ 962 14;
BA_ "GenMsgCycleTimeFast" BO_ 706 10;
BA_ "GenMsgCycleTime" BO_ 706 10;
BA_ "GenMsgSendType" BO_ 706 0;
BA_ "VFrameFormat" BO_ 706 14;
BA_ "GenMsgCycleTimeFast" BO_ 591 20;
BA_ "GenMsgCycleTime" BO_ 591 20;
BA_ "GenMsgSendType" BO_ 591 0;
BA_ "VFrameFormat" BO_ 591 0;
BA_ "GenMsgCycleTimeFast" BO_ 382 10;
BA_ "GenMsgCycleTime" BO_ 382 10;
BA_ "GenMsgSendType" BO_ 382 0;
BA_ "VFrameFormat" BO_ 382 0;
BA_ "GenMsgCycleTimeFast" BO_ 368 10;
BA_ "GenMsgCycleTime" BO_ 368 10;
BA_ "GenMsgSendType" BO_ 368 0;
BA_ "VFrameFormat" BO_ 368 0;
BA_ "GenMsgCycleTimeFast" BO_ 384 10;
BA_ "GenMsgCycleTime" BO_ 384 10;
BA_ "GenMsgSendType" BO_ 384 0;
BA_ "VFrameFormat" BO_ 384 0;
BA_ "GenMsgCycleTimeFast" BO_ 697 50;
BA_ "GenMsgCycleTime" BO_ 697 50;
BA_ "GenMsgSendType" BO_ 697 0;
BA_ "VFrameFormat" BO_ 697 14;
BA_ "GenMsgCycleTimeFast" BO_ 625 10;
BA_ "GenMsgCycleTime" BO_ 625 10;
BA_ "GenMsgSendType" BO_ 625 0;
BA_ "VFrameFormat" BO_ 625 14;
BA_ "GenMsgCycleTimeFast" BO_ 646 40;
BA_ "GenMsgCycleTime" BO_ 646 500;
BA_ "GenMsgSendType" BO_ 646 8;
BA_ "VFrameFormat" BO_ 646 14;
BA_ "GenMsgCycleTimeFast" BO_ 1317 100;
BA_ "GenMsgCycleTime" BO_ 1317 100;
BA_ "GenMsgSendType" BO_ 1317 8;
BA_ "VFrameFormat" BO_ 1317 0;
BA_ "GenMsgCycleTimeFast" BO_ 1537 100;
BA_ "GenMsgCycleTime" BO_ 1537 100;
BA_ "GenMsgSendType" BO_ 1537 8;
BA_ "VFrameFormat" BO_ 1537 0;
BA_ "GenMsgCycleTimeFast" BO_ 1553 100;
BA_ "GenMsgCycleTime" BO_ 1553 100;
BA_ "GenMsgSendType" BO_ 1553 0;
BA_ "VFrameFormat" BO_ 1553 0;
BA_ "GenMsgCycleTimeFast" BO_ 1554 100;
BA_ "GenMsgCycleTime" BO_ 1554 100;
BA_ "GenMsgSendType" BO_ 1554 0;
BA_ "VFrameFormat" BO_ 1554 0;
BA_ "GenMsgCycleTimeFast" BO_ 1555 100;
BA_ "GenMsgCycleTime" BO_ 1555 100;
BA_ "GenMsgSendType" BO_ 1555 0;
BA_ "VFrameFormat" BO_ 1555 0;
BA_ "GenMsgCycleTimeFast" BO_ 1616 10;
BA_ "GenMsgCycleTime" BO_ 1616 10;
BA_ "GenMsgSendType" BO_ 1616 0;
BA_ "VFrameFormat" BO_ 1616 14;
BA_ "GenMsgCycleTimeFast" BO_ 1617 10;
BA_ "GenMsgCycleTime" BO_ 1617 10;
BA_ "GenMsgSendType" BO_ 1617 0;
BA_ "VFrameFormat" BO_ 1617 14;
BA_ "GenMsgCycleTimeFast" BO_ 1618 10;
BA_ "GenMsgCycleTime" BO_ 1618 10;
BA_ "GenMsgSendType" BO_ 1618 0;
BA_ "VFrameFormat" BO_ 1618 14;
BA_ "GenMsgCycleTimeFast" BO_ 1619 10;
BA_ "GenMsgCycleTime" BO_ 1619 10;
BA_ "GenMsgSendType" BO_ 1619 0;
BA_ "VFrameFormat" BO_ 1619 14;
BA_ "GenMsgCycleTimeFast" BO_ 1620 10;
BA_ "GenMsgCycleTime" BO_ 1620 10;
BA_ "GenMsgSendType" BO_ 1620 0;
BA_ "VFrameFormat" BO_ 1620 14;
BA_ "GenSigStartValue" SG_ 383 INS_GPS_Time 0;
BA_ "GenSigStartValue" SG_ 383 FL_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Heading 18000;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Heading_Accuracy 0;
BA_ "GenSigStartValue" SG_ 383 L_wheel_factor 0;
BA_ "GenSigStartValue" SG_ 383 R_wheel_factor 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Pitch_Accuracy 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Roll_Accuracy 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Pitch 18000;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Roll 18000;
BA_ "GenSigStartValue" SG_ 383 FR_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 RL_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 RR_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Pitch_Confidence 0;
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Heading_Confiden 0;
BA_ "SystemSignalLongSymbol" SG_ 383 INS_Current_Pos_Heading_Confiden "INS_Current_Pos_Heading_Confidence";
BA_ "GenSigStartValue" SG_ 383 INS_Current_Pos_Roll_Confidence 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 383 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 383 INS_RollingCounter_17F 0;
BA_ "GenSigStartValue" SG_ 383 INS_CRCCheck_17F 0;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_X_Accel 40000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Y_Accel 40000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_X_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Y_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Z_Accel 40000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Z_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_X_Rate 250000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_X_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Y_Rate 250000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Y_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Current_Pos_Z_Rate 250000;
BA_ "GenSigStartValue" SG_ 326 INS_Pos_Z_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_X 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_Y 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_Z 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_Quaternion_W 1000000;
BA_ "GenSigStartValue" SG_ 326 INS_IMU_Time 0;
BA_ "GenSigStartValue" SG_ 326 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 326 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 326 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 326 INS_IMU_Valid 0;
BA_ "GenSigStartValue" SG_ 326 INS_Roll_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 326 INS_Pitch_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 326 INS_Yaw_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 326 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 326 INS_RollingCounter_146 0;
BA_ "GenSigStartValue" SG_ 326 INS_CRCCheck_146 0;
BA_ "GenSigStartValue" SG_ 1459 INS_Wheel_Scale_factor 500000;
BA_ "GenSigStartValue" SG_ 353 VCUShiftPostionValid 0;
BA_ "GenSigStartValue" SG_ 353 VCUAPARequestEnable 0;
BA_ "GenSigStartValue" SG_ 353 VCUAPAdriverInterruption 0;
BA_ "GenSigStartValue" SG_ 353 VCUAccPedShield 0;
BA_ "GenSigStartValue" SG_ 353 VcuAccrPedlPosn 0;
BA_ "GenSigStartValue" SG_ 353 VcuCalcnAccrPedlPosn 0;
BA_ "GenSigStartValue" SG_ 353 VcuAPATorqRequestAvailable 0;
BA_ "GenSigStartValue" SG_ 353 VcuAccrPedlPosnVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuCalcnAccrPedlPosnVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuEnyRecyclMod 0;
BA_ "GenSigStartValue" SG_ 353 VcuComFltSts 0;
BA_ "GenSigStartValue" SG_ 353 VcuSimnEpbSwtSts 0;
BA_ "GenSigStartValue" SG_ 353 VcuSimnEpbSwtStsVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuGearPosn 3;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqReqAvl 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqRealVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMinVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMaxVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuOvrdModReq 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMax 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqLimMin 0;
BA_ "GenSigStartValue" SG_ 353 VcuShiftLvlPosn 0;
BA_ "GenSigStartValue" SG_ 353 VcuPtTqReal 0;
BA_ "GenSigStartValue" SG_ 353 VcuVehWhlReqTqVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuVehGearPosnVld 0;
BA_ "GenSigStartValue" SG_ 353 VcuRdySts 0;
BA_ "GenSigStartValue" SG_ 353 VcuVehWhlReqTq 0;
BA_ "GenSigStartValue" SG_ 353 GW_ADSSecOCVerifyFailureFlag 0;
BA_ "GenSigStartValue" SG_ 353 VcuCycCntr161 0;
BA_ "GenSigStartValue" SG_ 353 VcuCrcChk161 0;
BA_ "GenSigStartValue" SG_ 583 APA_SteeringAngleReqProtection 0;
BA_ "GenSigStartValue" SG_ 583 APA_ErrorStatus 0;
BA_ "GenSigStartValue" SG_ 583 APA_indication 0;
BA_ "GenSigStartValue" SG_ 583 APA_APAOnOff 0;
BA_ "GenSigStartValue" SG_ 583 APA_EmergenceBrake 0;
BA_ "GenSigStartValue" SG_ 583 APA_SteeringAngleReq 32767;
BA_ "GenSigStartValue" SG_ 583 APA_RemoteOnOff 0;
BA_ "GenSigStartValue" SG_ 583 APA_ButtonPress 0;
BA_ "GenSigStartValue" SG_ 583 APA_IncreasePressureReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_TurnLightsCommand 0;
BA_ "GenSigStartValue" SG_ 583 APA_ParkNotice_4 0;
BA_ "GenSigStartValue" SG_ 583 APA_ParkNotice 0;
BA_ "GenSigStartValue" SG_ 583 APA_ParkingPercentage 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_264 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRCCheck_264 0;
BA_ "GenSigStartValue" SG_ 583 APA_EPBrequest 0;
BA_ "GenSigStartValue" SG_ 583 APA_EPBrequestValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_TargetAccelerationValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_TransPRNDShiftRequest 0;
BA_ "GenSigStartValue" SG_ 583 APA_TransPRNDShiftReqValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_TargetAcceleration 100;
BA_ "GenSigStartValue" SG_ 583 APA_EngTorqReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_Activation_Status 0;
BA_ "GenSigStartValue" SG_ 583 APA_TransPRNDShiftEnable 0;
BA_ "GenSigStartValue" SG_ 583 APA_LSCAction 0;
BA_ "GenSigStartValue" SG_ 583 APA_HSAHDforbidden 0;
BA_ "GenSigStartValue" SG_ 583 APA_EngineTrqReqEnable 0;
BA_ "GenSigStartValue" SG_ 583 APA_AccPedShieldReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_ESPDecompressionModel 0;
BA_ "GenSigStartValue" SG_ 583 APA_PrefillReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_DynamicSlotWarning 0;
BA_ "GenSigStartValue" SG_ 583 APA_SlotNotice 0;
BA_ "GenSigStartValue" SG_ 583 APA_TCUClutchCombinationReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_TrqHoldForTCUCl 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_26C 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRCCheck_26C 0;
BA_ "GenSigStartValue" SG_ 583 APA_PtTorqReq 32768;
BA_ "GenSigStartValue" SG_ 583 APA_ESPDistToStop 0;
BA_ "GenSigStartValue" SG_ 583 APA_ESP_BrakeFunctionMode 0;
BA_ "GenSigStartValue" SG_ 583 APA_PtTrqReqValid 0;
BA_ "GenSigStartValue" SG_ 583 APA_VCUReadyReq 0;
BA_ "GenSigStartValue" SG_ 583 APA_ESP_StandstillRequest 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_236 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRC_Checksum_236 0;
BA_ "GenSigStartValue" SG_ 583 APA_RollingCounter_247 0;
BA_ "GenSigStartValue" SG_ 583 APA_CRCCheck_247 0;
BA_ "GenSigStartValue" SG_ 710 APA_ParkNotice_5 0;
BA_ "GenSigStartValue" SG_ 710 APA_LAEBReq 0;
BA_ "GenSigStartValue" SG_ 710 APA_LAEBStatus 0;
BA_ "GenSigStartValue" SG_ 710 APA_BLEConnectionRemind 0;
BA_ "GenSigStartValue" SG_ 710 APA_LAEBNotice 0;
BA_ "GenSigStartValue" SG_ 710 APA_RemoteParkingUsingRemind 0;
BA_ "GenSigStartValue" SG_ 710 APA_ASPAvailableStatus 0;
BA_ "GenSigStartValue" SG_ 710 APA_ASPStatus 0;
BA_ "GenSigStartValue" SG_ 710 APA_CrossModeSelectReq 0;
BA_ "GenSigStartValue" SG_ 710 APA_BCMHornCommand 0;
BA_ "GenSigStartValue" SG_ 710 APA_vehicleFrontdetect 0;
BA_ "GenSigStartValue" SG_ 710 APA_ReleasePressureReq 0;
BA_ "GenSigStartValue" SG_ 710 APA_PEPS_EngineOffLockRequest 0;
BA_ "GenSigStartValue" SG_ 710 APA_RADSNotice 0;
BA_ "GenSigStartValue" SG_ 710 APA_RollingCounter_2D4 0;
BA_ "GenSigStartValue" SG_ 710 APA_PEPS_EngineOffRequest 0;
BA_ "GenSigStartValue" SG_ 710 APA_CRCCheck_2D4 0;
BA_ "GenSigStartValue" SG_ 710 APA_RollingCounter_2C6 0;
BA_ "GenSigStartValue" SG_ 710 APA_CRCCheck_2C6 0;
BA_ "GenSigSendType" SG_ 1760 APA_AuthenticationStatus 1;
BA_ "GenSigStartValue" SG_ 1760 APA_AuthenticationStatus 0;
BA_ "GenSigStartValue" SG_ 798 APA_SystemFailureFlag 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiFrntData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiFrntDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiFrntDataVld 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeFrntData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeFrntDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeFrntDataVld 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_APCActiveStatus 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiReData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiReDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdRiReDataVld 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeReData 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeReDir 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdLeReDataVld 0;
BA_ "GenSigStartValue" SG_ 523 ESP_APA_DriverOverride 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdFrntLePls 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdFrntRiPls 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdReRiPls 0;
BA_ "GenSigStartValue" SG_ 523 WhlSpdReLePls 0;
BA_ "GenSigStartValue" SG_ 523 ESP_PrefillAvailable 0;
BA_ "GenSigStartValue" SG_ 523 ESP_PrefillActive 0;
BA_ "GenSigStartValue" SG_ 523 ESP_BrakeForce 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_APCReducedFuncAvail 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_APCFullFuncAvail 0;
BA_ "GenSigStartValue" SG_ 523 IbcuCycCntr20B 0;
BA_ "GenSigStartValue" SG_ 523 IBCU_ADCRampOffSuspendState 0;
BA_ "GenSigStartValue" SG_ 523 IbcuCrcChk20B 0;
BA_ "GenSigStartValue" SG_ 378 EspVehSpd 0;
BA_ "GenSigStartValue" SG_ 378 EspVehSpdVld 0;
BA_ "GenSigStartValue" SG_ 378 EPB_APArequest_Available 0;
BA_ "GenSigStartValue" SG_ 378 EPB_AchievedClampForce_Primary 0;
BA_ "GenSigStartValue" SG_ 378 EPB_AchievedClampForce 0;
BA_ "GenSigStartValue" SG_ 378 EPB_FailStatuss_Primary 0;
BA_ "GenSigStartValue" SG_ 378 ESP_RollingCounter_17A 0;
BA_ "GenSigStartValue" SG_ 378 ESP_CRCCheck_17A 0;
BA_ "GenSigStartValue" SG_ 962 EpbFailrSts 0;
BA_ "GenSigStartValue" SG_ 962 EpbSts 0;
BA_ "GenSigStartValue" SG_ 962 EspEpbReqAvl 0;
BA_ "GenSigStartValue" SG_ 962 EpbStsPrimary 0;
BA_ "GenSigStartValue" SG_ 962 EspCycCntr3C2 0;
BA_ "GenSigStartValue" SG_ 962 EspCrcChk3C2 0;
BA_ "GenSigStartValue" SG_ 706 IbBrkPedlStsGb 0;
BA_ "GenSigStartValue" SG_ 706 IbBrkPedlStsGbVld 0;
BA_ "GenSigStartValue" SG_ 706 IBCU_PFSBrakePressure 0;
BA_ "GenSigStartValue" SG_ 706 IBCU_PlungerBrakePressure 0;
BA_ "GenSigStartValue" SG_ 706 IbCycCntr2C2 0;
BA_ "GenSigStartValue" SG_ 706 IbCrcChk2C2 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ElectPowerConsumption 0;
BA_ "GenSigStartValue" SG_ 591 EPS_APA_EpasFAILED 0;
BA_ "GenSigStartValue" SG_ 591 EPS_APA_Abortfeedback 0;
BA_ "GenSigStartValue" SG_ 591 EPS_SteeringTorque 127;
BA_ "GenSigStartValue" SG_ 591 EPS_IACC_abortreason 0;
BA_ "GenSigStartValue" SG_ 591 EPS_APA_ControlFeedback 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ADASActiveMode 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ADS_ControlFeedback 0;
BA_ "GenSigStartValue" SG_ 591 EPS_SystemSt 0;
BA_ "GenSigStartValue" SG_ 591 EPS_RollingCounter_24F 0;
BA_ "GenSigStartValue" SG_ 591 EPS_ConcussAvailabilityStatus 0;
BA_ "GenSigStartValue" SG_ 591 EPS_CRCCheck_24F 0;
BA_ "GenSigStartValue" SG_ 382 EPS_RollingCounter_17E 0;
BA_ "GenSigStartValue" SG_ 382 EPS_LatCtrlAvailabilityStatus 0;
BA_ "GenSigStartValue" SG_ 382 EPS_LatCtrlActive 0;
BA_ "GenSigStartValue" SG_ 382 EPS_CRCCheck_17E 0;
BA_ "GenSigStartValue" SG_ 368 EPS_RollingCounter_170 0;
BA_ "GenSigStartValue" SG_ 368 EPS_fault_state 0;
BA_ "GenSigStartValue" SG_ 384 EpsSasSteerAg 0;
BA_ "GenSigStartValue" SG_ 384 EpsSteerAgRate 0;
BA_ "GenSigStartValue" SG_ 384 EpsSasCalSts 0;
BA_ "GenSigStartValue" SG_ 384 EpsSteerAgSensFilr 0;
BA_ "GenSigStartValue" SG_ 384 EpsSasSteerAgVld 0;
BA_ "GenSigStartValue" SG_ 384 EpsCycCntr180 0;
BA_ "GenSigStartValue" SG_ 384 EpsCrcChk180 0;
BA_ "GenSigStartValue" SG_ 697 APA_FunctionOnOffSts 0;
BA_ "GenSigStartValue" SG_ 697 APA_ActivationSts 0;
BA_ "GenSigStartValue" SG_ 697 APA_ReadySts 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 625 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 625 INS_TIleap_Difference 0;
BA_ "GenSigStartValue" SG_ 625 INS_RollingCounter_271 0;
BA_ "GenSigStartValue" SG_ 625 INS_CRCCheck_271 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_Type 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_Type 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_CRC 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_CRC 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_SequenceCnt 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_SequenceCnt 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_TimeDomain 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_TimeDomain 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_OVS_SGW 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_OVS_SGW 0;
BA_ "GenSigSendType" SG_ 646 ADS_SYNC_SyncTime 1;
BA_ "GenSigStartValue" SG_ 646 ADS_SYNC_SyncTime 0;
BA_ "GenSigSendType" SG_ 1317 TC397_TimeSyncInit 1;
BA_ "GenSigStartValue" SG_ 1317 TC397_TimeSyncInit 0;
BA_ "GenSigSendType" SG_ 1537 VCU_Distance_CMD 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_Distance_CMD 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM9 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM9 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM10 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM10 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM11 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM11 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM12 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM12 0;
BA_ "GenSigSendType" SG_ 1537 VCU_Machine_NUM 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_Machine_NUM 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM1 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM1 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM2 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM2 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM3 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM3 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM4 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM4 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM5 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM5 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM6 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM6 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM7 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM7 0;
BA_ "GenSigSendType" SG_ 1537 VCU_ProbeCMD_NUM8 1;
BA_ "GenSigStartValue" SG_ 1537 VCU_ProbeCMD_NUM8 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info1 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info2 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info3 0;
BA_ "GenSigStartValue" SG_ 1553 Ult_Probe_info4 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info5 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info6 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info7 0;
BA_ "GenSigStartValue" SG_ 1554 Ult_Probe_info8 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info9 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info10 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info11 0;
BA_ "GenSigStartValue" SG_ 1555 Ult_Probe_info12 0;
BA_ "GenSigStartValue" SG_ 1616 INS_Current_Pos_X_Accel 40000;
BA_ "GenSigStartValue" SG_ 1616 INS_Current_Pos_Y_Accel 40000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pos_X_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pos_Y_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 1616 INS_Current_Pos_Z_Accel 40000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pos_Z_Accel_Bias 10000;
BA_ "GenSigStartValue" SG_ 1616 INS_Current_Pos_X_Rate 250000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pos_X_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 1616 INS_Current_Pos_Y_Rate 250000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pos_Y_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 1616 INS_Current_Pos_Z_Rate 250000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pos_Z_Rate_Bias 10000;
BA_ "GenSigStartValue" SG_ 1616 INS_Quaternion_X 1000000;
BA_ "GenSigStartValue" SG_ 1616 INS_Quaternion_Y 1000000;
BA_ "GenSigStartValue" SG_ 1616 INS_Quaternion_Z 1000000;
BA_ "GenSigStartValue" SG_ 1616 INS_Quaternion_W 1000000;
BA_ "GenSigStartValue" SG_ 1616 INS_IMU_Time 0;
BA_ "GenSigStartValue" SG_ 1616 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 1616 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 1616 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 1616 INS_IMU_Valid 0;
BA_ "GenSigStartValue" SG_ 1616 INS_Roll_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 1616 INS_Pitch_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 1616 INS_Yaw_Mis_Angle_to_Veh 180000;
BA_ "GenSigStartValue" SG_ 1616 ICMSG_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1616 INS_RollingCounter_146 0;
BA_ "GenSigStartValue" SG_ 1616 INS_CRCCheck_146 0;
BA_ "GenSigStartValue" SG_ 1617 VCUAPARequestEnable 0;
BA_ "GenSigStartValue" SG_ 1617 VCUAPAdriverInterruption 0;
BA_ "GenSigStartValue" SG_ 1617 VCUAccPedShield 0;
BA_ "GenSigStartValue" SG_ 1617 VcuAPATorqRequestAvailable 0;
BA_ "GenSigStartValue" SG_ 1617 VCUShiftPostionValid 0;
BA_ "GenSigStartValue" SG_ 1617 VcuCalcnAccrPedlPosn 0;
BA_ "GenSigStartValue" SG_ 1617 VcuEnyRecyclMod 0;
BA_ "GenSigStartValue" SG_ 1617 VcuGearPosn 3;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqLimMax 0;
BA_ "GenSigStartValue" SG_ 1617 VcuComFltSts 0;
BA_ "GenSigStartValue" SG_ 1617 VcuCalcnAccrPedlPosnVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqLimMaxVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqLimMin 0;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqReal 0;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqLimMinVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqReqAvl 0;
BA_ "GenSigStartValue" SG_ 1617 VcuPtTqRealVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuVehWhlReqTq 0;
BA_ "GenSigStartValue" SG_ 1617 VcuVehGearPosnVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuShiftLvlPosn 0;
BA_ "GenSigStartValue" SG_ 1617 VcuRdySts 0;
BA_ "GenSigStartValue" SG_ 1617 VcuVehWhlReqTqVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuCrcChk161 0;
BA_ "GenSigStartValue" SG_ 1617 VcuSimnEpbSwtStsVld 0;
BA_ "GenSigStartValue" SG_ 1617 VcuSimnEpbSwtSts 0;
BA_ "GenSigStartValue" SG_ 1617 VcuCycCntr161 0;
BA_ "GenSigStartValue" SG_ 1617 EPS_fault_state 0;
BA_ "GenSigStartValue" SG_ 1617 EPS_RollingCounter_170 0;
BA_ "GenSigStartValue" SG_ 1617 EpsCrcChk180 0;
BA_ "GenSigStartValue" SG_ 1617 EpsSasCalSts 0;
BA_ "GenSigStartValue" SG_ 1617 EpsCycCntr180 0;
BA_ "GenSigStartValue" SG_ 1617 EpsSasSteerAg 0;
BA_ "GenSigStartValue" SG_ 1617 EpsSasSteerAgVld 0;
BA_ "GenSigStartValue" SG_ 1617 EpsSteerAgRate 0;
BA_ "GenSigStartValue" SG_ 1617 EpsSteerAgSensFilr 0;
BA_ "GenSigStartValue" SG_ 1617 ICMSG_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1617 VcuAccrPedlPosn 0;
BA_ "GenSigStartValue" SG_ 1617 GW_ADSSecOCVerifyFailureFlag 0;
BA_ "GenSigStartValue" SG_ 1617 VcuOvrdModReq 0;
BA_ "GenSigStartValue" SG_ 1617 VcuAccrPedlPosnVld 0;
BA_ "GenSigStartValue" SG_ 1617 INS_TiStamp 0;
BA_ "GenSigStartValue" SG_ 1617 INS_TIleap_Difference 0;
BA_ "GenSigStartValue" SG_ 1617 INS_TiOut 0;
BA_ "GenSigStartValue" SG_ 1617 INS_TiLeap 0;
BA_ "GenSigStartValue" SG_ 1617 INS_TiBas 0;
BA_ "GenSigStartValue" SG_ 1617 INS_RollingCounter_271 0;
BA_ "GenSigStartValue" SG_ 1617 INS_CRCCheck_271 0;
BA_ "GenSigStartValue" SG_ 1618 EspAbsFailr 0;
BA_ "GenSigStartValue" SG_ 1618 EspAbsActv 0;
BA_ "GenSigStartValue" SG_ 1618 EspReWhlDecTarTq 0;
BA_ "GenSigStartValue" SG_ 1618 EspReWhlDecTarTqActv 0;
BA_ "GenSigStartValue" SG_ 1618 EspReWhlIncTarTq 0;
BA_ "GenSigStartValue" SG_ 1618 EspEbdFailr 0;
BA_ "GenSigStartValue" SG_ 1618 EspTcsActvSts 0;
BA_ "GenSigStartValue" SG_ 1618 EspVdcActvSts 0;
BA_ "GenSigStartValue" SG_ 1618 EspReWhlIncTarTqActv 0;
BA_ "GenSigStartValue" SG_ 1618 EspVehSpd 0;
BA_ "GenSigStartValue" SG_ 1618 EspVehSpdVld 0;
BA_ "GenSigStartValue" SG_ 1618 ESP_CRCCheck_17A 0;
BA_ "GenSigStartValue" SG_ 1618 ESP_RollingCounter_17A 0;
BA_ "GenSigStartValue" SG_ 1618 EPB_AchievedClampForce 0;
BA_ "GenSigStartValue" SG_ 1618 EPB_AchievedClampForce_Primary 0;
BA_ "GenSigStartValue" SG_ 1618 EPB_FailStatuss_Primary 0;
BA_ "GenSigStartValue" SG_ 1618 EPB_APArequest_Available 0;
BA_ "GenSigStartValue" SG_ 1618 EPS_CRCCheck_17E 0;
BA_ "GenSigStartValue" SG_ 1618 EPS_LatCtrlAvailabilityStatus 0;
BA_ "GenSigStartValue" SG_ 1618 EPS_LatCtrlActive 0;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Heading 18000;
BA_ "GenSigStartValue" SG_ 1618 EPS_RollingCounter_17E 0;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Heading_Accuracy 0;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Pitch 18000;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Heading_Confiden 0;
BA_ "SystemSignalLongSymbol" SG_ 1618 INS_Current_Pos_Heading_Confiden "INS_Current_Pos_Heading_Confidence";
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Pitch_Accuracy 0;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Roll 18000;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Pitch_Confidence 0;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Roll_Accuracy 0;
BA_ "GenSigStartValue" SG_ 1618 INS_Current_Pos_Roll_Confidence 0;
BA_ "GenSigStartValue" SG_ 1618 FL_wheel_vel_for_IPC 100000;
BA_ "GenSigStartValue" SG_ 1618 FR_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 1618 RL_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 1618 RR_wheel_vel_for_IPC 0;
BA_ "GenSigStartValue" SG_ 1618 L_wheel_factor 0;
BA_ "GenSigStartValue" SG_ 1618 R_wheel_factor 0;
BA_ "GenSigStartValue" SG_ 1618 ICMSG_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1618 EspAutoHoldActvSts 0;
BA_ "GenSigStartValue" SG_ 1618 EspTcsFailr 0;
BA_ "GenSigStartValue" SG_ 1618 EspFctOpenSts 0;
BA_ "GenSigStartValue" SG_ 1618 EspActvSts 0;
BA_ "GenSigStartValue" SG_ 1618 EspVehStandstill 0;
BA_ "GenSigStartValue" SG_ 1618 IBCU_ADCActiveState 0;
BA_ "GenSigStartValue" SG_ 1618 IBCU_CommunicationInvalid 0;
BA_ "GenSigStartValue" SG_ 1618 IBCU_ADCFullFuncAvail 0;
BA_ "GenSigStartValue" SG_ 1618 IBCU_ADCReducedFuncAvail 0;
BA_ "GenSigStartValue" SG_ 1619 ESP_BrakeForce 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_SteeringTorque 127;
BA_ "GenSigStartValue" SG_ 1619 ESP_APA_DriverOverride 0;
BA_ "GenSigStartValue" SG_ 1619 ESP_PrefillAvailable 0;
BA_ "GenSigStartValue" SG_ 1619 ESP_PrefillActive 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdFrntLePls 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdFrntRiPls 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdLeFrntData 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdLeReData 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdLeFrntDir 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdLeFrntDataVld 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdLeReDir 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdLeReDataVld 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdReLePls 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdReRiPls 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdRiFrntData 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdRiReData 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdRiFrntDir 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdRiFrntDataVld 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdRiReDir 0;
BA_ "GenSigStartValue" SG_ 1619 WhlSpdRiReDataVld 0;
BA_ "GenSigStartValue" SG_ 1619 IbcuCrcChk20B 0;
BA_ "GenSigStartValue" SG_ 1619 IbcuCycCntr20B 0;
BA_ "GenSigStartValue" SG_ 1619 IBCU_ADCRampOffSuspendState 0;
BA_ "GenSigStartValue" SG_ 1619 IBCU_APCActiveStatus 0;
BA_ "GenSigStartValue" SG_ 1619 IBCU_APCReducedFuncAvail 0;
BA_ "GenSigStartValue" SG_ 1619 IBCU_APCFullFuncAvail 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_ConcussAvailabilityStatus 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_APA_EpasFAILED 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_APA_ControlFeedback 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_APA_Abortfeedback 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_CRCCheck_24F 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_ElectPowerConsumption 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_IACC_abortreason 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_SystemSt 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_RollingCounter_24F 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_ADS_ControlFeedback 0;
BA_ "GenSigStartValue" SG_ 1619 EPS_ADASActiveMode 0;
BA_ "GenSigStartValue" SG_ 1619 IbBrkPedlStsGb 0;
BA_ "GenSigStartValue" SG_ 1619 IBCU_PFSBrakePressure 0;
BA_ "GenSigStartValue" SG_ 1619 IbBrkPedlStsGbVld 0;
BA_ "GenSigStartValue" SG_ 1619 IBCU_PlungerBrakePressure 0;
BA_ "GenSigStartValue" SG_ 1619 IbCrcChk2C2 0;
BA_ "GenSigStartValue" SG_ 1619 IbCycCntr2C2 0;
BA_ "GenSigStartValue" SG_ 1619 EpbStsPrimary 0;
BA_ "GenSigStartValue" SG_ 1619 EpbSts 0;
BA_ "GenSigStartValue" SG_ 1619 EpbFailrSts 0;
BA_ "GenSigStartValue" SG_ 1619 EspCrcChk3C2 0;
BA_ "GenSigStartValue" SG_ 1619 EspCycCntr3C2 0;
BA_ "GenSigStartValue" SG_ 1619 INS_Wheel_Scale_factor 500000;
BA_ "GenSigStartValue" SG_ 1619 EspEpbReqAvl 0;
BA_ "GenSigStartValue" SG_ 1619 ICMSG_TimeStamp 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info1 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info2 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info3 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info4 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info5 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info6 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info7 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info8 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info9 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info10 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info11 0;
BA_ "GenSigStartValue" SG_ 1620 Ult_Probe_info12 0;
BA_ "GenSigStartValue" SG_ 1620 ICMSG_TimeStamp 0;


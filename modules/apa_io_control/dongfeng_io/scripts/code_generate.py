import os
import time
import glob
from pathlib import Path
import shutil
from datetime import datetime
import json
import re

def read_lines(file_path):
    f = open(file_path, 'r')
    f.seek(0)
    lines = f.read().splitlines()
    f.close()
    return lines


def generate4up_signals():
    lines = read_lines("up_signals.txt")
    idx = 0
    for line in lines:
        line = line.strip()
        if not line:
            print("")
            idx += 1
            continue
        fields = line.split(".")
        print(f"GetSignalValue(&CANSIG_ICMSG_{650+idx}__{fields[1]}_g, {line});")

def generate4down_signals():
    lines = read_lines("down_signals.txt")
    for line in lines:
        line = line.strip()
        if ' = ' in line:
            equal_fields = line.split("=")
            variable_name = equal_fields[-1].strip()[:-1]
            signal_fields = variable_name.split(".")
            print(f"FillVehCtrlData(&CANSIG_{signal_fields[0].upper()}__{signal_fields[1]}_g, {variable_name}, data_p);")            
        else:
            print(line)
            
                
if __name__ == "__main__":
    generate4down_signals()
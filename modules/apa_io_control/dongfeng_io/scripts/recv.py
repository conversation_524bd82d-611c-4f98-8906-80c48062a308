import cantools
from socketcan import CanRawSocket, CanFrame

db = cantools.database.load_file('can0_c385_apa_uss_20230131.dbc')

interface = "can0"
s = CanRawSocket(interface=interface)

for idx in range(10):
    frame = s.recv()
    print("vcan0  {0:8X}   [{1}]  {2}".format(frame.can_id,
                                              len(frame.data),
                                              " ".join(["{0:02X}".format(b) for b in frame.data ])
                                              )
          )
    msg = db.decode_message(frame.can_id, frame.data)
    print(msg)


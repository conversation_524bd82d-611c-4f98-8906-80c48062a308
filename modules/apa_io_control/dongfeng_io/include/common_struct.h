#ifndef __COMMON_MSG__
#define __COMMON_MSG__
#pragma once

#include <string>
#include <queue>
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"
#include "uss.h"
#include <sstream>

using rainbowdash::common::CommonGearPosition;
using rainbowdash::common::CommonBool;
using rainbowdash::common::CommonErrorType;
using rainbowdash::control_by_wire::SystemCheckErrReason;
using rainbowdash::control_by_wire::EpsErrReason;
using rainbowdash::control_by_wire::EpbErrReason;
using rainbowdash::control_by_wire::EspErrReason;
using rainbowdash::control_by_wire::VcuErrReason;
using rainbowdash::control_by_wire::IBCLSMCtrlFaultSts;
using rainbowdash::control_by_wire::LatShakeHandErrReason;
using rainbowdash::control_by_wire::LonShakeHandErrReason;


#define ENABLE_RADAR_CLOUD 0

#define USE_RECORD_CHANGE 1
/************************
  0x341, 0x320, 0x33C, 0x4DA, 0x30A, 0x0A2, 0x133, 0x11D, 0x12C, 0x101, 0x172, 0x143, 0x0FC, 0x1C8,
    0x0FE, 0x0FF, 0x17A, 0x0A5, 0x0B0, 0x167, 0x23A, 0x040, 0x322, 0x365, 0x4C1, 0x367, 0x4C0, 0x3A7,
    0x119,
 * 7)驾驶员按下开始泊车按钮后，系统开始检测静态坡度(IBC SIpe)后背门(
        POT BackDoorPosSt)四个车门和主驾安全带(根据仪表主驾安全带未系报警)的状态。若
        检测到坡度>15%，则系统退出，并提示“坡度过大，泊车系统退出，请接管车辆”。若检测
        到后背门或者四个车门有一个未关提醒“请关闭车门及后备箱门”:若检测到主驾安全带未
        系，提醒“请系好安全带”其中事件显示优先级为坡度过大>车门未关>安全带未系(算法判
        断后通知应用)。未找到后背门、主驾安全带信号
 * **********************/
#define SUB_PDCU_FC                 0xFC//
#define SUB_IBC_143                    0x143
#define SUB_IBC_12C                    0x12C
#define SUB_IBC_101                    0x101
#define SUB_IBC_A2                      0xA2
#define SUB_IBC_172                    0x172
#define SUB_EPS_B0                      0xB0
#define SUB_EPS_A5                      0xA5//
#define SUB_SGW_C_346             0x346//
#define SUB_SGW_C_40               0x40//
#define SUB_SGW_C_119             0x119//
#define SUB_SGW_C_710             0x710
#define SUB_EPS_17A                   0x17A
#define SUB_IBC_11D                    0x11D
#define SUB_SGW_C_23A            0x23A
#define SUB_PDCU_FE                  0xFE
#define SUB_SGW_C_3F0                0x3F0
#define SUB_IBC_133                  0x133

#define SUB_PDCU_1C8               0x1C8
#define SUB_PDCU_FF                  0xFF

#define SUB_SGW_I_30C            0x30C
#define SUB_SGW_I_304            0x304
#define SUB_SGW_I_33D            0x33D

//radar
#define SUB_RADAR_FL_41            0x41
#define SUB_RADAR_FL_42            0x42
#define SUB_RADAR_FL_40            0x40
#define SUB_RADAR_FR_241            0x241
#define SUB_RADAR_FR_242            0x242
#define SUB_RADAR_FR_240            0x240
#define SUB_RADAR_FC_341            0x341
#define SUB_RADAR_FC_342            0x342
#define SUB_RADAR_FC_340            0x340
#define SUB_RADAR_RL_441            0x441
#define SUB_RADAR_RL_442            0x442
#define SUB_RADAR_RL_440            0x440
#define SUB_RADAR_RR_641            0x641
#define SUB_RADAR_RR_642            0x642
#define SUB_RADAR_RR_640            0x640

#define SUB_IC_TOPIC_IMU       "ImuSensor/resp"
#define SUB_IC_TOPIC_APA        "APA/MessageInfo"

#define SUB_ACK_IVI                          0xA0
#define SUB_ACK_EX                          0x05
#define SUB_PUB_USS                      0xA1
#define SUB_PUB_HB                         0x02
#define SUB_PUB_EX                          0x06
#define SUB_ACK_SH                          0x01
#define SUB_ACK_CTL                        0x03
#define SUB_PUB_MCU_ET              0x04
#define SUB_IMU_MSG                       0x99 //自定义
#define SUB_ACK_APA_ST                0xA2
#define SUB_ACK_TRQ_CTL                0x13
#define SUB_ACK_ACC_SH                          0x11

namespace io_server{

template <typename T> 
class ZeroStruct 
{ 
public: 
    ZeroStruct() 
    { 
        memset(this,0,sizeof(T)); 
    } 
};

struct StruUltProbeInfo:ZeroStruct<StruUltProbeInfo>
{
    uint32_t is_update;
    double uss_measurement_time;
    spi_SendData_type uss_data_info;
};

struct SGW_C_3F0:ZeroStruct<SGW_C_3F0>
{
    bool DKM_BLEAPASwReq;
    bool DKM_BLEAPPRPAParkReq;
    uint32_t DKM_BLEConnectSt;
    uint32_t DKM_BLEAPAModeSelectReq;
    uint32_t DKM_BLEAPPAPAMoveReq;
    double measurement_time;
};

struct SGW_C_346:ZeroStruct<SGW_C_346>
{
    uint32_t VIUL_PEPS_RKECommand2;
    uint32_t VIUL_PEPS_EngineStartMode;
    double measurement_time;
};

struct IBC_133:ZeroStruct<IBC_133>
{
    uint32_t IBC_BrakePedalSt;
    double IBC_BrkFricTotAtWhlsTorq;
    double measurement_time;
};

struct StruAppRpaReqInfo:ZeroStruct<StruAppRpaReqInfo>
{
    uint32_t is_update;
    double rpa_req_measurement_time;
    SGW_C_3F0 msg_SGW_C_3F0;
};

struct StruKeyRpaReqInfo:ZeroStruct<StruKeyRpaReqInfo>
{
    uint32_t is_update;
    SGW_C_346 msg_SGW_C_346;
};

struct StruInputAppRpaResp:ZeroStruct<StruInputAppRpaResp>
{
    uint32_t mpa_ready_st;
    uint32_t rpa_app_disp_start_req;
    uint32_t remote_park_resp;
    uint32_t mpa_park_notice;
    uint32_t exit_req;
    uint32_t parking_pode_st;
};

struct StruInputKeyRpaResp:ZeroStruct<StruInputKeyRpaResp>
{
    uint32_t rpa_app_disp_start_req;
    uint32_t mpa_ready_st;
    uint32_t parking_pode_st;
    uint32_t exit_req;
};

enum class ShakeHandStatus:int{
    DEFAULT=0,
    WAITSHAKEHANDREQ=1,
    SHAKEHANDSUS=2,
    SHAKEHANDFAILED=3,
    WAITSHAKEHANDREQQUIT=4,
    SHAKEHANDQUITSUS=5,
    SHAKEHANDQUITFAILED=6
};

enum class ShakeHandFailedRes:int{
    DEFAULT=0,
    SOCEXCEPTION=1,
    SHAKEHANDOVERTIME=2,
    SINGLEEXCEPTION=3,
    NOTSTANDSTILL=4,
    EPSFAILED=5,
    ApaNrakSysLongictlSt=6,
    IBCFAILED=7,
    WAITSOCHANDLE=8,
    WAITMCUHANDLE=9
};

enum class GearTransStatus:int{
    DEFAULT=0,
    GETTRANSGEARREQ=1,

};

enum class PDCU_ActualGear:int{
    P=0,
    R=1,
    N=2,
    D=3
};

enum class IBC_TargetGearReq:int{
    NOREQUEST=0,
    D=1,
    R=2,
    P=3,
    N=4,
    RESERVED1=5,
    RESERVED2=6,
    RESERVED3=7
};

enum class HAD_APA_ReqtTargetGearPosSt:uint32_t{
    NOREQUEST=0,
    P=1,
    R=2,
    N=3,
    D=4,
    RESERVED1=5,
    RESERVED2=6,
    Invalid=7
};


struct StruOutputDataChassis:ZeroStruct<StruOutputDataChassis>
{
    CommonBool::Enum LatSt_eps_valid;
    CommonErrorType::Enum LatSt_eps_err_type;
    EpsErrReason::Enum LatSt_eps_err_reason;

    double LatDa_steering_angle;
    double LatDa_steering_velocity;
    double LatDa_steering_torque;

    CommonBool::Enum LonSt_esp_valid;
    CommonErrorType::Enum LonSt_esp_err_type;
    EspErrReason::Enum LonSt_esp_err_reason;
    CommonBool::Enum LonSt_epb_valid;
    CommonErrorType::Enum LonSt_epb_err_type;
    EpbErrReason::Enum LonSt_epb_err_reason;
    CommonBool::Enum LonSt_vcu_valid;
    CommonErrorType::Enum LonSt_vcu_err_type;
    VcuErrReason::Enum LonSt_vcu_err_reason;
    IBCLSMCtrlFaultSts::Enum LonSt_ibc_lsm_ctrl_fault_sts;

    CommonBool::Enum LonDa_stand_still;
    CommonBool::Enum epb_sys_st;
    double LonDa_lon_acceleration;
    double LonDa_speed;
    double LonDa_cur_torque;
    double LonDa_min_torque;
    double LonDa_max_torque;
    uint32_t LonDa_gear_pos;
    double LonDa_ibcu_pressure;
    double LonDa_slope;
    uint32_t LonDa_sv_result;
    double LonDa_brake_torque;
    double LonDa_ack_distance;
    double LonDa_ack_velocity;
    CommonBool::Enum LonDa_pdcu_accel_peadal_valid;
    double LonDa_pdcu_accel_peadal_st;
};

struct StruOutputDataShakeHand:ZeroStruct<StruOutputDataShakeHand>
{
    uint32_t is_update;
    CommonBool::Enum lat_active;
    CommonErrorType::Enum lat_shakehand_err_type;
    LatShakeHandErrReason::Enum lat_shakehand_err_reason;
    CommonBool::Enum long_active;
    CommonErrorType::Enum long_shakehand_err_type;
    LonShakeHandErrReason::Enum long_shakehand_err_reason;
};

struct StruOutputDataWheelSpeedInfo:ZeroStruct<StruOutputDataWheelSpeedInfo>
{
    // uint32_t is_update;
    CommonBool::Enum valid;
    double wheelspeed_fl;
    double wheelspeed_fr;
    double wheelspeed_rl;
    double wheelspeed_rr;
    int wheelsign_fl;
    int wheelsign_fr;
    int wheelsign_rl;
    int wheelsign_rr;
    int wheel_edgessum_fl;
    int wheel_edgessum_fr;
    int wheel_edgessum_rl;
    int wheel_edgessum_rr;
    double measurement_time;
};

struct StruOutputDataWheelSpeedInfoQueu:ZeroStruct<StruOutputDataWheelSpeedInfoQueu>
{
    uint32_t is_update;
    std::queue<StruOutputDataWheelSpeedInfo> wheel_speed_info_queu;
};

struct StruOutputDataSystemCheck:ZeroStruct<StruOutputDataSystemCheck>
{
    CommonBool::Enum is_check_ok ;//车门状态等辅助信息
    SystemCheckErrReason::Enum check_err_reason ; //TODO: define enum
};

struct StruOutputDataVehicleBod:ZeroStruct<StruOutputDataVehicleBod>
{
    CommonBool::Enum right_turn_lamp_st ;
    CommonBool::Enum left_turn_lamp_st ;
    CommonBool::Enum rl_turn_lamp_st ;
};

struct StruOutputDataInsInfo:ZeroStruct<StruOutputDataInsInfo>
{
    CommonBool::Enum valid ;
    double lon_acceleration ;
    double lat_acceleration ;

};

struct StruInsGps
{
    double height;
    double time;
};
struct StruInsGnns
{
    double height;
    double week;
};
struct StruPoint3D
{
    double x;
    double y;
    double z;
};

struct StruOutputDataImuInfo:ZeroStruct<StruOutputDataImuInfo>
{
    uint32_t is_update;
    double measurement_time;
    float measurement_span;
    StruPoint3D linear_acceleration;
    StruPoint3D angular_velocity;

    StruPoint3D location;
    StruPoint3D rotation;
    StruInsGps ins_gps;
    StruInsGnns ins_gnns;
    int seq_num;
};

struct StruOutputDataImuInfoQueu:ZeroStruct<StruOutputDataImuInfoQueu>
{
    uint32_t is_update;
    std::queue<StruOutputDataImuInfo> imu_info_queu;
};

struct StruOutputDataSOCImuInfoQueu:ZeroStruct<StruOutputDataSOCImuInfoQueu>
{
    uint32_t is_update;
    std::queue<StruOutputDataImuInfo> soc_imu_info_queu;
};

struct StruChassisShiftGearInfo:ZeroStruct<StruChassisShiftGearInfo> 
{
    bool gear_trans_valid;
    uint32_t req_gear;
    uint32_t cur_gear;
};

struct StruAPAStatusAckInfo:ZeroStruct<StruAPAStatusAckInfo> 
{
    bool is_update;
    uint32_t scenario;
    uint32_t apa_status;
};

struct StruInputSystemCheckCmd:ZeroStruct<StruInputSystemCheckCmd>
{
    bool is_update;
    CommonBool::Enum request_check;
};

struct StruInputLatAndLongShakeHandCmd:ZeroStruct<StruInputLatAndLongShakeHandCmd>
{
    bool is_update;
    CommonBool::Enum activate_lat;
    CommonBool::Enum activate_long;
};

struct StruInputLatAndLongControlCmd:ZeroStruct<StruInputLatAndLongControlCmd>
{
    bool is_update;
    // CommonBool::Enum is_driving;
    double tar_velocity;
    double tar_distance;
    // CommonBool::Enum is_braking;
    uint32_t apa_failure_brake_mode;
    CommonBool::Enum is_steering;
    double tar_steer_angle;
    CommonBool::Enum put_gear;
    uint32_t tar_gear;
};

struct StruInputLatAndLongTrqControlCmd:ZeroStruct<StruInputLatAndLongTrqControlCmd>
{
    bool is_update;
    CommonBool::Enum is_driving;
    CommonBool::Enum is_braking;
    double tar_torque;
    double tar_deceleration;
    CommonBool::Enum is_steering;
    double tar_steer_angle;
    CommonBool::Enum put_gear;
    uint32_t tar_gear;
    double tar_acc;
    uint32_t lamp_ctl;
};

struct StruInputAEBController:ZeroStruct<StruInputAEBController>
{
    uint32_t brake_prefill_status;
    uint32_t hba_level;
    uint32_t fcw_level;
    uint32_t eba_status;
    double deceleration;
    uint32_t ego_car_stop;
};

struct IBC172WHLPLS:ZeroStruct<IBC172WHLPLS>
{
    double ibc_172_measurement_time;
    uint32_t ibc_sum_edge_fl_wss; //IBC_SumOfEdgeFLWSS (0x172) 左前
    uint32_t ibc_sum_edge_fr_wss; //IBC_SumOfEdgeFRWSS (0x172)  右前
    uint32_t ibc_sum_edge_fl_wss_vld; //IBC_SumOfEdgeFLWSSValid (0x172) 
    uint32_t ibc_sum_edge_fr_wss_vld; //IBC_SumOfEdgeFRWSSValid (0x172) 
    uint32_t ibc_whl_diriection_fl_vld; //IBC_WheelDirectionFLValid (0x172) 
    uint32_t ibc_whl_diriection_fr_vld; //IBC_WheelDirectionFRValid (0x172) 
    uint32_t ibc_whl_diriection_fl_st; //IBC_WheelDirectionFLSt (0x172) 0x0: INVALID 0x1: FORWARD 0x2: REVERSE 0x3: STOP
    uint32_t ibc_whl_diriection_fr_st; //IBC_WheelDirectionFRSt (0x172) 
    uint32_t ibc_sum_edge_rl_wss; //IBC_SumOfEdgeRLWSS  (0x172) 左后
    uint32_t ibc_sum_edge_rr_wss; //IBC_SumOfEdgeRRWSS  (0x172) 右后
    uint32_t ibc_sum_edge_rl_wss_vld; //IBC_SumOfEdgeRLWSSValid (0x172) 
    uint32_t ibc_sum_edge_rr_wss_vld; //IBC_SumOfEdgeRRWSSValid (0x172) 
    uint32_t ibc_whl_diriection_rl_vld; //IBC_WheelDirectionRLValid  (0x172) 
    uint32_t ibc_whl_diriection_rr_vld; //IBC_WheelDirectionRRValid (0x172) 
    uint32_t ibc_whl_diriection_rl_st; //IBC_WheelDirectionRLSt (0x172) 
    uint32_t ibc_whl_diriection_rr_st; //IBC_WheelDirectionRRSt (0x172) 
    uint32_t ibc_172_rc;//RollingCounter172
};

struct SWGC119IMU:ZeroStruct<SWGC119IMU>
{
    double swgc_119_measurement_time;
    double acu_yaw_rate_st;//ACU_YawRateSt (0x119)
    double acu_lateral_accelaration_st;//ACU_LateralAccelarationSt (0x119)
    uint32_t acu_longitdaccler_valid;//ACU_LongitdAcclerValid (0x119)
    uint32_t acu_lateral_accler_valid;//ACU_LateralAccelareValid (0x119)
    uint32_t acu_yaw_rate_valid;//ACU_YawrateValiditySt (0x119)
    double acu_longitud_acceleration_st;//ACU_LongitudAccelerationSt (0x119)
    uint32_t swgc_119_rc;//RollingCounter119
};

// 仅用于HIL测试，借用SWGC710IMU来传递carmaker中的imu信息
struct SWGC710IMU:ZeroStruct<SWGC710IMU>
{
    double sgwc_710_measurement_time;
    double HIL_RollRateSt; // angular velocity along x axis
    double HIL_LongitudeAccelSt; // linear acceleration along x axis
    double HIL_PitchRateSt; // angular velocity along y axis
    double HIL_LateralAccelSt; // linear acceleration along y axis
    double HIL_YawRateSt; // angular velocity along z axis
    double HIL_VerticalAccelSt; // linear acceleration along z axis
};

struct SOCIMU:ZeroStruct<SOCIMU>
{
    double imu_measurement_time;
    StruPoint3D linear_acceleration;//IMU
    StruPoint3D angular_velocity;//IMU
};


struct Point2d
{
    double x;
    double y;
};

struct StruNewFareoRadar:ZeroStruct<StruNewFareoRadar>
{
    int32_t frame_id;
    int32_t object_id;

    Point2d position;
    float accelerate;
    float velocity;

    float length;
    float length_covariance;

    float width;
    float width_covariance;

    int32_t track_age;
    float exist_probablity;

    float orientation_rad;
    float orientation_covariance;

    float yaw_rate;
    float yaw_rate_covariance;

    float covariance[5];
    double measurement_time;
};

struct StruNewFareoRadars:ZeroStruct<StruNewFareoRadars>
{
    uint32_t radar_fc_is_update;
    std::queue<StruNewFareoRadar> radar_fc;
    double radar_fc_measurement_time;
    uint32_t radar_fl_is_update;
    std::queue<StruNewFareoRadar> radar_fl;
    double radar_fl_measurement_time;
    uint32_t radar_fr_is_update;
    std::queue<StruNewFareoRadar> radar_fr;
    double radar_fr_measurement_time;
    uint32_t radar_rl_is_update;
    std::queue<StruNewFareoRadar> radar_rl;
    double radar_rl_measurement_time;
    uint32_t radar_rr_is_update;
    std::queue<StruNewFareoRadar> radar_rr;
    double radar_rr_measurement_time;
};

struct StruIOChassisInfoDebug:ZeroStruct<StruIOChassisInfoDebug>
{
    double velocity; 
    //shake hand
    uint32_t takeover_stat;
    uint32_t shakehand_valid;
    uint32_t eps_shakehand_sts;
    uint32_t ibc_shakehand_sts;

    uint32_t lat_fail_type;
    uint32_t lon_fail_type;

    //transGEAR
    uint32_t trans_result;
    uint32_t sv_result;
    double ack_distance;
    double ack_velocity;

    // l2 ctl feedback
    double tar_torque;
    double tar_deceleration;
    double angle;
    uint32_t lamp_ctl;

    //control EPS
    uint32_t eps_b0_valid;//0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double eps_b0_measurement_time;
    uint32_t eps_apa_status;//EPS_APASt (0xB0)
    uint32_t eps_apa_function_mode_status;//EPS_APAFuncModeSt (0xB0)
    uint32_t eps_apa_prohibbited_reason;//EPS_APAProhibitedResaon (0xB0)
    uint32_t eps_b0_rc;//EPS_APARollingCountB0
    //value
    uint32_t eps_17a_valid;
    double eps_17a_measurement_time;
    uint32_t eps_steering_angle_flag; //EPS_SteeringAngleFlag (0x17A)
    double eps_steering_angle; //EPS_SteeringAngle (0x17A)
    uint32_t eps_steering_torque_sensor_st;//EPS_SteeringTorqueSensorSt (0x17A)
    double eps_steering_torque_value;//EPS_SteeringTorqueValue (0x17A)
    uint32_t eps_17a_rc;//EPS_RollingCount17A

    uint32_t sas_a5_valid;
    double sas_a5_measurement_time;
    double sas_steering_angle;//TAS_SAS_SteeringAngle (0xA5)
    double sas_steering_rotspd; //TAS_SAS_SteeringRotSpd (0xA5)
    uint32_t sas_calibrated_st; //TAS_SAS_CalibratedSt (0xA5)
    uint32_t sas_failure_st; //TAS_SAS_SASFailureSt (0xA5)
    uint32_t sas_a5_rc;//TAS_SAS_RollingCountA5
    
    //control IBC
    uint32_t ibc_143_valid;
    double ibc_143_measurement_time;
    uint32_t ibc_apa_braksyslongictlst;//IBC_APABrakSysLongictlSt (0x143)
    uint32_t ibc_apa_brakemodest;//IBC_APABrakeModeSt (0x143) IBC正在执行的刹车模式
    uint32_t ibc_lsm_ctrl_fault_st;//IBC_LSMCtrlFaultSt (0x143)
    uint32_t req_gear_fb;//IBC_TargetGearReq (0x143)//01234 nDRPN
    uint32_t ibc_143_rc;//RollingCounter143

    //value
    uint32_t ibc_a2_valid;
    double ibc_a2_measurement_time;
    uint32_t ibc_vehicle_speed_valid; //IBC_VehicleSpeedValid(0xA2)
    double ibc_vehicle_speed; //IBC_vehicleSpeed(0xA2)
    uint32_t ibc_vehicle_standstill_st;//IBC_VehicleStandstillSt(0xA2)
    uint32_t ibc_vehicle_standstill_st_valid;////IBC_VehicleStandstillStValid(0xA2)
    uint32_t ibc_vehicle_driving_direction;//IBC_VehicleDrivingDirection(0xA2)
    uint32_t ibc_esc_off_st;//IBC_ESCoffSt(0xA2)
    uint32_t ibc_esc_fault_st;//IBC_ESCFaultSt(0xA2)
    uint32_t ibc_a2_rc;//RollingCounter0A2

    uint32_t ibc_12c_valid;
    double ibc_12c_measurement_time;
    uint32_t ibc_epb_st;//IBC_EPBSt (0x12C)
    double ibc_slope;//IBC_Slope (0x12C)
    uint32_t ibc_slope_st;//IBC_SlopeSt (0x12C)
    uint32_t ibc_slope_high_warn;//IBC_SlopHighWarn (0x12C) not use
    uint32_t ibc_12c_rc;//RollingCounter31A

    // uint32_t ibc_pedel_travel_sensor_st;//
    // uint32_t ibc_brake_pedal_st;// IBC_BrakePedalSt (0x133)
    uint32_t ibc_11d_valid;
    double ibc_11d_measurement_time;
    double ibc_plunger_pressure;//IBC_PlungerPressure (0x11D)
    uint32_t ibc_pedal_travel_sensor;//IBC_PedalTravelSensor (0x11D)
    uint32_t ibc_pedal_travel_sensor_st;//IBC_PedalTravelSensorSt (0x11D)

    uint32_t pdcu_1c8_valid;
    double pdcu_1c8_measurement_time;
    uint32_t cur_gear;//PDCU_ActualGear(0x1C8)//0123 PRND
    double pdcu_accel_peadal_st; //PDCU_AccelPedalSt(0x1C8)
    uint32_t pdcu_accel_peadal_valid; //PDCU_AccelPedalValid(0x1C8)
    uint32_t pdcu_1c8_rc;//RollingCounter1C8

    uint32_t pdcu_ff_valid;
    double pdcu_ff_measurement_time;
    uint32_t pdcu_axle_torque_st;//PDCU_AxleTorqueSt(0xFF)
    int32_t pdcu_realized_powertrain_whltp;//PDCU_RealizedPowertrainWhlTq(0xFF)
    uint32_t pdcu_acc_fun_ihibition_req;//PDCU_ACCFunIhibitionReq(acc 0xFF)
    uint32_t pdcu_drive_asisttacc_st;//PDCU_DriveAsiSttACCSt(acc 0xFF)
    uint32_t pdcu_acc_response_st;//PDCU_ACCResponseSt(acc 0xFF)
    uint32_t pdcu_acc_control_available_st;//PDCU_ACCcontrolAvailableSt(acc 0xFF)
    uint32_t pdcu_whether_acc_req_realized_st;//PDCU_WhetherACCReqRealizedSt(acc 0xFF)
    uint32_t pdcu_ff_rc;//RollingCounter0FF

    //speed
    uint32_t ibc_101_valid;
    double ibc_101_measurement_time;
    double ibc_whlspd_rl;//IBC_wheelSpeedRL (0x101)左后
    uint32_t ibc_whlspd_rl_vld;//IBC_wheelSpeedRLValid(0x101)
    double ibc_whlspd_rr;//IBC_wheelSpeedRR (0x101)右后
    uint32_t ibc_whlspd_rr_vld;//IBC_wheelSpeedRRValid(0x101)
    double ibc_whlspd_fl;//IBC_wheelSpeedFL (0x101)左前
    uint32_t ibc_whlspd_fl_vld;//IBC_wheelSpeedFLValid(0x101)
    double ibc_whlspd_fr;//IBC_wheelSpeedFR (0x101)右前
    uint32_t ibc_whlspd_fr_vld;//IBC_wheelSpeedFRValid (0x101)
    uint32_t ibc_101_rc;//RollingCounter101

    uint32_t ibc_172_valid;
    std::queue<IBC172WHLPLS> ibc_172_pls_vec;
    uint32_t ibc_whl_diriection_rl_st; //IBC_WheelDirectionRLSt (0x172) 
    uint32_t ibc_whl_diriection_rr_st; //IBC_WheelDirectionRRSt (0x172) 

    //IMU
    uint32_t imu_valid;
    std::queue<SOCIMU> soc_imu_vec;

    //can acc
    uint32_t swgc_119_valid;
    std::queue<SWGC119IMU> swgc_119_imu_vec;
    uint32_t swgc_710_valid;
    std::queue<SWGC710IMU> swgc_710_imu_vec;
    double acu_longitud_acceleration_st;

    //uss
    uint32_t uss_valid;
    double uss_measurement_time;
    spi_SendData_type uss_data_info;

    //vehicle body can
    uint32_t pdcu_fe_valid;
    double pdcu_fe_measurement_time;
    uint32_t pdcu_drive_ready_st;//PDCU_DriveReadySt 0不允许行车 1允许行车

    uint32_t swgc_23a_valid;
    double swgc_23a_measurement_time;
    uint32_t viul_lfdoorswst;//VIUL_LFDoorSwSt 左前车门 0关闭 1开启
    uint32_t viul_rfdoorswst;//VIUL_RFDoorSwSt 右前车门
    uint32_t viul_rrdoorswst;//VIUL_RRDoorSwSt 右后车门
    uint32_t viul_lrdoorswst;//VIUL_LRDoorSwSt 左后车门
    uint32_t viul_left_light_st;//VIUL_LetfligthSt 左转信号 0灭 1亮
    uint32_t viul_right_light_st;//VIUL_RightligthSt 右转信号灯
    uint32_t viul_high_beam_st;//VIUL_HighBeamSt 远光灯
    uint32_t viul_low_beam_st;//VIUL_LowBeamSt 近光灯
    uint32_t viul_hazard_lamp_st;//VIUL_HazardLampSt 危险报警灯状态
    uint32_t viul_enghood_unlock_warn;//VIUL_EngHoodUnlockWarm 发动机罩未锁紧报警信号 0:锁紧 1:未锁紧，报警

    uint32_t swgi_30c_valid;
    double swgi_30c_measurement_time;
    uint32_t acu_driverbelt_swsigst;//ACU_DriverBeltSwSigSt 0安全带系上 1安全带未系 2安全带故障 3 预留
    uint32_t acu_passenger_belt_st; //ACU_PassengerBeltSt
    uint32_t acu_rl_belt_st; //ACU_RLBeltSt
    uint32_t acu_rr_belt_st; //ACU_RRBeltSt

    uint32_t swgi_304_valid;
    double swgi_304_measurement_time;
    uint32_t pot_backdoor_posst;//POT_BackDoorPosSt 0全锁 1半锁 2半开 3全开

    uint32_t swgi_33d_valid;
    double swgi_33d_measurement_time;
    uint32_t viul_rearvmirrorfb_st;//VIUL_WashModeRearMirrorFbSt 0后视镜折叠 1后视镜展开

    uint32_t msg_SGW_C_3F0_valid;
    SGW_C_3F0 msg_SGW_C_3F0;

    uint32_t msg_SGW_C_346_valid;
    SGW_C_346 msg_SGW_C_346;
    
    uint32_t msg_IBC_133_valid;
    IBC_133 msg_IBC_133;

    //ack apa status
    uint32_t spa_st_valid;
    uint32_t scenario;
    uint32_t apa_status;

    //acc signal
    uint32_t eps_lka_166_valid;
    uint32_t eps_lka_driver_override_st; // EPS_LKA_DriverOverrideSt(0x166)
    uint32_t eps_lka_st; // EPS_LKASt(0x166)
};

//ican 30c 33d 304
struct StruVehicleBodyInfo:ZeroStruct<StruVehicleBodyInfo>
{
    uint32_t speed_is_update;
    double speed_measurement_time;
    double ibc_vehicle_speed;//IBC_vehicleSpeed(0xA2)

    uint32_t swgi_33d_is_update;//ican
    uint32_t swgi_33d_is_frist;
    double swgi_33d_measurement_time;
    uint32_t viul_rearvmirrorfb_st;//VIUL_WashModeRearMirrorFbSt 0后视镜折叠 1后视镜展开

    uint32_t swgc_23a_is_update;
    uint32_t swgc_23a_is_frist;
    double swgc_23a_measurement_time;
    uint32_t viul_lfdoorswst;//VIUL_LFDoorSwSt 左前车门 0关闭 1开启
    uint32_t viul_rfdoorswst;//VIUL_RFDoorSwSt 右前车门
    uint32_t viul_rrdoorswst;//VIUL_RRDoorSwSt 右后车门
    uint32_t viul_lrdoorswst;//VIUL_LRDoorSwSt 左后车门

    uint32_t swgi_30c_is_update;//ican
    uint32_t swgi_30c_is_frist;
    double swgi_30c_measurement_time;
    uint32_t acu_driverbelt_swsigst;//ACU_DriverBeltSwSigSt 0安全带系上 1安全带未系 2安全带故障 3 预留

    uint32_t pdcu_fe_is_update;
    uint32_t pdcu_fe_is_frist;
    double pdcu_fe_measurement_time;
    uint32_t pdcu_drive_ready_st;//PDCU_DriveReadySt 0不允许行车 1允许行车

    uint32_t swgi_304_is_update;//ican
    uint32_t swgi_304_is_frist;
    double swgi_304_measurement_time;
    uint32_t pot_backdoor_posst;//POT_BackDoorPosSt 0全锁 1半锁 2半开 3全开

    double pedal_travel_measurement_time;
    uint32_t pedal_travel_sensor;//IBC_PedalTravelSensor (0x11D)
    double slope;//IBC_Slope (0x12C)
};

struct StruInfoByLocation:ZeroStruct<StruInfoByLocation>
{
    uint32_t gear_is_update;
    double pdcu_1c8_measurement_time;
    uint32_t cur_gear;//PDCU_ActualGear(0x1C8)//0123 PRND

    uint32_t standstill_is_update;
    double ibc_a2_measurement_time;
    CommonBool::Enum standstill_st;//IBC_VehicleStandstillSt(0xA2)

    uint32_t steer_angle_is_update;
    double sas_a5_measurement_time;
    double sas_steering_angle;//TAS_SAS_SteeringAngle (0xA5)
    double sas_steering_rotspd; //TAS_SAS_SteeringRotSpd (0xA5)
};

enum class IBC_BrakePedalSt:uint32_t
{
    NOT_PRESSED_INIT_DEFAULT = 0,
    PRESSED = 1,
    RESERVED = 2,
    ERROR = 3
};

enum class EPS_APASt:uint32_t
{
Unavailable = 0,
Standby = 1,
ControlInProcess = 2,
Abort = 3
};

struct IVI_160
{
    uint32_t HAD_APA_LongitudinalCtrlReq;
    uint32_t HAD_APA_SystemSt;
    uint32_t HAD_APA_ReqToStopDst;
    uint32_t HAD_APA_ReqtTargetGearPosSt;//01234 nPRND
    uint32_t HAD_APA_FailureBrakeModeSt;
    double HAD_APA_BrakeTargetMaxSpeed;
    uint32_t HAD_APA_EmergencySt;
    uint32_t HAD_APA_Emergency_Valid;
    uint32_t HAD_APA_ESC_Funmode;
    double HAD_APA_ReqEPSTargetAngleReq;
    uint32_t HAD_APA_ControlEPSReq;
    uint32_t HAD_EPS_FuncModeReq;
};

struct IVI_4C1
{

};

struct IVI_35A
{
    uint32_t HAD_APA_AutomaticParkingPodeSt;
    uint32_t HAD_APA_TurnLightsCommandReq;
    uint32_t HAD_APA_LightControlValid;
    uint32_t HAD_APA_OnOffDisp;
    uint32_t HAD_APA_UPAErrorDisp;
    uint32_t HAD_APA_MPAPArkNoticeDisp;
    uint32_t HAD_APA_MPAStFeedbackSt;
    uint32_t HAD_APA_MPAReadyFbSt;
    uint32_t RollingCounter35A;
    uint32_t CheckSum35A;
};

enum class ErrorLevel:uint32_t{
    Available = 0,//no errorr
    Warn = 1,//警告，对手件不退出握手，FAPA暂时无反应
    GeneralError = 2,//一般错误，对手件主动退出握手，FAPA退出握手
    SeriousError = 3//严重错误，对手件主动退出握手，一般本次点火周期不可恢复
};

enum class IBC_LSMCtrlFaultSts:uint32_t{
    NoError = 0, //无故障
    VehIVIleBlocked = 1, //车辆受阻
    UnexpectedGearPosition = 2, //档位不正确
    UnexpectedEPB_Action = 3, //EPB动作
    UnexpectedTravelDistance = 4, //距离过远
    UnexpectedSpeedLimitation = 5, //速度超限
    UnexpectedGearIntervention = 6, //换挡干预 
    UnexpectedDriverAcceleration = 7, //驾驶员加速
    UnexpectedEngineFailure = 8, //EMS故障（PDCU故障)
    UnexpectedTCUFailure = 9, //TCU故障
    UnexpectedAPAFailure = 10, //FAPA/LAPA故障
    StatIVISlopeOutofRange = 11, //坡度超限
    ESCFailure = 12, //ESC故障
    TimeOut = 13, //时长超限 
    DriverUnpresent = 14, //驾驶员不在
    Reserved = 15, //保留
    BrakeTakeover = 16 //制动接管
};

enum class EPS_APAProhibitedResaon:uint32_t{
    NoControlInterruption = 0, //未中断泊车转向控制
    PickUpDriver = 1, //驾驶员干预
    VehicleSpeedTooHigh = 2, //车速过高
    AngularErrorTooHigh = 3, //转角差值过大
    ObstacleAtTheWheel = 4, //方向盘转不动了
    EPSFailure = 5, //EPS内部故障
    DAEthermalSafetyCatch = 6, //EPS过热 
    OtherFault = 7, //其他故障
};

//acc
struct StruVehicleSignal{
    uint32_t turn_signal;
    bool high_beam;
    bool low_beam;
    bool horn;
    bool emergency_light;

    CommonBool::Enum standstill_st;//IBC_VehicleStandstillSt(0xA2)
    CommonBool::Enum epb_st;

    uint32_t lf_doorsw_st; //VIUL_LFDoorSwSt 左前车门 0关闭 1开启
    uint32_t rf_doorsw_st; //VIUL_RFDoorSwSt 右前车门
    uint32_t rr_doorsw_st; //VIUL_RRDoorSwSt 右后车门
    uint32_t lr_doorsw_st; //VIUL_LRDoorSwSt 左后车门
    uint32_t pot_backdoor_posst; // 0全锁 1半锁 2半开 3全开
    uint32_t pot_enginehood_posst; // 0关闭 1开启

    uint32_t driverbelt_swsig_st; //0安全带系上 1安全带未系 2安全带故障 3 预留
    uint32_t passenger_belt_st; 
    uint32_t rl_belt_st; 
    uint32_t rr_belt_st; 
    uint32_t drive_ready_st; // 0不允许行车 1允许行车
};
struct StruVehicleSpeed{
    double vehicle_speed;
    double wheel_speed_fl;
    double wheel_speed_fr;
    double wheel_speed_rl;
    double wheel_speed_rr;
    double instrument_vehicle_speed;
    double velocity_vector;
};
struct StruAcceleration{
    double longitudinal;
    double lateral;
};
struct StruSteeringSystem{
    double steering_wheel_angle;
    double steering_wheel_rate;
    double steering_wheel_torque;
    double steering_angle;  
};
struct StruChassis:ZeroStruct<StruChassis>
{
    uint32_t driving_mode;
    uint32_t gear_locationb;
    StruVehicleSignal vehicle_signal;
    StruVehicleSpeed vehicle_speed;
    double maximum_user_speed;
    // 
    double throttle_perceptage;
    double brake_percentage;
    StruAcceleration acceleration;
    double yaw_rate;
    StruSteeringSystem steering_system;
    uint32_t time_gap;
    uint32_t cur_torque;
    double slope_estimation;
    double mass_estimation;
};


struct StrucNewRadarData:ZeroStruct<StrucNewRadarData>
{
    //radar fc/fl/fr/rl/rr
    uint32_t radar_fc_valid;
    std::queue<StruNewFareoRadar> radar_fc;
    double radar_fc_measurement_time;
    uint32_t radar_fl_valid;
    std::queue<StruNewFareoRadar> radar_fl;
    double radar_fl_measurement_time;
    uint32_t radar_fr_valid;
    std::queue<StruNewFareoRadar> radar_fr;
    double radar_fr_measurement_time;
    uint32_t radar_rl_valid;
    std::queue<StruNewFareoRadar> radar_rl;
    double radar_rl_measurement_time;
    uint32_t radar_rr_valid;
    std::queue<StruNewFareoRadar> radar_rr;
    double radar_rr_measurement_time;
};

//TODO:根据消息收到的方式配置
struct StruRadarDetection:ZeroStruct<StruRadarDetection>
{
    double CoGRange_NI_YY; //CoGRange_NI_YY
    double CoGDoppler_NI_YY; //CoGDoppler_NI_YY
    uint32_t Reserve1_1bit_NI_YY; //Reserve1_1bit_NI_YY
    uint32_t PowerDB_NI_YY; //PowerDB_NI_YY
    uint32_t SNRdB_NI_YY; //SNRdB_NI_YY
    double StdAzimuth_NI_YY; //StdAzimuth_NI_YY
    double Azimuth_NI_YY; //Azimuth_NI_YY
    uint32_t Beam_NI_YY; //Beam_NI_YY
    uint32_t NoInfrastructure_NI_YY; //NoInfrastructure_NI_YY
    uint32_t ValidXBeam_NI_YY; //ValidXBeam_NI_YY
};

struct StruRadarCANDetection:ZeroStruct<StruRadarCANDetection>
{
    double CoGRange_NI_0; //CoGRange_NI_YY
    double CoGDoppler_NI_0; //CoGDoppler_NI_YY
    uint32_t Reserve1_1bit_NI_0; //Reserve1_1bit_NI_YY
    uint32_t PowerDB_NI_0; //PowerDB_NI_YY
    uint32_t SNRdB_NI_0; //SNRdB_NI_YY
    double StdAzimuth_NI_0; //StdAzimuth_NI_YY
    double Azimuth_NI_0; //Azimuth_NI_YY
    uint32_t Beam_NI_0; //Beam_NI_YY
    uint32_t NoInfrastructure_NI_0; //NoInfrastructure_NI_YY
    uint32_t ValidXBeam_NI_0; //ValidXBeam_NI_YY
    double CoGRange_NI_1; //CoGRange_NI_YY
    double CoGDoppler_NI_1; //CoGDoppler_NI_YY
    uint32_t Reserve1_1bit_NI_1; //Reserve1_1bit_NI_YY
    uint32_t PowerDB_NI_1; //PowerDB_NI_YY
    uint32_t SNRdB_NI_1; //SNRdB_NI_YY
    double StdAzimuth_NI_1; //StdAzimuth_NI_YY
    double Azimuth_NI_1; //Azimuth_NI_YY
    uint32_t Beam_NI_1; //Beam_NI_YY
    uint32_t NoInfrastructure_NI_1; //NoInfrastructure_NI_YY
    uint32_t ValidXBeam_NI_1; //ValidXBeam_NI_YY
    double CoGRange_NI_2; //CoGRange_NI_YY
    double CoGDoppler_NI_2; //CoGDoppler_NI_YY
    uint32_t Reserve1_1bit_NI_2; //Reserve1_1bit_NI_YY
    uint32_t PowerDB_NI_2; //PowerDB_NI_YY
    uint32_t SNRdB_NI_2; //SNRdB_NI_YY
    double StdAzimuth_NI_2; //StdAzimuth_NI_YY
    double Azimuth_NI_2; //Azimuth_NI_YY
    uint32_t Beam_NI_2; //Beam_NI_YY
    uint32_t NoInfrastructure_NI_2; //NoInfrastructure_NI_YY
    uint32_t ValidXBeam_NI_2; //ValidXBeam_NI_YY
    double CoGRange_NI_3; //CoGRange_NI_YY
    double CoGDoppler_NI_3; //CoGDoppler_NI_YY
    uint32_t Reserve1_1bit_NI_3; //Reserve1_1bit_NI_YY
    uint32_t PowerDB_NI_3; //PowerDB_NI_YY
    uint32_t SNRdB_NI_3; //SNRdB_NI_YY
    double StdAzimuth_NI_3; //StdAzimuth_NI_YY
    double Azimuth_NI_3; //Azimuth_NI_YY
    uint32_t Beam_NI_3; //Beam_NI_YY
    uint32_t NoInfrastructure_NI_3; //NoInfrastructure_NI_YY
    uint32_t ValidXBeam_NI_3; //ValidXBeam_NI_YY
    double CoGRange_NI_4; //CoGRange_NI_YY
    double CoGDoppler_NI_4; //CoGDoppler_NI_YY
    uint32_t Reserve1_1bit_NI_4; //Reserve1_1bit_NI_YY
    uint32_t PowerDB_NI_4; //PowerDB_NI_YY
    uint32_t SNRdB_NI_4; //SNRdB_NI_YY
    double StdAzimuth_NI_4; //StdAzimuth_NI_YY
    double Azimuth_NI_4; //Azimuth_NI_YY
    uint32_t Beam_NI_4; //Beam_NI_YY
    uint32_t NoInfrastructure_NI_4; //NoInfrastructure_NI_YY
    uint32_t ValidXBeam_NI_4; //ValidXBeam_NI_YY
    double measurement_time;
};

enum class RadarPosition : int32_t {
    FC = 0, // 前中
    FL,     // 前左
    FR,     // 前右
    RL,     // 后左
    RR,     // 后右
};

}

#endif

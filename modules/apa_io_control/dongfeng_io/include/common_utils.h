#ifndef __COMMON_UTILS__
#define __COMMON_UTILS__
#pragma once

#include <sys/time.h>
#include "json.hpp"
#include "cyber/common/log.h"
#include "common_struct.h"
#include "can_api.h"
namespace io_server{

std::string GetTimeInHHMMSSmmm();
uint64_t GetTimeInMillis();
int ParseJsonString(const std::string& input, nlohmann::json& out_json);
double GetTimeS(struct timeval* pt);

uint32_t commonGearToPDCUGear(uint32_t common_gear);
uint32_t PDCUGearToCommonGear(uint32_t pdcu_gear);
uint32_t commonGearToIBCGear(uint32_t common_gear);
uint32_t IBCGearToCommonGear(uint32_t ibc_gear);
uint32_t commonGearToAPAGear(uint32_t common_gear);
uint32_t APAGearToCommonGear(uint32_t apa_gear);
int32_t APAToWMWheelDir(int32_t dir);

template <typename T>
bool GetSignalValue(struct veh_signal *signal, T &value) {
  veh_signal_value physical_value;
  signal_status signal_status = signal->GetValue.fpGetter(&physical_value, nullptr);
  if (strcmp(signal->sig_type, "float") == 0) {
    value = physical_value.val_float;
  } else if (strcmp(signal->sig_type, "int32_t") == 0) {
    value = physical_value.val_int32_t;
  } else if (strcmp(signal->sig_type, "uint32_t") == 0) {
    value = physical_value.val_uint32_t;
  } else if (strcmp(signal->sig_type, "int64_t") == 0) {
    value = physical_value.val_int64_t;
  } else if (strcmp(signal->sig_type, "uint64_t") == 0) {
    value = physical_value.val_uint64_t;
  } else {
    // printf("get signal value error:%s\n", signal->sig_name);
    AINFO << "get signal value error:" << signal->sig_name;
    return false;
  }
  return true;
}

}

#endif

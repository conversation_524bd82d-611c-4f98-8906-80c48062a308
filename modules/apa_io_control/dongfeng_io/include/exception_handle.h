#pragma once
#include "common_utils.h"
#include "thread_safe_queue.hpp"
#include "singleton.hpp"
#include "spi_rpc.h"
#include "uart_rpc.h"
#include "singleton.hpp"
#include "exception_type.h"
#include "thirdparty/recommend_protocols/common/proto/exception.pb.h"
#include "config_data.h"

#include "cyber/cyber.h"
#include "cyber/node/writer.h"
using apollo::cyber::Writer;
using rainbowdash::common::ExceptionPtr;

using namespace exception;

namespace io_server {
namespace dongfeng{
class ExceptionHandle :public Singleton<ExceptionHandle, true> {
      //exception handle
    public:
        ExceptionHandle() = default;
        bool initExceptionHandleData();
        void ExceptionHandler();
        void PubException(ExceptionType err_type, const std::string& name);

        void resendCachedMsg();
        void cacheMsg(std::shared_ptr<uart_down_exception_t_> msg);
        void removeCachedMsg(uint8_t cycle_count);
        bool IsMCUException(uint32_t exception_code);
        void enterStop();
        void enterStart();
        void enterECO();

    private:
        std::shared_mutex uart_msg_mutex_;
        std::unordered_map<uint8_t, std::shared_ptr<uart_down_exception_t_>> soc_exception_cached_map_;//这里只是soc发送过来的异常，也只有这里需要失败重发机制
        //Jira-Id: GT-14985 AIS-39728 档位干预
        //以下注册MCU异常会通过cyber通道发送异常，未被注册的MCU异常会被丢弃
        std::map<uint32_t, std::string> mcu_code_name_map_={
            {0x08000001, "INTERNAL_ERROR"},
            {0x08000002, "EXTERNAL_ERROR"},
            {0x08000003, "EPS_INTERVENTION"},
            {0x08000004, "IBC_UNEXPECTED_GEARINTERVENTION"},
            {0x08000005, "SOC_SYSTEM_EXCEPT"},
            {0x08000006, "USS_AVOID"},
            {0x08000007, "ACC_IBCBRAK_INTERVENTION"},
            {0x08000008, "ACC_EPS_INTERVENTION"}
        };

        void MCUExceptionHandler();
        void SOCExceptionHandler();

        //0: start_mode 1:stop_mode 2:eco_mode
        std::atomic<uint32_t> io_module_mode_{0};

    public:
        std::shared_ptr<Writer<ExceptionPtr>> pExceptionCaptureInfo = nullptr;
        std::shared_ptr<ThreadSafeQueue<uint32_t>> mcu_exception_queue_;//from mcu exception
        std::shared_ptr<ThreadSafeQueue<uint32_t>> soc_exception_queue_;//from soc exception，这个异常不需要处理，直接在回调中发给mcu
        
    };
}
}
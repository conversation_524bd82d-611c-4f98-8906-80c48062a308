#ifndef __JSONPARSER_H__
#define __JSONPARSER_H__
#pragma once

#include <string>
#include <vector>
#include "json/json.hpp"
#include <fstream>
#include <iostream>
#include "cyber/cyber.h"

namespace io_server{
    class JsonParser
    {
    public:
        JsonParser(const std::string& fileName):isValied_(false)
        {
            AINFO<< __func__<<": Config file path is: " << fileName.c_str() <<std::endl;
            _fileName = fileName;
            std::ifstream ifs(fileName);
            if (!ifs.is_open()) {
                AINFO<< __func__<<" E: Can not find config file: " << fileName.c_str();
                AINFO<< __func__<<" E: Can not find config file: " << fileName.c_str() <<std::endl;
                return;
            }
            ifs >> _info;
            ifs.close();
            AINFO<< __func__<<": " << _info.dump().c_str() <<std::endl;

            isValied_ = true;

        }

        bool isValid(){
            return isValied_;
        }

        nlohmann::json getJsonObject(){
            return _info;
        }

    private:
        /* data */
        nlohmann::json _info;
        bool isValied_;
        std::string _fileName;

    };
}
#endif


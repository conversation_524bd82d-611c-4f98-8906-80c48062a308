#ifndef __FILTER_CA_H__
#define __FILTER_CA_H__
#pragma once

#include "common_struct.h"

namespace io_server{
    class Filter
    {
        public:
            void SetFilterPara(uint32_t window_size, uint32_t index)
            {
                window_size_ = window_size;
                index_ = index;
            }

            double Run(double idata) 
            {
                #define FILTER_WINDOW_SIZE window_size_
                //#define FILTER_WINDOW_SIZE 20

                if (filter_buf.size() >= FILTER_WINDOW_SIZE) {
                    //double min_data = filter_buf[0];
                    std::vector<double> sorted_buf(filter_buf);
                    std::sort(sorted_buf.begin(), sorted_buf.end());
                    

                    for (uint32_t i=0; i < FILTER_WINDOW_SIZE-1; i++) {
                        //if (filter_buf[i+1] < min_data)
                            //min_data = filter_buf[i+1];

                        filter_buf[i] = filter_buf[i+1];
                    }
                    filter_buf[FILTER_WINDOW_SIZE-1] = idata;

                    //return min_data;
                    return sorted_buf[index_];

                } else {
                    filter_buf.push_back(idata);
                    return idata; 
                }

            }

        private:
            std::vector<double> filter_buf;
            uint32_t window_size_;
            uint32_t index_;
    };
}

#endif

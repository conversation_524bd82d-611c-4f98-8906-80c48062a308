#pragma once
#include <stdint.h>
#define MAX_VALUE_LEN 1024

enum class SpiMsgType { up_ans = 0, up_heartbeat, up_exception, up_handshake, up_vehctrl, };

//APA状态应答
typedef struct {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
  uint8_t scenario;
  uint8_t scenario_len;
  uint8_t scenario_value;
  uint8_t apa_status;
  uint8_t apa_status_len;
  uint8_t apa_status_value;
} __attribute__((packed)) apa_status_ack_t_;

// 应答包
typedef struct {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
  uint8_t result;
} __attribute__((packed)) spi_up_ans_t_;

// MCU链路检测请求
typedef struct {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
} __attribute__((packed)) spi_up_heartbeat_t_;

// MCU上报异常给8155
typedef struct _spi_up_exception_t {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
  uint8_t len;
  uint32_t value[MAX_VALUE_LEN];
} __attribute__((packed)) spi_up_exception_t_;

  // 握手应答
  typedef struct {
    uint8_t d_type;
    uint8_t sig_kind;
    uint8_t index;
    uint8_t eps_state;
    uint8_t ibc_state;
    uint8_t eps_error;
    uint8_t ibc_error;
  } __attribute__((packed)) spi_up_handshake_t_;

    // 握手应答
  typedef struct {
    uint8_t d_type;
    uint8_t sig_kind;
    uint8_t index;
    uint8_t eps_state;
    uint8_t ibc_state;
  } __attribute__((packed)) spi_up_mcu_event_t_;

typedef struct {
  float d_type;
  uint8_t ibc_state;
  uint8_t eps_error;
  uint8_t ibc_error;
} __attribute__((packed)) test_p;

// 控车信号应答
typedef struct {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
  uint8_t result;
  uint8_t reason;
  uint8_t sv_result;
  uint16_t distance;
  uint16_t velocity;
} __attribute__((packed)) spi_up_vehctrl_t_;

// 扭矩控车信号应答
typedef struct {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
  uint8_t drive_flag;
  uint8_t brake_flag;
  uint32_t tar_torque;
  uint16_t tar_deceleration;
  uint8_t angle_flag;
  uint16_t angle;
  uint8_t gear_flag;
  uint8_t gear;
  uint16_t tar_acc;
  uint8_t lamp_ctl;  
} __attribute__((packed)) spi_up_trq_vehctrl_t_;

typedef struct imu_sensors_data_t {
    /* sensor event type */
    int type;
    /* sequence number */
    uint64_t seq_id;
    /* time is in nanosecond */
    uint64_t timestamp;
    /* acceleration values m/s**2 */
    double acceleration_mg[3];
    /* gyroscope values rad/s */
    double angular_rate_mdps[3];
    /* temperature value degrees */
    double temperature;
}__attribute__((__packed__))imu_data_t;

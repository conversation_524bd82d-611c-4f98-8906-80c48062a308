#pragma once
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <list>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

// #include "interface/listener/pubsub_listener_interface.h"
// #include "ipc/megaipc_api.h"
#include "megaipc_api.h"
// #include "singleton/singleton.h"
// #include "structs/ipc_message.h"

using namespace megaipc;

namespace io_server {
class CacheMessage {
 public:
  CacheMessage(const std::string& topic, const IpcMessage& msg);
  CacheMessage(const std::string& topic, const uint8_t* data, uint32_t data_len,
               bool retain = true);
  CacheMessage();
  ~CacheMessage();
  CacheMessage(const CacheMessage& msg);
  CacheMessage(CacheMessage&& msg) noexcept;
  CacheMessage& operator=(const CacheMessage& msg);
  CacheMessage& operator=(CacheMessage&& msg) noexcept;

  IpcMessage toIpcMessage();
  uint8_t cyclecount() const { return cycle_count_; }
  uint8_t DataLen() const { return data_len_; }
  std::string topic() const { return topic_; }

 private:
  uint8_t getCyclecount(const uint8_t* data);

 private:
  std::vector<uint8_t> data_allocated_;
  uint8_t* data_;
  uint32_t data_len_;
  uint8_t cycle_count_;
  bool retain_;
  std::string topic_;
};
}  // namespace io_server

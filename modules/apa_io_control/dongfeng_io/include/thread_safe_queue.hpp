
#pragma once

#include <atomic>
#include <condition_variable>
#include <list>
#include <vector>
#include <mutex>
#include <queue>
#include <shared_mutex>
#include <utility>


namespace io_server {
namespace dongfeng{

template <typename T>
class ThreadSafeQueue {
 public:
  explicit ThreadSafeQueue(const std::size_t capacity);
  ~ThreadSafeQueue();
  void Enqueue(const T& element);
  void Enqueue(const T& element, bool* const discard);
  void EnqueueIfEmpty(const T& element);
  bool Dequeue(T* const element);
  bool WaitDequeue(T* const element);
  bool DequeueTimeout(uint64_t timeout_ns, T* const element);
  bool GetFrontAndBack(T* const front, T* const back);
  bool GetFront(T* const front);
  bool PopFront();
  bool GetBack(T* const back);
  std::size_t size() const;
  bool Empty() const;
  void BreakAllWait();
  bool IsBreak() const;
  bool ExtractAll(std::list<T>* const result);
  bool ExtractAll(std::vector<T>* const result);
  bool GetAll(std::list<T>* const result) const;
  void Clear();

 private:
  std::atomic<bool> break_all_wait_ = {false};
  mutable std::shared_mutex mutex_;
  std::list<T> queue_;
  std::condition_variable_any cv_;
  // 0 if no limit
  std::size_t capacity_ = 0;

 private:
  // DISALLOW_COPY_AND_ASSIGN(ThreadSafeQueue);
};

template <typename T>
ThreadSafeQueue<T>::ThreadSafeQueue(const std::size_t capacity) : capacity_(capacity) {
}

template <typename T>
ThreadSafeQueue<T>::~ThreadSafeQueue() {
  BreakAllWait();
}

template <typename T>
void ThreadSafeQueue<T>::Enqueue(const T& element) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  queue_.emplace_back(element);
  while (capacity_ > 0 && queue_.size() > capacity_) {
    queue_.pop_front();
  }
  cv_.notify_one();
}

template <typename T>
void ThreadSafeQueue<T>::Enqueue(const T& element, bool* const discard) {
  if (discard == nullptr) {
    return;
  }
  std::unique_lock<std::shared_mutex> lock(mutex_);
  *discard = false;
  queue_.emplace_back(element);
  while (capacity_ > 0 && queue_.size() > capacity_) {
    queue_.pop_front();
    *discard = true;
  }
  cv_.notify_one();
}

template <typename T>
void ThreadSafeQueue<T>::EnqueueIfEmpty(const T& element) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  if (queue_.empty()) {
    queue_.emplace_back(element);
    cv_.notify_one();
  }
}

template <typename T>
bool ThreadSafeQueue<T>::Dequeue(T* const element) {
  if (element == nullptr) {
    return false;
  }
  std::unique_lock<std::shared_mutex> lock(mutex_);
  if (queue_.empty()) {
    return false;
  }
  *element = std::move(queue_.front());
  queue_.pop_front();
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::WaitDequeue(T* const element) {
  if (element == nullptr) {
    return false;
  }
  std::unique_lock<std::shared_mutex> lock(mutex_);

  cv_.wait(lock, [this]() { return this->break_all_wait_ == true || !this->queue_.empty(); });

  if (break_all_wait_) {
    return false;
  }

  *element = std::move(queue_.front());
  queue_.pop_front();
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::DequeueTimeout(uint64_t timeout_ns, T* const element) {
  if (element == nullptr) {
    return false;
  }
  std::unique_lock<std::shared_mutex> lock(mutex_);

  cv_.wait_for(lock, std::chrono::nanoseconds(timeout_ns),
               [this]() { return !this->queue_.empty(); });

  if (queue_.empty()) {
    return false;
  }

  *element = std::move(queue_.front());
  queue_.pop_front();
  return true;
}

template <typename T>
std::size_t ThreadSafeQueue<T>::size() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return queue_.size();
}

template <typename T>
bool ThreadSafeQueue<T>::Empty() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return queue_.empty();
}

template <typename T>
bool ThreadSafeQueue<T>::GetFrontAndBack(T* const front, T* const back) {
  if (front == nullptr) {
    return false;
  }
  if (back == nullptr) {
    return false;
  }
  std::shared_lock<std::shared_mutex> lock(mutex_);
  if (queue_.empty()) {
    return false;
  }
  *front = queue_.front();
  *back = queue_.back();
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::GetFront(T* const front) {
  if (front == nullptr) {
    return false;
  }
  std::shared_lock<std::shared_mutex> lock(mutex_);
  if (queue_.empty()) {
    return false;
  }
  *front = queue_.front();
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::PopFront() {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  if (queue_.empty()) {
    return false;
  }
  queue_.pop_front();
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::GetBack(T* const back) {
  if (back == nullptr) {
    return false;
  }
  std::shared_lock<std::shared_mutex> lock(mutex_);
  if (queue_.empty()) {
    return false;
  }
  *back = queue_.back();
  return true;
}

template <typename T>
void ThreadSafeQueue<T>::BreakAllWait() {
  break_all_wait_ = true;
  cv_.notify_all();
}

template <typename T>
bool ThreadSafeQueue<T>::IsBreak() const {
  return break_all_wait_;
}

template <typename T>
void ThreadSafeQueue<T>::Clear() {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  while (!queue_.empty()) {
    queue_.pop_front();
  }
}

template <typename T>
bool ThreadSafeQueue<T>::ExtractAll(std::list<T>* const result) {
  if (result == nullptr) {
    return false;
  }
  std::unique_lock<std::shared_mutex> lock(mutex_);
  result->clear();
  while (!queue_.empty()) {
    result->emplace_back(queue_.front());
    queue_.pop_front();
  }
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::ExtractAll(std::vector<T>* const result) {
  if (result == nullptr) {
    return false;
  }
  std::unique_lock<std::shared_mutex> lock(mutex_);
  result->clear();
  while (!queue_.empty()) {
    result->emplace_back(queue_.front());
    queue_.pop_front();
  }
  return true;
}

template <typename T>
bool ThreadSafeQueue<T>::GetAll(std::list<T>* const result) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  *result = queue_;
  return true;
}

}  // namespace 
}  // namespace 


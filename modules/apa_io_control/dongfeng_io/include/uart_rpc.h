#pragma once
#include <stdint.h>
#define UARTRPC_DEV_CHN_VAL 1
#define MAX_VALUE_LEN 1024

// 信号
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind = 0xA0;
  uint8_t index;
  uint8_t sig_type;
  uint8_t len;
  uint8_t value[MAX_VALUE_LEN];
} __attribute__((packed)) uart_down_sig_t_;

// 握手控制请求
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind = 0x01;
  uint8_t index;
  uint8_t eps;
  uint8_t ibc;
} __attribute__((packed)) uart_down_handshake_t_;

// ACC握手控制请求
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind = 0x11;
  uint8_t index;
  uint8_t eps;
  uint8_t ibc;
} __attribute__((packed)) uart_down_acc_handshake_t_;

// 控车数据
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind= 0x03;
  uint8_t index;
  uint8_t brake_mode;
  uint16_t distance;
  uint16_t velocity;
  uint8_t angle_flag;
  uint16_t angle;
  uint8_t gear_flag;
  uint8_t gear;  
} __attribute__((packed)) uart_down_vehctrl_t_;

// 扭矩方式控车数据
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind= 0x13;
  uint8_t index;
  uint8_t drive_flag;
  uint8_t brake_flag;
  uint32_t tar_torque;
  uint16_t tar_deceleration;
  uint8_t angle_flag;
  uint16_t angle;
  uint8_t gear_flag;
  uint8_t gear;
  uint16_t tar_acc;
  uint8_t lamp_ctl;  
} __attribute__((packed)) uart_down_trq_vehctrl_t_;

// 8155异常上报给MCU
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind = 0x05;
  uint8_t index;
  uint8_t len;
  uint32_t value[MAX_VALUE_LEN];
} __attribute__((packed)) uart_down_exception_t_;

// 应答包
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind;
  uint8_t index;
} __attribute__((packed)) uart_down_ans_t_;

//APA状态
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind = 0xA2;
  uint8_t index;
  uint8_t scenario = 0x01;
  uint8_t scenario_len = 0x01;
  uint8_t scenario_value;
  uint8_t apa_status = 0x02;
  uint8_t apa_status_len = 0x01;
  uint8_t apa_status_value;
} __attribute__((packed)) apa_status_t_;

// AEB
typedef struct {
  uint8_t d_type = 0x2A;
  uint8_t sig_kind= 0x15;
  uint8_t index;
  uint8_t brake_prefill_status;
  uint8_t hba_level;
  uint8_t eba_status;
  uint16_t deceleration;
  uint32_t ego_car_stop;
} __attribute__((packed)) uart_down_aeb_t_;

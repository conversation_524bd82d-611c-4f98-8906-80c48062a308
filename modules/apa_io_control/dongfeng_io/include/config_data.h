#ifndef __CONFIG_CA_H__
#define __CONFIG_CA_H__
#pragma once

#include "singleton.hpp"
#include "common_struct.h"
#include "json_parser.hpp"
#include <vector>

// using namespace base::util;

namespace io_server {
namespace dongfeng{
    class ConfigData: public Singleton<ConfigData, true>
    {
    public:
        ConfigData() = default;
        // ~ConfigData();
        bool initConfigData(std::string io_cfg_path);

    public:
        bool is_valid_ = false;
        std::string pub_topic_160_;
        std::string pub_topic_4c1_;
        std::string pub_topic_35a_;
        std::string pub_topic_;
        std::vector<std::string> sub_topic_;

        double steering_rate_limit_ = 9.0;
        uint32_t ibc_pedal_travel_ = 20;
        uint32_t filter_window_size_ = 20;
        uint32_t filter_pickup_index_ = 3;
        double uss_valid_distance_cm_ = 300.0;

        nlohmann::json error_level_;

        IVI_160 ivi_160_init_;
        IVI_4C1 ivi_4c1_init_;
        IVI_35A ivi_35a_init_;

    private:
        bool praseConfig(std::string io_cfg_path);
        void initConfigParam(nlohmann::json param);
        void initIVI160BaseDBC(nlohmann::json init_data);
        void initIVI35ABaseDBC(nlohmann::json init_data);
        void initIVI4C1BaseDBC(nlohmann::json init_data);

    public:
        uint32_t use_heart_beat_check_ = 0;
        uint32_t use_exception_function_ = 0;
        uint32_t use_resend_function_ = 0;
        uint32_t use_imu_whlspd_monitor_ = 0;
        uint32_t overtime_span_warn_ = 0;
        uint32_t overtime_span_error_ = 0;
        uint32_t warnning_rate_ = 0;

        double eps_tk_torque_threshold_ = 0.0;
        uint64_t eps_tk_keep_time_ = 0;
        //AIS-45502
        double eco_pub_speed_threshold_ = 30.0;
        double eco_no_pub_speed_threshold_ = 34.0;
        //radar_handle_type
        int32_t radar_handle_type_ = 1;
        //slope configure
        uint64_t slope_mean_filter_ws_ = 30; //外部数据
        uint64_t slope_mean_ws_ = 8;  //内部数据
        uint64_t slope_ws_ = 80; // 暂时未用
        uint8_t slope_debug_ = 0;
        //mass configure
        double real_mass_ = 2650.0;
        double mass_friction_ = 80.0;
        double mass_a_ = 0.0;
        double mass_b_ = 0.0;
        double mass_c_ = 0.0;
        double mass_offset_ = 0.0;
        double mass_min_speed_limited_ = 0.5;
        double mass_max_speed_limited_ = 7.0;
        double mass_acc_limited_ = 0.05;
        double mass_wheel_radius_ = 0.3937;
        double mass_slope_limited_ = 3.0;
        double mass_eps_limited_ = 30.0;
        uint8_t mass_debug_ = 0;
        uint8_t use_system_identification_ = 0;
    };
    
}
}

#endif

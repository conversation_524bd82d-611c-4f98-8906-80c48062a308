#ifndef __CYBER_MESSENGER_H__
#define __CYBER_MESSENGER_H__
#pragma once

#include "common_struct.h"
#include "config_data.h"
#include "ult_filter.h"
#include "common_utils.h"

#include "base/util/config_parser.hpp"
#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"

#include "cyber/cyber.h"
#include "cyber/node/writer.h"
#include "cyber/timer/timer.h"
#include "thirdparty/recommend_protocols/apa_io_control/proto/io_proto.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/imu.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/ultrasonic_sf.pb.h"
#include "inhouse_messenger.h"
#include "exception_handle.h"
#include "thirdparty/recommend_protocols/drivers/proto/vehicle_body.pb.h"
#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/radar.pb.h"
#include "thirdparty/recommend_protocols/aeb/proto/aeb.pb.h"


using apollo::cyber::Node;
using apollo::cyber::Writer;
using changan::io::IOChassisInfoDebug;
using changan::io::IOTakeover;
using changan::io::IOControl;
using changan::io::IOControlUnitTest;
using changan::io::EnableUSSUlt;
using changan::io::GearEventInfo;

//output
// using rainbowdash::common::CommonBool;
using rainbowdash::common::CommonGearPosition;
using rainbowdash::drivers::Imu;
using rainbowdash::drivers::PiUssApp2DataInfo;
using rainbowdash::control_by_wire::SystemCheckResultInfo;
using rainbowdash::control_by_wire::ShakeHandStateInfo;
using rainbowdash::control_by_wire::VehicleBodyInfo;
using rainbowdash::control_by_wire::ChassisLatInfo;
using rainbowdash::control_by_wire::ChassisLongInfo;
using rainbowdash::control_by_wire::WheelSpeedInfo;
using rainbowdash::control_by_wire::ChassisShiftGearInfo;
using rainbowdash::control_by_wire::AppRpaReq;
using rainbowdash::control_by_wire::KeyRpaReq;
using rainbowdash::control_by_wire::ApaStatusAck;
using rainbowdash::common::FApaScenario;
using rainbowdash::control_by_wire::ApaStatusAck_Status;
// l2
using rainbowdash::control_by_wire::DrivingMode;
using rainbowdash::control_by_wire::Acceleration;
using rainbowdash::control_by_wire::SteeringSystem;
using rainbowdash::control_by_wire::TurnSignal;
using rainbowdash::control_by_wire::VehicleSignal;
using rainbowdash::control_by_wire::Chassis;
//input
using rainbowdash::control_by_wire::LatAndLongControlCmd;
using rainbowdash::control_by_wire::HoldPressureChangan;
using rainbowdash::control_by_wire::LatAndLongShakeHandCmd;
using rainbowdash::control_by_wire::SystemCheckCmd;
using rainbowdash::control_by_wire::SVControlData;
using rainbowdash::control_by_wire::TorqueControlData;
using rainbowdash::control_by_wire::AppRpaResp;
using rainbowdash::control_by_wire::KeyRpaResp;
using rainbowdash::planning::ApaStatus;
//vehicle body info
using rainbowdash::drivers::VehicleSpeed;
using rainbowdash::drivers::WashModeRearMirrorFbSt;
using rainbowdash::drivers::DoorSwSt;
using rainbowdash::drivers::DriverBeltSwSigSt;
using rainbowdash::drivers::DriveReadySt;
using rainbowdash::drivers::PotBackDoorPos;
using rainbowdash::drivers::PedalTravelSensor;
//for location measurement
using rainbowdash::control_by_wire::LocationGearInfo;
using rainbowdash::control_by_wire::LocationStandstillInfo;
using rainbowdash::control_by_wire::LocationSteerAngleInfo;

using rainbowdash::drivers::NewFareoRadarDetections;
using rainbowdash::drivers::NewRadarPcloud;
using rainbowdash::aeb::AEBController;

namespace io_server{
namespace dongfeng{

    class CyberMessenger
    {
    private:
        /* data */
    public:
        CyberMessenger(std::shared_ptr <Node> node, NodeCfg& nodecfg);
        ~CyberMessenger();

    public:

        void processSavedTopicData(StruIOChassisInfoDebug& io_chassis_info_debug);
        void savedSystemIdentification(double slope_estimation, double mass_estimation);
        void twoDividedFrequencyPub();
        void dataChangeToPub();
        bool getRecordCyberInfoStr(std::string& str);  
        bool getRadarRecordCyberInfoStr(std::string& str){
            str = std::move(cyber_radar_str_);
            return true;
        }

#if ENABLE_RADAR_CLOUD
        void processRadarSavedTopicData(StrucNewRadarData& radar_data,
            std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>>& radar_map_data);
#else
        void processRadarSavedTopicData(StrucNewRadarData& radar_data);
#endif
        void radarTargetPub();
        void radarCloudPub();

        void enterStop();
        void enterStart();
        void enterECO();

    private:
        void initCyberPub();
        void initCyberSub();
        bool initUltFliterParam();
        bool initParams();
        bool init();

        double getTimeS(struct timeval * pt);
        void SaveRadarPoints(std::vector<StruRadarDetection>& points, 
                                                        StruRadarCANDetection& can_points);
        
        //input control callback
        void LatAndLongControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& lat_lon_control_cmd);
        void LatAndLongShakeHandCmdCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& shake_hand_cmd);
        void AppRpaRespCallback(const std::shared_ptr <AppRpaResp>& rpa_resp);
        void KeyRpaRespCallback(const std::shared_ptr <KeyRpaResp>& rpa_resp);
        void SystemCheckCmdCallback(const std::shared_ptr <SystemCheckCmd>& sys_chk_cmd);
        void ExceptionCmdCallback(const std::shared_ptr <ExceptionPtr>& exception_cmd);
        void ApaStatusCallback(const std::shared_ptr <ApaStatus>& apa_status_cmd);
        void LatAndLongTrqControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& lat_lon_control_cmd);
        void LatAndLongACCShakeHandCmdCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& shake_hand_cmd);
        void AEBControllerCallback(const std::shared_ptr <AEBController>& shake_hand_cmd);

        //io contorl debug
        bool isBrakeTakeover(const uint32_t ibc_pedal_travel_sensor);
        //output feedback info
        void ImuInfoChassisPub(StruOutputDataImuInfoQueu& output_data_imu_info_que);
        void ImuInfoPub(StruOutputDataSOCImuInfoQueu& output_data_imu_info);
        void UltrasonicInfoPub(StruUltProbeInfo& ult_probe_info);
        void AppRpaReqInfoPub(StruAppRpaReqInfo& rpa_req_info);
        void KeyRpaReqInfoPub(StruKeyRpaReqInfo& rpa_req_info);
        void VehicleBodyInfoPub();
        void ShakeHandStateInfoPub(StruOutputDataShakeHand& output_data_shake_hand);
        void SystemCheckResultInfoPub(const StruOutputDataSystemCheck& output_data_system_check);
        void ChassisLatInfoPub(const StruOutputDataChassis& output_data_chassis);
        void ChassisLongInfoPub(const StruOutputDataChassis& output_data_chassis);
        void WheelSpeedInfoPub(StruOutputDataWheelSpeedInfoQueu& output_data_wheel_speed_que);
        void ChassisShiftGearInfoPub(const StruChassisShiftGearInfo& gear_event_info);
        void APAStatusAckInfoPub(StruAPAStatusAckInfo& apa_status_ack_info);
        // l2 
        void ACCShakeHandStateInfoPub(StruOutputDataShakeHand& output_data_shake_hand);
        void ChassisPub(StruChassis& chassis);

        //vehicle body
        void VehicleSpeedInfoPub(StruVehicleBodyInfo& vehicle_body_info);
        void WashModeRearMirrorFbStInfoPub(StruVehicleBodyInfo& vehicle_body_info);
        void DoorSwStInfoPub(StruVehicleBodyInfo& vehicle_body_info);
        void DriverBeltSwSigStInfoPub(StruVehicleBodyInfo& vehicle_body_info);
        void DriveReadyStInfoPub(StruVehicleBodyInfo& vehicle_body_info);
        void PotBackDoorPosInfoPub(StruVehicleBodyInfo& vehicle_body_info);
        void PedalTravelSensorInfoPub(StruVehicleBodyInfo& vehicle_body_info);

        //for location measurement
        void LocationGearInfoPub(StruInfoByLocation& need_by_location_info);
        void LocationStandstillInfoPub(StruInfoByLocation& need_by_location_info);
        void LocationSteerAngleInfoPub(StruInfoByLocation& need_by_location_info);

        //for radar
        void FareoRadarsPub(StruNewFareoRadars& fareo_radars);
        void FareoRadarsClear(StruNewFareoRadars& fareo_radars);
        void FareoRadarsPointsPub();
        void FareoRadarPointsClear();
        bool StruRadarDetectionIsValid(StruRadarDetection& p);
        bool PrintRadarDetection(StruRadarDetection& p);

        //AIS-45502
        bool ecoModeFollowSpeedPub();

    private:
        std::shared_ptr <Node> pNode = nullptr;
        NodeCfg nodeCfg;

        //debug
        std::shared_ptr< Writer<IOChassisInfoDebug> > pWriterIOChassisInfoDebug = nullptr;
        //writer
        std::shared_ptr< Writer<Imu> > pWriterImuInfoChassis = nullptr;
        std::shared_ptr< Writer<Imu> > pWriterImuInfo = nullptr;
        std::shared_ptr< Writer<AppRpaReq> > pWriterAppRpaReqInfo = nullptr;
        std::shared_ptr< Writer<KeyRpaReq> > pWriterKeyRpaReqInfo = nullptr;
        std::shared_ptr< Writer<PiUssApp2DataInfo> > pWriterUltrasonicInfo = nullptr;
        std::shared_ptr< Writer<SystemCheckResultInfo> > pWriterSystemCheckResultInfo = nullptr;
        std::shared_ptr< Writer<ShakeHandStateInfo> > pWriterShakeHandStateInfo = nullptr;
        std::shared_ptr< Writer<VehicleBodyInfo> > pWriterVehicleBodyInfo = nullptr;
        std::shared_ptr< Writer<ChassisLatInfo> > pWriterChassisLatInfo = nullptr;
        std::shared_ptr< Writer<ChassisLongInfo> > pWriterChassisLongInfo = nullptr;
        std::shared_ptr< Writer<WheelSpeedInfo> > pWriterWheelSpeedInfo = nullptr;     
        std::shared_ptr< Writer<ChassisShiftGearInfo> > pWriterChassisShiftGearInfo = nullptr;
        std::shared_ptr< Writer<ApaStatusAck> > pWriterApaStatusAckInfo = nullptr;
        // l2 writer
        std::shared_ptr< Writer<ShakeHandStateInfo> > pWriterACCShakeHandStateInfo = nullptr;
        std::shared_ptr< Writer<Chassis> > pWriterChassis = nullptr;
        
        //writer vehicle body info
        std::shared_ptr< Writer<VehicleSpeed> > pWriterVehicleSpeedInfo = nullptr;
        std::shared_ptr< Writer<WashModeRearMirrorFbSt> > pWriterWashModeRearMirrorFbStInfo = nullptr;
        std::shared_ptr< Writer<DoorSwSt> > pWriterDoorSwStInfo = nullptr;
        std::shared_ptr< Writer<DriverBeltSwSigSt> > pWriterDriverBeltSwSigStInfo = nullptr;
        std::shared_ptr< Writer<DriveReadySt> > pWriterDriveReadyStInfo = nullptr;
        std::shared_ptr< Writer<PotBackDoorPos> > pWriterPotBackDoorPosInfo = nullptr;
        std::shared_ptr< Writer<PedalTravelSensor> > pWriterPedalTravelSensorInfo = nullptr;

        //for location measurement
        std::shared_ptr< Writer<LocationGearInfo> > pWriterLocationGearInfo = nullptr;
        std::shared_ptr< Writer<LocationStandstillInfo> > pWriterLocationStandstillInfo = nullptr;
        std::shared_ptr< Writer<LocationSteerAngleInfo> > pWriterLocationSteerAngleInfo = nullptr;

        //for radar
        std::shared_ptr< Writer<NewFareoRadarDetections> > pWriterFareoRadar = nullptr;

        std::shared_ptr< Writer<NewRadarPcloud> > pWriterFareoRadarPointsFC = nullptr;
        std::shared_ptr< Writer<NewRadarPcloud> > pWriterFareoRadarPointsFL = nullptr;
        std::shared_ptr< Writer<NewRadarPcloud> > pWriterFareoRadarPointsFR = nullptr;
        std::shared_ptr< Writer<NewRadarPcloud> > pWriterFareoRadarPointsRL = nullptr;
        std::shared_ptr< Writer<NewRadarPcloud> > pWriterFareoRadarPointsRR = nullptr;

    private:
        //debug
        StruChassisShiftGearInfo gear_event_info_;
        std::string cyber_str_;
        std::string cyber_radar_str_;
        // ExceptionHandleInfo exception_handle_info_;
        //input
        StruInputLatAndLongControlCmd input_lat_lon_control_;
        StruInputLatAndLongShakeHandCmd input_shake_hand_;
        //output
        StruOutputDataChassis output_data_chassis_;
        // StruOutputDataWheelSpeedInfo output_data_wheel_speed_;
        StruOutputDataWheelSpeedInfoQueu output_data_wheel_speed_;
        StruOutputDataShakeHand output_data_shake_hand_;
        StruOutputDataSystemCheck output_data_system_check_;
        // StruOutputDataImuInfo output_data_imu_info_;
        StruOutputDataSOCImuInfoQueu output_data_imu_info_;
        StruAppRpaReqInfo output_data_app_rpareq_info_;
        StruKeyRpaReqInfo output_data_key_rpareq_info_;
        // StruOutputDataImuInfo output_data_imu_info_chassis_;
        StruOutputDataImuInfoQueu output_data_imu_info_chassis_;
        StruUltProbeInfo ult_data_info_;
        StruVehicleBodyInfo  vehicle_body_info_;
        StruInfoByLocation need_by_location_info_;
        uint32_t ibc_pedal_travel_ = 20;
        StruAPAStatusAckInfo apa_status_ack_info_;
        StruNewFareoRadars fareo_radars_;
        std::tuple<bool, double, std::vector<StruRadarDetection> > fareo_radars_point_fc_; //pair<measurement_time, points>
        std::tuple<bool, double, std::vector<StruRadarDetection> > fareo_radars_point_fl_;
        std::tuple<bool, double, std::vector<StruRadarDetection> > fareo_radars_point_fr_;
        std::tuple<bool, double, std::vector<StruRadarDetection> > fareo_radars_point_rl_;
        std::tuple<bool, double, std::vector<StruRadarDetection> > fareo_radars_point_rr_;
        std::vector<uint32_t> fc_can_vec_ = {
            Radar_FC252, Radar_FC253, Radar_FC254, Radar_FC255, Radar_FC256,
            Radar_FC257, Radar_FC258, Radar_FC259, Radar_FC25A, Radar_FC25B,
            Radar_FC25C, Radar_FC25D, Radar_FC25E, Radar_FC25F, Radar_FC260,
            Radar_FC261, Radar_FC262, Radar_FC263, Radar_FC264, Radar_FC265,
            Radar_FC266, Radar_FC267, Radar_FC268, Radar_FC269, Radar_FC26A
        };
        std::vector<uint32_t> fl_can_vec_ = {
            Radar_FL202, Radar_FL203, Radar_FL204, Radar_FL205, Radar_FL206,
            Radar_FL207, Radar_FL208, Radar_FL209, Radar_FL20A, Radar_FL20B,
            Radar_FL20C, Radar_FL20D, Radar_FL20E, Radar_FL20F, Radar_FL210,
            Radar_FL211, Radar_FL212, Radar_FL213, Radar_FL214, Radar_FL215,
            Radar_FL216, Radar_FL217, Radar_FL218, Radar_FL219, Radar_FL21A
        };
        std::vector<uint32_t> fr_can_vec_ = {
            Radar_FR282, Radar_FR283, Radar_FR284, Radar_FR285, Radar_FR286,
            Radar_FR287, Radar_FR288, Radar_FR289, Radar_FR28A, Radar_FR28B,
            Radar_FR28C, Radar_FR28D, Radar_FR28E, Radar_FR28F, Radar_FR290,
            Radar_FR291, Radar_FR292, Radar_FR293, Radar_FR294, Radar_FR295,
            Radar_FR296, Radar_FR297, Radar_FR298, Radar_FR299, Radar_FR29A
        };
        std::vector<uint32_t> rl_can_vec_ = {
            Radar_RL102, Radar_RL103, Radar_RL104, Radar_RL105, Radar_RL106,
            Radar_RL107, Radar_RL108, Radar_RL109, Radar_RL10A, Radar_RL10B,
            Radar_RL10C, Radar_RL10D, Radar_RL10E, Radar_RL10F, Radar_RL110,
            Radar_RL111, Radar_RL112, Radar_RL113, Radar_RL114, Radar_RL115,
            Radar_RL116, Radar_RL117, Radar_RL118, Radar_RL119, Radar_RL11A
        };
        std::vector<uint32_t> rr_can_vec_ = {
            Radar_RR182, Radar_RR183, Radar_RR184, Radar_RR185, Radar_RR186,
            Radar_RR187, Radar_RR188, Radar_RR189, Radar_RR18A, Radar_RR18B,
            Radar_RR18C, Radar_RR18D, Radar_RR18E, Radar_RR18F, Radar_RR190,
            Radar_RR191, Radar_RR192, Radar_RR193, Radar_RR194, Radar_RR195,
            Radar_RR196, Radar_RR197, Radar_RR198, Radar_RR199, Radar_RR19A
        };
        //ult
        uint32_t filter_window_size_ = 20;
        uint32_t filter_pickup_index_ = 3;
        double uss_valid_distance_cm_ = 1000.0;
        Filter uls_filter_[12];
        int32_t processUltDistance(int32_t s_dis);
        //0: start_mode 1:stop_mode 2:eco_mode
        std::atomic<uint32_t> io_module_mode_{0};
	//AIS-45502
        double eco_pub_speed_threshold_ = 30.0;
        double eco_no_pub_speed_threshold_ = 34.0;
        // l2 chassis info
        StruChassis chassis_info_;
        // l2 0:apa 1:not apa
        // std::atomic<uint32_t> io_module_pub_mode_{0};

    public:
        static std::atomic<uint32_t> ult_enable_;//0:default 1:req 2:sucess
        // l2 0:apa 1:not apa
        std::atomic<uint32_t> io_module_pub_mode_{1};
    };
    
}
}

#endif

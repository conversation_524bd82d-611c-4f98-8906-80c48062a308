#pragma once

/*! Udp数据包最大长度 */
#define MAX_DATA_LEN 1024

typedef struct can_udp_packet
{
    uint8_t bus_id;
    uint32_t can_id;
    uint8_t message_len;
    uint8_t data[MAX_DATA_LEN];
    void clone(can_udp_packet *d)
    {
        if(d == nullptr)
        {
            return;
        }
        d->bus_id = this->bus_id;
        d->can_id = this->can_id;
        d->message_len = this->message_len;
        memcpy(d->data, this->data, d->message_len);
    }
} __attribute__((packed)) can_udp_packet_t;

typedef struct raw_udp_packet
{
    uint32_t ctl_cmd;
    uint32_t ack_type;
    uint64_t index;
    int32_t len;
    uint8_t data[MAX_DATA_LEN];
    void clone(raw_udp_packet *d)
    {
        if(d == nullptr)
        {
            return;
        }
        d->ctl_cmd = this->ctl_cmd;
        d->ack_type = this->ack_type;
        d->index = this->index;
        d->len = this->len;
        memcpy(d->data, this->data, d->len);
    }
} __attribute__((packed)) raw_udp_packet_t;
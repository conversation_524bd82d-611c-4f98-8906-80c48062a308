#pragma once
#include "common_utils.h"
#include "thread_safe_queue.hpp"
#include "structs/can_udp_packet_define.h"
#include "common_struct.h"
#include "config_data.h"
extern "C" {
#include "can_api.h"
#include "fc_can_api.h"
#include "fl_can_api.h"
#include "fr_can_api.h"
#include "rl_can_api.h"
#include "rr_can_api.h"
}

#define RADAR_FC (0)
#define RADAR_FL (1)
#define RADAR_FR (2)
#define RADAR_RL (3)
#define RADAR_RR (4)

#define RADAR_FC_HEADER (0X251)
#define RADAR_FL_HEADER (0X201)
#define RADAR_FR_HEADER (0X281)
#define RADAR_RL_HEADER (0X101)
#define RADAR_RR_HEADER (0X181)

// FC (0x252 - 0x26A)
#define Radar_FC252 (0x252)
#define Radar_FC253 (0x253)
#define Radar_FC254 (0x254)
#define Radar_FC255 (0x255)
#define Radar_FC256 (0x256)
#define Radar_FC257 (0x257)
#define Radar_FC258 (0x258)
#define Radar_FC259 (0x259)
#define Radar_FC25A (0x25A)
#define Radar_FC25B (0x25B)
#define Radar_FC25C (0x25C)
#define Radar_FC25D (0x25D)
#define Radar_FC25E (0x25E)
#define Radar_FC25F (0x25F)
#define Radar_FC260 (0x260)
#define Radar_FC261 (0x261)
#define Radar_FC262 (0x262)
#define Radar_FC263 (0x263)
#define Radar_FC264 (0x264)
#define Radar_FC265 (0x265)
#define Radar_FC266 (0x266)
#define Radar_FC267 (0x267)
#define Radar_FC268 (0x268)
#define Radar_FC269 (0x269)
#define Radar_FC26A (0x26A)
	
	
// FL (0x202 - 0x21A)
#define Radar_FL202 (0x202)
#define Radar_FL203 (0x203)
#define Radar_FL204 (0x204)
#define Radar_FL205 (0x205)
#define Radar_FL206 (0x206)
#define Radar_FL207 (0x207)
#define Radar_FL208 (0x208)
#define Radar_FL209 (0x209)
#define Radar_FL20A (0x20A)
#define Radar_FL20B (0x20B)
#define Radar_FL20C (0x20C)
#define Radar_FL20D (0x20D)
#define Radar_FL20E (0x20E)
#define Radar_FL20F (0x20F)
#define Radar_FL210 (0x210)
#define Radar_FL211 (0x211)
#define Radar_FL212 (0x212)
#define Radar_FL213 (0x213)
#define Radar_FL214 (0x214)
#define Radar_FL215 (0x215)
#define Radar_FL216 (0x216)
#define Radar_FL217 (0x217)
#define Radar_FL218 (0x218)
#define Radar_FL219 (0x219)
#define Radar_FL21A (0x21A)
	
	
// FR (0x282 - 0x29A)
#define Radar_FR282 (0x282)
#define Radar_FR283 (0x283)
#define Radar_FR284 (0x284)
#define Radar_FR285 (0x285)
#define Radar_FR286 (0x286)
#define Radar_FR287 (0x287)
#define Radar_FR288 (0x288)
#define Radar_FR289 (0x289)
#define Radar_FR28A (0x28A)
#define Radar_FR28B (0x28B)
#define Radar_FR28C (0x28C)
#define Radar_FR28D (0x28D)
#define Radar_FR28E (0x28E)
#define Radar_FR28F (0x28F)
#define Radar_FR290 (0x290)
#define Radar_FR291 (0x291)
#define Radar_FR292 (0x292)
#define Radar_FR293 (0x293)
#define Radar_FR294 (0x294)
#define Radar_FR295 (0x295)
#define Radar_FR296 (0x296)
#define Radar_FR297 (0x297)
#define Radar_FR298 (0x298)
#define Radar_FR299 (0x299)
#define Radar_FR29A (0x29A)

	
	
// RL (0x102 - 0x11A)
#define Radar_RL102 (0x102)
#define Radar_RL103 (0x103)
#define Radar_RL104 (0x104)
#define Radar_RL105 (0x105)
#define Radar_RL106 (0x106)
#define Radar_RL107 (0x107)
#define Radar_RL108 (0x108)
#define Radar_RL109 (0x109)
#define Radar_RL10A (0x10A)
#define Radar_RL10B (0x10B)
#define Radar_RL10C (0x10C)
#define Radar_RL10D (0x10D)
#define Radar_RL10E (0x10E)
#define Radar_RL10F (0x10F)
#define Radar_RL110 (0x110)
#define Radar_RL111 (0x111)
#define Radar_RL112 (0x112)
#define Radar_RL113 (0x113)
#define Radar_RL114 (0x114)
#define Radar_RL115 (0x115)
#define Radar_RL116 (0x116)
#define Radar_RL117 (0x117)
#define Radar_RL118 (0x118)
#define Radar_RL119 (0x119)
#define Radar_RL11A (0x11A)

	
	
// RR (0x182 - 0x19A)
#define Radar_RR182 (0x182)
#define Radar_RR183 (0x183)
#define Radar_RR184 (0x184)
#define Radar_RR185 (0x185)
#define Radar_RR186 (0x186)
#define Radar_RR187 (0x187)
#define Radar_RR188 (0x188)
#define Radar_RR189 (0x189)
#define Radar_RR18A (0x18A)
#define Radar_RR18B (0x18B)
#define Radar_RR18C (0x18C)
#define Radar_RR18D (0x18D)
#define Radar_RR18E (0x18E)
#define Radar_RR18F (0x18F)
#define Radar_RR190 (0x190)
#define Radar_RR191 (0x191)
#define Radar_RR192 (0x192)
#define Radar_RR193 (0x193)
#define Radar_RR194 (0x194)
#define Radar_RR195 (0x195)
#define Radar_RR196 (0x196)
#define Radar_RR197 (0x197)
#define Radar_RR198 (0x198)
#define Radar_RR199 (0x199)
#define Radar_RR19A (0x19A)


#define PARSE_RADAR_PREFIX(STRCANID) \
static uint64_t last_time = 0;                                  \
static bool first_record_time = true;                   \
uint64_t time_span = time - last_time;              \
if(true == first_record_time){                                  \
    first_record_time = false;                                          \
    time_span = 0;                                                        \
}                                                                               \
last_time = time;                        \
StruRadarCANDetection radar_data;                              \
radar_data.measurement_time = static_cast<double>(time) / 1000.0;               \
std::string radar_str;                                      \
radar_str.append(", radar_").append(STRCANID).append("_measurement_time=").append(std::to_string(radar_data.measurement_time)        \
                                        ).append(", radar_").append(STRCANID).append("_time_span=").append(std::to_string(time_span));                                 

#define GET_SIGNAL_VALUE(STRCANID, TYPE, DETECTION_ORDER, POINT_ORDER, COMMON_ORDER)                        \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__CoGRange_NI_##POINT_ORDER##_##TYPE##_g, radar_data.CoGRange_NI_##COMMON_ORDER);                   \
radar_str.append(", ").append(STRCANID).append("_CoGRange_NI_YY=").append(std::to_string(radar_data.CoGRange_NI_##COMMON_ORDER));                                        \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__CoGDoppler_NI_##POINT_ORDER##_##TYPE##_g, radar_data.CoGDoppler_NI_##COMMON_ORDER);           \
radar_str.append(", ").append(STRCANID).append("_CoGDoppler_NI_YY=").append(std::to_string(radar_data.CoGDoppler_NI_##COMMON_ORDER));                                \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__Reserve1_1bit_NI_##POINT_ORDER##_##TYPE##_g, radar_data.Reserve1_1bit_NI_##COMMON_ORDER); \
radar_str.append(", ").append(STRCANID).append("_Reserve1_1bit_NI_YY=").append(std::to_string(radar_data.Reserve1_1bit_NI_##COMMON_ORDER));                          \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__PowerDB_NI_##POINT_ORDER##_##TYPE##_g, radar_data.PowerDB_NI_##COMMON_ORDER);                         \
radar_str.append(", ").append(STRCANID).append("_PowerDB_NI_YY=").append(std::to_string(radar_data.PowerDB_NI_##COMMON_ORDER));                                                  \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__SNRdB_NI_##POINT_ORDER##_##TYPE##_g, radar_data.SNRdB_NI_##COMMON_ORDER);                                         \
radar_str.append(", ").append(STRCANID).append("_SNRdB_NI_YY=").append(std::to_string(radar_data.SNRdB_NI_##COMMON_ORDER));                                                          \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__StdAzimuth_NI_##POINT_ORDER##_##TYPE##_g, radar_data.StdAzimuth_NI_##COMMON_ORDER);                   \
radar_str.append(", ").append(STRCANID).append("_StdAzimuth_NI_YY=").append(std::to_string(radar_data.StdAzimuth_NI_##COMMON_ORDER));                                            \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__Azimuth_NI_##POINT_ORDER##_##TYPE##_g, radar_data.Azimuth_NI_##COMMON_ORDER);                                         \
radar_str.append(", ").append(STRCANID).append("_Azimuth_NI_YY=").append(std::to_string(radar_data.Azimuth_NI_##COMMON_ORDER));                                                  \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__Beam_NI_##POINT_ORDER##_##TYPE##_g, radar_data.Beam_NI_##COMMON_ORDER);                                               \
radar_str.append(", ").append(STRCANID).append("_Beam_NI_YY=").append(std::to_string(radar_data.Beam_NI_##COMMON_ORDER));                                                                        \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__NoInfrastructure_NI_##POINT_ORDER##_##TYPE##_g, radar_data.NoInfrastructure_NI_##COMMON_ORDER);       \
radar_str.append(", ").append(STRCANID).append("_NoInfrastructure_NI_YY=").append(std::to_string(radar_data.NoInfrastructure_NI_##COMMON_ORDER));                            \
GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_##DETECTION_ORDER##_##TYPE##__ValidXBeam_NI_##POINT_ORDER##_##TYPE##_g, radar_data.ValidXBeam_NI_##COMMON_ORDER);                               \
radar_str.append(", ").append(STRCANID).append("_ValidXBeam_NI_YY=").append(std::to_string(radar_data.ValidXBeam_NI_##COMMON_ORDER));                                                    

#define PARSE_RADAR_SUFFIX(TYPE, CANID, DEBUG_FLAG) \
std::get<2>(radar_msg_save_map_.at(RADAR_##TYPE))[CANID].first = true;            \
std::get<2>(radar_msg_save_map_.at(RADAR_##TYPE))[CANID].second = radar_data;             \
if(DEBUG_FLAG){             \
    cloud_debug_info_ += radar_str;             \
}



namespace io_server {
namespace dongfeng {                                                                    

extern "C" {
  extern void CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
  extern void MK_FC_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
  extern void MK_FL_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
  extern void MK_FR_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
  extern void MK_RL_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
  extern void MK_RR_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
}
    class RadarHandler
    {
    public:
        RadarHandler(/* args */);
        ~RadarHandler();

        bool Init();

        bool IsRadarCanData(uint32_t can_id);
        bool EnqueueRadarData(std::pair<uint64_t, can_udp_packet_t> data);
        bool ClearRadarData();

        void ParseAllRadarCanRcvMsg();
#if ENABLE_RADAR_CLOUD
        bool GetRadarDebugInfo(StrucNewRadarData& radar_data,
            std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>>& radar_map_data);
#else
        bool GetRadarDebugInfo(StrucNewRadarData& radar_data);
#endif
        void ResetRadarDebugInfoValid();
        bool getRadarTargetRecordInfoStr(std::string& str){
            str = std::move(target_debug_info_);
            return true;
        }
        bool getRadarCloudRecordInfoStr(std::string& str){
            str = std::move(cloud_debug_info_);
            return true;
        }

    private:
        std::vector<uint32_t> fc_can_vec_ = {
            Radar_FC252, Radar_FC253, Radar_FC254, Radar_FC255, Radar_FC256,
            Radar_FC257, Radar_FC258, Radar_FC259, Radar_FC25A, Radar_FC25B,
            Radar_FC25C, Radar_FC25D, Radar_FC25E, Radar_FC25F, Radar_FC260,
            Radar_FC261, Radar_FC262, Radar_FC263, Radar_FC264, Radar_FC265,
            Radar_FC266, Radar_FC267, Radar_FC268, Radar_FC269, Radar_FC26A
        };
        std::vector<uint32_t> fl_can_vec_ = {
            Radar_FL202, Radar_FL203, Radar_FL204, Radar_FL205, Radar_FL206,
            Radar_FL207, Radar_FL208, Radar_FL209, Radar_FL20A, Radar_FL20B,
            Radar_FL20C, Radar_FL20D, Radar_FL20E, Radar_FL20F, Radar_FL210,
            Radar_FL211, Radar_FL212, Radar_FL213, Radar_FL214, Radar_FL215,
            Radar_FL216, Radar_FL217, Radar_FL218, Radar_FL219, Radar_FL21A
        };
        std::vector<uint32_t> fr_can_vec_ = {
            Radar_FR282, Radar_FR283, Radar_FR284, Radar_FR285, Radar_FR286,
            Radar_FR287, Radar_FR288, Radar_FR289, Radar_FR28A, Radar_FR28B,
            Radar_FR28C, Radar_FR28D, Radar_FR28E, Radar_FR28F, Radar_FR290,
            Radar_FR291, Radar_FR292, Radar_FR293, Radar_FR294, Radar_FR295,
            Radar_FR296, Radar_FR297, Radar_FR298, Radar_FR299, Radar_FR29A
        };
        std::vector<uint32_t> rl_can_vec_ = {
            Radar_RL102, Radar_RL103, Radar_RL104, Radar_RL105, Radar_RL106,
            Radar_RL107, Radar_RL108, Radar_RL109, Radar_RL10A, Radar_RL10B,
            Radar_RL10C, Radar_RL10D, Radar_RL10E, Radar_RL10F, Radar_RL110,
            Radar_RL111, Radar_RL112, Radar_RL113, Radar_RL114, Radar_RL115,
            Radar_RL116, Radar_RL117, Radar_RL118, Radar_RL119, Radar_RL11A
        };
        std::vector<uint32_t> rr_can_vec_ = {
            Radar_RR182, Radar_RR183, Radar_RR184, Radar_RR185, Radar_RR186,
            Radar_RR187, Radar_RR188, Radar_RR189, Radar_RR18A, Radar_RR18B,
            Radar_RR18C, Radar_RR18D, Radar_RR18E, Radar_RR18F, Radar_RR190,
            Radar_RR191, Radar_RR192, Radar_RR193, Radar_RR194, Radar_RR195,
            Radar_RR196, Radar_RR197, Radar_RR198, Radar_RR199, Radar_RR19A
        };
#if ENABLE_RADAR_CLOUD
        std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>> radar_msg_save_map_; //整个vector包含fc fl fr rl rr五个，tuple<valid(header), time, map<canid pair<valid, struct>>>
#endif        
        StrucNewRadarData io_radar_info_debug_tmp_;
        
        std::shared_ptr<ThreadSafeQueue<std::pair<uint64_t, can_udp_packet_t>>> radar_can_msg_queue_; //主要用于存储雷达can数据
        std::shared_ptr<ThreadSafeQueue<std::pair<uint64_t, can_udp_packet_t>>> radar_points_can_msg_queue_; //主要用于存储雷达can数据，存储动点信息
#if ENABLE_RADAR_CLOUD
        std::vector<std::pair<uint64_t, can_udp_packet_t>> radar_points_fc_can_msg_;
        std::vector<std::pair<uint64_t, can_udp_packet_t>> radar_points_fl_can_msg_;
        std::vector<std::pair<uint64_t, can_udp_packet_t>> radar_points_fr_can_msg_;
        std::vector<std::pair<uint64_t, can_udp_packet_t>> radar_points_rl_can_msg_;
        std::vector<std::pair<uint64_t, can_udp_packet_t>> radar_points_rr_can_msg_;
        std::queue<std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>>> radar_points_fc_can_msg_queue_; // valid(收到header信号) time vector<time candata>
        std::queue<std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>>> radar_points_fl_can_msg_queue_; 
        std::queue<std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>>> radar_points_fr_can_msg_queue_; 
        std::queue<std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>>> radar_points_rl_can_msg_queue_; 
        std::queue<std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>>> radar_points_rr_can_msg_queue_; 
#endif
        std::string cloud_debug_info_;
        std::string target_debug_info_;
        //2:雷达点云和目标数据都解析并输出 0和1:目标数据解析并输出
        int32_t radar_handle_type_ = 1;
        bool init_flag_ = false;

    private:
        void ParseRadarTargetCanRcvMsg();
#if ENABLE_RADAR_CLOUD
        void ParseRadarCloudCanRcvMsg();
#endif
        //radar目标解析
        void ParseRadarFC341(uint64_t time);//radar can
        void ParseRadarFC342(uint64_t time);//radar can
        void ParseRadarFC340(uint64_t time);//radar can
        void ParseRadarFL41(uint64_t time);//radar can
        void ParseRadarFL42(uint64_t time);//radar can
        void ParseRadarFL40(uint64_t time);//radar can
        void ParseRadarFR241(uint64_t time);//radar can
        void ParseRadarFR242(uint64_t time);//radar can
        void ParseRadarFR240(uint64_t time);//radar can
        void ParseRadarRL441(uint64_t time);//radar can
        void ParseRadarRL442(uint64_t time);//radar can
        void ParseRadarRL440(uint64_t time);//radar can
        void ParseRadarRR641(uint64_t time);//radar can
        void ParseRadarRR642(uint64_t time);//radar can
        void ParseRadarRR640(uint64_t time);//radar can
#if ENABLE_RADAR_CLOUD
        //radar点云解析
        void PraseRadarHeader251(uint64_t time);
        void PraseRadarHeader201(uint64_t time);
        void PraseRadarHeader281(uint64_t time);
        void PraseRadarHeader101(uint64_t time);
        void PraseRadarHeader181(uint64_t time);
        void PraseRadar252(uint64_t time);
        void PraseRadar253(uint64_t time);
        void PraseRadar254(uint64_t time);
        void PraseRadar255(uint64_t time);
        void PraseRadar256(uint64_t time);
        void PraseRadar257(uint64_t time);
        void PraseRadar258(uint64_t time);
        void PraseRadar259(uint64_t time);
        void PraseRadar25A(uint64_t time);
        void PraseRadar25B(uint64_t time);
        void PraseRadar25C(uint64_t time);
        void PraseRadar25D(uint64_t time);
        void PraseRadar25E(uint64_t time);
        void PraseRadar25F(uint64_t time);
        void PraseRadar260(uint64_t time);
        void PraseRadar261(uint64_t time);
        void PraseRadar262(uint64_t time);
        void PraseRadar263(uint64_t time);
        void PraseRadar264(uint64_t time);
        void PraseRadar265(uint64_t time);
        void PraseRadar266(uint64_t time);
        void PraseRadar267(uint64_t time);
        void PraseRadar268(uint64_t time);
        void PraseRadar269(uint64_t time);
        void PraseRadar26A(uint64_t time);

        void PraseRadar202(uint64_t time);
        void PraseRadar203(uint64_t time);
        void PraseRadar204(uint64_t time);
        void PraseRadar205(uint64_t time);
        void PraseRadar206(uint64_t time);
        void PraseRadar207(uint64_t time);
        void PraseRadar208(uint64_t time);
        void PraseRadar209(uint64_t time);
        void PraseRadar20A(uint64_t time);
        void PraseRadar20B(uint64_t time);
        void PraseRadar20C(uint64_t time);
        void PraseRadar20D(uint64_t time);
        void PraseRadar20E(uint64_t time);
        void PraseRadar20F(uint64_t time);
        void PraseRadar210(uint64_t time);
        void PraseRadar211(uint64_t time);
        void PraseRadar212(uint64_t time);
        void PraseRadar213(uint64_t time);
        void PraseRadar214(uint64_t time);
        void PraseRadar215(uint64_t time);
        void PraseRadar216(uint64_t time);
        void PraseRadar217(uint64_t time);
        void PraseRadar218(uint64_t time);
        void PraseRadar219(uint64_t time);
        void PraseRadar21A(uint64_t time);

        void PraseRadar282(uint64_t time);
        void PraseRadar283(uint64_t time);
        void PraseRadar284(uint64_t time);
        void PraseRadar285(uint64_t time);
        void PraseRadar286(uint64_t time);
        void PraseRadar287(uint64_t time);
        void PraseRadar288(uint64_t time);
        void PraseRadar289(uint64_t time);
        void PraseRadar28A(uint64_t time);
        void PraseRadar28B(uint64_t time);
        void PraseRadar28C(uint64_t time);
        void PraseRadar28D(uint64_t time);
        void PraseRadar28E(uint64_t time);
        void PraseRadar28F(uint64_t time);
        void PraseRadar290(uint64_t time);
        void PraseRadar291(uint64_t time);
        void PraseRadar292(uint64_t time);
        void PraseRadar293(uint64_t time);
        void PraseRadar294(uint64_t time);
        void PraseRadar295(uint64_t time);
        void PraseRadar296(uint64_t time);
        void PraseRadar297(uint64_t time);
        void PraseRadar298(uint64_t time);
        void PraseRadar299(uint64_t time);
        void PraseRadar29A(uint64_t time);

        void PraseRadar102(uint64_t time);
        void PraseRadar103(uint64_t time);
        void PraseRadar104(uint64_t time);
        void PraseRadar105(uint64_t time);
        void PraseRadar106(uint64_t time);
        void PraseRadar107(uint64_t time);
        void PraseRadar108(uint64_t time);
        void PraseRadar109(uint64_t time);
        void PraseRadar10A(uint64_t time);
        void PraseRadar10B(uint64_t time);
        void PraseRadar10C(uint64_t time);
        void PraseRadar10D(uint64_t time);
        void PraseRadar10E(uint64_t time);
        void PraseRadar10F(uint64_t time);
        void PraseRadar110(uint64_t time);
        void PraseRadar111(uint64_t time);
        void PraseRadar112(uint64_t time);
        void PraseRadar113(uint64_t time);
        void PraseRadar114(uint64_t time);
        void PraseRadar115(uint64_t time);
        void PraseRadar116(uint64_t time);
        void PraseRadar117(uint64_t time);
        void PraseRadar118(uint64_t time);
        void PraseRadar119(uint64_t time);
        void PraseRadar11A(uint64_t time);

        void PraseRadar182(uint64_t time);
        void PraseRadar183(uint64_t time);
        void PraseRadar184(uint64_t time);
        void PraseRadar185(uint64_t time);
        void PraseRadar186(uint64_t time);
        void PraseRadar187(uint64_t time);
        void PraseRadar188(uint64_t time);
        void PraseRadar189(uint64_t time);
        void PraseRadar18A(uint64_t time);
        void PraseRadar18B(uint64_t time);
        void PraseRadar18C(uint64_t time);
        void PraseRadar18D(uint64_t time);
        void PraseRadar18E(uint64_t time);
        void PraseRadar18F(uint64_t time);
        void PraseRadar190(uint64_t time);
        void PraseRadar191(uint64_t time);
        void PraseRadar192(uint64_t time);
        void PraseRadar193(uint64_t time);
        void PraseRadar194(uint64_t time);
        void PraseRadar195(uint64_t time);
        void PraseRadar196(uint64_t time);
        void PraseRadar197(uint64_t time);
        void PraseRadar198(uint64_t time);
        void PraseRadar199(uint64_t time);
        void PraseRadar19A(uint64_t time);
#endif
        // void PraseRadar254(uint64_t time);
        // void PraseRadar255(uint64_t time);
        // void PraseRadar256(uint64_t time);
        // void PraseRadar257(uint64_t time);
        // void PraseRadar258(uint64_t time);
        // void PraseRadar259(uint64_t time);

        std::unordered_set<uint32_t> radar_target_msg_table_ = {
            SUB_RADAR_FC_341, SUB_RADAR_FC_342, SUB_RADAR_FC_340,
            SUB_RADAR_FL_41, SUB_RADAR_FL_42, SUB_RADAR_FL_40,
            SUB_RADAR_FR_241, SUB_RADAR_FR_242, SUB_RADAR_FR_240,
            SUB_RADAR_RL_441, SUB_RADAR_RL_442, SUB_RADAR_RL_440,
            SUB_RADAR_RR_641, SUB_RADAR_RR_642, SUB_RADAR_RR_640
        };

        typedef void (RadarHandler::*pFun)(uint64_t time);
        std::unordered_map<uint32_t, pFun> can234msg_parser_map_ = {
            //radar目标解析
            {SUB_RADAR_FC_341, &RadarHandler::ParseRadarFC341},
            {SUB_RADAR_FC_342, &RadarHandler::ParseRadarFC342},
            {SUB_RADAR_FC_340, &RadarHandler::ParseRadarFC340},
            {SUB_RADAR_FL_41, &RadarHandler::ParseRadarFL41},
            {SUB_RADAR_FL_42, &RadarHandler::ParseRadarFL42},
            {SUB_RADAR_FL_40, &RadarHandler::ParseRadarFL40},
            {SUB_RADAR_FR_241, &RadarHandler::ParseRadarFR241},
            {SUB_RADAR_FR_242, &RadarHandler::ParseRadarFR242},
            {SUB_RADAR_FR_240, &RadarHandler::ParseRadarFR240},
            {SUB_RADAR_RL_441, &RadarHandler::ParseRadarRL441},
            {SUB_RADAR_RL_442, &RadarHandler::ParseRadarRL442},
            {SUB_RADAR_RL_440, &RadarHandler::ParseRadarRL440},
            {SUB_RADAR_RR_641, &RadarHandler::ParseRadarRR641},
            {SUB_RADAR_RR_642, &RadarHandler::ParseRadarRR642},
            {SUB_RADAR_RR_640, &RadarHandler::ParseRadarRR640}
#if ENABLE_RADAR_CLOUD
            //radar点云解析
            ,{RADAR_FC_HEADER, &RadarHandler::PraseRadarHeader251},
            {RADAR_FL_HEADER, &RadarHandler::PraseRadarHeader201},
            {RADAR_FR_HEADER, &RadarHandler::PraseRadarHeader281},
            {RADAR_RL_HEADER, &RadarHandler::PraseRadarHeader101},
            {RADAR_RR_HEADER, &RadarHandler::PraseRadarHeader181},

            {Radar_FC252, &RadarHandler::PraseRadar252},
            {Radar_FC253, &RadarHandler::PraseRadar253},
            {Radar_FC254, &RadarHandler::PraseRadar254},
            {Radar_FC255, &RadarHandler::PraseRadar255},
            {Radar_FC256, &RadarHandler::PraseRadar256},
            {Radar_FC257, &RadarHandler::PraseRadar257},
            {Radar_FC258, &RadarHandler::PraseRadar258},
            {Radar_FC259, &RadarHandler::PraseRadar259},
            {Radar_FC25A, &RadarHandler::PraseRadar25A},
            {Radar_FC25B, &RadarHandler::PraseRadar25B},
            {Radar_FC25C, &RadarHandler::PraseRadar25C},
            {Radar_FC25D, &RadarHandler::PraseRadar25D},
            {Radar_FC25E, &RadarHandler::PraseRadar25E},
            {Radar_FC25F, &RadarHandler::PraseRadar25F},
            {Radar_FC260, &RadarHandler::PraseRadar260},
            {Radar_FC261, &RadarHandler::PraseRadar261},
            {Radar_FC262, &RadarHandler::PraseRadar262},
            {Radar_FC263, &RadarHandler::PraseRadar263},
            {Radar_FC264, &RadarHandler::PraseRadar264},
            {Radar_FC265, &RadarHandler::PraseRadar265},
            {Radar_FC266, &RadarHandler::PraseRadar266},
            {Radar_FC267, &RadarHandler::PraseRadar267},
            {Radar_FC268, &RadarHandler::PraseRadar268},
            {Radar_FC269, &RadarHandler::PraseRadar269},
            {Radar_FC26A, &RadarHandler::PraseRadar26A},

            {Radar_FL202, &RadarHandler::PraseRadar202},
            {Radar_FL203, &RadarHandler::PraseRadar203},
            {Radar_FL204, &RadarHandler::PraseRadar204},
            {Radar_FL205, &RadarHandler::PraseRadar205},
            {Radar_FL206, &RadarHandler::PraseRadar206},
            {Radar_FL207, &RadarHandler::PraseRadar207},
            {Radar_FL208, &RadarHandler::PraseRadar208},
            {Radar_FL209, &RadarHandler::PraseRadar209},
            {Radar_FL20A, &RadarHandler::PraseRadar20A},
            {Radar_FL20B, &RadarHandler::PraseRadar20B},
            {Radar_FL20C, &RadarHandler::PraseRadar20C},
            {Radar_FL20D, &RadarHandler::PraseRadar20D},
            {Radar_FL20E, &RadarHandler::PraseRadar20E},
            {Radar_FL20F, &RadarHandler::PraseRadar20F},
            {Radar_FL210, &RadarHandler::PraseRadar210},
            {Radar_FL211, &RadarHandler::PraseRadar211},
            {Radar_FL212, &RadarHandler::PraseRadar212},
            {Radar_FL213, &RadarHandler::PraseRadar213},
            {Radar_FL214, &RadarHandler::PraseRadar214},
            {Radar_FL215, &RadarHandler::PraseRadar215},
            {Radar_FL216, &RadarHandler::PraseRadar216},
            {Radar_FL217, &RadarHandler::PraseRadar217},
            {Radar_FL218, &RadarHandler::PraseRadar218},
            {Radar_FL219, &RadarHandler::PraseRadar219},
            {Radar_FL21A, &RadarHandler::PraseRadar21A},

            {Radar_FR282, &RadarHandler::PraseRadar282},
            {Radar_FR283, &RadarHandler::PraseRadar283},
            {Radar_FR284, &RadarHandler::PraseRadar284},
            {Radar_FR285, &RadarHandler::PraseRadar285},
            {Radar_FR286, &RadarHandler::PraseRadar286},
            {Radar_FR287, &RadarHandler::PraseRadar287},
            {Radar_FR288, &RadarHandler::PraseRadar288},
            {Radar_FR289, &RadarHandler::PraseRadar289},
            {Radar_FR28A, &RadarHandler::PraseRadar28A},
            {Radar_FR28B, &RadarHandler::PraseRadar28B},
            {Radar_FR28C, &RadarHandler::PraseRadar28C},
            {Radar_FR28D, &RadarHandler::PraseRadar28D},
            {Radar_FR28E, &RadarHandler::PraseRadar28E},
            {Radar_FR28F, &RadarHandler::PraseRadar28F},
            {Radar_FR290, &RadarHandler::PraseRadar290},
            {Radar_FR291, &RadarHandler::PraseRadar291},
            {Radar_FR292, &RadarHandler::PraseRadar292},
            {Radar_FR293, &RadarHandler::PraseRadar293},
            {Radar_FR294, &RadarHandler::PraseRadar294},
            {Radar_FR295, &RadarHandler::PraseRadar295},
            {Radar_FR296, &RadarHandler::PraseRadar296},
            {Radar_FR297, &RadarHandler::PraseRadar297},
            {Radar_FR298, &RadarHandler::PraseRadar298},
            {Radar_FR299, &RadarHandler::PraseRadar299},
            {Radar_FR29A, &RadarHandler::PraseRadar29A},

            {Radar_RL102, &RadarHandler::PraseRadar102},
            {Radar_RL103, &RadarHandler::PraseRadar103},
            {Radar_RL104, &RadarHandler::PraseRadar104},
            {Radar_RL105, &RadarHandler::PraseRadar105},
            {Radar_RL106, &RadarHandler::PraseRadar106},
            {Radar_RL107, &RadarHandler::PraseRadar107},
            {Radar_RL108, &RadarHandler::PraseRadar108},
            {Radar_RL109, &RadarHandler::PraseRadar109},
            {Radar_RL10A, &RadarHandler::PraseRadar10A},
            {Radar_RL10B, &RadarHandler::PraseRadar10B},
            {Radar_RL10C, &RadarHandler::PraseRadar10C},
            {Radar_RL10D, &RadarHandler::PraseRadar10D},
            {Radar_RL10E, &RadarHandler::PraseRadar10E},
            {Radar_RL10F, &RadarHandler::PraseRadar10F},
            {Radar_RL110, &RadarHandler::PraseRadar110},
            {Radar_RL111, &RadarHandler::PraseRadar111},
            {Radar_RL112, &RadarHandler::PraseRadar112},
            {Radar_RL113, &RadarHandler::PraseRadar113},
            {Radar_RL114, &RadarHandler::PraseRadar114},
            {Radar_RL115, &RadarHandler::PraseRadar115},
            {Radar_RL116, &RadarHandler::PraseRadar116},
            {Radar_RL117, &RadarHandler::PraseRadar117},
            {Radar_RL118, &RadarHandler::PraseRadar118},
            {Radar_RL119, &RadarHandler::PraseRadar119},
            {Radar_RL11A, &RadarHandler::PraseRadar11A},
            
            {Radar_RR182, &RadarHandler::PraseRadar182},
            {Radar_RR183, &RadarHandler::PraseRadar183},
            {Radar_RR184, &RadarHandler::PraseRadar184},
            {Radar_RR185, &RadarHandler::PraseRadar185},
            {Radar_RR186, &RadarHandler::PraseRadar186},
            {Radar_RR187, &RadarHandler::PraseRadar187},
            {Radar_RR188, &RadarHandler::PraseRadar188},
            {Radar_RR189, &RadarHandler::PraseRadar189},
            {Radar_RR18A, &RadarHandler::PraseRadar18A},
            {Radar_RR18B, &RadarHandler::PraseRadar18B},
            {Radar_RR18C, &RadarHandler::PraseRadar18C},
            {Radar_RR18D, &RadarHandler::PraseRadar18D},
            {Radar_RR18E, &RadarHandler::PraseRadar18E},
            {Radar_RR18F, &RadarHandler::PraseRadar18F},
            {Radar_RR190, &RadarHandler::PraseRadar190},
            {Radar_RR191, &RadarHandler::PraseRadar191},
            {Radar_RR192, &RadarHandler::PraseRadar192},
            {Radar_RR193, &RadarHandler::PraseRadar193},
            {Radar_RR194, &RadarHandler::PraseRadar194},
            {Radar_RR195, &RadarHandler::PraseRadar195},
            {Radar_RR196, &RadarHandler::PraseRadar196},
            {Radar_RR197, &RadarHandler::PraseRadar197},
            {Radar_RR198, &RadarHandler::PraseRadar198},
            {Radar_RR199, &RadarHandler::PraseRadar199},
            {Radar_RR19A, &RadarHandler::PraseRadar19A}
#endif
        };  

    };
    
}
} 


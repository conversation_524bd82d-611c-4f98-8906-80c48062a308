#ifndef _IMU__H_
#define _IMU__H_

#include <stdint.h>
/*
 * imu sensors event type
typedef enum {
    EVENT_INVALID       = 0,
    EVENT_ACCEL_ONLY    = 1,
    EVENT_GYRO_ONLY     = 2,
    EVENT_ACCEL_GYRO    = 3,
    EVENT_TEMP_ONLY     = 4,
    EVENT_ACCEL_TEMP    = 5,
    EVENT_GYRO_TEMP     = 6,
    EVENT_ALL           = 7,
}event_type;
*/

typedef struct imu_sensors_data_t {
    /* sensor event type */
    int type;
    /* sequence number */
    uint64_t seq_id;
    /* time is in nanosecond */
    uint64_t timestamp;
    /* acceleration values m/s**2 */
    double acceleration_mg[3];
    /* gyroscope values rad/s */
    double angular_rate_mdps[3];
    /* temperature value degrees */
    double temperature;
}__attribute__((__packed__))imu_data_t;

#endif  //_IMU__H_

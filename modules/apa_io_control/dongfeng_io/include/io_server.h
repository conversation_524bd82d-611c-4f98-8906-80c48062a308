#ifndef __IO_SERVER_CA_H__
#define __IO_SERVER_CA_H__
#pragma once

#include "common_struct.h"
#ifndef CYBER_TIMER_EN
#include "timer_common.h"
#endif
#include "cyber_message_handle.h"
#include "inhouse_messenger.h"
#include "config_data.h"
#include "common_utils.h"
#include "exception_handle.h"
#include "thirdparty/common-algorithm/system_identification/include/slope_estimation_single_sensor.h"
#include "thirdparty/common-algorithm/system_identification/include/vehicle_mass_estimation_rls.h"


namespace io_server{
namespace dongfeng{

    class IOServerCA
    {
    public:
        IOServerCA(std::shared_ptr <Node> node, NodeCfg& nodecfg, string io_cfg_path);
        ~IOServerCA();
        void io_stop();
        void io_start();
        void io_eco();

    private:
        // std::shared_ptr <Node> pNode = nullptr;
#ifdef CYBER_TIMER_EN
        std::shared_ptr <apollo::cyber::Timer> pTimerHandleRadar = nullptr;
        std::shared_ptr <apollo::cyber::Timer> pTimerProcessTopic = nullptr;
#else   
        std::unique_ptr<TimerCommon::Timer> pTimerProcessTopic = nullptr;
        std::unique_ptr<TimerCommon::Timer> pTimerHandleRadar = nullptr;
#endif

    private:
        void timerProcessTopicHandler();
        void timerRadarHandler();

        void recordIOChassisDebugInfo(const StruIOChassisInfoDebug& io_chassis_info_debug, const long cur_time);
        void recordIORadarDebugInfo(const long cur_time);
        std::string calibrationDebugInfo(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void enterStop();
        void enterStart();
        void enterECO();
        bool systemIdentificationInit();
        std::tuple<double, double> systemIdentification(StruIOChassisInfoDebug& io_chassis_info_debug);

    private:
        std::shared_ptr<InhouseMessenger> inhouse_messenger_ = nullptr;
        std::shared_ptr<CyberMessenger> cyber_messenger_ = nullptr;

        bool b_inited_ = false;
        //0: start_mode 1:stop_mode 2:eco_mode
        std::atomic<uint32_t> io_module_mode_{0};

        //system_identification
        std::shared_ptr<pnc::system_identification::SingleSensorSlopeEstimation> slope_estimation_ = nullptr;
        std::shared_ptr<pnc::system_identification::RLSVehicleMassEstimation> mass_estimation_ = nullptr;

        //slope configure
        uint64_t slope_mean_ws_ = 8;  //内部数据过滤
        uint64_t slope_ws_ = 80; // 暂时未用
        bool slope_debug_ = true;
        double spd_limit_ = 0.1; //uint: m/s
        double slope_change_limited_min_ = 0.0017; //uint: rad
        double slope_change_limited_max_ = 0.02; //uint: rad

        //mass configure
        double real_mass_ = 2650.0;
        double mass_friction_ = 80.0;
        double mass_a_ = 0.0;
        double mass_b_ = 0.0;
        double mass_c_ = 0.0;
        double mass_offset_ = 0.0;
        double mass_min_speed_limited_ = 0.5;
        double mass_max_speed_limited_ = 7.0;
        double mass_acc_limited_ = 0.05;
        double mass_wheel_radius_ = 0.3937;
        double mass_slope_limited_ = 3.0;
        double mass_eps_limited_ = 30.0;
        double mass_speed_limited_ = 5.0;
        double mass_torque_limited_ = 25.0;
        bool mass_debug_ = true;
        bool use_system_identification_ = false;
        std::string si_debug_;

    };

template <typename T>
T my_clamp(const T& n, const T& lower, const T& upper) {
  return std::max(lower, std::min(n, upper));
}

}
}

#endif

#pragma once

#define USSLane_MAX_SIZE 5
#define PDC_Pose_SIZE 10
#define SDG_Pose_SIZE 6
#define  USS_INDEX 16
#define  USS_VERSION 22
#define USS_Slot_SIZE 6

typedef struct {
  uint16_t FMR;
  uint16_t FML;
  uint16_t FLS;
  uint16_t FL;
  uint16_t RLS;
  uint16_t RL;
  uint16_t FRS;
  uint16_t FR;
  uint16_t RRS;
  uint16_t RR;
  uint16_t RMR;
  uint16_t RML;
} __attribute__((packed)) p_Dis_T;

typedef struct {
  uint64_t FMR;
  uint64_t FML;
  uint64_t FLS;
  uint64_t FL;
  uint64_t RLS;
  uint64_t RL;
  uint64_t FRS;
  uint64_t FR;
  uint64_t RRS;
  uint64_t RR;
  uint64_t RMR;
  uint64_t RML;
} __attribute__((packed)) p_TimeStamp_T;

typedef struct {
  p_Dis_T DE;
  p_Dis_T PDC;
  p_TimeStamp_T TimeStamp;
} __attribute__((packed)) p_UssDistanceInfo_T;

typedef struct {
  int16_t Ax;
  int16_t Ay;
  int16_t Bx;
  int16_t By;
  int16_t Cx;
  int16_t Cy;
  int16_t Dx;
  int16_t Dy;
  int16_t Ex;
  int16_t Ey;
  int16_t Fx;
  int16_t Fy;
  int16_t Depth;
  uint16_t CurbDis;
  uint8_t SlotType;
  uint8_t SlotID;
  uint16_t Length_Width;
} __attribute__((packed)) p_UssSlot_T;

typedef p_UssSlot_T p_Rte_DT_UssSlotInfo_t_0[6];

typedef struct {
  p_Rte_DT_UssSlotInfo_t_0 UssSlot;
} __attribute__((packed)) p_UssSlotInfo_t;

typedef struct {
  p_UssDistanceInfo_T UssDistanceInfo;
  p_UssSlotInfo_t UssSlotInfo;
} __attribute__((packed)) p_USS_DataInfo_t;

typedef float p_Rte_DT_USS_EMapInfo_7[5];

typedef float p_Rte_DT_USS_EMapInfo_8[5];

typedef float p_Rte_DT_USS_EMapInfo_9[5];

typedef float p_Rte_DT_USS_EMapInfo_10[5];

typedef float p_Rte_DT_USS_EMapInfo_12[5];

typedef float p_Rte_DT_USS_EMapInfo_13[5];

typedef float p_Rte_DT_USS_EMapInfo_14[5];

typedef float p_Rte_DT_USS_EMapInfo_15[5];

typedef int16_t p_Rte_DT_USS_EMapInfo_16[10];

typedef int16_t p_Rte_DT_USS_EMapInfo_17[10];

typedef int16_t p_Rte_DT_USS_EMapInfo_18[6];

typedef int16_t p_Rte_DT_USS_EMapInfo_19[6];

typedef uint8_t p_Rte_DT_USS_EMapInfo_20[16];

typedef struct {
  uint8_t SlotUpdatePtB_Flag;
  float SlotUpdate_Bx;
  float SlotUpdate_By;
  uint8_t SlotUpdatePtC_Flag;
  float SlotUpdate_Cx;
  float SlotUpdate_Cy;
  // uint8_t Ultra_Left_Point_Num;
  // p_Rte_DT_USS_EMapInfo_7 Ultra_Left_Point1_x;
  // p_Rte_DT_USS_EMapInfo_8 Ultra_Left_Point1_y;
  // p_Rte_DT_USS_EMapInfo_9 Ultra_Left_Point2_x;
  // p_Rte_DT_USS_EMapInfo_10 Ultra_Left_Point2_y;
  // uint8_t Ultra_Right_Point_Num;
  // p_Rte_DT_USS_EMapInfo_12 Ultra_Right_Point1_x;
  // p_Rte_DT_USS_EMapInfo_13 Ultra_Right_Point1_y;
  // p_Rte_DT_USS_EMapInfo_14 Ultra_Right_Point2_x;
  // p_Rte_DT_USS_EMapInfo_15 Ultra_Right_Point2_y;
  p_Rte_DT_USS_EMapInfo_16 PDC_Pose_x;
  p_Rte_DT_USS_EMapInfo_17 PDC_Pose_y;
  p_Rte_DT_USS_EMapInfo_18 SDG_Pose_x;
  p_Rte_DT_USS_EMapInfo_19 SDG_Pose_y;
  p_Rte_DT_USS_EMapInfo_20 ObjType;
} __attribute__((packed)) p_USS_EMapInfo;

typedef uint8_t p_Rte_DT_USS_FaultInfo_0[22];

typedef struct {
  p_Rte_DT_USS_FaultInfo_0 UssFaultListArray;
} __attribute__((packed)) p_USS_FaultInfo;

typedef struct {
  float x;
  float y;
  uint8_t reset_flag;
} __attribute__((packed)) p_USS_DR;

typedef struct {
  uint8_t USS_FrontJam;
  uint8_t USS_RearJam;
} __attribute__((packed)) p_USS_Jan;

typedef uint8_t p_Rte_DT_USS_DebugInfo_0[32];

typedef struct {
  p_Rte_DT_USS_DebugInfo_0 UssFaultListArray;
} __attribute__((packed)) p_USS_DebugInfo;

typedef struct {
  p_USS_DataInfo_t USS_DataInfo;
  p_USS_EMapInfo USS_EMapInfo;
  p_USS_FaultInfo USS_FaultInfo;
  // p_USS_DR USS_DR;
  p_USS_Jan USS_Jan;
  p_USS_DebugInfo USS_DebugInfo;
} __attribute__((packed)) p_PiUssApp2DataInfo;

typedef uint8_t p_UssVersion_Array_t[22];

typedef struct spi_SendData {
  uint8_t d_type;
  uint8_t sig_kind;
  uint8_t index;
  uint64_t measurement_time;
  p_UssVersion_Array_t ver;
  p_PiUssApp2DataInfo ussData;
} __attribute__((packed)) spi_SendData_type;



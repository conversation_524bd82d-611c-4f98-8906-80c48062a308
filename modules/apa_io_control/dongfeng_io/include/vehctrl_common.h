#pragma once
#include <string.h>
#include "megaipc_api.h"
#include "can_api.h"

#define DEFAULT_CAN_MSG_FRAME_SIZE (74)
#define DEFAULT_LIN_MSG_FRAME_SIZE (18)

#define DEFAULT_CTRL_DATA_SIZE (8)
#define DEFAULT_CTRL_FRAME_SIZE (13)
#define DEFAULT_CTRL_DATA_FLAG (0x0A)
#define DEFAULT_SIGNAL_DATA_LEN (18)
#define SPI_CTRL_DATA_FLAG (0x11)

#define SPIRPC_APA_CONTROL_COMMAND_REQ     "APA/ControlCommand"
#define SPIRPC_APA_USS_DATA_REQ     "APA/UssData"

#define UARTRPC_SVC_TOPIC_VEH_CTRL_REQ     "uartrpc_svc/veh_ctrl/req"
#define UARTRPC_SVC_TOPIC_VEH_CTRL_RESP    "uartrpc_svc/veh_ctrl/resp"

#define UARTRPC_SVC_IPC_TOPIC_DOWNLINK "uartrpc/svc/downlink"
#define UARTRPC_SVC_IPC_TOPIC_UPLINK "uartrpc/svc/uplink"

#define SPIRPC_SVC_TOPIC_VEH_CTRL_REQ "spirpc_svc/veh_ctrl/req"
#define SPIRPC_SVC_TOPIC_VEH_CTRL_RESP "spirpc_svc/veh_ctrl/resp"

#ifdef __cplusplus
#include <typeinfo>

enum VehctrlStatusCode {
  VEHCTRL_STATUS_SUCCESS = 0,
  VEHCTRL_STATUS_CRC_ERROR = 1,
  VEHCTRL_STATUS_FORMAT_ERROR = 2,
  VEHCTRL_STATUS_COUNTER_SAME = 3
};

struct vehctrl_ackmsg {
  uint8_t proto_id{0};
  uint8_t msg_type{0};
  uint8_t cycle_count{0};
  uint8_t status_code{0};
};

/*
 * @brief 编码单帧下行控制信号(旧方案value使用float 统配所有类型存在经度转换损失)
 * @param  sig              CAN信号结构体
 * @param  value            信号值
 * @param  value_type       信号值类型
 * @param  out_data         编码后的数据
 */

int codec_vehctrl_data(struct veh_signal *sig, float value, enum signal_value_type value_type,
                       uint8_t *out_data, uint8_t msg_type = 0, uint8_t sig_count = 1,
                       bool spi_flag = false);

uint8_t next_cycle_count(bool spi_flag = false);

int codec_ctrl_frame_head(uint8_t *out_data, uint8_t msg_type = 0, uint8_t sig_count = 1,
                          bool spi_flag = false);

int codec_ctrl_frame_crc(uint8_t *frame_data, uint32_t frame_len);

uint8_t read_cycle_count(const uint8_t *frame_data);

/**
 * @brief 编码单个下行控制信号(新方案实现：使用模板类型T统配所有类型
 * 并校验与parser生成的sig_type是否匹配)
 * @param  sig              CAN信号结构体
 * @param  value            信号值
 * @param  out_data         编码后的数据
 */
template <typename T>
int codec_sigle_down_signal_data(struct veh_signal *sig, T value, uint8_t *out_data) {
  if (sig == NULL || out_data == NULL) {
    printf("Error: %s", "sig or out_data is null");
    return -1;
  }

  struct veh_message *p_msg = (struct veh_message *)sig->p_veh_message;
  if (p_msg == NULL) {
    printf("Error:, %s", "p_msg is null");
    return -1;
  }

  int length = DEFAULT_CTRL_DATA_SIZE;
  VehSignalValue_t val;
  memset(val.buffer, 0, 8);
  uint16_t start_bit = (uint16_t)sig->orig_start_bit;

  memcpy(out_data, (char *)&p_msg->msg_id, 2);
  memcpy(out_data + 2, (char *)&start_bit, 2);
  memcpy(out_data + 4, (char *)&length, 1);

  if (typeid(T) == typeid(float) && strcmp(sig->sig_type, "float") == 0) {
    val.val_float = value;
  } else if (typeid(T) == typeid(int32_t) && strcmp(sig->sig_type, "int32_t") == 0) {
    val.val_int32_t = value;
  } else if (typeid(T) == typeid(uint32_t) && strcmp(sig->sig_type, "uint32_t") == 0) {
    val.val_uint32_t = value;
  } else if (typeid(T) == typeid(int64_t) && strcmp(sig->sig_type, "int64_t") == 0) {
    val.val_int64_t = value;
  } else if (typeid(T) == typeid(uint64_t) && strcmp(sig->sig_type, "uint64_t") == 0) {
    val.val_uint64_t = value;
  } else {
    printf("Error: signal val type error for %s: %s, %s\n", sig->sig_name, typeid(T).name(), sig->sig_type);
    return -1;
  }
  memcpy(out_data + 5, val.buffer, length);
  return length;
}

int decode_ctrl_ackmsg(const uint8_t *data, uint32_t data_len, struct vehctrl_ackmsg *ackmsg);


static const uint32_t Crc8Table[256] = { //正规查询表即颠倒的查询表.
    0x00, 0xD5, 0x7F, 0xAA, 0xFE, 0x2B, 0x81, 0x54,
    0x29, 0xFC, 0x56, 0x83, 0xD7, 0x02, 0xA8, 0x7D,
    0x52, 0x87, 0x2D, 0xF8, 0xAC, 0x79, 0xD3, 0x06,
    0x7B, 0xAE, 0x04, 0xD1, 0x85, 0x50, 0xFA, 0x2F,
    0xA4, 0x71, 0xDB, 0x0E, 0x5A, 0x8F, 0x25, 0xF0,
    0x8D, 0x58, 0xF2, 0x27, 0x73, 0xA6, 0x0C, 0xD9,
    0xF6, 0x23, 0x89, 0x5C, 0x08, 0xDD, 0x77, 0xA2,
    0xDF, 0x0A, 0xA0, 0x75, 0x21, 0xF4, 0x5E, 0x8B,
    0x9D, 0x48, 0xE2, 0x37, 0x63, 0xB6, 0x1C, 0xC9,
    0xB4, 0x61, 0xCB, 0x1E, 0x4A, 0x9F, 0x35, 0xE0,
    0xCF, 0x1A, 0xB0, 0x65, 0x31, 0xE4, 0x4E, 0x9B,
    0xE6, 0x33, 0x99, 0x4C, 0x18, 0xCD, 0x67, 0xB2,
    0x39, 0xEC, 0x46, 0x93, 0xC7, 0x12, 0xB8, 0x6D,
    0x10, 0xC5, 0x6F, 0xBA, 0xEE, 0x3B, 0x91, 0x44,
    0x6B, 0xBE, 0x14, 0xC1, 0x95, 0x40, 0xEA, 0x3F,
    0x42, 0x97, 0x3D, 0xE8, 0xBC, 0x69, 0xC3, 0x16,
    0xEF, 0x3A, 0x90, 0x45, 0x11, 0xC4, 0x6E, 0xBB,
    0xC6, 0x13, 0xB9, 0x6C, 0x38, 0xED, 0x47, 0x92,
    0xBD, 0x68, 0xC2, 0x17, 0x43, 0x96, 0x3C, 0xE9,
    0x94, 0x41, 0xEB, 0x3E, 0x6A, 0xBF, 0x15, 0xC0,
    0x4B, 0x9E, 0x34, 0xE1, 0xB5, 0x60, 0xCA, 0x1F,
    0x62, 0xB7, 0x1D, 0xC8, 0x9C, 0x49, 0xE3, 0x36,
    0x19, 0xCC, 0x66, 0xB3, 0xE7, 0x32, 0x98, 0x4D,
    0x30, 0xE5, 0x4F, 0x9A, 0xCE, 0x1B, 0xB1, 0x64,
    0x72, 0xA7, 0x0D, 0xD8, 0x8C, 0x59, 0xF3, 0x26,
    0x5B, 0x8E, 0x24, 0xF1, 0xA5, 0x70, 0xDA, 0x0F,
    0x20, 0xF5, 0x5F, 0x8A, 0xDE, 0x0B, 0xA1, 0x74,
    0x09, 0xDC, 0x76, 0xA3, 0xF7, 0x22, 0x88, 0x5D,
    0xD6, 0x03, 0xA9, 0x7C, 0x28, 0xFD, 0x57, 0x82,
    0xFF, 0x2A, 0x80, 0x55, 0x01, 0xD4, 0x7E, 0xAB,
    0x84, 0x51, 0xFB, 0x2E, 0x7A, 0xAF, 0x05, 0xD0,
    0xAD, 0x78, 0xD2, 0x07, 0x53, 0x86, 0x2C, 0xF9,
};
#endif


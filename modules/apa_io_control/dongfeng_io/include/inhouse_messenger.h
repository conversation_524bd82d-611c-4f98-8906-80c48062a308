#pragma once
#include "common_utils.h"
#include "ipc_listener_interface.h"
#include "megaipc_api.h"
#include "structs/can_udp_packet_define.h"
#include "polym/Queue.hpp"
#include "parser_typedef.h"
#include "common_struct.h"
#include "json_parser.hpp"
#include "json.hpp"
#include "payload_info.h"
#include "cache_message.h"
#include "vehctrl_common.h"
#include "msg_ops.h"
#include "spi_rpc.h"
#include "uart_rpc.h"
#include "uss.h"
#include "thread_safe_queue.hpp"
#include "singleton.hpp"
#include "exception_handle.h"
#include "config_data.h"
#include "radar_handle.h"

#define MAX_VEHCTRL_DATA_LENGTH (100 * DEFAULT_CTRL_FRAME_SIZE + 4 + 1)

// 雷达开关信号，ic-->adas_io-->uartrpc
#define UARTRPC_APA_CONTROL_COMMAND_REQ     "APA/ControlCommand"
// 雷达数据信号：spirpc-->adas_io-->ic
#define APA_AVM_FRONT_RADAR "AVM/FrontRadar"
#define APA_AVM_REAR_RADAR "AVM/RearRadar"
#define APA_AVM_SIDE_RADAR "AVM/SideRadar"
#define APA_AVM_FRONT_MINEXT "AVM/FrontMinExt"
#define APA_AVM_REAR_MINEXT "AVM/RearMinExt"
#define APA_AVM_DKM_RPAREQ  "AVM/DKM/BLEAPASwReq"
#define APA_AVM_VIUL_RPAREQ "AVM/VIUL/PEPS_RKECommand2"

namespace io_server {
namespace dongfeng {

typedef enum ADAS_MSG_TYPE_ENUM
{
    ADAS_SPI_CAN_MSG = 0,
    ADAS_SPI_RAW_MSG,
    ADAS_UART_CAN_MSG,
} ADAS_MSG_TYPE_E;

/**
 * ReceiverListenerInterface用于接收上行信号
 * PubSubListenerInterface用于发送下行信号
 */
class InhouseMessenger : public IPCListenerInterface {
 public:
  InhouseMessenger();
  ~InhouseMessenger();
  // 上行信号相关
  void ParseAllRcvMsg();
  void ParseAllChassisCanRcvMsg();
  void ParseAllRawRcvMsg();

  void ParseAllRadarCanRcvMsg();
#if ENABLE_RADAR_CLOUD
  bool GetRadarDebugInfo(StrucNewRadarData& radar_data,
        std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>>& radar_map_data);
#else
  bool GetRadarDebugInfo(StrucNewRadarData& radar_data);
#endif
  void ResetRadarDebugInfoValid();

  // 下行信号相关
  
  // ReceiverListenerInterface
  // void onReceivedData(const char *data, int length);

  // PubSubListenerInterface
  void onMessageArrival(const std::string& topic, const IpcMessage& msg) override;
  void onMessageDelivered(const std::string& msgId) override;
  void onConnectStateChanged(const std::string& nodeId, const ConnectState& state) override;
  void onMessageArrival(const string &topic, const RequestMsg &req_msg, ResponseMsg **resp_msg) override;
  // void sendMsg() override;

  void RegisterSpirpcCallback();
  int32_t AdasUartCanMsgProcess(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data, uint64_t timestamp);
  int32_t AdasCanMsgProcess(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data, uint64_t timestamp);
  int32_t AdasRawMsgProcess(raw_udp_packet_t& raw_packet);
  int32_t AdasImuMsgProcess(raw_udp_packet_t& raw_packet, uint64_t timestamp);

  void ResetDebugInfoValid();
  void UpdateIOChassisInfo();
  void getSafeDebugInfo(StruIOChassisInfoDebug& io_chassis_info_debug);
  void getDebugInfo(StruIOChassisInfoDebug& io_chassis_info_debug);

  bool getRecordInfoStr(std::string& str);
  bool getRecordRawInfoStr(std::string& str);
  bool getRadarTargetRecordInfoStr(std::string& str){
      radar_handle_.getRadarTargetRecordInfoStr(str);
      return true;
  }
  bool getRadarCloudRecordInfoStr(std::string& str){
    radar_handle_.getRadarCloudRecordInfoStr(str);
      return true;
  }

  void SubscribeTopic();
  static void enterStop();
  static void enterStart();
  static void enterECO();

 private:
  bool ParamInit();
  template <typename T>
  void PublishIpcMessage(const std::string &topic, T value, long long time = 0,
                     nlohmann::json extension = nullptr);

  void ParsePDCUFC(uint64_t time);
  void ParseIBC143(uint64_t time);
  void ParseIBC12C(uint64_t time);
  void ParseIBC101(uint64_t time);
  void ParseIBCA2(uint64_t time);
  void ParseIBC172(uint64_t time);
  void ParseEPSB0(uint64_t time);
  void ParseEPSA5(uint64_t time);
  void ParseSGWC346(uint64_t time);
  void ParseSGWC40(uint64_t time);
  void ParseSGWC119(uint64_t time);
  void ParseSGWC710(uint64_t time);
  void ParseEPS17A(uint64_t time);
  void ParseIBC11D(uint64_t time);
  void ParsePDCU1C8(uint64_t time);
  void ParsePDCUFF(uint64_t time);
  void ParseSWGC23A(uint64_t time);
  void ParsePDCUFE(uint64_t time);
  void Parse_SGW_C_3F0(uint64_t time);
  void Parse_SGW_C_346(uint64_t time);
  void Parse_IBC_133(uint64_t time);
  
  void ParseSWGI304(uint64_t time);//ican
  void ParseSWGI30C(uint64_t time);//ican
  void ParseSWGI33D(uint64_t time);//ican

  void ParseAckIVI(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseAckException(uint64_t time, raw_udp_packet_t raw_packet);
  void ParsePubUSS(uint64_t time, raw_udp_packet_t raw_packet);
  void ParsePubHeartBeat(uint64_t time, raw_udp_packet_t raw_packet);
  void RecordPubHeartBeat(uint64_t time, raw_udp_packet_t raw_packet);
  void ParsePubException(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseAckShakeHand(uint64_t time, raw_udp_packet_t raw_packet);
  void ParsePubMCUEvent(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseAckControl(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseImuMsg(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseAckAPASt(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseAckTrqControl(uint64_t time, raw_udp_packet_t raw_packet);
  void ParseAckACCShakeHand(uint64_t time, raw_udp_packet_t raw_packet);

  std::atomic<bool> b_inited_{false};

  // ECO模式下需要存储解析的信号
  // 对应于以下解析格式，这里也采用三个列表
  //ccan_msg:15个, 0x12C, 0x3F0, 0x346, 0x172, 0x101, 0x23A, 0xFE, 0x11D, 0x1C8, 0xA2, 0xA5
  std::unordered_set<uint32_t> eco_ccanmsg_table_ = {
    SUB_IBC_12C, SUB_SGW_C_3F0, SUB_SGW_C_346, SUB_IBC_172, SUB_IBC_101, 
    SUB_SGW_C_23A, SUB_PDCU_FE, SUB_IBC_11D, SUB_PDCU_1C8, SUB_IBC_A2,
    SUB_EPS_A5
  };
  //ican_msg: 3个, 0x33D, 0x30C, 0x304
  std::unordered_set<uint32_t> eco_icanmsg_table_ = {
    SUB_SGW_I_33D, SUB_SGW_I_30C, SUB_SGW_I_304
  };
  //raw_msg: 3个, 0x99, 0x05, 0x06
  std::unordered_set<uint32_t> eco_rawmsg_table_ = {
    SUB_IMU_MSG, SUB_ACK_EX, SUB_PUB_EX
  };
  
  // 上行信号相关
  typedef void (InhouseMessenger::*pFun)(uint64_t time);
  std::unordered_map<uint32_t, pFun> ccanmsg_parser_map_ = {
    {SUB_PDCU_FC, &InhouseMessenger::ParsePDCUFC},
    {SUB_IBC_143, &InhouseMessenger::ParseIBC143},
    {SUB_IBC_12C, &InhouseMessenger::ParseIBC12C},
    {SUB_IBC_101, &InhouseMessenger::ParseIBC101},
    {SUB_IBC_A2, &InhouseMessenger::ParseIBCA2},
    {SUB_IBC_172, &InhouseMessenger::ParseIBC172},
    {SUB_EPS_B0, &InhouseMessenger::ParseEPSB0},
    {SUB_EPS_A5, &InhouseMessenger::ParseEPSA5},
    {SUB_SGW_C_40, &InhouseMessenger::ParseSGWC40},
    {SUB_SGW_C_119, &InhouseMessenger::ParseSGWC119},
    {SUB_SGW_C_710, &InhouseMessenger::ParseSGWC710},
    {SUB_EPS_17A, &InhouseMessenger::ParseEPS17A},
    {SUB_IBC_11D, &InhouseMessenger::ParseIBC11D},
    {SUB_PDCU_1C8, &InhouseMessenger::ParsePDCU1C8},
    {SUB_PDCU_FF, &InhouseMessenger::ParsePDCUFF},
    {SUB_SGW_C_23A, &InhouseMessenger::ParseSWGC23A},
    {SUB_PDCU_FE, &InhouseMessenger::ParsePDCUFE},
    {SUB_SGW_C_3F0, &InhouseMessenger::Parse_SGW_C_3F0},
    {SUB_SGW_C_346, &InhouseMessenger::Parse_SGW_C_346},
    {SUB_IBC_133, &InhouseMessenger::Parse_IBC_133}
  };  
  std::unordered_map<uint32_t, pFun> icanmsg_parser_map_ = {
    {SUB_SGW_I_30C, &InhouseMessenger::ParseSWGI30C},
    {SUB_SGW_I_304, &InhouseMessenger::ParseSWGI304},
    {SUB_SGW_I_33D, &InhouseMessenger::ParseSWGI33D}
  };

  typedef void (InhouseMessenger::*pFunc)(uint64_t time, raw_udp_packet_t raw_packet);
  std::unordered_map<uint32_t, pFunc> rawmsg_parser_map_ = {
    {SUB_ACK_IVI, &InhouseMessenger::ParseAckIVI},
    {SUB_ACK_EX, &InhouseMessenger::ParseAckException},
    {SUB_PUB_USS, &InhouseMessenger::ParsePubUSS},
    // {SUB_PUB_HB, &InhouseMessenger::ParsePubHeartBeat},//放在回调中解析并处理了
    {SUB_PUB_HB, &InhouseMessenger::RecordPubHeartBeat},
    {SUB_PUB_EX, &InhouseMessenger::ParsePubException},
    {SUB_ACK_SH, &InhouseMessenger::ParseAckShakeHand},
    {SUB_PUB_MCU_ET, &InhouseMessenger::ParsePubMCUEvent},
  #ifndef BUILD_HIL_TEST
    // hil测试不需要解析板载imu信号，否则会干扰正常测试
    {SUB_IMU_MSG, &InhouseMessenger::ParseImuMsg},
  #endif
    {SUB_ACK_CTL, &InhouseMessenger::ParseAckControl},
    {SUB_ACK_APA_ST, &InhouseMessenger::ParseAckAPASt},
    {SUB_ACK_TRQ_CTL, &InhouseMessenger::ParseAckTrqControl},
    {SUB_ACK_ACC_SH, &InhouseMessenger::ParseAckACCShakeHand}
  };  

  megaipc::MegaIpcApi* megaipc_instance_;
  std::shared_mutex prase_data_mtx_;//no use
  StruIOChassisInfoDebug io_chassis_info_debug_;
  StruIOChassisInfoDebug io_chassis_info_debug_tmp_;

  void heartBeatCheck();
  std::queue<uint64_t> time_queue_;//用于确定mcu心跳
  std::size_t time_queue_capacity_ = 50;
  uint64_t overtime_span_warn_ = 200;
  uint64_t overtime_span_error_ = 5000;

  //imu whlspeed rate (1s)
  uint32_t imu_record_start_ = 0;
  uint32_t imu_record_num_ = 0;
  uint32_t whlspd_record_start_ = 0;
  uint32_t whlspd_record_num_ = 0;
  uint32_t warnning_rate_ = 5;
  void imuWhlSpdRateMonitor();
  std::map<uint32_t, std::string> io_code_name_map_={
      {0x07010001, "IMU_SIG_RATE_EXCEPT"},
      {0x07010002, "WHL_SPEED_SIG_RATE_EXCEPT"},
      {0x07000001, "MCU_HEARTBEAT_EXCEPT"}
  };

  //init queue for saving can msg and raw msg
  std::shared_ptr<ThreadSafeQueue<std::pair<uint64_t, can_udp_packet_t>>> can_msg_queue_;// timestamp, can_udp_packet_t,优化方向（将不需要的信息直接剔除）
  std::shared_ptr<ThreadSafeQueue<std::pair<uint64_t, raw_udp_packet_t>>> raw_msg_queue_;//应答消息类型, data

  // int32_t SpiCanMsgProcessWrapper(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data);
  std::string debug_info_;
  std::string debug_info_raw_;

  RadarHandler radar_handle_;

public:
  //0: start_mode 1:stop_mode 2:eco_mode
  static std::atomic<uint32_t> io_module_mode_;

};

std::shared_ptr<InhouseMessenger> InitInhouseMessenger();
void PubUartrpcMsg();
void PubHandShakeReqUartrpcMsg(uint8_t eps, uint8_t ibc);
void PubACCHandShakeReqUartrpcMsg(uint8_t eps, uint8_t ibc);
void PubAppRpaRespUartrpcMsg(StruInputAppRpaResp &rpa_resp);
void PubKeyRpaRespUartrpcMsg(StruInputKeyRpaResp &rpa_resp);
void PubVehCtrlReqUartrpcMsg(uint8_t brake_mode, uint16_t distance, uint16_t velocity,
                                                          uint8_t angle_flag, int16_t angle, uint8_t gear_flag, uint8_t gear);
void PubTrqVehCtrlReqUartrpcMsg(StruInputLatAndLongTrqControlCmd& trq_control_cmd);
void PubExceptionUartrpcMsg(uint32_t* error, uint8_t len);
void PubExceptionUartrpcMsg(uart_down_exception_t_& sig, uint8_t len);
void PubAnsUartrpcMsg(uint8_t index, uint8_t sig_kind);
void PubAPAStatusMsg(apa_status_t_& sig);
void PubAEBControllerUartrpcMsg(StruInputAEBController& aeb_cmd);

} // namespace dongfeng
} // namespace io_server

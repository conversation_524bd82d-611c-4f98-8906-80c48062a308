#ifndef __LIB_UART_RPC_H__
#define __LIB_UART_RPC_H__

#include <stdint.h>

/*
* Callback function pointer type.
*
* bus_id - CAN bus ID.
* can_id - CAN ID.
* len    - data length in bytes.
* data   - data
* return value: 0 for success, -1 for failure.
*/
typedef int32_t (*uartrpc_msg_callback_t)(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t* data);


/*
* Initialize the UART RPC channel.
*
* dev_name - the uartrpc dev_name, /dev/apa_uartrpc for APA, /dev/eoc_uartrpc for EOC
* chn_idx  - the RPC channel index to be initialized.
* msg_cb   - the callback for this RPC channel.
* return value: 0 for success, -1 for failure.
*
* The library will open the specified RPC channel,
* receive RPC msg via the RPC channel, parse the received RPC msg and
* invoke the callback to process each app data packaged in the received
* RPC msg.
*/
int32_t uartrpc_init(char* dev_name, int32_t chn_idx, uartrpc_msg_callback_t msg_cb);


#endif

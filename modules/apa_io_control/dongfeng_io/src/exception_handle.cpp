#include "exception_handle.h"
#include "inhouse_messenger.h"

namespace io_server {
namespace dongfeng{
      //exception handle

  bool ExceptionHandle::initExceptionHandleData(){
    mcu_exception_queue_.reset(new ThreadSafeQueue<uint32_t>(200));
    soc_exception_queue_.reset(new ThreadSafeQueue<uint32_t>(200));
    return true;
  }

  void ExceptionHandle::enterStop(){
    io_module_mode_ = 1;
  }

  void ExceptionHandle::enterStart(){
    io_module_mode_ = 0;
  }

  void ExceptionHandle::enterECO(){
    io_module_mode_ = 2;
  }

  //20ms的周期
  void ExceptionHandle::ExceptionHandler(){
    resendCachedMsg();
    MCUExceptionHandler();
    SOCExceptionHandler();
  }

  void ExceptionHandle::resendCachedMsg() {
    //一直发送，直到没有了，20ms的周期
    if(0 == ConfigData::Instance()->use_resend_function_) return;
    std::unique_lock<std::shared_mutex> lk(uart_msg_mutex_);
    if(0 == io_module_mode_){
      AINFO<<__func__<<": resend_size="<<soc_exception_cached_map_.size();
    }
    for (auto iter = soc_exception_cached_map_.begin(); iter != soc_exception_cached_map_.end(); ++iter){
      PubExceptionUartrpcMsg(*(iter->second),iter->second->len);
    }
  }

  void ExceptionHandle::cacheMsg(std::shared_ptr<uart_down_exception_t_> msg) {
    if(0 == ConfigData::Instance()->use_resend_function_) return;
    std::unique_lock<std::shared_mutex> lk(uart_msg_mutex_);
    soc_exception_cached_map_[msg->index] = msg;
  }

  void ExceptionHandle::removeCachedMsg(uint8_t cycle_count) {
    if(0 == ConfigData::Instance()->use_resend_function_) return;
    std::unique_lock<std::shared_mutex> lk(uart_msg_mutex_);
      auto it = soc_exception_cached_map_.find(cycle_count);
    if (it != soc_exception_cached_map_.end()) {
      soc_exception_cached_map_.erase(it);
    }
  }

  void ExceptionHandle::MCUExceptionHandler(){
  //先进行异常的过滤，将本次的，或许不需要过滤，直接发送异常就可以了
  std::vector<uint32_t> vec_rev_msg;
  mcu_exception_queue_->ExtractAll(&vec_rev_msg);
  if(vec_rev_msg.empty()){
    // AINFO << "mcu_exception msg is empty.";
  }
  else{
    int msg_size = vec_rev_msg.size();
    if(0 == io_module_mode_){
      AINFO << "has mcu_exception msg, mcu_exception_size="<<msg_size;
    }
    for(int i = 0;i < msg_size;i++){
      auto iter = mcu_code_name_map_.find(vec_rev_msg.at(i));
      if(iter != mcu_code_name_map_.end()){
        PubException(static_cast<ExceptionType>(iter->first), iter->second);
      }
      else{
        if(0 == io_module_mode_){
          AINFO<< __func__<<": cant find this error type from mcu, mcu_error_type="<<vec_rev_msg.at(i);
        }
      }
    }
  }
}

//可以做成20ms处理一次
void ExceptionHandle::SOCExceptionHandler(){
  //这里需要做ack逻辑，暂时先不做ack逻辑，可以按照定期器的周期10ms
  static uint8_t index = 0;
  std::vector<uint32_t> vec_rev_msg;
  soc_exception_queue_->ExtractAll(&vec_rev_msg);
  if(vec_rev_msg.empty()){
    // AINFO << "soc_exception msg is empty.";
  }
  else{
    int msg_size = vec_rev_msg.size();
    if(0 == io_module_mode_){
      AINFO << "has soc_exception msg, soc_exception_size="<<msg_size;
    }
    uart_down_exception_t_ sig;
    sig.index = index;
    sig.len = msg_size;
    for(int i=0; i<msg_size; i++){
      sig.value[i] = vec_rev_msg.at(i);
      // std::cout<<__func__<<", soc_exception_size="<<msg_size<<
      // ", exception_"<<i<<"="<<vec_rev_msg.at(i)<<
      // std::endl;
    }
    if(++index > 255){
      index = 0;
    }

    cacheMsg(std::make_shared<uart_down_exception_t_>(sig));
    PubExceptionUartrpcMsg(sig, msg_size);
  }
}

void ExceptionHandle::PubException(ExceptionType err_type, const std::string& name) {

      ExceptionPtr msg_ptr{};
      msg_ptr.set_timestamp(GetTimeS(nullptr));
      msg_ptr.set_code((uint64_t) err_type);
      msg_ptr.set_name(name);
      if(0 == io_module_mode_){
        AINFO<<__func__ <<" io exception name : "<< name <<  " code :0x " << std::hex <<  (uint32_t)err_type;
      }
      pExceptionCaptureInfo->Write(msg_ptr);
  }

  bool ExceptionHandle::IsMCUException(uint32_t exception_code){
    auto iter = mcu_code_name_map_.find(exception_code);
      if(iter != mcu_code_name_map_.end()){
         return true;
      }
      return false;
  }

}
}

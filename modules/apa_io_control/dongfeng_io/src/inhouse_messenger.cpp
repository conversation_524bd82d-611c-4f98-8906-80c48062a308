#include "inhouse_messenger.h"

#include <chrono>
#include <mutex>
#include <sstream>
#include <thread>

extern "C" {
#include "can_api.h"
#include "fc_can_api.h"
#include "fl_can_api.h"
#include "fr_can_api.h"
#include "rl_can_api.h"
#include "rr_can_api.h"
} // extern "C"
#include "string.h"

namespace io_server {
namespace dongfeng {

std::atomic<uint32_t> InhouseMessenger::io_module_mode_{0};

std::shared_ptr<InhouseMessenger> instance_;
// std::shared_ptr<InhouseMessenger> instance_(new InhouseMessenger);

std::shared_ptr<InhouseMessenger> InitInhouseMessenger() {
  if (!instance_) {
    instance_ = std::make_shared<InhouseMessenger>();
    // instance_ = new InhouseMessenger;
    // std::shared_ptr<int> p(new int);

  }
  return instance_;
}

/*!< 每帧CAN消息最大字节长度 */
const uint8_t NUM_BYTES_PER_FRAME = 64;

// muc_service转发udp can报文格式为{bus_id}+{can_id}+{msg_len}+{msg_content}+{timestamp}
const uint8_t MIN_NUM_BYTES_PER_PACKET = sizeof(can_udp_packet_t::bus_id) +
                                         sizeof(can_udp_packet_t::can_id) +
                                         sizeof(can_udp_packet_t::message_len) + 8;

void printHex(const uint8_t *data, int len) {
  char buff[len * 3 + 1] = {0};
  for (int i = 0; i < len; i++) {
    sprintf(buff + i * 3, "%02x ", data[i]);
  }
}

template<typename T>
uint8_t* DuplicateBuffer(uint8_t *data, size_t size = 0) {
  size_t buf_size = size > 0 ? size : sizeof(T);
  uint8_t *buf = (uint8_t *)malloc(buf_size);
  memcpy(buf, data, buf_size);
  return buf;
}

int32_t SpiImuMsgProcessWrapper(uint8_t *data, int32_t len) {
  raw_udp_packet_t raw_udp_packet_tmp;
  raw_udp_packet_tmp.ctl_cmd =  0x5A;
  raw_udp_packet_tmp.ack_type =  SUB_IMU_MSG;
  raw_udp_packet_tmp.len =  len;
  memcpy(raw_udp_packet_tmp.data, data, raw_udp_packet_tmp.len);
  imu_data_t* ptr = (imu_data_t*)(&raw_udp_packet_tmp.data);
  raw_udp_packet_tmp.index =  ptr->seq_id;
  instance_->AdasImuMsgProcess(raw_udp_packet_tmp, ptr->timestamp);
  return 0;
}

int32_t SpiCanMsgProcessWrapper(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data, uint64_t timestamp) {
  return instance_->AdasCanMsgProcess(bus_id, can_id, len, data, timestamp);
}

int32_t UartCanMsgProcessWrapper(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data, uint64_t timestamp) {
  return instance_->AdasUartCanMsgProcess(bus_id, can_id, len, data, timestamp);
}

int32_t SpiRawMsgProcessWrapper(uint8_t *data, int32_t size) {
  if (!data || data[0] != 0x5A) {
    AINFO << "invalid data received...";
    return -1;
  }
  raw_udp_packet_t raw_udp_packet_tmp;
  raw_udp_packet_tmp.ctl_cmd =  data[0];
  raw_udp_packet_tmp.ack_type =  data[1];
  raw_udp_packet_tmp.index =  data[2];
  raw_udp_packet_tmp.len =  size;
  memcpy(raw_udp_packet_tmp.data, data, raw_udp_packet_tmp.len);

  instance_->AdasRawMsgProcess(raw_udp_packet_tmp);
  return 0;
}

InhouseMessenger::InhouseMessenger() {
  AINFO << "begin to init inhouse messenger...";
  ParamInit();
  can_msg_queue_.reset(new ThreadSafeQueue<std::pair<uint64_t, can_udp_packet_t>>(120));
  raw_msg_queue_.reset(new ThreadSafeQueue<std::pair<uint64_t, raw_udp_packet_t>>(100));
  megaipc_instance_ = megaipc::MegaIpcApi::instance().createInstance();
  megaipc_instance_->init();
  SubscribeTopic();
  megaipc_instance_->setListener(this);

  radar_handle_.Init();
  b_inited_ = true;
}

void InhouseMessenger::enterStop(){
  io_module_mode_ = 1;
}

void InhouseMessenger::enterStart(){
  io_module_mode_ = 0;
}

void InhouseMessenger::enterECO(){
  io_module_mode_ = 2;
}

bool InhouseMessenger::ParamInit(){
  if(false == ConfigData::Instance()->is_valid_){
    AINFO<<__func__<<": ConfigData is invalid....";
    return false;
  }
  overtime_span_warn_ = ConfigData::Instance()->overtime_span_warn_;
  overtime_span_error_ = ConfigData::Instance()->overtime_span_error_;
  warnning_rate_ = ConfigData::Instance()->warnning_rate_;
  return true;
}

InhouseMessenger::~InhouseMessenger() { 
  AINFO << "inhouse messenger destoryed...";
}

void InhouseMessenger::SubscribeTopic() {
    if (megaipc_instance_->subscribe(UARTRPC_APA_CONTROL_COMMAND_REQ) == false) {
      AINFO << __func__ << ": Subscribe topic " << SPIRPC_APA_CONTROL_COMMAND_REQ << " failed.";
    } else {
      AINFO << __func__ << ": Subscribe topic " << SPIRPC_APA_CONTROL_COMMAND_REQ << " succeed.";
    }
}

void InhouseMessenger::RegisterSpirpcCallback() {
  spirpc_can_init(ADAS_SPI_CAN_MSG, SpiCanMsgProcessWrapper);
  uartrpc_can_init(ADAS_SPI_CAN_MSG, UartCanMsgProcessWrapper);
  spirpc_raw_init(ADAS_SPI_RAW_MSG, SpiRawMsgProcessWrapper);
  imu_raw_init(SpiImuMsgProcessWrapper);
}

int32_t InhouseMessenger::AdasCanMsgProcess(uint8_t bus_id, uint32_t can_id, uint8_t len,
                                         uint8_t *data, uint64_t timestamp) {
  if (len <= 0 || !b_inited_) {
    return -1;
  }
  if(1 == io_module_mode_){//stop mode
    can_msg_queue_->Clear();
    radar_handle_.ClearRadarData();
    return 0;
  }
  can_udp_packet_t can_udp_packet;
  can_udp_packet.bus_id = bus_id;
  can_udp_packet.can_id = can_id;
  can_udp_packet.message_len = len;
  memcpy(can_udp_packet.data, data, len);

  std::pair<uint64_t, can_udp_packet_t> time_can(timestamp, can_udp_packet);
  if(2 == io_module_mode_){//eco mode
    if(can_udp_packet.bus_id >= 2 && can_udp_packet.bus_id <= 4 && 
        radar_handle_.IsRadarCanData(can_udp_packet.can_id)){
      //雷达数据单独保存队列
      radar_handle_.EnqueueRadarData(time_can);
    }
    else{
      //bus_id = 1 ccanmsg
      if(1 == can_udp_packet.bus_id){
        auto it = eco_ccanmsg_table_.find(can_udp_packet.can_id);
        if(it != eco_ccanmsg_table_.end()){
          can_msg_queue_->Enqueue(time_can);
        }
      }
      //bus_id = 0 icanmsg
      if(0 == can_udp_packet.bus_id){
        auto it = eco_icanmsg_table_.find(can_udp_packet.can_id);
        if(it != eco_icanmsg_table_.end()){
          can_msg_queue_->Enqueue(time_can);
        }
      }
    }
  }
  else if(0 == io_module_mode_){//start mode
    if(can_udp_packet.bus_id >= 2 && can_udp_packet.bus_id <= 4 && 
        radar_handle_.IsRadarCanData(can_udp_packet.can_id)){
      //雷达数据单独保存队列
      radar_handle_.EnqueueRadarData(time_can);
    }
    else{
      can_msg_queue_->Enqueue(time_can);
    }
  }
  return 1;
}

int32_t InhouseMessenger::AdasUartCanMsgProcess(uint8_t bus_id, uint32_t can_id, uint8_t len,
                                         uint8_t *data, uint64_t timestamp) {
  if (len <= 0 || !b_inited_) {
    return -1;
  }
  if(1 == io_module_mode_){//stop mode
    can_msg_queue_->Clear();
    return 0;
  }
  can_udp_packet_t can_udp_packet;
  can_udp_packet.bus_id = bus_id;
  can_udp_packet.can_id = can_id;
  can_udp_packet.message_len = len;
  memcpy(can_udp_packet.data, data, len);

  std::pair<uint64_t, can_udp_packet_t> time_can(timestamp, can_udp_packet);
  if(2 == io_module_mode_){//eco mode
      //bus_id = 1 ccanmsg
      if(1 == can_udp_packet.bus_id){
        auto it = eco_ccanmsg_table_.find(can_udp_packet.can_id);
        if(it != eco_ccanmsg_table_.end()){
          can_msg_queue_->Enqueue(time_can);
        }
      }
      //bus_id = 0 icanmsg
      if(0 == can_udp_packet.bus_id){
        auto it = eco_icanmsg_table_.find(can_udp_packet.can_id);
        if(it != eco_icanmsg_table_.end()){
          can_msg_queue_->Enqueue(time_can);
        }
      }
  }
  else if(0 == io_module_mode_){//start mode
      can_msg_queue_->Enqueue(time_can);
  }
  return 1;
}

int32_t InhouseMessenger::AdasRawMsgProcess(raw_udp_packet_t& raw_packet){
  if(!b_inited_){
    return -1;
  }
  if(1 == io_module_mode_){//stop mode
    raw_msg_queue_->Clear();
    return 0;
  }
  uint64_t cur_time = GetTimeInMillis();
  if(0x2 == raw_packet.ack_type){//在回调函数中回复心跳
    ParsePubHeartBeat(cur_time,raw_packet);
  }
  if(2 == io_module_mode_){//eco mode
    auto it = eco_rawmsg_table_.find(raw_packet.ack_type);
    if(it != eco_rawmsg_table_.end()){
      raw_msg_queue_->Enqueue(std::pair<uint64_t, raw_udp_packet_t>(cur_time, raw_packet));
    }
  }
  else{
    raw_msg_queue_->Enqueue(std::pair<uint64_t, raw_udp_packet_t>(cur_time, raw_packet));
  }
  return 1;
}

int32_t InhouseMessenger::AdasImuMsgProcess(raw_udp_packet_t& raw_packet, uint64_t timestamp){
  if(!b_inited_){
    return -1;
  }
  if(1 == io_module_mode_){//stop mode
    raw_msg_queue_->Clear();
    return 0;
  }
  if(2 == io_module_mode_){//eco mode
    auto it = eco_rawmsg_table_.find(raw_packet.ack_type);
    if(it != eco_rawmsg_table_.end()){
      raw_msg_queue_->Enqueue(std::pair<uint64_t, raw_udp_packet_t>(timestamp, raw_packet));
    }
  }
  else{
    raw_msg_queue_->Enqueue(std::pair<uint64_t, raw_udp_packet_t>(timestamp, raw_packet));
  }
  return 1;
}

void InhouseMessenger::ParseAllRcvMsg() {
  ParseAllChassisCanRcvMsg();
  ParseAllRawRcvMsg();
  //update other msg
  UpdateIOChassisInfo();
  //heart beat check
  heartBeatCheck();
  //imu whlspd monitor
  imuWhlSpdRateMonitor();
  //save io_chassis_info
  std::unique_lock<std::shared_mutex> prase_data_lock(prase_data_mtx_, std::defer_lock);
  prase_data_lock.lock();
  io_chassis_info_debug_ = io_chassis_info_debug_tmp_;
  prase_data_lock.unlock();
}

void InhouseMessenger::ParseAllRadarCanRcvMsg(){
  radar_handle_.ParseAllRadarCanRcvMsg();
}

#if ENABLE_RADAR_CLOUD
bool InhouseMessenger::GetRadarDebugInfo(StrucNewRadarData& radar_data,
        std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>>& radar_map_data){
          radar_handle_.GetRadarDebugInfo(radar_data, radar_map_data);
          return true;
}
#else
bool InhouseMessenger::GetRadarDebugInfo(StrucNewRadarData& radar_data){
          radar_handle_.GetRadarDebugInfo(radar_data);
          return true;
}
#endif

void InhouseMessenger::ResetRadarDebugInfoValid(){
  radar_handle_.ResetRadarDebugInfoValid();
}

  void InhouseMessenger::ResetDebugInfoValid() {
    io_chassis_info_debug_tmp_.imu_valid = 0;
    io_chassis_info_debug_tmp_.uss_valid = 0;

    io_chassis_info_debug_tmp_.ibc_101_valid = 0;
    io_chassis_info_debug_tmp_.ibc_172_valid = 0;

    io_chassis_info_debug_tmp_.eps_b0_valid = 0;
    io_chassis_info_debug_tmp_.ibc_143_valid = 0;

    io_chassis_info_debug_tmp_.eps_17a_valid = 0;
    io_chassis_info_debug_tmp_.ibc_a2_valid = 0;
    io_chassis_info_debug_tmp_.ibc_12c_valid = 0;
    io_chassis_info_debug_tmp_.ibc_11d_valid = 0;
    io_chassis_info_debug_tmp_.pdcu_1c8_valid = 0;
    io_chassis_info_debug_tmp_.pdcu_ff_valid = 0;
    io_chassis_info_debug_tmp_.swgc_119_valid = 0;
    io_chassis_info_debug_tmp_.pdcu_fe_valid = 0;

    io_chassis_info_debug_tmp_.shakehand_valid = 0;

    io_chassis_info_debug_tmp_.swgc_23a_valid = 0;
    io_chassis_info_debug_tmp_.swgi_30c_valid = 0;
    io_chassis_info_debug_tmp_.swgi_304_valid = 0;
    io_chassis_info_debug_tmp_.swgi_33d_valid = 0;
    io_chassis_info_debug_tmp_.msg_SGW_C_3F0_valid = 0;
    io_chassis_info_debug_tmp_.msg_SGW_C_346_valid = 0;

    io_chassis_info_debug_tmp_.spa_st_valid = 0;

    while(!io_chassis_info_debug_tmp_.ibc_172_pls_vec.empty()){
      io_chassis_info_debug_tmp_.ibc_172_pls_vec.pop();
    }

    while(!io_chassis_info_debug_tmp_.swgc_119_imu_vec.empty()){
      io_chassis_info_debug_tmp_.swgc_119_imu_vec.pop();
    }

    while(!io_chassis_info_debug_tmp_.swgc_710_imu_vec.empty()){
      io_chassis_info_debug_tmp_.swgc_710_imu_vec.pop();
    }

    while(!io_chassis_info_debug_tmp_.soc_imu_vec.empty()){
      io_chassis_info_debug_tmp_.soc_imu_vec.pop();
    }

  };

  void InhouseMessenger::UpdateIOChassisInfo() {
    if (static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug_tmp_.eps_shakehand_sts &&
        static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug_tmp_.ibc_shakehand_sts) {
      io_chassis_info_debug_tmp_.takeover_stat = 1;
    } else if (static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDQUITSUS) == io_chassis_info_debug_tmp_.eps_shakehand_sts &&
        static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDQUITSUS) == io_chassis_info_debug_tmp_.ibc_shakehand_sts) {
      io_chassis_info_debug_tmp_.takeover_stat = 2;
    }
    else{
      io_chassis_info_debug_tmp_.takeover_stat = 3;
    }
  }

  void InhouseMessenger::getSafeDebugInfo(StruIOChassisInfoDebug& io_chassis_info_debug) {
    std::shared_lock<std::shared_mutex> prase_data_lock(prase_data_mtx_);
    io_chassis_info_debug = io_chassis_info_debug_;
  }

  void InhouseMessenger::getDebugInfo(StruIOChassisInfoDebug& io_chassis_info_debug) {
    io_chassis_info_debug = io_chassis_info_debug_;
  }

  bool InhouseMessenger::getRecordInfoStr(std::string& str){
    str = std::move(debug_info_);
    return true;
  }

  bool InhouseMessenger::getRecordRawInfoStr(std::string& str){
    str = std::move(debug_info_raw_);
    return true;
  }

void InhouseMessenger::ParseAllChassisCanRcvMsg() {
  std::vector<std::pair<uint64_t, can_udp_packet_t>> vec_rev_msg;
  vec_rev_msg.reserve(can_msg_queue_->size());
  can_msg_queue_->ExtractAll(&vec_rev_msg);
  if(vec_rev_msg.empty()){
    // AINFO << "can msg is empty.";
  }
  else{
    int msg_size = vec_rev_msg.size();
    //AINFO << "has can msg, msg_size="<<msg_size;
    for(int i = 0;i < msg_size;i++){
      std::pair<uint64_t, can_udp_packet_t> msgx = vec_rev_msg.at(i);
      can_udp_packet_t can_packet = msgx.second;
      //prase bus_id = 1
      if(1 == can_packet.bus_id){
        auto it = ccanmsg_parser_map_.find(can_packet.can_id);
        if(it != ccanmsg_parser_map_.end()){
          CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                              can_packet.message_len, can_packet.data);//
          (this->*(it->second))(msgx.first);
        }
      }
      //prase bus_id = 0
      if(0 == can_packet.bus_id){
        auto it = icanmsg_parser_map_.find(can_packet.can_id);
        if(it != icanmsg_parser_map_.end()){
          CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                              can_packet.message_len, can_packet.data);//
          (this->*(it->second))(msgx.first);
        }
      }
    }
  }
}

void InhouseMessenger::ParseAllRawRcvMsg() {
  std::vector<std::pair<uint64_t, raw_udp_packet_t>> vec_rev_msg;
  vec_rev_msg.reserve(raw_msg_queue_->size());
  raw_msg_queue_->ExtractAll(&vec_rev_msg);
  if(vec_rev_msg.empty()){
    // AINFO << "raw msg is empty.";
  }
  else{
    int msg_size = vec_rev_msg.size();
    //AINFO << "has raw msg, raw_msg_size="<<msg_size;
    for(int i = 0;i < msg_size;i++){
      std::pair<uint64_t, raw_udp_packet_t> msgx = vec_rev_msg.at(i);
      raw_udp_packet_t raw_packet = msgx.second;

      auto it = rawmsg_parser_map_.find(raw_packet.ack_type);
      if(it != rawmsg_parser_map_.end()){
        (this->*(it->second))(msgx.first, msgx.second);
      }
    }
  }
}

void InhouseMessenger::ParsePDCUFC(uint64_t time) {}

void InhouseMessenger::ParseIBC143(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.ibc_143_measurement_time = static_cast<double>(last_time)/1000.0;
  std::ostringstream ibc_143_str;
  ibc_143_str << ", ibc_143_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.ibc_143_measurement_time<<
                                            ", ibc_143_ts="<<time_span;
  // GetSignalValue(&CANSIG_IBC_143__IBC_APABrakSysLongictlSt_g,
  //                io_chassis_info_debug_tmp_.ibc_apa_braksyslongictlst);
  // GetSignalValue(&CANSIG_IBC_143__IBC_APABrakeModeSt_g,
  //                io_chassis_info_debug_tmp_.ibc_apa_brakemodest);
  GetSignalValue(&CANSIG_IBC_143__IBC_LSMCtrlFaultSt_g,
                 io_chassis_info_debug_tmp_.ibc_lsm_ctrl_fault_st);
  ibc_143_str << ", ibc_lsm_ctrl_fault_st=" << io_chassis_info_debug_tmp_.ibc_lsm_ctrl_fault_st;
  // GetSignalValue(&CANSIG_IBC_143__IBC_TargetGearReq_g, io_chassis_info_debug_tmp_.req_gear_fb);
  // GetSignalValue(&CANSIG_IBC_143__RollingCounter143_g, io_chassis_info_debug_tmp_.ibc_143_rc);
  io_chassis_info_debug_tmp_.ibc_143_valid = 1;

  // SF-12514：日志优化
  // debug_info_ += ibc_143_str.str();
}

void InhouseMessenger::ParseIBC12C(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.ibc_12c_measurement_time = static_cast<double>(last_time)/1000.0;
  std::ostringstream ibc_12c_str;
  ibc_12c_str << ", ibc_12c_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.ibc_12c_measurement_time<<
                                            ", ibc_12c_ts="<<time_span;
  GetSignalValue(&CANSIG_IBC_12C__IBC_EPBSt_g, io_chassis_info_debug_tmp_.ibc_epb_st);
  ibc_12c_str<<", ibc_epb_st="<<io_chassis_info_debug_tmp_.ibc_epb_st;
  GetSignalValue(&CANSIG_IBC_12C__IBC_SlopeSt_g, io_chassis_info_debug_tmp_.ibc_slope_st);
  ibc_12c_str<< ", ibc_slope_st="<<io_chassis_info_debug_tmp_.ibc_slope_st;
  // if (0x1 == io_chassis_info_debug_tmp_.ibc_slope_st) 
  {
    GetSignalValue(&CANSIG_IBC_12C__IBC_Slope_g, io_chassis_info_debug_tmp_.ibc_slope);
    ibc_12c_str<<", ibc_slope="<<io_chassis_info_debug_tmp_.ibc_slope;
  }
  GetSignalValue(&CANSIG_IBC_12C__IBC_SlopHighWarn_g,io_chassis_info_debug_tmp_.ibc_slope_high_warn);
  // ibc_12c_str<<", ibc_slope_high_warn="<<io_chassis_info_debug_tmp_.ibc_slope_high_warn;
  // GetSignalValue(&CANSIG_IBC_12C__RollingCounter12C_g, io_chassis_info_debug_tmp_.ibc_12c_rc);
  // ibc_12c_str<<", ibc_12c_rc="<<io_chassis_info_debug_tmp_.ibc_12c_rc;
  io_chassis_info_debug_tmp_.ibc_12c_valid = 1;

  debug_info_ += ibc_12c_str.str();
}

void InhouseMessenger::ParseIBC101(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.ibc_101_measurement_time = static_cast<double>(last_time)/1000.0;
  std::ostringstream ibc_101_str;
  ibc_101_str << ", ibc_101_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.ibc_101_measurement_time<<
                                            ", ibc_101_ts="<<time_span;

  GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedRLValid_g,io_chassis_info_debug_tmp_.ibc_whlspd_rl_vld);
  ibc_101_str<<", ibc_whlspd_rl_vld="<<io_chassis_info_debug_tmp_.ibc_whlspd_rl_vld;
  if (0x1 == io_chassis_info_debug_tmp_.ibc_whlspd_rl_vld) {
    GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedRL_g, io_chassis_info_debug_tmp_.ibc_whlspd_rl);
    ibc_101_str<<", ibc_whlspd_rl="<<io_chassis_info_debug_tmp_.ibc_whlspd_rl;
  }
  GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedRRValid_g,io_chassis_info_debug_tmp_.ibc_whlspd_rr_vld);
  if (0x1 == io_chassis_info_debug_tmp_.ibc_whlspd_rr_vld) {
    GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedRR_g, io_chassis_info_debug_tmp_.ibc_whlspd_rr);
  }
  GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedFLValid_g,
                 io_chassis_info_debug_tmp_.ibc_whlspd_fl_vld);
  if (0x1 == io_chassis_info_debug_tmp_.ibc_whlspd_fl_vld) {
    GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedFL_g, io_chassis_info_debug_tmp_.ibc_whlspd_fl);
  }
  GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedFRValid_g,
                 io_chassis_info_debug_tmp_.ibc_whlspd_fr_vld);
  if (0x1 == io_chassis_info_debug_tmp_.ibc_whlspd_fr_vld) {
    GetSignalValue(&CANSIG_IBC_101__IBC_wheelSpeedFR_g, io_chassis_info_debug_tmp_.ibc_whlspd_fr);
  }
  GetSignalValue(&CANSIG_IBC_101__RollingCounter101_g, io_chassis_info_debug_tmp_.ibc_101_rc);
  ibc_101_str<<", ibc_101_rc="<<io_chassis_info_debug_tmp_.ibc_101_rc;
  io_chassis_info_debug_tmp_.ibc_101_valid = 1;

  debug_info_ += ibc_101_str.str();
}

void InhouseMessenger::ParseIBCA2(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.ibc_a2_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream ibc_a2_str;
  ibc_a2_str << ", ibc_a2_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.ibc_a2_measurement_time<<
                                        ", ibc_a2_ts="<<time_span;

  GetSignalValue(&CANSIG_IBC_A2__IBC_VehicleDrivingDirection_g,
                 io_chassis_info_debug_tmp_.ibc_vehicle_driving_direction);
  ibc_a2_str<<", ibc_driving_dir="<<io_chassis_info_debug_tmp_.ibc_vehicle_driving_direction;
  GetSignalValue(&CANSIG_IBC_A2__IBC_VehicleSpeedValid_g,
                 io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid);
  // ibc_a2_str<<", ibc_vehicle_speed_valid="<<io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid;
  if (0x1 == io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid) {
    GetSignalValue(&CANSIG_IBC_A2__IBC_VehicleSpeed_g,
                   io_chassis_info_debug_tmp_.ibc_vehicle_speed);
    ibc_a2_str<<", ibc_speed="<<io_chassis_info_debug_tmp_.ibc_vehicle_speed;
    io_chassis_info_debug_tmp_.velocity = io_chassis_info_debug_tmp_.ibc_vehicle_speed / 3.6;
    if (0x2 == io_chassis_info_debug_tmp_.ibc_vehicle_driving_direction) {
      io_chassis_info_debug_tmp_.velocity *= -1;
    }
  }

  GetSignalValue(&CANSIG_IBC_A2__IBC_VehicleStandstillValid_g,
                 io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st_valid);
  if (0x1 == io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st_valid) {
    GetSignalValue(&CANSIG_IBC_A2__IBC_VehicleStandstillSt_g,
                   io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st);
    ibc_a2_str<<", ibc_standstill_st="<<io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st;
  }

  GetSignalValue(&CANSIG_IBC_A2__IBC_ESCoffSt_g, io_chassis_info_debug_tmp_.ibc_esc_off_st);
  GetSignalValue(&CANSIG_IBC_A2__IBC_ESCFaultSt_g, io_chassis_info_debug_tmp_.ibc_esc_fault_st);
  GetSignalValue(&CANSIG_IBC_A2__RollingCounter0A2_g, io_chassis_info_debug_tmp_.ibc_a2_rc);
  ibc_a2_str<<", ibc_a2_rc="<<io_chassis_info_debug_tmp_.ibc_a2_rc;
  io_chassis_info_debug_tmp_.ibc_a2_valid = 1;
  debug_info_ += ibc_a2_str.str();
}

void InhouseMessenger::ParseIBC172(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  IBC172WHLPLS stru_ibc_172_tmp;
  stru_ibc_172_tmp.ibc_172_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream ibc_172_str;
  ibc_172_str << ", ibc_172_mt="<<std::fixed<<std::setprecision(6)<<stru_ibc_172_tmp.ibc_172_measurement_time<<
                                          ", ibc_172_ts="<<time_span;

  GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeFLWSSValid_g,
                 stru_ibc_172_tmp.ibc_sum_edge_fl_wss_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_sum_edge_fl_wss_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeFLWSS_g,
                   stru_ibc_172_tmp.ibc_sum_edge_fl_wss);
  }
  GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeFRWSSValid_g,
                 stru_ibc_172_tmp.ibc_sum_edge_fr_wss_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_sum_edge_fr_wss_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeFRWSS_g,
                   stru_ibc_172_tmp.ibc_sum_edge_fr_wss);
  }
  GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeRLWSSValid_g,
                 stru_ibc_172_tmp.ibc_sum_edge_rl_wss_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_sum_edge_rl_wss_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeRLWSS_g,
                   stru_ibc_172_tmp.ibc_sum_edge_rl_wss);
    ibc_172_str<<", ibc_sum_edge_rl_wss="<<stru_ibc_172_tmp.ibc_sum_edge_rl_wss;
  }
  GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeRRWSSValid_g,
                 stru_ibc_172_tmp.ibc_sum_edge_rr_wss_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_sum_edge_rr_wss_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_SumOfEdgeRRWSS_g,
                   stru_ibc_172_tmp.ibc_sum_edge_rr_wss);
    ibc_172_str<<", ibc_sum_edge_rr_wss="<<stru_ibc_172_tmp.ibc_sum_edge_rr_wss;  
  }

  GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionFLValid_g,
                 stru_ibc_172_tmp.ibc_whl_diriection_fl_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_whl_diriection_fl_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionFLSt_g,
                   stru_ibc_172_tmp.ibc_whl_diriection_fl_st);
  }
  GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionFRValid_g,
                 stru_ibc_172_tmp.ibc_whl_diriection_fr_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_whl_diriection_fr_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionFRSt_g,
                   stru_ibc_172_tmp.ibc_whl_diriection_fr_st);
  }
  GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionRLValid_g,
                 stru_ibc_172_tmp.ibc_whl_diriection_rl_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_whl_diriection_rl_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionRLSt_g,
                   stru_ibc_172_tmp.ibc_whl_diriection_rl_st);
    // ibc_172_str<<", ibc_whl_diriection_rl_st="<<stru_ibc_172_tmp.ibc_whl_diriection_rl_st;
    io_chassis_info_debug_tmp_.ibc_whl_diriection_rl_st = stru_ibc_172_tmp.ibc_whl_diriection_rl_st;
  }
  GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionRRValid_g,
                 stru_ibc_172_tmp.ibc_whl_diriection_rr_vld);
  if (0x1 == stru_ibc_172_tmp.ibc_whl_diriection_rr_vld) {
    GetSignalValue(&CANSIG_IBC_172__IBC_WheelDirectionRRSt_g,
                   stru_ibc_172_tmp.ibc_whl_diriection_rr_st);
    // ibc_172_str<<", ibc_whl_diriection_rr_st="<<stru_ibc_172_tmp.ibc_whl_diriection_rr_st;
    io_chassis_info_debug_tmp_.ibc_whl_diriection_rr_st = stru_ibc_172_tmp.ibc_whl_diriection_rr_st;
  }
  GetSignalValue(&CANSIG_IBC_172__RollingCounter172_g, stru_ibc_172_tmp.ibc_172_rc);
  ibc_172_str<<", ibc_172_rc="<<stru_ibc_172_tmp.ibc_172_rc;

  io_chassis_info_debug_tmp_.ibc_172_pls_vec.emplace(stru_ibc_172_tmp);
  io_chassis_info_debug_tmp_.ibc_172_valid = 1;

  whlspd_record_start_ = 1;
  whlspd_record_num_ ++;
  debug_info_ += ibc_172_str.str();
}

void InhouseMessenger::ParseEPSB0(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.eps_b0_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream eps_b0_str;
  eps_b0_str << ", eps_b0_measurement_time="<<io_chassis_info_debug_tmp_.eps_b0_measurement_time<<
                                        ", eps_b0_time_span="<<time_span;
  GetSignalValue(&CANSIG_EPS_B0__EPS_APASt_g, io_chassis_info_debug_tmp_.eps_apa_status);
  GetSignalValue(&CANSIG_EPS_B0__EPS_APAFuncModeSt_g,
                 io_chassis_info_debug_tmp_.eps_apa_function_mode_status);
  GetSignalValue(&CANSIG_EPS_B0__EPS_APAProhibitedResaon_g,
                 io_chassis_info_debug_tmp_.eps_apa_prohibbited_reason);
  eps_b0_str<<", eps_apa_prohibbited_reason="<<io_chassis_info_debug_tmp_.eps_apa_prohibbited_reason;
  GetSignalValue(&CANSIG_EPS_B0__EPS_APARollingCountB0_g, io_chassis_info_debug_tmp_.eps_b0_rc);
  eps_b0_str<<", eps_b0_rc="<<io_chassis_info_debug_tmp_.eps_b0_rc;
  io_chassis_info_debug_tmp_.eps_b0_valid = 1;

  // SF-12514：日志优化
  // debug_info_ += eps_b0_str.str();
}

void InhouseMessenger::ParseEPSA5(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.sas_a5_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream eps_a5_str;
  eps_a5_str << ", sas_a5_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.sas_a5_measurement_time<<
                                        ", sas_a5_ts="<<time_span;
  GetSignalValue(&CANSIG_EPS_A5__TAS_SAS_SteeringAngle_g,
                 io_chassis_info_debug_tmp_.sas_steering_angle);
  eps_a5_str<<", sas_steering_angle="<<io_chassis_info_debug_tmp_.sas_steering_angle;
  GetSignalValue(&CANSIG_EPS_A5__TAS_SAS_SteeringRotSpd_g,
                 io_chassis_info_debug_tmp_.sas_steering_rotspd);
  // eps_a5_str<<", sas_steering_rotspd="<<io_chassis_info_debug_tmp_.sas_steering_rotspd;
  GetSignalValue(&CANSIG_EPS_A5__TAS_SAS_CalibratedSt_g,
                 io_chassis_info_debug_tmp_.sas_calibrated_st);
  // eps_a5_str<<", sas_calibrated_st="<<io_chassis_info_debug_tmp_.sas_calibrated_st;
  GetSignalValue(&CANSIG_EPS_A5__TAS_SAS_SASFailureSt_g,
                 io_chassis_info_debug_tmp_.sas_failure_st);
  // eps_a5_str<<", sas_failure_st="<<io_chassis_info_debug_tmp_.sas_failure_st;
  GetSignalValue(&CANSIG_EPS_A5__TAS_SAS_RollingCountA5_g, io_chassis_info_debug_tmp_.sas_a5_rc);
  eps_a5_str<<", sas_a5_rc="<<io_chassis_info_debug_tmp_.sas_a5_rc;
  io_chassis_info_debug_tmp_.sas_a5_valid = 1;

  debug_info_ += eps_a5_str.str();
}

void InhouseMessenger::ParseSGWC40(uint64_t time) {}

void InhouseMessenger::ParseSGWC710(uint64_t time) {
  SWGC710IMU swgc_710;
  swgc_710.sgwc_710_measurement_time = static_cast<double>(time)/1000.0;
  std::ostringstream swgc_710_str;
  swgc_710_str << ", sgwc_710_mt="<<std::fixed<<std::setprecision(6)<<swgc_710.sgwc_710_measurement_time;
  GetSignalValue(&CANSIG_SGW_C_710__HIL_RollRateSt_g, swgc_710.HIL_RollRateSt);
  GetSignalValue(&CANSIG_SGW_C_710__HIL_LongitudeAccelSt_g, swgc_710.HIL_LongitudeAccelSt);
  GetSignalValue(&CANSIG_SGW_C_710__HIL_PitchRateSt_g, swgc_710.HIL_PitchRateSt);
  GetSignalValue(&CANSIG_SGW_C_710__HIL_LateralAccelSt_g, swgc_710.HIL_LateralAccelSt);
  GetSignalValue(&CANSIG_SGW_C_710__HIL_YawRateSt_g, swgc_710.HIL_YawRateSt);
  GetSignalValue(&CANSIG_SGW_C_710__HIL_VerticalAccelSt_g, swgc_710.HIL_VerticalAccelSt);
  swgc_710_str << ", "<<swgc_710.HIL_RollRateSt<<
                    ", "<<swgc_710.HIL_PitchRateSt<<
                    ", "<<swgc_710.HIL_YawRateSt<<
                    ", "<<swgc_710.HIL_LongitudeAccelSt<<
                    ", "<<swgc_710.HIL_LateralAccelSt<<
                    ", "<<swgc_710.HIL_VerticalAccelSt;

  io_chassis_info_debug_tmp_.swgc_710_imu_vec.emplace(swgc_710);
  io_chassis_info_debug_tmp_.swgc_710_valid = 1;

  imu_record_start_ = 1;
  imu_record_num_++;
  debug_info_ += swgc_710_str.str();
}

void InhouseMessenger::ParseSGWC119(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  SWGC119IMU stru_swgc_119_tmp;
  stru_swgc_119_tmp.swgc_119_measurement_time = static_cast<double>(last_time)/1000.0;
  std::ostringstream swgc_119_str;
  swgc_119_str << ", swgc_119_mt="<<std::fixed<<std::setprecision(6)<<stru_swgc_119_tmp.swgc_119_measurement_time<<
                                        ", swgc_119_ts="<<time_span;
  GetSignalValue(&CANSIG_SGW_C_119__ACU_LongitdAcclerValid_g, 
                        stru_swgc_119_tmp.acu_longitdaccler_valid);
  if (0x0 == stru_swgc_119_tmp.acu_longitdaccler_valid) {
    GetSignalValue(&CANSIG_SGW_C_119__ACU_LongitudAccelerationSt_g, 
                        stru_swgc_119_tmp.acu_longitud_acceleration_st);
    swgc_119_str<<", acu_lon_a_st="<<stru_swgc_119_tmp.acu_longitud_acceleration_st;
    io_chassis_info_debug_tmp_.acu_longitud_acceleration_st = stru_swgc_119_tmp.acu_longitud_acceleration_st;
  }
  GetSignalValue(&CANSIG_SGW_C_119__ACU_LateralAccelareValid_g, 
                          stru_swgc_119_tmp.acu_lateral_accler_valid);
  if (0x0 == stru_swgc_119_tmp.acu_lateral_accler_valid) {
    GetSignalValue(&CANSIG_SGW_C_119__ACU_LateralAccelarationSt_g, 
                          stru_swgc_119_tmp.acu_lateral_accelaration_st);
    swgc_119_str<<", acu_lat_a_st="<<stru_swgc_119_tmp.acu_lateral_accelaration_st;
  }
  GetSignalValue(&CANSIG_SGW_C_119__ACU_YawrateValiditySt_g, 
                          stru_swgc_119_tmp.acu_yaw_rate_valid);
  if (0x0 == stru_swgc_119_tmp.acu_yaw_rate_valid) {
    GetSignalValue(&CANSIG_SGW_C_119__ACU_YawRateSt_g, 
                          stru_swgc_119_tmp.acu_yaw_rate_st);
    swgc_119_str<<", acu_yaw_rate_st="<<stru_swgc_119_tmp.acu_yaw_rate_st;
  }
  GetSignalValue(&CANSIG_SGW_C_119__RollingCounter119_g, 
                          stru_swgc_119_tmp.swgc_119_rc);
  swgc_119_str<<", swgc_119_rc="<<stru_swgc_119_tmp.swgc_119_rc;

  io_chassis_info_debug_tmp_.swgc_119_imu_vec.emplace(stru_swgc_119_tmp);
  io_chassis_info_debug_tmp_.swgc_119_valid = 1;

  imu_record_start_ = 1;
  imu_record_num_++;
  debug_info_ += swgc_119_str.str();
}

void InhouseMessenger::ParseEPS17A(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.eps_17a_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream eps_17a_str;
  eps_17a_str << ", eps_17a_measurement_time="<<io_chassis_info_debug_tmp_.eps_17a_measurement_time<<
                                        ", eps_17a_time_span="<<time_span;
  GetSignalValue(&CANSIG_EPS_17A__EPS_SteeringAngleFlag_g,
                 io_chassis_info_debug_tmp_.eps_steering_angle_flag);
  if (0x1 == io_chassis_info_debug_tmp_.eps_steering_angle_flag) {
    GetSignalValue(&CANSIG_EPS_17A__EPS_SteeringAngle_g,
                   io_chassis_info_debug_tmp_.eps_steering_angle);
  }
  GetSignalValue(&CANSIG_EPS_17A__EPS_SteeringTorqueSensorSt_g,
                 io_chassis_info_debug_tmp_.eps_steering_torque_sensor_st);
  eps_17a_str<<", eps_steering_torque_sensor_st="<<io_chassis_info_debug_tmp_.eps_steering_torque_sensor_st;
  if (0x0 == io_chassis_info_debug_tmp_.eps_steering_torque_sensor_st) {
    GetSignalValue(&CANSIG_EPS_17A__EPS_SteeringTorqueValue_g,
                   io_chassis_info_debug_tmp_.eps_steering_torque_value);
    eps_17a_str<<", eps_steering_torque_value="<<io_chassis_info_debug_tmp_.eps_steering_torque_value;
  }
  GetSignalValue(&CANSIG_EPS_17A__EPS_RollingCount17A_g, io_chassis_info_debug_tmp_.eps_17a_rc);
  eps_17a_str<<", eps_17a_rc="<<io_chassis_info_debug_tmp_.eps_17a_rc;
  io_chassis_info_debug_tmp_.eps_17a_valid = 1;

  // SF-12514：日志优化
  // debug_info_ += eps_17a_str.str();
}

void InhouseMessenger::ParseIBC11D(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.ibc_11d_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream ibc_11d_str;
  ibc_11d_str << ", ibc_11d_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.ibc_11d_measurement_time<<
                                        ", ibc_11d_ts="<<time_span;
  GetSignalValue(&CANSIG_IBC_11D__IBC_PlungerPressure_g,
                 io_chassis_info_debug_tmp_.ibc_plunger_pressure);
  // ibc_11d_str<<", ibc_plunger_pressure="<<io_chassis_info_debug_tmp_.ibc_plunger_pressure;
  GetSignalValue(&CANSIG_IBC_11D__IBC_PedalTravelSensorSt_g,
                 io_chassis_info_debug_tmp_.ibc_pedal_travel_sensor_st);
  if(io_chassis_info_debug_tmp_.ibc_pedal_travel_sensor_st){
    GetSignalValue(&CANSIG_IBC_11D__IBC_PedalTravelSensor_g,
                 io_chassis_info_debug_tmp_.ibc_pedal_travel_sensor);
     ibc_11d_str<<", ibc_pedal_travel_sensor="<<io_chassis_info_debug_tmp_.ibc_pedal_travel_sensor;
  }
  
  io_chassis_info_debug_tmp_.ibc_11d_valid = 1;
  debug_info_ += ibc_11d_str.str();
}

void InhouseMessenger::ParsePDCU1C8(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.pdcu_1c8_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream pdcu_1c8_str;
  pdcu_1c8_str << ", pdcu_1c8_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.pdcu_1c8_measurement_time<<
                                          ", pdcu_1c8_ts="<<time_span;
  GetSignalValue(&CANSIG_PDCU_1C8__PDCU_ActualGear_g, io_chassis_info_debug_tmp_.cur_gear);
  pdcu_1c8_str<<", cur_gear="<<io_chassis_info_debug_tmp_.cur_gear;
  GetSignalValue(&CANSIG_PDCU_1C8__PDCU_AccelPedalSt_g, io_chassis_info_debug_tmp_.pdcu_accel_peadal_st);
  pdcu_1c8_str<<", pdcu_accel_peadal_st="<<io_chassis_info_debug_tmp_.pdcu_accel_peadal_st;
  GetSignalValue(&CANSIG_PDCU_1C8__PDCU_AccelPedalValid_g, io_chassis_info_debug_tmp_.pdcu_accel_peadal_valid);
  // pdcu_1c8_str<<", pdcu_accel_peadal_valid="<<io_chassis_info_debug_tmp_.pdcu_accel_peadal_valid;
  GetSignalValue(&CANSIG_PDCU_1C8__RollingCounter1C8_g, io_chassis_info_debug_tmp_.pdcu_1c8_rc);
  pdcu_1c8_str<<", pdcu_1c8_rc="<<io_chassis_info_debug_tmp_.pdcu_1c8_rc;
  io_chassis_info_debug_tmp_.pdcu_1c8_valid = 1;
  debug_info_ += pdcu_1c8_str.str();
}

void InhouseMessenger::ParsePDCUFF(uint64_t time) {
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.pdcu_ff_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream pdcu_ff_str;
  pdcu_ff_str << ", pdcu_ff_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.pdcu_ff_measurement_time<<
                                        ", pdcu_ff_ts="<<time_span;
  GetSignalValue(&CANSIG_PDCU_FF__PDCU_AxleTorqueSt_g,
                 io_chassis_info_debug_tmp_.pdcu_axle_torque_st);
  // if (0x0 == io_chassis_info_debug_tmp_.pdcu_axle_torque_st) 
  {
    GetSignalValue(&CANSIG_PDCU_FF__PDCU_RealizedPowertrainWhlTq_g,
                   io_chassis_info_debug_tmp_.pdcu_realized_powertrain_whltp);
    pdcu_ff_str<<", pdcu_realized_powertrain_whltp="<<io_chassis_info_debug_tmp_.pdcu_realized_powertrain_whltp;
  }
  GetSignalValue(&CANSIG_PDCU_FF__PDCU_ACCFunIhibitionReq_g, io_chassis_info_debug_tmp_.pdcu_acc_fun_ihibition_req);
  // pdcu_ff_str<<", pdcu_acc_fun_ihibition_req="<<io_chassis_info_debug_tmp_.pdcu_acc_fun_ihibition_req;
  GetSignalValue(&CANSIG_PDCU_FF__PDCU_DriveAsiSttACCSt_g, io_chassis_info_debug_tmp_.pdcu_drive_asisttacc_st);
  // pdcu_ff_str<<", pdcu_drive_asisttacc_st="<<io_chassis_info_debug_tmp_.pdcu_drive_asisttacc_st;
  GetSignalValue(&CANSIG_PDCU_FF__PDCU_ACCResponseSt_g, io_chassis_info_debug_tmp_.pdcu_acc_response_st);
  // pdcu_ff_str<<", pdcu_acc_response_st="<<io_chassis_info_debug_tmp_.pdcu_acc_response_st;
  GetSignalValue(&CANSIG_PDCU_FF__PDCU_ACCcontrolAvailableSt_g, io_chassis_info_debug_tmp_.pdcu_acc_control_available_st);
  // pdcu_ff_str<<", pdcu_acc_control_available_st="<<io_chassis_info_debug_tmp_.pdcu_acc_control_available_st;
  GetSignalValue(&CANSIG_PDCU_FF__PDCU_WhetherACCReqRealizedSt_g, io_chassis_info_debug_tmp_.pdcu_whether_acc_req_realized_st);
  // pdcu_ff_str<<", pdcu_whether_acc_req_realized_st="<<io_chassis_info_debug_tmp_.pdcu_whether_acc_req_realized_st;
  GetSignalValue(&CANSIG_PDCU_FF__RollingCounter0FF_g, io_chassis_info_debug_tmp_.pdcu_ff_rc);
  pdcu_ff_str<<", pdcu_ff_rc="<<io_chassis_info_debug_tmp_.pdcu_ff_rc;
  io_chassis_info_debug_tmp_.pdcu_ff_valid = 1;
  debug_info_ += pdcu_ff_str.str();
}

void InhouseMessenger::ParseSWGC23A(uint64_t time){
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;
  io_chassis_info_debug_tmp_.swgc_23a_measurement_time = static_cast<double>(last_time)/1000.0;

  std::ostringstream swgc_23a_str;
  swgc_23a_str << ", swgc_23a_measurement_time="<<io_chassis_info_debug_tmp_.swgc_23a_measurement_time<<
                                        ", swgc_23a_time_span="<<time_span;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_LFDoorSwSt_g,
                 io_chassis_info_debug_tmp_.viul_lfdoorswst);
  swgc_23a_str << ", viul_lfdoorswst=" << io_chassis_info_debug_tmp_.viul_lfdoorswst;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_RFDoorSwSt_g,
                 io_chassis_info_debug_tmp_.viul_rfdoorswst);
  swgc_23a_str << ", viul_rfdoorswst=" << io_chassis_info_debug_tmp_.viul_rfdoorswst;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_RRDoorSwSt_g,
                 io_chassis_info_debug_tmp_.viul_rrdoorswst);
  swgc_23a_str << ", viul_rrdoorswst=" << io_chassis_info_debug_tmp_.viul_rrdoorswst;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_LRDoorSwSt_g,
                 io_chassis_info_debug_tmp_.viul_lrdoorswst);
  swgc_23a_str << ", viul_lrdoorswst=" << io_chassis_info_debug_tmp_.viul_lrdoorswst;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_LetfligthSt_g,
                 io_chassis_info_debug_tmp_.viul_left_light_st);
  swgc_23a_str << ", viul_left_light_st=" << 
                 io_chassis_info_debug_tmp_.viul_left_light_st;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_RightligthSt_g,
                 io_chassis_info_debug_tmp_.viul_right_light_st);
  swgc_23a_str << ", viul_right_light_st=" << 
                 io_chassis_info_debug_tmp_.viul_right_light_st;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_HighBeamSt_g,
                 io_chassis_info_debug_tmp_.viul_high_beam_st);
  swgc_23a_str << ", viul_high_beam_st=" << 
                 io_chassis_info_debug_tmp_.viul_high_beam_st;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_LowBeamSt_g,
                 io_chassis_info_debug_tmp_.viul_low_beam_st);
  swgc_23a_str << ", viul_low_beam_st=" << 
                 io_chassis_info_debug_tmp_.viul_low_beam_st;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_HazardLampSt_g,
                 io_chassis_info_debug_tmp_.viul_hazard_lamp_st);
  swgc_23a_str << ", viul_hazard_lamp_st=" << 
                 io_chassis_info_debug_tmp_.viul_hazard_lamp_st;
  GetSignalValue(&CANSIG_SGW_C_23A__VIUL_EngHoodUnlockWarn_g,
                 io_chassis_info_debug_tmp_.viul_enghood_unlock_warn);
  swgc_23a_str << ", viul_enghood_unlock_warn=" << 
                 io_chassis_info_debug_tmp_.viul_enghood_unlock_warn;
  io_chassis_info_debug_tmp_.swgc_23a_valid = 1;

  // SF-12514：日志优化
  // debug_info_ += swgc_23a_str.str();
}

  void InhouseMessenger::ParsePDCUFE(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    io_chassis_info_debug_tmp_.pdcu_fe_measurement_time = static_cast<double>(last_time)/1000.0;

    std::ostringstream pdcu_fe_str;
    pdcu_fe_str << ", pdcu_fe_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.pdcu_fe_measurement_time<<
                                          ", pdcu_fe_ts="<<time_span;
    GetSignalValue(&CANSIG_PDCU_FE__PDCU_DriveReadySt_g,
                  io_chassis_info_debug_tmp_.pdcu_drive_ready_st);
    pdcu_fe_str << ", pdcu_drive_ready_st=" << io_chassis_info_debug_tmp_.pdcu_drive_ready_st;
    io_chassis_info_debug_tmp_.pdcu_fe_valid = 1;
    debug_info_ += pdcu_fe_str.str();
  }

#define set_measurement_time(msg)       \
        io_chassis_info_debug_tmp_.msg_##msg.measurement_time = static_cast<double>(last_time)/1000.0

#define measurement_time_debug(msg)                                                                 \
        debug_str_##msg = ", "#msg"_measurement_time=" +                                            \
                          std::to_string(io_chassis_info_debug_tmp_.msg_##msg.measurement_time) +   \
                          ", "#msg"_time_span=" +                                                   \
                          std::to_string(time_span)

#define signal_parser(msg, sig)        \
        GetSignalValue(&CANSIG_##msg##__##sig##_g, io_chassis_info_debug_tmp_.msg_##msg.sig)

#define signal_debug(msg, sig)                                  \
        debug_str_##msg += ", "#msg"__"#sig"=" +                \
                           std::to_string(io_chassis_info_debug_tmp_.msg_##msg.sig)

  void InhouseMessenger::Parse_SGW_C_3F0(uint64_t time){
    static bool prev_DKM_BLEAPASwReq = 0;
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    std::string debug_str_SGW_C_3F0;

    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }

    last_time = time;

    set_measurement_time(SGW_C_3F0);
    measurement_time_debug(SGW_C_3F0);

    signal_parser(SGW_C_3F0, DKM_BLEConnectSt);
    signal_parser(SGW_C_3F0, DKM_BLEAPASwReq);
    signal_parser(SGW_C_3F0, DKM_BLEAPAModeSelectReq);
    signal_parser(SGW_C_3F0, DKM_BLEAPPRPAParkReq);
    signal_parser(SGW_C_3F0, DKM_BLEAPPAPAMoveReq);

    signal_debug(SGW_C_3F0, DKM_BLEConnectSt);
    signal_debug(SGW_C_3F0, DKM_BLEAPASwReq);
    signal_debug(SGW_C_3F0, DKM_BLEAPAModeSelectReq);
    signal_debug(SGW_C_3F0, DKM_BLEAPPRPAParkReq);
    signal_debug(SGW_C_3F0, DKM_BLEAPPAPAMoveReq);

    if (prev_DKM_BLEAPASwReq != io_chassis_info_debug_tmp_.msg_SGW_C_3F0.DKM_BLEAPASwReq)
    {
        if (1 == io_chassis_info_debug_tmp_.pdcu_drive_ready_st || 1 == prev_DKM_BLEAPASwReq)
        {
            PublishIpcMessage(APA_AVM_DKM_RPAREQ, unsigned(io_chassis_info_debug_tmp_.msg_SGW_C_3F0.DKM_BLEAPASwReq),
                              time);
            prev_DKM_BLEAPASwReq = io_chassis_info_debug_tmp_.msg_SGW_C_3F0.DKM_BLEAPASwReq;
            AINFO << "pub " << APA_AVM_DKM_RPAREQ <<
                     "=" << io_chassis_info_debug_tmp_.msg_SGW_C_3F0.DKM_BLEAPASwReq <<
                     "\n";
        }
        else
        {
            AINFO << "prev_DKM_BLEAPASwReq = " << prev_DKM_BLEAPASwReq <<
                     " DKM_BLEAPASwReq = " << io_chassis_info_debug_tmp_.msg_SGW_C_3F0.DKM_BLEAPASwReq <<
                     " PDCU_DriveReadySt = " << io_chassis_info_debug_tmp_.pdcu_drive_ready_st <<
                     "\n";
        }
    }

    io_chassis_info_debug_tmp_.msg_SGW_C_3F0_valid = 1;

    // SF-12514：日志优化
    // debug_info_ += debug_str_SGW_C_3F0;
  }

  void InhouseMessenger::Parse_SGW_C_346(uint64_t time){
    static uint32_t prev_VIUL_PEPS_RKECommand2 = 0;
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    std::string debug_str_SGW_C_346;

    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }

    last_time = time;

    set_measurement_time(SGW_C_346);
    measurement_time_debug(SGW_C_346);

    signal_parser(SGW_C_346, VIUL_PEPS_RKECommand2);
    signal_parser(SGW_C_346, VIUL_PEPS_EngineStartMode);

    signal_debug(SGW_C_346, VIUL_PEPS_RKECommand2);
    signal_debug(SGW_C_346, VIUL_PEPS_EngineStartMode);

    if (prev_VIUL_PEPS_RKECommand2 != io_chassis_info_debug_tmp_.msg_SGW_C_346.VIUL_PEPS_RKECommand2)
    {
        if (1 == io_chassis_info_debug_tmp_.pdcu_drive_ready_st || 1 == prev_VIUL_PEPS_RKECommand2)
        {
            PublishIpcMessage(APA_AVM_VIUL_RPAREQ, unsigned(io_chassis_info_debug_tmp_.msg_SGW_C_346.VIUL_PEPS_RKECommand2),
                              time);
            prev_VIUL_PEPS_RKECommand2 = io_chassis_info_debug_tmp_.msg_SGW_C_346.VIUL_PEPS_RKECommand2;
            AINFO << "pub " << APA_AVM_VIUL_RPAREQ << 
                     "=" << io_chassis_info_debug_tmp_.msg_SGW_C_346.VIUL_PEPS_RKECommand2 <<
                     "\n";
        }
        else
        {
            AINFO << "prev_VIUL_PEPS_RKECommand2 = " << prev_VIUL_PEPS_RKECommand2 <<
                     " VIUL_PEPS_RKECommand2 = " << io_chassis_info_debug_tmp_.msg_SGW_C_346.VIUL_PEPS_RKECommand2 <<
                     " PDCU_DriveReadySt = " << io_chassis_info_debug_tmp_.pdcu_drive_ready_st <<
                     "\n";
        }
    }

    io_chassis_info_debug_tmp_.msg_SGW_C_346_valid = 1;
    // SF-12514：日志优化
    // debug_info_ += debug_str_SGW_C_346;
  }

  void InhouseMessenger::Parse_IBC_133(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    std::string debug_str_IBC_133;

    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }

    last_time = time;

    set_measurement_time(IBC_133);
    measurement_time_debug(IBC_133);

    signal_parser(IBC_133, IBC_BrakePedalSt);
    signal_parser(IBC_133, IBC_BrkFricTotAtWhlsTorq);

    signal_debug(IBC_133, IBC_BrakePedalSt);
    signal_debug(IBC_133, IBC_BrkFricTotAtWhlsTorq);

    io_chassis_info_debug_tmp_.msg_IBC_133_valid = 1;
    debug_info_ += debug_str_IBC_133;
  }

  void InhouseMessenger::ParseSWGI30C(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    io_chassis_info_debug_tmp_.swgi_30c_measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream swgi_30c_str;
    swgi_30c_str << ", swgi_30c_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.swgi_30c_measurement_time<<
                                        ", swgi_30c_ts="<<time_span;

    GetSignalValue(&CANSIG_SGW_I_30C__ACU_DriverBeltSwSigSt_g,
                 io_chassis_info_debug_tmp_.acu_driverbelt_swsigst);
    swgi_30c_str << ", acu_driverbelt_swsigst=" << io_chassis_info_debug_tmp_.acu_driverbelt_swsigst;
    GetSignalValue(&CANSIG_SGW_I_30C__ACU_PassengerBeltSt_g,
                 io_chassis_info_debug_tmp_.acu_passenger_belt_st);
    // swgi_30c_str << ", acu_passenger_belt_st=" << io_chassis_info_debug_tmp_.acu_passenger_belt_st;
    GetSignalValue(&CANSIG_SGW_I_30C__ACU_RLBeltSt_g,
                 io_chassis_info_debug_tmp_.acu_rl_belt_st);
    // swgi_30c_str << ", acu_rl_belt_st=" << io_chassis_info_debug_tmp_.acu_rl_belt_st;
    GetSignalValue(&CANSIG_SGW_I_30C__ACU_RRBeltSt_g,
                 io_chassis_info_debug_tmp_.acu_rr_belt_st);
    // swgi_30c_str << ", acu_rr_belt_st=" << io_chassis_info_debug_tmp_.acu_rr_belt_st;
    io_chassis_info_debug_tmp_.swgi_30c_valid = 1;

    debug_info_ += swgi_30c_str.str();
  }

  void InhouseMessenger::ParseSWGI304(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    io_chassis_info_debug_tmp_.swgi_304_measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream swgi_304_str;
    swgi_304_str << ", swgi_304_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.swgi_304_measurement_time<<
                                        ", swgi_304_ts="<<time_span;

    GetSignalValue(&CANSIG_SGW_I_304__POT_BackDoorPosSt_g,
                 io_chassis_info_debug_tmp_.pot_backdoor_posst);
    swgi_304_str << ", pot_backdoor_posst=" << io_chassis_info_debug_tmp_.pot_backdoor_posst;
    io_chassis_info_debug_tmp_.swgi_304_valid = 1;
    // SF-12514：日志优化
    // debug_info_ += swgi_304_str.str();
  }

  void InhouseMessenger::ParseSWGI33D(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    io_chassis_info_debug_tmp_.swgi_33d_measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream swgi_33d_str;
    swgi_33d_str << ", swgi_33d_mt="<<std::fixed<<std::setprecision(6)<<io_chassis_info_debug_tmp_.swgi_33d_measurement_time<<
                                        ", swgi_33d_ts="<<time_span;

    GetSignalValue(&CANSIG_SGW_I_33D__VIUL_WashModeRearMirrorFbSt_g,
                 io_chassis_info_debug_tmp_.viul_rearvmirrorfb_st);
    swgi_33d_str << ", viul_rearvmirrorfb_st=" << io_chassis_info_debug_tmp_.viul_rearvmirrorfb_st;
    io_chassis_info_debug_tmp_.swgi_33d_valid = 1;
    debug_info_ += swgi_33d_str.str();
  }

// void InhouseMessenger::ParseAckIVI(uint64_t time, const raw_udp_packet_t& raw_packet)
void InhouseMessenger::ParseAckIVI(uint64_t time, raw_udp_packet_t raw_packet){
//do nothing
}

void InhouseMessenger::ParseAckException(uint64_t time, raw_udp_packet_t raw_packet){
  //这里只做记录，时间+index
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  ExceptionHandle::Instance()->removeCachedMsg(raw_packet.index);
  std::ostringstream ack_ex_str;
  ack_ex_str << ", ack_ex_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                        ", ack_ex_ts="<<time_span<<
                                        ", ack_ex_index="<<raw_packet.index;
                                        
  debug_info_raw_ += ack_ex_str.str();
}

void InhouseMessenger::ParsePubUSS(uint64_t time, raw_udp_packet_t raw_packet){
  //暂时只进行转发，后续根据需求增加处理逻辑
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream pub_uss_str;
  pub_uss_str << ", pub_uss_time="<<static_cast<double>(last_time)/1000.0<<
                                              ", pub_uss_time_span="<<time_span<<
                                              ", pub_uss_index="<<raw_packet.index<<
                                              ", pub_uss_len="<<raw_packet.len;
  
  spi_SendData_type* ptr = (spi_SendData_type*)(&raw_packet.data);
  io_chassis_info_debug_tmp_.uss_measurement_time = static_cast<double>(ptr->measurement_time)/1000.0;
  io_chassis_info_debug_tmp_.uss_data_info.ussData = ptr->ussData;
  memcpy(io_chassis_info_debug_tmp_.uss_data_info.ver,ptr->ver,USS_VERSION);

  std::string str((char*)(io_chassis_info_debug_tmp_.uss_data_info.ver), USS_VERSION << 1);
  pub_uss_str << 
                                ", pub_uss_measurement_time="<<io_chassis_info_debug_tmp_.uss_measurement_time<<
                                ", pub_uss_de_fls=" << ptr->ussData.USS_DataInfo.UssDistanceInfo.DE.FLS << 
                                ", pub_uss_FJam=" << ptr->ussData.USS_Jan.USS_FrontJam <<
                                ", pub_uss_RJam=" << ptr->ussData.USS_Jan.USS_RearJam <<
                                ", pub_uss_version=" << str;
  for(int i = 0; i<USS_Slot_SIZE; i++){
    pub_uss_str << 
                                  ", pub_uss_slot_"<<i<<"=" << 
                                  io_chassis_info_debug_tmp_.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].SlotID;
  }
  
  io_chassis_info_debug_tmp_.uss_valid = 1;

  // SF-12514：日志优化
  // debug_info_raw_ += pub_uss_str.str();
}

//心跳回复，1定时器、2独立线程、3回调直接回复，暂时是在10ms的定时器中回复的
void InhouseMessenger::ParsePubHeartBeat(uint64_t time, raw_udp_packet_t raw_packet){
  //这里需要进行记录以及立即答复ack，是否有必要校验控制指令和帧类型
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  uint64_t cur_time = GetTimeInMillis();

  std::ostringstream pub_hb_str;
  pub_hb_str << ", hb_ack_time="<<static_cast<double>(cur_time)/1000.0<<
                                              ", hb_measurement_time="<<static_cast<double>(last_time)/1000.0<<
                                              ", hb_time_span="<<time_span<<
                                              ", hb_index="<<raw_packet.index;
  PubAnsUartrpcMsg(raw_packet.index, raw_packet.ack_type);
  if(0 == io_module_mode_){
    AINFO <<__func__<<pub_hb_str.str();
  }
}

void InhouseMessenger::RecordPubHeartBeat(uint64_t time, raw_udp_packet_t raw_packet){
  //这里需要进行记录以及立即答复ack，是否有必要校验控制指令和帧类型
  if(1 == ConfigData::Instance()->use_heart_beat_check_){
    time_queue_.emplace(time);
    while(time_queue_.size() > time_queue_capacity_){
      time_queue_.pop();
    }
  }
  
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream pub_hb_str;
  pub_hb_str << ", pub_hb_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", pub_hb_ts="<<time_span<<
                                              ", pub_hb_index="<<raw_packet.index;
  debug_info_raw_ += pub_hb_str.str();                                            
}

void InhouseMessenger::ParsePubException(uint64_t time, raw_udp_packet_t raw_packet){
  //暂时先不做处理，后续根据功能需求添加，按照约定需要回复ack，这里立即答复ack
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  spi_up_exception_t_* ptr = (spi_up_exception_t_*)(&raw_packet.data);

  std::ostringstream pub_ex_str;
  pub_ex_str << ", pub_ex_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", pub_ex_ts="<<time_span<<
                                              ", pub_ex_index="<<raw_packet.index<<
                                              ", pub_ex_len="<<ptr->len;
  PubAnsUartrpcMsg(raw_packet.index, raw_packet.ack_type);
  int exception_size = (int)ptr->len;
  for(int i=0;i<exception_size;i++){
    if(1 == ConfigData::Instance()->use_exception_function_){
      ExceptionHandle::Instance()->mcu_exception_queue_->Enqueue((uint32_t)ptr->value[i]);
    }
    pub_ex_str << ", value_" << i << "=" << (uint32_t)ptr->value[i];
  }
  debug_info_raw_ += pub_ex_str.str();
  if(2 == io_module_mode_){
    AINFO<<__func__<< pub_ex_str.str();
  }
}

void InhouseMessenger::ParseAckShakeHand(uint64_t time, raw_udp_packet_t raw_packet){
  //暂时不做处理，进行解析存储以及发布，但是发布的时机是否需要调整
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream ack_apa_sh_str;
  ack_apa_sh_str << ", ack_apa_sh_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", ack_apa_sh_ts="<<time_span<<
                                              ", ack_apa_sh_index="<<raw_packet.index;
  
  spi_up_handshake_t_* ptr = (spi_up_handshake_t_*)(&raw_packet.data);
  io_chassis_info_debug_tmp_.eps_shakehand_sts = ptr->eps_state;//需要处理
  io_chassis_info_debug_tmp_.ibc_shakehand_sts = ptr->ibc_state;
  io_chassis_info_debug_tmp_.lat_fail_type = ptr->eps_error;
  io_chassis_info_debug_tmp_.lon_fail_type = ptr->ibc_error;
  ack_apa_sh_str <<", ack_apa_sh_eps_state="<<io_chassis_info_debug_tmp_.eps_shakehand_sts
                                                    <<", ack_apa_sh_ibc_state="<<io_chassis_info_debug_tmp_.ibc_shakehand_sts
                                                    <<", ack_apa_sh_eps_error="<<io_chassis_info_debug_tmp_.lat_fail_type
                                                    <<", ack_apa_sh_ibc_error="<<io_chassis_info_debug_tmp_.lon_fail_type;

  io_chassis_info_debug_tmp_.shakehand_valid = 1;
  debug_info_raw_ += ack_apa_sh_str.str();
}

void InhouseMessenger::ParseAckACCShakeHand(uint64_t time, raw_udp_packet_t raw_packet){
  //暂时不做处理，进行解析存储以及发布，但是发布的时机是否需要调整
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream ack_acc_sh_str;
  ack_acc_sh_str << ", ack_acc_sh_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", ack_acc_sh_ts="<<time_span<<
                                              ", ack_acc_sh_index="<<raw_packet.index;
  
  spi_up_handshake_t_* ptr = (spi_up_handshake_t_*)(&raw_packet.data);
  io_chassis_info_debug_tmp_.eps_shakehand_sts = ptr->eps_state;//需要处理
  io_chassis_info_debug_tmp_.ibc_shakehand_sts = ptr->ibc_state;
  io_chassis_info_debug_tmp_.lat_fail_type = ptr->eps_error;
  io_chassis_info_debug_tmp_.lon_fail_type = ptr->ibc_error;
  ack_acc_sh_str <<", ack_acc_sh_eps_state="<<io_chassis_info_debug_tmp_.eps_shakehand_sts
                                                    <<", ack_acc_sh_ibc_state="<<io_chassis_info_debug_tmp_.ibc_shakehand_sts
                                                    <<", ack_acc_sh_eps_error="<<io_chassis_info_debug_tmp_.lat_fail_type
                                                    <<", ack_acc_sh_ibc_error="<<io_chassis_info_debug_tmp_.lon_fail_type;

  io_chassis_info_debug_tmp_.shakehand_valid = 1;
  debug_info_raw_ += ack_acc_sh_str.str();
}

void InhouseMessenger::ParsePubMCUEvent(uint64_t time, raw_udp_packet_t raw_packet){
  //这里需要进行记录以及立即答复ack，是否有必要校验控制指令和帧类型
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream mcu_et_str ;
  mcu_et_str << ", mcu_et_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", mcu_et_ts="<<time_span<<
                                              ", mcu_et_index="<<raw_packet.index;

  spi_up_mcu_event_t_* ptr = (spi_up_mcu_event_t_*)(&raw_packet.data);
  io_chassis_info_debug_tmp_.eps_shakehand_sts = ptr->eps_state;//需要处理
  io_chassis_info_debug_tmp_.ibc_shakehand_sts = ptr->ibc_state;
  mcu_et_str <<", mcu_et_eps_state="<<io_chassis_info_debug_tmp_.eps_shakehand_sts
                                                    <<", mcu_et_ibc_state="<<io_chassis_info_debug_tmp_.ibc_shakehand_sts;

  PubAnsUartrpcMsg(raw_packet.index, raw_packet.ack_type);

  debug_info_raw_ += mcu_et_str.str();
}

void InhouseMessenger::ParseAckControl(uint64_t time, raw_udp_packet_t raw_packet){
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream ack_ctl_str;
  ack_ctl_str << ", ack_ctl_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", ack_ctl_ts="<<time_span<<
                                              ", ack_ctl_index="<<raw_packet.index;
  if(1 == ConfigData::Instance()->use_heart_beat_check_){
    time_queue_.emplace(time);
    while(time_queue_.size() > time_queue_capacity_){
      time_queue_.pop();
    }
  }

  spi_up_vehctrl_t_* ptr = (spi_up_vehctrl_t_*)(&raw_packet.data);
  io_chassis_info_debug_tmp_.trans_result = ptr->result;//需要处理
  ack_ctl_str<<", ack_ctl_trans_result="<<io_chassis_info_debug_tmp_.trans_result;
  io_chassis_info_debug_tmp_.sv_result = ptr->sv_result;//需要处理
  ack_ctl_str<<", ack_ctl_sv_result="<<io_chassis_info_debug_tmp_.sv_result;
  io_chassis_info_debug_tmp_.ack_distance = static_cast<double>(ptr->distance)/100.0;//需要处理
  ack_ctl_str<<", ack_ctl_distance="<<io_chassis_info_debug_tmp_.ack_distance;
  io_chassis_info_debug_tmp_.ack_velocity = static_cast<double>(ptr->velocity)/100.0/3.6;
  ack_ctl_str<<", ack_ctl_velocity="<<io_chassis_info_debug_tmp_.ack_velocity;
  debug_info_raw_ += ack_ctl_str.str();                                
}

void InhouseMessenger::ParseAckTrqControl(uint64_t time, raw_udp_packet_t raw_packet){
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream ack_trq_ctl_str;
  ack_trq_ctl_str << ", ack_ctl_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                              ", ack_ctl_ts="<<time_span<<
                                              ", ack_ctl_index="<<raw_packet.index;
  if(1 == ConfigData::Instance()->use_heart_beat_check_){
    time_queue_.emplace(time);
    while(time_queue_.size() > time_queue_capacity_){
      time_queue_.pop();
    }
  }

  spi_up_trq_vehctrl_t_* ptr = (spi_up_trq_vehctrl_t_*)(&raw_packet.data);
  io_chassis_info_debug_tmp_.tar_torque = ptr->tar_torque;//需要处理
  ack_trq_ctl_str<<", ack_ctl_tar_torque="<<io_chassis_info_debug_tmp_.tar_torque;
  io_chassis_info_debug_tmp_.tar_deceleration = ptr->tar_deceleration;//需要处理
  ack_trq_ctl_str<<", ack_ctl_tar_deceleration="<<io_chassis_info_debug_tmp_.tar_deceleration;
  io_chassis_info_debug_tmp_.angle = static_cast<double>(ptr->angle)/10.0;//需要处理
  ack_trq_ctl_str<<", ack_ctl_angle="<<io_chassis_info_debug_tmp_.angle;
  io_chassis_info_debug_tmp_.lamp_ctl = static_cast<double>(ptr->lamp_ctl);
  ack_trq_ctl_str<<", ack_ctl_lamp_ctl="<<io_chassis_info_debug_tmp_.lamp_ctl;
  debug_info_raw_ += ack_trq_ctl_str.str();                                
}

void InhouseMessenger::ParseImuMsg(uint64_t time, raw_udp_packet_t raw_packet){
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  SOCIMU stru_soc_imu_tmp;
  imu_data_t* ptr = (imu_data_t*)(&raw_packet.data);
  stru_soc_imu_tmp.imu_measurement_time = static_cast<double>(ptr->timestamp/1000.0);
  stru_soc_imu_tmp.linear_acceleration.x = ptr->acceleration_mg[0];
  stru_soc_imu_tmp.linear_acceleration.y = ptr->acceleration_mg[1];
  stru_soc_imu_tmp.linear_acceleration.z = ptr->acceleration_mg[2];
  stru_soc_imu_tmp.angular_velocity.x = ptr->angular_rate_mdps[0];
  stru_soc_imu_tmp.angular_velocity.y = ptr->angular_rate_mdps[1];
  stru_soc_imu_tmp.angular_velocity.z = ptr->angular_rate_mdps[2];

  std::ostringstream imu_msg_str;
  imu_msg_str << ", imu_msg_mt="<<std::fixed<<std::setprecision(6)<<
                                            static_cast<double>(stru_soc_imu_tmp.imu_measurement_time)<<
                                            ", imu_msg_ts="<<time_span<<
                                            ", imu_msg_index="<<raw_packet.index;
  imu_msg_str<<", imu_seq_id="<<ptr->seq_id
                                                            <<", imu_msg_l_x="<<stru_soc_imu_tmp.linear_acceleration.x
                                                            <<", imu_msg_l_y="<<stru_soc_imu_tmp.linear_acceleration.y
                                                            <<", imu_msg_a_z="<<stru_soc_imu_tmp.angular_velocity.z;
  
  io_chassis_info_debug_tmp_.soc_imu_vec.emplace(stru_soc_imu_tmp);
  io_chassis_info_debug_tmp_.imu_valid = 1;

  debug_info_raw_ += imu_msg_str.str();
}

void InhouseMessenger::ParseAckAPASt(uint64_t time, raw_udp_packet_t raw_packet){
  static uint64_t last_time = 0;
  static bool frist_record_time = true;
  uint64_t time_span = time - last_time;
  if(true == frist_record_time){
    frist_record_time = false;
    time_span = 0.0;
  }
  last_time = time;

  std::ostringstream apa_st_ack_msg_str;
  apa_st_ack_msg_str << ", apa_st_ack_mt="<<std::fixed<<std::setprecision(6)<<
                                            static_cast<double>(last_time)/1000.0<<
                                            ", apa_st_ack_ts="<<time_span<<
                                            ", apa_st_ack_index="<<raw_packet.index;

  apa_status_ack_t_ * ptr = (apa_status_ack_t_ *)(&raw_packet.data);
  if(0x01 == static_cast<uint8_t>(ptr->scenario)){
    io_chassis_info_debug_tmp_.scenario = ptr->scenario_value;
  }
  else if(0x02 == static_cast<uint8_t>(ptr->scenario)){
    io_chassis_info_debug_tmp_.apa_status = ptr->scenario_value;
  }

  if(0x01 == static_cast<uint8_t>(ptr->apa_status)){
    io_chassis_info_debug_tmp_.scenario = ptr->apa_status_value;
  }
  else if(0x02 == static_cast<uint8_t>(ptr->apa_status)){
    io_chassis_info_debug_tmp_.apa_status = ptr->apa_status_value;
  }

  apa_st_ack_msg_str<<" ,apa_st_ack_scenario="<<io_chassis_info_debug_tmp_.scenario<<
                                                " ,apa_st_ack_apa_status="<<io_chassis_info_debug_tmp_.apa_status;
  
  io_chassis_info_debug_tmp_.spa_st_valid = 1;
  debug_info_raw_ += apa_st_ack_msg_str.str();
}

template <typename T>
void InhouseMessenger::PublishIpcMessage(const std::string &topic, T value, long long time,
                     nlohmann::json extension) {
  nlohmann::json json = PayloadInfo(value, true, false, time, extension);
  std::string msg = json.dump();
  IpcMessage message = {(uint32_t)msg.length(), (uint8_t *)msg.data(), true};
  megaipc_instance_->publish(topic, message);
}

// 接收下行信号发送的ack消息
void InhouseMessenger::onMessageArrival(const std::string &topic, const IpcMessage &msg) {
  if (!b_inited_ || topic.empty() || !msg.data || msg.length == 0) {
    return;
  }

  if (topic == UARTRPC_APA_CONTROL_COMMAND_REQ) {
    if(msg.data[0] == 0x2A && msg.data[1] == 0xA0) {
      // 透传雷达状态信号
      uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, msg.data, msg.length);
    } else {
      if(0 == io_module_mode_){
        AINFO << "unknow apa/controlcommand msg:" << 
                          static_cast<uint32_t>(msg.data[0]) << " " <<
                          static_cast<uint32_t>(msg.data[1]) ;
      }
    }
  } 
}

void InhouseMessenger::onMessageArrival(const string &topic, const RequestMsg &req_msg, ResponseMsg **resp_msg)
{
    std::cout << "MegaIpcListenerTest request message arrival" << std::endl;
}

void InhouseMessenger::onMessageDelivered(const std::string &msgId) {
  // printf("onMessageDelivered: %s\n", msgId.data());
}

void InhouseMessenger::onConnectStateChanged(const std::string &nodeId, const ConnectState &state) {
  AINFO << "onConnectStateChanged: " << nodeId.data();
}

void InhouseMessenger::heartBeatCheck(){
  //非接管状态下，不进行心跳监控且清空队列
  if(0 == ConfigData::Instance()->use_heart_beat_check_)return;
  if(1 != io_chassis_info_debug_tmp_.takeover_stat){
    while(!time_queue_.empty()){
      time_queue_.pop();
  }}
  //若心跳时间队列小于等于1，则不进行判断
  if(time_queue_.empty() || time_queue_.size()<=1){
    return;
  }
  uint64_t last_time,cur_time;
  for(std::size_t i=0;i<time_queue_.size()-1;i++){
    last_time = time_queue_.front();
    time_queue_.pop();
    cur_time = time_queue_.front();
    if(std::fabs(cur_time - last_time) > overtime_span_warn_){
      //心跳超时
      if(0 == io_module_mode_){
        AINFO << __func__ << ": heart beat overtime warn, cur_time_warn="<<cur_time;
      }
    }
    if(std::fabs(cur_time - last_time) > overtime_span_error_){
      //心跳超时
      ExceptionHandle::Instance()->PubException(static_cast<ExceptionType>(0x07000001), "MCU_HEARTBEAT_EXCEPT");
      if(0 == io_module_mode_){
        AINFO << __func__ << ": heart beat overtime error, cur_time_error="<<cur_time;
      }
    }
  }
}

void InhouseMessenger::imuWhlSpdRateMonitor(){
  if(0 == ConfigData::Instance()->use_imu_whlspd_monitor_)return;
  static int seq_1000ms = 0;//100
  if(seq_1000ms >= 100){
    seq_1000ms = 0;
    if(1 == imu_record_start_ ){
      if(imu_record_num_<warnning_rate_){
        ExceptionHandle::Instance()->PubException(static_cast<ExceptionType>(0x07010002), "IMU_SIG_RATE_EXCEPT");
        if(0 == io_module_mode_){
          AINFO << __func__ <<"imu_rate lower warnning_rate_, imu_rate="<<imu_record_num_;
        }
      }
    }
    if(1 == whlspd_record_start_){
      if(whlspd_record_num_<warnning_rate_){
        ExceptionHandle::Instance()->PubException(static_cast<ExceptionType>(0x07010001), "WHL_SPEED_SIG_RATE_EXCEPT");
        if(0 == io_module_mode_){
          AINFO << __func__ <<"whlspd_rate lower warnning_rate_, whlspd_rate="<<whlspd_record_num_;
        }
      }
    }
    imu_record_num_ = 0;
    whlspd_record_num_ = 0;
  }
  seq_1000ms++;
}


void PubUartrpcMsg() {
  // todo: 构建其他
  uart_down_sig_t_ sig;
  sig.index = 0;
  sig.sig_type = 1;
  sig.len = 5;
  for(int i = 0; i < sig.len; i++) {
    sig.value[i] = i;
  }
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sig.len + 5);
}

void PubHandShakeReqUartrpcMsg(uint8_t eps, uint8_t ibc){
  static uint8_t index = 0;
  uart_down_handshake_t_ sig;
  sig.index = index;
  sig.eps = eps;
  sig.ibc = ibc;
  if(++index > 255){
    index = 0;
  }
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(uart_down_handshake_t_)/sizeof(uint8_t));
}

void PubACCHandShakeReqUartrpcMsg(uint8_t eps, uint8_t ibc){
  static uint8_t index = 0;
  uart_down_acc_handshake_t_ sig;
  sig.index = index;
  sig.eps = eps;
  sig.ibc = ibc;
  if(++index > 255){
    index = 0;
  }
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(uart_down_acc_handshake_t_)/sizeof(uint8_t));
}

template <uint32_t buf_len>
static inline void fillRpaRespBuf(uint32_t sig_val, uint8_t flag, uint32_t &offset, uint8_t (&buf)[buf_len])
{
  if (sig_val)
  {
    buf[offset] = flag;
    buf[offset + 1] = 1;
    if (0xFFFF != sig_val)
    {
        buf[offset + 2] = sig_val;
    }
    else
    {
        buf[offset + 2] = 0;
    }
    offset += 3;
  }
}

void PubAppRpaRespUartrpcMsg(StruInputAppRpaResp &rpa_resp){
  static uint8_t index = 0;
  uint8_t buff[MAX_VALUE_LEN] = {0x2A, 0xA0, 0x0};
  uint32_t offset = 3;

  buff[2] = index;
  fillRpaRespBuf(rpa_resp.rpa_app_disp_start_req, 0x08, offset, buff);
  fillRpaRespBuf(rpa_resp.mpa_ready_st, 0x09, offset, buff);
  fillRpaRespBuf(rpa_resp.parking_pode_st, 0x0A, offset, buff);
  fillRpaRespBuf(rpa_resp.remote_park_resp, 0x0B, offset, buff);
  fillRpaRespBuf(rpa_resp.exit_req, 0x0C, offset, buff);
  fillRpaRespBuf(rpa_resp.mpa_park_notice, 0x0D, offset, buff);

  if(++index > 255){
    index = 0;
  }
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, buff, offset);
}

void PubKeyRpaRespUartrpcMsg(StruInputKeyRpaResp &rpa_resp){
  static uint8_t index = 0;
  uint8_t buff[MAX_VALUE_LEN] = {0x2A, 0xA0, 0x0};
  uint32_t offset = 3;

  buff[2] = index;
  fillRpaRespBuf(rpa_resp.rpa_app_disp_start_req, 0x08, offset, buff);
  fillRpaRespBuf(rpa_resp.mpa_ready_st, 0x09, offset, buff);
  fillRpaRespBuf(rpa_resp.parking_pode_st, 0x0A, offset, buff);
  fillRpaRespBuf(rpa_resp.exit_req, 0x0C, offset, buff);

  if(++index > 255){
    index = 0;
  }
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, buff, offset);
}

void PubVehCtrlReqUartrpcMsg(uint8_t brake_mode, uint16_t distance, uint16_t velocity,
                                                            uint8_t angle_flag, int16_t angle, uint8_t gear_flag, uint8_t gear){
  static uint8_t index = 0;
  uart_down_vehctrl_t_ sig;
  sig.index = index;
  sig.brake_mode = brake_mode;
  sig.distance = distance;
  sig.velocity = velocity;
  sig.angle_flag = angle_flag;
  sig.angle = angle;
  sig.gear_flag = gear_flag;
  sig.gear = gear;
  if(++index > 255){
    index = 0;
  }
  uint8_t* sigtest = (uint8_t*)&sig;
  int size = sizeof(uart_down_vehctrl_t_)/sizeof(uint8_t);
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(uart_down_vehctrl_t_)/sizeof(uint8_t));
}

void PubTrqVehCtrlReqUartrpcMsg(StruInputLatAndLongTrqControlCmd& trq_control_cmd){
  static uint8_t index = 0;
  uart_down_trq_vehctrl_t_ sig;
  sig.index = index;
  sig.drive_flag = static_cast<uint8_t>(trq_control_cmd.is_driving);
  sig.brake_flag = static_cast<uint8_t>(trq_control_cmd.is_braking);
  sig.tar_torque = static_cast<uint32_t>(trq_control_cmd.tar_torque);
  sig.tar_deceleration = static_cast<int16_t>(trq_control_cmd.tar_deceleration);
  sig.angle_flag = static_cast<uint8_t>(trq_control_cmd.is_steering);
  sig.angle = static_cast<int16_t>(trq_control_cmd.tar_steer_angle);
  sig.gear_flag = static_cast<uint8_t>(trq_control_cmd.put_gear);
  sig.gear = static_cast<uint8_t>(trq_control_cmd.tar_gear);
  sig.tar_acc = static_cast<uint16_t>(trq_control_cmd.tar_acc);
  sig.lamp_ctl = static_cast<uint8_t>(trq_control_cmd.lamp_ctl);
  if(++index > 255){
    index = 0;
  }
  
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(uart_down_trq_vehctrl_t_)/sizeof(uint8_t));

  // uint8_t* sigtest = (uint8_t*)&sig;
  // int size = sizeof(uart_down_trq_vehctrl_t_)/sizeof(uint8_t);
  // std::cout << __func__ ;
  // for(int i = 0; i < size; i++){
  //   std::cout << ' '  << std::hex << static_cast<int32_t>(*(sigtest+i)) ;
  // }
  // std::cout << std::endl;
}

void PubExceptionUartrpcMsg(uint32_t* error, uint8_t len){
  static uint8_t index = 0;
  uart_down_exception_t_ sig;
  sig.index = index;
  sig.len = len;
  for(int i=0; i<len; i++){
    sig.value[i] = error[i];
  }
  if(++index > 255){
    index = 0;
  }
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, 4+len*4);
}

void PubExceptionUartrpcMsg(uart_down_exception_t_& sig, uint8_t len){
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, 4+len*4);
}

void PubAnsUartrpcMsg(uint8_t index, uint8_t sig_kind){
  uart_down_ans_t_ sig;
  sig.sig_kind = sig_kind;
  sig.index = index;
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(uart_down_ans_t_)/sizeof(uint8_t));
}

void PubAPAStatusMsg(apa_status_t_& sig){
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(apa_status_t_)/sizeof(uint8_t));
}

void PubAEBControllerUartrpcMsg(StruInputAEBController& aeb_cmd){
  static uint8_t index = 0;
  uart_down_aeb_t_ sig;
  sig.index = index;
  sig.brake_prefill_status = static_cast<uint8_t>(aeb_cmd.brake_prefill_status);
  sig.hba_level = static_cast<uint8_t>(aeb_cmd.hba_level);
  sig.eba_status = static_cast<uint8_t>(aeb_cmd.eba_status);
  sig.deceleration = static_cast<int16_t>(aeb_cmd.deceleration * 100.0);
  sig.ego_car_stop = static_cast<uint32_t>(aeb_cmd.ego_car_stop);
  if(++index > 255){
    index = 0;
  }
  
  uartrpc_send_msg(UARTRPC_DEV_CHN_VAL, (uint8_t*)&sig, sizeof(uart_down_aeb_t_)/sizeof(uint8_t));

}

}  // namespace donegfeng
}  // namespace io_server

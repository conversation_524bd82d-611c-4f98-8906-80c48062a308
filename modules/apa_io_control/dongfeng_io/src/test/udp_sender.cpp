#include <chrono>
#include <ctime>
#include <getopt.h>
#include <mutex>
#include <random>
#include <string>
#include <thread>

#include "string.h"
#include "udp_multicaster.h"

using namespace megaipc;
int sleep_ms_ = 10;

int32_t SendUdp() {
  uint8_t bus_id = 0, len;
  uint8_t data[64];
  int can_id, offset = 0, start_id = 1616;  //[650-654]

  static int error_count = 0;
  std::random_device dev;
  std::mt19937 rng(dev());
  std::uniform_int_distribution<std::mt19937::result_type> dist255(
      0, 255);  // distribution in range [0,255]
  int cnt = 0;
  while (true) {
    can_id = start_id + (offset++ % 5);
    len = can_id == 1620 ? 32 : 64;
    for (int i = 0; i < len; i++) {
      data[i] = dist255(rng);
    }

    std::string output;
    output.assign(reinterpret_cast<char *>(&bus_id),
                  reinterpret_cast<char *>(&bus_id) + sizeof(bus_id));
    output.append(reinterpret_cast<char *>(&can_id),
                  reinterpret_cast<char *>(&can_id) + sizeof(can_id));
    output.append(reinterpret_cast<char *>(&len), reinterpret_cast<char *>(&len) + sizeof(len));
    output.append(reinterpret_cast<char *>(data), len);
    int64_t timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::system_clock::now().time_since_epoch())
                            .count();
    output.append(reinterpret_cast<char *>(&timestamp),
                  reinterpret_cast<char *>(&timestamp) + sizeof(timestamp));

    int ret = UdpMulticaster::getInstance().send(output);
    if (ret < 0) {
      error_count++;
      if (error_count >= 100) {
        printf("UdpMulticaster::getInstance().send end. ret = %d\n", ret);
        error_count = 0;
      }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(sleep_ms_));
    if (++cnt % 100 == 0) {
      printf("udp package %d sent, can id:%d\n", cnt, can_id);
    }
  }
  return 0;
}

int main(int argc, char *argv[]) {
  printf("begin to send udp package...\n");
  int t_opt;
  int t_option_index = 0;
  static struct option t_long_options[] = {
      {"sleep", required_argument, 0, 's'}, {"help", no_argument, 0, 'h'}, {0, 0, 0, 0}};

  while ((t_opt = getopt_long(argc, argv, "s:h", t_long_options, &t_option_index)) !=
         -1) {
    switch (t_opt) {
      default:
      case '?':
      case 'h':
        // print_usage(argv[0]);
        return EXIT_FAILURE;
      case 's':
        sleep_ms_ = std::stoi(optarg);
        break;
    }
  }

  printf("sleep time in ms: %d\n", sleep_ms_);
  SendUdp();
  return 0;
}

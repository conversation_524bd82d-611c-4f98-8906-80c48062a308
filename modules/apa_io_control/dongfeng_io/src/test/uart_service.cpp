#include <stdio.h>
#include <string.h>

extern "C" {
#include "libuart_rpc.h"
}

#define CHN_IDX 0
#define SPIRPC2_DATA_MAX_LEN 1024
#define DEV_NAME_APA "dev/apa_uartrpc"

void printLogInfo(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data)
{
    char buff[1024] = {0};
    sprintf(buff, "sender%x:data len: %d, bus id: %x, can id: %x, data: ", can_id, len, bus_id, can_id);
    int _len = (int)strlen(buff);
    for (int i = 0; i < len; i++)
    {
        sprintf(buff + i*3 + _len, "%02x ", data[i]);
    }
    printf("%s\n", buff);
}


int32_t spirpc_msg_callback(uint8_t bus_id, uint32_t can_id, uint8_t len, uint8_t *data)
{
    if(len > 64){
        printf("can data len error: %u\n", len);
        return 0;
    }
    
    printLogInfo(bus_id, can_id, len, data);

    // std::string output;
    // output.assign(reinterpret_cast<char *>(&bus_id), reinterpret_cast<char *>(&bus_id) + sizeof(bus_id));
    // output.append(reinterpret_cast<char *>(&can_id), reinterpret_cast<char *>(&can_id) + sizeof(can_id));
    // output.append(reinterpret_cast<char *>(&len), reinterpret_cast<char *>(&len) + sizeof(len));
    // output.append(reinterpret_cast<char *>(data), len);
    // int64_t timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    // output.append(reinterpret_cast<char *>(&timestamp), reinterpret_cast<char *>(&timestamp) + sizeof(timestamp));

    // int ret = UdpMulticaster::getInstance().send(output);
    // static int error_count = 0;
    // if (ret < 0){
    //     error_count++;
    //     if(error_count >= 10000)
    //     {
    //          IC_LOG_ERROR("UdpMulticaster::getInstance().send end. ret = %d\n", ret);
    //          error_count = 0;
    //     }
    // }     
    // return ret;  
    return 0;
}



void uart_init()
{
    uartrpc_init((char*)DEV_NAME_APA,CHN_IDX,spirpc_msg_callback);
    printf("uartrpc inited...\n");
}

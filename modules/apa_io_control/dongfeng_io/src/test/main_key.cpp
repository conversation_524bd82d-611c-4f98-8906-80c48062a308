#include <sys/time.h>
#include "json_parser.hpp"
#include "common_struct.h"
#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"
#include "thirdparty/recommend_protocols/apa_io_control/proto/io_proto.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/ultrasonic.pb.h"
#include <stdio.h>
#include <iostream>
#include "cyber/cyber.h"
#include "cyber/node/writer.h"
#include "cyber/timer/timer.h"
#include "base/util/config_parser.hpp"
#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"
#include "exception_type.h"
#include "thirdparty/recommend_protocols/common/proto/exception.pb.h"

using namespace io_server;
using apollo::cyber::Node;
using apollo::cyber::Writer;

using rainbowdash::control_by_wire::LatAndLongControlCmd;
using rainbowdash::control_by_wire::LatAndLongShakeHandCmd;
using rainbowdash::common::CommonGearPosition;
using rainbowdash::control_by_wire::SVControlData;
using rainbowdash::control_by_wire::TorqueControlData;

using rainbowdash::common::ExceptionPtr;

using namespace exception;

StruInputLatAndLongControlCmd control_cmd_;
StruInputLatAndLongTrqControlCmd trq_control_cmd_;
uint32_t control_mode_ = 0;
std::atomic<uint32_t> io_module_pub_mode_{1};//0:apa 1:l2 

std::string apa_str = "************************************************************\n\
* q:    quit code\n\
* z:     break_mode = 1\n\
* x:     break_mode = 2\n\
* c:     break_mode = 3\n\
* a:    shake hand all\n\
* g:    shake hand lat\n\
* k:    shake hand lon\n\
* h:    shake hand all quit\n\
* b:    eps control 10.0deg\n\
* n:    gear N\n\
* d:    gear D\n\
* r:     gear R\n\
* p:    gear P\n\
* y:    steering angle control mode, input steering angle to contorl steering angle\n\
* t:    SV control mode, input tar_distance and  tar_velocity to contorl vehicle\n\
* v:   control mode: 0:single control 1:mutil control\n\
* m:  pub exception\n\
************************************************************";

std::string l2_str = "************************************************************\n\
* q:    quit code\n\
* z:     tar_deceleration = -1.0\n\
* x:     tar_deceleration = -0.5\n\
* a:    shake hand all\n\
* g:    shake hand lat\n\
* k:    shake hand lon\n\
* h:    shake hand all quit\n\
* n:    gear N\n\
* d:    gear D\n\
* r:     gear R\n\
* p:    gear P\n\
* y:    steering angle control mode, input steering angle to contorl steering angle\n\
* t:    torque control mode, input tar_torque to contorl vehicle\n\
* u:    deceleration control mode, input tar_deceleration to contorl vehicle\n\
* v:   control mode: 0:single control 1:mutil control\n\
* m:  pub exception\n\
* l:  change apa acc control mode: 0:apa 1:acc\n\
************************************************************";

std::string node_cfg = "conf/io_server.json";

std::shared_ptr< Writer<LatAndLongShakeHandCmd> > pWriterLatAndLongShakeHandCmd = nullptr;
std::shared_ptr< Writer<LatAndLongControlCmd> > pWriterLatAndLongControlCmd = nullptr;
std::shared_ptr<Writer<ExceptionPtr>> pExceptionCaptureInfo = nullptr;
std::shared_ptr< Writer<LatAndLongShakeHandCmd> > pWriterLatAndLongACCShakeHandCmd = nullptr;
std::shared_ptr< Writer<LatAndLongControlCmd> > pWriterLatAndLongACCControlCmd = nullptr;

bool clearStruInputLatAndLongControlCmd(){
    control_cmd_.is_update = 0;
    control_cmd_.tar_velocity = 0;
    control_cmd_.tar_distance = 0;
    control_cmd_.apa_failure_brake_mode = 0;
    control_cmd_.is_steering = CommonBool::FALSE;
    control_cmd_.tar_steer_angle = 0;
    control_cmd_.put_gear = CommonBool::FALSE;
    control_cmd_.tar_gear = 0;

    trq_control_cmd_.is_update = 0;
    trq_control_cmd_.is_driving = CommonBool::FALSE;
    trq_control_cmd_.is_braking = CommonBool::FALSE;
    trq_control_cmd_.tar_torque = 0.0;
    trq_control_cmd_.tar_deceleration = 0.0;
    trq_control_cmd_.is_steering = CommonBool::FALSE;
    trq_control_cmd_.tar_steer_angle = 0.0;
    trq_control_cmd_.put_gear = CommonBool::FALSE;
     trq_control_cmd_.tar_gear = 0;
    trq_control_cmd_.tar_acc = 0.0;
    trq_control_cmd_.lamp_ctl = 0;
    return true;
}

double getTimeS(struct timeval * pt)
{
    double N = 1000.0;
    struct timeval tv;
    if(pt==nullptr)
        gettimeofday(&tv, NULL); // get current time
    else
        tv = *pt;
    double milliseconds = tv.tv_sec * N + tv.tv_usec / N; // calculate milliseconds
    return milliseconds / 1000;
}

void pubShakehand(int lat_shakehnd, int lon_shakehand){
    LatAndLongShakeHandCmd cmd;
    static int64_t seq=0;
    CommonBool::Enum enum_tmp = CommonBool::NONE;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);
    if(0 == lat_shakehnd){enum_tmp = CommonBool::NONE;}
    else if(1 == lat_shakehnd){enum_tmp = CommonBool::TRUE;}
    else if(2 == lat_shakehnd){enum_tmp = CommonBool::FALSE;}
    cmd.set_activate_lat(enum_tmp);
    if(0 == lon_shakehand){enum_tmp = CommonBool::NONE;}
    else if(1 == lon_shakehand){enum_tmp = CommonBool::TRUE;}
    else if(2 == lon_shakehand){enum_tmp = CommonBool::FALSE;}
    cmd.set_activate_long(enum_tmp);
    if(0 == io_module_pub_mode_){
        pWriterLatAndLongShakeHandCmd->Write(cmd);
    }
    else if(1 == io_module_pub_mode_){
        pWriterLatAndLongACCShakeHandCmd->Write(cmd);
    }
}

void pubSVControlData(uint32_t brake_mode, double tar_distance, double tar_velocity,
                                       bool  is_steering, double tar_steer_angle, bool put_gear, int tar_gear){
    LatAndLongControlCmd cmd;
    static int64_t seq=0;
    CommonBool::Enum enum_tmp = CommonBool::NONE;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);

    SVControlData data_cmd;
    data_cmd.set_apa_failure_brake_mode(brake_mode);
    data_cmd.set_tar_distance(tar_distance);
    data_cmd.set_tar_velocity(tar_velocity);
    enum_tmp = (false == is_steering) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_steering(enum_tmp);
    data_cmd.set_tar_steer_angle(tar_steer_angle);
    enum_tmp = (false == put_gear) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_put_gear(enum_tmp);

    CommonGearPosition::Enum gear_tmp = CommonGearPosition::NONE;
    if(0 == tar_gear){gear_tmp = CommonGearPosition::NONE;}
    else if(1 == tar_gear){gear_tmp = CommonGearPosition::NEUTRAL;}
    else if(2 == tar_gear){gear_tmp = CommonGearPosition::DRIVING;}
    else if(3 == tar_gear){gear_tmp = CommonGearPosition::REVERSE;}
    else if(4 == tar_gear){gear_tmp = CommonGearPosition::PARKING;}
    data_cmd.set_tar_gear(gear_tmp);
    
    cmd.mutable_data()->PackFrom(data_cmd);
    pWriterLatAndLongControlCmd->Write(cmd);
}

void pubTorqueControlData(bool is_driving, double tar_torque, double tar_acc,
                                                            bool is_braking, double tar_deceleration,
                                                            bool  is_steering, double tar_steer_angle, 
                                                            bool put_gear, int tar_gear,
                                                            uint32_t lamp_ctl){
    LatAndLongControlCmd cmd;
    static int64_t seq=0;
    CommonBool::Enum enum_tmp = CommonBool::NONE;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);

    TorqueControlData data_cmd;
    enum_tmp = (false == is_driving) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_driving(enum_tmp);
    data_cmd.set_tar_torque(tar_torque);
    data_cmd.set_tar_acc(tar_acc);
    enum_tmp = (false == is_braking) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_braking(enum_tmp);
    data_cmd.set_tar_deceleration(tar_deceleration);
    enum_tmp = (false == is_steering) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_steering(enum_tmp);
    data_cmd.set_tar_steer_angle(tar_steer_angle);
    enum_tmp = (false == put_gear) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_put_gear(enum_tmp);

    CommonGearPosition::Enum gear_tmp = CommonGearPosition::NONE;
    if(0 == tar_gear){gear_tmp = CommonGearPosition::NONE;}
    else if(1 == tar_gear){gear_tmp = CommonGearPosition::NEUTRAL;}
    else if(2 == tar_gear){gear_tmp = CommonGearPosition::DRIVING;}
    else if(3 == tar_gear){gear_tmp = CommonGearPosition::REVERSE;}
    else if(4 == tar_gear){gear_tmp = CommonGearPosition::PARKING;}
    data_cmd.set_tar_gear(gear_tmp);

    data_cmd.set_lamp_ctl(lamp_ctl);
    
    cmd.mutable_data()->PackFrom(data_cmd);
    pWriterLatAndLongACCControlCmd->Write(cmd);
}

void PubException(ExceptionType err_type, const std::string& name) {
    ExceptionPtr msg_ptr{};
    msg_ptr.set_code((uint64_t) err_type);
    msg_ptr.set_name(name);
    AINFO<<__func__ <<"exception name : "<<GET_EXCEPTION_NAME() <<GET_EXCEPTION_NAME(err_type) << "code : " << (int64_t)err_type;
    std::cerr <<"exception error! "<<name<<std::endl;
    pExceptionCaptureInfo->Write(msg_ptr);
}

bool ACCControlTest(char ch){
    if(ch == 'z'){//breaking -1.0m/ss
        pubTorqueControlData(0,0,0,
                                                        1,-1.0,
                                                        0,0,
                                                        0,0,
                                                        0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'x'){//breaking -0.5m/ss
        pubTorqueControlData(0,0,0,
                                                        1,-0.5,
                                                        0,0,
                                                        0,0,
                                                        0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'a'){//all takeover
        pubShakehand(1,1);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'g'){//lat takeover
        pubShakehand(1,0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'k'){//lon takeover
        pubShakehand(0,1);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'h'){//quit takeover
        pubShakehand(2,2);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'n'){//eps 0.0
        pubTorqueControlData(0,0,0,
                                                        0,0,
                                                        0,0,
                                                        1,1,
                                                        0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'd'){//gear D
        pubTorqueControlData(0,0,0,
                                                        0,0,
                                                        0,0,
                                                        1,2,
                                                        0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'r'){//gear R
        pubTorqueControlData(0,0,0,
                                                        0,0,
                                                        0,0,
                                                        1,3,
                                                        0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'p'){//gear P
        pubTorqueControlData(0,0,0,
                                                        0,0,
                                                        0,0,
                                                        1,4,
                                                        0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'y'){//steering angle control
        double steering_angle = 0; 
        static double steering_angle_pre = 0;
        if(0 == control_mode_){
            std::cout<<"[Prompt] Single control mode. Steering angle control mode, Please input steering angle(double). [999] for Quit this loop"<<std::endl;
        }
        else if(1 == control_mode_){
            std::cout<<"[Prompt]  Mutil control mode. Steering angle control mode, Please input steering angle(double). [999] for Quit this loop"<<std::endl;
        }
        std::cin>>steering_angle;
        if(abs(steering_angle_pre-steering_angle) >0.1 && abs(999-steering_angle) >0.01){
            if(0 == control_mode_){
                pubTorqueControlData(0,0,0,
                                                                0,0,
                                                                1,steering_angle,
                                                                0,0,
                                                                0);
            }
            else if(1 == control_mode_){
                trq_control_cmd_.is_steering = CommonBool::TRUE;
                trq_control_cmd_.tar_steer_angle = steering_angle;
                bool is_steering_tmp = trq_control_cmd_.is_steering == CommonBool::TRUE ? 1 : 0;
                bool is_driving_tmp = trq_control_cmd_.is_driving == CommonBool::TRUE ? 1 : 0;
                bool is_braking_tmp = trq_control_cmd_.is_braking == CommonBool::TRUE ? 1 : 0;
                pubTorqueControlData(is_steering_tmp,trq_control_cmd_.tar_torque,trq_control_cmd_.tar_acc,
                                                                is_braking_tmp,trq_control_cmd_.tar_deceleration,
                                                                is_steering_tmp,trq_control_cmd_.tar_steer_angle,
                                                                0,trq_control_cmd_.tar_gear,
                                                                trq_control_cmd_.lamp_ctl);
            }
        }
    }
    else if(ch == 't'){//torque angle control
        double tar_torque = 0; 
        if(0 == control_mode_){
            std::cout<<"[Prompt]  Single control mode. torque control mode, Please input torque(double N*m).[999] for Quit this loop "<<std::endl;
        }
        else if(1 == control_mode_){
            std::cout<<"[Prompt]  Mutil control mode. torque control mode, Please input torque(double N*m).[999] for Quit this loop "<<std::endl;
        }
        std::cout<<"Please input tar_torque:"<<std::endl;
        std::cin>>tar_torque;
        if(abs(999-tar_torque) >0.01){
            if(0 == control_mode_){
                pubTorqueControlData(1,tar_torque,0,
                                                            0,0,
                                                            0,0,
                                                            0,0,
                                                            0);
            }
            else if(1 == control_mode_){
                trq_control_cmd_.is_driving = CommonBool::TRUE;
                trq_control_cmd_.tar_torque = tar_torque;
                bool is_steering_tmp = trq_control_cmd_.is_steering == CommonBool::TRUE ? 1 : 0;
                bool is_driving_tmp = trq_control_cmd_.is_driving == CommonBool::TRUE ? 1 : 0;
                bool is_braking_tmp = trq_control_cmd_.is_braking == CommonBool::TRUE ? 1 : 0;
                pubTorqueControlData(is_steering_tmp,trq_control_cmd_.tar_torque,trq_control_cmd_.tar_acc,
                                                                is_braking_tmp,trq_control_cmd_.tar_deceleration,
                                                                is_steering_tmp,trq_control_cmd_.tar_steer_angle,
                                                                0,trq_control_cmd_.tar_gear,
                                                                trq_control_cmd_.lamp_ctl);
            }
        }
    }
    else if(ch == 'u'){//
        double tar_deceleration = 0; 
        if(0 == control_mode_){
            std::cout<<"[Prompt]  Single control mode. deceleration control mode, Please input deceleration(double m/s^2).[999] for Quit this loop "<<std::endl;
        }
        else if(1 == control_mode_){
            std::cout<<"[Prompt]  Mutil control mode. deceleration control mode, Please input deceleration(double m/s^2).[999] for Quit this loop "<<std::endl;
        }
        std::cout<<"Please input tar_deceleration:"<<std::endl;
        std::cin>>tar_deceleration;
        if(abs(999-tar_deceleration) >0.01){
            if(0 == control_mode_){
                pubTorqueControlData(0,0,0,
                                                            1,tar_deceleration,
                                                            0,0,
                                                            0,0,
                                                            0);
            }
            else if(1 == control_mode_){
                trq_control_cmd_.is_braking = CommonBool::TRUE;
                trq_control_cmd_.tar_deceleration = tar_deceleration;
                bool is_steering_tmp = trq_control_cmd_.is_steering == CommonBool::TRUE ? 1 : 0;
                bool is_driving_tmp = trq_control_cmd_.is_driving == CommonBool::TRUE ? 1 : 0;
                bool is_braking_tmp = trq_control_cmd_.is_braking == CommonBool::TRUE ? 1 : 0;
                pubTorqueControlData(is_steering_tmp,trq_control_cmd_.tar_torque,trq_control_cmd_.tar_acc,
                                                                is_braking_tmp,trq_control_cmd_.tar_deceleration,
                                                                is_steering_tmp,trq_control_cmd_.tar_steer_angle,
                                                                0,trq_control_cmd_.tar_gear,
                                                                trq_control_cmd_.lamp_ctl);
            }
        }
    }
    else{
        return false;
    }
    return true;
}

bool APAControlTest(char ch){
    if(ch == 'z'){//breaking -1.0m/ss
        pubSVControlData(1,0,0,0,0,0,0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'x'){//breaking -0.5m/ss
        pubSVControlData(2,0,0,0,0,0,0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'c'){//breaking -0.5m/ss
        pubSVControlData(3,0,0,0,0,0,0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'u'){
        // pubEnableUSS();
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'a'){//all takeover
        pubShakehand(1,1);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'g'){//lat takeover
        pubShakehand(1,0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'k'){//lon takeover
        pubShakehand(0,1);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'h'){//quit takeover
        pubShakehand(2,2);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'b'){//eps 10.0
        pubSVControlData(0,0,0,1,10,0,0);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'n'){//eps 0.0
        pubSVControlData(0,0,0,0,0,1,1);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'd'){//gear D
        pubSVControlData(0,0,0,0,0,1,2);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'r'){//gear R
        pubSVControlData(0,0,0,0,0,1,3);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'p'){//gear P
        pubSVControlData(0,0,0,0,0,1,4);
        std::cout <<ch<<std::endl;
    }
    else if(ch == 'y'){//steering angle control
        double steering_angle = 0; 
        static double steering_angle_pre = 0;
        if(0 == control_mode_){
            std::cout<<"[Prompt] Single control mode. Steering angle control mode, Please input steering angle(double). [99] for Quit this loop"<<std::endl;
        }
        else if(1 == control_mode_){
            std::cout<<"[Prompt]  Mutil control mode. Steering angle control mode, Please input steering angle(double). [99] for Quit this loop"<<std::endl;
        }
        std::cin>>steering_angle;
        if(abs(steering_angle_pre-steering_angle) >0.1 && abs(99-steering_angle) >0.01){
            if(0 == control_mode_){
                pubSVControlData(0,0,0,1,steering_angle,0,0);
            }
            else if(1 == control_mode_){
                control_cmd_.tar_steer_angle = steering_angle;
                pubSVControlData(0,control_cmd_.tar_distance,control_cmd_.tar_velocity,1,control_cmd_.tar_steer_angle,0,0);
            }
        }
    }
    else if(ch == 't'){//torque angle control
        double tar_distance = 0, tar_velocity = 0; 
        static int torque_pre = 0;
        if(0 == control_mode_){
            std::cout<<"[Prompt]  Single control mode. SV control mode, Please input SV(double m,m/s).[99] for Quit this loop "<<std::endl;
        }
        else if(1 == control_mode_){
            std::cout<<"[Prompt]  Mutil control mode. SV control mode, Please input SV(double m,m/s).[99] for Quit this loop "<<std::endl;
        }
        std::cout<<"Please input tar_distance:"<<std::endl;
        std::cin>>tar_distance;
        if(abs(99-tar_distance) > 0.01){
            std::cout<<"Please input tar_velocity:"<<std::endl;
            std::cin>>tar_velocity;
            if(abs(99-tar_velocity) >0.01){
                if(0 == control_mode_){
                    pubSVControlData(0,tar_distance,tar_velocity,0,0,0,0);
                }
                else if(1 == control_mode_){
                    control_cmd_.tar_distance = tar_distance;
                    control_cmd_.tar_velocity = tar_velocity;
                    pubSVControlData(0,control_cmd_.tar_distance,control_cmd_.tar_velocity,1,control_cmd_.tar_steer_angle,0,0);
                }
            }
        }
    }
    else{
        return false;
    }
    return true;
}

int main(int argc, char *argv[]) {

    std::cout<<"Enter io_control_test_node accv1.0..."<<std::endl;

    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        std::cout<<"node_cfg prase failed!";
        return 0; 
    }

    if(0 == io_module_pub_mode_){
        std::cout<<apa_str<<std::endl;
    }
    else if(1 == io_module_pub_mode_){
        std::cout<<l2_str<<std::endl;
    }

    apollo::cyber::Init(argv[0]);
    std::shared_ptr <Node> node(apollo::cyber::CreateNode("io_control_test_node"));

    if(pWriterLatAndLongControlCmd == nullptr){
        auto io_in_chassisctl = nodeCfg.getSubChannel("io_in_chassisctl");
        pWriterLatAndLongControlCmd = node->CreateWriter<LatAndLongControlCmd>(io_in_chassisctl.name);
    }
    if(pWriterLatAndLongShakeHandCmd == nullptr){
        auto io_in_shake_hand_req = nodeCfg.getSubChannel("io_in_shake_hand_req");
        pWriterLatAndLongShakeHandCmd = node->CreateWriter<LatAndLongShakeHandCmd>(io_in_shake_hand_req.name);
    }
    if(pExceptionCaptureInfo == nullptr){
        pExceptionCaptureInfo = node->CreateWriter<ExceptionPtr>("sync/channel/exception");
    }
    if(pWriterLatAndLongACCControlCmd == nullptr){
        auto io_in_trq_chassisctl = nodeCfg.getSubChannel("io_in_trq_chassisctl");
        pWriterLatAndLongACCControlCmd = node->CreateWriter<LatAndLongControlCmd>(io_in_trq_chassisctl.name);
    }
    if(pWriterLatAndLongACCShakeHandCmd == nullptr){
        auto io_in_acc_shake_hand_req = nodeCfg.getSubChannel("io_in_acc_shake_hand_req");
        pWriterLatAndLongACCShakeHandCmd = node->CreateWriter<LatAndLongShakeHandCmd>(io_in_acc_shake_hand_req.name);
    }

    char ch;
    std::cout<<"[Prompt] continue test or not?  [q] for Quit: "<<std::endl;
    // scanf("%c",&ch);
    std::cin>>ch;
    // q z x u a g k h b n d r p y t
    while (ch != 'q')
    {
        /* code */
        bool control_flag = false;
        if(0 == io_module_pub_mode_){
            std::cout<<"apa control test..."<<std::endl;
            control_flag = APAControlTest(ch);
        }
        else if(1 == io_module_pub_mode_){
            std::cout<<"acc control test..."<<std::endl;
            control_flag = ACCControlTest(ch);
        }
        if(control_flag){
            //do noting
        }
        else if(ch == 'v'){//Stall no control
            int control_mode = 0; 
            static int control_mode_pre = 0;
            std::cout<<"[Prompt]  Change control mode, Please input num(int).[999] for Quit this loop"<<std::endl;
            std::cin>>control_mode;
            if(abs(control_mode_pre-control_mode) >0.01 && abs(999-control_mode) >0.01){
                control_mode_ = control_mode;
                std::cout<<"control_mode="<<control_mode_<<std::endl;
                clearStruInputLatAndLongControlCmd();
            }
        }
        else if(ch == 'm'){//
            PubException(static_cast<ExceptionType>(0x8000001), "MCU_HEARTBEAT_EXCEPT");
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'l'){//
            int io_module_pub_mode = 0; 
            static int io_module_pub_mode_pre = 0;
            std::cout<<"[Prompt]  Change apa acc control mode, Please input num(int).[999] for Quit this loop"<<std::endl;
            std::cin>>io_module_pub_mode;
            if(abs(io_module_pub_mode_pre-io_module_pub_mode) >0.01 && abs(999-io_module_pub_mode) >0.01){
                io_module_pub_mode_ = io_module_pub_mode;
                std::cout<<"io_module_pub_mode="<<io_module_pub_mode_<<std::endl;
                if(0 == io_module_pub_mode_){
                    std::cout<<apa_str<<std::endl;
                }
                else if(1 == io_module_pub_mode_){
                    std::cout<<l2_str<<std::endl;
                }
            }
        }
        else{
                std::cout <<"other char"<<std::endl;
        }

        std::cout<<"[Prompt] continue test or not?  [q] for Quit: "<<std::endl;
        std::cin>>ch;
        
    }
    std::cout<<"Quit io_control_test_node..."<<std::endl;

    apollo::cyber::WaitForShutdown();
    
    return 0;
}


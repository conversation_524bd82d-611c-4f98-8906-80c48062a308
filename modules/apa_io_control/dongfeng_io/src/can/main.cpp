
#include "ipc/megaipc_api.h"
#include "ipc/udp_multicast/udp_multicast_receiver.h"
#include <iostream>
#include <getopt.h>
#include "cyber/cyber.h"
#include "inhouse_messenger.h"
#include "base/util/config_parser.hpp"
#include "io_server.h"
using apollo::cyber::Rate;
using apollo::cyber::Time;
using apollo::cyber::Node;
using apollo::cyber::Writer;
using namespace megaipc;
using namespace io_server;

extern void uart_init();

int main(int argc, char *argv[]) {
  AINFO << "begin to init can service..." << std::endl;
  std::string config_file;
  int t_opt;
  int t_option_index = 0;
  static struct option t_long_options[] = {
      {"conf", required_argument, 0, 'c'}, {"help", no_argument, 0, 'h'}, {0, 0, 0, 0}};

  while ((t_opt = getopt_long(argc, argv, "c:d::phj::fa:", t_long_options, &t_option_index)) !=
         -1) {
    switch (t_opt) {
      default:
      case '?':
      case 'h':
        // print_usage(argv[0]);
        return EXIT_FAILURE;
      case 'c':
        config_file = optarg;
        printf("use config file: %s\n", config_file.c_str());
        break;
      case 0:
        printf("option %s", t_long_options[t_option_index].name);
        if (optarg) printf(" with arg %s", optarg);
        printf("\n");
        break;
    }
  }

  // uart_init();
  // printf("uartrpc2 inited...\n");
  
  MegaIpcApi::instance().init();
  printf("mega ipc initialized\n");
  ReceiverListenerInterface *udp_listener = new InhouseMessenger();
  UdpMulticastReceiver receiver(udp_listener);
  receiver.init();

  NodeCfg node_cfg(config_file);
  if (!node_cfg.isValid()) {
    return 1;
  }


  apollo::cyber::Init(argv[0]);
  std::shared_ptr<Node> node(apollo::cyber::CreateNode(node_cfg.getName()));

  printf("creating io_server...\n");
  auto io_server_ = std::make_shared<IOServer>(node, node_cfg, "conf/io_conf.json");
  printf("Init done. Enjoy.\n");

  apollo::cyber::WaitForShutdown();

  return 0;
}

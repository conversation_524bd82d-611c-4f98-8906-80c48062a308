#include "io_server.h"
#include "msg_ops.h"

namespace io_server{
namespace dongfeng{ 
    IOServerCA::IOServerCA(std::shared_ptr <Node> node, NodeCfg& nodecfg, string io_cfg_path)
    {
        AINFO << "mega-ipc init"; 
        auto config = ConfigData::Instance();
        config->initConfigData(io_cfg_path);

        auto exception = ExceptionHandle::Instance();
        exception->initExceptionHandleData();

        inhouse_messenger_ = InitInhouseMessenger();
        inhouse_messenger_->RegisterSpirpcCallback(); 

        cyber_messenger_ = std::make_shared<CyberMessenger>(node,nodecfg);

        //system_identification
        if(!systemIdentificationInit()){
            AINFO << "System Identification Init failed.";
        }
        
        AINFO << "Timer start";
#ifdef CYBER_TIMER_EN
        pTimerProcessTopic = std::make_shared<apollo::cyber::Timer>(10,std::bind(&IOServerCA::timerProcessTopicHandler,this),false, "io_process");
        pTimerProcessTopic->Start();
        pTimerHandleRadar = std::make_shared<apollo::cyber::Timer>(10,std::bind(&IOServerCA::timerRadarHandler,this),false, "io_radar");
        pTimerHandleRadar->Start();
#else
        pTimerProcessTopic = std::make_unique<TimerCommon::Timer>(10, std::bind(&IOServerCA::timerProcessTopicHandler,this), "io_process");
        pTimerProcessTopic->Start();
        pTimerHandleRadar = std::make_unique<TimerCommon::Timer>(10, std::bind(&IOServerCA::timerRadarHandler,this), "io_radar");
        pTimerHandleRadar->Start();
#endif

        b_inited_ = true;
    }

    IOServerCA::~IOServerCA()
    {
    }

    void IOServerCA::io_stop(){
        if(!b_inited_){
            AINFO <<__func__<< "io_server is not inited!";
            return;
        }
        ExceptionHandle::Instance()->PubException(static_cast<ExceptionType>(0x07000002), "RECEIVE_STOP_EVENT");
        ExceptionHandle::Instance()->PubException(static_cast<ExceptionType>(0x07000002), "RECEIVE_STOP_EVENT");
        ExceptionHandle::Instance()->PubException(static_cast<ExceptionType>(0x07000002), "RECEIVE_STOP_EVENT");
        //enter sleep mode
        enterStop();
    }

    void IOServerCA::io_start(){
        if(!b_inited_){
            AINFO << __func__<<"io_server is not inited!";
            return;
        }
        enterStart();
    }

    void IOServerCA::io_eco(){
        if(!b_inited_){
            AINFO << __func__<<"io_server is not inited!";
            return;
        }
        enterECO();
    }

    void IOServerCA::enterStop(){
        cyber_messenger_->enterStop();
        inhouse_messenger_->enterStop();
        ExceptionHandle::Instance()->enterStop();
        io_module_mode_ = 1;
        // pTimerProcessTopic->Stop();
        MsgOpsQuiet(true);
    }

    void IOServerCA::enterStart(){
        cyber_messenger_->enterStart();
        inhouse_messenger_->enterStart();
        ExceptionHandle::Instance()->enterStart();
        io_module_mode_ = 0;
        // pTimerProcessTopic->Start();
        MsgOpsQuiet(false);
    }

    void IOServerCA::enterECO(){
        cyber_messenger_->enterECO();
        inhouse_messenger_->enterECO();
        ExceptionHandle::Instance()->enterECO();
        io_module_mode_ = 2;
        // pTimerProcessTopic->Start();
        MsgOpsQuiet(true);
    }

    void IOServerCA::timerProcessTopicHandler(){
        //sub process
        // 50hz handle test
        if(1 == io_module_mode_){//stop mode
            return;
        }
        else if(2 == io_module_mode_){ //ECO mode
            static int32_t seq_pub = 0;
            if(++seq_pub >=2){
                seq_pub = 0;
                StruIOChassisInfoDebug io_chassis_info_debug_tmp;
                inhouse_messenger_->ParseAllRcvMsg();
                inhouse_messenger_->getDebugInfo(io_chassis_info_debug_tmp);
                cyber_messenger_->processSavedTopicData(io_chassis_info_debug_tmp);
                inhouse_messenger_->ResetDebugInfoValid();

                cyber_messenger_->twoDividedFrequencyPub();
                if(1 == ConfigData::Instance()->use_exception_function_){
                    ExceptionHandle::Instance()->ExceptionHandler();
                }
                cyber_messenger_->dataChangeToPub();
                long cur_time = GetTimeInMillis();
                recordIOChassisDebugInfo(io_chassis_info_debug_tmp,cur_time);
            }
        }
        else{//start mode
            StruIOChassisInfoDebug io_chassis_info_debug_tmp;
            inhouse_messenger_->ParseAllRcvMsg();
            inhouse_messenger_->getDebugInfo(io_chassis_info_debug_tmp);

            cyber_messenger_->processSavedTopicData(io_chassis_info_debug_tmp);
            if(use_system_identification_){
                std::tuple<double,double> res =  systemIdentification(io_chassis_info_debug_tmp);
                cyber_messenger_->savedSystemIdentification(std::get<0>(res), std::get<1>(res));
            }
            inhouse_messenger_->ResetDebugInfoValid();

            static int32_t seq = 0;
            long cur_time = GetTimeInMillis();
            static long start_time = cur_time;
            if(cur_time-start_time >= 1000){
                start_time = cur_time;
                // if(0 == io_module_mode_){
                    // AERROR<<__func__<<": seq="<<seq
                    //         <<", io_module_mode="<<io_module_mode_
                    //         <<", io_module_pub_mode="<<cyber_messenger_->io_module_pub_mode_;
                // }
                if(++seq>1000){
                    seq=0;
                }
            }
            
            //lat and lon info pub 
            static int32_t seq_pub = 0;
            if(++seq_pub >=2){
                seq_pub = 0;
                cyber_messenger_->twoDividedFrequencyPub();
                if(1 == ConfigData::Instance()->use_exception_function_){
                    ExceptionHandle::Instance()->ExceptionHandler();
                }
            }

            cyber_messenger_->dataChangeToPub();

            recordIOChassisDebugInfo(io_chassis_info_debug_tmp,cur_time);
        }
    }

    void IOServerCA::timerRadarHandler(){
        //pub process
        StrucNewRadarData io_radar_info_debug_tmp;
#if ENABLE_RADAR_CLOUD
        std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>> radar_map_data_tmp;
#endif
        inhouse_messenger_->ParseAllRadarCanRcvMsg();
#if ENABLE_RADAR_CLOUD
        inhouse_messenger_->GetRadarDebugInfo(io_radar_info_debug_tmp, radar_map_data_tmp);
        cyber_messenger_->processRadarSavedTopicData(io_radar_info_debug_tmp, radar_map_data_tmp);
#else
        inhouse_messenger_->GetRadarDebugInfo(io_radar_info_debug_tmp);
        cyber_messenger_->processRadarSavedTopicData(io_radar_info_debug_tmp);
#endif
        inhouse_messenger_->ResetRadarDebugInfoValid();

        static int32_t seq = 0;
        long cur_time = GetTimeInMillis();
        static long start_time = cur_time;
        if(cur_time-start_time >= 1000){
            start_time = cur_time;
            // if(0 == io_module_mode_){
                // AERROR<<__func__<<": seq="<<seq;
                //         <<", io_module_mode="<<io_module_mode_
                //         <<", io_module_pub_mode="<<cyber_messenger_->io_module_pub_mode_;
            // }
            if(++seq>1000){
                seq=0;
            }
        }

        static int32_t radar_seq_pub = 0;
        if(++radar_seq_pub >=10){
            radar_seq_pub = 0;
            cyber_messenger_->radarTargetPub();
        }
#if ENABLE_RADAR_CLOUD
        cyber_messenger_->radarCloudPub();
#endif
        recordIORadarDebugInfo(cur_time);
    }

    void IOServerCA::recordIORadarDebugInfo(const long cur_time){
        static long last_time = cur_time;
        static std::string record_str;
        std::string record_str_tmp;
        std::string record_target_str_tmp;
        std::string record_cloud_str_tmp;
        std::string record_cyber_str_tmp;

        inhouse_messenger_->getRadarTargetRecordInfoStr(record_target_str_tmp);
        inhouse_messenger_->getRadarCloudRecordInfoStr(record_cloud_str_tmp);
        cyber_messenger_->getRadarRecordCyberInfoStr(record_cyber_str_tmp);

        record_str = record_str +", d_cur_time="+std::to_string(cur_time) + record_target_str_tmp+record_cloud_str_tmp+record_cyber_str_tmp;
        static int32_t record_seq = 0;
        if(++record_seq >=2){
            record_seq = 0;
            if(!record_str.empty()){
                record_str = record_str
                        +", d_time_span="+std::to_string(cur_time-last_time)
                        +", io_module_mode="+std::to_string(io_module_mode_)
                        +", io_module_pub_mode="+std::to_string(cyber_messenger_->io_module_pub_mode_);
                if(0 == io_module_mode_){
                    AINFO<<__func__<<": "<<record_str;
                    // AINFO << calibrationDebugInfo(io_chassis_info_debug);
                }
                record_str.clear();
                last_time = cur_time;
            }
        }
    }

    void IOServerCA::recordIOChassisDebugInfo(const StruIOChassisInfoDebug& io_chassis_info_debug, const long cur_time){
        #ifdef USE_RECORD_CHANGE
        static long last_time = cur_time;
        static std::string record_str;
        std::string record_str_tmp;
        std::string record_raw_str_tmp;
        std::string record_cyber_str_tmp;

        inhouse_messenger_->getRecordInfoStr(record_str_tmp);
        inhouse_messenger_->getRecordRawInfoStr(record_raw_str_tmp);
        cyber_messenger_->getRecordCyberInfoStr(record_cyber_str_tmp);

        record_str = record_str +", d_cur_time="+std::to_string(cur_time) + record_str_tmp+record_raw_str_tmp+record_cyber_str_tmp;
        if(use_system_identification_){
            record_str = record_str + si_debug_;
            si_debug_.clear();
        }
        static int32_t record_seq = 0;
        if(++record_seq >=2){
            record_seq = 0;
            if(!record_str.empty()){
                record_str = record_str
                        +", d_takeover_stat="+std::to_string(io_chassis_info_debug.takeover_stat)
                        +", d_time_span="+std::to_string(cur_time-last_time)
                        +", io_module_mode="+std::to_string(io_module_mode_)
                        +", io_module_pub_mode="+std::to_string(cyber_messenger_->io_module_pub_mode_);
                if(0 == io_module_mode_){
                    AINFO<<__func__<<": "<<record_str;
                    // AINFO << calibrationDebugInfo(io_chassis_info_debug);
                }
                record_str.clear();
                last_time = cur_time;
            }
        }
        #endif
    }


    std::string IOServerCA::calibrationDebugInfo(const StruIOChassisInfoDebug& io_chassis_info_debug){
        std::string calibration_str = "calibrationDebugInfo, calibration_info: ";
        calibration_str = calibration_str + 
                                            ", velocity=" + std::to_string(io_chassis_info_debug.velocity) +
                                            ", sas_steering_angle=" + std::to_string(io_chassis_info_debug.sas_steering_angle) +
                                            ", ibc_slope=" + std::to_string(io_chassis_info_debug.ibc_slope) +
                                            ", ibc_plunger_pressure=" + std::to_string(io_chassis_info_debug.ibc_plunger_pressure) +
                                            ", pdcu_realized_powertrain_whltp=" + std::to_string(io_chassis_info_debug.pdcu_realized_powertrain_whltp) +
                                            ", IBC_BrkFricTotAtWhlsTorq=" + std::to_string(io_chassis_info_debug.msg_IBC_133.IBC_BrkFricTotAtWhlsTorq) +
                                            ", acu_longitud_acceleration_st=" + std::to_string(io_chassis_info_debug.acu_longitud_acceleration_st);
        return calibration_str;
    }

    bool IOServerCA::systemIdentificationInit(){
        //init conf
        //slope configure
        slope_mean_ws_ = ConfigData::Instance()->slope_mean_ws_; 
        slope_ws_ = ConfigData::Instance()->slope_ws_; 
        slope_debug_ = ConfigData::Instance()->slope_debug_ ? true : false; 
        //mass configure
        real_mass_ = ConfigData::Instance()->real_mass_; 
        mass_friction_ = ConfigData::Instance()->mass_friction_; 
        mass_a_ = ConfigData::Instance()->mass_a_; 
        mass_b_ = ConfigData::Instance()->mass_b_; 
        mass_c_ = ConfigData::Instance()->mass_c_; 
        mass_offset_ =ConfigData::Instance()->mass_offset_; 
        mass_min_speed_limited_ = ConfigData::Instance()->mass_min_speed_limited_; 
        mass_max_speed_limited_ = ConfigData::Instance()->mass_max_speed_limited_; 
        mass_acc_limited_ = ConfigData::Instance()->mass_acc_limited_; 
        mass_wheel_radius_ = ConfigData::Instance()->mass_wheel_radius_; 
        mass_slope_limited_ = ConfigData::Instance()->mass_slope_limited_; 
        mass_eps_limited_ = ConfigData::Instance()->mass_eps_limited_; 
        mass_debug_ = ConfigData::Instance()->mass_debug_ ? true : false; 
        use_system_identification_ = ConfigData::Instance()->use_system_identification_ ? true : false; 

        //init slope_estimation
        slope_estimation_ = std::make_shared<pnc::system_identification::SingleSensorSlopeEstimation>();
        slope_estimation_->init(slope_mean_ws_, slope_ws_);
        slope_estimation_->setDebug(slope_debug_);
        //init mass_estimation
        mass_estimation_ = std::make_shared<pnc::system_identification::RLSVehicleMassEstimation>();
        mass_estimation_->setFriction(mass_friction_);
        mass_estimation_->init(mass_min_speed_limited_, mass_max_speed_limited_, 
                                                        mass_estimation_->getFriction() ,mass_acc_limited_);
        mass_estimation_->setWheelRadius(mass_wheel_radius_);
        mass_estimation_->setDebug(mass_debug_);
        mass_estimation_->setParams(mass_a_, mass_b_, mass_c_, mass_offset_);

        return true;
    }

    std::tuple<double, double> IOServerCA::systemIdentification(StruIOChassisInfoDebug& io_chassis_info_debug){
        //system_identification 
        static double pre_slope_estimation = 0.0;
        double ct = GetTimeS(NULL);
        static double pt = 0;
        double dt = ct - pt;
        double slope_estimation = slope_estimation_->computeSlope(
            io_chassis_info_debug.acu_longitud_acceleration_st,
            io_chassis_info_debug.velocity, dt);  
        pt = ct;
        double slope_deg = slope_estimation*180.0/3.1415926;
        if(dt > 200) {dt = 0.0;}
        si_debug_ = ": si_speed="+std::to_string(io_chassis_info_debug.velocity)+
                                                ", si_acc="+std::to_string(io_chassis_info_debug.acu_longitud_acceleration_st)+
                                                ", si_dt="+std::to_string(dt*1000.0)+
                                                ", si_slope_nums="+std::to_string(slope_estimation_->getDataNumbers())+
                                                ", si_slope_deg="+std::to_string(slope_estimation*180.0/3.1415926)+
                                                ", si_slope_rad="+std::to_string(slope_estimation)+
                                                ", si_mean_dev_vx="+std::to_string(slope_estimation_->getMeanDevVx())+
                                                ", si_mean_ax="+std::to_string(slope_estimation_->getMeanAx());

        static double mass_estimation = 0.0;
        if(std::fabs(io_chassis_info_debug.sas_steering_angle) < mass_eps_limited_ && 
            std::fabs(io_chassis_info_debug.velocity) >= mass_speed_limited_ && 
            std::fabs(io_chassis_info_debug.pdcu_realized_powertrain_whltp) >= mass_torque_limited_ && 
            std::fabs(slope_deg) < mass_slope_limited_){
                mass_estimation = mass_estimation_->computeVehicleMass(
                    io_chassis_info_debug.velocity, 
                    io_chassis_info_debug.pdcu_realized_powertrain_whltp,
                    io_chassis_info_debug.acu_longitud_acceleration_st);
        }
        int mass_reset = 0;
        if(1 == io_chassis_info_debug.viul_lfdoorswst ||1 == io_chassis_info_debug.viul_rfdoorswst ||
            1 == io_chassis_info_debug.viul_lrdoorswst ||1 == io_chassis_info_debug.viul_rrdoorswst ||
            0 != io_chassis_info_debug.pot_backdoor_posst){
            mass_estimation_->restRLSVehicleMassEstimation();
            mass_reset = 1;
        }
        si_debug_=si_debug_+", si_mass_reset="+std::to_string(mass_reset)+
                                                ", si_steering_angle="+std::to_string(io_chassis_info_debug.sas_steering_angle)+
                                                ", si_msaa_nums="+std::to_string(mass_estimation_->getDataNumbers())+
                                                ", si_mass="+std::to_string(mass_estimation);
        return std::make_tuple(slope_estimation, mass_estimation);
    }
}
}

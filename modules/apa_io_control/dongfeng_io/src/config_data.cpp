#include "config_data.h"
#include <unordered_map>
#include <vector>

namespace io_server {
namespace dongfeng{

    bool ConfigData::initConfigData(std::string io_cfg_path){
        if(!praseConfig(io_cfg_path)){
            is_valid_ = false;
            return false;
        }
        is_valid_ = true;
        return true;
    }

    bool ConfigData::praseConfig(std::string io_cfg_path){
        std::shared_ptr<JsonParser> ioConfPtr = std::make_shared<JsonParser>(io_cfg_path);
        if (!ioConfPtr->isValid()) {
            AERROR<<__func__<<" E: Prase json file failed."<<std::endl;
            return false; 
        }
        auto io_conf = ioConfPtr->getJsonObject();
        auto dbc_init = io_conf["DBC_INIT"];

        auto pub_topic = io_conf["Pub_Topic"];
        pub_topic_160_ = pub_topic["topic_160"];
        pub_topic_4c1_ = pub_topic["topic_4c1"];
        pub_topic_35a_ = pub_topic["topic_35a"];
        pub_topic_ = pub_topic["pub_topic"];

        auto sub_topic = io_conf["Sub_Topic"];
        sub_topic_.emplace_back(sub_topic["sub_apa_topic"]);
        sub_topic_.emplace_back(sub_topic["sub_imu_topic"]);
        
        //init param
        auto param = io_conf["Param"];
        initConfigParam(param);

        //init IVI_160 base dbc
        initIVI160BaseDBC(dbc_init);

        //init IVI_4C1 base dbc
        initIVI4C1BaseDBC(dbc_init);

        //init IVI_35A base dbc
        initIVI35ABaseDBC(dbc_init);

        //init error_level
        error_level_ = io_conf["ErrorLevel"];

        return true;
    }
    
    void ConfigData::initConfigParam(nlohmann::json param){
        steering_rate_limit_ = param["steering_rate_limit"];
        ibc_pedal_travel_ = param["ibc_pedal_travel"];
        use_heart_beat_check_ = param["use_heart_beat_check"];
        use_exception_function_ = param["use_exception_function"];
        use_resend_function_ = param["use_resend_function"];
        use_imu_whlspd_monitor_ = param["use_imu_whlspd_monitor"];
        overtime_span_warn_ = param["overtime_span_warn"];
        overtime_span_error_ = param["overtime_span_error"];
        warnning_rate_ = param["warnning_rate"];
        filter_window_size_ = param["filter_window_size"];
        filter_pickup_index_ = param["filter_pickup_index"];
        uss_valid_distance_cm_ = param["uss_valid_distance_cm"];
        //eps takeover params
        eps_tk_torque_threshold_ = param["eps_tk_torque_threshold"];
        eps_tk_keep_time_ = param["eps_tk_keep_time"];
        //AIS-45502
        eco_pub_speed_threshold_ = param["eco_pub_speed_threshold"];
        radar_handle_type_ = param["radar_handle_type"];
        eco_no_pub_speed_threshold_ = param["eco_no_pub_speed_threshold"];
        //slope configure
        slope_mean_filter_ws_ = param["slope_mean_filter_ws"];
        slope_mean_ws_ = param["slope_mean_ws"];
        slope_ws_ = param["slope_ws"];
        slope_debug_ = param["slope_debug"];
        //mass configure
        real_mass_ = param["real_mass"];
        mass_friction_ = param["mass_friction"];
        mass_a_ = param["mass_a"];
        mass_b_ = param["mass_b"];
        mass_c_ = param["mass_c"];
        mass_offset_ = param["mass_offset"];
        mass_min_speed_limited_ = param["mass_min_speed_limited"];
        mass_max_speed_limited_ = param["mass_max_speed_limited"];
        mass_acc_limited_ = param["mass_acc_limited"];
        mass_wheel_radius_ = param["mass_wheel_radius"];
        mass_slope_limited_ = param["mass_slope_limited"];
        mass_eps_limited_ = param["mass_eps_limited"];
        mass_debug_ = param["mass_debug"];
        use_system_identification_ = param["use_system_identification"];
        AINFO << "ConfigData: eps_tk_torque_threshold="<<eps_tk_torque_threshold_
                            << ", eps_tk_keep_time="<<eps_tk_keep_time_
                            << ", eco_pub_speed_threshold="<<eco_pub_speed_threshold_
                            << ", eco_no_pub_speed_threshold="<<eco_no_pub_speed_threshold_
                            << ", radar_handle_type="<<radar_handle_type_
                            << ", slope_mean_filter_ws="<<slope_mean_filter_ws_
                            << ", slope_mean_ws="<<slope_mean_ws_
                            << ", slope_ws="<<slope_ws_
                            << ", slope_debug="<<slope_debug_
                            << ", real_mass="<<real_mass_
                            << ", mass_friction="<<mass_friction_
                            << ", mass_a="<<mass_a_
                            << ", mass_b="<<mass_b_
                            << ", mass_c="<<mass_c_
                            << ", mass_offset="<<mass_offset_
                            << ", mass_min_speed_limited="<<mass_min_speed_limited_
                            << ", mass_max_speed_limited="<<mass_max_speed_limited_
                            << ", mass_acc_limited="<<mass_acc_limited_
                            << ", mass_wheel_radius="<<mass_wheel_radius_
                            << ", mass_slope_limited="<<mass_slope_limited_
                            << ", mass_eps_limited="<<mass_eps_limited_
                            << ", mass_debug="<<mass_debug_
                            << ", use_system_identification="<<use_system_identification_;
    }

    void ConfigData::initIVI160BaseDBC(nlohmann::json init_data){
        ivi_160_init_.HAD_APA_LongitudinalCtrlReq = init_data["IVI_160"]["HAD_APA_LongitudinalCtrlReq"];
        ivi_160_init_.HAD_APA_SystemSt = init_data["IVI_160"]["HAD_APA_SystemSt"];
        ivi_160_init_.HAD_APA_ReqToStopDst = init_data["IVI_160"]["HAD_APA_ReqToStopDst"];
        ivi_160_init_.HAD_APA_ReqtTargetGearPosSt = init_data["IVI_160"]["HAD_APA_ReqtTargetGearPosSt"];
        ivi_160_init_.HAD_APA_FailureBrakeModeSt = init_data["IVI_160"]["HAD_APA_FailureBrakeModeSt"];
        ivi_160_init_.HAD_APA_BrakeTargetMaxSpeed = init_data["IVI_160"]["HAD_APA_BrakeTargetMaxSpeed"];
        ivi_160_init_.HAD_APA_EmergencySt = init_data["IVI_160"]["HAD_APA_EmergencySt"];
        ivi_160_init_.HAD_APA_Emergency_Valid = init_data["IVI_160"]["HAD_APA_Emergency_Valid"];
        ivi_160_init_.HAD_APA_ESC_Funmode = init_data["IVI_160"]["HAD_APA_ESC_Funmode"];
        ivi_160_init_.HAD_APA_ReqEPSTargetAngleReq = init_data["IVI_160"]["HAD_APA_ReqEPSTargetAngleReq"];
        // ivi_160_init_.RollingCounter160 = init_data["IVI_160"]["RollingCounter160"];
        ivi_160_init_.HAD_APA_ControlEPSReq = init_data["IVI_160"]["HAD_APA_ControlEPSReq"];
        ivi_160_init_.HAD_EPS_FuncModeReq = init_data["IVI_160"]["HAD_EPS_FuncModeReq"];
    }

    void ConfigData::initIVI4C1BaseDBC(nlohmann::json init_data){
    }

    void ConfigData::initIVI35ABaseDBC(nlohmann::json init_data){
        ivi_35a_init_.HAD_APA_AutomaticParkingPodeSt = init_data["IVI_35A"]["HAD_APA_AutomaticParkingPodeSt"];
        ivi_35a_init_.HAD_APA_TurnLightsCommandReq = init_data["IVI_35A"]["HAD_APA_TurnLightsCommandReq"];
        ivi_35a_init_.HAD_APA_LightControlValid = init_data["IVI_35A"]["HAD_APA_LightControlValid"];
        ivi_35a_init_.HAD_APA_OnOffDisp = init_data["IVI_35A"]["HAD_APA_OnOffDisp"];
        ivi_35a_init_.HAD_APA_UPAErrorDisp = init_data["IVI_35A"]["HAD_APA_UPAErrorDisp"];
        ivi_35a_init_.HAD_APA_MPAPArkNoticeDisp = init_data["IVI_35A"]["HAD_APA_MPAPArkNoticeDisp"];
        ivi_35a_init_.HAD_APA_MPAStFeedbackSt = init_data["IVI_35A"]["HAD_APA_MPAStFeedbackSt"];
        ivi_35a_init_.HAD_APA_MPAReadyFbSt = init_data["IVI_35A"]["HAD_APA_MPAReadyFbSt"];
    }

}
}

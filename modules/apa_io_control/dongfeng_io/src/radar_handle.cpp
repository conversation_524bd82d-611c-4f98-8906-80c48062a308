#include "radar_handle.h"

namespace io_server {
namespace dongfeng {

RadarHandler::RadarHandler()
{
}

RadarHandler::~RadarHandler()
{
}

void RadarHandler::ParseRadarTargetCanRcvMsg(){
    std::vector<std::pair<uint64_t, can_udp_packet_t>> vec_rev_msg;
  radar_can_msg_queue_->ExtractAll(&vec_rev_msg);
  if(vec_rev_msg.empty()){
    // AINFO << "can msg is empty.";
  }
  else{
    int msg_size = vec_rev_msg.size();
    //AINFO << "has can msg, msg_size="<<msg_size;
    for(int i = 0;i < msg_size;i++){
        std::pair<uint64_t, can_udp_packet_t> msgx = vec_rev_msg.at(i);
        can_udp_packet_t can_packet = msgx.second;
        //prase bus_id = 2/3/4   
        auto it = can234msg_parser_map_.find(can_packet.can_id);
        if(it != can234msg_parser_map_.end())
        {
            CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                                    can_packet.message_len, can_packet.data);//
            (this->*(it->second))(msgx.first);
        }
    }
  }
}

#if ENABLE_RADAR_CLOUD
void RadarHandler::ParseRadarCloudCanRcvMsg(){
    //division canmsg with header
    std::vector<std::pair<uint64_t, can_udp_packet_t>> vec_rev_msg;
    radar_points_can_msg_queue_->ExtractAll(&vec_rev_msg);
    if(vec_rev_msg.empty()){
        // AINFO << "can msg is empty.";
    }
    else{
        int msg_size = vec_rev_msg.size();
        //AINFO << "has can msg, msg_size="<<msg_size;
        radar_points_fc_can_msg_.reserve(msg_size);
        for(int i = 0;i < msg_size;i++){
            std::pair<uint64_t, can_udp_packet_t> msgx = vec_rev_msg.at(i);
            can_udp_packet_t can_packet = msgx.second;
            if (can_packet.can_id >= 0x251 && 0x26A >= can_packet.can_id)
            {
                radar_points_fc_can_msg_.emplace_back(msgx);
                if(can_packet.can_id == 0x251){
                    std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> can_tuple = 
                        std::make_tuple(true, msgx.first, radar_points_fc_can_msg_);
                    radar_points_fc_can_msg_.clear();
                    radar_points_fc_can_msg_queue_.emplace(can_tuple);
                    while (radar_points_fc_can_msg_queue_.size() > 3)
                    {
                        radar_points_fc_can_msg_queue_.pop();
                    }
                }
            }
            else if (can_packet.can_id >= 0x201 && 0x21A >= can_packet.can_id)
            {
                radar_points_fl_can_msg_.emplace_back(msgx);
                if(can_packet.can_id == 0x201){
                    std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> can_tuple = 
                        std::make_tuple(true, msgx.first, radar_points_fl_can_msg_);
                    radar_points_fl_can_msg_.clear();
                    radar_points_fl_can_msg_queue_.emplace(can_tuple);
                    while (radar_points_fl_can_msg_queue_.size() > 3)
                    {
                        radar_points_fl_can_msg_queue_.pop();
                    }
                }
            }
            else if (can_packet.can_id >= 0x281 && 0x29A >= can_packet.can_id)
            {
                radar_points_fr_can_msg_.emplace_back(msgx);
                if(can_packet.can_id == 0x281){
                    std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> can_tuple = 
                        std::make_tuple(true, msgx.first, radar_points_fr_can_msg_);
                    radar_points_fr_can_msg_.clear();
                    radar_points_fr_can_msg_queue_.emplace(can_tuple);
                    while (radar_points_fr_can_msg_queue_.size() > 3)
                    {
                        radar_points_fr_can_msg_queue_.pop();
                    }
                }
            }
            else if (can_packet.can_id >= 0x101 && 0x11A >= can_packet.can_id)
            {
                radar_points_rl_can_msg_.emplace_back(msgx);
                if(can_packet.can_id == 0x101){
                    std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> can_tuple = 
                        std::make_tuple(true, msgx.first, radar_points_rl_can_msg_);
                    radar_points_rl_can_msg_.clear();
                    radar_points_rl_can_msg_queue_.emplace(can_tuple);
                    while (radar_points_rl_can_msg_queue_.size() > 3)
                    {
                        radar_points_rl_can_msg_queue_.pop();
                    }
                }
            }
            else if (can_packet.can_id >= 0x181 && 0x19A >= can_packet.can_id)
            {
                radar_points_rr_can_msg_.emplace_back(msgx);
                if(can_packet.can_id == 0x181){
                    std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> can_tuple = 
                        std::make_tuple(true, msgx.first, radar_points_rr_can_msg_);
                    radar_points_rr_can_msg_.clear();
                    radar_points_rr_can_msg_queue_.emplace(can_tuple);
                    while (radar_points_rr_can_msg_queue_.size() > 3)
                    {
                        radar_points_rr_can_msg_queue_.pop();
                    }
                }
            }
        }
    }

    //parse can msg
    if(!radar_points_fc_can_msg_queue_.empty()){
        std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> elem = 
            radar_points_fc_can_msg_queue_.front();
        if(std::get<0>(elem)){ //can队列是否收到header
            radar_points_fc_can_msg_queue_.pop();
            std::get<0>(radar_msg_save_map_.at(RADAR_FC)) = true; // valid
            std::get<1>(radar_msg_save_map_.at(RADAR_FC)) = std::get<1>(elem); //time
            std::vector<std::pair<uint64_t, can_udp_packet_t>> can_vec = std::get<2>(elem);
            int can_vec_size = can_vec.size();
            for(int i = 0; i < can_vec_size; i++){
                can_udp_packet_t can_packet = can_vec.at(i).second;
                auto it = can234msg_parser_map_.find(can_packet.can_id);
                if(it != can234msg_parser_map_.end())
                {
                    MK_FC_CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                                            can_packet.message_len, can_packet.data);//
                    (this->*(it->second))(can_vec.at(i).first);
                }
            }
        }
    }
    if(!radar_points_fl_can_msg_queue_.empty()){
        std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> elem = 
            radar_points_fl_can_msg_queue_.front();
        if(std::get<0>(elem)){
            radar_points_fl_can_msg_queue_.pop();
            std::get<0>(radar_msg_save_map_.at(RADAR_FL)) = true; // valid
            std::get<1>(radar_msg_save_map_.at(RADAR_FL)) = std::get<1>(elem); //time
            std::vector<std::pair<uint64_t, can_udp_packet_t>> can_vec = std::get<2>(elem);
            int can_vec_size = can_vec.size();
            for(int i = 0; i < can_vec_size; i++){
                can_udp_packet_t can_packet = can_vec.at(i).second;
                auto it = can234msg_parser_map_.find(can_packet.can_id);
                if(it != can234msg_parser_map_.end())
                {
                    MK_FL_CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                                            can_packet.message_len, can_packet.data);//
                    (this->*(it->second))(can_vec.at(i).first);
                }
            }
        }
    }
    if(!radar_points_fr_can_msg_queue_.empty()){
        std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> elem = 
            radar_points_fr_can_msg_queue_.front();
        if(std::get<0>(elem)){
            radar_points_fr_can_msg_queue_.pop();
            // radar_msg_save_map_.at(RADAR_FR).first = std::get<1>(elem);
            std::get<0>(radar_msg_save_map_.at(RADAR_FR)) = true; // valid
            std::get<1>(radar_msg_save_map_.at(RADAR_FR)) = std::get<1>(elem); //time
            std::vector<std::pair<uint64_t, can_udp_packet_t>> can_vec = std::get<2>(elem);
            int can_vec_size = can_vec.size();
            for(int i = 0; i < can_vec_size; i++){
                can_udp_packet_t can_packet = can_vec.at(i).second;
                auto it = can234msg_parser_map_.find(can_packet.can_id);
                if(it != can234msg_parser_map_.end())
                {
                    MK_FR_CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                                            can_packet.message_len, can_packet.data);//
                    (this->*(it->second))(can_vec.at(i).first);
                }
            }
        }
    }
    if(!radar_points_rl_can_msg_queue_.empty()){
        std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> elem = 
            radar_points_rl_can_msg_queue_.front();
        if(std::get<0>(elem)){
            radar_points_rl_can_msg_queue_.pop();
            std::get<0>(radar_msg_save_map_.at(RADAR_RL)) = true; // valid
            std::get<1>(radar_msg_save_map_.at(RADAR_RL)) = std::get<1>(elem); //time
            std::vector<std::pair<uint64_t, can_udp_packet_t>> can_vec = std::get<2>(elem);
            int can_vec_size = can_vec.size();
            for(int i = 0; i < can_vec_size; i++){
                can_udp_packet_t can_packet = can_vec.at(i).second;
                auto it = can234msg_parser_map_.find(can_packet.can_id);
                if(it != can234msg_parser_map_.end())
                {
                    MK_RL_CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                                            can_packet.message_len, can_packet.data);//
                    (this->*(it->second))(can_vec.at(i).first);
                }
            }
        }
    }
    if(!radar_points_rr_can_msg_queue_.empty()){
        std::tuple<bool, uint64_t, std::vector<std::pair<uint64_t, can_udp_packet_t>>> elem = 
            radar_points_rr_can_msg_queue_.front();
        if(std::get<0>(elem)){
            radar_points_rr_can_msg_queue_.pop();
            std::get<0>(radar_msg_save_map_.at(RADAR_RR)) = true; // valid
            std::get<1>(radar_msg_save_map_.at(RADAR_RR)) = std::get<1>(elem); //time
            std::vector<std::pair<uint64_t, can_udp_packet_t>> can_vec = std::get<2>(elem);
            int can_vec_size = can_vec.size();
            for(int i = 0; i < can_vec_size; i++){
                can_udp_packet_t can_packet = can_vec.at(i).second;
                auto it = can234msg_parser_map_.find(can_packet.can_id);
                if(it != can234msg_parser_map_.end())
                {
                    MK_RR_CAN_PARSER_FrameHandler(can_packet.bus_id, can_packet.can_id,
                                            can_packet.message_len, can_packet.data);//
                    (this->*(it->second))(can_vec.at(i).first);
                }
            }
        }
    }
}
#endif

void RadarHandler::ParseAllRadarCanRcvMsg() {
    if(2 == radar_handle_type_){
        ParseRadarTargetCanRcvMsg();
#if ENABLE_RADAR_CLOUD
        ParseRadarCloudCanRcvMsg();
#endif
    }
    else{
        ParseRadarTargetCanRcvMsg();
    }
}
  
bool RadarHandler::Init(){
    radar_can_msg_queue_.reset(new ThreadSafeQueue<std::pair<uint64_t, can_udp_packet_t>>(200));
    radar_points_can_msg_queue_.reset(new ThreadSafeQueue<std::pair<uint64_t, can_udp_packet_t>>(280));

#if ENABLE_RADAR_CLOUD
    radar_msg_save_map_.resize(5);
    std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>> radar_fc_msg_save_map; //canid pair<valid, struct> 
    std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>> radar_fl_msg_save_map;
    std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>> radar_fr_msg_save_map;
    std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>> radar_rl_msg_save_map; 
    std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>> radar_rr_msg_save_map; 
    for(auto id : fc_can_vec_){
        radar_fc_msg_save_map.emplace(id, std::make_pair(false, StruRadarCANDetection()));
    }
    for(auto id : fl_can_vec_){
        radar_fl_msg_save_map.emplace(id, std::make_pair(false, StruRadarCANDetection()));
    }
    for(auto id : fr_can_vec_){
        radar_fr_msg_save_map.emplace(id, std::make_pair(false, StruRadarCANDetection()));
    }
    for(auto id : rl_can_vec_){
        radar_rl_msg_save_map.emplace(id, std::make_pair(false, StruRadarCANDetection()));
    }
    for(auto id : rr_can_vec_){
        radar_rr_msg_save_map.emplace(id, std::make_pair(false, StruRadarCANDetection()));
    }
    radar_msg_save_map_.at(RADAR_FC) = 
        std::make_tuple(false, 0.0, radar_fc_msg_save_map);
    radar_msg_save_map_.at(RADAR_FL) = 
        std::make_tuple(false, 0.0, radar_fl_msg_save_map);
    radar_msg_save_map_.at(RADAR_FR) = 
        std::make_tuple(false, 0.0, radar_fr_msg_save_map);
    radar_msg_save_map_.at(RADAR_RL) = 
        std::make_tuple(false, 0.0, radar_rl_msg_save_map);
    radar_msg_save_map_.at(RADAR_RR) = 
        std::make_tuple(false, 0.0, radar_rr_msg_save_map);
#endif

    radar_handle_type_ = ConfigData::Instance()->radar_handle_type_;
    AINFO << "radar_handle: radar_handle_type="<<radar_handle_type_;

    init_flag_ = true;
    return true;
}

bool RadarHandler::IsRadarCanData(uint32_t can_id){
    auto it = can234msg_parser_map_.find(can_id);
    if(it != can234msg_parser_map_.end()){
        return true;
    }
    return false;
}

bool RadarHandler::EnqueueRadarData(std::pair<uint64_t, can_udp_packet_t> data){
    auto it = radar_target_msg_table_.find(data.second.can_id);
    if(it != radar_target_msg_table_.end()){
        //丢弃与最新时间对比大于150ms的数据
        std::pair<uint64_t, can_udp_packet_t> front_data;
        if(radar_can_msg_queue_->GetFront(&front_data)){
            if(fabs(data.first - front_data.first) > 150){
                radar_can_msg_queue_->PopFront();
            }
        }
        radar_can_msg_queue_->Enqueue(data);
    }
    else{
        if(2 == radar_handle_type_){
            radar_points_can_msg_queue_->Enqueue(data);
        }
    }
    return true;
}

bool RadarHandler::ClearRadarData(){
    radar_can_msg_queue_->Clear();
    radar_points_can_msg_queue_->Clear();
    return true;
}

#if ENABLE_RADAR_CLOUD
bool RadarHandler::GetRadarDebugInfo(StrucNewRadarData& radar_data,
        std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>>& radar_map_data){
    radar_data = io_radar_info_debug_tmp_;
    radar_map_data = radar_msg_save_map_;
    return true;
}
#else
bool RadarHandler::GetRadarDebugInfo(StrucNewRadarData& radar_data){
    radar_data = io_radar_info_debug_tmp_;
    return true;
}
#endif

void RadarHandler::ResetRadarDebugInfoValid() {

    while(!io_radar_info_debug_tmp_.radar_fc.empty()){
      io_radar_info_debug_tmp_.radar_fc.pop();
    }
    while(!io_radar_info_debug_tmp_.radar_fl.empty()){
      io_radar_info_debug_tmp_.radar_fl.pop();
    }
    while(!io_radar_info_debug_tmp_.radar_fr.empty()){
      io_radar_info_debug_tmp_.radar_fr.pop();
    }
    while(!io_radar_info_debug_tmp_.radar_rl.empty()){
      io_radar_info_debug_tmp_.radar_rl.pop();
    }
    while(!io_radar_info_debug_tmp_.radar_rr.empty()){
      io_radar_info_debug_tmp_.radar_rr.pop();
    }

    io_radar_info_debug_tmp_.radar_fc_valid = 0;
    io_radar_info_debug_tmp_.radar_fl_valid = 0;
    io_radar_info_debug_tmp_.radar_fr_valid = 0;
    io_radar_info_debug_tmp_.radar_rl_valid = 0;
    io_radar_info_debug_tmp_.radar_rr_valid = 0;

#if ENABLE_RADAR_CLOUD
    std::get<0>(radar_msg_save_map_.at(RADAR_FC)) = false;
    std::get<0>(radar_msg_save_map_.at(RADAR_FL)) = false;
    std::get<0>(radar_msg_save_map_.at(RADAR_FR)) = false;
    std::get<0>(radar_msg_save_map_.at(RADAR_RL)) = false;
    std::get<0>(radar_msg_save_map_.at(RADAR_RR)) = false;
    for(auto canid: fc_can_vec_){
        std::get<2>(radar_msg_save_map_.at(RADAR_FC))[canid].first = false;
        // radar_msg_save_map_.at(RADAR_FC).second[canid].first = false;
        // radar_msg_save_map_[canid].first = false;
    }
    for(auto canid: fl_can_vec_){
        std::get<2>(radar_msg_save_map_.at(RADAR_FL))[canid].first = false;
    }
    for(auto canid: fr_can_vec_){
        std::get<2>(radar_msg_save_map_.at(RADAR_FR))[canid].first = false;
    }
    for(auto canid: rl_can_vec_){
        std::get<2>(radar_msg_save_map_.at(RADAR_RL))[canid].first = false;
    }
    for(auto canid: rr_can_vec_){
        std::get<2>(radar_msg_save_map_.at(RADAR_RR))[canid].first = false;
    }
#endif
}

#if ENABLE_RADAR_CLOUD
void RadarHandler::PraseRadarHeader251(uint64_t time){
    static uint64_t last_time = 0;                                  
    static bool frist_record_time = true;                   
    uint64_t time_span = time - last_time;              
    if(true == frist_record_time){                                  
        frist_record_time = false;                                          
        time_span = 0.0;                                                        
    }                                                                               
    last_time = time;      

    std::string radar_str;
    radar_str.append(", radar_fc_251_measurement_time=").append(std::to_string(static_cast<double>(time) / 1000.0)
                                          ).append(", radar_fc_251_time_span=").append(std::to_string(time_span)); 
    uint32_t numInfrastructureDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_FC__numInfrastructureDetected_FC_g, numInfrastructureDetected);
    radar_str.append(", fc_251_numInfrastructureDetected=").append(std::to_string(numInfrastructureDetected));
    uint32_t numNonInfraDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_FC__numNonInfraDetected_FC_g, numNonInfraDetected);
    radar_str.append(", fc_251_numNonInfraDetected=").append(std::to_string(numNonInfraDetected));

    cloud_debug_info_ += radar_str;                              
}

void RadarHandler::PraseRadarHeader201(uint64_t time){
    static uint64_t last_time = 0;                                  
    static bool frist_record_time = true;                   
    uint64_t time_span = time - last_time;              
    if(true == frist_record_time){                                  
        frist_record_time = false;                                          
        time_span = 0.0;                                                        
    }                                                                               
    last_time = time;      

    std::string radar_str;
    radar_str.append(", radar_fl_201_measurement_time=").append(std::to_string(static_cast<double>(time) / 1000.0)
                                          ).append(", radar_fl_201_time_span=").append(std::to_string(time_span)); 
    uint32_t numInfrastructureDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_FL__numInfrastructureDetected_FL_g, numInfrastructureDetected);
    radar_str.append(", fl_201_numInfrastructureDetected=").append(std::to_string(numInfrastructureDetected));
    uint32_t numNonInfraDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_FL__numNonInfraDetected_FL_g, numNonInfraDetected);
    radar_str.append(", fl_201_numNonInfraDetected=").append(std::to_string(numNonInfraDetected));

    cloud_debug_info_ += radar_str; 
}

void RadarHandler::PraseRadarHeader281(uint64_t time){
    static uint64_t last_time = 0;                                  
    static bool frist_record_time = true;                   
    uint64_t time_span = time - last_time;              
    if(true == frist_record_time){                                  
        frist_record_time = false;                                          
        time_span = 0.0;                                                        
    }                                                                               
    last_time = time;      

    std::string radar_str;
    radar_str.append(", radar_fr_281_measurement_time=").append(std::to_string(static_cast<double>(time) / 1000.0)
                                          ).append(", radar_fr_281_time_span=").append(std::to_string(time_span)); 
    uint32_t numInfrastructureDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_FR__numInfrastructureDetected_FR_g, numInfrastructureDetected);
    radar_str.append(", fr_281_numInfrastructureDetected=").append(std::to_string(numInfrastructureDetected));
    uint32_t numNonInfraDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_FR__numNonInfraDetected_FR_g, numNonInfraDetected);
    radar_str.append(", fr_281_numNonInfraDetected=").append(std::to_string(numNonInfraDetected));

    cloud_debug_info_ += radar_str; 
}

void RadarHandler::PraseRadarHeader101(uint64_t time){
    static uint64_t last_time = 0;                                  
    static bool frist_record_time = true;                   
    uint64_t time_span = time - last_time;              
    if(true == frist_record_time){                                  
        frist_record_time = false;                                          
        time_span = 0.0;                                                        
    }                                                                               
    last_time = time;      

    std::string radar_str;
    radar_str.append(", radar_rl_101_measurement_time=").append(std::to_string(static_cast<double>(time) / 1000.0)
                                          ).append(", radar_rl_101_time_span=").append(std::to_string(time_span)); 
    uint32_t numInfrastructureDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_RL__numInfrastructureDetected_RL_g, numInfrastructureDetected);
    radar_str.append(", rl_101_numInfrastructureDetected=").append(std::to_string(numInfrastructureDetected));
    uint32_t numNonInfraDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_RL__numNonInfraDetected_RL_g, numNonInfraDetected);
    radar_str.append(", rl_101_numNonInfraDetected=").append(std::to_string(numNonInfraDetected));

    cloud_debug_info_ += radar_str; 
}

void RadarHandler::PraseRadarHeader181(uint64_t time){
    static uint64_t last_time = 0;                                  
    static bool frist_record_time = true;                   
    uint64_t time_span = time - last_time;              
    if(true == frist_record_time){                                  
        frist_record_time = false;                                          
        time_span = 0.0;                                                        
    }                                                                               
    last_time = time;      

    std::string radar_str;
    radar_str.append(", radar_rr_181_measurement_time=").append(std::to_string(static_cast<double>(time) / 1000.0)
                                          ).append(", radar_rr_181_time_span=").append(std::to_string(time_span)); 
    uint32_t numInfrastructureDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_RR__numInfrastructureDetected_RR_g, numInfrastructureDetected);
    radar_str.append(", rr_181_numInfrastructureDetected=").append(std::to_string(numInfrastructureDetected));
    uint32_t numNonInfraDetected = 0;
    GetSignalValue(&CANSIG_MK_TARGET_DETECTION_HEADER_RR__numNonInfraDetected_RR_g, numNonInfraDetected);
    radar_str.append(", rr_181_numNonInfraDetected=").append(std::to_string(numNonInfraDetected));

    cloud_debug_info_ += radar_str; 
}

//以下是点云解析


void RadarHandler::PraseRadar252(uint64_t time){
    static uint64_t last_time = 0;                                  
    static bool frist_record_time = true;                   
    uint64_t time_span = time - last_time;              
    if(true == frist_record_time){                                  
        frist_record_time = false;                                          
        time_span = 0.0;                                                        
    }                                                                               
    last_time = time;                               
                                                      
    StruRadarCANDetection radar_data;
    radar_data.measurement_time = static_cast<double>(time) / 1000.0;
    std::string radar_str;
    radar_str.append(", radar_252_measurement_time=").append(std::to_string(radar_data.measurement_time)
                                          ).append(", radar_252_time_span=").append(std::to_string(time_span)); 
                                                                                                            
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_0_FC_g, radar_data.CoGRange_NI_0);
    radar_str.append(", 252_CoGRange_NI_0=").append(std::to_string(radar_data.CoGRange_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_0_FC_g, radar_data.CoGDoppler_NI_0);
    radar_str.append(", 252_CoGDoppler_NI_0=").append(std::to_string(radar_data.CoGDoppler_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_0_FC_g, radar_data.Reserve1_1bit_NI_0);
    radar_str.append(", 252_Reserve1_1bit_NI_0=").append(std::to_string(radar_data.Reserve1_1bit_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_0_FC_g, radar_data.PowerDB_NI_0);
    radar_str.append(", 252_PowerDB_NI_0=").append(std::to_string(radar_data.PowerDB_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_0_FC_g, radar_data.SNRdB_NI_0);
    radar_str.append(", 252_SNRdB_NI_0=").append(std::to_string(radar_data.SNRdB_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_0_FC_g, radar_data.StdAzimuth_NI_0);
    radar_str.append(", 252_StdAzimuth_NI_0=").append(std::to_string(radar_data.StdAzimuth_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_0_FC_g, radar_data.Azimuth_NI_0);
    radar_str.append(", 252_Azimuth_NI_0=").append(std::to_string(radar_data.Azimuth_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_0_FC_g, radar_data.Beam_NI_0);
    radar_str.append(", 252_Beam_NI_0=").append(std::to_string(radar_data.Beam_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_0_FC_g, radar_data.NoInfrastructure_NI_0);
    radar_str.append(", 252_NoInfrastructure_NI_0=").append(std::to_string(radar_data.NoInfrastructure_NI_0));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_0_FC_g, radar_data.ValidXBeam_NI_0);
    radar_str.append(", 252_ValidXBeam_NI_0=").append(std::to_string(radar_data.ValidXBeam_NI_0));       

    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_1_FC_g, radar_data.CoGRange_NI_1);
    radar_str.append(", 252_CoGRange_NI_1=").append(std::to_string(radar_data.CoGRange_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_1_FC_g, radar_data.CoGDoppler_NI_1);
    radar_str.append(", 252_CoGDoppler_NI_1=").append(std::to_string(radar_data.CoGDoppler_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_1_FC_g, radar_data.Reserve1_1bit_NI_1);
    radar_str.append(", 252_Reserve1_1bit_NI_1=").append(std::to_string(radar_data.Reserve1_1bit_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_1_FC_g, radar_data.PowerDB_NI_1);
    radar_str.append(", 252_PowerDB_NI_1=").append(std::to_string(radar_data.PowerDB_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_1_FC_g, radar_data.SNRdB_NI_1);
    radar_str.append(", 252_SNRdB_NI_1=").append(std::to_string(radar_data.SNRdB_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_1_FC_g, radar_data.StdAzimuth_NI_1);
    radar_str.append(", 252_StdAzimuth_NI_1=").append(std::to_string(radar_data.StdAzimuth_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_1_FC_g, radar_data.Azimuth_NI_1);
    radar_str.append(", 252_Azimuth_NI_1=").append(std::to_string(radar_data.Azimuth_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_1_FC_g, radar_data.Beam_NI_1);
    radar_str.append(", 252_Beam_NI_1=").append(std::to_string(radar_data.Beam_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_1_FC_g, radar_data.NoInfrastructure_NI_1);
    radar_str.append(", 252_NoInfrastructure_NI_1=").append(std::to_string(radar_data.NoInfrastructure_NI_1));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_1_FC_g, radar_data.ValidXBeam_NI_1);
    radar_str.append(", 252_ValidXBeam_NI_1=").append(std::to_string(radar_data.ValidXBeam_NI_1));       

    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_2_FC_g, radar_data.CoGRange_NI_2);
    radar_str.append(", 252_CoGRange_NI_2=").append(std::to_string(radar_data.CoGRange_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_2_FC_g, radar_data.CoGDoppler_NI_2);
    radar_str.append(", 252_CoGDoppler_NI_2=").append(std::to_string(radar_data.CoGDoppler_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_2_FC_g, radar_data.Reserve1_1bit_NI_2);
    radar_str.append(", 252_Reserve1_1bit_NI_2=").append(std::to_string(radar_data.Reserve1_1bit_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_2_FC_g, radar_data.PowerDB_NI_2);
    radar_str.append(", 252_PowerDB_NI_2=").append(std::to_string(radar_data.PowerDB_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_2_FC_g, radar_data.SNRdB_NI_2);
    radar_str.append(", 252_SNRdB_NI_2=").append(std::to_string(radar_data.SNRdB_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_2_FC_g, radar_data.StdAzimuth_NI_2);
    radar_str.append(", 252_StdAzimuth_NI_2=").append(std::to_string(radar_data.StdAzimuth_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_2_FC_g, radar_data.Azimuth_NI_2);
    radar_str.append(", 252_Azimuth_NI_2=").append(std::to_string(radar_data.Azimuth_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_2_FC_g, radar_data.Beam_NI_2);
    radar_str.append(", 252_Beam_NI_2=").append(std::to_string(radar_data.Beam_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_2_FC_g, radar_data.NoInfrastructure_NI_2);
    radar_str.append(", 252_NoInfrastructure_NI_2=").append(std::to_string(radar_data.NoInfrastructure_NI_2));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_2_FC_g, radar_data.ValidXBeam_NI_2);
    radar_str.append(", 252_ValidXBeam_NI_2=").append(std::to_string(radar_data.ValidXBeam_NI_2));       

    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_3_FC_g, radar_data.CoGRange_NI_3);
    radar_str.append(", 252_CoGRange_NI_3=").append(std::to_string(radar_data.CoGRange_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_3_FC_g, radar_data.CoGDoppler_NI_3);
    radar_str.append(", 252_CoGDoppler_NI_3=").append(std::to_string(radar_data.CoGDoppler_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_3_FC_g, radar_data.Reserve1_1bit_NI_3);
    radar_str.append(", 252_Reserve1_1bit_NI_3=").append(std::to_string(radar_data.Reserve1_1bit_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_3_FC_g, radar_data.PowerDB_NI_3);
    radar_str.append(", 252_PowerDB_NI_3=").append(std::to_string(radar_data.PowerDB_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_3_FC_g, radar_data.SNRdB_NI_3);
    radar_str.append(", 252_SNRdB_NI_3=").append(std::to_string(radar_data.SNRdB_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_3_FC_g, radar_data.StdAzimuth_NI_3);
    radar_str.append(", 252_StdAzimuth_NI_3=").append(std::to_string(radar_data.StdAzimuth_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_3_FC_g, radar_data.Azimuth_NI_3);
    radar_str.append(", 252_Azimuth_NI_3=").append(std::to_string(radar_data.Azimuth_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_3_FC_g, radar_data.Beam_NI_3);
    radar_str.append(", 252_Beam_NI_3=").append(std::to_string(radar_data.Beam_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_3_FC_g, radar_data.NoInfrastructure_NI_3);
    radar_str.append(", 252_NoInfrastructure_NI_3=").append(std::to_string(radar_data.NoInfrastructure_NI_3));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_3_FC_g, radar_data.ValidXBeam_NI_3);
    radar_str.append(", 252_ValidXBeam_NI_3=").append(std::to_string(radar_data.ValidXBeam_NI_3));       

    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_4_FC_g, radar_data.CoGRange_NI_4);
    radar_str.append(", 252_CoGRange_NI_4=").append(std::to_string(radar_data.CoGRange_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_4_FC_g, radar_data.CoGDoppler_NI_4);
    radar_str.append(", 252_CoGDoppler_NI_4=").append(std::to_string(radar_data.CoGDoppler_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_4_FC_g, radar_data.Reserve1_1bit_NI_4);
    radar_str.append(", 252_Reserve1_1bit_NI_4=").append(std::to_string(radar_data.Reserve1_1bit_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_4_FC_g, radar_data.PowerDB_NI_4);
    radar_str.append(", 252_PowerDB_NI_4=").append(std::to_string(radar_data.PowerDB_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_4_FC_g, radar_data.SNRdB_NI_4);
    radar_str.append(", 252_SNRdB_NI_4=").append(std::to_string(radar_data.SNRdB_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_4_FC_g, radar_data.StdAzimuth_NI_4);
    radar_str.append(", 252_StdAzimuth_NI_4=").append(std::to_string(radar_data.StdAzimuth_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_4_FC_g, radar_data.Azimuth_NI_4);
    radar_str.append(", 252_Azimuth_NI_4=").append(std::to_string(radar_data.Azimuth_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_4_FC_g, radar_data.Beam_NI_4);
    radar_str.append(", 252_Beam_NI_4=").append(std::to_string(radar_data.Beam_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_4_FC_g, radar_data.NoInfrastructure_NI_4);
    radar_str.append(", 252_NoInfrastructure_NI_4=").append(std::to_string(radar_data.NoInfrastructure_NI_4));
    GetSignalValue(&CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_4_FC_g, radar_data.ValidXBeam_NI_4);
    radar_str.append(", 252_ValidXBeam_NI_4=").append(std::to_string(radar_data.ValidXBeam_NI_4));              
                                                      
    std::get<2>(radar_msg_save_map_.at(RADAR_FC))[Radar_FC252].first = true;
    std::get<2>(radar_msg_save_map_.at(RADAR_FC))[Radar_FC252].second = radar_data;  
    cloud_debug_info_ += radar_str;                
}

void RadarHandler::PraseRadar253(uint64_t time){
    PARSE_RADAR_PREFIX("253")

    GET_SIGNAL_VALUE("253", FC, 1, 5, 0)
    GET_SIGNAL_VALUE("253", FC, 1, 6, 1)
    GET_SIGNAL_VALUE("253", FC, 1, 7, 2)
    GET_SIGNAL_VALUE("253", FC, 1, 8, 3)
    GET_SIGNAL_VALUE("253", FC, 1, 9, 4)

    PARSE_RADAR_SUFFIX(FC, 0x253, 0)
}
void RadarHandler::PraseRadar254(uint64_t time){
    PARSE_RADAR_PREFIX("254")
    GET_SIGNAL_VALUE("254", FC, 2, 10, 0)
    GET_SIGNAL_VALUE("254", FC, 2, 11, 1)
    GET_SIGNAL_VALUE("254", FC, 2, 12, 2)
    GET_SIGNAL_VALUE("254", FC, 2, 13, 3)
    GET_SIGNAL_VALUE("254", FC, 2, 14, 4)
    PARSE_RADAR_SUFFIX(FC, 0x254, 0)
}
void RadarHandler::PraseRadar255(uint64_t time){
    PARSE_RADAR_PREFIX("255")
    GET_SIGNAL_VALUE("255", FC, 3, 15, 0)
    GET_SIGNAL_VALUE("255", FC, 3, 16, 1)
    GET_SIGNAL_VALUE("255", FC, 3, 17, 2)
    GET_SIGNAL_VALUE("255", FC, 3, 18, 3)
    GET_SIGNAL_VALUE("255", FC, 3, 19, 4)
    PARSE_RADAR_SUFFIX(FC, 0x255, 0)
}
void RadarHandler::PraseRadar256(uint64_t time){
    PARSE_RADAR_PREFIX("256")
    GET_SIGNAL_VALUE("256", FC, 4, 20, 0)
    GET_SIGNAL_VALUE("256", FC, 4, 21, 1)
    GET_SIGNAL_VALUE("256", FC, 4, 22, 2)
    GET_SIGNAL_VALUE("256", FC, 4, 23, 3)
    GET_SIGNAL_VALUE("256", FC, 4, 24, 4)
    PARSE_RADAR_SUFFIX(FC, 0x256, 0)
}
void RadarHandler::PraseRadar257(uint64_t time){
    PARSE_RADAR_PREFIX("257")
    GET_SIGNAL_VALUE("257", FC, 5, 25, 0)
    GET_SIGNAL_VALUE("257", FC, 5, 26, 1)
    GET_SIGNAL_VALUE("257", FC, 5, 27, 2)
    GET_SIGNAL_VALUE("257", FC, 5, 28, 3)
    GET_SIGNAL_VALUE("257", FC, 5, 29, 4)
    PARSE_RADAR_SUFFIX(FC, 0x257, 0)
}
void RadarHandler::PraseRadar258(uint64_t time){
    PARSE_RADAR_PREFIX("258")
    GET_SIGNAL_VALUE("258", FC, 6, 30, 0)
    GET_SIGNAL_VALUE("258", FC, 6, 31, 1)
    GET_SIGNAL_VALUE("258", FC, 6, 32, 2)
    GET_SIGNAL_VALUE("258", FC, 6, 33, 3)
    GET_SIGNAL_VALUE("258", FC, 6, 34, 4)
    PARSE_RADAR_SUFFIX(FC, 0x258, 0)
}
void RadarHandler::PraseRadar259(uint64_t time){
    PARSE_RADAR_PREFIX("259")
    GET_SIGNAL_VALUE("259", FC, 7, 35, 0)
    GET_SIGNAL_VALUE("259", FC, 7, 36, 1)
    GET_SIGNAL_VALUE("259", FC, 7, 37, 2)
    GET_SIGNAL_VALUE("259", FC, 7, 38, 3)
    GET_SIGNAL_VALUE("259", FC, 7, 39, 4)
    PARSE_RADAR_SUFFIX(FC, 0x259, 0)
}
void RadarHandler::PraseRadar25A(uint64_t time){
    PARSE_RADAR_PREFIX("25A")
    GET_SIGNAL_VALUE("25A", FC, 8, 40, 0)
    GET_SIGNAL_VALUE("25A", FC, 8, 41, 1)
    GET_SIGNAL_VALUE("25A", FC, 8, 42, 2)
    GET_SIGNAL_VALUE("25A", FC, 8, 43, 3)
    GET_SIGNAL_VALUE("25A", FC, 8, 44, 4)
    PARSE_RADAR_SUFFIX(FC, 0x25A, 0)
}
void RadarHandler::PraseRadar25B(uint64_t time){
    PARSE_RADAR_PREFIX("25B")
    GET_SIGNAL_VALUE("25B", FC, 9, 45, 0)
    GET_SIGNAL_VALUE("25B", FC, 9, 46, 1)
    GET_SIGNAL_VALUE("25B", FC, 9, 47, 2)
    GET_SIGNAL_VALUE("25B", FC, 9, 48, 3)
    GET_SIGNAL_VALUE("25B", FC, 9, 49, 4)
    PARSE_RADAR_SUFFIX(FC, 0x25B, 0)
}
void RadarHandler::PraseRadar25C(uint64_t time){
    PARSE_RADAR_PREFIX("25C")
    GET_SIGNAL_VALUE("25C", FC, 10, 50, 0)
    GET_SIGNAL_VALUE("25C", FC, 10, 51, 1)
    GET_SIGNAL_VALUE("25C", FC, 10, 52, 2)
    GET_SIGNAL_VALUE("25C", FC, 10, 53, 3)
    GET_SIGNAL_VALUE("25C", FC, 10, 54, 4)
    PARSE_RADAR_SUFFIX(FC, 0x25C, 0)
}
void RadarHandler::PraseRadar25D(uint64_t time){
    PARSE_RADAR_PREFIX("25D")
    GET_SIGNAL_VALUE("25D", FC, 11, 55, 0)
    GET_SIGNAL_VALUE("25D", FC, 11, 56, 1)
    GET_SIGNAL_VALUE("25D", FC, 11, 57, 2)
    GET_SIGNAL_VALUE("25D", FC, 11, 58, 3)
    GET_SIGNAL_VALUE("25D", FC, 11, 59, 4)
    PARSE_RADAR_SUFFIX(FC, 0x25D, 0)
}
void RadarHandler::PraseRadar25E(uint64_t time){
    PARSE_RADAR_PREFIX("25E")
    GET_SIGNAL_VALUE("25E", FC, 12, 60, 0)
    GET_SIGNAL_VALUE("25E", FC, 12, 61, 1)
    GET_SIGNAL_VALUE("25E", FC, 12, 62, 2)
    GET_SIGNAL_VALUE("25E", FC, 12, 63, 3)
    GET_SIGNAL_VALUE("25E", FC, 12, 64, 4)
    PARSE_RADAR_SUFFIX(FC, 0x25E, 0)
}
void RadarHandler::PraseRadar25F(uint64_t time){
    PARSE_RADAR_PREFIX("25F")
    GET_SIGNAL_VALUE("25F", FC, 13, 65, 0)
    GET_SIGNAL_VALUE("25F", FC, 13, 66, 1)
    GET_SIGNAL_VALUE("25F", FC, 13, 67, 2)
    GET_SIGNAL_VALUE("25F", FC, 13, 68, 3)
    GET_SIGNAL_VALUE("25F", FC, 13, 69, 4)
    PARSE_RADAR_SUFFIX(FC, 0x25F, 0)
}
void RadarHandler::PraseRadar260(uint64_t time){
    PARSE_RADAR_PREFIX("260")
    GET_SIGNAL_VALUE("260", FC, 14, 70, 0)
    GET_SIGNAL_VALUE("260", FC, 14, 71, 1)
    GET_SIGNAL_VALUE("260", FC, 14, 72, 2)
    GET_SIGNAL_VALUE("260", FC, 14, 73, 3)
    GET_SIGNAL_VALUE("260", FC, 14, 74, 4)
    PARSE_RADAR_SUFFIX(FC, 0x260, 0)
}
void RadarHandler::PraseRadar261(uint64_t time){
    PARSE_RADAR_PREFIX("261")
    GET_SIGNAL_VALUE("261", FC, 15, 75, 0)
    GET_SIGNAL_VALUE("261", FC, 15, 76, 1)
    GET_SIGNAL_VALUE("261", FC, 15, 77, 2)
    GET_SIGNAL_VALUE("261", FC, 15, 78, 3)
    GET_SIGNAL_VALUE("261", FC, 15, 79, 4)
    PARSE_RADAR_SUFFIX(FC, 0x261, 0)
}
void RadarHandler::PraseRadar262(uint64_t time){
    PARSE_RADAR_PREFIX("262")
    GET_SIGNAL_VALUE("262", FC, 16, 80, 0)
    GET_SIGNAL_VALUE("262", FC, 16, 81, 1)
    GET_SIGNAL_VALUE("262", FC, 16, 82, 2)
    GET_SIGNAL_VALUE("262", FC, 16, 83, 3)
    GET_SIGNAL_VALUE("262", FC, 16, 84, 4)
    PARSE_RADAR_SUFFIX(FC, 0x262, 0)
}
void RadarHandler::PraseRadar263(uint64_t time){
    PARSE_RADAR_PREFIX("263")
    GET_SIGNAL_VALUE("263", FC, 17, 85, 0)
    GET_SIGNAL_VALUE("263", FC, 17, 86, 1)
    GET_SIGNAL_VALUE("263", FC, 17, 87, 2)
    GET_SIGNAL_VALUE("263", FC, 17, 88, 3)
    GET_SIGNAL_VALUE("263", FC, 17, 89, 4)
    PARSE_RADAR_SUFFIX(FC, 0x263, 0)
}
void RadarHandler::PraseRadar264(uint64_t time){
    PARSE_RADAR_PREFIX("264")
    GET_SIGNAL_VALUE("264", FC, 18, 90, 0)
    GET_SIGNAL_VALUE("264", FC, 18, 91, 1)
    GET_SIGNAL_VALUE("264", FC, 18, 92, 2)
    GET_SIGNAL_VALUE("264", FC, 18, 93, 3)
    GET_SIGNAL_VALUE("264", FC, 18, 94, 4)
    PARSE_RADAR_SUFFIX(FC, 0x264, 0)
}
void RadarHandler::PraseRadar265(uint64_t time){
    PARSE_RADAR_PREFIX("265")
    GET_SIGNAL_VALUE("265", FC, 19, 95, 0)
    GET_SIGNAL_VALUE("265", FC, 19, 96, 1)
    GET_SIGNAL_VALUE("265", FC, 19, 97, 2)
    GET_SIGNAL_VALUE("265", FC, 19, 98, 3)
    GET_SIGNAL_VALUE("265", FC, 19, 99, 4)
    PARSE_RADAR_SUFFIX(FC, 0x265, 0)
}
void RadarHandler::PraseRadar266(uint64_t time){
    PARSE_RADAR_PREFIX("266")
    GET_SIGNAL_VALUE("266", FC, 20, 100, 0)
    GET_SIGNAL_VALUE("266", FC, 20, 101, 1)
    GET_SIGNAL_VALUE("266", FC, 20, 102, 2)
    GET_SIGNAL_VALUE("266", FC, 20, 103, 3)
    GET_SIGNAL_VALUE("266", FC, 20, 104, 4)
    PARSE_RADAR_SUFFIX(FC, 0x266, 0)
}
void RadarHandler::PraseRadar267(uint64_t time){
    PARSE_RADAR_PREFIX("267")
    GET_SIGNAL_VALUE("267", FC, 21, 105, 0)
    GET_SIGNAL_VALUE("267", FC, 21, 106, 1)
    GET_SIGNAL_VALUE("267", FC, 21, 107, 2)
    GET_SIGNAL_VALUE("267", FC, 21, 108, 3)
    GET_SIGNAL_VALUE("267", FC, 21, 109, 4)
    PARSE_RADAR_SUFFIX(FC, 0x267, 0)
}
void RadarHandler::PraseRadar268(uint64_t time){
    PARSE_RADAR_PREFIX("268")
    GET_SIGNAL_VALUE("268", FC, 22, 110, 0)
    GET_SIGNAL_VALUE("268", FC, 22, 111, 1)
    GET_SIGNAL_VALUE("268", FC, 22, 112, 2)
    GET_SIGNAL_VALUE("268", FC, 22, 113, 3)
    GET_SIGNAL_VALUE("268", FC, 22, 114, 4)
    PARSE_RADAR_SUFFIX(FC, 0x268, 0)
}
void RadarHandler::PraseRadar269(uint64_t time){
    PARSE_RADAR_PREFIX("269")
    GET_SIGNAL_VALUE("269", FC, 23, 115, 0)
    GET_SIGNAL_VALUE("269", FC, 23, 116, 1)
    GET_SIGNAL_VALUE("269", FC, 23, 117, 2)
    GET_SIGNAL_VALUE("269", FC, 23, 118, 3)
    GET_SIGNAL_VALUE("269", FC, 23, 119, 4)
    PARSE_RADAR_SUFFIX(FC, 0x269, 0)
}
void RadarHandler::PraseRadar26A(uint64_t time){
    PARSE_RADAR_PREFIX("26A")
    GET_SIGNAL_VALUE("26A", FC, 24, 120, 0)
    GET_SIGNAL_VALUE("26A", FC, 24, 121, 1)
    GET_SIGNAL_VALUE("26A", FC, 24, 122, 2)
    GET_SIGNAL_VALUE("26A", FC, 24, 123, 3)
    GET_SIGNAL_VALUE("26A", FC, 24, 124, 4)
    PARSE_RADAR_SUFFIX(FC, 0x26A, 0)
}

void RadarHandler::PraseRadar202(uint64_t time){
    PARSE_RADAR_PREFIX("202")
    GET_SIGNAL_VALUE("202", FL, 0, 0, 0)
    GET_SIGNAL_VALUE("202", FL, 0, 1, 1)
    GET_SIGNAL_VALUE("202", FL, 0, 2, 2)
    GET_SIGNAL_VALUE("202", FL, 0, 3, 3)
    GET_SIGNAL_VALUE("202", FL, 0, 4, 4)
    PARSE_RADAR_SUFFIX(FL, 0x202, 0)
}
void RadarHandler::PraseRadar203(uint64_t time){
    PARSE_RADAR_PREFIX("203")
    GET_SIGNAL_VALUE("203", FL, 1, 5, 0)
    GET_SIGNAL_VALUE("203", FL, 1, 6, 1)
    GET_SIGNAL_VALUE("203", FL, 1, 7, 2)
    GET_SIGNAL_VALUE("203", FL, 1, 8, 3)
    GET_SIGNAL_VALUE("203", FL, 1, 9, 4)
    PARSE_RADAR_SUFFIX(FL, 0x203, 0)
}
void RadarHandler::PraseRadar204(uint64_t time){
    PARSE_RADAR_PREFIX("204")
    GET_SIGNAL_VALUE("204", FL, 2, 10, 0)
    GET_SIGNAL_VALUE("204", FL, 2, 11, 1)
    GET_SIGNAL_VALUE("204", FL, 2, 12, 2)
    GET_SIGNAL_VALUE("204", FL, 2, 13, 3)
    GET_SIGNAL_VALUE("204", FL, 2, 14, 4)
    PARSE_RADAR_SUFFIX(FL, 0x204, 0)
}
void RadarHandler::PraseRadar205(uint64_t time){
    PARSE_RADAR_PREFIX("205")
    GET_SIGNAL_VALUE("205", FL, 3, 15, 0)
    GET_SIGNAL_VALUE("205", FL, 3, 16, 1)
    GET_SIGNAL_VALUE("205", FL, 3, 17, 2)
    GET_SIGNAL_VALUE("205", FL, 3, 18, 3)
    GET_SIGNAL_VALUE("205", FL, 3, 19, 4)
    PARSE_RADAR_SUFFIX(FL, 0x205, 0)
}
void RadarHandler::PraseRadar206(uint64_t time){
    PARSE_RADAR_PREFIX("206")
    GET_SIGNAL_VALUE("206", FL, 4, 20, 0)
    GET_SIGNAL_VALUE("206", FL, 4, 21, 1)
    GET_SIGNAL_VALUE("206", FL, 4, 22, 2)
    GET_SIGNAL_VALUE("206", FL, 4, 23, 3)
    GET_SIGNAL_VALUE("206", FL, 4, 24, 4)
    PARSE_RADAR_SUFFIX(FL, 0x206, 0)
}
void RadarHandler::PraseRadar207(uint64_t time){
    PARSE_RADAR_PREFIX("207")
    GET_SIGNAL_VALUE("207", FL, 5, 25, 0)
    GET_SIGNAL_VALUE("207", FL, 5, 26, 1)
    GET_SIGNAL_VALUE("207", FL, 5, 27, 2)
    GET_SIGNAL_VALUE("207", FL, 5, 28, 3)
    GET_SIGNAL_VALUE("207", FL, 5, 29, 4)
    PARSE_RADAR_SUFFIX(FL, 0x207, 0)
}
void RadarHandler::PraseRadar208(uint64_t time){
    PARSE_RADAR_PREFIX("208")
    GET_SIGNAL_VALUE("208", FL, 6, 30, 0)
    GET_SIGNAL_VALUE("208", FL, 6, 31, 1)
    GET_SIGNAL_VALUE("208", FL, 6, 32, 2)
    GET_SIGNAL_VALUE("208", FL, 6, 33, 3)
    GET_SIGNAL_VALUE("208", FL, 6, 34, 4)
    PARSE_RADAR_SUFFIX(FL, 0x208, 0)
}
void RadarHandler::PraseRadar209(uint64_t time){
    PARSE_RADAR_PREFIX("209")
    GET_SIGNAL_VALUE("209", FL, 7, 35, 0)
    GET_SIGNAL_VALUE("209", FL, 7, 36, 1)
    GET_SIGNAL_VALUE("209", FL, 7, 37, 2)
    GET_SIGNAL_VALUE("209", FL, 7, 38, 3)
    GET_SIGNAL_VALUE("209", FL, 7, 39, 4)
    PARSE_RADAR_SUFFIX(FL, 0x209, 0)
}
void RadarHandler::PraseRadar20A(uint64_t time){
    PARSE_RADAR_PREFIX("20A")
    GET_SIGNAL_VALUE("20A", FL, 8, 40, 0)
    GET_SIGNAL_VALUE("20A", FL, 8, 41, 1)
    GET_SIGNAL_VALUE("20A", FL, 8, 42, 2)
    GET_SIGNAL_VALUE("20A", FL, 8, 43, 3)
    GET_SIGNAL_VALUE("20A", FL, 8, 44, 4)
    PARSE_RADAR_SUFFIX(FL, 0x20A, 0)
}
void RadarHandler::PraseRadar20B(uint64_t time){
    PARSE_RADAR_PREFIX("20B")
    GET_SIGNAL_VALUE("20B", FL, 9, 45, 0)
    GET_SIGNAL_VALUE("20B", FL, 9, 46, 1)
    GET_SIGNAL_VALUE("20B", FL, 9, 47, 2)
    GET_SIGNAL_VALUE("20B", FL, 9, 48, 3)
    GET_SIGNAL_VALUE("20B", FL, 9, 49, 4)
    PARSE_RADAR_SUFFIX(FL, 0x20B, 0)
}
void RadarHandler::PraseRadar20C(uint64_t time){
    PARSE_RADAR_PREFIX("20C")
    GET_SIGNAL_VALUE("20C", FL, 10, 50, 0)
    GET_SIGNAL_VALUE("20C", FL, 10, 51, 1)
    GET_SIGNAL_VALUE("20C", FL, 10, 52, 2)
    GET_SIGNAL_VALUE("20C", FL, 10, 53, 3)
    GET_SIGNAL_VALUE("20C", FL, 10, 54, 4)
    PARSE_RADAR_SUFFIX(FL, 0x20C, 0)
}
void RadarHandler::PraseRadar20D(uint64_t time){
    PARSE_RADAR_PREFIX("20D")
    GET_SIGNAL_VALUE("20D", FL, 11, 55, 0)
    GET_SIGNAL_VALUE("20D", FL, 11, 56, 1)
    GET_SIGNAL_VALUE("20D", FL, 11, 57, 2)
    GET_SIGNAL_VALUE("20D", FL, 11, 58, 3)
    GET_SIGNAL_VALUE("20D", FL, 11, 59, 4)
    PARSE_RADAR_SUFFIX(FL, 0x20D, 0)
}
void RadarHandler::PraseRadar20E(uint64_t time){
    PARSE_RADAR_PREFIX("20E")
    GET_SIGNAL_VALUE("20E", FL, 12, 60, 0)
    GET_SIGNAL_VALUE("20E", FL, 12, 61, 1)
    GET_SIGNAL_VALUE("20E", FL, 12, 62, 2)
    GET_SIGNAL_VALUE("20E", FL, 12, 63, 3)
    GET_SIGNAL_VALUE("20E", FL, 12, 64, 4)
    PARSE_RADAR_SUFFIX(FL, 0x20E, 0)
}
void RadarHandler::PraseRadar20F(uint64_t time){
    PARSE_RADAR_PREFIX("20F")
    GET_SIGNAL_VALUE("20F", FL, 13, 65, 0)
    GET_SIGNAL_VALUE("20F", FL, 13, 66, 1)
    GET_SIGNAL_VALUE("20F", FL, 13, 67, 2)
    GET_SIGNAL_VALUE("20F", FL, 13, 68, 3)
    GET_SIGNAL_VALUE("20F", FL, 13, 69, 4)
    PARSE_RADAR_SUFFIX(FL, 0x20F, 0)
}
void RadarHandler::PraseRadar210(uint64_t time){
    PARSE_RADAR_PREFIX("210")
    GET_SIGNAL_VALUE("210", FL, 14, 70, 0)
    GET_SIGNAL_VALUE("210", FL, 14, 71, 1)
    GET_SIGNAL_VALUE("210", FL, 14, 72, 2)
    GET_SIGNAL_VALUE("210", FL, 14, 73, 3)
    GET_SIGNAL_VALUE("210", FL, 14, 74, 4)
    PARSE_RADAR_SUFFIX(FL, 0x210, 0)
}
void RadarHandler::PraseRadar211(uint64_t time){
    PARSE_RADAR_PREFIX("211")
    GET_SIGNAL_VALUE("211", FL, 15, 75, 0)
    GET_SIGNAL_VALUE("211", FL, 15, 76, 1)
    GET_SIGNAL_VALUE("211", FL, 15, 77, 2)
    GET_SIGNAL_VALUE("211", FL, 15, 78, 3)
    GET_SIGNAL_VALUE("211", FL, 15, 79, 4)
    PARSE_RADAR_SUFFIX(FL, 0x211, 0)
}
void RadarHandler::PraseRadar212(uint64_t time){
    PARSE_RADAR_PREFIX("212")
    GET_SIGNAL_VALUE("212", FL, 16, 80, 0)
    GET_SIGNAL_VALUE("212", FL, 16, 81, 1)
    GET_SIGNAL_VALUE("212", FL, 16, 82, 2)
    GET_SIGNAL_VALUE("212", FL, 16, 83, 3)
    GET_SIGNAL_VALUE("212", FL, 16, 84, 4)
    PARSE_RADAR_SUFFIX(FL, 0x212, 0)
}
void RadarHandler::PraseRadar213(uint64_t time){
    PARSE_RADAR_PREFIX("213")
    GET_SIGNAL_VALUE("213", FL, 17, 85, 0)
    GET_SIGNAL_VALUE("213", FL, 17, 86, 1)
    GET_SIGNAL_VALUE("213", FL, 17, 87, 2)
    GET_SIGNAL_VALUE("213", FL, 17, 88, 3)
    GET_SIGNAL_VALUE("213", FL, 17, 89, 4)
    PARSE_RADAR_SUFFIX(FL, 0x213, 0)
}
void RadarHandler::PraseRadar214(uint64_t time){
    PARSE_RADAR_PREFIX("214")
    GET_SIGNAL_VALUE("214", FL, 18, 90, 0)
    GET_SIGNAL_VALUE("214", FL, 18, 91, 1)
    GET_SIGNAL_VALUE("214", FL, 18, 92, 2)
    GET_SIGNAL_VALUE("214", FL, 18, 93, 3)
    GET_SIGNAL_VALUE("214", FL, 18, 94, 4)
    PARSE_RADAR_SUFFIX(FL, 0x214, 0)
}
void RadarHandler::PraseRadar215(uint64_t time){
    PARSE_RADAR_PREFIX("215")
    GET_SIGNAL_VALUE("215", FL, 19, 95, 0)
    GET_SIGNAL_VALUE("215", FL, 19, 96, 1)
    GET_SIGNAL_VALUE("215", FL, 19, 97, 2)
    GET_SIGNAL_VALUE("215", FL, 19, 98, 3)
    GET_SIGNAL_VALUE("215", FL, 19, 99, 4)
    PARSE_RADAR_SUFFIX(FL, 0x215, 0)
}
void RadarHandler::PraseRadar216(uint64_t time){
    PARSE_RADAR_PREFIX("216")
    GET_SIGNAL_VALUE("216", FL, 20, 100, 0)
    GET_SIGNAL_VALUE("216", FL, 20, 101, 1)
    GET_SIGNAL_VALUE("216", FL, 20, 102, 2)
    GET_SIGNAL_VALUE("216", FL, 20, 103, 3)
    GET_SIGNAL_VALUE("216", FL, 20, 104, 4)
    PARSE_RADAR_SUFFIX(FL, 0x216, 0)
}
void RadarHandler::PraseRadar217(uint64_t time){
    PARSE_RADAR_PREFIX("217")
    GET_SIGNAL_VALUE("217", FL, 21, 105, 0)
    GET_SIGNAL_VALUE("217", FL, 21, 106, 1)
    GET_SIGNAL_VALUE("217", FL, 21, 107, 2)
    GET_SIGNAL_VALUE("217", FL, 21, 108, 3)
    GET_SIGNAL_VALUE("217", FL, 21, 109, 4)
    PARSE_RADAR_SUFFIX(FL, 0x217, 0)
}
void RadarHandler::PraseRadar218(uint64_t time){
    PARSE_RADAR_PREFIX("218")
    GET_SIGNAL_VALUE("218", FL, 22, 110, 0)
    GET_SIGNAL_VALUE("218", FL, 22, 111, 1)
    GET_SIGNAL_VALUE("218", FL, 22, 112, 2)
    GET_SIGNAL_VALUE("218", FL, 22, 113, 3)
    GET_SIGNAL_VALUE("218", FL, 22, 114, 4)
    PARSE_RADAR_SUFFIX(FL, 0x218, 0)
}
void RadarHandler::PraseRadar219(uint64_t time){
    PARSE_RADAR_PREFIX("219")
    GET_SIGNAL_VALUE("219", FL, 23, 115, 0)
    GET_SIGNAL_VALUE("219", FL, 23, 116, 1)
    GET_SIGNAL_VALUE("219", FL, 23, 117, 2)
    GET_SIGNAL_VALUE("219", FL, 23, 118, 3)
    GET_SIGNAL_VALUE("219", FL, 23, 119, 4)
    PARSE_RADAR_SUFFIX(FL, 0x219, 0)
}
void RadarHandler::PraseRadar21A(uint64_t time){
    PARSE_RADAR_PREFIX("21A")
    GET_SIGNAL_VALUE("21A", FL, 24, 120, 0)
    GET_SIGNAL_VALUE("21A", FL, 24, 121, 1)
    GET_SIGNAL_VALUE("21A", FL, 24, 122, 2)
    GET_SIGNAL_VALUE("21A", FL, 24, 123, 3)
    GET_SIGNAL_VALUE("21A", FL, 24, 124, 4)
    PARSE_RADAR_SUFFIX(FL, 0x21A, 0)
}

void RadarHandler::PraseRadar282(uint64_t time){
    PARSE_RADAR_PREFIX("282")
    GET_SIGNAL_VALUE("282", FR, 0, 0, 0)
    GET_SIGNAL_VALUE("282", FR, 0, 1, 1)
    GET_SIGNAL_VALUE("282", FR, 0, 2, 2)
    GET_SIGNAL_VALUE("282", FR, 0, 3, 3)
    GET_SIGNAL_VALUE("282", FR, 0, 4, 4)
    PARSE_RADAR_SUFFIX(FR, 0x282, 0)
}
void RadarHandler::PraseRadar283(uint64_t time){
    PARSE_RADAR_PREFIX("283")
    GET_SIGNAL_VALUE("283", FR, 1, 5, 0)
    GET_SIGNAL_VALUE("283", FR, 1, 6, 1)
    GET_SIGNAL_VALUE("283", FR, 1, 7, 2)
    GET_SIGNAL_VALUE("283", FR, 1, 8, 3)
    GET_SIGNAL_VALUE("283", FR, 1, 9, 4)
    PARSE_RADAR_SUFFIX(FR, 0x283, 0)
}
void RadarHandler::PraseRadar284(uint64_t time){
    PARSE_RADAR_PREFIX("284")
    GET_SIGNAL_VALUE("284", FR, 2, 10, 0)
    GET_SIGNAL_VALUE("284", FR, 2, 11, 1)
    GET_SIGNAL_VALUE("284", FR, 2, 12, 2)
    GET_SIGNAL_VALUE("284", FR, 2, 13, 3)
    GET_SIGNAL_VALUE("284", FR, 2, 14, 4)
    PARSE_RADAR_SUFFIX(FR, 0x284, 0)
}
void RadarHandler::PraseRadar285(uint64_t time){
    PARSE_RADAR_PREFIX("285")
    GET_SIGNAL_VALUE("285", FR, 3, 15, 0)
    GET_SIGNAL_VALUE("285", FR, 3, 16, 1)
    GET_SIGNAL_VALUE("285", FR, 3, 17, 2)
    GET_SIGNAL_VALUE("285", FR, 3, 18, 3)
    GET_SIGNAL_VALUE("285", FR, 3, 19, 4)
    PARSE_RADAR_SUFFIX(FR, 0x285, 0)
}
void RadarHandler::PraseRadar286(uint64_t time){
    PARSE_RADAR_PREFIX("286")
    GET_SIGNAL_VALUE("286", FR, 4, 20, 0)
    GET_SIGNAL_VALUE("286", FR, 4, 21, 1)
    GET_SIGNAL_VALUE("286", FR, 4, 22, 2)
    GET_SIGNAL_VALUE("286", FR, 4, 23, 3)
    GET_SIGNAL_VALUE("286", FR, 4, 24, 4)
    PARSE_RADAR_SUFFIX(FR, 0x286, 0)
}
void RadarHandler::PraseRadar287(uint64_t time){
    PARSE_RADAR_PREFIX("287")
    GET_SIGNAL_VALUE("287", FR, 5, 25, 0)
    GET_SIGNAL_VALUE("287", FR, 5, 26, 1)
    GET_SIGNAL_VALUE("287", FR, 5, 27, 2)
    GET_SIGNAL_VALUE("287", FR, 5, 28, 3)
    GET_SIGNAL_VALUE("287", FR, 5, 29, 4)
    PARSE_RADAR_SUFFIX(FR, 0x287, 0)
}
void RadarHandler::PraseRadar288(uint64_t time){
    PARSE_RADAR_PREFIX("288")
    GET_SIGNAL_VALUE("288", FR, 6, 30, 0)
    GET_SIGNAL_VALUE("288", FR, 6, 31, 1)
    GET_SIGNAL_VALUE("288", FR, 6, 32, 2)
    GET_SIGNAL_VALUE("288", FR, 6, 33, 3)
    GET_SIGNAL_VALUE("288", FR, 6, 34, 4)
    PARSE_RADAR_SUFFIX(FR, 0x288, 0)
}
void RadarHandler::PraseRadar289(uint64_t time){
    PARSE_RADAR_PREFIX("289")
    GET_SIGNAL_VALUE("289", FR, 7, 35, 0)
    GET_SIGNAL_VALUE("289", FR, 7, 36, 1)
    GET_SIGNAL_VALUE("289", FR, 7, 37, 2)
    GET_SIGNAL_VALUE("289", FR, 7, 38, 3)
    GET_SIGNAL_VALUE("289", FR, 7, 39, 4)
    PARSE_RADAR_SUFFIX(FR, 0x289, 0)
}
void RadarHandler::PraseRadar28A(uint64_t time){
    PARSE_RADAR_PREFIX("28A")
    GET_SIGNAL_VALUE("28A", FR, 8, 40, 0)
    GET_SIGNAL_VALUE("28A", FR, 8, 41, 1)
    GET_SIGNAL_VALUE("28A", FR, 8, 42, 2)
    GET_SIGNAL_VALUE("28A", FR, 8, 43, 3)
    GET_SIGNAL_VALUE("28A", FR, 8, 44, 4)
    PARSE_RADAR_SUFFIX(FR, 0x28A, 0)
}
void RadarHandler::PraseRadar28B(uint64_t time){
    PARSE_RADAR_PREFIX("28B")
    GET_SIGNAL_VALUE("28B", FR, 9, 45, 0)
    GET_SIGNAL_VALUE("28B", FR, 9, 46, 1)
    GET_SIGNAL_VALUE("28B", FR, 9, 47, 2)
    GET_SIGNAL_VALUE("28B", FR, 9, 48, 3)
    GET_SIGNAL_VALUE("28B", FR, 9, 49, 4)
    PARSE_RADAR_SUFFIX(FR, 0x28B, 0)
}
void RadarHandler::PraseRadar28C(uint64_t time){
    PARSE_RADAR_PREFIX("28C")
    GET_SIGNAL_VALUE("28C", FR, 10, 50, 0)
    GET_SIGNAL_VALUE("28C", FR, 10, 51, 1)
    GET_SIGNAL_VALUE("28C", FR, 10, 52, 2)
    GET_SIGNAL_VALUE("28C", FR, 10, 53, 3)
    GET_SIGNAL_VALUE("28C", FR, 10, 54, 4)
    PARSE_RADAR_SUFFIX(FR, 0x28C, 0)
}
void RadarHandler::PraseRadar28D(uint64_t time){
    PARSE_RADAR_PREFIX("28D")
    GET_SIGNAL_VALUE("28D", FR, 11, 55, 0)
    GET_SIGNAL_VALUE("28D", FR, 11, 56, 1)
    GET_SIGNAL_VALUE("28D", FR, 11, 57, 2)
    GET_SIGNAL_VALUE("28D", FR, 11, 58, 3)
    GET_SIGNAL_VALUE("28D", FR, 11, 59, 4)
    PARSE_RADAR_SUFFIX(FR, 0x28D, 0)
}
void RadarHandler::PraseRadar28E(uint64_t time){
    PARSE_RADAR_PREFIX("28E")
    GET_SIGNAL_VALUE("28E", FR, 12, 60, 0)
    GET_SIGNAL_VALUE("28E", FR, 12, 61, 1)
    GET_SIGNAL_VALUE("28E", FR, 12, 62, 2)
    GET_SIGNAL_VALUE("28E", FR, 12, 63, 3)
    GET_SIGNAL_VALUE("28E", FR, 12, 64, 4)
    PARSE_RADAR_SUFFIX(FR, 0x28E, 0)
}
void RadarHandler::PraseRadar28F(uint64_t time){
    PARSE_RADAR_PREFIX("28F")
    GET_SIGNAL_VALUE("28F", FR, 13, 65, 0)
    GET_SIGNAL_VALUE("28F", FR, 13, 66, 1)
    GET_SIGNAL_VALUE("28F", FR, 13, 67, 2)
    GET_SIGNAL_VALUE("28F", FR, 13, 68, 3)
    GET_SIGNAL_VALUE("28F", FR, 13, 69, 4)
    PARSE_RADAR_SUFFIX(FR, 0x28F, 0)
}
void RadarHandler::PraseRadar290(uint64_t time){
    PARSE_RADAR_PREFIX("290")
    GET_SIGNAL_VALUE("290", FR, 14, 70, 0)
    GET_SIGNAL_VALUE("290", FR, 14, 71, 1)
    GET_SIGNAL_VALUE("290", FR, 14, 72, 2)
    GET_SIGNAL_VALUE("290", FR, 14, 73, 3)
    GET_SIGNAL_VALUE("290", FR, 14, 74, 4)
    PARSE_RADAR_SUFFIX(FR, 0x290, 0)
}
void RadarHandler::PraseRadar291(uint64_t time){
    PARSE_RADAR_PREFIX("291")
    GET_SIGNAL_VALUE("291", FR, 15, 75, 0)
    GET_SIGNAL_VALUE("291", FR, 15, 76, 1)
    GET_SIGNAL_VALUE("291", FR, 15, 77, 2)
    GET_SIGNAL_VALUE("291", FR, 15, 78, 3)
    GET_SIGNAL_VALUE("291", FR, 15, 79, 4)
    PARSE_RADAR_SUFFIX(FR, 0x291, 0)
}
void RadarHandler::PraseRadar292(uint64_t time){
    PARSE_RADAR_PREFIX("292")
    GET_SIGNAL_VALUE("292", FR, 16, 80, 0)
    GET_SIGNAL_VALUE("292", FR, 16, 81, 1)
    GET_SIGNAL_VALUE("292", FR, 16, 82, 2)
    GET_SIGNAL_VALUE("292", FR, 16, 83, 3)
    GET_SIGNAL_VALUE("292", FR, 16, 84, 4)
    PARSE_RADAR_SUFFIX(FR, 0x292, 0)
}
void RadarHandler::PraseRadar293(uint64_t time){
    PARSE_RADAR_PREFIX("293")
    GET_SIGNAL_VALUE("293", FR, 17, 85, 0)
    GET_SIGNAL_VALUE("293", FR, 17, 86, 1)
    GET_SIGNAL_VALUE("293", FR, 17, 87, 2)
    GET_SIGNAL_VALUE("293", FR, 17, 88, 3)
    GET_SIGNAL_VALUE("293", FR, 17, 89, 4)
    PARSE_RADAR_SUFFIX(FR, 0x293, 0)
}
void RadarHandler::PraseRadar294(uint64_t time){
    PARSE_RADAR_PREFIX("294")
    GET_SIGNAL_VALUE("294", FR, 18, 90, 0)
    GET_SIGNAL_VALUE("294", FR, 18, 91, 1)
    GET_SIGNAL_VALUE("294", FR, 18, 92, 2)
    GET_SIGNAL_VALUE("294", FR, 18, 93, 3)
    GET_SIGNAL_VALUE("294", FR, 18, 94, 4)
    PARSE_RADAR_SUFFIX(FR, 0x294, 0)
}
void RadarHandler::PraseRadar295(uint64_t time){
    PARSE_RADAR_PREFIX("295")
    GET_SIGNAL_VALUE("295", FR, 19, 95, 0)
    GET_SIGNAL_VALUE("295", FR, 19, 96, 1)
    GET_SIGNAL_VALUE("295", FR, 19, 97, 2)
    GET_SIGNAL_VALUE("295", FR, 19, 98, 3)
    GET_SIGNAL_VALUE("295", FR, 19, 99, 4)
    PARSE_RADAR_SUFFIX(FR, 0x295, 0)
}
void RadarHandler::PraseRadar296(uint64_t time){
    PARSE_RADAR_PREFIX("296")
    GET_SIGNAL_VALUE("296", FR, 20, 100, 0)
    GET_SIGNAL_VALUE("296", FR, 20, 101, 1)
    GET_SIGNAL_VALUE("296", FR, 20, 102, 2)
    GET_SIGNAL_VALUE("296", FR, 20, 103, 3)
    GET_SIGNAL_VALUE("296", FR, 20, 104, 4)
    PARSE_RADAR_SUFFIX(FR, 0x296, 0)
}
void RadarHandler::PraseRadar297(uint64_t time){
    PARSE_RADAR_PREFIX("297")
    GET_SIGNAL_VALUE("297", FR, 21, 105, 0)
    GET_SIGNAL_VALUE("297", FR, 21, 106, 1)
    GET_SIGNAL_VALUE("297", FR, 21, 107, 2)
    GET_SIGNAL_VALUE("297", FR, 21, 108, 3)
    GET_SIGNAL_VALUE("297", FR, 21, 109, 4)
    PARSE_RADAR_SUFFIX(FR, 0x297, 0)
}
void RadarHandler::PraseRadar298(uint64_t time){
    PARSE_RADAR_PREFIX("298")
    GET_SIGNAL_VALUE("298", FR, 22, 110, 0)
    GET_SIGNAL_VALUE("298", FR, 22, 111, 1)
    GET_SIGNAL_VALUE("298", FR, 22, 112, 2)
    GET_SIGNAL_VALUE("298", FR, 22, 113, 3)
    GET_SIGNAL_VALUE("298", FR, 22, 114, 4)
    PARSE_RADAR_SUFFIX(FR, 0x298, 0)
}
void RadarHandler::PraseRadar299(uint64_t time){
    PARSE_RADAR_PREFIX("299")
    GET_SIGNAL_VALUE("299", FR, 23, 115, 0)
    GET_SIGNAL_VALUE("299", FR, 23, 116, 1)
    GET_SIGNAL_VALUE("299", FR, 23, 117, 2)
    GET_SIGNAL_VALUE("299", FR, 23, 118, 3)
    GET_SIGNAL_VALUE("299", FR, 23, 119, 4)
    PARSE_RADAR_SUFFIX(FR, 0x299, 0)
}
void RadarHandler::PraseRadar29A(uint64_t time){
    PARSE_RADAR_PREFIX("29A")
    GET_SIGNAL_VALUE("29A", FR, 24, 120, 0)
    GET_SIGNAL_VALUE("29A", FR, 24, 121, 1)
    GET_SIGNAL_VALUE("29A", FR, 24, 122, 2)
    GET_SIGNAL_VALUE("29A", FR, 24, 123, 3)
    GET_SIGNAL_VALUE("29A", FR, 24, 124, 4)
    PARSE_RADAR_SUFFIX(FR, 0x29A, 0)
}

void RadarHandler::PraseRadar102(uint64_t time){
    PARSE_RADAR_PREFIX("102")
    GET_SIGNAL_VALUE("102", RL, 0, 0, 0)
    GET_SIGNAL_VALUE("102", RL, 0, 1, 1)
    GET_SIGNAL_VALUE("102", RL, 0, 2, 2)
    GET_SIGNAL_VALUE("102", RL, 0, 3, 3)
    GET_SIGNAL_VALUE("102", RL, 0, 4, 4)
    PARSE_RADAR_SUFFIX(RL, 0x102, 0)
}
void RadarHandler::PraseRadar103(uint64_t time){
    PARSE_RADAR_PREFIX("103")
    GET_SIGNAL_VALUE("103", RL, 1, 5, 0)
    GET_SIGNAL_VALUE("103", RL, 1, 6, 1)
    GET_SIGNAL_VALUE("103", RL, 1, 7, 2)
    GET_SIGNAL_VALUE("103", RL, 1, 8, 3)
    GET_SIGNAL_VALUE("103", RL, 1, 9, 4)
    PARSE_RADAR_SUFFIX(RL, 0x103, 0)
}
void RadarHandler::PraseRadar104(uint64_t time){
    PARSE_RADAR_PREFIX("104")
    GET_SIGNAL_VALUE("104", RL, 2, 10, 0)
    GET_SIGNAL_VALUE("104", RL, 2, 11, 1)
    GET_SIGNAL_VALUE("104", RL, 2, 12, 2)
    GET_SIGNAL_VALUE("104", RL, 2, 13, 3)
    GET_SIGNAL_VALUE("104", RL, 2, 14, 4)
    PARSE_RADAR_SUFFIX(RL, 0x104, 0)
}
void RadarHandler::PraseRadar105(uint64_t time){
    PARSE_RADAR_PREFIX("105")
    GET_SIGNAL_VALUE("105", RL, 3, 15, 0)
    GET_SIGNAL_VALUE("105", RL, 3, 16, 1)
    GET_SIGNAL_VALUE("105", RL, 3, 17, 2)
    GET_SIGNAL_VALUE("105", RL, 3, 18, 3)
    GET_SIGNAL_VALUE("105", RL, 3, 19, 4)
    PARSE_RADAR_SUFFIX(RL, 0x105, 0)
}
void RadarHandler::PraseRadar106(uint64_t time){
    PARSE_RADAR_PREFIX("106")
    GET_SIGNAL_VALUE("106", RL, 4, 20, 0)
    GET_SIGNAL_VALUE("106", RL, 4, 21, 1)
    GET_SIGNAL_VALUE("106", RL, 4, 22, 2)
    GET_SIGNAL_VALUE("106", RL, 4, 23, 3)
    GET_SIGNAL_VALUE("106", RL, 4, 24, 4)
    PARSE_RADAR_SUFFIX(RL, 0x106, 0)
}
void RadarHandler::PraseRadar107(uint64_t time){
    PARSE_RADAR_PREFIX("107")
    GET_SIGNAL_VALUE("107", RL, 5, 25, 0)
    GET_SIGNAL_VALUE("107", RL, 5, 26, 1)
    GET_SIGNAL_VALUE("107", RL, 5, 27, 2)
    GET_SIGNAL_VALUE("107", RL, 5, 28, 3)
    GET_SIGNAL_VALUE("107", RL, 5, 29, 4)
    PARSE_RADAR_SUFFIX(RL, 0x107, 0)
}
void RadarHandler::PraseRadar108(uint64_t time){
    PARSE_RADAR_PREFIX("108")
    GET_SIGNAL_VALUE("108", RL, 6, 30, 0)
    GET_SIGNAL_VALUE("108", RL, 6, 31, 1)
    GET_SIGNAL_VALUE("108", RL, 6, 32, 2)
    GET_SIGNAL_VALUE("108", RL, 6, 33, 3)
    GET_SIGNAL_VALUE("108", RL, 6, 34, 4)
    PARSE_RADAR_SUFFIX(RL, 0x108, 0)
}
void RadarHandler::PraseRadar109(uint64_t time){
    PARSE_RADAR_PREFIX("109")
    GET_SIGNAL_VALUE("109", RL, 7, 35, 0)
    GET_SIGNAL_VALUE("109", RL, 7, 36, 1)
    GET_SIGNAL_VALUE("109", RL, 7, 37, 2)
    GET_SIGNAL_VALUE("109", RL, 7, 38, 3)
    GET_SIGNAL_VALUE("109", RL, 7, 39, 4)
    PARSE_RADAR_SUFFIX(RL, 0x109, 0)
}
void RadarHandler::PraseRadar10A(uint64_t time){
    PARSE_RADAR_PREFIX("10A")
    GET_SIGNAL_VALUE("10A", RL, 8, 40, 0)
    GET_SIGNAL_VALUE("10A", RL, 8, 41, 1)
    GET_SIGNAL_VALUE("10A", RL, 8, 42, 2)
    GET_SIGNAL_VALUE("10A", RL, 8, 43, 3)
    GET_SIGNAL_VALUE("10A", RL, 8, 44, 4)
    PARSE_RADAR_SUFFIX(RL, 0x10A, 0)
}
void RadarHandler::PraseRadar10B(uint64_t time){
    PARSE_RADAR_PREFIX("10B")
    GET_SIGNAL_VALUE("10B", RL, 9, 45, 0)
    GET_SIGNAL_VALUE("10B", RL, 9, 46, 1)
    GET_SIGNAL_VALUE("10B", RL, 9, 47, 2)
    GET_SIGNAL_VALUE("10B", RL, 9, 48, 3)
    GET_SIGNAL_VALUE("10B", RL, 9, 49, 4)
    PARSE_RADAR_SUFFIX(RL, 0x10B, 0)
}
void RadarHandler::PraseRadar10C(uint64_t time){
    PARSE_RADAR_PREFIX("10C")
    GET_SIGNAL_VALUE("10C", RL, 10, 50, 0)
    GET_SIGNAL_VALUE("10C", RL, 10, 51, 1)
    GET_SIGNAL_VALUE("10C", RL, 10, 52, 2)
    GET_SIGNAL_VALUE("10C", RL, 10, 53, 3)
    GET_SIGNAL_VALUE("10C", RL, 10, 54, 4)
    PARSE_RADAR_SUFFIX(RL, 0x10C, 0)
}
void RadarHandler::PraseRadar10D(uint64_t time){
    PARSE_RADAR_PREFIX("10D")
    GET_SIGNAL_VALUE("10D", RL, 11, 55, 0)
    GET_SIGNAL_VALUE("10D", RL, 11, 56, 1)
    GET_SIGNAL_VALUE("10D", RL, 11, 57, 2)
    GET_SIGNAL_VALUE("10D", RL, 11, 58, 3)
    GET_SIGNAL_VALUE("10D", RL, 11, 59, 4)
    PARSE_RADAR_SUFFIX(RL, 0x10D, 0)
}
void RadarHandler::PraseRadar10E(uint64_t time){
    PARSE_RADAR_PREFIX("10E")
    GET_SIGNAL_VALUE("10E", RL, 12, 60, 0)
    GET_SIGNAL_VALUE("10E", RL, 12, 61, 1)
    GET_SIGNAL_VALUE("10E", RL, 12, 62, 2)
    GET_SIGNAL_VALUE("10E", RL, 12, 63, 3)
    GET_SIGNAL_VALUE("10E", RL, 12, 64, 4)
    PARSE_RADAR_SUFFIX(RL, 0x10E, 0)
}
void RadarHandler::PraseRadar10F(uint64_t time){
    PARSE_RADAR_PREFIX("10F")
    GET_SIGNAL_VALUE("10F", RL, 13, 65, 0)
    GET_SIGNAL_VALUE("10F", RL, 13, 66, 1)
    GET_SIGNAL_VALUE("10F", RL, 13, 67, 2)
    GET_SIGNAL_VALUE("10F", RL, 13, 68, 3)
    GET_SIGNAL_VALUE("10F", RL, 13, 69, 4)
    PARSE_RADAR_SUFFIX(RL, 0x10F, 0)
}
void RadarHandler::PraseRadar110(uint64_t time){
    PARSE_RADAR_PREFIX("110")
    GET_SIGNAL_VALUE("110", RL, 14, 70, 0)
    GET_SIGNAL_VALUE("110", RL, 14, 71, 1)
    GET_SIGNAL_VALUE("110", RL, 14, 72, 2)
    GET_SIGNAL_VALUE("110", RL, 14, 73, 3)
    GET_SIGNAL_VALUE("110", RL, 14, 74, 4)
    PARSE_RADAR_SUFFIX(RL, 0x110, 0)
}
void RadarHandler::PraseRadar111(uint64_t time){
    PARSE_RADAR_PREFIX("111")
    GET_SIGNAL_VALUE("111", RL, 15, 75, 0)
    GET_SIGNAL_VALUE("111", RL, 15, 76, 1)
    GET_SIGNAL_VALUE("111", RL, 15, 77, 2)
    GET_SIGNAL_VALUE("111", RL, 15, 78, 3)
    GET_SIGNAL_VALUE("111", RL, 15, 79, 4)
    PARSE_RADAR_SUFFIX(RL, 0x111, 0)
}
void RadarHandler::PraseRadar112(uint64_t time){
    PARSE_RADAR_PREFIX("112")
    GET_SIGNAL_VALUE("112", RL, 16, 80, 0)
    GET_SIGNAL_VALUE("112", RL, 16, 81, 1)
    GET_SIGNAL_VALUE("112", RL, 16, 82, 2)
    GET_SIGNAL_VALUE("112", RL, 16, 83, 3)
    GET_SIGNAL_VALUE("112", RL, 16, 84, 4)
    PARSE_RADAR_SUFFIX(RL, 0x112, 0)
}
void RadarHandler::PraseRadar113(uint64_t time){
    PARSE_RADAR_PREFIX("113")
    GET_SIGNAL_VALUE("113", RL, 17, 85, 0)
    GET_SIGNAL_VALUE("113", RL, 17, 86, 1)
    GET_SIGNAL_VALUE("113", RL, 17, 87, 2)
    GET_SIGNAL_VALUE("113", RL, 17, 88, 3)
    GET_SIGNAL_VALUE("113", RL, 17, 89, 4)
    PARSE_RADAR_SUFFIX(RL, 0x113, 0)
}
void RadarHandler::PraseRadar114(uint64_t time){
    PARSE_RADAR_PREFIX("114")
    GET_SIGNAL_VALUE("114", RL, 18, 90, 0)
    GET_SIGNAL_VALUE("114", RL, 18, 91, 1)
    GET_SIGNAL_VALUE("114", RL, 18, 92, 2)
    GET_SIGNAL_VALUE("114", RL, 18, 93, 3)
    GET_SIGNAL_VALUE("114", RL, 18, 94, 4)
    PARSE_RADAR_SUFFIX(RL, 0x114, 0)
}
void RadarHandler::PraseRadar115(uint64_t time){
    PARSE_RADAR_PREFIX("115")
    GET_SIGNAL_VALUE("115", RL, 19, 95, 0)
    GET_SIGNAL_VALUE("115", RL, 19, 96, 1)
    GET_SIGNAL_VALUE("115", RL, 19, 97, 2)
    GET_SIGNAL_VALUE("115", RL, 19, 98, 3)
    GET_SIGNAL_VALUE("115", RL, 19, 99, 4)
    PARSE_RADAR_SUFFIX(RL, 0x115, 0)
}
void RadarHandler::PraseRadar116(uint64_t time){
    PARSE_RADAR_PREFIX("116")
    GET_SIGNAL_VALUE("116", RL, 20, 100, 0)
    GET_SIGNAL_VALUE("116", RL, 20, 101, 1)
    GET_SIGNAL_VALUE("116", RL, 20, 102, 2)
    GET_SIGNAL_VALUE("116", RL, 20, 103, 3)
    GET_SIGNAL_VALUE("116", RL, 20, 104, 4)
    PARSE_RADAR_SUFFIX(RL, 0x116, 0)
}
void RadarHandler::PraseRadar117(uint64_t time){
    PARSE_RADAR_PREFIX("117")
    GET_SIGNAL_VALUE("117", RL, 21, 105, 0)
    GET_SIGNAL_VALUE("117", RL, 21, 106, 1)
    GET_SIGNAL_VALUE("117", RL, 21, 107, 2)
    GET_SIGNAL_VALUE("117", RL, 21, 108, 3)
    GET_SIGNAL_VALUE("117", RL, 21, 109, 4)
    PARSE_RADAR_SUFFIX(RL, 0x117, 0)
}
void RadarHandler::PraseRadar118(uint64_t time){
    PARSE_RADAR_PREFIX("118")
    GET_SIGNAL_VALUE("118", RL, 22, 110, 0)
    GET_SIGNAL_VALUE("118", RL, 22, 111, 1)
    GET_SIGNAL_VALUE("118", RL, 22, 112, 2)
    GET_SIGNAL_VALUE("118", RL, 22, 113, 3)
    GET_SIGNAL_VALUE("118", RL, 22, 114, 4)
    PARSE_RADAR_SUFFIX(RL, 0x118, 0)
}
void RadarHandler::PraseRadar119(uint64_t time){
    PARSE_RADAR_PREFIX("119")
    GET_SIGNAL_VALUE("119", RL, 23, 115, 0)
    GET_SIGNAL_VALUE("119", RL, 23, 116, 1)
    GET_SIGNAL_VALUE("119", RL, 23, 117, 2)
    GET_SIGNAL_VALUE("119", RL, 23, 118, 3)
    GET_SIGNAL_VALUE("119", RL, 23, 119, 4)
    PARSE_RADAR_SUFFIX(RL, 0x119, 0)
}
void RadarHandler::PraseRadar11A(uint64_t time){
    PARSE_RADAR_PREFIX("11A")
    GET_SIGNAL_VALUE("11A", RL, 24, 120, 0)
    GET_SIGNAL_VALUE("11A", RL, 24, 121, 1)
    GET_SIGNAL_VALUE("11A", RL, 24, 122, 2)
    GET_SIGNAL_VALUE("11A", RL, 24, 123, 3)
    GET_SIGNAL_VALUE("11A", RL, 24, 124, 4)
    PARSE_RADAR_SUFFIX(RL, 0x11A, 0)
}

void RadarHandler::PraseRadar182(uint64_t time){
    PARSE_RADAR_PREFIX("182")
    GET_SIGNAL_VALUE("182", RR, 0, 0, 0)
    GET_SIGNAL_VALUE("182", RR, 0, 1, 1)
    GET_SIGNAL_VALUE("182", RR, 0, 2, 2)
    GET_SIGNAL_VALUE("182", RR, 0, 3, 3)
    GET_SIGNAL_VALUE("182", RR, 0, 4, 4)
    PARSE_RADAR_SUFFIX(RR, 0x182, 0)
}
void RadarHandler::PraseRadar183(uint64_t time){
    PARSE_RADAR_PREFIX("183")
    GET_SIGNAL_VALUE("183", RR, 1, 5, 0)
    GET_SIGNAL_VALUE("183", RR, 1, 6, 1)
    GET_SIGNAL_VALUE("183", RR, 1, 7, 2)
    GET_SIGNAL_VALUE("183", RR, 1, 8, 3)
    GET_SIGNAL_VALUE("183", RR, 1, 9, 4)
    PARSE_RADAR_SUFFIX(RR, 0x183, 0)
}
void RadarHandler::PraseRadar184(uint64_t time){
    PARSE_RADAR_PREFIX("184")
    GET_SIGNAL_VALUE("184", RR, 2, 10, 0)
    GET_SIGNAL_VALUE("184", RR, 2, 11, 1)
    GET_SIGNAL_VALUE("184", RR, 2, 12, 2)
    GET_SIGNAL_VALUE("184", RR, 2, 13, 3)
    GET_SIGNAL_VALUE("184", RR, 2, 14, 4)
    PARSE_RADAR_SUFFIX(RR, 0x184, 0)
}
void RadarHandler::PraseRadar185(uint64_t time){
    PARSE_RADAR_PREFIX("185")
    GET_SIGNAL_VALUE("185", RR, 3, 15, 0)
    GET_SIGNAL_VALUE("185", RR, 3, 16, 1)
    GET_SIGNAL_VALUE("185", RR, 3, 17, 2)
    GET_SIGNAL_VALUE("185", RR, 3, 18, 3)
    GET_SIGNAL_VALUE("185", RR, 3, 19, 4)
    PARSE_RADAR_SUFFIX(RR, 0x185, 0)
}
void RadarHandler::PraseRadar186(uint64_t time){
    PARSE_RADAR_PREFIX("186")
    GET_SIGNAL_VALUE("186", RR, 4, 20, 0)
    GET_SIGNAL_VALUE("186", RR, 4, 21, 1)
    GET_SIGNAL_VALUE("186", RR, 4, 22, 2)
    GET_SIGNAL_VALUE("186", RR, 4, 23, 3)
    GET_SIGNAL_VALUE("186", RR, 4, 24, 4)
    PARSE_RADAR_SUFFIX(RR, 0x186, 0)
}
void RadarHandler::PraseRadar187(uint64_t time){
    PARSE_RADAR_PREFIX("187")
    GET_SIGNAL_VALUE("187", RR, 5, 25, 0)
    GET_SIGNAL_VALUE("187", RR, 5, 26, 1)
    GET_SIGNAL_VALUE("187", RR, 5, 27, 2)
    GET_SIGNAL_VALUE("187", RR, 5, 28, 3)
    GET_SIGNAL_VALUE("187", RR, 5, 29, 4)
    PARSE_RADAR_SUFFIX(RR, 0x187, 0)
}
void RadarHandler::PraseRadar188(uint64_t time){
    PARSE_RADAR_PREFIX("188")
    GET_SIGNAL_VALUE("188", RR, 6, 30, 0)
    GET_SIGNAL_VALUE("188", RR, 6, 31, 1)
    GET_SIGNAL_VALUE("188", RR, 6, 32, 2)
    GET_SIGNAL_VALUE("188", RR, 6, 33, 3)
    GET_SIGNAL_VALUE("188", RR, 6, 34, 4)
    PARSE_RADAR_SUFFIX(RR, 0x188, 0)
}
void RadarHandler::PraseRadar189(uint64_t time){
    PARSE_RADAR_PREFIX("189")
    GET_SIGNAL_VALUE("189", RR, 7, 35, 0)
    GET_SIGNAL_VALUE("189", RR, 7, 36, 1)
    GET_SIGNAL_VALUE("189", RR, 7, 37, 2)
    GET_SIGNAL_VALUE("189", RR, 7, 38, 3)
    GET_SIGNAL_VALUE("189", RR, 7, 39, 4)
    PARSE_RADAR_SUFFIX(RR, 0x189, 0)
}
void RadarHandler::PraseRadar18A(uint64_t time){
    PARSE_RADAR_PREFIX("18A")
    GET_SIGNAL_VALUE("18A", RR, 8, 40, 0)
    GET_SIGNAL_VALUE("18A", RR, 8, 41, 1)
    GET_SIGNAL_VALUE("18A", RR, 8, 42, 2)
    GET_SIGNAL_VALUE("18A", RR, 8, 43, 3)
    GET_SIGNAL_VALUE("18A", RR, 8, 44, 4)
    PARSE_RADAR_SUFFIX(RR, 0x18A, 0)
}
void RadarHandler::PraseRadar18B(uint64_t time){
    PARSE_RADAR_PREFIX("18B")
    GET_SIGNAL_VALUE("18B", RR, 9, 45, 0)
    GET_SIGNAL_VALUE("18B", RR, 9, 46, 1)
    GET_SIGNAL_VALUE("18B", RR, 9, 47, 2)
    GET_SIGNAL_VALUE("18B", RR, 9, 48, 3)
    GET_SIGNAL_VALUE("18B", RR, 9, 49, 4)
    PARSE_RADAR_SUFFIX(RR, 0x18B, 0)
}
void RadarHandler::PraseRadar18C(uint64_t time){
    PARSE_RADAR_PREFIX("18C")
    GET_SIGNAL_VALUE("18C", RR, 10, 50, 0)
    GET_SIGNAL_VALUE("18C", RR, 10, 51, 1)
    GET_SIGNAL_VALUE("18C", RR, 10, 52, 2)
    GET_SIGNAL_VALUE("18C", RR, 10, 53, 3)
    GET_SIGNAL_VALUE("18C", RR, 10, 54, 4)
    PARSE_RADAR_SUFFIX(RR, 0x18C, 0)
}
void RadarHandler::PraseRadar18D(uint64_t time){
    PARSE_RADAR_PREFIX("18D")
    GET_SIGNAL_VALUE("18D", RR, 11, 55, 0)
    GET_SIGNAL_VALUE("18D", RR, 11, 56, 1)
    GET_SIGNAL_VALUE("18D", RR, 11, 57, 2)
    GET_SIGNAL_VALUE("18D", RR, 11, 58, 3)
    GET_SIGNAL_VALUE("18D", RR, 11, 59, 4)
    PARSE_RADAR_SUFFIX(RR, 0x18D, 0)
}
void RadarHandler::PraseRadar18E(uint64_t time){
    PARSE_RADAR_PREFIX("18E")
    GET_SIGNAL_VALUE("18E", RR, 12, 60, 0)
    GET_SIGNAL_VALUE("18E", RR, 12, 61, 1)
    GET_SIGNAL_VALUE("18E", RR, 12, 62, 2)
    GET_SIGNAL_VALUE("18E", RR, 12, 63, 3)
    GET_SIGNAL_VALUE("18E", RR, 12, 64, 4)
    PARSE_RADAR_SUFFIX(RR, 0x18E, 0)
}
void RadarHandler::PraseRadar18F(uint64_t time){
    PARSE_RADAR_PREFIX("18F")
    GET_SIGNAL_VALUE("18F", RR, 13, 65, 0)
    GET_SIGNAL_VALUE("18F", RR, 13, 66, 1)
    GET_SIGNAL_VALUE("18F", RR, 13, 67, 2)
    GET_SIGNAL_VALUE("18F", RR, 13, 68, 3)
    GET_SIGNAL_VALUE("18F", RR, 13, 69, 4)
    PARSE_RADAR_SUFFIX(RR, 0x18F, 0)
}
void RadarHandler::PraseRadar190(uint64_t time){
    PARSE_RADAR_PREFIX("190")
    GET_SIGNAL_VALUE("190", RR, 14, 70, 0)
    GET_SIGNAL_VALUE("190", RR, 14, 71, 1)
    GET_SIGNAL_VALUE("190", RR, 14, 72, 2)
    GET_SIGNAL_VALUE("190", RR, 14, 73, 3)
    GET_SIGNAL_VALUE("190", RR, 14, 74, 4)
    PARSE_RADAR_SUFFIX(RR, 0x190, 0)
}
void RadarHandler::PraseRadar191(uint64_t time){
    PARSE_RADAR_PREFIX("191")
    GET_SIGNAL_VALUE("191", RR, 15, 75, 0)
    GET_SIGNAL_VALUE("191", RR, 15, 76, 1)
    GET_SIGNAL_VALUE("191", RR, 15, 77, 2)
    GET_SIGNAL_VALUE("191", RR, 15, 78, 3)
    GET_SIGNAL_VALUE("191", RR, 15, 79, 4)
    PARSE_RADAR_SUFFIX(RR, 0x191, 0)
}
void RadarHandler::PraseRadar192(uint64_t time){
    PARSE_RADAR_PREFIX("192")
    GET_SIGNAL_VALUE("192", RR, 16, 80, 0)
    GET_SIGNAL_VALUE("192", RR, 16, 81, 1)
    GET_SIGNAL_VALUE("192", RR, 16, 82, 2)
    GET_SIGNAL_VALUE("192", RR, 16, 83, 3)
    GET_SIGNAL_VALUE("192", RR, 16, 84, 4)
    PARSE_RADAR_SUFFIX(RR, 0x192, 0)
}
void RadarHandler::PraseRadar193(uint64_t time){
    PARSE_RADAR_PREFIX("193")
    GET_SIGNAL_VALUE("193", RR, 17, 85, 0)
    GET_SIGNAL_VALUE("193", RR, 17, 86, 1)
    GET_SIGNAL_VALUE("193", RR, 17, 87, 2)
    GET_SIGNAL_VALUE("193", RR, 17, 88, 3)
    GET_SIGNAL_VALUE("193", RR, 17, 89, 4)
    PARSE_RADAR_SUFFIX(RR, 0x193, 0)
}
void RadarHandler::PraseRadar194(uint64_t time){
    PARSE_RADAR_PREFIX("194")
    GET_SIGNAL_VALUE("194", RR, 18, 90, 0)
    GET_SIGNAL_VALUE("194", RR, 18, 91, 1)
    GET_SIGNAL_VALUE("194", RR, 18, 92, 2)
    GET_SIGNAL_VALUE("194", RR, 18, 93, 3)
    GET_SIGNAL_VALUE("194", RR, 18, 94, 4)
    PARSE_RADAR_SUFFIX(RR, 0x194, 0)
}
void RadarHandler::PraseRadar195(uint64_t time){
    PARSE_RADAR_PREFIX("195")
    GET_SIGNAL_VALUE("195", RR, 19, 95, 0)
    GET_SIGNAL_VALUE("195", RR, 19, 96, 1)
    GET_SIGNAL_VALUE("195", RR, 19, 97, 2)
    GET_SIGNAL_VALUE("195", RR, 19, 98, 3)
    GET_SIGNAL_VALUE("195", RR, 19, 99, 4)
    PARSE_RADAR_SUFFIX(RR, 0x195, 0)
}
void RadarHandler::PraseRadar196(uint64_t time){
    PARSE_RADAR_PREFIX("196")
    GET_SIGNAL_VALUE("196", RR, 20, 100, 0)
    GET_SIGNAL_VALUE("196", RR, 20, 101, 1)
    GET_SIGNAL_VALUE("196", RR, 20, 102, 2)
    GET_SIGNAL_VALUE("196", RR, 20, 103, 3)
    GET_SIGNAL_VALUE("196", RR, 20, 104, 4)
    PARSE_RADAR_SUFFIX(RR, 0x196, 0)
}
void RadarHandler::PraseRadar197(uint64_t time){
    PARSE_RADAR_PREFIX("197")
    GET_SIGNAL_VALUE("197", RR, 21, 105, 0)
    GET_SIGNAL_VALUE("197", RR, 21, 106, 1)
    GET_SIGNAL_VALUE("197", RR, 21, 107, 2)
    GET_SIGNAL_VALUE("197", RR, 21, 108, 3)
    GET_SIGNAL_VALUE("197", RR, 21, 109, 4)
    PARSE_RADAR_SUFFIX(RR, 0x197, 0)
}
void RadarHandler::PraseRadar198(uint64_t time){
    PARSE_RADAR_PREFIX("198")
    GET_SIGNAL_VALUE("198", RR, 22, 110, 0)
    GET_SIGNAL_VALUE("198", RR, 22, 111, 1)
    GET_SIGNAL_VALUE("198", RR, 22, 112, 2)
    GET_SIGNAL_VALUE("198", RR, 22, 113, 3)
    GET_SIGNAL_VALUE("198", RR, 22, 114, 4)
    PARSE_RADAR_SUFFIX(RR, 0x198, 0)
}
void RadarHandler::PraseRadar199(uint64_t time){
    PARSE_RADAR_PREFIX("199")
    GET_SIGNAL_VALUE("199", RR, 23, 115, 0)
    GET_SIGNAL_VALUE("199", RR, 23, 116, 1)
    GET_SIGNAL_VALUE("199", RR, 23, 117, 2)
    GET_SIGNAL_VALUE("199", RR, 23, 118, 3)
    GET_SIGNAL_VALUE("199", RR, 23, 119, 4)
    PARSE_RADAR_SUFFIX(RR, 0x199, 0)
}
void RadarHandler::PraseRadar19A(uint64_t time){
    PARSE_RADAR_PREFIX("19A")
    GET_SIGNAL_VALUE("19A", RR, 24, 120, 0)
    GET_SIGNAL_VALUE("19A", RR, 24, 121, 1)
    GET_SIGNAL_VALUE("19A", RR, 24, 122, 2)
    GET_SIGNAL_VALUE("19A", RR, 24, 123, 3)
    GET_SIGNAL_VALUE("19A", RR, 24, 124, 4)
    PARSE_RADAR_SUFFIX(RR, 0x19A, 0)
}
#endif

//以下是目标解析
  //CANMSG_OBJ_HEADER_FC_s_signal_array
  //CANMSG_OBJ_LIST_YY_FC_s_signal_array
  //CANMSG_OBJ_EoT_FC_s_signal_array
  void RadarHandler::ParseRadarFC341(uint64_t time){
    std::ostringstream radar_fc_341_str;
    radar_fc_341_str << ", radar_fc_341_mt="<<std::fixed<<std::setprecision(6)<< static_cast<double>(time)/1000.0;
    GetSignalValue(&CANSIG_OBJ_HEADER_FC__OBJ_TimeStamp_FC_g,
                 io_radar_info_debug_tmp_.radar_fc_measurement_time);
    radar_fc_341_str << ", radar_fc_mt="<<std::fixed<<std::setprecision(6)<< 
                                            io_radar_info_debug_tmp_.radar_fc_measurement_time;
    target_debug_info_ += radar_fc_341_str.str();
  }

  void RadarHandler::ParseRadarFC342(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    StruNewFareoRadar fareo_radar;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    fareo_radar.measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream radar_fc_342_str;
    radar_fc_342_str << ", radar_fc_342_mt="<<std::fixed<<std::setprecision(6)<< static_cast<double>(last_time)/1000.0 <<
                                        ", radar_fc_342_ts="<< time_span;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Frame_ID_YY_FC_g,
                 fareo_radar.frame_id);
    radar_fc_342_str << ", radar_fc_frame_id=" <<
                  fareo_radar.frame_id;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Object_ID_YY_FC_g,
                 fareo_radar.object_id);
    radar_fc_342_str << ", radar_fc_object_id=" <<
                  fareo_radar.object_id;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__x_m_YY_FC_g,
                 fareo_radar.position.x);
    radar_fc_342_str << ", radar_fc_position_x=" <<
                  fareo_radar.position.x;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__y_m_YY_FC_g,
                 fareo_radar.position.y);
    radar_fc_342_str << ", radar_fc_position_y=" <<
                  fareo_radar.position.y;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__a_m_s_s_YY_FC_g,
                 fareo_radar.accelerate);
    radar_fc_342_str << ", radar_fc_accelerate=" <<
                  fareo_radar.accelerate;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__v_m_s_YY_FC_g,
                 fareo_radar.velocity);
    radar_fc_342_str << ", radar_fc_velocity=" <<
                  fareo_radar.velocity;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Length_YY_FC_g,
                 fareo_radar.length);
    radar_fc_342_str << ", radar_fc_length=" <<
                  fareo_radar.length;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_Half_FCength_YY_FC_g,
                 fareo_radar.length_covariance);
    radar_fc_342_str << ", radar_fc_length_covariance=" <<
                  fareo_radar.length_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Width_YY_FC_g,
                 fareo_radar.width);
    radar_fc_342_str << ", radar_fc_width=" <<
                  fareo_radar.width;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_Half_Width_YY_FC_g,
                 fareo_radar.width_covariance);
    radar_fc_342_str << ", radar_fc_width_covariance=" <<
                  fareo_radar.width_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__TrackAge_YY_FC_g,
                 fareo_radar.track_age);
    radar_fc_342_str << ", radar_fc_track_age=" <<
                  fareo_radar.track_age;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__ExistenceProbability_YY_FC_g,
                 fareo_radar.exist_probablity);
    radar_fc_342_str << ", radar_fc_exist_probablity=" <<
                  fareo_radar.exist_probablity;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Orientation_rad_YY_FC_g,
                 fareo_radar.orientation_rad);
    radar_fc_342_str << ", radar_fc_orientation_rad=" <<
                  fareo_radar.orientation_rad;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_Orientation_YY_FC_g,
                 fareo_radar.orientation_covariance);
    radar_fc_342_str << ", radar_fc_orientation_covariance=" <<
                  fareo_radar.orientation_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__YawRate_rad_s_YY_FC_g,
                 fareo_radar.yaw_rate);
    radar_fc_342_str << ", radar_fc_yaw_rate=" <<
                  fareo_radar.yaw_rate;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_YawRate_YY_FC_g,
                 fareo_radar.yaw_rate_covariance);
    radar_fc_342_str << ", radar_fc_yaw_rate_covariance=" <<
                  fareo_radar.yaw_rate_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_x_YY_FC_g,
                 fareo_radar.covariance[0]);
    radar_fc_342_str << ", radar_fc_covariance0=" <<
                  fareo_radar.covariance[0];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_y_YY_FC_g,
                 fareo_radar.covariance[1]);
    radar_fc_342_str << ", radar_fc_covariance1=" <<
                  fareo_radar.covariance[1];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_xy_YY_FC_g,
                 fareo_radar.covariance[2]);
    radar_fc_342_str << ", radar_fc_covariance2=" <<
                  fareo_radar.covariance[2];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_a_YY_FC_g,
                 fareo_radar.covariance[3]);
    radar_fc_342_str << ", radar_fc_covariance3=" <<
                  fareo_radar.covariance[3];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FC__Cov_v_YY_FC_g,
                 fareo_radar.covariance[4]);
    radar_fc_342_str << ", radar_fc_covariance4=" <<
                  fareo_radar.covariance[4];

    io_radar_info_debug_tmp_.radar_fc.emplace(fareo_radar);
    io_radar_info_debug_tmp_.radar_fc_valid = 1;
    // target_debug_info_ += radar_fc_342_str.str();
  }

  void RadarHandler::ParseRadarFC340(uint64_t time){
    //do noting
  }

  void RadarHandler::ParseRadarFL41(uint64_t time){
    std::ostringstream radar_fl_41_str;
    radar_fl_41_str << ", radar_fl_41_mt="<<std::fixed<<std::setprecision(6)<< static_cast<double>(time)/1000.0;
    GetSignalValue(&CANSIG_OBJ_HEADER_FL__OBJ_TimeStamp_FL_g,
                 io_radar_info_debug_tmp_.radar_fl_measurement_time);
    radar_fl_41_str << ", radar_fl_mt="<<std::fixed<<std::setprecision(6)<< 
                  io_radar_info_debug_tmp_.radar_fl_measurement_time;
    target_debug_info_ += radar_fl_41_str.str();
  }

  void RadarHandler::ParseRadarFL42(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    StruNewFareoRadar fareo_radar;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    fareo_radar.measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream radar_fl_42_str;
    radar_fl_42_str << ", radar_fl_42_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                        ", radar_fl_42_ts="<<time_span;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Frame_ID_YY_FL_g,
                 fareo_radar.frame_id);
    radar_fl_42_str << ", radar_fl_frame_id=" << 
                  fareo_radar.frame_id;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Object_ID_YY_FL_g,
                 fareo_radar.object_id);
    radar_fl_42_str << ", radar_fl_object_id=" << 
                  fareo_radar.object_id;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__x_m_YY_FL_g,
                 fareo_radar.position.x);
    radar_fl_42_str << ", radar_fl_position_x=" << 
                  fareo_radar.position.x;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__y_m_YY_FL_g,
                 fareo_radar.position.y);
    radar_fl_42_str << ", radar_fl_position_y=" << 
                  fareo_radar.position.y;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__a_m_s_s_YY_FL_g,
                 fareo_radar.accelerate);
    radar_fl_42_str << ", radar_fl_accelerate=" << 
                  fareo_radar.accelerate;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__v_m_s_YY_FL_g,
                 fareo_radar.velocity);
    radar_fl_42_str << ", radar_fl_velocity=" << 
                  fareo_radar.velocity;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Length_YY_FL_g,
                 fareo_radar.length);
    radar_fl_42_str << ", radar_fl_length=" << 
                  fareo_radar.length;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_Half_Length_YY_FL_g,
                 fareo_radar.length_covariance);
    radar_fl_42_str << ", radar_fl_length_covariance=" << 
                  fareo_radar.length_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Width_YY_FL_g,
                 fareo_radar.width);
    radar_fl_42_str << ", radar_fl_width=" << 
                  fareo_radar.width;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_Half_Width_YY_FL_g,
                 fareo_radar.width_covariance);
    radar_fl_42_str << ", radar_fl_width_covariance=" << 
                  fareo_radar.width_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__TrackAge_YY_FL_g,
                 fareo_radar.track_age);
    radar_fl_42_str << ", radar_fl_track_age=" << 
                  fareo_radar.track_age;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__ExistenceProbability_YY_FL_g,
                 fareo_radar.exist_probablity);
    radar_fl_42_str << ", radar_fl_exist_probablity=" << 
                  fareo_radar.exist_probablity;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Orientation_rad_YY_FL_g,
                 fareo_radar.orientation_rad);
    radar_fl_42_str << ", radar_fl_orientation_rad=" << 
                  fareo_radar.orientation_rad;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_Orientation_YY_FL_g,
                 fareo_radar.orientation_covariance);
    radar_fl_42_str << ", radar_fl_orientation_covariance=" << 
                  fareo_radar.orientation_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__YawRate_rad_s_YY_FL_g,
                 fareo_radar.yaw_rate);
    radar_fl_42_str << ", radar_fl_yaw_rate=" << 
                  fareo_radar.yaw_rate;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_YawRate_YY_FL_g,
                 fareo_radar.yaw_rate_covariance);
    radar_fl_42_str << ", radar_fl_yaw_rate_covariance=" << 
                  fareo_radar.yaw_rate_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_x_YY_FL_g,
                 fareo_radar.covariance[0]);
    radar_fl_42_str << ", radar_fl_covariance0=" << 
                  fareo_radar.covariance[0];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_y_YY_FL_g,
                 fareo_radar.covariance[1]);
    radar_fl_42_str << ", radar_fl_covariance1=" << 
                  fareo_radar.covariance[1];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_xy_YY_FL_g,
                 fareo_radar.covariance[2]);
    radar_fl_42_str << ", radar_fl_covariance2=" << 
                  fareo_radar.covariance[2];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_a_YY_FL_g,
                 fareo_radar.covariance[3]);
    radar_fl_42_str << ", radar_fl_covariance3=" << 
                  fareo_radar.covariance[3];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FL__Cov_v_YY_FL_g,
                 fareo_radar.covariance[4]);
    radar_fl_42_str << ", radar_fl_covariance4=" << 
                  fareo_radar.covariance[4];

    io_radar_info_debug_tmp_.radar_fl.emplace(fareo_radar);
    io_radar_info_debug_tmp_.radar_fl_valid = 1;
    // target_debug_info_ += radar_fl_42_str.str();

  }

  void RadarHandler::ParseRadarFL40(uint64_t time){
    
  }

  void RadarHandler::ParseRadarFR241(uint64_t time){
    std::ostringstream radar_fr_241_str;
    radar_fr_241_str << ", radar_fr_241_mt="<<std::fixed<<std::setprecision(6)<< static_cast<double>(time)/1000.0;
    GetSignalValue(&CANSIG_OBJ_HEADER_FR__OBJ_TimeStamp_FR_g,
                 io_radar_info_debug_tmp_.radar_fr_measurement_time);
    radar_fr_241_str << ", radar_fr_mt="<<std::fixed<<std::setprecision(6)<< 
                  io_radar_info_debug_tmp_.radar_fr_measurement_time;
    target_debug_info_ += radar_fr_241_str.str();
  }

  void RadarHandler::ParseRadarFR242(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    StruNewFareoRadar fareo_radar;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    fareo_radar.measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream radar_fr_242_str;
    radar_fr_242_str << ", radar_fr_242_measurement_time="<<static_cast<double>(last_time)/1000.0<<
                                        ", radar_fr_242_time_span="<<time_span;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Frame_ID_YY_FR_g,
                 fareo_radar.frame_id);
    radar_fr_242_str << ", radar_fr_frame_id=" << 
                  fareo_radar.frame_id;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Object_ID_YY_FR_g,
                 fareo_radar.object_id);
    radar_fr_242_str << ", radar_fr_object_id=" << 
                  fareo_radar.object_id;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__x_m_YY_FR_g,
                 fareo_radar.position.x);
    radar_fr_242_str << ", radar_fr_position_x=" << 
                  fareo_radar.position.x;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__y_m_YY_FR_g,
                 fareo_radar.position.y);
    radar_fr_242_str << ", radar_fr_position_y=" << 
                  fareo_radar.position.y;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__a_m_s_s_YY_FR_g,
                 fareo_radar.accelerate);
    radar_fr_242_str << ", radar_fr_accelerate=" << 
                  fareo_radar.accelerate;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__v_m_s_YY_FR_g,
                 fareo_radar.velocity);
    radar_fr_242_str << ", radar_fr_velocity=" << 
                  fareo_radar.velocity;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Length_YY_FR_g,
                 fareo_radar.length);
    radar_fr_242_str << ", radar_fr_length=" << 
                  fareo_radar.length;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_Half_Length_YY_FR_g,
                 fareo_radar.length_covariance);
    radar_fr_242_str << ", radar_fr_length_covariance=" << 
                  fareo_radar.length_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Width_YY_FR_g,
                 fareo_radar.width);
    radar_fr_242_str << ", radar_fr_width=" << 
                  fareo_radar.width;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_Half_Width_YY_FR_g,
                 fareo_radar.width_covariance);
    radar_fr_242_str << ", radar_fr_width_covariance=" << 
                  fareo_radar.width_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__TrackAge_YY_FR_g,
                 fareo_radar.track_age);
    radar_fr_242_str << ", radar_fr_track_age=" << 
                  fareo_radar.track_age;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__ExistenceProbability_YY_FR_g,
                 fareo_radar.exist_probablity);
    radar_fr_242_str << ", radar_fr_exist_probablity=" << 
                  fareo_radar.exist_probablity;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Orientation_rad_YY_FR_g,
                 fareo_radar.orientation_rad);
    radar_fr_242_str << ", radar_fr_orientation_rad=" << 
                  fareo_radar.orientation_rad;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_Orientation_YY_FR_g,
                 fareo_radar.orientation_covariance);
    radar_fr_242_str << ", radar_fr_orientation_covariance=" << 
                  fareo_radar.orientation_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__YawRate_rad_s_YY_FR_g,
                 fareo_radar.yaw_rate);
    radar_fr_242_str << ", radar_fr_yaw_rate=" << 
                  fareo_radar.yaw_rate;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_YawRate_YY_FR_g,
                 fareo_radar.yaw_rate_covariance);
    radar_fr_242_str << ", radar_fr_yaw_rate_covariance=" << 
                  fareo_radar.yaw_rate_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_x_YY_FR_g,
                 fareo_radar.covariance[0]);
    radar_fr_242_str << ", radar_fr_covariance0=" << 
                  fareo_radar.covariance[0];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_y_YY_FR_g,
                 fareo_radar.covariance[1]);
    radar_fr_242_str << ", radar_fr_covariance1=" << 
                  fareo_radar.covariance[1];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_xy_YY_FR_g,
                 fareo_radar.covariance[2]);
    radar_fr_242_str << ", radar_fr_covariance2=" << 
                  fareo_radar.covariance[2];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_a_YY_FR_g,
                 fareo_radar.covariance[3]);
    radar_fr_242_str << ", radar_fr_covariance3=" << 
                  fareo_radar.covariance[3];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_FR__Cov_v_YY_FR_g,
                 fareo_radar.covariance[4]);
    radar_fr_242_str << ", radar_fr_covariance4=" << 
                  fareo_radar.covariance[4];

    io_radar_info_debug_tmp_.radar_fr.emplace(fareo_radar);
    io_radar_info_debug_tmp_.radar_fr_valid = 1;
    // target_debug_info_ += radar_fr_242_str.str();
  }

  void RadarHandler::ParseRadarFR240(uint64_t time){

  }

  void RadarHandler::ParseRadarRL441(uint64_t time){
    std::ostringstream radar_rl_441_str;
    radar_rl_441_str << ", radar_rl_441_mt="<<std::fixed<<std::setprecision(6)<< static_cast<double>(time)/1000.0;
    GetSignalValue(&CANSIG_OBJ_HEADER_RL__OBJ_TimeStamp_RL_g,
                 io_radar_info_debug_tmp_.radar_rl_measurement_time);
    radar_rl_441_str << ", radar_rl_mt="<<std::fixed<<std::setprecision(6)<<
                  io_radar_info_debug_tmp_.radar_rl_measurement_time;
    target_debug_info_ += radar_rl_441_str.str();
  }

  void RadarHandler::ParseRadarRL442(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    StruNewFareoRadar fareo_radar;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    fareo_radar.measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream radar_rl_442_str;
    radar_rl_442_str << ", radar_rl_442_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                        ", radar_rl_442_ts="<<time_span;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Frame_ID_YY_RL_g,
                 fareo_radar.frame_id);
    radar_rl_442_str << ", radar_rl_frame_id=" << 
                  fareo_radar.frame_id;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Object_ID_YY_RL_g,
                 fareo_radar.object_id);
    radar_rl_442_str << ", radar_rl_object_id=" << 
                  fareo_radar.object_id;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__x_m_YY_RL_g,
                 fareo_radar.position.x);
    radar_rl_442_str << ", radar_rl_position_x=" << 
                  fareo_radar.position.x;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__y_m_YY_RL_g,
                 fareo_radar.position.y);
    radar_rl_442_str << ", radar_rl_position_y=" << 
                  fareo_radar.position.y;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__a_m_s_s_YY_RL_g,
                 fareo_radar.accelerate);
    radar_rl_442_str << ", radar_rl_accelerate=" << 
                  fareo_radar.accelerate;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__v_m_s_YY_RL_g,
                 fareo_radar.velocity);
    radar_rl_442_str << ", radar_rl_velocity=" << 
                  fareo_radar.velocity;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Length_YY_RL_g,
                 fareo_radar.length);
    radar_rl_442_str << ", radar_rl_length=" << 
                  fareo_radar.length;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_Half_Length_YY_RL_g,
                 fareo_radar.length_covariance);
    radar_rl_442_str << ", radar_rl_length_covariance=" << 
                  fareo_radar.length_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Width_YY_RL_g,
                 fareo_radar.width);
    radar_rl_442_str << ", radar_rl_width=" << 
                  fareo_radar.width;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_Half_Width_YY_RL_g,
                 fareo_radar.width_covariance);
    radar_rl_442_str << ", radar_rl_width_covariance=" << 
                  fareo_radar.width_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__TrackAge_YY_RL_g,
                 fareo_radar.track_age);
    radar_rl_442_str << ", radar_rl_track_age=" << 
                  fareo_radar.track_age;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__ExistenceProbability_YY_RL_g,
                 fareo_radar.exist_probablity);
    radar_rl_442_str << ", radar_rl_exist_probablity=" << 
                  fareo_radar.exist_probablity;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Orientation_rad_YY_RL_g,
                 fareo_radar.orientation_rad);
    radar_rl_442_str << ", radar_rl_orientation_rad=" << 
                  fareo_radar.orientation_rad;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_Orientation_YY_RL_g,
                 fareo_radar.orientation_covariance);
    radar_rl_442_str << ", radar_rl_orientation_covariance=" << 
                  fareo_radar.orientation_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__YawRate_rad_s_YY_RL_g,
                 fareo_radar.yaw_rate);
    radar_rl_442_str << ", radar_rl_yaw_rate=" << 
                  fareo_radar.yaw_rate;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_YawRate_YY_RL_g,
                 fareo_radar.yaw_rate_covariance);
    radar_rl_442_str << ", radar_rl_yaw_rate_covariance=" << 
                  fareo_radar.yaw_rate_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_x_YY_RL_g,
                 fareo_radar.covariance[0]);
    radar_rl_442_str << ", radar_rl_covariance0=" << 
                  fareo_radar.covariance[0];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_y_YY_RL_g,
                 fareo_radar.covariance[1]);
    radar_rl_442_str << ", radar_rl_covariance1=" << 
                  fareo_radar.covariance[1];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_xy_YY_RL_g,
                 fareo_radar.covariance[2]);
    radar_rl_442_str << ", radar_rl_covariance2=" << 
                  fareo_radar.covariance[2];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_a_YY_RL_g,
                 fareo_radar.covariance[3]);
    radar_rl_442_str << ", radar_rl_covariance3=" << 
                  fareo_radar.covariance[3];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RL__Cov_v_YY_RL_g,
                 fareo_radar.covariance[4]);
    radar_rl_442_str << ", radar_rl_covariance4=" << 
                  fareo_radar.covariance[4];

    io_radar_info_debug_tmp_.radar_rl.emplace(fareo_radar);
    io_radar_info_debug_tmp_.radar_rl_valid = 1;
    // target_debug_info_ += radar_rl_442_str.str();
  }

  void RadarHandler::ParseRadarRL440(uint64_t time){

  }

  void RadarHandler::ParseRadarRR641(uint64_t time){
    std::ostringstream radar_rr_641_str;
    radar_rr_641_str << ", radar_rr_641_mt="<<std::fixed<<std::setprecision(6)<< static_cast<double>(time)/1000.0;
    GetSignalValue(&CANSIG_OBJ_HEADER_RR__OBJ_TimeStamp_RR_g,
                 io_radar_info_debug_tmp_.radar_rr_measurement_time);
    radar_rr_641_str << ", radar_rr_mt="<<std::fixed<<std::setprecision(6)<<
                  io_radar_info_debug_tmp_.radar_rr_measurement_time;
    target_debug_info_ += radar_rr_641_str.str();
  }

  void RadarHandler::ParseRadarRR642(uint64_t time){
    static uint64_t last_time = 0;
    static bool frist_record_time = true;
    uint64_t time_span = time - last_time;
    StruNewFareoRadar fareo_radar;
    if(true == frist_record_time){
      frist_record_time = false;
      time_span = 0.0;
    }
    last_time = time;
    fareo_radar.measurement_time = static_cast<double>(last_time)/1000.0;
    std::ostringstream radar_rr_642_str;
    radar_rr_642_str << ", radar_rr_642_mt="<<std::fixed<<std::setprecision(6)<<static_cast<double>(last_time)/1000.0<<
                                        ", radar_rr_642_ts="<<time_span;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Frame_ID_YY_RR_g,
                 fareo_radar.frame_id);
    radar_rr_642_str << ", radar_rr_frame_id=" << 
                  fareo_radar.frame_id;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Object_ID_YY_RR_g,
                 fareo_radar.object_id);
    radar_rr_642_str << ", radar_rr_object_id=" << 
                  fareo_radar.object_id;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__x_m_YY_RR_g,
                 fareo_radar.position.x);
    radar_rr_642_str << ", radar_rr_position_x=" << 
                  fareo_radar.position.x;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__y_m_YY_RR_g,
                 fareo_radar.position.y);
    radar_rr_642_str << ", radar_rr_position_y=" << 
                  fareo_radar.position.y;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__a_m_s_s_YY_RR_g,
                 fareo_radar.accelerate);
    radar_rr_642_str << ", radar_rr_accelerate=" << 
                  fareo_radar.accelerate;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__v_m_s_YY_RR_g,
                 fareo_radar.velocity);
    radar_rr_642_str << ", radar_rr_velocity=" << 
                  fareo_radar.velocity;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Length_YY_RR_g,
                 fareo_radar.length);
    radar_rr_642_str << ", radar_rr_length=" << 
                  fareo_radar.length;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_Half_Length_YY_RR_g,
                 fareo_radar.length_covariance);
    radar_rr_642_str << ", radar_rr_length_covariance=" << 
                  fareo_radar.length_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Width_YY_RR_g,
                 fareo_radar.width);
    radar_rr_642_str << ", radar_rr_width=" << 
                  fareo_radar.width;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_Half_Width_YY_RR_g,
                 fareo_radar.width_covariance);
    radar_rr_642_str << ", radar_rr_width_covariance=" << 
                  fareo_radar.width_covariance;

    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__TrackAge_YY_RR_g,
                 fareo_radar.track_age);
    radar_rr_642_str << ", radar_rr_track_age=" << 
                  fareo_radar.track_age;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__ExistenceProbability_YY_RR_g,
                 fareo_radar.exist_probablity);
    radar_rr_642_str << ", radar_rr_exist_probablity=" << 
                  fareo_radar.exist_probablity;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Orientation_rad_YY_RR_g,
                 fareo_radar.orientation_rad);
    radar_rr_642_str << ", radar_rr_orientation_rad=" << 
                  fareo_radar.orientation_rad;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_Orientation_YY_RR_g,
                 fareo_radar.orientation_covariance);
    radar_rr_642_str << ", radar_rr_orientation_covariance=" << 
                  fareo_radar.orientation_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__YawRate_rad_s_YY_RR_g,
                 fareo_radar.yaw_rate);
    radar_rr_642_str << ", radar_rr_yaw_rate=" << 
                  fareo_radar.yaw_rate;
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_YawRate_YY_RR_g,
                 fareo_radar.yaw_rate_covariance);
    radar_rr_642_str << ", radar_rr_yaw_rate_covariance=" << 
                  fareo_radar.yaw_rate_covariance;
    
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_x_YY_RR_g,
                 fareo_radar.covariance[0]);
    radar_rr_642_str << ", radar_rr_covariance0=" << 
                  fareo_radar.covariance[0];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_y_YY_RR_g,
                 fareo_radar.covariance[1]);
    radar_rr_642_str << ", radar_rr_covariance1=" << 
                  fareo_radar.covariance[1];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_xy_YY_RR_g,
                 fareo_radar.covariance[2]);
    radar_rr_642_str << ", radar_rr_covariance2=" << 
                  fareo_radar.covariance[2];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_a_YY_RR_g,
                 fareo_radar.covariance[3]);
    radar_rr_642_str << ", radar_rr_covariance3=" << 
                  fareo_radar.covariance[3];
    GetSignalValue(&CANSIG_OBJ_LIST_YY_RR__Cov_v_YY_RR_g,
                 fareo_radar.covariance[4]);
    radar_rr_642_str << ", radar_rr_covariance4=" << 
                  fareo_radar.covariance[4];

    io_radar_info_debug_tmp_.radar_rr.emplace(fareo_radar);
    io_radar_info_debug_tmp_.radar_rr_valid = 1;
    // target_debug_info_ += radar_rr_642_str.str();
  }

  void RadarHandler::ParseRadarRR640(uint64_t time){

  }

}
} 
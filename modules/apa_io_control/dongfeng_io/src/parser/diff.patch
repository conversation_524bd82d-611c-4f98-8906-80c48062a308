diff --git a/scripts/analyze/analyze_dbc.py b/scripts/analyze/analyze_dbc.py
index 80eb221..104f76c 100755
--- a/scripts/analyze/analyze_dbc.py
+++ b/scripts/analyze/analyze_dbc.py
@@ -55,10 +55,16 @@ class CAnalyzerDbc(object):
         CAnalyzerDbc.StringListUniqueProcess(l_matrix_db.sig_sendtype_names)
         CAnalyzerDbc.StringListUniqueProcess(l_matrix_db.msg_frametype_names)
         #####
-
+        # can_ids = {0xFC, 0x143, 0x12C, 0x101, 0xA2, 0x172, 0xB0, 0xA5, 0x346, 0x40, 0x119, 0x17A, 0x11D, 0x1C8, 0xFF}
+        can_ids = {0x341, 0x320, 0x33C, 0x4DA, 0x30A, 0x0A2, 0x133, 0x11D, 0x12C, 0x101, 0x172, 0x143, 0x0FC, 0x1C8, 0x0FE, 0x0FF, 0x17A, 0x0A5, 0x0B0, 0x167, 0x23A, 0x040, 0x322, 0x365, 0x4C1, 0x367, 0x4C0, 0x3A7, 0x119, 0x30C, 0x304, 0x33D, 0x3F0, 0x346, 0x710, 0x279}
+        exclude_can_names = {'SGW_I_346'}
         for l_bu in l_matrix_db.boardUnits:
             self._ecu_list.append(l_bu.name)
         for msg in l_matrix_db._fl._list:
+            if not msg.id in can_ids:
+                continue
+            if msg.name in exclude_can_names:
+                continue
             ##### filters #####
             if l_ecu_name not in msg.transmitter and l_ecu_name not in msg.receiver:
                 continue #msg不被接受
diff --git a/scripts/functions/function_can_type_def.py b/scripts/functions/function_can_type_def.py
index b0e9c4e..5774a0e 100755
--- a/scripts/functions/function_can_type_def.py
+++ b/scripts/functions/function_can_type_def.py
@@ -3,7 +3,7 @@
 
 import sys, os, re, io
 import cog, glob, copy, string, operator
-import formatter
+# import formatter
 
 from CogConfig          import *
 from env.env_param      import CEnvParam
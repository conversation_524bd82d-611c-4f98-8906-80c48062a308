#include "parser_typedef.h"
#pragma once

#if 0
//--------------//
extern void CAN_SetCanParserLockHandler(fpParserLockHandler_t func);
extern void CAN_SetCanParserUnlockHandler(fpParserUnlockHandler_t func);
extern void CAN_SetMsgCounterCalcHandler(fpMsgCounterCalcHandler_t func);
extern void CAN_SetMsgChecksumCalcHandler(fpMsgChecksumCalcHandler_t func);
extern void CAN_SetMsgChecksumVerityHandler(fpMsgChecksumVerityHandler_t func);
extern void CAN_SetMsgChangeHandler(fpMsgChangeHandler_t func);
extern void CAN_SetMsgChecksumErrorHandler(fpMsgChecksumErrorHandler_t func);
extern void CAN_SetMsgCounterErrorHandler(fpMsgCounterErrorHandler_t func);
extern void CAN_SetMsgTimeoutHandler(fpMsgTimeoutHandler_t func);
extern void CAN_SetMsgDlcHandler(fpMsgDlcHandler_t func);
extern void CAN_SetMsgOutRangeHandler(fpMsgOutRangeHandler_t func);
extern void CAN_SetSigChangeHandler(fpSigChangeHandler_t func);
extern void CAN_SetSigOnWriteHandler(fpSigOnWriteHandler_t func);
extern void CAN_SetSignalChangedHook(fpSignalChangedHook_t func);
extern void CAN_SetSignalSetCallBack(fpSignalTriggerCallBack_t func);
extern void CAN_SetSignalGetCallBack(fpSignalTriggerCallBack_t func);
//=====================================================================================//
extern struct veh_message *CAN_RxMessageList[27];

extern struct veh_message *CAN_TxMessageList[1];

extern struct veh_signal* CAN_ALL_Signal_Array[];
#endif
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Beam_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Beam_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Beam_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Beam_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Beam_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_0_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_1_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_2_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_3_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_4_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Beam_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Beam_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Beam_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Beam_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Beam_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_50_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_51_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_52_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_53_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_54_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Beam_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Beam_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Beam_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Beam_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Beam_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_55_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_56_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_57_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_58_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_59_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Beam_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Beam_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Beam_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Beam_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Beam_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_60_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_61_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_62_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_63_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_64_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Beam_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Beam_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Beam_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Beam_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Beam_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_65_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_66_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_67_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_68_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_69_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Beam_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Beam_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Beam_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Beam_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Beam_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_70_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_71_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_72_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_73_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_74_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Beam_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Beam_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Beam_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Beam_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Beam_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_75_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_76_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_77_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_78_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_79_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Beam_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Beam_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Beam_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Beam_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Beam_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_80_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_81_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_82_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_83_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_84_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Beam_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Beam_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Beam_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Beam_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Beam_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_85_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_86_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_87_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_88_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_89_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Beam_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Beam_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Beam_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Beam_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Beam_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_90_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_91_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_92_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_93_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_94_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Beam_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Beam_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Beam_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Beam_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Beam_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_95_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_96_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_97_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_98_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_99_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Beam_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Beam_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Beam_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Beam_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Beam_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_5_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_6_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_7_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_8_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_9_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Beam_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Beam_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Beam_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Beam_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Beam_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_100_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_101_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_102_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_103_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_104_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Beam_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Beam_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Beam_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Beam_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Beam_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_105_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_106_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_107_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_108_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_109_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Beam_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Beam_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Beam_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Beam_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Beam_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_110_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_111_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_112_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_113_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_114_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Beam_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Beam_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Beam_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Beam_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Beam_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_115_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_116_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_117_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_118_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_119_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Beam_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Beam_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Beam_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Beam_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Beam_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_120_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_121_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_122_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_123_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_124_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Beam_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Beam_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Beam_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Beam_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Beam_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_10_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_11_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_12_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_13_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_14_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Beam_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Beam_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Beam_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Beam_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Beam_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_15_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_16_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_17_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_18_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_19_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Beam_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Beam_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Beam_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Beam_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Beam_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_20_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_21_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_22_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_23_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_24_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Beam_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Beam_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Beam_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Beam_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Beam_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_25_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_26_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_27_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_28_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_29_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Beam_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Beam_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Beam_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Beam_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Beam_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_30_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_31_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_32_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_33_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_34_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Beam_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Beam_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Beam_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Beam_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Beam_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_35_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_36_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_37_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_38_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_39_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Beam_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Beam_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Beam_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Beam_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Beam_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_40_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_41_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_42_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_43_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_44_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Beam_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Beam_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Beam_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Beam_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Beam_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_45_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_46_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_47_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_48_FR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_49_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__BW100KHz1_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__BW100KHz2_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__BW100KHz3_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__CycleNumber_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__DetectionListVersion_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__FC1MHz1_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__FC1MHz2_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__FC1MHz3_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__HostAccelLatEst_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__HostAccelLongEst_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__HostVelEst_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__HostYawEst_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__InterferenceExceeded_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__TimeStamp_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__UnambiguousVelMeas1_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__UnambiguousVelMeas2_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__UnambiguousVelMeas3_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__numCmplxValPerDetectionBeam1_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__numCmplxValPerDetectionBeam2_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__numCmplxValPerDetectionBeam3_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__numFreespaceDetections_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__numInfrastructureDetected_FR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FR__numNonInfraDetected_FR_g;

extern void CAN_SetRawMessage_MK_TARGET_DETECTION_HEADER_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_0_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_1_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_2_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_3_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_4_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_5_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_6_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_7_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_8_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_9_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_10_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_11_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_12_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_13_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_14_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_15_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_16_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_17_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_18_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_19_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_20_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_21_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_22_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_23_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_24_FR(uint8_t *values, uint32_t length);

extern void CAN_ResetMessage_MK_TARGET_DETECTION_HEADER_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_0_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_1_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_2_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_3_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_4_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_5_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_6_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_7_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_8_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_9_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_10_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_11_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_12_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_13_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_14_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_15_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_16_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_17_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_18_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_19_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_20_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_21_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_22_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_23_FR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_24_FR(enum reset_flg flags);

#if 0
extern void CAN_MessageElapseTime(int bus_id, int time_ms, int restart);
#endif


extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_0_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_10_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_11_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_12_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_13_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_14_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_15_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_16_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_17_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_18_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_19_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_1_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_20_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_21_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_22_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_23_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_24_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_2_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_3_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_4_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_5_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_6_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_7_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_8_FR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_9_FR(void);
extern void CAN_Message_RDLock_MK_TARGET_DETECTION_HEADER_FR(void);

extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_0_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_10_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_11_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_12_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_13_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_14_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_15_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_16_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_17_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_18_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_19_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_1_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_20_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_21_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_22_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_23_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_24_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_2_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_3_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_4_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_5_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_6_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_7_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_8_FR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_9_FR(void);
extern void CAN_Message_WRLock_MK_TARGET_DETECTION_HEADER_FR(void);

extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_0_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_10_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_11_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_12_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_13_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_14_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_15_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_16_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_17_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_18_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_19_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_1_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_20_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_21_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_22_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_23_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_24_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_2_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_3_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_4_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_5_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_6_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_7_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_8_FR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_9_FR(void);
extern void CAN_Message_Unlock_MK_TARGET_DETECTION_HEADER_FR(void);



extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Azimuth_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Beam_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Beam_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Beam_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Beam_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Beam_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGDoppler_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__CoGRange_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__NoInfrastructure_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__PowerDB_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__Reserve1_1bit_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__SNRdB_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__StdAzimuth_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__ValidXBeam_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WDoppler10DB_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_0_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FR__WRange10DB_NI_4_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Azimuth_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Beam_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Beam_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Beam_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Beam_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Beam_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGDoppler_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__CoGRange_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__NoInfrastructure_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__PowerDB_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__Reserve1_1bit_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__SNRdB_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__StdAzimuth_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__ValidXBeam_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WDoppler10DB_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_50_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_51_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_52_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_53_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FR__WRange10DB_NI_54_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Azimuth_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Beam_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Beam_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Beam_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Beam_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Beam_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGDoppler_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__CoGRange_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__NoInfrastructure_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__PowerDB_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__Reserve1_1bit_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__SNRdB_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__StdAzimuth_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__ValidXBeam_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WDoppler10DB_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_55_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_56_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_57_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_58_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FR__WRange10DB_NI_59_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Azimuth_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Beam_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Beam_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Beam_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Beam_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Beam_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGDoppler_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__CoGRange_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__NoInfrastructure_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__PowerDB_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__Reserve1_1bit_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__SNRdB_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__StdAzimuth_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__ValidXBeam_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WDoppler10DB_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_60_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_61_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_62_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_63_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FR__WRange10DB_NI_64_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Azimuth_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Beam_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Beam_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Beam_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Beam_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Beam_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGDoppler_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__CoGRange_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__NoInfrastructure_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__PowerDB_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__Reserve1_1bit_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__SNRdB_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__StdAzimuth_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__ValidXBeam_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WDoppler10DB_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_65_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_66_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_67_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_68_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FR__WRange10DB_NI_69_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Azimuth_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Beam_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Beam_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Beam_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Beam_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Beam_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGDoppler_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__CoGRange_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__NoInfrastructure_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__PowerDB_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__Reserve1_1bit_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__SNRdB_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__StdAzimuth_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__ValidXBeam_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WDoppler10DB_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_70_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_71_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_72_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_73_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FR__WRange10DB_NI_74_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Azimuth_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Beam_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Beam_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Beam_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Beam_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Beam_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGDoppler_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__CoGRange_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__NoInfrastructure_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__PowerDB_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__Reserve1_1bit_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__SNRdB_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__StdAzimuth_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__ValidXBeam_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WDoppler10DB_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_75_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_76_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_77_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_78_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FR__WRange10DB_NI_79_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Azimuth_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Beam_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Beam_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Beam_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Beam_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Beam_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGDoppler_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__CoGRange_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__NoInfrastructure_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__PowerDB_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__Reserve1_1bit_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__SNRdB_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__StdAzimuth_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__ValidXBeam_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WDoppler10DB_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_80_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_81_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_82_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_83_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FR__WRange10DB_NI_84_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Azimuth_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Beam_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Beam_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Beam_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Beam_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Beam_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGDoppler_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__CoGRange_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__NoInfrastructure_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__PowerDB_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__Reserve1_1bit_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__SNRdB_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__StdAzimuth_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__ValidXBeam_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WDoppler10DB_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_85_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_86_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_87_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_88_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FR__WRange10DB_NI_89_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Azimuth_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Beam_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Beam_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Beam_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Beam_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Beam_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGDoppler_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__CoGRange_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__NoInfrastructure_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__PowerDB_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__Reserve1_1bit_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__SNRdB_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__StdAzimuth_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__ValidXBeam_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WDoppler10DB_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_90_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_91_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_92_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_93_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FR__WRange10DB_NI_94_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Azimuth_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Beam_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Beam_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Beam_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Beam_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Beam_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGDoppler_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__CoGRange_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__NoInfrastructure_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__PowerDB_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__Reserve1_1bit_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__SNRdB_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__StdAzimuth_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__ValidXBeam_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WDoppler10DB_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_95_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_96_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_97_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_98_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FR__WRange10DB_NI_99_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Azimuth_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Beam_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Beam_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Beam_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Beam_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Beam_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGDoppler_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__CoGRange_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__NoInfrastructure_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__PowerDB_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__Reserve1_1bit_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__SNRdB_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__StdAzimuth_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__ValidXBeam_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WDoppler10DB_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_5_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_6_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_7_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_8_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FR__WRange10DB_NI_9_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Azimuth_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Beam_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Beam_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Beam_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Beam_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Beam_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGDoppler_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__CoGRange_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__NoInfrastructure_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__PowerDB_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__Reserve1_1bit_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__SNRdB_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__StdAzimuth_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__ValidXBeam_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WDoppler10DB_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_100_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_101_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_102_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_103_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FR__WRange10DB_NI_104_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Azimuth_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Beam_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Beam_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Beam_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Beam_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Beam_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGDoppler_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__CoGRange_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__NoInfrastructure_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__PowerDB_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__Reserve1_1bit_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__SNRdB_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__StdAzimuth_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__ValidXBeam_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WDoppler10DB_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_105_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_106_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_107_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_108_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FR__WRange10DB_NI_109_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Azimuth_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Beam_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Beam_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Beam_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Beam_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Beam_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGDoppler_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__CoGRange_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__NoInfrastructure_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__PowerDB_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__Reserve1_1bit_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__SNRdB_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__StdAzimuth_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__ValidXBeam_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WDoppler10DB_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_110_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_111_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_112_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_113_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FR__WRange10DB_NI_114_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Azimuth_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Beam_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Beam_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Beam_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Beam_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Beam_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGDoppler_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__CoGRange_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__NoInfrastructure_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__PowerDB_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__Reserve1_1bit_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__SNRdB_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__StdAzimuth_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__ValidXBeam_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WDoppler10DB_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_115_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_116_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_117_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_118_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FR__WRange10DB_NI_119_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Azimuth_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Beam_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Beam_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Beam_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Beam_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Beam_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGDoppler_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__CoGRange_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__NoInfrastructure_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__PowerDB_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__Reserve1_1bit_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__SNRdB_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__StdAzimuth_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__ValidXBeam_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WDoppler10DB_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_120_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_121_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_122_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_123_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FR__WRange10DB_NI_124_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Azimuth_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Beam_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Beam_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Beam_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Beam_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Beam_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGDoppler_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__CoGRange_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__NoInfrastructure_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__PowerDB_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__Reserve1_1bit_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__SNRdB_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__StdAzimuth_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__ValidXBeam_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WDoppler10DB_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_10_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_11_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_12_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_13_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FR__WRange10DB_NI_14_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Azimuth_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Beam_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Beam_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Beam_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Beam_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Beam_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGDoppler_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__CoGRange_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__NoInfrastructure_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__PowerDB_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__Reserve1_1bit_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__SNRdB_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__StdAzimuth_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__ValidXBeam_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WDoppler10DB_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_15_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_16_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_17_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_18_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FR__WRange10DB_NI_19_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Azimuth_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Beam_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Beam_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Beam_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Beam_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Beam_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGDoppler_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__CoGRange_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__NoInfrastructure_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__PowerDB_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__Reserve1_1bit_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__SNRdB_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__StdAzimuth_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__ValidXBeam_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WDoppler10DB_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_20_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_21_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_22_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_23_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FR__WRange10DB_NI_24_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Azimuth_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Beam_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Beam_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Beam_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Beam_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Beam_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGDoppler_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__CoGRange_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__NoInfrastructure_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__PowerDB_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__Reserve1_1bit_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__SNRdB_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__StdAzimuth_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__ValidXBeam_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WDoppler10DB_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_25_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_26_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_27_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_28_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FR__WRange10DB_NI_29_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Azimuth_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Beam_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Beam_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Beam_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Beam_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Beam_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGDoppler_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__CoGRange_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__NoInfrastructure_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__PowerDB_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__Reserve1_1bit_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__SNRdB_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__StdAzimuth_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__ValidXBeam_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WDoppler10DB_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_30_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_31_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_32_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_33_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FR__WRange10DB_NI_34_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Azimuth_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Beam_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Beam_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Beam_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Beam_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Beam_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGDoppler_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__CoGRange_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__NoInfrastructure_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__PowerDB_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__Reserve1_1bit_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__SNRdB_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__StdAzimuth_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__ValidXBeam_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WDoppler10DB_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_35_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_36_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_37_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_38_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FR__WRange10DB_NI_39_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Azimuth_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Beam_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Beam_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Beam_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Beam_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Beam_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGDoppler_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__CoGRange_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__NoInfrastructure_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__PowerDB_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__Reserve1_1bit_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__SNRdB_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__StdAzimuth_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__ValidXBeam_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WDoppler10DB_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_40_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_41_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_42_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_43_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FR__WRange10DB_NI_44_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Azimuth_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Beam_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Beam_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Beam_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Beam_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Beam_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGDoppler_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__CoGRange_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__NoInfrastructure_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__PowerDB_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__Reserve1_1bit_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__SNRdB_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__StdAzimuth_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__ValidXBeam_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WDoppler10DB_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_45_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_46_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_47_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_48_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FR__WRange10DB_NI_49_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__BW100KHz1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__BW100KHz2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__BW100KHz3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__CycleNumber_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__DetectionListVersion_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__FC1MHz1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__FC1MHz2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__FC1MHz3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__HostAccelLatEst_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__HostAccelLongEst_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__HostVelEst_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__HostYawEst_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__InterferenceExceeded_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__TimeStamp_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__UnambiguousVelMeas1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__UnambiguousVelMeas2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__UnambiguousVelMeas3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__numCmplxValPerDetectionBeam1_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__numCmplxValPerDetectionBeam2_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__numCmplxValPerDetectionBeam3_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__numFreespaceDetections_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__numInfrastructureDetected_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FR__numNonInfraDetected_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);

extern void MK_FR_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
#if 0
extern void CAN_PARSER_Init(void);
extern void CAN_PARSER_MSG_Init(struct veh_message *p_message_list[]);
#endif

//=====================================================================================//



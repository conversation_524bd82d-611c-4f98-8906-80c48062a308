#include "parser_typedef.h"
#pragma once
//=====================================================================================//
#define CANMSG_MK_NON_INF_DETECTION_0_FR_ID        (0x0282)
#define CANMSG_MK_NON_INF_DETECTION_0_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_0_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_0_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_4_FR : 1;
        uint64_t NoInfrastructure_NI_4_FR : 1;
        uint64_t Beam_NI_4_FR : 2;
        uint64_t Azimuth_NI_4_FR : 12;
        uint64_t StdAzimuth_NI_4_FR : 8;
        uint64_t SNRdB_NI_4_FR : 8;
        uint64_t PowerDB_NI_4_FR : 8;
        uint64_t WDoppler10DB_NI_4_FR : 7;
        uint64_t Reserve1_1bit_NI_4_FR : 1;
        uint64_t WRange10DB_NI_4_FR : 8;
        uint64_t CoGDoppler_NI_4_FR : 16;
        uint64_t CoGRange_NI_4_FR : 16;
        uint64_t ValidXBeam_NI_3_FR : 1;
        uint64_t NoInfrastructure_NI_3_FR : 1;
        uint64_t Beam_NI_3_FR : 2;
        uint64_t Azimuth_NI_3_FR : 12;
        uint64_t StdAzimuth_NI_3_FR : 8;
        uint64_t SNRdB_NI_3_FR : 8;
        uint64_t PowerDB_NI_3_FR : 8;
        uint64_t WDoppler10DB_NI_3_FR : 7;
        uint64_t Reserve1_1bit_NI_3_FR : 1;
        uint64_t WRange10DB_NI_3_FR : 8;
        uint64_t CoGDoppler_NI_3_FR : 16;
        uint64_t CoGRange_NI_3_FR : 16;
        uint64_t ValidXBeam_NI_2_FR : 1;
        uint64_t NoInfrastructure_NI_2_FR : 1;
        uint64_t Beam_NI_2_FR : 2;
        uint64_t Azimuth_NI_2_FR__S1 : 4;
        uint64_t Azimuth_NI_2_FR__S0 : 8;
        uint64_t StdAzimuth_NI_2_FR : 8;
        uint64_t SNRdB_NI_2_FR : 8;
        uint64_t PowerDB_NI_2_FR : 8;
        uint64_t WDoppler10DB_NI_2_FR : 7;
        uint64_t Reserve1_1bit_NI_2_FR : 1;
        uint64_t WRange10DB_NI_2_FR : 8;
        uint64_t CoGDoppler_NI_2_FR : 16;
        uint64_t CoGRange_NI_2_FR : 16;
        uint64_t ValidXBeam_NI_1_FR : 1;
        uint64_t NoInfrastructure_NI_1_FR : 1;
        uint64_t Beam_NI_1_FR : 2;
        uint64_t Azimuth_NI_1_FR : 12;
        uint64_t StdAzimuth_NI_1_FR : 8;
        uint64_t SNRdB_NI_1_FR : 8;
        uint64_t PowerDB_NI_1_FR : 8;
        uint64_t WDoppler10DB_NI_1_FR : 7;
        uint64_t Reserve1_1bit_NI_1_FR : 1;
        uint64_t WRange10DB_NI_1_FR : 8;
        uint64_t CoGDoppler_NI_1_FR : 16;
        uint64_t CoGRange_NI_1_FR : 16;
        uint64_t ValidXBeam_NI_0_FR : 1;
        uint64_t NoInfrastructure_NI_0_FR : 1;
        uint64_t Beam_NI_0_FR : 2;
        uint64_t Azimuth_NI_0_FR : 12;
        uint64_t StdAzimuth_NI_0_FR : 8;
        uint64_t SNRdB_NI_0_FR : 8;
        uint64_t PowerDB_NI_0_FR : 8;
        uint64_t WDoppler10DB_NI_0_FR : 7;
        uint64_t Reserve1_1bit_NI_0_FR : 1;
        uint64_t WRange10DB_NI_0_FR : 8;
        uint64_t CoGDoppler_NI_0_FR : 16;
        uint64_t CoGRange_NI_0_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_0_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_0_fr_data_t data;
};

union cansig_mk_non_inf_detection_0_fr__azimuth_ni_2_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_2_FR__S1 : 4;
        uint32_t Azimuth_NI_2_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_10_FR_ID       (0x028C)
#define CANMSG_MK_NON_INF_DETECTION_10_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_10_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_10_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_54_FR : 1;
        uint64_t NoInfrastructure_NI_54_FR : 1;
        uint64_t Beam_NI_54_FR : 2;
        uint64_t Azimuth_NI_54_FR : 12;
        uint64_t StdAzimuth_NI_54_FR : 8;
        uint64_t SNRdB_NI_54_FR : 8;
        uint64_t PowerDB_NI_54_FR : 8;
        uint64_t WDoppler10DB_NI_54_FR : 7;
        uint64_t Reserve1_1bit_NI_54_FR : 1;
        uint64_t WRange10DB_NI_54_FR : 8;
        uint64_t CoGDoppler_NI_54_FR : 16;
        uint64_t CoGRange_NI_54_FR : 16;
        uint64_t ValidXBeam_NI_53_FR : 1;
        uint64_t NoInfrastructure_NI_53_FR : 1;
        uint64_t Beam_NI_53_FR : 2;
        uint64_t Azimuth_NI_53_FR : 12;
        uint64_t StdAzimuth_NI_53_FR : 8;
        uint64_t SNRdB_NI_53_FR : 8;
        uint64_t PowerDB_NI_53_FR : 8;
        uint64_t WDoppler10DB_NI_53_FR : 7;
        uint64_t Reserve1_1bit_NI_53_FR : 1;
        uint64_t WRange10DB_NI_53_FR : 8;
        uint64_t CoGDoppler_NI_53_FR : 16;
        uint64_t CoGRange_NI_53_FR : 16;
        uint64_t ValidXBeam_NI_52_FR : 1;
        uint64_t NoInfrastructure_NI_52_FR : 1;
        uint64_t Beam_NI_52_FR : 2;
        uint64_t Azimuth_NI_52_FR__S1 : 4;
        uint64_t Azimuth_NI_52_FR__S0 : 8;
        uint64_t StdAzimuth_NI_52_FR : 8;
        uint64_t SNRdB_NI_52_FR : 8;
        uint64_t PowerDB_NI_52_FR : 8;
        uint64_t WDoppler10DB_NI_52_FR : 7;
        uint64_t Reserve1_1bit_NI_52_FR : 1;
        uint64_t WRange10DB_NI_52_FR : 8;
        uint64_t CoGDoppler_NI_52_FR : 16;
        uint64_t CoGRange_NI_52_FR : 16;
        uint64_t ValidXBeam_NI_51_FR : 1;
        uint64_t NoInfrastructure_NI_51_FR : 1;
        uint64_t Beam_NI_51_FR : 2;
        uint64_t Azimuth_NI_51_FR : 12;
        uint64_t StdAzimuth_NI_51_FR : 8;
        uint64_t SNRdB_NI_51_FR : 8;
        uint64_t PowerDB_NI_51_FR : 8;
        uint64_t WDoppler10DB_NI_51_FR : 7;
        uint64_t Reserve1_1bit_NI_51_FR : 1;
        uint64_t WRange10DB_NI_51_FR : 8;
        uint64_t CoGDoppler_NI_51_FR : 16;
        uint64_t CoGRange_NI_51_FR : 16;
        uint64_t ValidXBeam_NI_50_FR : 1;
        uint64_t NoInfrastructure_NI_50_FR : 1;
        uint64_t Beam_NI_50_FR : 2;
        uint64_t Azimuth_NI_50_FR : 12;
        uint64_t StdAzimuth_NI_50_FR : 8;
        uint64_t SNRdB_NI_50_FR : 8;
        uint64_t PowerDB_NI_50_FR : 8;
        uint64_t WDoppler10DB_NI_50_FR : 7;
        uint64_t Reserve1_1bit_NI_50_FR : 1;
        uint64_t WRange10DB_NI_50_FR : 8;
        uint64_t CoGDoppler_NI_50_FR : 16;
        uint64_t CoGRange_NI_50_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_10_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_10_fr_data_t data;
};

union cansig_mk_non_inf_detection_10_fr__azimuth_ni_52_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_52_FR__S1 : 4;
        uint32_t Azimuth_NI_52_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_11_FR_ID       (0x028D)
#define CANMSG_MK_NON_INF_DETECTION_11_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_11_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_11_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_59_FR : 1;
        uint64_t NoInfrastructure_NI_59_FR : 1;
        uint64_t Beam_NI_59_FR : 2;
        uint64_t Azimuth_NI_59_FR : 12;
        uint64_t StdAzimuth_NI_59_FR : 8;
        uint64_t SNRdB_NI_59_FR : 8;
        uint64_t PowerDB_NI_59_FR : 8;
        uint64_t WDoppler10DB_NI_59_FR : 7;
        uint64_t Reserve1_1bit_NI_59_FR : 1;
        uint64_t WRange10DB_NI_59_FR : 8;
        uint64_t CoGDoppler_NI_59_FR : 16;
        uint64_t CoGRange_NI_59_FR : 16;
        uint64_t ValidXBeam_NI_58_FR : 1;
        uint64_t NoInfrastructure_NI_58_FR : 1;
        uint64_t Beam_NI_58_FR : 2;
        uint64_t Azimuth_NI_58_FR : 12;
        uint64_t StdAzimuth_NI_58_FR : 8;
        uint64_t SNRdB_NI_58_FR : 8;
        uint64_t PowerDB_NI_58_FR : 8;
        uint64_t WDoppler10DB_NI_58_FR : 7;
        uint64_t Reserve1_1bit_NI_58_FR : 1;
        uint64_t WRange10DB_NI_58_FR : 8;
        uint64_t CoGDoppler_NI_58_FR : 16;
        uint64_t CoGRange_NI_58_FR : 16;
        uint64_t ValidXBeam_NI_57_FR : 1;
        uint64_t NoInfrastructure_NI_57_FR : 1;
        uint64_t Beam_NI_57_FR : 2;
        uint64_t Azimuth_NI_57_FR__S1 : 4;
        uint64_t Azimuth_NI_57_FR__S0 : 8;
        uint64_t StdAzimuth_NI_57_FR : 8;
        uint64_t SNRdB_NI_57_FR : 8;
        uint64_t PowerDB_NI_57_FR : 8;
        uint64_t WDoppler10DB_NI_57_FR : 7;
        uint64_t Reserve1_1bit_NI_57_FR : 1;
        uint64_t WRange10DB_NI_57_FR : 8;
        uint64_t CoGDoppler_NI_57_FR : 16;
        uint64_t CoGRange_NI_57_FR : 16;
        uint64_t ValidXBeam_NI_56_FR : 1;
        uint64_t NoInfrastructure_NI_56_FR : 1;
        uint64_t Beam_NI_56_FR : 2;
        uint64_t Azimuth_NI_56_FR : 12;
        uint64_t StdAzimuth_NI_56_FR : 8;
        uint64_t SNRdB_NI_56_FR : 8;
        uint64_t PowerDB_NI_56_FR : 8;
        uint64_t WDoppler10DB_NI_56_FR : 7;
        uint64_t Reserve1_1bit_NI_56_FR : 1;
        uint64_t WRange10DB_NI_56_FR : 8;
        uint64_t CoGDoppler_NI_56_FR : 16;
        uint64_t CoGRange_NI_56_FR : 16;
        uint64_t ValidXBeam_NI_55_FR : 1;
        uint64_t NoInfrastructure_NI_55_FR : 1;
        uint64_t Beam_NI_55_FR : 2;
        uint64_t Azimuth_NI_55_FR : 12;
        uint64_t StdAzimuth_NI_55_FR : 8;
        uint64_t SNRdB_NI_55_FR : 8;
        uint64_t PowerDB_NI_55_FR : 8;
        uint64_t WDoppler10DB_NI_55_FR : 7;
        uint64_t Reserve1_1bit_NI_55_FR : 1;
        uint64_t WRange10DB_NI_55_FR : 8;
        uint64_t CoGDoppler_NI_55_FR : 16;
        uint64_t CoGRange_NI_55_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_11_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_11_fr_data_t data;
};

union cansig_mk_non_inf_detection_11_fr__azimuth_ni_57_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_57_FR__S1 : 4;
        uint32_t Azimuth_NI_57_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_12_FR_ID       (0x028E)
#define CANMSG_MK_NON_INF_DETECTION_12_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_12_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_12_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_64_FR : 1;
        uint64_t NoInfrastructure_NI_64_FR : 1;
        uint64_t Beam_NI_64_FR : 2;
        uint64_t Azimuth_NI_64_FR : 12;
        uint64_t StdAzimuth_NI_64_FR : 8;
        uint64_t SNRdB_NI_64_FR : 8;
        uint64_t PowerDB_NI_64_FR : 8;
        uint64_t WDoppler10DB_NI_64_FR : 7;
        uint64_t Reserve1_1bit_NI_64_FR : 1;
        uint64_t WRange10DB_NI_64_FR : 8;
        uint64_t CoGDoppler_NI_64_FR : 16;
        uint64_t CoGRange_NI_64_FR : 16;
        uint64_t ValidXBeam_NI_63_FR : 1;
        uint64_t NoInfrastructure_NI_63_FR : 1;
        uint64_t Beam_NI_63_FR : 2;
        uint64_t Azimuth_NI_63_FR : 12;
        uint64_t StdAzimuth_NI_63_FR : 8;
        uint64_t SNRdB_NI_63_FR : 8;
        uint64_t PowerDB_NI_63_FR : 8;
        uint64_t WDoppler10DB_NI_63_FR : 7;
        uint64_t Reserve1_1bit_NI_63_FR : 1;
        uint64_t WRange10DB_NI_63_FR : 8;
        uint64_t CoGDoppler_NI_63_FR : 16;
        uint64_t CoGRange_NI_63_FR : 16;
        uint64_t ValidXBeam_NI_62_FR : 1;
        uint64_t NoInfrastructure_NI_62_FR : 1;
        uint64_t Beam_NI_62_FR : 2;
        uint64_t Azimuth_NI_62_FR__S1 : 4;
        uint64_t Azimuth_NI_62_FR__S0 : 8;
        uint64_t StdAzimuth_NI_62_FR : 8;
        uint64_t SNRdB_NI_62_FR : 8;
        uint64_t PowerDB_NI_62_FR : 8;
        uint64_t WDoppler10DB_NI_62_FR : 7;
        uint64_t Reserve1_1bit_NI_62_FR : 1;
        uint64_t WRange10DB_NI_62_FR : 8;
        uint64_t CoGDoppler_NI_62_FR : 16;
        uint64_t CoGRange_NI_62_FR : 16;
        uint64_t ValidXBeam_NI_61_FR : 1;
        uint64_t NoInfrastructure_NI_61_FR : 1;
        uint64_t Beam_NI_61_FR : 2;
        uint64_t Azimuth_NI_61_FR : 12;
        uint64_t StdAzimuth_NI_61_FR : 8;
        uint64_t SNRdB_NI_61_FR : 8;
        uint64_t PowerDB_NI_61_FR : 8;
        uint64_t WDoppler10DB_NI_61_FR : 7;
        uint64_t Reserve1_1bit_NI_61_FR : 1;
        uint64_t WRange10DB_NI_61_FR : 8;
        uint64_t CoGDoppler_NI_61_FR : 16;
        uint64_t CoGRange_NI_61_FR : 16;
        uint64_t ValidXBeam_NI_60_FR : 1;
        uint64_t NoInfrastructure_NI_60_FR : 1;
        uint64_t Beam_NI_60_FR : 2;
        uint64_t Azimuth_NI_60_FR : 12;
        uint64_t StdAzimuth_NI_60_FR : 8;
        uint64_t SNRdB_NI_60_FR : 8;
        uint64_t PowerDB_NI_60_FR : 8;
        uint64_t WDoppler10DB_NI_60_FR : 7;
        uint64_t Reserve1_1bit_NI_60_FR : 1;
        uint64_t WRange10DB_NI_60_FR : 8;
        uint64_t CoGDoppler_NI_60_FR : 16;
        uint64_t CoGRange_NI_60_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_12_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_12_fr_data_t data;
};

union cansig_mk_non_inf_detection_12_fr__azimuth_ni_62_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_62_FR__S1 : 4;
        uint32_t Azimuth_NI_62_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_13_FR_ID       (0x028F)
#define CANMSG_MK_NON_INF_DETECTION_13_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_13_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_13_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_69_FR : 1;
        uint64_t NoInfrastructure_NI_69_FR : 1;
        uint64_t Beam_NI_69_FR : 2;
        uint64_t Azimuth_NI_69_FR : 12;
        uint64_t StdAzimuth_NI_69_FR : 8;
        uint64_t SNRdB_NI_69_FR : 8;
        uint64_t PowerDB_NI_69_FR : 8;
        uint64_t WDoppler10DB_NI_69_FR : 7;
        uint64_t Reserve1_1bit_NI_69_FR : 1;
        uint64_t WRange10DB_NI_69_FR : 8;
        uint64_t CoGDoppler_NI_69_FR : 16;
        uint64_t CoGRange_NI_69_FR : 16;
        uint64_t ValidXBeam_NI_68_FR : 1;
        uint64_t NoInfrastructure_NI_68_FR : 1;
        uint64_t Beam_NI_68_FR : 2;
        uint64_t Azimuth_NI_68_FR : 12;
        uint64_t StdAzimuth_NI_68_FR : 8;
        uint64_t SNRdB_NI_68_FR : 8;
        uint64_t PowerDB_NI_68_FR : 8;
        uint64_t WDoppler10DB_NI_68_FR : 7;
        uint64_t Reserve1_1bit_NI_68_FR : 1;
        uint64_t WRange10DB_NI_68_FR : 8;
        uint64_t CoGDoppler_NI_68_FR : 16;
        uint64_t CoGRange_NI_68_FR : 16;
        uint64_t ValidXBeam_NI_67_FR : 1;
        uint64_t NoInfrastructure_NI_67_FR : 1;
        uint64_t Beam_NI_67_FR : 2;
        uint64_t Azimuth_NI_67_FR__S1 : 4;
        uint64_t Azimuth_NI_67_FR__S0 : 8;
        uint64_t StdAzimuth_NI_67_FR : 8;
        uint64_t SNRdB_NI_67_FR : 8;
        uint64_t PowerDB_NI_67_FR : 8;
        uint64_t WDoppler10DB_NI_67_FR : 7;
        uint64_t Reserve1_1bit_NI_67_FR : 1;
        uint64_t WRange10DB_NI_67_FR : 8;
        uint64_t CoGDoppler_NI_67_FR : 16;
        uint64_t CoGRange_NI_67_FR : 16;
        uint64_t ValidXBeam_NI_66_FR : 1;
        uint64_t NoInfrastructure_NI_66_FR : 1;
        uint64_t Beam_NI_66_FR : 2;
        uint64_t Azimuth_NI_66_FR : 12;
        uint64_t StdAzimuth_NI_66_FR : 8;
        uint64_t SNRdB_NI_66_FR : 8;
        uint64_t PowerDB_NI_66_FR : 8;
        uint64_t WDoppler10DB_NI_66_FR : 7;
        uint64_t Reserve1_1bit_NI_66_FR : 1;
        uint64_t WRange10DB_NI_66_FR : 8;
        uint64_t CoGDoppler_NI_66_FR : 16;
        uint64_t CoGRange_NI_66_FR : 16;
        uint64_t ValidXBeam_NI_65_FR : 1;
        uint64_t NoInfrastructure_NI_65_FR : 1;
        uint64_t Beam_NI_65_FR : 2;
        uint64_t Azimuth_NI_65_FR : 12;
        uint64_t StdAzimuth_NI_65_FR : 8;
        uint64_t SNRdB_NI_65_FR : 8;
        uint64_t PowerDB_NI_65_FR : 8;
        uint64_t WDoppler10DB_NI_65_FR : 7;
        uint64_t Reserve1_1bit_NI_65_FR : 1;
        uint64_t WRange10DB_NI_65_FR : 8;
        uint64_t CoGDoppler_NI_65_FR : 16;
        uint64_t CoGRange_NI_65_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_13_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_13_fr_data_t data;
};

union cansig_mk_non_inf_detection_13_fr__azimuth_ni_67_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_67_FR__S1 : 4;
        uint32_t Azimuth_NI_67_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_14_FR_ID       (0x0290)
#define CANMSG_MK_NON_INF_DETECTION_14_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_14_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_14_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_74_FR : 1;
        uint64_t NoInfrastructure_NI_74_FR : 1;
        uint64_t Beam_NI_74_FR : 2;
        uint64_t Azimuth_NI_74_FR : 12;
        uint64_t StdAzimuth_NI_74_FR : 8;
        uint64_t SNRdB_NI_74_FR : 8;
        uint64_t PowerDB_NI_74_FR : 8;
        uint64_t WDoppler10DB_NI_74_FR : 7;
        uint64_t Reserve1_1bit_NI_74_FR : 1;
        uint64_t WRange10DB_NI_74_FR : 8;
        uint64_t CoGDoppler_NI_74_FR : 16;
        uint64_t CoGRange_NI_74_FR : 16;
        uint64_t ValidXBeam_NI_73_FR : 1;
        uint64_t NoInfrastructure_NI_73_FR : 1;
        uint64_t Beam_NI_73_FR : 2;
        uint64_t Azimuth_NI_73_FR : 12;
        uint64_t StdAzimuth_NI_73_FR : 8;
        uint64_t SNRdB_NI_73_FR : 8;
        uint64_t PowerDB_NI_73_FR : 8;
        uint64_t WDoppler10DB_NI_73_FR : 7;
        uint64_t Reserve1_1bit_NI_73_FR : 1;
        uint64_t WRange10DB_NI_73_FR : 8;
        uint64_t CoGDoppler_NI_73_FR : 16;
        uint64_t CoGRange_NI_73_FR : 16;
        uint64_t ValidXBeam_NI_72_FR : 1;
        uint64_t NoInfrastructure_NI_72_FR : 1;
        uint64_t Beam_NI_72_FR : 2;
        uint64_t Azimuth_NI_72_FR__S1 : 4;
        uint64_t Azimuth_NI_72_FR__S0 : 8;
        uint64_t StdAzimuth_NI_72_FR : 8;
        uint64_t SNRdB_NI_72_FR : 8;
        uint64_t PowerDB_NI_72_FR : 8;
        uint64_t WDoppler10DB_NI_72_FR : 7;
        uint64_t Reserve1_1bit_NI_72_FR : 1;
        uint64_t WRange10DB_NI_72_FR : 8;
        uint64_t CoGDoppler_NI_72_FR : 16;
        uint64_t CoGRange_NI_72_FR : 16;
        uint64_t ValidXBeam_NI_71_FR : 1;
        uint64_t NoInfrastructure_NI_71_FR : 1;
        uint64_t Beam_NI_71_FR : 2;
        uint64_t Azimuth_NI_71_FR : 12;
        uint64_t StdAzimuth_NI_71_FR : 8;
        uint64_t SNRdB_NI_71_FR : 8;
        uint64_t PowerDB_NI_71_FR : 8;
        uint64_t WDoppler10DB_NI_71_FR : 7;
        uint64_t Reserve1_1bit_NI_71_FR : 1;
        uint64_t WRange10DB_NI_71_FR : 8;
        uint64_t CoGDoppler_NI_71_FR : 16;
        uint64_t CoGRange_NI_71_FR : 16;
        uint64_t ValidXBeam_NI_70_FR : 1;
        uint64_t NoInfrastructure_NI_70_FR : 1;
        uint64_t Beam_NI_70_FR : 2;
        uint64_t Azimuth_NI_70_FR : 12;
        uint64_t StdAzimuth_NI_70_FR : 8;
        uint64_t SNRdB_NI_70_FR : 8;
        uint64_t PowerDB_NI_70_FR : 8;
        uint64_t WDoppler10DB_NI_70_FR : 7;
        uint64_t Reserve1_1bit_NI_70_FR : 1;
        uint64_t WRange10DB_NI_70_FR : 8;
        uint64_t CoGDoppler_NI_70_FR : 16;
        uint64_t CoGRange_NI_70_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_14_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_14_fr_data_t data;
};

union cansig_mk_non_inf_detection_14_fr__azimuth_ni_72_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_72_FR__S1 : 4;
        uint32_t Azimuth_NI_72_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_15_FR_ID       (0x0291)
#define CANMSG_MK_NON_INF_DETECTION_15_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_15_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_15_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_79_FR : 1;
        uint64_t NoInfrastructure_NI_79_FR : 1;
        uint64_t Beam_NI_79_FR : 2;
        uint64_t Azimuth_NI_79_FR : 12;
        uint64_t StdAzimuth_NI_79_FR : 8;
        uint64_t SNRdB_NI_79_FR : 8;
        uint64_t PowerDB_NI_79_FR : 8;
        uint64_t WDoppler10DB_NI_79_FR : 7;
        uint64_t Reserve1_1bit_NI_79_FR : 1;
        uint64_t WRange10DB_NI_79_FR : 8;
        uint64_t CoGDoppler_NI_79_FR : 16;
        uint64_t CoGRange_NI_79_FR : 16;
        uint64_t ValidXBeam_NI_78_FR : 1;
        uint64_t NoInfrastructure_NI_78_FR : 1;
        uint64_t Beam_NI_78_FR : 2;
        uint64_t Azimuth_NI_78_FR : 12;
        uint64_t StdAzimuth_NI_78_FR : 8;
        uint64_t SNRdB_NI_78_FR : 8;
        uint64_t PowerDB_NI_78_FR : 8;
        uint64_t WDoppler10DB_NI_78_FR : 7;
        uint64_t Reserve1_1bit_NI_78_FR : 1;
        uint64_t WRange10DB_NI_78_FR : 8;
        uint64_t CoGDoppler_NI_78_FR : 16;
        uint64_t CoGRange_NI_78_FR : 16;
        uint64_t ValidXBeam_NI_77_FR : 1;
        uint64_t NoInfrastructure_NI_77_FR : 1;
        uint64_t Beam_NI_77_FR : 2;
        uint64_t Azimuth_NI_77_FR__S1 : 4;
        uint64_t Azimuth_NI_77_FR__S0 : 8;
        uint64_t StdAzimuth_NI_77_FR : 8;
        uint64_t SNRdB_NI_77_FR : 8;
        uint64_t PowerDB_NI_77_FR : 8;
        uint64_t WDoppler10DB_NI_77_FR : 7;
        uint64_t Reserve1_1bit_NI_77_FR : 1;
        uint64_t WRange10DB_NI_77_FR : 8;
        uint64_t CoGDoppler_NI_77_FR : 16;
        uint64_t CoGRange_NI_77_FR : 16;
        uint64_t ValidXBeam_NI_76_FR : 1;
        uint64_t NoInfrastructure_NI_76_FR : 1;
        uint64_t Beam_NI_76_FR : 2;
        uint64_t Azimuth_NI_76_FR : 12;
        uint64_t StdAzimuth_NI_76_FR : 8;
        uint64_t SNRdB_NI_76_FR : 8;
        uint64_t PowerDB_NI_76_FR : 8;
        uint64_t WDoppler10DB_NI_76_FR : 7;
        uint64_t Reserve1_1bit_NI_76_FR : 1;
        uint64_t WRange10DB_NI_76_FR : 8;
        uint64_t CoGDoppler_NI_76_FR : 16;
        uint64_t CoGRange_NI_76_FR : 16;
        uint64_t ValidXBeam_NI_75_FR : 1;
        uint64_t NoInfrastructure_NI_75_FR : 1;
        uint64_t Beam_NI_75_FR : 2;
        uint64_t Azimuth_NI_75_FR : 12;
        uint64_t StdAzimuth_NI_75_FR : 8;
        uint64_t SNRdB_NI_75_FR : 8;
        uint64_t PowerDB_NI_75_FR : 8;
        uint64_t WDoppler10DB_NI_75_FR : 7;
        uint64_t Reserve1_1bit_NI_75_FR : 1;
        uint64_t WRange10DB_NI_75_FR : 8;
        uint64_t CoGDoppler_NI_75_FR : 16;
        uint64_t CoGRange_NI_75_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_15_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_15_fr_data_t data;
};

union cansig_mk_non_inf_detection_15_fr__azimuth_ni_77_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_77_FR__S1 : 4;
        uint32_t Azimuth_NI_77_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_16_FR_ID       (0x0292)
#define CANMSG_MK_NON_INF_DETECTION_16_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_16_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_16_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_84_FR : 1;
        uint64_t NoInfrastructure_NI_84_FR : 1;
        uint64_t Beam_NI_84_FR : 2;
        uint64_t Azimuth_NI_84_FR : 12;
        uint64_t StdAzimuth_NI_84_FR : 8;
        uint64_t SNRdB_NI_84_FR : 8;
        uint64_t PowerDB_NI_84_FR : 8;
        uint64_t WDoppler10DB_NI_84_FR : 7;
        uint64_t Reserve1_1bit_NI_84_FR : 1;
        uint64_t WRange10DB_NI_84_FR : 8;
        uint64_t CoGDoppler_NI_84_FR : 16;
        uint64_t CoGRange_NI_84_FR : 16;
        uint64_t ValidXBeam_NI_83_FR : 1;
        uint64_t NoInfrastructure_NI_83_FR : 1;
        uint64_t Beam_NI_83_FR : 2;
        uint64_t Azimuth_NI_83_FR : 12;
        uint64_t StdAzimuth_NI_83_FR : 8;
        uint64_t SNRdB_NI_83_FR : 8;
        uint64_t PowerDB_NI_83_FR : 8;
        uint64_t WDoppler10DB_NI_83_FR : 7;
        uint64_t Reserve1_1bit_NI_83_FR : 1;
        uint64_t WRange10DB_NI_83_FR : 8;
        uint64_t CoGDoppler_NI_83_FR : 16;
        uint64_t CoGRange_NI_83_FR : 16;
        uint64_t ValidXBeam_NI_82_FR : 1;
        uint64_t NoInfrastructure_NI_82_FR : 1;
        uint64_t Beam_NI_82_FR : 2;
        uint64_t Azimuth_NI_82_FR__S1 : 4;
        uint64_t Azimuth_NI_82_FR__S0 : 8;
        uint64_t StdAzimuth_NI_82_FR : 8;
        uint64_t SNRdB_NI_82_FR : 8;
        uint64_t PowerDB_NI_82_FR : 8;
        uint64_t WDoppler10DB_NI_82_FR : 7;
        uint64_t Reserve1_1bit_NI_82_FR : 1;
        uint64_t WRange10DB_NI_82_FR : 8;
        uint64_t CoGDoppler_NI_82_FR : 16;
        uint64_t CoGRange_NI_82_FR : 16;
        uint64_t ValidXBeam_NI_81_FR : 1;
        uint64_t NoInfrastructure_NI_81_FR : 1;
        uint64_t Beam_NI_81_FR : 2;
        uint64_t Azimuth_NI_81_FR : 12;
        uint64_t StdAzimuth_NI_81_FR : 8;
        uint64_t SNRdB_NI_81_FR : 8;
        uint64_t PowerDB_NI_81_FR : 8;
        uint64_t WDoppler10DB_NI_81_FR : 7;
        uint64_t Reserve1_1bit_NI_81_FR : 1;
        uint64_t WRange10DB_NI_81_FR : 8;
        uint64_t CoGDoppler_NI_81_FR : 16;
        uint64_t CoGRange_NI_81_FR : 16;
        uint64_t ValidXBeam_NI_80_FR : 1;
        uint64_t NoInfrastructure_NI_80_FR : 1;
        uint64_t Beam_NI_80_FR : 2;
        uint64_t Azimuth_NI_80_FR : 12;
        uint64_t StdAzimuth_NI_80_FR : 8;
        uint64_t SNRdB_NI_80_FR : 8;
        uint64_t PowerDB_NI_80_FR : 8;
        uint64_t WDoppler10DB_NI_80_FR : 7;
        uint64_t Reserve1_1bit_NI_80_FR : 1;
        uint64_t WRange10DB_NI_80_FR : 8;
        uint64_t CoGDoppler_NI_80_FR : 16;
        uint64_t CoGRange_NI_80_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_16_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_16_fr_data_t data;
};

union cansig_mk_non_inf_detection_16_fr__azimuth_ni_82_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_82_FR__S1 : 4;
        uint32_t Azimuth_NI_82_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_17_FR_ID       (0x0293)
#define CANMSG_MK_NON_INF_DETECTION_17_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_17_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_17_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_89_FR : 1;
        uint64_t NoInfrastructure_NI_89_FR : 1;
        uint64_t Beam_NI_89_FR : 2;
        uint64_t Azimuth_NI_89_FR : 12;
        uint64_t StdAzimuth_NI_89_FR : 8;
        uint64_t SNRdB_NI_89_FR : 8;
        uint64_t PowerDB_NI_89_FR : 8;
        uint64_t WDoppler10DB_NI_89_FR : 7;
        uint64_t Reserve1_1bit_NI_89_FR : 1;
        uint64_t WRange10DB_NI_89_FR : 8;
        uint64_t CoGDoppler_NI_89_FR : 16;
        uint64_t CoGRange_NI_89_FR : 16;
        uint64_t ValidXBeam_NI_88_FR : 1;
        uint64_t NoInfrastructure_NI_88_FR : 1;
        uint64_t Beam_NI_88_FR : 2;
        uint64_t Azimuth_NI_88_FR : 12;
        uint64_t StdAzimuth_NI_88_FR : 8;
        uint64_t SNRdB_NI_88_FR : 8;
        uint64_t PowerDB_NI_88_FR : 8;
        uint64_t WDoppler10DB_NI_88_FR : 7;
        uint64_t Reserve1_1bit_NI_88_FR : 1;
        uint64_t WRange10DB_NI_88_FR : 8;
        uint64_t CoGDoppler_NI_88_FR : 16;
        uint64_t CoGRange_NI_88_FR : 16;
        uint64_t ValidXBeam_NI_87_FR : 1;
        uint64_t NoInfrastructure_NI_87_FR : 1;
        uint64_t Beam_NI_87_FR : 2;
        uint64_t Azimuth_NI_87_FR__S1 : 4;
        uint64_t Azimuth_NI_87_FR__S0 : 8;
        uint64_t StdAzimuth_NI_87_FR : 8;
        uint64_t SNRdB_NI_87_FR : 8;
        uint64_t PowerDB_NI_87_FR : 8;
        uint64_t WDoppler10DB_NI_87_FR : 7;
        uint64_t Reserve1_1bit_NI_87_FR : 1;
        uint64_t WRange10DB_NI_87_FR : 8;
        uint64_t CoGDoppler_NI_87_FR : 16;
        uint64_t CoGRange_NI_87_FR : 16;
        uint64_t ValidXBeam_NI_86_FR : 1;
        uint64_t NoInfrastructure_NI_86_FR : 1;
        uint64_t Beam_NI_86_FR : 2;
        uint64_t Azimuth_NI_86_FR : 12;
        uint64_t StdAzimuth_NI_86_FR : 8;
        uint64_t SNRdB_NI_86_FR : 8;
        uint64_t PowerDB_NI_86_FR : 8;
        uint64_t WDoppler10DB_NI_86_FR : 7;
        uint64_t Reserve1_1bit_NI_86_FR : 1;
        uint64_t WRange10DB_NI_86_FR : 8;
        uint64_t CoGDoppler_NI_86_FR : 16;
        uint64_t CoGRange_NI_86_FR : 16;
        uint64_t ValidXBeam_NI_85_FR : 1;
        uint64_t NoInfrastructure_NI_85_FR : 1;
        uint64_t Beam_NI_85_FR : 2;
        uint64_t Azimuth_NI_85_FR : 12;
        uint64_t StdAzimuth_NI_85_FR : 8;
        uint64_t SNRdB_NI_85_FR : 8;
        uint64_t PowerDB_NI_85_FR : 8;
        uint64_t WDoppler10DB_NI_85_FR : 7;
        uint64_t Reserve1_1bit_NI_85_FR : 1;
        uint64_t WRange10DB_NI_85_FR : 8;
        uint64_t CoGDoppler_NI_85_FR : 16;
        uint64_t CoGRange_NI_85_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_17_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_17_fr_data_t data;
};

union cansig_mk_non_inf_detection_17_fr__azimuth_ni_87_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_87_FR__S1 : 4;
        uint32_t Azimuth_NI_87_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_18_FR_ID       (0x0294)
#define CANMSG_MK_NON_INF_DETECTION_18_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_18_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_18_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_94_FR : 1;
        uint64_t NoInfrastructure_NI_94_FR : 1;
        uint64_t Beam_NI_94_FR : 2;
        uint64_t Azimuth_NI_94_FR : 12;
        uint64_t StdAzimuth_NI_94_FR : 8;
        uint64_t SNRdB_NI_94_FR : 8;
        uint64_t PowerDB_NI_94_FR : 8;
        uint64_t WDoppler10DB_NI_94_FR : 7;
        uint64_t Reserve1_1bit_NI_94_FR : 1;
        uint64_t WRange10DB_NI_94_FR : 8;
        uint64_t CoGDoppler_NI_94_FR : 16;
        uint64_t CoGRange_NI_94_FR : 16;
        uint64_t ValidXBeam_NI_93_FR : 1;
        uint64_t NoInfrastructure_NI_93_FR : 1;
        uint64_t Beam_NI_93_FR : 2;
        uint64_t Azimuth_NI_93_FR : 12;
        uint64_t StdAzimuth_NI_93_FR : 8;
        uint64_t SNRdB_NI_93_FR : 8;
        uint64_t PowerDB_NI_93_FR : 8;
        uint64_t WDoppler10DB_NI_93_FR : 7;
        uint64_t Reserve1_1bit_NI_93_FR : 1;
        uint64_t WRange10DB_NI_93_FR : 8;
        uint64_t CoGDoppler_NI_93_FR : 16;
        uint64_t CoGRange_NI_93_FR : 16;
        uint64_t ValidXBeam_NI_92_FR : 1;
        uint64_t NoInfrastructure_NI_92_FR : 1;
        uint64_t Beam_NI_92_FR : 2;
        uint64_t Azimuth_NI_92_FR__S1 : 4;
        uint64_t Azimuth_NI_92_FR__S0 : 8;
        uint64_t StdAzimuth_NI_92_FR : 8;
        uint64_t SNRdB_NI_92_FR : 8;
        uint64_t PowerDB_NI_92_FR : 8;
        uint64_t WDoppler10DB_NI_92_FR : 7;
        uint64_t Reserve1_1bit_NI_92_FR : 1;
        uint64_t WRange10DB_NI_92_FR : 8;
        uint64_t CoGDoppler_NI_92_FR : 16;
        uint64_t CoGRange_NI_92_FR : 16;
        uint64_t ValidXBeam_NI_91_FR : 1;
        uint64_t NoInfrastructure_NI_91_FR : 1;
        uint64_t Beam_NI_91_FR : 2;
        uint64_t Azimuth_NI_91_FR : 12;
        uint64_t StdAzimuth_NI_91_FR : 8;
        uint64_t SNRdB_NI_91_FR : 8;
        uint64_t PowerDB_NI_91_FR : 8;
        uint64_t WDoppler10DB_NI_91_FR : 7;
        uint64_t Reserve1_1bit_NI_91_FR : 1;
        uint64_t WRange10DB_NI_91_FR : 8;
        uint64_t CoGDoppler_NI_91_FR : 16;
        uint64_t CoGRange_NI_91_FR : 16;
        uint64_t ValidXBeam_NI_90_FR : 1;
        uint64_t NoInfrastructure_NI_90_FR : 1;
        uint64_t Beam_NI_90_FR : 2;
        uint64_t Azimuth_NI_90_FR : 12;
        uint64_t StdAzimuth_NI_90_FR : 8;
        uint64_t SNRdB_NI_90_FR : 8;
        uint64_t PowerDB_NI_90_FR : 8;
        uint64_t WDoppler10DB_NI_90_FR : 7;
        uint64_t Reserve1_1bit_NI_90_FR : 1;
        uint64_t WRange10DB_NI_90_FR : 8;
        uint64_t CoGDoppler_NI_90_FR : 16;
        uint64_t CoGRange_NI_90_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_18_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_18_fr_data_t data;
};

union cansig_mk_non_inf_detection_18_fr__azimuth_ni_92_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_92_FR__S1 : 4;
        uint32_t Azimuth_NI_92_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_19_FR_ID       (0x0295)
#define CANMSG_MK_NON_INF_DETECTION_19_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_19_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_19_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_99_FR : 1;
        uint64_t NoInfrastructure_NI_99_FR : 1;
        uint64_t Beam_NI_99_FR : 2;
        uint64_t Azimuth_NI_99_FR : 12;
        uint64_t StdAzimuth_NI_99_FR : 8;
        uint64_t SNRdB_NI_99_FR : 8;
        uint64_t PowerDB_NI_99_FR : 8;
        uint64_t WDoppler10DB_NI_99_FR : 7;
        uint64_t Reserve1_1bit_NI_99_FR : 1;
        uint64_t WRange10DB_NI_99_FR : 8;
        uint64_t CoGDoppler_NI_99_FR : 16;
        uint64_t CoGRange_NI_99_FR : 16;
        uint64_t ValidXBeam_NI_98_FR : 1;
        uint64_t NoInfrastructure_NI_98_FR : 1;
        uint64_t Beam_NI_98_FR : 2;
        uint64_t Azimuth_NI_98_FR : 12;
        uint64_t StdAzimuth_NI_98_FR : 8;
        uint64_t SNRdB_NI_98_FR : 8;
        uint64_t PowerDB_NI_98_FR : 8;
        uint64_t WDoppler10DB_NI_98_FR : 7;
        uint64_t Reserve1_1bit_NI_98_FR : 1;
        uint64_t WRange10DB_NI_98_FR : 8;
        uint64_t CoGDoppler_NI_98_FR : 16;
        uint64_t CoGRange_NI_98_FR : 16;
        uint64_t ValidXBeam_NI_97_FR : 1;
        uint64_t NoInfrastructure_NI_97_FR : 1;
        uint64_t Beam_NI_97_FR : 2;
        uint64_t Azimuth_NI_97_FR__S1 : 4;
        uint64_t Azimuth_NI_97_FR__S0 : 8;
        uint64_t StdAzimuth_NI_97_FR : 8;
        uint64_t SNRdB_NI_97_FR : 8;
        uint64_t PowerDB_NI_97_FR : 8;
        uint64_t WDoppler10DB_NI_97_FR : 7;
        uint64_t Reserve1_1bit_NI_97_FR : 1;
        uint64_t WRange10DB_NI_97_FR : 8;
        uint64_t CoGDoppler_NI_97_FR : 16;
        uint64_t CoGRange_NI_97_FR : 16;
        uint64_t ValidXBeam_NI_96_FR : 1;
        uint64_t NoInfrastructure_NI_96_FR : 1;
        uint64_t Beam_NI_96_FR : 2;
        uint64_t Azimuth_NI_96_FR : 12;
        uint64_t StdAzimuth_NI_96_FR : 8;
        uint64_t SNRdB_NI_96_FR : 8;
        uint64_t PowerDB_NI_96_FR : 8;
        uint64_t WDoppler10DB_NI_96_FR : 7;
        uint64_t Reserve1_1bit_NI_96_FR : 1;
        uint64_t WRange10DB_NI_96_FR : 8;
        uint64_t CoGDoppler_NI_96_FR : 16;
        uint64_t CoGRange_NI_96_FR : 16;
        uint64_t ValidXBeam_NI_95_FR : 1;
        uint64_t NoInfrastructure_NI_95_FR : 1;
        uint64_t Beam_NI_95_FR : 2;
        uint64_t Azimuth_NI_95_FR : 12;
        uint64_t StdAzimuth_NI_95_FR : 8;
        uint64_t SNRdB_NI_95_FR : 8;
        uint64_t PowerDB_NI_95_FR : 8;
        uint64_t WDoppler10DB_NI_95_FR : 7;
        uint64_t Reserve1_1bit_NI_95_FR : 1;
        uint64_t WRange10DB_NI_95_FR : 8;
        uint64_t CoGDoppler_NI_95_FR : 16;
        uint64_t CoGRange_NI_95_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_19_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_19_fr_data_t data;
};

union cansig_mk_non_inf_detection_19_fr__azimuth_ni_97_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_97_FR__S1 : 4;
        uint32_t Azimuth_NI_97_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_1_FR_ID        (0x0283)
#define CANMSG_MK_NON_INF_DETECTION_1_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_1_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_1_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_9_FR : 1;
        uint64_t NoInfrastructure_NI_9_FR : 1;
        uint64_t Beam_NI_9_FR : 2;
        uint64_t Azimuth_NI_9_FR : 12;
        uint64_t StdAzimuth_NI_9_FR : 8;
        uint64_t SNRdB_NI_9_FR : 8;
        uint64_t PowerDB_NI_9_FR : 8;
        uint64_t WDoppler10DB_NI_9_FR : 7;
        uint64_t Reserve1_1bit_NI_9_FR : 1;
        uint64_t WRange10DB_NI_9_FR : 8;
        uint64_t CoGDoppler_NI_9_FR : 16;
        uint64_t CoGRange_NI_9_FR : 16;
        uint64_t ValidXBeam_NI_8_FR : 1;
        uint64_t NoInfrastructure_NI_8_FR : 1;
        uint64_t Beam_NI_8_FR : 2;
        uint64_t Azimuth_NI_8_FR : 12;
        uint64_t StdAzimuth_NI_8_FR : 8;
        uint64_t SNRdB_NI_8_FR : 8;
        uint64_t PowerDB_NI_8_FR : 8;
        uint64_t WDoppler10DB_NI_8_FR : 7;
        uint64_t Reserve1_1bit_NI_8_FR : 1;
        uint64_t WRange10DB_NI_8_FR : 8;
        uint64_t CoGDoppler_NI_8_FR : 16;
        uint64_t CoGRange_NI_8_FR : 16;
        uint64_t ValidXBeam_NI_7_FR : 1;
        uint64_t NoInfrastructure_NI_7_FR : 1;
        uint64_t Beam_NI_7_FR : 2;
        uint64_t Azimuth_NI_7_FR__S1 : 4;
        uint64_t Azimuth_NI_7_FR__S0 : 8;
        uint64_t StdAzimuth_NI_7_FR : 8;
        uint64_t SNRdB_NI_7_FR : 8;
        uint64_t PowerDB_NI_7_FR : 8;
        uint64_t WDoppler10DB_NI_7_FR : 7;
        uint64_t Reserve1_1bit_NI_7_FR : 1;
        uint64_t WRange10DB_NI_7_FR : 8;
        uint64_t CoGDoppler_NI_7_FR : 16;
        uint64_t CoGRange_NI_7_FR : 16;
        uint64_t ValidXBeam_NI_6_FR : 1;
        uint64_t NoInfrastructure_NI_6_FR : 1;
        uint64_t Beam_NI_6_FR : 2;
        uint64_t Azimuth_NI_6_FR : 12;
        uint64_t StdAzimuth_NI_6_FR : 8;
        uint64_t SNRdB_NI_6_FR : 8;
        uint64_t PowerDB_NI_6_FR : 8;
        uint64_t WDoppler10DB_NI_6_FR : 7;
        uint64_t Reserve1_1bit_NI_6_FR : 1;
        uint64_t WRange10DB_NI_6_FR : 8;
        uint64_t CoGDoppler_NI_6_FR : 16;
        uint64_t CoGRange_NI_6_FR : 16;
        uint64_t ValidXBeam_NI_5_FR : 1;
        uint64_t NoInfrastructure_NI_5_FR : 1;
        uint64_t Beam_NI_5_FR : 2;
        uint64_t Azimuth_NI_5_FR : 12;
        uint64_t StdAzimuth_NI_5_FR : 8;
        uint64_t SNRdB_NI_5_FR : 8;
        uint64_t PowerDB_NI_5_FR : 8;
        uint64_t WDoppler10DB_NI_5_FR : 7;
        uint64_t Reserve1_1bit_NI_5_FR : 1;
        uint64_t WRange10DB_NI_5_FR : 8;
        uint64_t CoGDoppler_NI_5_FR : 16;
        uint64_t CoGRange_NI_5_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_1_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_1_fr_data_t data;
};

union cansig_mk_non_inf_detection_1_fr__azimuth_ni_7_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_7_FR__S1 : 4;
        uint32_t Azimuth_NI_7_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_20_FR_ID       (0x0296)
#define CANMSG_MK_NON_INF_DETECTION_20_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_20_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_20_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_104_FR : 1;
        uint64_t NoInfrastructure_NI_104_FR : 1;
        uint64_t Beam_NI_104_FR : 2;
        uint64_t Azimuth_NI_104_FR : 12;
        uint64_t StdAzimuth_NI_104_FR : 8;
        uint64_t SNRdB_NI_104_FR : 8;
        uint64_t PowerDB_NI_104_FR : 8;
        uint64_t WDoppler10DB_NI_104_FR : 7;
        uint64_t Reserve1_1bit_NI_104_FR : 1;
        uint64_t WRange10DB_NI_104_FR : 8;
        uint64_t CoGDoppler_NI_104_FR : 16;
        uint64_t CoGRange_NI_104_FR : 16;
        uint64_t ValidXBeam_NI_103_FR : 1;
        uint64_t NoInfrastructure_NI_103_FR : 1;
        uint64_t Beam_NI_103_FR : 2;
        uint64_t Azimuth_NI_103_FR : 12;
        uint64_t StdAzimuth_NI_103_FR : 8;
        uint64_t SNRdB_NI_103_FR : 8;
        uint64_t PowerDB_NI_103_FR : 8;
        uint64_t WDoppler10DB_NI_103_FR : 7;
        uint64_t Reserve1_1bit_NI_103_FR : 1;
        uint64_t WRange10DB_NI_103_FR : 8;
        uint64_t CoGDoppler_NI_103_FR : 16;
        uint64_t CoGRange_NI_103_FR : 16;
        uint64_t ValidXBeam_NI_102_FR : 1;
        uint64_t NoInfrastructure_NI_102_FR : 1;
        uint64_t Beam_NI_102_FR : 2;
        uint64_t Azimuth_NI_102_FR__S1 : 4;
        uint64_t Azimuth_NI_102_FR__S0 : 8;
        uint64_t StdAzimuth_NI_102_FR : 8;
        uint64_t SNRdB_NI_102_FR : 8;
        uint64_t PowerDB_NI_102_FR : 8;
        uint64_t WDoppler10DB_NI_102_FR : 7;
        uint64_t Reserve1_1bit_NI_102_FR : 1;
        uint64_t WRange10DB_NI_102_FR : 8;
        uint64_t CoGDoppler_NI_102_FR : 16;
        uint64_t CoGRange_NI_102_FR : 16;
        uint64_t ValidXBeam_NI_101_FR : 1;
        uint64_t NoInfrastructure_NI_101_FR : 1;
        uint64_t Beam_NI_101_FR : 2;
        uint64_t Azimuth_NI_101_FR : 12;
        uint64_t StdAzimuth_NI_101_FR : 8;
        uint64_t SNRdB_NI_101_FR : 8;
        uint64_t PowerDB_NI_101_FR : 8;
        uint64_t WDoppler10DB_NI_101_FR : 7;
        uint64_t Reserve1_1bit_NI_101_FR : 1;
        uint64_t WRange10DB_NI_101_FR : 8;
        uint64_t CoGDoppler_NI_101_FR : 16;
        uint64_t CoGRange_NI_101_FR : 16;
        uint64_t ValidXBeam_NI_100_FR : 1;
        uint64_t NoInfrastructure_NI_100_FR : 1;
        uint64_t Beam_NI_100_FR : 2;
        uint64_t Azimuth_NI_100_FR : 12;
        uint64_t StdAzimuth_NI_100_FR : 8;
        uint64_t SNRdB_NI_100_FR : 8;
        uint64_t PowerDB_NI_100_FR : 8;
        uint64_t WDoppler10DB_NI_100_FR : 7;
        uint64_t Reserve1_1bit_NI_100_FR : 1;
        uint64_t WRange10DB_NI_100_FR : 8;
        uint64_t CoGDoppler_NI_100_FR : 16;
        uint64_t CoGRange_NI_100_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_20_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_20_fr_data_t data;
};

union cansig_mk_non_inf_detection_20_fr__azimuth_ni_102_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_102_FR__S1 : 4;
        uint32_t Azimuth_NI_102_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_21_FR_ID       (0x0297)
#define CANMSG_MK_NON_INF_DETECTION_21_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_21_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_21_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_109_FR : 1;
        uint64_t NoInfrastructure_NI_109_FR : 1;
        uint64_t Beam_NI_109_FR : 2;
        uint64_t Azimuth_NI_109_FR : 12;
        uint64_t StdAzimuth_NI_109_FR : 8;
        uint64_t SNRdB_NI_109_FR : 8;
        uint64_t PowerDB_NI_109_FR : 8;
        uint64_t WDoppler10DB_NI_109_FR : 7;
        uint64_t Reserve1_1bit_NI_109_FR : 1;
        uint64_t WRange10DB_NI_109_FR : 8;
        uint64_t CoGDoppler_NI_109_FR : 16;
        uint64_t CoGRange_NI_109_FR : 16;
        uint64_t ValidXBeam_NI_108_FR : 1;
        uint64_t NoInfrastructure_NI_108_FR : 1;
        uint64_t Beam_NI_108_FR : 2;
        uint64_t Azimuth_NI_108_FR : 12;
        uint64_t StdAzimuth_NI_108_FR : 8;
        uint64_t SNRdB_NI_108_FR : 8;
        uint64_t PowerDB_NI_108_FR : 8;
        uint64_t WDoppler10DB_NI_108_FR : 7;
        uint64_t Reserve1_1bit_NI_108_FR : 1;
        uint64_t WRange10DB_NI_108_FR : 8;
        uint64_t CoGDoppler_NI_108_FR : 16;
        uint64_t CoGRange_NI_108_FR : 16;
        uint64_t ValidXBeam_NI_107_FR : 1;
        uint64_t NoInfrastructure_NI_107_FR : 1;
        uint64_t Beam_NI_107_FR : 2;
        uint64_t Azimuth_NI_107_FR__S1 : 4;
        uint64_t Azimuth_NI_107_FR__S0 : 8;
        uint64_t StdAzimuth_NI_107_FR : 8;
        uint64_t SNRdB_NI_107_FR : 8;
        uint64_t PowerDB_NI_107_FR : 8;
        uint64_t WDoppler10DB_NI_107_FR : 7;
        uint64_t Reserve1_1bit_NI_107_FR : 1;
        uint64_t WRange10DB_NI_107_FR : 8;
        uint64_t CoGDoppler_NI_107_FR : 16;
        uint64_t CoGRange_NI_107_FR : 16;
        uint64_t ValidXBeam_NI_106_FR : 1;
        uint64_t NoInfrastructure_NI_106_FR : 1;
        uint64_t Beam_NI_106_FR : 2;
        uint64_t Azimuth_NI_106_FR : 12;
        uint64_t StdAzimuth_NI_106_FR : 8;
        uint64_t SNRdB_NI_106_FR : 8;
        uint64_t PowerDB_NI_106_FR : 8;
        uint64_t WDoppler10DB_NI_106_FR : 7;
        uint64_t Reserve1_1bit_NI_106_FR : 1;
        uint64_t WRange10DB_NI_106_FR : 8;
        uint64_t CoGDoppler_NI_106_FR : 16;
        uint64_t CoGRange_NI_106_FR : 16;
        uint64_t ValidXBeam_NI_105_FR : 1;
        uint64_t NoInfrastructure_NI_105_FR : 1;
        uint64_t Beam_NI_105_FR : 2;
        uint64_t Azimuth_NI_105_FR : 12;
        uint64_t StdAzimuth_NI_105_FR : 8;
        uint64_t SNRdB_NI_105_FR : 8;
        uint64_t PowerDB_NI_105_FR : 8;
        uint64_t WDoppler10DB_NI_105_FR : 7;
        uint64_t Reserve1_1bit_NI_105_FR : 1;
        uint64_t WRange10DB_NI_105_FR : 8;
        uint64_t CoGDoppler_NI_105_FR : 16;
        uint64_t CoGRange_NI_105_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_21_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_21_fr_data_t data;
};

union cansig_mk_non_inf_detection_21_fr__azimuth_ni_107_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_107_FR__S1 : 4;
        uint32_t Azimuth_NI_107_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_22_FR_ID       (0x0298)
#define CANMSG_MK_NON_INF_DETECTION_22_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_22_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_22_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_114_FR : 1;
        uint64_t NoInfrastructure_NI_114_FR : 1;
        uint64_t Beam_NI_114_FR : 2;
        uint64_t Azimuth_NI_114_FR : 12;
        uint64_t StdAzimuth_NI_114_FR : 8;
        uint64_t SNRdB_NI_114_FR : 8;
        uint64_t PowerDB_NI_114_FR : 8;
        uint64_t WDoppler10DB_NI_114_FR : 7;
        uint64_t Reserve1_1bit_NI_114_FR : 1;
        uint64_t WRange10DB_NI_114_FR : 8;
        uint64_t CoGDoppler_NI_114_FR : 16;
        uint64_t CoGRange_NI_114_FR : 16;
        uint64_t ValidXBeam_NI_113_FR : 1;
        uint64_t NoInfrastructure_NI_113_FR : 1;
        uint64_t Beam_NI_113_FR : 2;
        uint64_t Azimuth_NI_113_FR : 12;
        uint64_t StdAzimuth_NI_113_FR : 8;
        uint64_t SNRdB_NI_113_FR : 8;
        uint64_t PowerDB_NI_113_FR : 8;
        uint64_t WDoppler10DB_NI_113_FR : 7;
        uint64_t Reserve1_1bit_NI_113_FR : 1;
        uint64_t WRange10DB_NI_113_FR : 8;
        uint64_t CoGDoppler_NI_113_FR : 16;
        uint64_t CoGRange_NI_113_FR : 16;
        uint64_t ValidXBeam_NI_112_FR : 1;
        uint64_t NoInfrastructure_NI_112_FR : 1;
        uint64_t Beam_NI_112_FR : 2;
        uint64_t Azimuth_NI_112_FR__S1 : 4;
        uint64_t Azimuth_NI_112_FR__S0 : 8;
        uint64_t StdAzimuth_NI_112_FR : 8;
        uint64_t SNRdB_NI_112_FR : 8;
        uint64_t PowerDB_NI_112_FR : 8;
        uint64_t WDoppler10DB_NI_112_FR : 7;
        uint64_t Reserve1_1bit_NI_112_FR : 1;
        uint64_t WRange10DB_NI_112_FR : 8;
        uint64_t CoGDoppler_NI_112_FR : 16;
        uint64_t CoGRange_NI_112_FR : 16;
        uint64_t ValidXBeam_NI_111_FR : 1;
        uint64_t NoInfrastructure_NI_111_FR : 1;
        uint64_t Beam_NI_111_FR : 2;
        uint64_t Azimuth_NI_111_FR : 12;
        uint64_t StdAzimuth_NI_111_FR : 8;
        uint64_t SNRdB_NI_111_FR : 8;
        uint64_t PowerDB_NI_111_FR : 8;
        uint64_t WDoppler10DB_NI_111_FR : 7;
        uint64_t Reserve1_1bit_NI_111_FR : 1;
        uint64_t WRange10DB_NI_111_FR : 8;
        uint64_t CoGDoppler_NI_111_FR : 16;
        uint64_t CoGRange_NI_111_FR : 16;
        uint64_t ValidXBeam_NI_110_FR : 1;
        uint64_t NoInfrastructure_NI_110_FR : 1;
        uint64_t Beam_NI_110_FR : 2;
        uint64_t Azimuth_NI_110_FR : 12;
        uint64_t StdAzimuth_NI_110_FR : 8;
        uint64_t SNRdB_NI_110_FR : 8;
        uint64_t PowerDB_NI_110_FR : 8;
        uint64_t WDoppler10DB_NI_110_FR : 7;
        uint64_t Reserve1_1bit_NI_110_FR : 1;
        uint64_t WRange10DB_NI_110_FR : 8;
        uint64_t CoGDoppler_NI_110_FR : 16;
        uint64_t CoGRange_NI_110_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_22_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_22_fr_data_t data;
};

union cansig_mk_non_inf_detection_22_fr__azimuth_ni_112_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_112_FR__S1 : 4;
        uint32_t Azimuth_NI_112_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_23_FR_ID       (0x0299)
#define CANMSG_MK_NON_INF_DETECTION_23_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_23_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_23_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_119_FR : 1;
        uint64_t NoInfrastructure_NI_119_FR : 1;
        uint64_t Beam_NI_119_FR : 2;
        uint64_t Azimuth_NI_119_FR : 12;
        uint64_t StdAzimuth_NI_119_FR : 8;
        uint64_t SNRdB_NI_119_FR : 8;
        uint64_t PowerDB_NI_119_FR : 8;
        uint64_t WDoppler10DB_NI_119_FR : 7;
        uint64_t Reserve1_1bit_NI_119_FR : 1;
        uint64_t WRange10DB_NI_119_FR : 8;
        uint64_t CoGDoppler_NI_119_FR : 16;
        uint64_t CoGRange_NI_119_FR : 16;
        uint64_t ValidXBeam_NI_118_FR : 1;
        uint64_t NoInfrastructure_NI_118_FR : 1;
        uint64_t Beam_NI_118_FR : 2;
        uint64_t Azimuth_NI_118_FR : 12;
        uint64_t StdAzimuth_NI_118_FR : 8;
        uint64_t SNRdB_NI_118_FR : 8;
        uint64_t PowerDB_NI_118_FR : 8;
        uint64_t WDoppler10DB_NI_118_FR : 7;
        uint64_t Reserve1_1bit_NI_118_FR : 1;
        uint64_t WRange10DB_NI_118_FR : 8;
        uint64_t CoGDoppler_NI_118_FR : 16;
        uint64_t CoGRange_NI_118_FR : 16;
        uint64_t ValidXBeam_NI_117_FR : 1;
        uint64_t NoInfrastructure_NI_117_FR : 1;
        uint64_t Beam_NI_117_FR : 2;
        uint64_t Azimuth_NI_117_FR__S1 : 4;
        uint64_t Azimuth_NI_117_FR__S0 : 8;
        uint64_t StdAzimuth_NI_117_FR : 8;
        uint64_t SNRdB_NI_117_FR : 8;
        uint64_t PowerDB_NI_117_FR : 8;
        uint64_t WDoppler10DB_NI_117_FR : 7;
        uint64_t Reserve1_1bit_NI_117_FR : 1;
        uint64_t WRange10DB_NI_117_FR : 8;
        uint64_t CoGDoppler_NI_117_FR : 16;
        uint64_t CoGRange_NI_117_FR : 16;
        uint64_t ValidXBeam_NI_116_FR : 1;
        uint64_t NoInfrastructure_NI_116_FR : 1;
        uint64_t Beam_NI_116_FR : 2;
        uint64_t Azimuth_NI_116_FR : 12;
        uint64_t StdAzimuth_NI_116_FR : 8;
        uint64_t SNRdB_NI_116_FR : 8;
        uint64_t PowerDB_NI_116_FR : 8;
        uint64_t WDoppler10DB_NI_116_FR : 7;
        uint64_t Reserve1_1bit_NI_116_FR : 1;
        uint64_t WRange10DB_NI_116_FR : 8;
        uint64_t CoGDoppler_NI_116_FR : 16;
        uint64_t CoGRange_NI_116_FR : 16;
        uint64_t ValidXBeam_NI_115_FR : 1;
        uint64_t NoInfrastructure_NI_115_FR : 1;
        uint64_t Beam_NI_115_FR : 2;
        uint64_t Azimuth_NI_115_FR : 12;
        uint64_t StdAzimuth_NI_115_FR : 8;
        uint64_t SNRdB_NI_115_FR : 8;
        uint64_t PowerDB_NI_115_FR : 8;
        uint64_t WDoppler10DB_NI_115_FR : 7;
        uint64_t Reserve1_1bit_NI_115_FR : 1;
        uint64_t WRange10DB_NI_115_FR : 8;
        uint64_t CoGDoppler_NI_115_FR : 16;
        uint64_t CoGRange_NI_115_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_23_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_23_fr_data_t data;
};

union cansig_mk_non_inf_detection_23_fr__azimuth_ni_117_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_117_FR__S1 : 4;
        uint32_t Azimuth_NI_117_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_24_FR_ID       (0x029A)
#define CANMSG_MK_NON_INF_DETECTION_24_FR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_24_FR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_24_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_124_FR : 1;
        uint64_t NoInfrastructure_NI_124_FR : 1;
        uint64_t Beam_NI_124_FR : 2;
        uint64_t Azimuth_NI_124_FR : 12;
        uint64_t StdAzimuth_NI_124_FR : 8;
        uint64_t SNRdB_NI_124_FR : 8;
        uint64_t PowerDB_NI_124_FR : 8;
        uint64_t WDoppler10DB_NI_124_FR : 7;
        uint64_t Reserve1_1bit_NI_124_FR : 1;
        uint64_t WRange10DB_NI_124_FR : 8;
        uint64_t CoGDoppler_NI_124_FR : 16;
        uint64_t CoGRange_NI_124_FR : 16;
        uint64_t ValidXBeam_NI_123_FR : 1;
        uint64_t NoInfrastructure_NI_123_FR : 1;
        uint64_t Beam_NI_123_FR : 2;
        uint64_t Azimuth_NI_123_FR : 12;
        uint64_t StdAzimuth_NI_123_FR : 8;
        uint64_t SNRdB_NI_123_FR : 8;
        uint64_t PowerDB_NI_123_FR : 8;
        uint64_t WDoppler10DB_NI_123_FR : 7;
        uint64_t Reserve1_1bit_NI_123_FR : 1;
        uint64_t WRange10DB_NI_123_FR : 8;
        uint64_t CoGDoppler_NI_123_FR : 16;
        uint64_t CoGRange_NI_123_FR : 16;
        uint64_t ValidXBeam_NI_122_FR : 1;
        uint64_t NoInfrastructure_NI_122_FR : 1;
        uint64_t Beam_NI_122_FR : 2;
        uint64_t Azimuth_NI_122_FR__S1 : 4;
        uint64_t Azimuth_NI_122_FR__S0 : 8;
        uint64_t StdAzimuth_NI_122_FR : 8;
        uint64_t SNRdB_NI_122_FR : 8;
        uint64_t PowerDB_NI_122_FR : 8;
        uint64_t WDoppler10DB_NI_122_FR : 7;
        uint64_t Reserve1_1bit_NI_122_FR : 1;
        uint64_t WRange10DB_NI_122_FR : 8;
        uint64_t CoGDoppler_NI_122_FR : 16;
        uint64_t CoGRange_NI_122_FR : 16;
        uint64_t ValidXBeam_NI_121_FR : 1;
        uint64_t NoInfrastructure_NI_121_FR : 1;
        uint64_t Beam_NI_121_FR : 2;
        uint64_t Azimuth_NI_121_FR : 12;
        uint64_t StdAzimuth_NI_121_FR : 8;
        uint64_t SNRdB_NI_121_FR : 8;
        uint64_t PowerDB_NI_121_FR : 8;
        uint64_t WDoppler10DB_NI_121_FR : 7;
        uint64_t Reserve1_1bit_NI_121_FR : 1;
        uint64_t WRange10DB_NI_121_FR : 8;
        uint64_t CoGDoppler_NI_121_FR : 16;
        uint64_t CoGRange_NI_121_FR : 16;
        uint64_t ValidXBeam_NI_120_FR : 1;
        uint64_t NoInfrastructure_NI_120_FR : 1;
        uint64_t Beam_NI_120_FR : 2;
        uint64_t Azimuth_NI_120_FR : 12;
        uint64_t StdAzimuth_NI_120_FR : 8;
        uint64_t SNRdB_NI_120_FR : 8;
        uint64_t PowerDB_NI_120_FR : 8;
        uint64_t WDoppler10DB_NI_120_FR : 7;
        uint64_t Reserve1_1bit_NI_120_FR : 1;
        uint64_t WRange10DB_NI_120_FR : 8;
        uint64_t CoGDoppler_NI_120_FR : 16;
        uint64_t CoGRange_NI_120_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_24_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_24_fr_data_t data;
};

union cansig_mk_non_inf_detection_24_fr__azimuth_ni_122_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_122_FR__S1 : 4;
        uint32_t Azimuth_NI_122_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_2_FR_ID        (0x0284)
#define CANMSG_MK_NON_INF_DETECTION_2_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_2_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_2_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_14_FR : 1;
        uint64_t NoInfrastructure_NI_14_FR : 1;
        uint64_t Beam_NI_14_FR : 2;
        uint64_t Azimuth_NI_14_FR : 12;
        uint64_t StdAzimuth_NI_14_FR : 8;
        uint64_t SNRdB_NI_14_FR : 8;
        uint64_t PowerDB_NI_14_FR : 8;
        uint64_t WDoppler10DB_NI_14_FR : 7;
        uint64_t Reserve1_1bit_NI_14_FR : 1;
        uint64_t WRange10DB_NI_14_FR : 8;
        uint64_t CoGDoppler_NI_14_FR : 16;
        uint64_t CoGRange_NI_14_FR : 16;
        uint64_t ValidXBeam_NI_13_FR : 1;
        uint64_t NoInfrastructure_NI_13_FR : 1;
        uint64_t Beam_NI_13_FR : 2;
        uint64_t Azimuth_NI_13_FR : 12;
        uint64_t StdAzimuth_NI_13_FR : 8;
        uint64_t SNRdB_NI_13_FR : 8;
        uint64_t PowerDB_NI_13_FR : 8;
        uint64_t WDoppler10DB_NI_13_FR : 7;
        uint64_t Reserve1_1bit_NI_13_FR : 1;
        uint64_t WRange10DB_NI_13_FR : 8;
        uint64_t CoGDoppler_NI_13_FR : 16;
        uint64_t CoGRange_NI_13_FR : 16;
        uint64_t ValidXBeam_NI_12_FR : 1;
        uint64_t NoInfrastructure_NI_12_FR : 1;
        uint64_t Beam_NI_12_FR : 2;
        uint64_t Azimuth_NI_12_FR__S1 : 4;
        uint64_t Azimuth_NI_12_FR__S0 : 8;
        uint64_t StdAzimuth_NI_12_FR : 8;
        uint64_t SNRdB_NI_12_FR : 8;
        uint64_t PowerDB_NI_12_FR : 8;
        uint64_t WDoppler10DB_NI_12_FR : 7;
        uint64_t Reserve1_1bit_NI_12_FR : 1;
        uint64_t WRange10DB_NI_12_FR : 8;
        uint64_t CoGDoppler_NI_12_FR : 16;
        uint64_t CoGRange_NI_12_FR : 16;
        uint64_t ValidXBeam_NI_11_FR : 1;
        uint64_t NoInfrastructure_NI_11_FR : 1;
        uint64_t Beam_NI_11_FR : 2;
        uint64_t Azimuth_NI_11_FR : 12;
        uint64_t StdAzimuth_NI_11_FR : 8;
        uint64_t SNRdB_NI_11_FR : 8;
        uint64_t PowerDB_NI_11_FR : 8;
        uint64_t WDoppler10DB_NI_11_FR : 7;
        uint64_t Reserve1_1bit_NI_11_FR : 1;
        uint64_t WRange10DB_NI_11_FR : 8;
        uint64_t CoGDoppler_NI_11_FR : 16;
        uint64_t CoGRange_NI_11_FR : 16;
        uint64_t ValidXBeam_NI_10_FR : 1;
        uint64_t NoInfrastructure_NI_10_FR : 1;
        uint64_t Beam_NI_10_FR : 2;
        uint64_t Azimuth_NI_10_FR : 12;
        uint64_t StdAzimuth_NI_10_FR : 8;
        uint64_t SNRdB_NI_10_FR : 8;
        uint64_t PowerDB_NI_10_FR : 8;
        uint64_t WDoppler10DB_NI_10_FR : 7;
        uint64_t Reserve1_1bit_NI_10_FR : 1;
        uint64_t WRange10DB_NI_10_FR : 8;
        uint64_t CoGDoppler_NI_10_FR : 16;
        uint64_t CoGRange_NI_10_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_2_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_2_fr_data_t data;
};

union cansig_mk_non_inf_detection_2_fr__azimuth_ni_12_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_12_FR__S1 : 4;
        uint32_t Azimuth_NI_12_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_3_FR_ID        (0x0285)
#define CANMSG_MK_NON_INF_DETECTION_3_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_3_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_3_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_19_FR : 1;
        uint64_t NoInfrastructure_NI_19_FR : 1;
        uint64_t Beam_NI_19_FR : 2;
        uint64_t Azimuth_NI_19_FR : 12;
        uint64_t StdAzimuth_NI_19_FR : 8;
        uint64_t SNRdB_NI_19_FR : 8;
        uint64_t PowerDB_NI_19_FR : 8;
        uint64_t WDoppler10DB_NI_19_FR : 7;
        uint64_t Reserve1_1bit_NI_19_FR : 1;
        uint64_t WRange10DB_NI_19_FR : 8;
        uint64_t CoGDoppler_NI_19_FR : 16;
        uint64_t CoGRange_NI_19_FR : 16;
        uint64_t ValidXBeam_NI_18_FR : 1;
        uint64_t NoInfrastructure_NI_18_FR : 1;
        uint64_t Beam_NI_18_FR : 2;
        uint64_t Azimuth_NI_18_FR : 12;
        uint64_t StdAzimuth_NI_18_FR : 8;
        uint64_t SNRdB_NI_18_FR : 8;
        uint64_t PowerDB_NI_18_FR : 8;
        uint64_t WDoppler10DB_NI_18_FR : 7;
        uint64_t Reserve1_1bit_NI_18_FR : 1;
        uint64_t WRange10DB_NI_18_FR : 8;
        uint64_t CoGDoppler_NI_18_FR : 16;
        uint64_t CoGRange_NI_18_FR : 16;
        uint64_t ValidXBeam_NI_17_FR : 1;
        uint64_t NoInfrastructure_NI_17_FR : 1;
        uint64_t Beam_NI_17_FR : 2;
        uint64_t Azimuth_NI_17_FR__S1 : 4;
        uint64_t Azimuth_NI_17_FR__S0 : 8;
        uint64_t StdAzimuth_NI_17_FR : 8;
        uint64_t SNRdB_NI_17_FR : 8;
        uint64_t PowerDB_NI_17_FR : 8;
        uint64_t WDoppler10DB_NI_17_FR : 7;
        uint64_t Reserve1_1bit_NI_17_FR : 1;
        uint64_t WRange10DB_NI_17_FR : 8;
        uint64_t CoGDoppler_NI_17_FR : 16;
        uint64_t CoGRange_NI_17_FR : 16;
        uint64_t ValidXBeam_NI_16_FR : 1;
        uint64_t NoInfrastructure_NI_16_FR : 1;
        uint64_t Beam_NI_16_FR : 2;
        uint64_t Azimuth_NI_16_FR : 12;
        uint64_t StdAzimuth_NI_16_FR : 8;
        uint64_t SNRdB_NI_16_FR : 8;
        uint64_t PowerDB_NI_16_FR : 8;
        uint64_t WDoppler10DB_NI_16_FR : 7;
        uint64_t Reserve1_1bit_NI_16_FR : 1;
        uint64_t WRange10DB_NI_16_FR : 8;
        uint64_t CoGDoppler_NI_16_FR : 16;
        uint64_t CoGRange_NI_16_FR : 16;
        uint64_t ValidXBeam_NI_15_FR : 1;
        uint64_t NoInfrastructure_NI_15_FR : 1;
        uint64_t Beam_NI_15_FR : 2;
        uint64_t Azimuth_NI_15_FR : 12;
        uint64_t StdAzimuth_NI_15_FR : 8;
        uint64_t SNRdB_NI_15_FR : 8;
        uint64_t PowerDB_NI_15_FR : 8;
        uint64_t WDoppler10DB_NI_15_FR : 7;
        uint64_t Reserve1_1bit_NI_15_FR : 1;
        uint64_t WRange10DB_NI_15_FR : 8;
        uint64_t CoGDoppler_NI_15_FR : 16;
        uint64_t CoGRange_NI_15_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_3_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_3_fr_data_t data;
};

union cansig_mk_non_inf_detection_3_fr__azimuth_ni_17_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_17_FR__S1 : 4;
        uint32_t Azimuth_NI_17_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_4_FR_ID        (0x0286)
#define CANMSG_MK_NON_INF_DETECTION_4_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_4_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_4_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_24_FR : 1;
        uint64_t NoInfrastructure_NI_24_FR : 1;
        uint64_t Beam_NI_24_FR : 2;
        uint64_t Azimuth_NI_24_FR : 12;
        uint64_t StdAzimuth_NI_24_FR : 8;
        uint64_t SNRdB_NI_24_FR : 8;
        uint64_t PowerDB_NI_24_FR : 8;
        uint64_t WDoppler10DB_NI_24_FR : 7;
        uint64_t Reserve1_1bit_NI_24_FR : 1;
        uint64_t WRange10DB_NI_24_FR : 8;
        uint64_t CoGDoppler_NI_24_FR : 16;
        uint64_t CoGRange_NI_24_FR : 16;
        uint64_t ValidXBeam_NI_23_FR : 1;
        uint64_t NoInfrastructure_NI_23_FR : 1;
        uint64_t Beam_NI_23_FR : 2;
        uint64_t Azimuth_NI_23_FR : 12;
        uint64_t StdAzimuth_NI_23_FR : 8;
        uint64_t SNRdB_NI_23_FR : 8;
        uint64_t PowerDB_NI_23_FR : 8;
        uint64_t WDoppler10DB_NI_23_FR : 7;
        uint64_t Reserve1_1bit_NI_23_FR : 1;
        uint64_t WRange10DB_NI_23_FR : 8;
        uint64_t CoGDoppler_NI_23_FR : 16;
        uint64_t CoGRange_NI_23_FR : 16;
        uint64_t ValidXBeam_NI_22_FR : 1;
        uint64_t NoInfrastructure_NI_22_FR : 1;
        uint64_t Beam_NI_22_FR : 2;
        uint64_t Azimuth_NI_22_FR__S1 : 4;
        uint64_t Azimuth_NI_22_FR__S0 : 8;
        uint64_t StdAzimuth_NI_22_FR : 8;
        uint64_t SNRdB_NI_22_FR : 8;
        uint64_t PowerDB_NI_22_FR : 8;
        uint64_t WDoppler10DB_NI_22_FR : 7;
        uint64_t Reserve1_1bit_NI_22_FR : 1;
        uint64_t WRange10DB_NI_22_FR : 8;
        uint64_t CoGDoppler_NI_22_FR : 16;
        uint64_t CoGRange_NI_22_FR : 16;
        uint64_t ValidXBeam_NI_21_FR : 1;
        uint64_t NoInfrastructure_NI_21_FR : 1;
        uint64_t Beam_NI_21_FR : 2;
        uint64_t Azimuth_NI_21_FR : 12;
        uint64_t StdAzimuth_NI_21_FR : 8;
        uint64_t SNRdB_NI_21_FR : 8;
        uint64_t PowerDB_NI_21_FR : 8;
        uint64_t WDoppler10DB_NI_21_FR : 7;
        uint64_t Reserve1_1bit_NI_21_FR : 1;
        uint64_t WRange10DB_NI_21_FR : 8;
        uint64_t CoGDoppler_NI_21_FR : 16;
        uint64_t CoGRange_NI_21_FR : 16;
        uint64_t ValidXBeam_NI_20_FR : 1;
        uint64_t NoInfrastructure_NI_20_FR : 1;
        uint64_t Beam_NI_20_FR : 2;
        uint64_t Azimuth_NI_20_FR : 12;
        uint64_t StdAzimuth_NI_20_FR : 8;
        uint64_t SNRdB_NI_20_FR : 8;
        uint64_t PowerDB_NI_20_FR : 8;
        uint64_t WDoppler10DB_NI_20_FR : 7;
        uint64_t Reserve1_1bit_NI_20_FR : 1;
        uint64_t WRange10DB_NI_20_FR : 8;
        uint64_t CoGDoppler_NI_20_FR : 16;
        uint64_t CoGRange_NI_20_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_4_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_4_fr_data_t data;
};

union cansig_mk_non_inf_detection_4_fr__azimuth_ni_22_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_22_FR__S1 : 4;
        uint32_t Azimuth_NI_22_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_5_FR_ID        (0x0287)
#define CANMSG_MK_NON_INF_DETECTION_5_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_5_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_5_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_29_FR : 1;
        uint64_t NoInfrastructure_NI_29_FR : 1;
        uint64_t Beam_NI_29_FR : 2;
        uint64_t Azimuth_NI_29_FR : 12;
        uint64_t StdAzimuth_NI_29_FR : 8;
        uint64_t SNRdB_NI_29_FR : 8;
        uint64_t PowerDB_NI_29_FR : 8;
        uint64_t WDoppler10DB_NI_29_FR : 7;
        uint64_t Reserve1_1bit_NI_29_FR : 1;
        uint64_t WRange10DB_NI_29_FR : 8;
        uint64_t CoGDoppler_NI_29_FR : 16;
        uint64_t CoGRange_NI_29_FR : 16;
        uint64_t ValidXBeam_NI_28_FR : 1;
        uint64_t NoInfrastructure_NI_28_FR : 1;
        uint64_t Beam_NI_28_FR : 2;
        uint64_t Azimuth_NI_28_FR : 12;
        uint64_t StdAzimuth_NI_28_FR : 8;
        uint64_t SNRdB_NI_28_FR : 8;
        uint64_t PowerDB_NI_28_FR : 8;
        uint64_t WDoppler10DB_NI_28_FR : 7;
        uint64_t Reserve1_1bit_NI_28_FR : 1;
        uint64_t WRange10DB_NI_28_FR : 8;
        uint64_t CoGDoppler_NI_28_FR : 16;
        uint64_t CoGRange_NI_28_FR : 16;
        uint64_t ValidXBeam_NI_27_FR : 1;
        uint64_t NoInfrastructure_NI_27_FR : 1;
        uint64_t Beam_NI_27_FR : 2;
        uint64_t Azimuth_NI_27_FR__S1 : 4;
        uint64_t Azimuth_NI_27_FR__S0 : 8;
        uint64_t StdAzimuth_NI_27_FR : 8;
        uint64_t SNRdB_NI_27_FR : 8;
        uint64_t PowerDB_NI_27_FR : 8;
        uint64_t WDoppler10DB_NI_27_FR : 7;
        uint64_t Reserve1_1bit_NI_27_FR : 1;
        uint64_t WRange10DB_NI_27_FR : 8;
        uint64_t CoGDoppler_NI_27_FR : 16;
        uint64_t CoGRange_NI_27_FR : 16;
        uint64_t ValidXBeam_NI_26_FR : 1;
        uint64_t NoInfrastructure_NI_26_FR : 1;
        uint64_t Beam_NI_26_FR : 2;
        uint64_t Azimuth_NI_26_FR : 12;
        uint64_t StdAzimuth_NI_26_FR : 8;
        uint64_t SNRdB_NI_26_FR : 8;
        uint64_t PowerDB_NI_26_FR : 8;
        uint64_t WDoppler10DB_NI_26_FR : 7;
        uint64_t Reserve1_1bit_NI_26_FR : 1;
        uint64_t WRange10DB_NI_26_FR : 8;
        uint64_t CoGDoppler_NI_26_FR : 16;
        uint64_t CoGRange_NI_26_FR : 16;
        uint64_t ValidXBeam_NI_25_FR : 1;
        uint64_t NoInfrastructure_NI_25_FR : 1;
        uint64_t Beam_NI_25_FR : 2;
        uint64_t Azimuth_NI_25_FR : 12;
        uint64_t StdAzimuth_NI_25_FR : 8;
        uint64_t SNRdB_NI_25_FR : 8;
        uint64_t PowerDB_NI_25_FR : 8;
        uint64_t WDoppler10DB_NI_25_FR : 7;
        uint64_t Reserve1_1bit_NI_25_FR : 1;
        uint64_t WRange10DB_NI_25_FR : 8;
        uint64_t CoGDoppler_NI_25_FR : 16;
        uint64_t CoGRange_NI_25_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_5_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_5_fr_data_t data;
};

union cansig_mk_non_inf_detection_5_fr__azimuth_ni_27_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_27_FR__S1 : 4;
        uint32_t Azimuth_NI_27_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_6_FR_ID        (0x0288)
#define CANMSG_MK_NON_INF_DETECTION_6_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_6_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_6_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_34_FR : 1;
        uint64_t NoInfrastructure_NI_34_FR : 1;
        uint64_t Beam_NI_34_FR : 2;
        uint64_t Azimuth_NI_34_FR : 12;
        uint64_t StdAzimuth_NI_34_FR : 8;
        uint64_t SNRdB_NI_34_FR : 8;
        uint64_t PowerDB_NI_34_FR : 8;
        uint64_t WDoppler10DB_NI_34_FR : 7;
        uint64_t Reserve1_1bit_NI_34_FR : 1;
        uint64_t WRange10DB_NI_34_FR : 8;
        uint64_t CoGDoppler_NI_34_FR : 16;
        uint64_t CoGRange_NI_34_FR : 16;
        uint64_t ValidXBeam_NI_33_FR : 1;
        uint64_t NoInfrastructure_NI_33_FR : 1;
        uint64_t Beam_NI_33_FR : 2;
        uint64_t Azimuth_NI_33_FR : 12;
        uint64_t StdAzimuth_NI_33_FR : 8;
        uint64_t SNRdB_NI_33_FR : 8;
        uint64_t PowerDB_NI_33_FR : 8;
        uint64_t WDoppler10DB_NI_33_FR : 7;
        uint64_t Reserve1_1bit_NI_33_FR : 1;
        uint64_t WRange10DB_NI_33_FR : 8;
        uint64_t CoGDoppler_NI_33_FR : 16;
        uint64_t CoGRange_NI_33_FR : 16;
        uint64_t ValidXBeam_NI_32_FR : 1;
        uint64_t NoInfrastructure_NI_32_FR : 1;
        uint64_t Beam_NI_32_FR : 2;
        uint64_t Azimuth_NI_32_FR__S1 : 4;
        uint64_t Azimuth_NI_32_FR__S0 : 8;
        uint64_t StdAzimuth_NI_32_FR : 8;
        uint64_t SNRdB_NI_32_FR : 8;
        uint64_t PowerDB_NI_32_FR : 8;
        uint64_t WDoppler10DB_NI_32_FR : 7;
        uint64_t Reserve1_1bit_NI_32_FR : 1;
        uint64_t WRange10DB_NI_32_FR : 8;
        uint64_t CoGDoppler_NI_32_FR : 16;
        uint64_t CoGRange_NI_32_FR : 16;
        uint64_t ValidXBeam_NI_31_FR : 1;
        uint64_t NoInfrastructure_NI_31_FR : 1;
        uint64_t Beam_NI_31_FR : 2;
        uint64_t Azimuth_NI_31_FR : 12;
        uint64_t StdAzimuth_NI_31_FR : 8;
        uint64_t SNRdB_NI_31_FR : 8;
        uint64_t PowerDB_NI_31_FR : 8;
        uint64_t WDoppler10DB_NI_31_FR : 7;
        uint64_t Reserve1_1bit_NI_31_FR : 1;
        uint64_t WRange10DB_NI_31_FR : 8;
        uint64_t CoGDoppler_NI_31_FR : 16;
        uint64_t CoGRange_NI_31_FR : 16;
        uint64_t ValidXBeam_NI_30_FR : 1;
        uint64_t NoInfrastructure_NI_30_FR : 1;
        uint64_t Beam_NI_30_FR : 2;
        uint64_t Azimuth_NI_30_FR : 12;
        uint64_t StdAzimuth_NI_30_FR : 8;
        uint64_t SNRdB_NI_30_FR : 8;
        uint64_t PowerDB_NI_30_FR : 8;
        uint64_t WDoppler10DB_NI_30_FR : 7;
        uint64_t Reserve1_1bit_NI_30_FR : 1;
        uint64_t WRange10DB_NI_30_FR : 8;
        uint64_t CoGDoppler_NI_30_FR : 16;
        uint64_t CoGRange_NI_30_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_6_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_6_fr_data_t data;
};

union cansig_mk_non_inf_detection_6_fr__azimuth_ni_32_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_32_FR__S1 : 4;
        uint32_t Azimuth_NI_32_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_7_FR_ID        (0x0289)
#define CANMSG_MK_NON_INF_DETECTION_7_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_7_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_7_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_39_FR : 1;
        uint64_t NoInfrastructure_NI_39_FR : 1;
        uint64_t Beam_NI_39_FR : 2;
        uint64_t Azimuth_NI_39_FR : 12;
        uint64_t StdAzimuth_NI_39_FR : 8;
        uint64_t SNRdB_NI_39_FR : 8;
        uint64_t PowerDB_NI_39_FR : 8;
        uint64_t WDoppler10DB_NI_39_FR : 7;
        uint64_t Reserve1_1bit_NI_39_FR : 1;
        uint64_t WRange10DB_NI_39_FR : 8;
        uint64_t CoGDoppler_NI_39_FR : 16;
        uint64_t CoGRange_NI_39_FR : 16;
        uint64_t ValidXBeam_NI_38_FR : 1;
        uint64_t NoInfrastructure_NI_38_FR : 1;
        uint64_t Beam_NI_38_FR : 2;
        uint64_t Azimuth_NI_38_FR : 12;
        uint64_t StdAzimuth_NI_38_FR : 8;
        uint64_t SNRdB_NI_38_FR : 8;
        uint64_t PowerDB_NI_38_FR : 8;
        uint64_t WDoppler10DB_NI_38_FR : 7;
        uint64_t Reserve1_1bit_NI_38_FR : 1;
        uint64_t WRange10DB_NI_38_FR : 8;
        uint64_t CoGDoppler_NI_38_FR : 16;
        uint64_t CoGRange_NI_38_FR : 16;
        uint64_t ValidXBeam_NI_37_FR : 1;
        uint64_t NoInfrastructure_NI_37_FR : 1;
        uint64_t Beam_NI_37_FR : 2;
        uint64_t Azimuth_NI_37_FR__S1 : 4;
        uint64_t Azimuth_NI_37_FR__S0 : 8;
        uint64_t StdAzimuth_NI_37_FR : 8;
        uint64_t SNRdB_NI_37_FR : 8;
        uint64_t PowerDB_NI_37_FR : 8;
        uint64_t WDoppler10DB_NI_37_FR : 7;
        uint64_t Reserve1_1bit_NI_37_FR : 1;
        uint64_t WRange10DB_NI_37_FR : 8;
        uint64_t CoGDoppler_NI_37_FR : 16;
        uint64_t CoGRange_NI_37_FR : 16;
        uint64_t ValidXBeam_NI_36_FR : 1;
        uint64_t NoInfrastructure_NI_36_FR : 1;
        uint64_t Beam_NI_36_FR : 2;
        uint64_t Azimuth_NI_36_FR : 12;
        uint64_t StdAzimuth_NI_36_FR : 8;
        uint64_t SNRdB_NI_36_FR : 8;
        uint64_t PowerDB_NI_36_FR : 8;
        uint64_t WDoppler10DB_NI_36_FR : 7;
        uint64_t Reserve1_1bit_NI_36_FR : 1;
        uint64_t WRange10DB_NI_36_FR : 8;
        uint64_t CoGDoppler_NI_36_FR : 16;
        uint64_t CoGRange_NI_36_FR : 16;
        uint64_t ValidXBeam_NI_35_FR : 1;
        uint64_t NoInfrastructure_NI_35_FR : 1;
        uint64_t Beam_NI_35_FR : 2;
        uint64_t Azimuth_NI_35_FR : 12;
        uint64_t StdAzimuth_NI_35_FR : 8;
        uint64_t SNRdB_NI_35_FR : 8;
        uint64_t PowerDB_NI_35_FR : 8;
        uint64_t WDoppler10DB_NI_35_FR : 7;
        uint64_t Reserve1_1bit_NI_35_FR : 1;
        uint64_t WRange10DB_NI_35_FR : 8;
        uint64_t CoGDoppler_NI_35_FR : 16;
        uint64_t CoGRange_NI_35_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_7_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_7_fr_data_t data;
};

union cansig_mk_non_inf_detection_7_fr__azimuth_ni_37_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_37_FR__S1 : 4;
        uint32_t Azimuth_NI_37_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_8_FR_ID        (0x028A)
#define CANMSG_MK_NON_INF_DETECTION_8_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_8_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_8_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_44_FR : 1;
        uint64_t NoInfrastructure_NI_44_FR : 1;
        uint64_t Beam_NI_44_FR : 2;
        uint64_t Azimuth_NI_44_FR : 12;
        uint64_t StdAzimuth_NI_44_FR : 8;
        uint64_t SNRdB_NI_44_FR : 8;
        uint64_t PowerDB_NI_44_FR : 8;
        uint64_t WDoppler10DB_NI_44_FR : 7;
        uint64_t Reserve1_1bit_NI_44_FR : 1;
        uint64_t WRange10DB_NI_44_FR : 8;
        uint64_t CoGDoppler_NI_44_FR : 16;
        uint64_t CoGRange_NI_44_FR : 16;
        uint64_t ValidXBeam_NI_43_FR : 1;
        uint64_t NoInfrastructure_NI_43_FR : 1;
        uint64_t Beam_NI_43_FR : 2;
        uint64_t Azimuth_NI_43_FR : 12;
        uint64_t StdAzimuth_NI_43_FR : 8;
        uint64_t SNRdB_NI_43_FR : 8;
        uint64_t PowerDB_NI_43_FR : 8;
        uint64_t WDoppler10DB_NI_43_FR : 7;
        uint64_t Reserve1_1bit_NI_43_FR : 1;
        uint64_t WRange10DB_NI_43_FR : 8;
        uint64_t CoGDoppler_NI_43_FR : 16;
        uint64_t CoGRange_NI_43_FR : 16;
        uint64_t ValidXBeam_NI_42_FR : 1;
        uint64_t NoInfrastructure_NI_42_FR : 1;
        uint64_t Beam_NI_42_FR : 2;
        uint64_t Azimuth_NI_42_FR__S1 : 4;
        uint64_t Azimuth_NI_42_FR__S0 : 8;
        uint64_t StdAzimuth_NI_42_FR : 8;
        uint64_t SNRdB_NI_42_FR : 8;
        uint64_t PowerDB_NI_42_FR : 8;
        uint64_t WDoppler10DB_NI_42_FR : 7;
        uint64_t Reserve1_1bit_NI_42_FR : 1;
        uint64_t WRange10DB_NI_42_FR : 8;
        uint64_t CoGDoppler_NI_42_FR : 16;
        uint64_t CoGRange_NI_42_FR : 16;
        uint64_t ValidXBeam_NI_41_FR : 1;
        uint64_t NoInfrastructure_NI_41_FR : 1;
        uint64_t Beam_NI_41_FR : 2;
        uint64_t Azimuth_NI_41_FR : 12;
        uint64_t StdAzimuth_NI_41_FR : 8;
        uint64_t SNRdB_NI_41_FR : 8;
        uint64_t PowerDB_NI_41_FR : 8;
        uint64_t WDoppler10DB_NI_41_FR : 7;
        uint64_t Reserve1_1bit_NI_41_FR : 1;
        uint64_t WRange10DB_NI_41_FR : 8;
        uint64_t CoGDoppler_NI_41_FR : 16;
        uint64_t CoGRange_NI_41_FR : 16;
        uint64_t ValidXBeam_NI_40_FR : 1;
        uint64_t NoInfrastructure_NI_40_FR : 1;
        uint64_t Beam_NI_40_FR : 2;
        uint64_t Azimuth_NI_40_FR : 12;
        uint64_t StdAzimuth_NI_40_FR : 8;
        uint64_t SNRdB_NI_40_FR : 8;
        uint64_t PowerDB_NI_40_FR : 8;
        uint64_t WDoppler10DB_NI_40_FR : 7;
        uint64_t Reserve1_1bit_NI_40_FR : 1;
        uint64_t WRange10DB_NI_40_FR : 8;
        uint64_t CoGDoppler_NI_40_FR : 16;
        uint64_t CoGRange_NI_40_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_8_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_8_fr_data_t data;
};

union cansig_mk_non_inf_detection_8_fr__azimuth_ni_42_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_42_FR__S1 : 4;
        uint32_t Azimuth_NI_42_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_9_FR_ID        (0x028B)
#define CANMSG_MK_NON_INF_DETECTION_9_FR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_9_FR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_9_fr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_49_FR : 1;
        uint64_t NoInfrastructure_NI_49_FR : 1;
        uint64_t Beam_NI_49_FR : 2;
        uint64_t Azimuth_NI_49_FR : 12;
        uint64_t StdAzimuth_NI_49_FR : 8;
        uint64_t SNRdB_NI_49_FR : 8;
        uint64_t PowerDB_NI_49_FR : 8;
        uint64_t WDoppler10DB_NI_49_FR : 7;
        uint64_t Reserve1_1bit_NI_49_FR : 1;
        uint64_t WRange10DB_NI_49_FR : 8;
        uint64_t CoGDoppler_NI_49_FR : 16;
        uint64_t CoGRange_NI_49_FR : 16;
        uint64_t ValidXBeam_NI_48_FR : 1;
        uint64_t NoInfrastructure_NI_48_FR : 1;
        uint64_t Beam_NI_48_FR : 2;
        uint64_t Azimuth_NI_48_FR : 12;
        uint64_t StdAzimuth_NI_48_FR : 8;
        uint64_t SNRdB_NI_48_FR : 8;
        uint64_t PowerDB_NI_48_FR : 8;
        uint64_t WDoppler10DB_NI_48_FR : 7;
        uint64_t Reserve1_1bit_NI_48_FR : 1;
        uint64_t WRange10DB_NI_48_FR : 8;
        uint64_t CoGDoppler_NI_48_FR : 16;
        uint64_t CoGRange_NI_48_FR : 16;
        uint64_t ValidXBeam_NI_47_FR : 1;
        uint64_t NoInfrastructure_NI_47_FR : 1;
        uint64_t Beam_NI_47_FR : 2;
        uint64_t Azimuth_NI_47_FR__S1 : 4;
        uint64_t Azimuth_NI_47_FR__S0 : 8;
        uint64_t StdAzimuth_NI_47_FR : 8;
        uint64_t SNRdB_NI_47_FR : 8;
        uint64_t PowerDB_NI_47_FR : 8;
        uint64_t WDoppler10DB_NI_47_FR : 7;
        uint64_t Reserve1_1bit_NI_47_FR : 1;
        uint64_t WRange10DB_NI_47_FR : 8;
        uint64_t CoGDoppler_NI_47_FR : 16;
        uint64_t CoGRange_NI_47_FR : 16;
        uint64_t ValidXBeam_NI_46_FR : 1;
        uint64_t NoInfrastructure_NI_46_FR : 1;
        uint64_t Beam_NI_46_FR : 2;
        uint64_t Azimuth_NI_46_FR : 12;
        uint64_t StdAzimuth_NI_46_FR : 8;
        uint64_t SNRdB_NI_46_FR : 8;
        uint64_t PowerDB_NI_46_FR : 8;
        uint64_t WDoppler10DB_NI_46_FR : 7;
        uint64_t Reserve1_1bit_NI_46_FR : 1;
        uint64_t WRange10DB_NI_46_FR : 8;
        uint64_t CoGDoppler_NI_46_FR : 16;
        uint64_t CoGRange_NI_46_FR : 16;
        uint64_t ValidXBeam_NI_45_FR : 1;
        uint64_t NoInfrastructure_NI_45_FR : 1;
        uint64_t Beam_NI_45_FR : 2;
        uint64_t Azimuth_NI_45_FR : 12;
        uint64_t StdAzimuth_NI_45_FR : 8;
        uint64_t SNRdB_NI_45_FR : 8;
        uint64_t PowerDB_NI_45_FR : 8;
        uint64_t WDoppler10DB_NI_45_FR : 7;
        uint64_t Reserve1_1bit_NI_45_FR : 1;
        uint64_t WRange10DB_NI_45_FR : 8;
        uint64_t CoGDoppler_NI_45_FR : 16;
        uint64_t CoGRange_NI_45_FR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_9_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_9_fr_data_t data;
};

union cansig_mk_non_inf_detection_9_fr__azimuth_ni_47_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_47_FR__S1 : 4;
        uint32_t Azimuth_NI_47_FR__S0 : 8;
    } fields;
};

#define CANMSG_MK_TARGET_DETECTION_HEADER_FR_ID    (0x0281)
#define CANMSG_MK_TARGET_DETECTION_HEADER_FR_DLC   (48)
#define CANMSG_MK_TARGET_DETECTION_HEADER_FR_MIN_DLC (48)
union canmsg_mk_target_detection_header_fr_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t InterferenceExceeded_FR : 8;
        uint64_t PAD2 : 8;
        uint64_t numFreespaceDetections_FR : 2;
        uint64_t DetectionListVersion_FR : 6;
        uint64_t numInfrastructureDetected_FR : 8;
        uint64_t numNonInfraDetected_FR : 8;
        uint64_t numCmplxValPerDetectionBeam3_FR : 8;
        uint64_t numCmplxValPerDetectionBeam2_FR : 8;
        uint64_t numCmplxValPerDetectionBeam1_FR : 8;
        uint64_t UnambiguousVelMeas3_FR : 8;
        uint64_t UnambiguousVelMeas2_FR : 8;
        uint64_t UnambiguousVelMeas1_FR : 8;
        uint64_t FC1MHz3_FR : 16;
        uint64_t FC1MHz2_FR : 16;
        uint64_t FC1MHz1_FR : 16;
        uint64_t HostYawEst_FR : 16;
        uint64_t HostAccelLatEst_FR : 16;
        uint64_t HostAccelLongEst_FR : 16;
        uint64_t HostVelEst_FR : 16;
        uint64_t BW100KHz3_FR : 16;
        uint64_t BW100KHz2_FR : 16;
        uint64_t BW100KHz1_FR : 16;
        uint64_t TimeStamp_FR : 32;
        uint64_t CycleNumber_FR : 32;
    } signals;
};

struct canmsg_mk_target_detection_header_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_target_detection_header_fr_data_t data;
};

//=====================================================================================//


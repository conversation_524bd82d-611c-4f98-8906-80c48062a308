#include "parser_typedef.h"
#pragma once
//=====================================================================================//
enum can_ecu_id
{
    eCAN_ECU_IVI = 0,
    eCAN_ECU_MK_FC = 1,
    eCAN_ECU_MK_FL = 2,
    eCAN_ECU_MK_FR = 3,
    eCAN_ECU_MK_RL = 4,
    eCAN_ECU_MK_RR = 5,
    eCAN_ECU_PC = 6,
    /////,
    eCAN_ECU_NUM = 7,
};

#define CANMSG_MK_NON_INF_DETECTION_0_FR                         (0x000000|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_10_FR                        (0x000100|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_11_FR                        (0x000200|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_12_FR                        (0x000300|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_13_FR                        (0x000400|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_14_FR                        (0x000500|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_15_FR                        (0x000600|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_16_FR                        (0x000700|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_17_FR                        (0x000800|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_18_FR                        (0x000900|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_19_FR                        (0x000A00|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_1_FR                         (0x000B00|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_20_FR                        (0x000C00|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_21_FR                        (0x000D00|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_22_FR                        (0x000E00|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_23_FR                        (0x000F00|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_24_FR                        (0x001000|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_2_FR                         (0x001100|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_3_FR                         (0x001200|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_4_FR                         (0x001300|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_5_FR                         (0x001400|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_6_FR                         (0x001500|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_7_FR                         (0x001600|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_8_FR                         (0x001700|(UID_CLASS_CAN<<4))
#define CANMSG_MK_NON_INF_DETECTION_9_FR                         (0x001800|(UID_CLASS_CAN<<4))
#define CANMSG_MK_TARGET_DETECTION_HEADER_FR                     (0x001900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__AZIMUTH_NI_0_FR        (0x001A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__AZIMUTH_NI_1_FR        (0x001B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__AZIMUTH_NI_2_FR        (0x001C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__AZIMUTH_NI_3_FR        (0x001D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__AZIMUTH_NI_4_FR        (0x001E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__BEAM_NI_0_FR           (0x001F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__BEAM_NI_1_FR           (0x002000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__BEAM_NI_2_FR           (0x002100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__BEAM_NI_3_FR           (0x002200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__BEAM_NI_4_FR           (0x002300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGDOPPLER_NI_0_FR     (0x002400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGDOPPLER_NI_1_FR     (0x002500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGDOPPLER_NI_2_FR     (0x002600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGDOPPLER_NI_3_FR     (0x002700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGDOPPLER_NI_4_FR     (0x002800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGRANGE_NI_0_FR       (0x002900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGRANGE_NI_1_FR       (0x002A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGRANGE_NI_2_FR       (0x002B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGRANGE_NI_3_FR       (0x002C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__COGRANGE_NI_4_FR       (0x002D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__NOINFRASTRUCTURE_NI_0_FR (0x002E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__NOINFRASTRUCTURE_NI_1_FR (0x002F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__NOINFRASTRUCTURE_NI_2_FR (0x003000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__NOINFRASTRUCTURE_NI_3_FR (0x003100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__NOINFRASTRUCTURE_NI_4_FR (0x003200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__POWERDB_NI_0_FR        (0x003300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__POWERDB_NI_1_FR        (0x003400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__POWERDB_NI_2_FR        (0x003500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__POWERDB_NI_3_FR        (0x003600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__POWERDB_NI_4_FR        (0x003700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__RESERVE1_1BIT_NI_0_FR  (0x003800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__RESERVE1_1BIT_NI_1_FR  (0x003900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__RESERVE1_1BIT_NI_2_FR  (0x003A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__RESERVE1_1BIT_NI_3_FR  (0x003B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__RESERVE1_1BIT_NI_4_FR  (0x003C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__SNRDB_NI_0_FR          (0x003D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__SNRDB_NI_1_FR          (0x003E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__SNRDB_NI_2_FR          (0x003F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__SNRDB_NI_3_FR          (0x004000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__SNRDB_NI_4_FR          (0x004100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__STDAZIMUTH_NI_0_FR     (0x004200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__STDAZIMUTH_NI_1_FR     (0x004300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__STDAZIMUTH_NI_2_FR     (0x004400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__STDAZIMUTH_NI_3_FR     (0x004500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__STDAZIMUTH_NI_4_FR     (0x004600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__VALIDXBEAM_NI_0_FR     (0x004700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__VALIDXBEAM_NI_1_FR     (0x004800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__VALIDXBEAM_NI_2_FR     (0x004900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__VALIDXBEAM_NI_3_FR     (0x004A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__VALIDXBEAM_NI_4_FR     (0x004B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WDOPPLER10DB_NI_0_FR   (0x004C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WDOPPLER10DB_NI_1_FR   (0x004D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WDOPPLER10DB_NI_2_FR   (0x004E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WDOPPLER10DB_NI_3_FR   (0x004F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WDOPPLER10DB_NI_4_FR   (0x005000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WRANGE10DB_NI_0_FR     (0x005100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WRANGE10DB_NI_1_FR     (0x005200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WRANGE10DB_NI_2_FR     (0x005300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WRANGE10DB_NI_3_FR     (0x005400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_0_FR__WRANGE10DB_NI_4_FR     (0x005500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__AZIMUTH_NI_50_FR      (0x005600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__AZIMUTH_NI_51_FR      (0x005700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__AZIMUTH_NI_52_FR      (0x005800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__AZIMUTH_NI_53_FR      (0x005900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__AZIMUTH_NI_54_FR      (0x005A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__BEAM_NI_50_FR         (0x005B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__BEAM_NI_51_FR         (0x005C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__BEAM_NI_52_FR         (0x005D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__BEAM_NI_53_FR         (0x005E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__BEAM_NI_54_FR         (0x005F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGDOPPLER_NI_50_FR   (0x006000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGDOPPLER_NI_51_FR   (0x006100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGDOPPLER_NI_52_FR   (0x006200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGDOPPLER_NI_53_FR   (0x006300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGDOPPLER_NI_54_FR   (0x006400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGRANGE_NI_50_FR     (0x006500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGRANGE_NI_51_FR     (0x006600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGRANGE_NI_52_FR     (0x006700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGRANGE_NI_53_FR     (0x006800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__COGRANGE_NI_54_FR     (0x006900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__NOINFRASTRUCTURE_NI_50_FR (0x006A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__NOINFRASTRUCTURE_NI_51_FR (0x006B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__NOINFRASTRUCTURE_NI_52_FR (0x006C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__NOINFRASTRUCTURE_NI_53_FR (0x006D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__NOINFRASTRUCTURE_NI_54_FR (0x006E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__POWERDB_NI_50_FR      (0x006F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__POWERDB_NI_51_FR      (0x007000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__POWERDB_NI_52_FR      (0x007100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__POWERDB_NI_53_FR      (0x007200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__POWERDB_NI_54_FR      (0x007300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__RESERVE1_1BIT_NI_50_FR (0x007400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__RESERVE1_1BIT_NI_51_FR (0x007500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__RESERVE1_1BIT_NI_52_FR (0x007600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__RESERVE1_1BIT_NI_53_FR (0x007700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__RESERVE1_1BIT_NI_54_FR (0x007800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__SNRDB_NI_50_FR        (0x007900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__SNRDB_NI_51_FR        (0x007A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__SNRDB_NI_52_FR        (0x007B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__SNRDB_NI_53_FR        (0x007C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__SNRDB_NI_54_FR        (0x007D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__STDAZIMUTH_NI_50_FR   (0x007E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__STDAZIMUTH_NI_51_FR   (0x007F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__STDAZIMUTH_NI_52_FR   (0x008000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__STDAZIMUTH_NI_53_FR   (0x008100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__STDAZIMUTH_NI_54_FR   (0x008200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__VALIDXBEAM_NI_50_FR   (0x008300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__VALIDXBEAM_NI_51_FR   (0x008400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__VALIDXBEAM_NI_52_FR   (0x008500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__VALIDXBEAM_NI_53_FR   (0x008600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__VALIDXBEAM_NI_54_FR   (0x008700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WDOPPLER10DB_NI_50_FR (0x008800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WDOPPLER10DB_NI_51_FR (0x008900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WDOPPLER10DB_NI_52_FR (0x008A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WDOPPLER10DB_NI_53_FR (0x008B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WDOPPLER10DB_NI_54_FR (0x008C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WRANGE10DB_NI_50_FR   (0x008D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WRANGE10DB_NI_51_FR   (0x008E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WRANGE10DB_NI_52_FR   (0x008F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WRANGE10DB_NI_53_FR   (0x009000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_10_FR__WRANGE10DB_NI_54_FR   (0x009100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__AZIMUTH_NI_55_FR      (0x009200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__AZIMUTH_NI_56_FR      (0x009300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__AZIMUTH_NI_57_FR      (0x009400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__AZIMUTH_NI_58_FR      (0x009500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__AZIMUTH_NI_59_FR      (0x009600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__BEAM_NI_55_FR         (0x009700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__BEAM_NI_56_FR         (0x009800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__BEAM_NI_57_FR         (0x009900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__BEAM_NI_58_FR         (0x009A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__BEAM_NI_59_FR         (0x009B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGDOPPLER_NI_55_FR   (0x009C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGDOPPLER_NI_56_FR   (0x009D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGDOPPLER_NI_57_FR   (0x009E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGDOPPLER_NI_58_FR   (0x009F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGDOPPLER_NI_59_FR   (0x00A000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGRANGE_NI_55_FR     (0x00A100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGRANGE_NI_56_FR     (0x00A200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGRANGE_NI_57_FR     (0x00A300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGRANGE_NI_58_FR     (0x00A400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__COGRANGE_NI_59_FR     (0x00A500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__NOINFRASTRUCTURE_NI_55_FR (0x00A600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__NOINFRASTRUCTURE_NI_56_FR (0x00A700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__NOINFRASTRUCTURE_NI_57_FR (0x00A800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__NOINFRASTRUCTURE_NI_58_FR (0x00A900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__NOINFRASTRUCTURE_NI_59_FR (0x00AA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__POWERDB_NI_55_FR      (0x00AB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__POWERDB_NI_56_FR      (0x00AC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__POWERDB_NI_57_FR      (0x00AD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__POWERDB_NI_58_FR      (0x00AE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__POWERDB_NI_59_FR      (0x00AF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__RESERVE1_1BIT_NI_55_FR (0x00B000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__RESERVE1_1BIT_NI_56_FR (0x00B100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__RESERVE1_1BIT_NI_57_FR (0x00B200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__RESERVE1_1BIT_NI_58_FR (0x00B300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__RESERVE1_1BIT_NI_59_FR (0x00B400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__SNRDB_NI_55_FR        (0x00B500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__SNRDB_NI_56_FR        (0x00B600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__SNRDB_NI_57_FR        (0x00B700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__SNRDB_NI_58_FR        (0x00B800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__SNRDB_NI_59_FR        (0x00B900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__STDAZIMUTH_NI_55_FR   (0x00BA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__STDAZIMUTH_NI_56_FR   (0x00BB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__STDAZIMUTH_NI_57_FR   (0x00BC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__STDAZIMUTH_NI_58_FR   (0x00BD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__STDAZIMUTH_NI_59_FR   (0x00BE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__VALIDXBEAM_NI_55_FR   (0x00BF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__VALIDXBEAM_NI_56_FR   (0x00C000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__VALIDXBEAM_NI_57_FR   (0x00C100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__VALIDXBEAM_NI_58_FR   (0x00C200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__VALIDXBEAM_NI_59_FR   (0x00C300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WDOPPLER10DB_NI_55_FR (0x00C400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WDOPPLER10DB_NI_56_FR (0x00C500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WDOPPLER10DB_NI_57_FR (0x00C600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WDOPPLER10DB_NI_58_FR (0x00C700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WDOPPLER10DB_NI_59_FR (0x00C800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WRANGE10DB_NI_55_FR   (0x00C900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WRANGE10DB_NI_56_FR   (0x00CA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WRANGE10DB_NI_57_FR   (0x00CB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WRANGE10DB_NI_58_FR   (0x00CC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_11_FR__WRANGE10DB_NI_59_FR   (0x00CD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__AZIMUTH_NI_60_FR      (0x00CE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__AZIMUTH_NI_61_FR      (0x00CF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__AZIMUTH_NI_62_FR      (0x00D000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__AZIMUTH_NI_63_FR      (0x00D100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__AZIMUTH_NI_64_FR      (0x00D200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__BEAM_NI_60_FR         (0x00D300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__BEAM_NI_61_FR         (0x00D400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__BEAM_NI_62_FR         (0x00D500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__BEAM_NI_63_FR         (0x00D600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__BEAM_NI_64_FR         (0x00D700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGDOPPLER_NI_60_FR   (0x00D800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGDOPPLER_NI_61_FR   (0x00D900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGDOPPLER_NI_62_FR   (0x00DA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGDOPPLER_NI_63_FR   (0x00DB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGDOPPLER_NI_64_FR   (0x00DC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGRANGE_NI_60_FR     (0x00DD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGRANGE_NI_61_FR     (0x00DE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGRANGE_NI_62_FR     (0x00DF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGRANGE_NI_63_FR     (0x00E000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__COGRANGE_NI_64_FR     (0x00E100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__NOINFRASTRUCTURE_NI_60_FR (0x00E200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__NOINFRASTRUCTURE_NI_61_FR (0x00E300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__NOINFRASTRUCTURE_NI_62_FR (0x00E400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__NOINFRASTRUCTURE_NI_63_FR (0x00E500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__NOINFRASTRUCTURE_NI_64_FR (0x00E600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__POWERDB_NI_60_FR      (0x00E700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__POWERDB_NI_61_FR      (0x00E800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__POWERDB_NI_62_FR      (0x00E900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__POWERDB_NI_63_FR      (0x00EA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__POWERDB_NI_64_FR      (0x00EB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__RESERVE1_1BIT_NI_60_FR (0x00EC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__RESERVE1_1BIT_NI_61_FR (0x00ED00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__RESERVE1_1BIT_NI_62_FR (0x00EE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__RESERVE1_1BIT_NI_63_FR (0x00EF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__RESERVE1_1BIT_NI_64_FR (0x00F000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__SNRDB_NI_60_FR        (0x00F100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__SNRDB_NI_61_FR        (0x00F200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__SNRDB_NI_62_FR        (0x00F300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__SNRDB_NI_63_FR        (0x00F400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__SNRDB_NI_64_FR        (0x00F500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__STDAZIMUTH_NI_60_FR   (0x00F600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__STDAZIMUTH_NI_61_FR   (0x00F700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__STDAZIMUTH_NI_62_FR   (0x00F800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__STDAZIMUTH_NI_63_FR   (0x00F900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__STDAZIMUTH_NI_64_FR   (0x00FA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__VALIDXBEAM_NI_60_FR   (0x00FB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__VALIDXBEAM_NI_61_FR   (0x00FC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__VALIDXBEAM_NI_62_FR   (0x00FD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__VALIDXBEAM_NI_63_FR   (0x00FE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__VALIDXBEAM_NI_64_FR   (0x00FF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WDOPPLER10DB_NI_60_FR (0x010000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WDOPPLER10DB_NI_61_FR (0x010100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WDOPPLER10DB_NI_62_FR (0x010200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WDOPPLER10DB_NI_63_FR (0x010300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WDOPPLER10DB_NI_64_FR (0x010400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WRANGE10DB_NI_60_FR   (0x010500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WRANGE10DB_NI_61_FR   (0x010600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WRANGE10DB_NI_62_FR   (0x010700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WRANGE10DB_NI_63_FR   (0x010800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_12_FR__WRANGE10DB_NI_64_FR   (0x010900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__AZIMUTH_NI_65_FR      (0x010A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__AZIMUTH_NI_66_FR      (0x010B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__AZIMUTH_NI_67_FR      (0x010C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__AZIMUTH_NI_68_FR      (0x010D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__AZIMUTH_NI_69_FR      (0x010E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__BEAM_NI_65_FR         (0x010F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__BEAM_NI_66_FR         (0x011000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__BEAM_NI_67_FR         (0x011100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__BEAM_NI_68_FR         (0x011200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__BEAM_NI_69_FR         (0x011300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGDOPPLER_NI_65_FR   (0x011400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGDOPPLER_NI_66_FR   (0x011500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGDOPPLER_NI_67_FR   (0x011600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGDOPPLER_NI_68_FR   (0x011700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGDOPPLER_NI_69_FR   (0x011800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGRANGE_NI_65_FR     (0x011900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGRANGE_NI_66_FR     (0x011A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGRANGE_NI_67_FR     (0x011B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGRANGE_NI_68_FR     (0x011C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__COGRANGE_NI_69_FR     (0x011D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__NOINFRASTRUCTURE_NI_65_FR (0x011E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__NOINFRASTRUCTURE_NI_66_FR (0x011F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__NOINFRASTRUCTURE_NI_67_FR (0x012000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__NOINFRASTRUCTURE_NI_68_FR (0x012100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__NOINFRASTRUCTURE_NI_69_FR (0x012200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__POWERDB_NI_65_FR      (0x012300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__POWERDB_NI_66_FR      (0x012400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__POWERDB_NI_67_FR      (0x012500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__POWERDB_NI_68_FR      (0x012600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__POWERDB_NI_69_FR      (0x012700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__RESERVE1_1BIT_NI_65_FR (0x012800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__RESERVE1_1BIT_NI_66_FR (0x012900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__RESERVE1_1BIT_NI_67_FR (0x012A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__RESERVE1_1BIT_NI_68_FR (0x012B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__RESERVE1_1BIT_NI_69_FR (0x012C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__SNRDB_NI_65_FR        (0x012D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__SNRDB_NI_66_FR        (0x012E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__SNRDB_NI_67_FR        (0x012F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__SNRDB_NI_68_FR        (0x013000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__SNRDB_NI_69_FR        (0x013100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__STDAZIMUTH_NI_65_FR   (0x013200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__STDAZIMUTH_NI_66_FR   (0x013300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__STDAZIMUTH_NI_67_FR   (0x013400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__STDAZIMUTH_NI_68_FR   (0x013500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__STDAZIMUTH_NI_69_FR   (0x013600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__VALIDXBEAM_NI_65_FR   (0x013700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__VALIDXBEAM_NI_66_FR   (0x013800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__VALIDXBEAM_NI_67_FR   (0x013900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__VALIDXBEAM_NI_68_FR   (0x013A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__VALIDXBEAM_NI_69_FR   (0x013B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WDOPPLER10DB_NI_65_FR (0x013C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WDOPPLER10DB_NI_66_FR (0x013D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WDOPPLER10DB_NI_67_FR (0x013E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WDOPPLER10DB_NI_68_FR (0x013F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WDOPPLER10DB_NI_69_FR (0x014000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WRANGE10DB_NI_65_FR   (0x014100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WRANGE10DB_NI_66_FR   (0x014200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WRANGE10DB_NI_67_FR   (0x014300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WRANGE10DB_NI_68_FR   (0x014400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_13_FR__WRANGE10DB_NI_69_FR   (0x014500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__AZIMUTH_NI_70_FR      (0x014600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__AZIMUTH_NI_71_FR      (0x014700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__AZIMUTH_NI_72_FR      (0x014800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__AZIMUTH_NI_73_FR      (0x014900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__AZIMUTH_NI_74_FR      (0x014A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__BEAM_NI_70_FR         (0x014B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__BEAM_NI_71_FR         (0x014C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__BEAM_NI_72_FR         (0x014D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__BEAM_NI_73_FR         (0x014E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__BEAM_NI_74_FR         (0x014F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGDOPPLER_NI_70_FR   (0x015000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGDOPPLER_NI_71_FR   (0x015100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGDOPPLER_NI_72_FR   (0x015200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGDOPPLER_NI_73_FR   (0x015300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGDOPPLER_NI_74_FR   (0x015400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGRANGE_NI_70_FR     (0x015500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGRANGE_NI_71_FR     (0x015600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGRANGE_NI_72_FR     (0x015700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGRANGE_NI_73_FR     (0x015800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__COGRANGE_NI_74_FR     (0x015900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__NOINFRASTRUCTURE_NI_70_FR (0x015A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__NOINFRASTRUCTURE_NI_71_FR (0x015B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__NOINFRASTRUCTURE_NI_72_FR (0x015C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__NOINFRASTRUCTURE_NI_73_FR (0x015D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__NOINFRASTRUCTURE_NI_74_FR (0x015E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__POWERDB_NI_70_FR      (0x015F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__POWERDB_NI_71_FR      (0x016000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__POWERDB_NI_72_FR      (0x016100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__POWERDB_NI_73_FR      (0x016200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__POWERDB_NI_74_FR      (0x016300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__RESERVE1_1BIT_NI_70_FR (0x016400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__RESERVE1_1BIT_NI_71_FR (0x016500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__RESERVE1_1BIT_NI_72_FR (0x016600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__RESERVE1_1BIT_NI_73_FR (0x016700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__RESERVE1_1BIT_NI_74_FR (0x016800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__SNRDB_NI_70_FR        (0x016900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__SNRDB_NI_71_FR        (0x016A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__SNRDB_NI_72_FR        (0x016B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__SNRDB_NI_73_FR        (0x016C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__SNRDB_NI_74_FR        (0x016D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__STDAZIMUTH_NI_70_FR   (0x016E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__STDAZIMUTH_NI_71_FR   (0x016F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__STDAZIMUTH_NI_72_FR   (0x017000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__STDAZIMUTH_NI_73_FR   (0x017100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__STDAZIMUTH_NI_74_FR   (0x017200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__VALIDXBEAM_NI_70_FR   (0x017300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__VALIDXBEAM_NI_71_FR   (0x017400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__VALIDXBEAM_NI_72_FR   (0x017500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__VALIDXBEAM_NI_73_FR   (0x017600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__VALIDXBEAM_NI_74_FR   (0x017700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WDOPPLER10DB_NI_70_FR (0x017800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WDOPPLER10DB_NI_71_FR (0x017900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WDOPPLER10DB_NI_72_FR (0x017A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WDOPPLER10DB_NI_73_FR (0x017B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WDOPPLER10DB_NI_74_FR (0x017C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WRANGE10DB_NI_70_FR   (0x017D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WRANGE10DB_NI_71_FR   (0x017E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WRANGE10DB_NI_72_FR   (0x017F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WRANGE10DB_NI_73_FR   (0x018000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_14_FR__WRANGE10DB_NI_74_FR   (0x018100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__AZIMUTH_NI_75_FR      (0x018200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__AZIMUTH_NI_76_FR      (0x018300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__AZIMUTH_NI_77_FR      (0x018400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__AZIMUTH_NI_78_FR      (0x018500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__AZIMUTH_NI_79_FR      (0x018600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__BEAM_NI_75_FR         (0x018700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__BEAM_NI_76_FR         (0x018800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__BEAM_NI_77_FR         (0x018900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__BEAM_NI_78_FR         (0x018A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__BEAM_NI_79_FR         (0x018B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGDOPPLER_NI_75_FR   (0x018C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGDOPPLER_NI_76_FR   (0x018D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGDOPPLER_NI_77_FR   (0x018E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGDOPPLER_NI_78_FR   (0x018F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGDOPPLER_NI_79_FR   (0x019000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGRANGE_NI_75_FR     (0x019100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGRANGE_NI_76_FR     (0x019200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGRANGE_NI_77_FR     (0x019300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGRANGE_NI_78_FR     (0x019400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__COGRANGE_NI_79_FR     (0x019500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__NOINFRASTRUCTURE_NI_75_FR (0x019600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__NOINFRASTRUCTURE_NI_76_FR (0x019700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__NOINFRASTRUCTURE_NI_77_FR (0x019800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__NOINFRASTRUCTURE_NI_78_FR (0x019900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__NOINFRASTRUCTURE_NI_79_FR (0x019A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__POWERDB_NI_75_FR      (0x019B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__POWERDB_NI_76_FR      (0x019C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__POWERDB_NI_77_FR      (0x019D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__POWERDB_NI_78_FR      (0x019E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__POWERDB_NI_79_FR      (0x019F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__RESERVE1_1BIT_NI_75_FR (0x01A000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__RESERVE1_1BIT_NI_76_FR (0x01A100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__RESERVE1_1BIT_NI_77_FR (0x01A200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__RESERVE1_1BIT_NI_78_FR (0x01A300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__RESERVE1_1BIT_NI_79_FR (0x01A400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__SNRDB_NI_75_FR        (0x01A500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__SNRDB_NI_76_FR        (0x01A600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__SNRDB_NI_77_FR        (0x01A700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__SNRDB_NI_78_FR        (0x01A800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__SNRDB_NI_79_FR        (0x01A900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__STDAZIMUTH_NI_75_FR   (0x01AA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__STDAZIMUTH_NI_76_FR   (0x01AB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__STDAZIMUTH_NI_77_FR   (0x01AC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__STDAZIMUTH_NI_78_FR   (0x01AD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__STDAZIMUTH_NI_79_FR   (0x01AE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__VALIDXBEAM_NI_75_FR   (0x01AF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__VALIDXBEAM_NI_76_FR   (0x01B000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__VALIDXBEAM_NI_77_FR   (0x01B100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__VALIDXBEAM_NI_78_FR   (0x01B200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__VALIDXBEAM_NI_79_FR   (0x01B300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WDOPPLER10DB_NI_75_FR (0x01B400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WDOPPLER10DB_NI_76_FR (0x01B500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WDOPPLER10DB_NI_77_FR (0x01B600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WDOPPLER10DB_NI_78_FR (0x01B700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WDOPPLER10DB_NI_79_FR (0x01B800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WRANGE10DB_NI_75_FR   (0x01B900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WRANGE10DB_NI_76_FR   (0x01BA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WRANGE10DB_NI_77_FR   (0x01BB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WRANGE10DB_NI_78_FR   (0x01BC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_15_FR__WRANGE10DB_NI_79_FR   (0x01BD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__AZIMUTH_NI_80_FR      (0x01BE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__AZIMUTH_NI_81_FR      (0x01BF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__AZIMUTH_NI_82_FR      (0x01C000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__AZIMUTH_NI_83_FR      (0x01C100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__AZIMUTH_NI_84_FR      (0x01C200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__BEAM_NI_80_FR         (0x01C300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__BEAM_NI_81_FR         (0x01C400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__BEAM_NI_82_FR         (0x01C500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__BEAM_NI_83_FR         (0x01C600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__BEAM_NI_84_FR         (0x01C700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGDOPPLER_NI_80_FR   (0x01C800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGDOPPLER_NI_81_FR   (0x01C900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGDOPPLER_NI_82_FR   (0x01CA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGDOPPLER_NI_83_FR   (0x01CB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGDOPPLER_NI_84_FR   (0x01CC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGRANGE_NI_80_FR     (0x01CD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGRANGE_NI_81_FR     (0x01CE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGRANGE_NI_82_FR     (0x01CF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGRANGE_NI_83_FR     (0x01D000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__COGRANGE_NI_84_FR     (0x01D100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__NOINFRASTRUCTURE_NI_80_FR (0x01D200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__NOINFRASTRUCTURE_NI_81_FR (0x01D300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__NOINFRASTRUCTURE_NI_82_FR (0x01D400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__NOINFRASTRUCTURE_NI_83_FR (0x01D500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__NOINFRASTRUCTURE_NI_84_FR (0x01D600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__POWERDB_NI_80_FR      (0x01D700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__POWERDB_NI_81_FR      (0x01D800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__POWERDB_NI_82_FR      (0x01D900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__POWERDB_NI_83_FR      (0x01DA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__POWERDB_NI_84_FR      (0x01DB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__RESERVE1_1BIT_NI_80_FR (0x01DC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__RESERVE1_1BIT_NI_81_FR (0x01DD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__RESERVE1_1BIT_NI_82_FR (0x01DE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__RESERVE1_1BIT_NI_83_FR (0x01DF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__RESERVE1_1BIT_NI_84_FR (0x01E000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__SNRDB_NI_80_FR        (0x01E100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__SNRDB_NI_81_FR        (0x01E200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__SNRDB_NI_82_FR        (0x01E300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__SNRDB_NI_83_FR        (0x01E400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__SNRDB_NI_84_FR        (0x01E500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__STDAZIMUTH_NI_80_FR   (0x01E600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__STDAZIMUTH_NI_81_FR   (0x01E700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__STDAZIMUTH_NI_82_FR   (0x01E800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__STDAZIMUTH_NI_83_FR   (0x01E900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__STDAZIMUTH_NI_84_FR   (0x01EA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__VALIDXBEAM_NI_80_FR   (0x01EB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__VALIDXBEAM_NI_81_FR   (0x01EC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__VALIDXBEAM_NI_82_FR   (0x01ED00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__VALIDXBEAM_NI_83_FR   (0x01EE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__VALIDXBEAM_NI_84_FR   (0x01EF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WDOPPLER10DB_NI_80_FR (0x01F000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WDOPPLER10DB_NI_81_FR (0x01F100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WDOPPLER10DB_NI_82_FR (0x01F200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WDOPPLER10DB_NI_83_FR (0x01F300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WDOPPLER10DB_NI_84_FR (0x01F400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WRANGE10DB_NI_80_FR   (0x01F500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WRANGE10DB_NI_81_FR   (0x01F600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WRANGE10DB_NI_82_FR   (0x01F700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WRANGE10DB_NI_83_FR   (0x01F800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_16_FR__WRANGE10DB_NI_84_FR   (0x01F900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__AZIMUTH_NI_85_FR      (0x01FA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__AZIMUTH_NI_86_FR      (0x01FB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__AZIMUTH_NI_87_FR      (0x01FC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__AZIMUTH_NI_88_FR      (0x01FD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__AZIMUTH_NI_89_FR      (0x01FE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__BEAM_NI_85_FR         (0x01FF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__BEAM_NI_86_FR         (0x020000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__BEAM_NI_87_FR         (0x020100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__BEAM_NI_88_FR         (0x020200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__BEAM_NI_89_FR         (0x020300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGDOPPLER_NI_85_FR   (0x020400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGDOPPLER_NI_86_FR   (0x020500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGDOPPLER_NI_87_FR   (0x020600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGDOPPLER_NI_88_FR   (0x020700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGDOPPLER_NI_89_FR   (0x020800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGRANGE_NI_85_FR     (0x020900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGRANGE_NI_86_FR     (0x020A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGRANGE_NI_87_FR     (0x020B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGRANGE_NI_88_FR     (0x020C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__COGRANGE_NI_89_FR     (0x020D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__NOINFRASTRUCTURE_NI_85_FR (0x020E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__NOINFRASTRUCTURE_NI_86_FR (0x020F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__NOINFRASTRUCTURE_NI_87_FR (0x021000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__NOINFRASTRUCTURE_NI_88_FR (0x021100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__NOINFRASTRUCTURE_NI_89_FR (0x021200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__POWERDB_NI_85_FR      (0x021300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__POWERDB_NI_86_FR      (0x021400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__POWERDB_NI_87_FR      (0x021500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__POWERDB_NI_88_FR      (0x021600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__POWERDB_NI_89_FR      (0x021700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__RESERVE1_1BIT_NI_85_FR (0x021800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__RESERVE1_1BIT_NI_86_FR (0x021900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__RESERVE1_1BIT_NI_87_FR (0x021A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__RESERVE1_1BIT_NI_88_FR (0x021B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__RESERVE1_1BIT_NI_89_FR (0x021C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__SNRDB_NI_85_FR        (0x021D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__SNRDB_NI_86_FR        (0x021E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__SNRDB_NI_87_FR        (0x021F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__SNRDB_NI_88_FR        (0x022000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__SNRDB_NI_89_FR        (0x022100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__STDAZIMUTH_NI_85_FR   (0x022200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__STDAZIMUTH_NI_86_FR   (0x022300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__STDAZIMUTH_NI_87_FR   (0x022400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__STDAZIMUTH_NI_88_FR   (0x022500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__STDAZIMUTH_NI_89_FR   (0x022600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__VALIDXBEAM_NI_85_FR   (0x022700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__VALIDXBEAM_NI_86_FR   (0x022800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__VALIDXBEAM_NI_87_FR   (0x022900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__VALIDXBEAM_NI_88_FR   (0x022A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__VALIDXBEAM_NI_89_FR   (0x022B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WDOPPLER10DB_NI_85_FR (0x022C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WDOPPLER10DB_NI_86_FR (0x022D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WDOPPLER10DB_NI_87_FR (0x022E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WDOPPLER10DB_NI_88_FR (0x022F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WDOPPLER10DB_NI_89_FR (0x023000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WRANGE10DB_NI_85_FR   (0x023100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WRANGE10DB_NI_86_FR   (0x023200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WRANGE10DB_NI_87_FR   (0x023300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WRANGE10DB_NI_88_FR   (0x023400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_17_FR__WRANGE10DB_NI_89_FR   (0x023500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__AZIMUTH_NI_90_FR      (0x023600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__AZIMUTH_NI_91_FR      (0x023700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__AZIMUTH_NI_92_FR      (0x023800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__AZIMUTH_NI_93_FR      (0x023900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__AZIMUTH_NI_94_FR      (0x023A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__BEAM_NI_90_FR         (0x023B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__BEAM_NI_91_FR         (0x023C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__BEAM_NI_92_FR         (0x023D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__BEAM_NI_93_FR         (0x023E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__BEAM_NI_94_FR         (0x023F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGDOPPLER_NI_90_FR   (0x024000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGDOPPLER_NI_91_FR   (0x024100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGDOPPLER_NI_92_FR   (0x024200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGDOPPLER_NI_93_FR   (0x024300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGDOPPLER_NI_94_FR   (0x024400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGRANGE_NI_90_FR     (0x024500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGRANGE_NI_91_FR     (0x024600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGRANGE_NI_92_FR     (0x024700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGRANGE_NI_93_FR     (0x024800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__COGRANGE_NI_94_FR     (0x024900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__NOINFRASTRUCTURE_NI_90_FR (0x024A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__NOINFRASTRUCTURE_NI_91_FR (0x024B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__NOINFRASTRUCTURE_NI_92_FR (0x024C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__NOINFRASTRUCTURE_NI_93_FR (0x024D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__NOINFRASTRUCTURE_NI_94_FR (0x024E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__POWERDB_NI_90_FR      (0x024F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__POWERDB_NI_91_FR      (0x025000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__POWERDB_NI_92_FR      (0x025100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__POWERDB_NI_93_FR      (0x025200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__POWERDB_NI_94_FR      (0x025300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__RESERVE1_1BIT_NI_90_FR (0x025400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__RESERVE1_1BIT_NI_91_FR (0x025500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__RESERVE1_1BIT_NI_92_FR (0x025600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__RESERVE1_1BIT_NI_93_FR (0x025700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__RESERVE1_1BIT_NI_94_FR (0x025800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__SNRDB_NI_90_FR        (0x025900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__SNRDB_NI_91_FR        (0x025A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__SNRDB_NI_92_FR        (0x025B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__SNRDB_NI_93_FR        (0x025C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__SNRDB_NI_94_FR        (0x025D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__STDAZIMUTH_NI_90_FR   (0x025E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__STDAZIMUTH_NI_91_FR   (0x025F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__STDAZIMUTH_NI_92_FR   (0x026000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__STDAZIMUTH_NI_93_FR   (0x026100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__STDAZIMUTH_NI_94_FR   (0x026200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__VALIDXBEAM_NI_90_FR   (0x026300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__VALIDXBEAM_NI_91_FR   (0x026400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__VALIDXBEAM_NI_92_FR   (0x026500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__VALIDXBEAM_NI_93_FR   (0x026600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__VALIDXBEAM_NI_94_FR   (0x026700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WDOPPLER10DB_NI_90_FR (0x026800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WDOPPLER10DB_NI_91_FR (0x026900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WDOPPLER10DB_NI_92_FR (0x026A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WDOPPLER10DB_NI_93_FR (0x026B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WDOPPLER10DB_NI_94_FR (0x026C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WRANGE10DB_NI_90_FR   (0x026D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WRANGE10DB_NI_91_FR   (0x026E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WRANGE10DB_NI_92_FR   (0x026F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WRANGE10DB_NI_93_FR   (0x027000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_18_FR__WRANGE10DB_NI_94_FR   (0x027100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__AZIMUTH_NI_95_FR      (0x027200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__AZIMUTH_NI_96_FR      (0x027300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__AZIMUTH_NI_97_FR      (0x027400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__AZIMUTH_NI_98_FR      (0x027500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__AZIMUTH_NI_99_FR      (0x027600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__BEAM_NI_95_FR         (0x027700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__BEAM_NI_96_FR         (0x027800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__BEAM_NI_97_FR         (0x027900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__BEAM_NI_98_FR         (0x027A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__BEAM_NI_99_FR         (0x027B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGDOPPLER_NI_95_FR   (0x027C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGDOPPLER_NI_96_FR   (0x027D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGDOPPLER_NI_97_FR   (0x027E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGDOPPLER_NI_98_FR   (0x027F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGDOPPLER_NI_99_FR   (0x028000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGRANGE_NI_95_FR     (0x028100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGRANGE_NI_96_FR     (0x028200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGRANGE_NI_97_FR     (0x028300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGRANGE_NI_98_FR     (0x028400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__COGRANGE_NI_99_FR     (0x028500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__NOINFRASTRUCTURE_NI_95_FR (0x028600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__NOINFRASTRUCTURE_NI_96_FR (0x028700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__NOINFRASTRUCTURE_NI_97_FR (0x028800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__NOINFRASTRUCTURE_NI_98_FR (0x028900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__NOINFRASTRUCTURE_NI_99_FR (0x028A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__POWERDB_NI_95_FR      (0x028B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__POWERDB_NI_96_FR      (0x028C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__POWERDB_NI_97_FR      (0x028D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__POWERDB_NI_98_FR      (0x028E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__POWERDB_NI_99_FR      (0x028F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__RESERVE1_1BIT_NI_95_FR (0x029000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__RESERVE1_1BIT_NI_96_FR (0x029100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__RESERVE1_1BIT_NI_97_FR (0x029200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__RESERVE1_1BIT_NI_98_FR (0x029300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__RESERVE1_1BIT_NI_99_FR (0x029400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__SNRDB_NI_95_FR        (0x029500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__SNRDB_NI_96_FR        (0x029600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__SNRDB_NI_97_FR        (0x029700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__SNRDB_NI_98_FR        (0x029800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__SNRDB_NI_99_FR        (0x029900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__STDAZIMUTH_NI_95_FR   (0x029A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__STDAZIMUTH_NI_96_FR   (0x029B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__STDAZIMUTH_NI_97_FR   (0x029C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__STDAZIMUTH_NI_98_FR   (0x029D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__STDAZIMUTH_NI_99_FR   (0x029E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__VALIDXBEAM_NI_95_FR   (0x029F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__VALIDXBEAM_NI_96_FR   (0x02A000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__VALIDXBEAM_NI_97_FR   (0x02A100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__VALIDXBEAM_NI_98_FR   (0x02A200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__VALIDXBEAM_NI_99_FR   (0x02A300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WDOPPLER10DB_NI_95_FR (0x02A400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WDOPPLER10DB_NI_96_FR (0x02A500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WDOPPLER10DB_NI_97_FR (0x02A600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WDOPPLER10DB_NI_98_FR (0x02A700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WDOPPLER10DB_NI_99_FR (0x02A800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WRANGE10DB_NI_95_FR   (0x02A900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WRANGE10DB_NI_96_FR   (0x02AA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WRANGE10DB_NI_97_FR   (0x02AB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WRANGE10DB_NI_98_FR   (0x02AC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_19_FR__WRANGE10DB_NI_99_FR   (0x02AD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__AZIMUTH_NI_5_FR        (0x02AE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__AZIMUTH_NI_6_FR        (0x02AF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__AZIMUTH_NI_7_FR        (0x02B000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__AZIMUTH_NI_8_FR        (0x02B100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__AZIMUTH_NI_9_FR        (0x02B200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__BEAM_NI_5_FR           (0x02B300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__BEAM_NI_6_FR           (0x02B400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__BEAM_NI_7_FR           (0x02B500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__BEAM_NI_8_FR           (0x02B600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__BEAM_NI_9_FR           (0x02B700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGDOPPLER_NI_5_FR     (0x02B800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGDOPPLER_NI_6_FR     (0x02B900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGDOPPLER_NI_7_FR     (0x02BA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGDOPPLER_NI_8_FR     (0x02BB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGDOPPLER_NI_9_FR     (0x02BC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGRANGE_NI_5_FR       (0x02BD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGRANGE_NI_6_FR       (0x02BE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGRANGE_NI_7_FR       (0x02BF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGRANGE_NI_8_FR       (0x02C000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__COGRANGE_NI_9_FR       (0x02C100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__NOINFRASTRUCTURE_NI_5_FR (0x02C200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__NOINFRASTRUCTURE_NI_6_FR (0x02C300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__NOINFRASTRUCTURE_NI_7_FR (0x02C400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__NOINFRASTRUCTURE_NI_8_FR (0x02C500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__NOINFRASTRUCTURE_NI_9_FR (0x02C600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__POWERDB_NI_5_FR        (0x02C700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__POWERDB_NI_6_FR        (0x02C800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__POWERDB_NI_7_FR        (0x02C900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__POWERDB_NI_8_FR        (0x02CA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__POWERDB_NI_9_FR        (0x02CB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__RESERVE1_1BIT_NI_5_FR  (0x02CC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__RESERVE1_1BIT_NI_6_FR  (0x02CD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__RESERVE1_1BIT_NI_7_FR  (0x02CE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__RESERVE1_1BIT_NI_8_FR  (0x02CF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__RESERVE1_1BIT_NI_9_FR  (0x02D000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__SNRDB_NI_5_FR          (0x02D100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__SNRDB_NI_6_FR          (0x02D200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__SNRDB_NI_7_FR          (0x02D300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__SNRDB_NI_8_FR          (0x02D400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__SNRDB_NI_9_FR          (0x02D500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__STDAZIMUTH_NI_5_FR     (0x02D600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__STDAZIMUTH_NI_6_FR     (0x02D700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__STDAZIMUTH_NI_7_FR     (0x02D800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__STDAZIMUTH_NI_8_FR     (0x02D900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__STDAZIMUTH_NI_9_FR     (0x02DA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__VALIDXBEAM_NI_5_FR     (0x02DB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__VALIDXBEAM_NI_6_FR     (0x02DC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__VALIDXBEAM_NI_7_FR     (0x02DD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__VALIDXBEAM_NI_8_FR     (0x02DE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__VALIDXBEAM_NI_9_FR     (0x02DF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WDOPPLER10DB_NI_5_FR   (0x02E000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WDOPPLER10DB_NI_6_FR   (0x02E100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WDOPPLER10DB_NI_7_FR   (0x02E200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WDOPPLER10DB_NI_8_FR   (0x02E300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WDOPPLER10DB_NI_9_FR   (0x02E400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WRANGE10DB_NI_5_FR     (0x02E500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WRANGE10DB_NI_6_FR     (0x02E600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WRANGE10DB_NI_7_FR     (0x02E700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WRANGE10DB_NI_8_FR     (0x02E800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_1_FR__WRANGE10DB_NI_9_FR     (0x02E900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__AZIMUTH_NI_100_FR     (0x02EA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__AZIMUTH_NI_101_FR     (0x02EB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__AZIMUTH_NI_102_FR     (0x02EC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__AZIMUTH_NI_103_FR     (0x02ED00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__AZIMUTH_NI_104_FR     (0x02EE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__BEAM_NI_100_FR        (0x02EF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__BEAM_NI_101_FR        (0x02F000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__BEAM_NI_102_FR        (0x02F100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__BEAM_NI_103_FR        (0x02F200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__BEAM_NI_104_FR        (0x02F300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGDOPPLER_NI_100_FR  (0x02F400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGDOPPLER_NI_101_FR  (0x02F500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGDOPPLER_NI_102_FR  (0x02F600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGDOPPLER_NI_103_FR  (0x02F700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGDOPPLER_NI_104_FR  (0x02F800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGRANGE_NI_100_FR    (0x02F900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGRANGE_NI_101_FR    (0x02FA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGRANGE_NI_102_FR    (0x02FB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGRANGE_NI_103_FR    (0x02FC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__COGRANGE_NI_104_FR    (0x02FD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__NOINFRASTRUCTURE_NI_100_FR (0x02FE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__NOINFRASTRUCTURE_NI_101_FR (0x02FF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__NOINFRASTRUCTURE_NI_102_FR (0x030000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__NOINFRASTRUCTURE_NI_103_FR (0x030100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__NOINFRASTRUCTURE_NI_104_FR (0x030200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__POWERDB_NI_100_FR     (0x030300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__POWERDB_NI_101_FR     (0x030400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__POWERDB_NI_102_FR     (0x030500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__POWERDB_NI_103_FR     (0x030600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__POWERDB_NI_104_FR     (0x030700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__RESERVE1_1BIT_NI_100_FR (0x030800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__RESERVE1_1BIT_NI_101_FR (0x030900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__RESERVE1_1BIT_NI_102_FR (0x030A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__RESERVE1_1BIT_NI_103_FR (0x030B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__RESERVE1_1BIT_NI_104_FR (0x030C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__SNRDB_NI_100_FR       (0x030D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__SNRDB_NI_101_FR       (0x030E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__SNRDB_NI_102_FR       (0x030F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__SNRDB_NI_103_FR       (0x031000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__SNRDB_NI_104_FR       (0x031100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__STDAZIMUTH_NI_100_FR  (0x031200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__STDAZIMUTH_NI_101_FR  (0x031300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__STDAZIMUTH_NI_102_FR  (0x031400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__STDAZIMUTH_NI_103_FR  (0x031500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__STDAZIMUTH_NI_104_FR  (0x031600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__VALIDXBEAM_NI_100_FR  (0x031700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__VALIDXBEAM_NI_101_FR  (0x031800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__VALIDXBEAM_NI_102_FR  (0x031900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__VALIDXBEAM_NI_103_FR  (0x031A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__VALIDXBEAM_NI_104_FR  (0x031B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WDOPPLER10DB_NI_100_FR (0x031C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WDOPPLER10DB_NI_101_FR (0x031D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WDOPPLER10DB_NI_102_FR (0x031E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WDOPPLER10DB_NI_103_FR (0x031F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WDOPPLER10DB_NI_104_FR (0x032000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WRANGE10DB_NI_100_FR  (0x032100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WRANGE10DB_NI_101_FR  (0x032200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WRANGE10DB_NI_102_FR  (0x032300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WRANGE10DB_NI_103_FR  (0x032400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_20_FR__WRANGE10DB_NI_104_FR  (0x032500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__AZIMUTH_NI_105_FR     (0x032600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__AZIMUTH_NI_106_FR     (0x032700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__AZIMUTH_NI_107_FR     (0x032800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__AZIMUTH_NI_108_FR     (0x032900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__AZIMUTH_NI_109_FR     (0x032A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__BEAM_NI_105_FR        (0x032B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__BEAM_NI_106_FR        (0x032C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__BEAM_NI_107_FR        (0x032D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__BEAM_NI_108_FR        (0x032E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__BEAM_NI_109_FR        (0x032F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGDOPPLER_NI_105_FR  (0x033000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGDOPPLER_NI_106_FR  (0x033100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGDOPPLER_NI_107_FR  (0x033200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGDOPPLER_NI_108_FR  (0x033300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGDOPPLER_NI_109_FR  (0x033400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGRANGE_NI_105_FR    (0x033500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGRANGE_NI_106_FR    (0x033600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGRANGE_NI_107_FR    (0x033700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGRANGE_NI_108_FR    (0x033800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__COGRANGE_NI_109_FR    (0x033900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__NOINFRASTRUCTURE_NI_105_FR (0x033A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__NOINFRASTRUCTURE_NI_106_FR (0x033B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__NOINFRASTRUCTURE_NI_107_FR (0x033C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__NOINFRASTRUCTURE_NI_108_FR (0x033D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__NOINFRASTRUCTURE_NI_109_FR (0x033E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__POWERDB_NI_105_FR     (0x033F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__POWERDB_NI_106_FR     (0x034000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__POWERDB_NI_107_FR     (0x034100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__POWERDB_NI_108_FR     (0x034200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__POWERDB_NI_109_FR     (0x034300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__RESERVE1_1BIT_NI_105_FR (0x034400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__RESERVE1_1BIT_NI_106_FR (0x034500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__RESERVE1_1BIT_NI_107_FR (0x034600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__RESERVE1_1BIT_NI_108_FR (0x034700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__RESERVE1_1BIT_NI_109_FR (0x034800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__SNRDB_NI_105_FR       (0x034900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__SNRDB_NI_106_FR       (0x034A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__SNRDB_NI_107_FR       (0x034B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__SNRDB_NI_108_FR       (0x034C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__SNRDB_NI_109_FR       (0x034D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__STDAZIMUTH_NI_105_FR  (0x034E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__STDAZIMUTH_NI_106_FR  (0x034F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__STDAZIMUTH_NI_107_FR  (0x035000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__STDAZIMUTH_NI_108_FR  (0x035100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__STDAZIMUTH_NI_109_FR  (0x035200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__VALIDXBEAM_NI_105_FR  (0x035300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__VALIDXBEAM_NI_106_FR  (0x035400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__VALIDXBEAM_NI_107_FR  (0x035500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__VALIDXBEAM_NI_108_FR  (0x035600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__VALIDXBEAM_NI_109_FR  (0x035700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WDOPPLER10DB_NI_105_FR (0x035800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WDOPPLER10DB_NI_106_FR (0x035900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WDOPPLER10DB_NI_107_FR (0x035A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WDOPPLER10DB_NI_108_FR (0x035B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WDOPPLER10DB_NI_109_FR (0x035C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WRANGE10DB_NI_105_FR  (0x035D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WRANGE10DB_NI_106_FR  (0x035E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WRANGE10DB_NI_107_FR  (0x035F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WRANGE10DB_NI_108_FR  (0x036000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_21_FR__WRANGE10DB_NI_109_FR  (0x036100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__AZIMUTH_NI_110_FR     (0x036200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__AZIMUTH_NI_111_FR     (0x036300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__AZIMUTH_NI_112_FR     (0x036400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__AZIMUTH_NI_113_FR     (0x036500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__AZIMUTH_NI_114_FR     (0x036600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__BEAM_NI_110_FR        (0x036700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__BEAM_NI_111_FR        (0x036800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__BEAM_NI_112_FR        (0x036900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__BEAM_NI_113_FR        (0x036A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__BEAM_NI_114_FR        (0x036B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGDOPPLER_NI_110_FR  (0x036C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGDOPPLER_NI_111_FR  (0x036D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGDOPPLER_NI_112_FR  (0x036E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGDOPPLER_NI_113_FR  (0x036F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGDOPPLER_NI_114_FR  (0x037000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGRANGE_NI_110_FR    (0x037100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGRANGE_NI_111_FR    (0x037200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGRANGE_NI_112_FR    (0x037300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGRANGE_NI_113_FR    (0x037400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__COGRANGE_NI_114_FR    (0x037500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__NOINFRASTRUCTURE_NI_110_FR (0x037600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__NOINFRASTRUCTURE_NI_111_FR (0x037700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__NOINFRASTRUCTURE_NI_112_FR (0x037800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__NOINFRASTRUCTURE_NI_113_FR (0x037900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__NOINFRASTRUCTURE_NI_114_FR (0x037A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__POWERDB_NI_110_FR     (0x037B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__POWERDB_NI_111_FR     (0x037C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__POWERDB_NI_112_FR     (0x037D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__POWERDB_NI_113_FR     (0x037E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__POWERDB_NI_114_FR     (0x037F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__RESERVE1_1BIT_NI_110_FR (0x038000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__RESERVE1_1BIT_NI_111_FR (0x038100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__RESERVE1_1BIT_NI_112_FR (0x038200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__RESERVE1_1BIT_NI_113_FR (0x038300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__RESERVE1_1BIT_NI_114_FR (0x038400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__SNRDB_NI_110_FR       (0x038500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__SNRDB_NI_111_FR       (0x038600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__SNRDB_NI_112_FR       (0x038700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__SNRDB_NI_113_FR       (0x038800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__SNRDB_NI_114_FR       (0x038900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__STDAZIMUTH_NI_110_FR  (0x038A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__STDAZIMUTH_NI_111_FR  (0x038B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__STDAZIMUTH_NI_112_FR  (0x038C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__STDAZIMUTH_NI_113_FR  (0x038D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__STDAZIMUTH_NI_114_FR  (0x038E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__VALIDXBEAM_NI_110_FR  (0x038F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__VALIDXBEAM_NI_111_FR  (0x039000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__VALIDXBEAM_NI_112_FR  (0x039100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__VALIDXBEAM_NI_113_FR  (0x039200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__VALIDXBEAM_NI_114_FR  (0x039300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WDOPPLER10DB_NI_110_FR (0x039400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WDOPPLER10DB_NI_111_FR (0x039500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WDOPPLER10DB_NI_112_FR (0x039600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WDOPPLER10DB_NI_113_FR (0x039700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WDOPPLER10DB_NI_114_FR (0x039800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WRANGE10DB_NI_110_FR  (0x039900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WRANGE10DB_NI_111_FR  (0x039A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WRANGE10DB_NI_112_FR  (0x039B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WRANGE10DB_NI_113_FR  (0x039C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_22_FR__WRANGE10DB_NI_114_FR  (0x039D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__AZIMUTH_NI_115_FR     (0x039E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__AZIMUTH_NI_116_FR     (0x039F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__AZIMUTH_NI_117_FR     (0x03A000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__AZIMUTH_NI_118_FR     (0x03A100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__AZIMUTH_NI_119_FR     (0x03A200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__BEAM_NI_115_FR        (0x03A300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__BEAM_NI_116_FR        (0x03A400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__BEAM_NI_117_FR        (0x03A500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__BEAM_NI_118_FR        (0x03A600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__BEAM_NI_119_FR        (0x03A700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGDOPPLER_NI_115_FR  (0x03A800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGDOPPLER_NI_116_FR  (0x03A900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGDOPPLER_NI_117_FR  (0x03AA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGDOPPLER_NI_118_FR  (0x03AB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGDOPPLER_NI_119_FR  (0x03AC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGRANGE_NI_115_FR    (0x03AD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGRANGE_NI_116_FR    (0x03AE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGRANGE_NI_117_FR    (0x03AF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGRANGE_NI_118_FR    (0x03B000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__COGRANGE_NI_119_FR    (0x03B100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__NOINFRASTRUCTURE_NI_115_FR (0x03B200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__NOINFRASTRUCTURE_NI_116_FR (0x03B300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__NOINFRASTRUCTURE_NI_117_FR (0x03B400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__NOINFRASTRUCTURE_NI_118_FR (0x03B500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__NOINFRASTRUCTURE_NI_119_FR (0x03B600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__POWERDB_NI_115_FR     (0x03B700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__POWERDB_NI_116_FR     (0x03B800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__POWERDB_NI_117_FR     (0x03B900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__POWERDB_NI_118_FR     (0x03BA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__POWERDB_NI_119_FR     (0x03BB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__RESERVE1_1BIT_NI_115_FR (0x03BC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__RESERVE1_1BIT_NI_116_FR (0x03BD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__RESERVE1_1BIT_NI_117_FR (0x03BE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__RESERVE1_1BIT_NI_118_FR (0x03BF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__RESERVE1_1BIT_NI_119_FR (0x03C000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__SNRDB_NI_115_FR       (0x03C100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__SNRDB_NI_116_FR       (0x03C200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__SNRDB_NI_117_FR       (0x03C300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__SNRDB_NI_118_FR       (0x03C400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__SNRDB_NI_119_FR       (0x03C500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__STDAZIMUTH_NI_115_FR  (0x03C600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__STDAZIMUTH_NI_116_FR  (0x03C700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__STDAZIMUTH_NI_117_FR  (0x03C800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__STDAZIMUTH_NI_118_FR  (0x03C900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__STDAZIMUTH_NI_119_FR  (0x03CA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__VALIDXBEAM_NI_115_FR  (0x03CB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__VALIDXBEAM_NI_116_FR  (0x03CC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__VALIDXBEAM_NI_117_FR  (0x03CD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__VALIDXBEAM_NI_118_FR  (0x03CE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__VALIDXBEAM_NI_119_FR  (0x03CF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WDOPPLER10DB_NI_115_FR (0x03D000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WDOPPLER10DB_NI_116_FR (0x03D100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WDOPPLER10DB_NI_117_FR (0x03D200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WDOPPLER10DB_NI_118_FR (0x03D300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WDOPPLER10DB_NI_119_FR (0x03D400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WRANGE10DB_NI_115_FR  (0x03D500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WRANGE10DB_NI_116_FR  (0x03D600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WRANGE10DB_NI_117_FR  (0x03D700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WRANGE10DB_NI_118_FR  (0x03D800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_23_FR__WRANGE10DB_NI_119_FR  (0x03D900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__AZIMUTH_NI_120_FR     (0x03DA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__AZIMUTH_NI_121_FR     (0x03DB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__AZIMUTH_NI_122_FR     (0x03DC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__AZIMUTH_NI_123_FR     (0x03DD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__AZIMUTH_NI_124_FR     (0x03DE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__BEAM_NI_120_FR        (0x03DF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__BEAM_NI_121_FR        (0x03E000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__BEAM_NI_122_FR        (0x03E100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__BEAM_NI_123_FR        (0x03E200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__BEAM_NI_124_FR        (0x03E300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGDOPPLER_NI_120_FR  (0x03E400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGDOPPLER_NI_121_FR  (0x03E500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGDOPPLER_NI_122_FR  (0x03E600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGDOPPLER_NI_123_FR  (0x03E700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGDOPPLER_NI_124_FR  (0x03E800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGRANGE_NI_120_FR    (0x03E900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGRANGE_NI_121_FR    (0x03EA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGRANGE_NI_122_FR    (0x03EB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGRANGE_NI_123_FR    (0x03EC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__COGRANGE_NI_124_FR    (0x03ED00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__NOINFRASTRUCTURE_NI_120_FR (0x03EE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__NOINFRASTRUCTURE_NI_121_FR (0x03EF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__NOINFRASTRUCTURE_NI_122_FR (0x03F000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__NOINFRASTRUCTURE_NI_123_FR (0x03F100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__NOINFRASTRUCTURE_NI_124_FR (0x03F200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__POWERDB_NI_120_FR     (0x03F300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__POWERDB_NI_121_FR     (0x03F400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__POWERDB_NI_122_FR     (0x03F500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__POWERDB_NI_123_FR     (0x03F600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__POWERDB_NI_124_FR     (0x03F700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__RESERVE1_1BIT_NI_120_FR (0x03F800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__RESERVE1_1BIT_NI_121_FR (0x03F900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__RESERVE1_1BIT_NI_122_FR (0x03FA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__RESERVE1_1BIT_NI_123_FR (0x03FB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__RESERVE1_1BIT_NI_124_FR (0x03FC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__SNRDB_NI_120_FR       (0x03FD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__SNRDB_NI_121_FR       (0x03FE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__SNRDB_NI_122_FR       (0x03FF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__SNRDB_NI_123_FR       (0x040000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__SNRDB_NI_124_FR       (0x040100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__STDAZIMUTH_NI_120_FR  (0x040200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__STDAZIMUTH_NI_121_FR  (0x040300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__STDAZIMUTH_NI_122_FR  (0x040400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__STDAZIMUTH_NI_123_FR  (0x040500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__STDAZIMUTH_NI_124_FR  (0x040600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__VALIDXBEAM_NI_120_FR  (0x040700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__VALIDXBEAM_NI_121_FR  (0x040800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__VALIDXBEAM_NI_122_FR  (0x040900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__VALIDXBEAM_NI_123_FR  (0x040A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__VALIDXBEAM_NI_124_FR  (0x040B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WDOPPLER10DB_NI_120_FR (0x040C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WDOPPLER10DB_NI_121_FR (0x040D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WDOPPLER10DB_NI_122_FR (0x040E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WDOPPLER10DB_NI_123_FR (0x040F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WDOPPLER10DB_NI_124_FR (0x041000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WRANGE10DB_NI_120_FR  (0x041100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WRANGE10DB_NI_121_FR  (0x041200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WRANGE10DB_NI_122_FR  (0x041300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WRANGE10DB_NI_123_FR  (0x041400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_24_FR__WRANGE10DB_NI_124_FR  (0x041500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__AZIMUTH_NI_10_FR       (0x041600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__AZIMUTH_NI_11_FR       (0x041700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__AZIMUTH_NI_12_FR       (0x041800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__AZIMUTH_NI_13_FR       (0x041900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__AZIMUTH_NI_14_FR       (0x041A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__BEAM_NI_10_FR          (0x041B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__BEAM_NI_11_FR          (0x041C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__BEAM_NI_12_FR          (0x041D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__BEAM_NI_13_FR          (0x041E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__BEAM_NI_14_FR          (0x041F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGDOPPLER_NI_10_FR    (0x042000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGDOPPLER_NI_11_FR    (0x042100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGDOPPLER_NI_12_FR    (0x042200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGDOPPLER_NI_13_FR    (0x042300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGDOPPLER_NI_14_FR    (0x042400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGRANGE_NI_10_FR      (0x042500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGRANGE_NI_11_FR      (0x042600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGRANGE_NI_12_FR      (0x042700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGRANGE_NI_13_FR      (0x042800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__COGRANGE_NI_14_FR      (0x042900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__NOINFRASTRUCTURE_NI_10_FR (0x042A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__NOINFRASTRUCTURE_NI_11_FR (0x042B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__NOINFRASTRUCTURE_NI_12_FR (0x042C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__NOINFRASTRUCTURE_NI_13_FR (0x042D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__NOINFRASTRUCTURE_NI_14_FR (0x042E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__POWERDB_NI_10_FR       (0x042F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__POWERDB_NI_11_FR       (0x043000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__POWERDB_NI_12_FR       (0x043100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__POWERDB_NI_13_FR       (0x043200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__POWERDB_NI_14_FR       (0x043300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__RESERVE1_1BIT_NI_10_FR (0x043400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__RESERVE1_1BIT_NI_11_FR (0x043500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__RESERVE1_1BIT_NI_12_FR (0x043600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__RESERVE1_1BIT_NI_13_FR (0x043700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__RESERVE1_1BIT_NI_14_FR (0x043800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__SNRDB_NI_10_FR         (0x043900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__SNRDB_NI_11_FR         (0x043A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__SNRDB_NI_12_FR         (0x043B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__SNRDB_NI_13_FR         (0x043C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__SNRDB_NI_14_FR         (0x043D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__STDAZIMUTH_NI_10_FR    (0x043E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__STDAZIMUTH_NI_11_FR    (0x043F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__STDAZIMUTH_NI_12_FR    (0x044000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__STDAZIMUTH_NI_13_FR    (0x044100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__STDAZIMUTH_NI_14_FR    (0x044200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__VALIDXBEAM_NI_10_FR    (0x044300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__VALIDXBEAM_NI_11_FR    (0x044400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__VALIDXBEAM_NI_12_FR    (0x044500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__VALIDXBEAM_NI_13_FR    (0x044600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__VALIDXBEAM_NI_14_FR    (0x044700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WDOPPLER10DB_NI_10_FR  (0x044800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WDOPPLER10DB_NI_11_FR  (0x044900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WDOPPLER10DB_NI_12_FR  (0x044A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WDOPPLER10DB_NI_13_FR  (0x044B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WDOPPLER10DB_NI_14_FR  (0x044C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WRANGE10DB_NI_10_FR    (0x044D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WRANGE10DB_NI_11_FR    (0x044E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WRANGE10DB_NI_12_FR    (0x044F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WRANGE10DB_NI_13_FR    (0x045000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_2_FR__WRANGE10DB_NI_14_FR    (0x045100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__AZIMUTH_NI_15_FR       (0x045200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__AZIMUTH_NI_16_FR       (0x045300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__AZIMUTH_NI_17_FR       (0x045400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__AZIMUTH_NI_18_FR       (0x045500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__AZIMUTH_NI_19_FR       (0x045600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__BEAM_NI_15_FR          (0x045700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__BEAM_NI_16_FR          (0x045800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__BEAM_NI_17_FR          (0x045900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__BEAM_NI_18_FR          (0x045A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__BEAM_NI_19_FR          (0x045B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGDOPPLER_NI_15_FR    (0x045C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGDOPPLER_NI_16_FR    (0x045D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGDOPPLER_NI_17_FR    (0x045E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGDOPPLER_NI_18_FR    (0x045F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGDOPPLER_NI_19_FR    (0x046000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGRANGE_NI_15_FR      (0x046100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGRANGE_NI_16_FR      (0x046200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGRANGE_NI_17_FR      (0x046300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGRANGE_NI_18_FR      (0x046400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__COGRANGE_NI_19_FR      (0x046500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__NOINFRASTRUCTURE_NI_15_FR (0x046600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__NOINFRASTRUCTURE_NI_16_FR (0x046700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__NOINFRASTRUCTURE_NI_17_FR (0x046800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__NOINFRASTRUCTURE_NI_18_FR (0x046900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__NOINFRASTRUCTURE_NI_19_FR (0x046A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__POWERDB_NI_15_FR       (0x046B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__POWERDB_NI_16_FR       (0x046C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__POWERDB_NI_17_FR       (0x046D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__POWERDB_NI_18_FR       (0x046E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__POWERDB_NI_19_FR       (0x046F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__RESERVE1_1BIT_NI_15_FR (0x047000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__RESERVE1_1BIT_NI_16_FR (0x047100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__RESERVE1_1BIT_NI_17_FR (0x047200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__RESERVE1_1BIT_NI_18_FR (0x047300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__RESERVE1_1BIT_NI_19_FR (0x047400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__SNRDB_NI_15_FR         (0x047500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__SNRDB_NI_16_FR         (0x047600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__SNRDB_NI_17_FR         (0x047700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__SNRDB_NI_18_FR         (0x047800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__SNRDB_NI_19_FR         (0x047900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__STDAZIMUTH_NI_15_FR    (0x047A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__STDAZIMUTH_NI_16_FR    (0x047B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__STDAZIMUTH_NI_17_FR    (0x047C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__STDAZIMUTH_NI_18_FR    (0x047D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__STDAZIMUTH_NI_19_FR    (0x047E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__VALIDXBEAM_NI_15_FR    (0x047F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__VALIDXBEAM_NI_16_FR    (0x048000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__VALIDXBEAM_NI_17_FR    (0x048100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__VALIDXBEAM_NI_18_FR    (0x048200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__VALIDXBEAM_NI_19_FR    (0x048300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WDOPPLER10DB_NI_15_FR  (0x048400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WDOPPLER10DB_NI_16_FR  (0x048500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WDOPPLER10DB_NI_17_FR  (0x048600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WDOPPLER10DB_NI_18_FR  (0x048700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WDOPPLER10DB_NI_19_FR  (0x048800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WRANGE10DB_NI_15_FR    (0x048900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WRANGE10DB_NI_16_FR    (0x048A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WRANGE10DB_NI_17_FR    (0x048B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WRANGE10DB_NI_18_FR    (0x048C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_3_FR__WRANGE10DB_NI_19_FR    (0x048D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__AZIMUTH_NI_20_FR       (0x048E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__AZIMUTH_NI_21_FR       (0x048F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__AZIMUTH_NI_22_FR       (0x049000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__AZIMUTH_NI_23_FR       (0x049100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__AZIMUTH_NI_24_FR       (0x049200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__BEAM_NI_20_FR          (0x049300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__BEAM_NI_21_FR          (0x049400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__BEAM_NI_22_FR          (0x049500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__BEAM_NI_23_FR          (0x049600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__BEAM_NI_24_FR          (0x049700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGDOPPLER_NI_20_FR    (0x049800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGDOPPLER_NI_21_FR    (0x049900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGDOPPLER_NI_22_FR    (0x049A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGDOPPLER_NI_23_FR    (0x049B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGDOPPLER_NI_24_FR    (0x049C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGRANGE_NI_20_FR      (0x049D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGRANGE_NI_21_FR      (0x049E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGRANGE_NI_22_FR      (0x049F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGRANGE_NI_23_FR      (0x04A000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__COGRANGE_NI_24_FR      (0x04A100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__NOINFRASTRUCTURE_NI_20_FR (0x04A200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__NOINFRASTRUCTURE_NI_21_FR (0x04A300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__NOINFRASTRUCTURE_NI_22_FR (0x04A400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__NOINFRASTRUCTURE_NI_23_FR (0x04A500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__NOINFRASTRUCTURE_NI_24_FR (0x04A600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__POWERDB_NI_20_FR       (0x04A700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__POWERDB_NI_21_FR       (0x04A800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__POWERDB_NI_22_FR       (0x04A900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__POWERDB_NI_23_FR       (0x04AA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__POWERDB_NI_24_FR       (0x04AB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__RESERVE1_1BIT_NI_20_FR (0x04AC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__RESERVE1_1BIT_NI_21_FR (0x04AD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__RESERVE1_1BIT_NI_22_FR (0x04AE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__RESERVE1_1BIT_NI_23_FR (0x04AF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__RESERVE1_1BIT_NI_24_FR (0x04B000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__SNRDB_NI_20_FR         (0x04B100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__SNRDB_NI_21_FR         (0x04B200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__SNRDB_NI_22_FR         (0x04B300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__SNRDB_NI_23_FR         (0x04B400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__SNRDB_NI_24_FR         (0x04B500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__STDAZIMUTH_NI_20_FR    (0x04B600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__STDAZIMUTH_NI_21_FR    (0x04B700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__STDAZIMUTH_NI_22_FR    (0x04B800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__STDAZIMUTH_NI_23_FR    (0x04B900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__STDAZIMUTH_NI_24_FR    (0x04BA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__VALIDXBEAM_NI_20_FR    (0x04BB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__VALIDXBEAM_NI_21_FR    (0x04BC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__VALIDXBEAM_NI_22_FR    (0x04BD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__VALIDXBEAM_NI_23_FR    (0x04BE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__VALIDXBEAM_NI_24_FR    (0x04BF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WDOPPLER10DB_NI_20_FR  (0x04C000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WDOPPLER10DB_NI_21_FR  (0x04C100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WDOPPLER10DB_NI_22_FR  (0x04C200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WDOPPLER10DB_NI_23_FR  (0x04C300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WDOPPLER10DB_NI_24_FR  (0x04C400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WRANGE10DB_NI_20_FR    (0x04C500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WRANGE10DB_NI_21_FR    (0x04C600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WRANGE10DB_NI_22_FR    (0x04C700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WRANGE10DB_NI_23_FR    (0x04C800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_4_FR__WRANGE10DB_NI_24_FR    (0x04C900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__AZIMUTH_NI_25_FR       (0x04CA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__AZIMUTH_NI_26_FR       (0x04CB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__AZIMUTH_NI_27_FR       (0x04CC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__AZIMUTH_NI_28_FR       (0x04CD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__AZIMUTH_NI_29_FR       (0x04CE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__BEAM_NI_25_FR          (0x04CF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__BEAM_NI_26_FR          (0x04D000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__BEAM_NI_27_FR          (0x04D100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__BEAM_NI_28_FR          (0x04D200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__BEAM_NI_29_FR          (0x04D300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGDOPPLER_NI_25_FR    (0x04D400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGDOPPLER_NI_26_FR    (0x04D500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGDOPPLER_NI_27_FR    (0x04D600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGDOPPLER_NI_28_FR    (0x04D700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGDOPPLER_NI_29_FR    (0x04D800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGRANGE_NI_25_FR      (0x04D900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGRANGE_NI_26_FR      (0x04DA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGRANGE_NI_27_FR      (0x04DB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGRANGE_NI_28_FR      (0x04DC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__COGRANGE_NI_29_FR      (0x04DD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__NOINFRASTRUCTURE_NI_25_FR (0x04DE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__NOINFRASTRUCTURE_NI_26_FR (0x04DF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__NOINFRASTRUCTURE_NI_27_FR (0x04E000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__NOINFRASTRUCTURE_NI_28_FR (0x04E100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__NOINFRASTRUCTURE_NI_29_FR (0x04E200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__POWERDB_NI_25_FR       (0x04E300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__POWERDB_NI_26_FR       (0x04E400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__POWERDB_NI_27_FR       (0x04E500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__POWERDB_NI_28_FR       (0x04E600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__POWERDB_NI_29_FR       (0x04E700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__RESERVE1_1BIT_NI_25_FR (0x04E800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__RESERVE1_1BIT_NI_26_FR (0x04E900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__RESERVE1_1BIT_NI_27_FR (0x04EA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__RESERVE1_1BIT_NI_28_FR (0x04EB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__RESERVE1_1BIT_NI_29_FR (0x04EC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__SNRDB_NI_25_FR         (0x04ED00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__SNRDB_NI_26_FR         (0x04EE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__SNRDB_NI_27_FR         (0x04EF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__SNRDB_NI_28_FR         (0x04F000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__SNRDB_NI_29_FR         (0x04F100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__STDAZIMUTH_NI_25_FR    (0x04F200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__STDAZIMUTH_NI_26_FR    (0x04F300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__STDAZIMUTH_NI_27_FR    (0x04F400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__STDAZIMUTH_NI_28_FR    (0x04F500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__STDAZIMUTH_NI_29_FR    (0x04F600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__VALIDXBEAM_NI_25_FR    (0x04F700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__VALIDXBEAM_NI_26_FR    (0x04F800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__VALIDXBEAM_NI_27_FR    (0x04F900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__VALIDXBEAM_NI_28_FR    (0x04FA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__VALIDXBEAM_NI_29_FR    (0x04FB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WDOPPLER10DB_NI_25_FR  (0x04FC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WDOPPLER10DB_NI_26_FR  (0x04FD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WDOPPLER10DB_NI_27_FR  (0x04FE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WDOPPLER10DB_NI_28_FR  (0x04FF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WDOPPLER10DB_NI_29_FR  (0x050000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WRANGE10DB_NI_25_FR    (0x050100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WRANGE10DB_NI_26_FR    (0x050200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WRANGE10DB_NI_27_FR    (0x050300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WRANGE10DB_NI_28_FR    (0x050400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_5_FR__WRANGE10DB_NI_29_FR    (0x050500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__AZIMUTH_NI_30_FR       (0x050600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__AZIMUTH_NI_31_FR       (0x050700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__AZIMUTH_NI_32_FR       (0x050800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__AZIMUTH_NI_33_FR       (0x050900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__AZIMUTH_NI_34_FR       (0x050A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__BEAM_NI_30_FR          (0x050B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__BEAM_NI_31_FR          (0x050C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__BEAM_NI_32_FR          (0x050D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__BEAM_NI_33_FR          (0x050E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__BEAM_NI_34_FR          (0x050F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGDOPPLER_NI_30_FR    (0x051000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGDOPPLER_NI_31_FR    (0x051100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGDOPPLER_NI_32_FR    (0x051200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGDOPPLER_NI_33_FR    (0x051300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGDOPPLER_NI_34_FR    (0x051400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGRANGE_NI_30_FR      (0x051500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGRANGE_NI_31_FR      (0x051600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGRANGE_NI_32_FR      (0x051700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGRANGE_NI_33_FR      (0x051800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__COGRANGE_NI_34_FR      (0x051900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__NOINFRASTRUCTURE_NI_30_FR (0x051A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__NOINFRASTRUCTURE_NI_31_FR (0x051B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__NOINFRASTRUCTURE_NI_32_FR (0x051C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__NOINFRASTRUCTURE_NI_33_FR (0x051D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__NOINFRASTRUCTURE_NI_34_FR (0x051E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__POWERDB_NI_30_FR       (0x051F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__POWERDB_NI_31_FR       (0x052000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__POWERDB_NI_32_FR       (0x052100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__POWERDB_NI_33_FR       (0x052200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__POWERDB_NI_34_FR       (0x052300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__RESERVE1_1BIT_NI_30_FR (0x052400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__RESERVE1_1BIT_NI_31_FR (0x052500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__RESERVE1_1BIT_NI_32_FR (0x052600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__RESERVE1_1BIT_NI_33_FR (0x052700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__RESERVE1_1BIT_NI_34_FR (0x052800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__SNRDB_NI_30_FR         (0x052900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__SNRDB_NI_31_FR         (0x052A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__SNRDB_NI_32_FR         (0x052B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__SNRDB_NI_33_FR         (0x052C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__SNRDB_NI_34_FR         (0x052D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__STDAZIMUTH_NI_30_FR    (0x052E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__STDAZIMUTH_NI_31_FR    (0x052F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__STDAZIMUTH_NI_32_FR    (0x053000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__STDAZIMUTH_NI_33_FR    (0x053100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__STDAZIMUTH_NI_34_FR    (0x053200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__VALIDXBEAM_NI_30_FR    (0x053300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__VALIDXBEAM_NI_31_FR    (0x053400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__VALIDXBEAM_NI_32_FR    (0x053500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__VALIDXBEAM_NI_33_FR    (0x053600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__VALIDXBEAM_NI_34_FR    (0x053700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WDOPPLER10DB_NI_30_FR  (0x053800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WDOPPLER10DB_NI_31_FR  (0x053900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WDOPPLER10DB_NI_32_FR  (0x053A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WDOPPLER10DB_NI_33_FR  (0x053B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WDOPPLER10DB_NI_34_FR  (0x053C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WRANGE10DB_NI_30_FR    (0x053D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WRANGE10DB_NI_31_FR    (0x053E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WRANGE10DB_NI_32_FR    (0x053F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WRANGE10DB_NI_33_FR    (0x054000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_6_FR__WRANGE10DB_NI_34_FR    (0x054100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__AZIMUTH_NI_35_FR       (0x054200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__AZIMUTH_NI_36_FR       (0x054300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__AZIMUTH_NI_37_FR       (0x054400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__AZIMUTH_NI_38_FR       (0x054500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__AZIMUTH_NI_39_FR       (0x054600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__BEAM_NI_35_FR          (0x054700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__BEAM_NI_36_FR          (0x054800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__BEAM_NI_37_FR          (0x054900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__BEAM_NI_38_FR          (0x054A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__BEAM_NI_39_FR          (0x054B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGDOPPLER_NI_35_FR    (0x054C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGDOPPLER_NI_36_FR    (0x054D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGDOPPLER_NI_37_FR    (0x054E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGDOPPLER_NI_38_FR    (0x054F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGDOPPLER_NI_39_FR    (0x055000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGRANGE_NI_35_FR      (0x055100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGRANGE_NI_36_FR      (0x055200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGRANGE_NI_37_FR      (0x055300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGRANGE_NI_38_FR      (0x055400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__COGRANGE_NI_39_FR      (0x055500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__NOINFRASTRUCTURE_NI_35_FR (0x055600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__NOINFRASTRUCTURE_NI_36_FR (0x055700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__NOINFRASTRUCTURE_NI_37_FR (0x055800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__NOINFRASTRUCTURE_NI_38_FR (0x055900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__NOINFRASTRUCTURE_NI_39_FR (0x055A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__POWERDB_NI_35_FR       (0x055B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__POWERDB_NI_36_FR       (0x055C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__POWERDB_NI_37_FR       (0x055D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__POWERDB_NI_38_FR       (0x055E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__POWERDB_NI_39_FR       (0x055F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__RESERVE1_1BIT_NI_35_FR (0x056000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__RESERVE1_1BIT_NI_36_FR (0x056100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__RESERVE1_1BIT_NI_37_FR (0x056200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__RESERVE1_1BIT_NI_38_FR (0x056300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__RESERVE1_1BIT_NI_39_FR (0x056400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__SNRDB_NI_35_FR         (0x056500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__SNRDB_NI_36_FR         (0x056600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__SNRDB_NI_37_FR         (0x056700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__SNRDB_NI_38_FR         (0x056800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__SNRDB_NI_39_FR         (0x056900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__STDAZIMUTH_NI_35_FR    (0x056A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__STDAZIMUTH_NI_36_FR    (0x056B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__STDAZIMUTH_NI_37_FR    (0x056C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__STDAZIMUTH_NI_38_FR    (0x056D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__STDAZIMUTH_NI_39_FR    (0x056E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__VALIDXBEAM_NI_35_FR    (0x056F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__VALIDXBEAM_NI_36_FR    (0x057000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__VALIDXBEAM_NI_37_FR    (0x057100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__VALIDXBEAM_NI_38_FR    (0x057200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__VALIDXBEAM_NI_39_FR    (0x057300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WDOPPLER10DB_NI_35_FR  (0x057400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WDOPPLER10DB_NI_36_FR  (0x057500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WDOPPLER10DB_NI_37_FR  (0x057600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WDOPPLER10DB_NI_38_FR  (0x057700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WDOPPLER10DB_NI_39_FR  (0x057800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WRANGE10DB_NI_35_FR    (0x057900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WRANGE10DB_NI_36_FR    (0x057A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WRANGE10DB_NI_37_FR    (0x057B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WRANGE10DB_NI_38_FR    (0x057C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_7_FR__WRANGE10DB_NI_39_FR    (0x057D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__AZIMUTH_NI_40_FR       (0x057E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__AZIMUTH_NI_41_FR       (0x057F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__AZIMUTH_NI_42_FR       (0x058000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__AZIMUTH_NI_43_FR       (0x058100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__AZIMUTH_NI_44_FR       (0x058200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__BEAM_NI_40_FR          (0x058300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__BEAM_NI_41_FR          (0x058400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__BEAM_NI_42_FR          (0x058500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__BEAM_NI_43_FR          (0x058600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__BEAM_NI_44_FR          (0x058700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGDOPPLER_NI_40_FR    (0x058800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGDOPPLER_NI_41_FR    (0x058900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGDOPPLER_NI_42_FR    (0x058A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGDOPPLER_NI_43_FR    (0x058B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGDOPPLER_NI_44_FR    (0x058C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGRANGE_NI_40_FR      (0x058D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGRANGE_NI_41_FR      (0x058E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGRANGE_NI_42_FR      (0x058F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGRANGE_NI_43_FR      (0x059000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__COGRANGE_NI_44_FR      (0x059100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__NOINFRASTRUCTURE_NI_40_FR (0x059200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__NOINFRASTRUCTURE_NI_41_FR (0x059300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__NOINFRASTRUCTURE_NI_42_FR (0x059400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__NOINFRASTRUCTURE_NI_43_FR (0x059500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__NOINFRASTRUCTURE_NI_44_FR (0x059600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__POWERDB_NI_40_FR       (0x059700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__POWERDB_NI_41_FR       (0x059800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__POWERDB_NI_42_FR       (0x059900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__POWERDB_NI_43_FR       (0x059A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__POWERDB_NI_44_FR       (0x059B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__RESERVE1_1BIT_NI_40_FR (0x059C00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__RESERVE1_1BIT_NI_41_FR (0x059D00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__RESERVE1_1BIT_NI_42_FR (0x059E00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__RESERVE1_1BIT_NI_43_FR (0x059F00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__RESERVE1_1BIT_NI_44_FR (0x05A000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__SNRDB_NI_40_FR         (0x05A100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__SNRDB_NI_41_FR         (0x05A200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__SNRDB_NI_42_FR         (0x05A300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__SNRDB_NI_43_FR         (0x05A400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__SNRDB_NI_44_FR         (0x05A500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__STDAZIMUTH_NI_40_FR    (0x05A600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__STDAZIMUTH_NI_41_FR    (0x05A700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__STDAZIMUTH_NI_42_FR    (0x05A800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__STDAZIMUTH_NI_43_FR    (0x05A900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__STDAZIMUTH_NI_44_FR    (0x05AA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__VALIDXBEAM_NI_40_FR    (0x05AB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__VALIDXBEAM_NI_41_FR    (0x05AC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__VALIDXBEAM_NI_42_FR    (0x05AD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__VALIDXBEAM_NI_43_FR    (0x05AE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__VALIDXBEAM_NI_44_FR    (0x05AF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WDOPPLER10DB_NI_40_FR  (0x05B000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WDOPPLER10DB_NI_41_FR  (0x05B100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WDOPPLER10DB_NI_42_FR  (0x05B200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WDOPPLER10DB_NI_43_FR  (0x05B300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WDOPPLER10DB_NI_44_FR  (0x05B400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WRANGE10DB_NI_40_FR    (0x05B500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WRANGE10DB_NI_41_FR    (0x05B600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WRANGE10DB_NI_42_FR    (0x05B700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WRANGE10DB_NI_43_FR    (0x05B800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_8_FR__WRANGE10DB_NI_44_FR    (0x05B900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__AZIMUTH_NI_45_FR       (0x05BA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__AZIMUTH_NI_46_FR       (0x05BB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__AZIMUTH_NI_47_FR       (0x05BC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__AZIMUTH_NI_48_FR       (0x05BD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__AZIMUTH_NI_49_FR       (0x05BE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__BEAM_NI_45_FR          (0x05BF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__BEAM_NI_46_FR          (0x05C000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__BEAM_NI_47_FR          (0x05C100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__BEAM_NI_48_FR          (0x05C200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__BEAM_NI_49_FR          (0x05C300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGDOPPLER_NI_45_FR    (0x05C400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGDOPPLER_NI_46_FR    (0x05C500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGDOPPLER_NI_47_FR    (0x05C600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGDOPPLER_NI_48_FR    (0x05C700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGDOPPLER_NI_49_FR    (0x05C800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGRANGE_NI_45_FR      (0x05C900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGRANGE_NI_46_FR      (0x05CA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGRANGE_NI_47_FR      (0x05CB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGRANGE_NI_48_FR      (0x05CC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__COGRANGE_NI_49_FR      (0x05CD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__NOINFRASTRUCTURE_NI_45_FR (0x05CE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__NOINFRASTRUCTURE_NI_46_FR (0x05CF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__NOINFRASTRUCTURE_NI_47_FR (0x05D000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__NOINFRASTRUCTURE_NI_48_FR (0x05D100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__NOINFRASTRUCTURE_NI_49_FR (0x05D200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__POWERDB_NI_45_FR       (0x05D300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__POWERDB_NI_46_FR       (0x05D400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__POWERDB_NI_47_FR       (0x05D500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__POWERDB_NI_48_FR       (0x05D600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__POWERDB_NI_49_FR       (0x05D700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__RESERVE1_1BIT_NI_45_FR (0x05D800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__RESERVE1_1BIT_NI_46_FR (0x05D900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__RESERVE1_1BIT_NI_47_FR (0x05DA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__RESERVE1_1BIT_NI_48_FR (0x05DB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__RESERVE1_1BIT_NI_49_FR (0x05DC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__SNRDB_NI_45_FR         (0x05DD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__SNRDB_NI_46_FR         (0x05DE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__SNRDB_NI_47_FR         (0x05DF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__SNRDB_NI_48_FR         (0x05E000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__SNRDB_NI_49_FR         (0x05E100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__STDAZIMUTH_NI_45_FR    (0x05E200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__STDAZIMUTH_NI_46_FR    (0x05E300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__STDAZIMUTH_NI_47_FR    (0x05E400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__STDAZIMUTH_NI_48_FR    (0x05E500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__STDAZIMUTH_NI_49_FR    (0x05E600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__VALIDXBEAM_NI_45_FR    (0x05E700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__VALIDXBEAM_NI_46_FR    (0x05E800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__VALIDXBEAM_NI_47_FR    (0x05E900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__VALIDXBEAM_NI_48_FR    (0x05EA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__VALIDXBEAM_NI_49_FR    (0x05EB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WDOPPLER10DB_NI_45_FR  (0x05EC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WDOPPLER10DB_NI_46_FR  (0x05ED00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WDOPPLER10DB_NI_47_FR  (0x05EE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WDOPPLER10DB_NI_48_FR  (0x05EF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WDOPPLER10DB_NI_49_FR  (0x05F000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WRANGE10DB_NI_45_FR    (0x05F100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WRANGE10DB_NI_46_FR    (0x05F200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WRANGE10DB_NI_47_FR    (0x05F300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WRANGE10DB_NI_48_FR    (0x05F400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_NON_INF_DETECTION_9_FR__WRANGE10DB_NI_49_FR    (0x05F500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__BW100KHZ1_FR       (0x05F600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__BW100KHZ2_FR       (0x05F700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__BW100KHZ3_FR       (0x05F800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__CYCLENUMBER_FR     (0x05F900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__DETECTIONLISTVERSION_FR (0x05FA00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__FC1MHZ1_FR         (0x05FB00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__FC1MHZ2_FR         (0x05FC00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__FC1MHZ3_FR         (0x05FD00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__HOSTACCELLATEST_FR (0x05FE00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__HOSTACCELLONGEST_FR (0x05FF00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__HOSTVELEST_FR      (0x060000|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__HOSTYAWEST_FR      (0x060100|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__INTERFERENCEEXCEEDED_FR (0x060200|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__TIMESTAMP_FR       (0x060300|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__UNAMBIGUOUSVELMEAS1_FR (0x060400|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__UNAMBIGUOUSVELMEAS2_FR (0x060500|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__UNAMBIGUOUSVELMEAS3_FR (0x060600|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__NUMCMPLXVALPERDETECTIONBEAM1_FR (0x060700|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__NUMCMPLXVALPERDETECTIONBEAM2_FR (0x060800|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__NUMCMPLXVALPERDETECTIONBEAM3_FR (0x060900|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__NUMFREESPACEDETECTIONS_FR (0x060A00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__NUMINFRASTRUCTUREDETECTED_FR (0x060B00|(UID_CLASS_CAN<<4))
#define CANSIG_MK_TARGET_DETECTION_HEADER_FR__NUMNONINFRADETECTED_FR (0x060C00|(UID_CLASS_CAN<<4))
#define CAN_UNIQUE_ID_DUMMY                                      (0x060D00|(UID_CLASS_CAN<<4))
#define NUM_OF_CAN_UNIQUE_ID                                     1550

enum canmsg_frametype {
    CANMSG_FRAMETYPE_DEFAULT         = 0,
    CANMSG_FRAMETYPE_STANDARDCAN     = 0,
    CANMSG_FRAMETYPE_EXTENDEDCAN     = 1,
    CANMSG_FRAMETYPE_RESERVED_2      = 2,
    CANMSG_FRAMETYPE_RESERVED_3      = 3,
    CANMSG_FRAMETYPE_RESERVED_4      = 4,
    CANMSG_FRAMETYPE_RESERVED_5      = 5,
    CANMSG_FRAMETYPE_RESERVED_6      = 6,
    CANMSG_FRAMETYPE_RESERVED_7      = 7,
    CANMSG_FRAMETYPE_RESERVED_8      = 8,
    CANMSG_FRAMETYPE_RESERVED_9      = 9,
    CANMSG_FRAMETYPE_RESERVED_10     = 10,
    CANMSG_FRAMETYPE_RESERVED_11     = 11,
    CANMSG_FRAMETYPE_RESERVED_12     = 12,
    CANMSG_FRAMETYPE_RESERVED_13     = 13,
    CANMSG_FRAMETYPE_STANDARDCAN_FD  = 14,
    CANMSG_FRAMETYPE_EXTENDEDCAN_FD  = 15,
};

enum canmsg_sendtype {
    CANMSG_SENDTYPE_CYCLIC           = 0,
    CANMSG_SENDTYPE_SPONTANEOUS      = 1,
    CANMSG_SENDTYPE_CYCLICIFACTIVE   = 2,
    CANMSG_SENDTYPE_SPONTANEOUSWITHDELAY = 3,
    CANMSG_SENDTYPE_CYCLICANDSPONTANEOUS = 4,
    CANMSG_SENDTYPE_CYCLICANDSPONTANEOUSWITHDELAY = 5,
    CANMSG_SENDTYPE_IFACTIVE         = 6,
    CANMSG_SENDTYPE_NOMSGSENDTYPE    = 7,
};

enum cansig_sendtype {
    CANSIG_SENDTYPE_CYCLIC                   = 0,
    CANSIG_SENDTYPE_ONWRITE                  = 1,
    CANSIG_SENDTYPE_ONWRITEWITHREPETITION    = 2,
    CANSIG_SENDTYPE_ONCHANGE                 = 3,
    CANSIG_SENDTYPE_ONCHANGEWITHREPETITION   = 4,
    CANSIG_SENDTYPE_IFACTIVE                 = 5,
    CANSIG_SENDTYPE_IFACTIVEWITHREPETITION   = 6,
    CANSIG_SENDTYPE_NOSIGSENDTYPE            = 7,
    CANSIG_SENDTYPE_NOTUSED_8                = 8,
    CANSIG_SENDTYPE_NOTUSED_9                = 9,
    CANSIG_SENDTYPE_NOTUSED_10               = 10,
    CANSIG_SENDTYPE_NOTUSED_11               = 11,
    CANSIG_SENDTYPE_NOTUSED_12               = 12,
};

//=====================================================================================//


#include "parser_typedef.h"
#pragma once

#if 0
//--------------//
extern void CAN_SetCanParserLockHandler(fpParserLockHandler_t func);
extern void CAN_SetCanParserUnlockHandler(fpParserUnlockHandler_t func);
extern void CAN_SetMsgCounterCalcHandler(fpMsgCounterCalcHandler_t func);
extern void CAN_SetMsgChecksumCalcHandler(fpMsgChecksumCalcHandler_t func);
extern void CAN_SetMsgChecksumVerityHandler(fpMsgChecksumVerityHandler_t func);
extern void CAN_SetMsgChangeHandler(fpMsgChangeHandler_t func);
extern void CAN_SetMsgChecksumErrorHandler(fpMsgChecksumErrorHandler_t func);
extern void CAN_SetMsgCounterErrorHandler(fpMsgCounterErrorHandler_t func);
extern void CAN_SetMsgTimeoutHandler(fpMsgTimeoutHandler_t func);
extern void CAN_SetMsgDlcHandler(fpMsgDlcHandler_t func);
extern void CAN_SetMsgOutRangeHandler(fpMsgOutRangeHandler_t func);
extern void CAN_SetSigChangeHandler(fpSigChangeHandler_t func);
extern void CAN_SetSigOnWriteHandler(fpSigOnWriteHandler_t func);
extern void CAN_SetSignalChangedHook(fpSignalChangedHook_t func);
extern void CAN_SetSignalSetCallBack(fpSignalTriggerCallBack_t func);
extern void CAN_SetSignalGetCallBack(fpSignalTriggerCallBack_t func);
//=====================================================================================//
extern struct veh_message *CAN_RxMessageList[27];

extern struct veh_message *CAN_TxMessageList[1];

extern struct veh_signal* CAN_ALL_Signal_Array[];
#endif
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Beam_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_0_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_1_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_2_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_3_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_4_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Beam_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Beam_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Beam_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Beam_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Beam_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_50_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_51_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_52_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_53_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_54_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Beam_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Beam_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Beam_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Beam_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Beam_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_55_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_56_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_57_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_58_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_59_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Beam_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Beam_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Beam_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Beam_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Beam_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_60_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_61_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_62_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_63_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_64_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Beam_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Beam_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Beam_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Beam_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Beam_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_65_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_66_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_67_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_68_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_69_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Beam_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Beam_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Beam_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Beam_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Beam_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_70_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_71_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_72_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_73_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_74_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Beam_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Beam_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Beam_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Beam_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Beam_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_75_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_76_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_77_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_78_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_79_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Beam_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Beam_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Beam_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Beam_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Beam_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_80_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_81_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_82_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_83_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_84_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Beam_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Beam_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Beam_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Beam_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Beam_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_85_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_86_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_87_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_88_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_89_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Beam_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Beam_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Beam_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Beam_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Beam_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_90_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_91_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_92_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_93_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_94_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Beam_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Beam_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Beam_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Beam_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Beam_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_95_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_96_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_97_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_98_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_99_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Beam_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Beam_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Beam_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Beam_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Beam_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_5_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_6_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_7_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_8_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_9_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Beam_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Beam_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Beam_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Beam_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Beam_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_100_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_101_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_102_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_103_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_104_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Beam_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Beam_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Beam_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Beam_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Beam_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_105_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_106_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_107_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_108_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_109_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Beam_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Beam_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Beam_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Beam_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Beam_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_110_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_111_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_112_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_113_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_114_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Beam_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Beam_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Beam_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Beam_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Beam_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_115_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_116_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_117_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_118_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_119_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Beam_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Beam_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Beam_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Beam_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Beam_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_120_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_121_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_122_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_123_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_124_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Beam_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Beam_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Beam_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Beam_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Beam_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_10_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_11_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_12_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_13_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_14_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Beam_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Beam_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Beam_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Beam_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Beam_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_15_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_16_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_17_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_18_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_19_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Beam_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Beam_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Beam_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Beam_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Beam_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_20_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_21_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_22_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_23_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_24_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Beam_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Beam_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Beam_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Beam_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Beam_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_25_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_26_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_27_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_28_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_29_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Beam_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Beam_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Beam_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Beam_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Beam_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_30_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_31_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_32_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_33_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_34_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Beam_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Beam_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Beam_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Beam_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Beam_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_35_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_36_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_37_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_38_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_39_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Beam_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Beam_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Beam_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Beam_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Beam_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_40_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_41_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_42_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_43_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_44_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Beam_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Beam_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Beam_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Beam_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Beam_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_45_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_46_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_47_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_48_FC_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_49_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__BW100KHz1_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__BW100KHz2_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__BW100KHz3_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__CycleNumber_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__DetectionListVersion_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__FC1MHz1_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__FC1MHz2_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__FC1MHz3_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__HostAccelLatEst_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__HostAccelLongEst_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__HostVelEst_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__HostYawEst_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__InterferenceExceeded_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__TimeStamp_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__UnambiguousVelMeas1_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__UnambiguousVelMeas2_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__UnambiguousVelMeas3_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__numCmplxValPerDetectionBeam1_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__numCmplxValPerDetectionBeam2_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__numCmplxValPerDetectionBeam3_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__numFreespaceDetections_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__numInfrastructureDetected_FC_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FC__numNonInfraDetected_FC_g;

extern void CAN_SetRawMessage_MK_TARGET_DETECTION_HEADER_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_0_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_1_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_2_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_3_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_4_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_5_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_6_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_7_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_8_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_9_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_10_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_11_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_12_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_13_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_14_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_15_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_16_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_17_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_18_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_19_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_20_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_21_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_22_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_23_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_24_FC(uint8_t *values, uint32_t length);

extern void CAN_ResetMessage_MK_TARGET_DETECTION_HEADER_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_0_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_1_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_2_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_3_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_4_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_5_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_6_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_7_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_8_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_9_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_10_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_11_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_12_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_13_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_14_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_15_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_16_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_17_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_18_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_19_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_20_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_21_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_22_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_23_FC(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_24_FC(enum reset_flg flags);

#if 0
extern void CAN_MessageElapseTime(int bus_id, int time_ms, int restart);
#endif


extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_0_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_10_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_11_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_12_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_13_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_14_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_15_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_16_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_17_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_18_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_19_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_1_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_20_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_21_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_22_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_23_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_24_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_2_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_3_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_4_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_5_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_6_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_7_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_8_FC(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_9_FC(void);
extern void CAN_Message_RDLock_MK_TARGET_DETECTION_HEADER_FC(void);

extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_0_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_10_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_11_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_12_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_13_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_14_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_15_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_16_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_17_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_18_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_19_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_1_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_20_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_21_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_22_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_23_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_24_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_2_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_3_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_4_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_5_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_6_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_7_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_8_FC(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_9_FC(void);
extern void CAN_Message_WRLock_MK_TARGET_DETECTION_HEADER_FC(void);

extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_0_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_10_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_11_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_12_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_13_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_14_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_15_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_16_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_17_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_18_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_19_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_1_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_20_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_21_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_22_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_23_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_24_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_2_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_3_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_4_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_5_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_6_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_7_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_8_FC(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_9_FC(void);
extern void CAN_Message_Unlock_MK_TARGET_DETECTION_HEADER_FC(void);



extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Azimuth_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Beam_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Beam_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Beam_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Beam_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Beam_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGDoppler_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__CoGRange_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__NoInfrastructure_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__PowerDB_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__Reserve1_1bit_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__SNRdB_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__StdAzimuth_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__ValidXBeam_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WDoppler10DB_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_0_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FC__WRange10DB_NI_4_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Azimuth_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Beam_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Beam_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Beam_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Beam_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Beam_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGDoppler_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__CoGRange_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__NoInfrastructure_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__PowerDB_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__Reserve1_1bit_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__SNRdB_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__StdAzimuth_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__ValidXBeam_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WDoppler10DB_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_50_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_51_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_52_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_53_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FC__WRange10DB_NI_54_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Azimuth_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Beam_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Beam_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Beam_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Beam_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Beam_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGDoppler_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__CoGRange_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__NoInfrastructure_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__PowerDB_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__Reserve1_1bit_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__SNRdB_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__StdAzimuth_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__ValidXBeam_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WDoppler10DB_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_55_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_56_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_57_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_58_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FC__WRange10DB_NI_59_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Azimuth_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Beam_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Beam_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Beam_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Beam_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Beam_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGDoppler_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__CoGRange_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__NoInfrastructure_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__PowerDB_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__Reserve1_1bit_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__SNRdB_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__StdAzimuth_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__ValidXBeam_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WDoppler10DB_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_60_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_61_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_62_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_63_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FC__WRange10DB_NI_64_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Azimuth_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Beam_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Beam_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Beam_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Beam_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Beam_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGDoppler_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__CoGRange_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__NoInfrastructure_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__PowerDB_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__Reserve1_1bit_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__SNRdB_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__StdAzimuth_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__ValidXBeam_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WDoppler10DB_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_65_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_66_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_67_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_68_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FC__WRange10DB_NI_69_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Azimuth_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Beam_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Beam_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Beam_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Beam_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Beam_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGDoppler_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__CoGRange_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__NoInfrastructure_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__PowerDB_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__Reserve1_1bit_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__SNRdB_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__StdAzimuth_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__ValidXBeam_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WDoppler10DB_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_70_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_71_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_72_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_73_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FC__WRange10DB_NI_74_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Azimuth_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Beam_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Beam_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Beam_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Beam_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Beam_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGDoppler_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__CoGRange_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__NoInfrastructure_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__PowerDB_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__Reserve1_1bit_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__SNRdB_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__StdAzimuth_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__ValidXBeam_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WDoppler10DB_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_75_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_76_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_77_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_78_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FC__WRange10DB_NI_79_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Azimuth_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Beam_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Beam_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Beam_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Beam_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Beam_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGDoppler_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__CoGRange_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__NoInfrastructure_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__PowerDB_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__Reserve1_1bit_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__SNRdB_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__StdAzimuth_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__ValidXBeam_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WDoppler10DB_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_80_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_81_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_82_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_83_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FC__WRange10DB_NI_84_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Azimuth_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Beam_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Beam_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Beam_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Beam_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Beam_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGDoppler_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__CoGRange_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__NoInfrastructure_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__PowerDB_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__Reserve1_1bit_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__SNRdB_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__StdAzimuth_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__ValidXBeam_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WDoppler10DB_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_85_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_86_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_87_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_88_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FC__WRange10DB_NI_89_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Azimuth_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Beam_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Beam_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Beam_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Beam_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Beam_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGDoppler_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__CoGRange_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__NoInfrastructure_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__PowerDB_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__Reserve1_1bit_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__SNRdB_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__StdAzimuth_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__ValidXBeam_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WDoppler10DB_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_90_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_91_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_92_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_93_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FC__WRange10DB_NI_94_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Azimuth_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Beam_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Beam_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Beam_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Beam_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Beam_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGDoppler_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__CoGRange_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__NoInfrastructure_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__PowerDB_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__Reserve1_1bit_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__SNRdB_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__StdAzimuth_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__ValidXBeam_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WDoppler10DB_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_95_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_96_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_97_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_98_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FC__WRange10DB_NI_99_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Azimuth_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Beam_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Beam_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Beam_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Beam_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Beam_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGDoppler_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__CoGRange_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__NoInfrastructure_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__PowerDB_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__Reserve1_1bit_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__SNRdB_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__StdAzimuth_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__ValidXBeam_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WDoppler10DB_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_5_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_6_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_7_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_8_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FC__WRange10DB_NI_9_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Azimuth_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Beam_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Beam_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Beam_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Beam_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Beam_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGDoppler_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__CoGRange_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__NoInfrastructure_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__PowerDB_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__Reserve1_1bit_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__SNRdB_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__StdAzimuth_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__ValidXBeam_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WDoppler10DB_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_100_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_101_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_102_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_103_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FC__WRange10DB_NI_104_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Azimuth_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Beam_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Beam_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Beam_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Beam_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Beam_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGDoppler_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__CoGRange_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__NoInfrastructure_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__PowerDB_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__Reserve1_1bit_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__SNRdB_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__StdAzimuth_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__ValidXBeam_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WDoppler10DB_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_105_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_106_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_107_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_108_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FC__WRange10DB_NI_109_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Azimuth_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Beam_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Beam_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Beam_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Beam_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Beam_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGDoppler_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__CoGRange_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__NoInfrastructure_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__PowerDB_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__Reserve1_1bit_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__SNRdB_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__StdAzimuth_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__ValidXBeam_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WDoppler10DB_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_110_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_111_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_112_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_113_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FC__WRange10DB_NI_114_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Azimuth_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Beam_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Beam_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Beam_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Beam_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Beam_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGDoppler_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__CoGRange_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__NoInfrastructure_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__PowerDB_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__Reserve1_1bit_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__SNRdB_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__StdAzimuth_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__ValidXBeam_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WDoppler10DB_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_115_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_116_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_117_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_118_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FC__WRange10DB_NI_119_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Azimuth_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Beam_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Beam_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Beam_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Beam_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Beam_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGDoppler_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__CoGRange_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__NoInfrastructure_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__PowerDB_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__Reserve1_1bit_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__SNRdB_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__StdAzimuth_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__ValidXBeam_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WDoppler10DB_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_120_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_121_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_122_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_123_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FC__WRange10DB_NI_124_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Azimuth_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Beam_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Beam_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Beam_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Beam_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Beam_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGDoppler_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__CoGRange_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__NoInfrastructure_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__PowerDB_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__Reserve1_1bit_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__SNRdB_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__StdAzimuth_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__ValidXBeam_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WDoppler10DB_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_10_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_11_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_12_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_13_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FC__WRange10DB_NI_14_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Azimuth_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Beam_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Beam_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Beam_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Beam_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Beam_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGDoppler_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__CoGRange_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__NoInfrastructure_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__PowerDB_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__Reserve1_1bit_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__SNRdB_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__StdAzimuth_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__ValidXBeam_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WDoppler10DB_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_15_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_16_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_17_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_18_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FC__WRange10DB_NI_19_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Azimuth_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Beam_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Beam_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Beam_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Beam_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Beam_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGDoppler_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__CoGRange_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__NoInfrastructure_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__PowerDB_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__Reserve1_1bit_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__SNRdB_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__StdAzimuth_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__ValidXBeam_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WDoppler10DB_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_20_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_21_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_22_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_23_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FC__WRange10DB_NI_24_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Azimuth_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Beam_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Beam_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Beam_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Beam_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Beam_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGDoppler_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__CoGRange_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__NoInfrastructure_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__PowerDB_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__Reserve1_1bit_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__SNRdB_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__StdAzimuth_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__ValidXBeam_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WDoppler10DB_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_25_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_26_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_27_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_28_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FC__WRange10DB_NI_29_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Azimuth_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Beam_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Beam_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Beam_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Beam_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Beam_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGDoppler_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__CoGRange_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__NoInfrastructure_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__PowerDB_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__Reserve1_1bit_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__SNRdB_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__StdAzimuth_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__ValidXBeam_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WDoppler10DB_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_30_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_31_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_32_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_33_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FC__WRange10DB_NI_34_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Azimuth_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Beam_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Beam_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Beam_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Beam_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Beam_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGDoppler_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__CoGRange_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__NoInfrastructure_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__PowerDB_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__Reserve1_1bit_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__SNRdB_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__StdAzimuth_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__ValidXBeam_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WDoppler10DB_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_35_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_36_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_37_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_38_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FC__WRange10DB_NI_39_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Azimuth_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Beam_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Beam_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Beam_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Beam_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Beam_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGDoppler_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__CoGRange_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__NoInfrastructure_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__PowerDB_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__Reserve1_1bit_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__SNRdB_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__StdAzimuth_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__ValidXBeam_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WDoppler10DB_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_40_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_41_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_42_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_43_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FC__WRange10DB_NI_44_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Azimuth_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Beam_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Beam_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Beam_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Beam_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Beam_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGDoppler_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__CoGRange_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__NoInfrastructure_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__PowerDB_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__Reserve1_1bit_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__SNRdB_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__StdAzimuth_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__ValidXBeam_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WDoppler10DB_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_45_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_46_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_47_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_48_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FC__WRange10DB_NI_49_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__BW100KHz1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__BW100KHz2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__BW100KHz3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__CycleNumber_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__DetectionListVersion_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__FC1MHz1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__FC1MHz2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__FC1MHz3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__HostAccelLatEst_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__HostAccelLongEst_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__HostVelEst_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__HostYawEst_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__InterferenceExceeded_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__TimeStamp_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__UnambiguousVelMeas1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__UnambiguousVelMeas2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__UnambiguousVelMeas3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__numCmplxValPerDetectionBeam1_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__numCmplxValPerDetectionBeam2_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__numCmplxValPerDetectionBeam3_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__numFreespaceDetections_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__numInfrastructureDetected_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FC__numNonInfraDetected_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);

extern void MK_FC_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
#if 0
extern void CAN_PARSER_Init(void);
extern void CAN_PARSER_MSG_Init(struct veh_message *p_message_list[]);
#endif

//=====================================================================================//



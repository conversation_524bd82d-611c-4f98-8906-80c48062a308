#include "parser_typedef.h"
#pragma once
//=====================================================================================//
#define CANMSG_MK_NON_INF_DETECTION_0_FC_ID        (0x0252)
#define CANMSG_MK_NON_INF_DETECTION_0_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_0_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_0_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_4_FC : 1;
        uint64_t NoInfrastructure_NI_4_FC : 1;
        uint64_t Beam_NI_4_FC : 2;
        uint64_t Azimuth_NI_4_FC : 12;
        uint64_t StdAzimuth_NI_4_FC : 8;
        uint64_t SNRdB_NI_4_FC : 8;
        uint64_t PowerDB_NI_4_FC : 8;
        uint64_t WDoppler10DB_NI_4_FC : 7;
        uint64_t Reserve1_1bit_NI_4_FC : 1;
        uint64_t WRange10DB_NI_4_FC : 8;
        uint64_t CoGDoppler_NI_4_FC : 16;
        uint64_t CoGRange_NI_4_FC : 16;
        uint64_t ValidXBeam_NI_3_FC : 1;
        uint64_t NoInfrastructure_NI_3_FC : 1;
        uint64_t Beam_NI_3_FC : 2;
        uint64_t Azimuth_NI_3_FC : 12;
        uint64_t StdAzimuth_NI_3_FC : 8;
        uint64_t SNRdB_NI_3_FC : 8;
        uint64_t PowerDB_NI_3_FC : 8;
        uint64_t WDoppler10DB_NI_3_FC : 7;
        uint64_t Reserve1_1bit_NI_3_FC : 1;
        uint64_t WRange10DB_NI_3_FC : 8;
        uint64_t CoGDoppler_NI_3_FC : 16;
        uint64_t CoGRange_NI_3_FC : 16;
        uint64_t ValidXBeam_NI_2_FC : 1;
        uint64_t NoInfrastructure_NI_2_FC : 1;
        uint64_t Beam_NI_2_FC : 2;
        uint64_t Azimuth_NI_2_FC__S1 : 4;
        uint64_t Azimuth_NI_2_FC__S0 : 8;
        uint64_t StdAzimuth_NI_2_FC : 8;
        uint64_t SNRdB_NI_2_FC : 8;
        uint64_t PowerDB_NI_2_FC : 8;
        uint64_t WDoppler10DB_NI_2_FC : 7;
        uint64_t Reserve1_1bit_NI_2_FC : 1;
        uint64_t WRange10DB_NI_2_FC : 8;
        uint64_t CoGDoppler_NI_2_FC : 16;
        uint64_t CoGRange_NI_2_FC : 16;
        uint64_t ValidXBeam_NI_1_FC : 1;
        uint64_t NoInfrastructure_NI_1_FC : 1;
        uint64_t Beam_NI_1_FC : 2;
        uint64_t Azimuth_NI_1_FC : 12;
        uint64_t StdAzimuth_NI_1_FC : 8;
        uint64_t SNRdB_NI_1_FC : 8;
        uint64_t PowerDB_NI_1_FC : 8;
        uint64_t WDoppler10DB_NI_1_FC : 7;
        uint64_t Reserve1_1bit_NI_1_FC : 1;
        uint64_t WRange10DB_NI_1_FC : 8;
        uint64_t CoGDoppler_NI_1_FC : 16;
        uint64_t CoGRange_NI_1_FC : 16;
        uint64_t ValidXBeam_NI_0_FC : 1;
        uint64_t NoInfrastructure_NI_0_FC : 1;
        uint64_t Beam_NI_0_FC : 2;
        uint64_t Azimuth_NI_0_FC : 12;
        uint64_t StdAzimuth_NI_0_FC : 8;
        uint64_t SNRdB_NI_0_FC : 8;
        uint64_t PowerDB_NI_0_FC : 8;
        uint64_t WDoppler10DB_NI_0_FC : 7;
        uint64_t Reserve1_1bit_NI_0_FC : 1;
        uint64_t WRange10DB_NI_0_FC : 8;
        uint64_t CoGDoppler_NI_0_FC : 16;
        uint64_t CoGRange_NI_0_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_0_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_0_fc_data_t data;
};

union cansig_mk_non_inf_detection_0_fc__azimuth_ni_2_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_2_FC__S1 : 4;
        uint32_t Azimuth_NI_2_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_10_FC_ID       (0x025C)
#define CANMSG_MK_NON_INF_DETECTION_10_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_10_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_10_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_54_FC : 1;
        uint64_t NoInfrastructure_NI_54_FC : 1;
        uint64_t Beam_NI_54_FC : 2;
        uint64_t Azimuth_NI_54_FC : 12;
        uint64_t StdAzimuth_NI_54_FC : 8;
        uint64_t SNRdB_NI_54_FC : 8;
        uint64_t PowerDB_NI_54_FC : 8;
        uint64_t WDoppler10DB_NI_54_FC : 7;
        uint64_t Reserve1_1bit_NI_54_FC : 1;
        uint64_t WRange10DB_NI_54_FC : 8;
        uint64_t CoGDoppler_NI_54_FC : 16;
        uint64_t CoGRange_NI_54_FC : 16;
        uint64_t ValidXBeam_NI_53_FC : 1;
        uint64_t NoInfrastructure_NI_53_FC : 1;
        uint64_t Beam_NI_53_FC : 2;
        uint64_t Azimuth_NI_53_FC : 12;
        uint64_t StdAzimuth_NI_53_FC : 8;
        uint64_t SNRdB_NI_53_FC : 8;
        uint64_t PowerDB_NI_53_FC : 8;
        uint64_t WDoppler10DB_NI_53_FC : 7;
        uint64_t Reserve1_1bit_NI_53_FC : 1;
        uint64_t WRange10DB_NI_53_FC : 8;
        uint64_t CoGDoppler_NI_53_FC : 16;
        uint64_t CoGRange_NI_53_FC : 16;
        uint64_t ValidXBeam_NI_52_FC : 1;
        uint64_t NoInfrastructure_NI_52_FC : 1;
        uint64_t Beam_NI_52_FC : 2;
        uint64_t Azimuth_NI_52_FC__S1 : 4;
        uint64_t Azimuth_NI_52_FC__S0 : 8;
        uint64_t StdAzimuth_NI_52_FC : 8;
        uint64_t SNRdB_NI_52_FC : 8;
        uint64_t PowerDB_NI_52_FC : 8;
        uint64_t WDoppler10DB_NI_52_FC : 7;
        uint64_t Reserve1_1bit_NI_52_FC : 1;
        uint64_t WRange10DB_NI_52_FC : 8;
        uint64_t CoGDoppler_NI_52_FC : 16;
        uint64_t CoGRange_NI_52_FC : 16;
        uint64_t ValidXBeam_NI_51_FC : 1;
        uint64_t NoInfrastructure_NI_51_FC : 1;
        uint64_t Beam_NI_51_FC : 2;
        uint64_t Azimuth_NI_51_FC : 12;
        uint64_t StdAzimuth_NI_51_FC : 8;
        uint64_t SNRdB_NI_51_FC : 8;
        uint64_t PowerDB_NI_51_FC : 8;
        uint64_t WDoppler10DB_NI_51_FC : 7;
        uint64_t Reserve1_1bit_NI_51_FC : 1;
        uint64_t WRange10DB_NI_51_FC : 8;
        uint64_t CoGDoppler_NI_51_FC : 16;
        uint64_t CoGRange_NI_51_FC : 16;
        uint64_t ValidXBeam_NI_50_FC : 1;
        uint64_t NoInfrastructure_NI_50_FC : 1;
        uint64_t Beam_NI_50_FC : 2;
        uint64_t Azimuth_NI_50_FC : 12;
        uint64_t StdAzimuth_NI_50_FC : 8;
        uint64_t SNRdB_NI_50_FC : 8;
        uint64_t PowerDB_NI_50_FC : 8;
        uint64_t WDoppler10DB_NI_50_FC : 7;
        uint64_t Reserve1_1bit_NI_50_FC : 1;
        uint64_t WRange10DB_NI_50_FC : 8;
        uint64_t CoGDoppler_NI_50_FC : 16;
        uint64_t CoGRange_NI_50_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_10_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_10_fc_data_t data;
};

union cansig_mk_non_inf_detection_10_fc__azimuth_ni_52_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_52_FC__S1 : 4;
        uint32_t Azimuth_NI_52_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_11_FC_ID       (0x025D)
#define CANMSG_MK_NON_INF_DETECTION_11_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_11_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_11_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_59_FC : 1;
        uint64_t NoInfrastructure_NI_59_FC : 1;
        uint64_t Beam_NI_59_FC : 2;
        uint64_t Azimuth_NI_59_FC : 12;
        uint64_t StdAzimuth_NI_59_FC : 8;
        uint64_t SNRdB_NI_59_FC : 8;
        uint64_t PowerDB_NI_59_FC : 8;
        uint64_t WDoppler10DB_NI_59_FC : 7;
        uint64_t Reserve1_1bit_NI_59_FC : 1;
        uint64_t WRange10DB_NI_59_FC : 8;
        uint64_t CoGDoppler_NI_59_FC : 16;
        uint64_t CoGRange_NI_59_FC : 16;
        uint64_t ValidXBeam_NI_58_FC : 1;
        uint64_t NoInfrastructure_NI_58_FC : 1;
        uint64_t Beam_NI_58_FC : 2;
        uint64_t Azimuth_NI_58_FC : 12;
        uint64_t StdAzimuth_NI_58_FC : 8;
        uint64_t SNRdB_NI_58_FC : 8;
        uint64_t PowerDB_NI_58_FC : 8;
        uint64_t WDoppler10DB_NI_58_FC : 7;
        uint64_t Reserve1_1bit_NI_58_FC : 1;
        uint64_t WRange10DB_NI_58_FC : 8;
        uint64_t CoGDoppler_NI_58_FC : 16;
        uint64_t CoGRange_NI_58_FC : 16;
        uint64_t ValidXBeam_NI_57_FC : 1;
        uint64_t NoInfrastructure_NI_57_FC : 1;
        uint64_t Beam_NI_57_FC : 2;
        uint64_t Azimuth_NI_57_FC__S1 : 4;
        uint64_t Azimuth_NI_57_FC__S0 : 8;
        uint64_t StdAzimuth_NI_57_FC : 8;
        uint64_t SNRdB_NI_57_FC : 8;
        uint64_t PowerDB_NI_57_FC : 8;
        uint64_t WDoppler10DB_NI_57_FC : 7;
        uint64_t Reserve1_1bit_NI_57_FC : 1;
        uint64_t WRange10DB_NI_57_FC : 8;
        uint64_t CoGDoppler_NI_57_FC : 16;
        uint64_t CoGRange_NI_57_FC : 16;
        uint64_t ValidXBeam_NI_56_FC : 1;
        uint64_t NoInfrastructure_NI_56_FC : 1;
        uint64_t Beam_NI_56_FC : 2;
        uint64_t Azimuth_NI_56_FC : 12;
        uint64_t StdAzimuth_NI_56_FC : 8;
        uint64_t SNRdB_NI_56_FC : 8;
        uint64_t PowerDB_NI_56_FC : 8;
        uint64_t WDoppler10DB_NI_56_FC : 7;
        uint64_t Reserve1_1bit_NI_56_FC : 1;
        uint64_t WRange10DB_NI_56_FC : 8;
        uint64_t CoGDoppler_NI_56_FC : 16;
        uint64_t CoGRange_NI_56_FC : 16;
        uint64_t ValidXBeam_NI_55_FC : 1;
        uint64_t NoInfrastructure_NI_55_FC : 1;
        uint64_t Beam_NI_55_FC : 2;
        uint64_t Azimuth_NI_55_FC : 12;
        uint64_t StdAzimuth_NI_55_FC : 8;
        uint64_t SNRdB_NI_55_FC : 8;
        uint64_t PowerDB_NI_55_FC : 8;
        uint64_t WDoppler10DB_NI_55_FC : 7;
        uint64_t Reserve1_1bit_NI_55_FC : 1;
        uint64_t WRange10DB_NI_55_FC : 8;
        uint64_t CoGDoppler_NI_55_FC : 16;
        uint64_t CoGRange_NI_55_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_11_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_11_fc_data_t data;
};

union cansig_mk_non_inf_detection_11_fc__azimuth_ni_57_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_57_FC__S1 : 4;
        uint32_t Azimuth_NI_57_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_12_FC_ID       (0x025E)
#define CANMSG_MK_NON_INF_DETECTION_12_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_12_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_12_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_64_FC : 1;
        uint64_t NoInfrastructure_NI_64_FC : 1;
        uint64_t Beam_NI_64_FC : 2;
        uint64_t Azimuth_NI_64_FC : 12;
        uint64_t StdAzimuth_NI_64_FC : 8;
        uint64_t SNRdB_NI_64_FC : 8;
        uint64_t PowerDB_NI_64_FC : 8;
        uint64_t WDoppler10DB_NI_64_FC : 7;
        uint64_t Reserve1_1bit_NI_64_FC : 1;
        uint64_t WRange10DB_NI_64_FC : 8;
        uint64_t CoGDoppler_NI_64_FC : 16;
        uint64_t CoGRange_NI_64_FC : 16;
        uint64_t ValidXBeam_NI_63_FC : 1;
        uint64_t NoInfrastructure_NI_63_FC : 1;
        uint64_t Beam_NI_63_FC : 2;
        uint64_t Azimuth_NI_63_FC : 12;
        uint64_t StdAzimuth_NI_63_FC : 8;
        uint64_t SNRdB_NI_63_FC : 8;
        uint64_t PowerDB_NI_63_FC : 8;
        uint64_t WDoppler10DB_NI_63_FC : 7;
        uint64_t Reserve1_1bit_NI_63_FC : 1;
        uint64_t WRange10DB_NI_63_FC : 8;
        uint64_t CoGDoppler_NI_63_FC : 16;
        uint64_t CoGRange_NI_63_FC : 16;
        uint64_t ValidXBeam_NI_62_FC : 1;
        uint64_t NoInfrastructure_NI_62_FC : 1;
        uint64_t Beam_NI_62_FC : 2;
        uint64_t Azimuth_NI_62_FC__S1 : 4;
        uint64_t Azimuth_NI_62_FC__S0 : 8;
        uint64_t StdAzimuth_NI_62_FC : 8;
        uint64_t SNRdB_NI_62_FC : 8;
        uint64_t PowerDB_NI_62_FC : 8;
        uint64_t WDoppler10DB_NI_62_FC : 7;
        uint64_t Reserve1_1bit_NI_62_FC : 1;
        uint64_t WRange10DB_NI_62_FC : 8;
        uint64_t CoGDoppler_NI_62_FC : 16;
        uint64_t CoGRange_NI_62_FC : 16;
        uint64_t ValidXBeam_NI_61_FC : 1;
        uint64_t NoInfrastructure_NI_61_FC : 1;
        uint64_t Beam_NI_61_FC : 2;
        uint64_t Azimuth_NI_61_FC : 12;
        uint64_t StdAzimuth_NI_61_FC : 8;
        uint64_t SNRdB_NI_61_FC : 8;
        uint64_t PowerDB_NI_61_FC : 8;
        uint64_t WDoppler10DB_NI_61_FC : 7;
        uint64_t Reserve1_1bit_NI_61_FC : 1;
        uint64_t WRange10DB_NI_61_FC : 8;
        uint64_t CoGDoppler_NI_61_FC : 16;
        uint64_t CoGRange_NI_61_FC : 16;
        uint64_t ValidXBeam_NI_60_FC : 1;
        uint64_t NoInfrastructure_NI_60_FC : 1;
        uint64_t Beam_NI_60_FC : 2;
        uint64_t Azimuth_NI_60_FC : 12;
        uint64_t StdAzimuth_NI_60_FC : 8;
        uint64_t SNRdB_NI_60_FC : 8;
        uint64_t PowerDB_NI_60_FC : 8;
        uint64_t WDoppler10DB_NI_60_FC : 7;
        uint64_t Reserve1_1bit_NI_60_FC : 1;
        uint64_t WRange10DB_NI_60_FC : 8;
        uint64_t CoGDoppler_NI_60_FC : 16;
        uint64_t CoGRange_NI_60_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_12_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_12_fc_data_t data;
};

union cansig_mk_non_inf_detection_12_fc__azimuth_ni_62_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_62_FC__S1 : 4;
        uint32_t Azimuth_NI_62_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_13_FC_ID       (0x025F)
#define CANMSG_MK_NON_INF_DETECTION_13_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_13_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_13_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_69_FC : 1;
        uint64_t NoInfrastructure_NI_69_FC : 1;
        uint64_t Beam_NI_69_FC : 2;
        uint64_t Azimuth_NI_69_FC : 12;
        uint64_t StdAzimuth_NI_69_FC : 8;
        uint64_t SNRdB_NI_69_FC : 8;
        uint64_t PowerDB_NI_69_FC : 8;
        uint64_t WDoppler10DB_NI_69_FC : 7;
        uint64_t Reserve1_1bit_NI_69_FC : 1;
        uint64_t WRange10DB_NI_69_FC : 8;
        uint64_t CoGDoppler_NI_69_FC : 16;
        uint64_t CoGRange_NI_69_FC : 16;
        uint64_t ValidXBeam_NI_68_FC : 1;
        uint64_t NoInfrastructure_NI_68_FC : 1;
        uint64_t Beam_NI_68_FC : 2;
        uint64_t Azimuth_NI_68_FC : 12;
        uint64_t StdAzimuth_NI_68_FC : 8;
        uint64_t SNRdB_NI_68_FC : 8;
        uint64_t PowerDB_NI_68_FC : 8;
        uint64_t WDoppler10DB_NI_68_FC : 7;
        uint64_t Reserve1_1bit_NI_68_FC : 1;
        uint64_t WRange10DB_NI_68_FC : 8;
        uint64_t CoGDoppler_NI_68_FC : 16;
        uint64_t CoGRange_NI_68_FC : 16;
        uint64_t ValidXBeam_NI_67_FC : 1;
        uint64_t NoInfrastructure_NI_67_FC : 1;
        uint64_t Beam_NI_67_FC : 2;
        uint64_t Azimuth_NI_67_FC__S1 : 4;
        uint64_t Azimuth_NI_67_FC__S0 : 8;
        uint64_t StdAzimuth_NI_67_FC : 8;
        uint64_t SNRdB_NI_67_FC : 8;
        uint64_t PowerDB_NI_67_FC : 8;
        uint64_t WDoppler10DB_NI_67_FC : 7;
        uint64_t Reserve1_1bit_NI_67_FC : 1;
        uint64_t WRange10DB_NI_67_FC : 8;
        uint64_t CoGDoppler_NI_67_FC : 16;
        uint64_t CoGRange_NI_67_FC : 16;
        uint64_t ValidXBeam_NI_66_FC : 1;
        uint64_t NoInfrastructure_NI_66_FC : 1;
        uint64_t Beam_NI_66_FC : 2;
        uint64_t Azimuth_NI_66_FC : 12;
        uint64_t StdAzimuth_NI_66_FC : 8;
        uint64_t SNRdB_NI_66_FC : 8;
        uint64_t PowerDB_NI_66_FC : 8;
        uint64_t WDoppler10DB_NI_66_FC : 7;
        uint64_t Reserve1_1bit_NI_66_FC : 1;
        uint64_t WRange10DB_NI_66_FC : 8;
        uint64_t CoGDoppler_NI_66_FC : 16;
        uint64_t CoGRange_NI_66_FC : 16;
        uint64_t ValidXBeam_NI_65_FC : 1;
        uint64_t NoInfrastructure_NI_65_FC : 1;
        uint64_t Beam_NI_65_FC : 2;
        uint64_t Azimuth_NI_65_FC : 12;
        uint64_t StdAzimuth_NI_65_FC : 8;
        uint64_t SNRdB_NI_65_FC : 8;
        uint64_t PowerDB_NI_65_FC : 8;
        uint64_t WDoppler10DB_NI_65_FC : 7;
        uint64_t Reserve1_1bit_NI_65_FC : 1;
        uint64_t WRange10DB_NI_65_FC : 8;
        uint64_t CoGDoppler_NI_65_FC : 16;
        uint64_t CoGRange_NI_65_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_13_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_13_fc_data_t data;
};

union cansig_mk_non_inf_detection_13_fc__azimuth_ni_67_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_67_FC__S1 : 4;
        uint32_t Azimuth_NI_67_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_14_FC_ID       (0x0260)
#define CANMSG_MK_NON_INF_DETECTION_14_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_14_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_14_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_74_FC : 1;
        uint64_t NoInfrastructure_NI_74_FC : 1;
        uint64_t Beam_NI_74_FC : 2;
        uint64_t Azimuth_NI_74_FC : 12;
        uint64_t StdAzimuth_NI_74_FC : 8;
        uint64_t SNRdB_NI_74_FC : 8;
        uint64_t PowerDB_NI_74_FC : 8;
        uint64_t WDoppler10DB_NI_74_FC : 7;
        uint64_t Reserve1_1bit_NI_74_FC : 1;
        uint64_t WRange10DB_NI_74_FC : 8;
        uint64_t CoGDoppler_NI_74_FC : 16;
        uint64_t CoGRange_NI_74_FC : 16;
        uint64_t ValidXBeam_NI_73_FC : 1;
        uint64_t NoInfrastructure_NI_73_FC : 1;
        uint64_t Beam_NI_73_FC : 2;
        uint64_t Azimuth_NI_73_FC : 12;
        uint64_t StdAzimuth_NI_73_FC : 8;
        uint64_t SNRdB_NI_73_FC : 8;
        uint64_t PowerDB_NI_73_FC : 8;
        uint64_t WDoppler10DB_NI_73_FC : 7;
        uint64_t Reserve1_1bit_NI_73_FC : 1;
        uint64_t WRange10DB_NI_73_FC : 8;
        uint64_t CoGDoppler_NI_73_FC : 16;
        uint64_t CoGRange_NI_73_FC : 16;
        uint64_t ValidXBeam_NI_72_FC : 1;
        uint64_t NoInfrastructure_NI_72_FC : 1;
        uint64_t Beam_NI_72_FC : 2;
        uint64_t Azimuth_NI_72_FC__S1 : 4;
        uint64_t Azimuth_NI_72_FC__S0 : 8;
        uint64_t StdAzimuth_NI_72_FC : 8;
        uint64_t SNRdB_NI_72_FC : 8;
        uint64_t PowerDB_NI_72_FC : 8;
        uint64_t WDoppler10DB_NI_72_FC : 7;
        uint64_t Reserve1_1bit_NI_72_FC : 1;
        uint64_t WRange10DB_NI_72_FC : 8;
        uint64_t CoGDoppler_NI_72_FC : 16;
        uint64_t CoGRange_NI_72_FC : 16;
        uint64_t ValidXBeam_NI_71_FC : 1;
        uint64_t NoInfrastructure_NI_71_FC : 1;
        uint64_t Beam_NI_71_FC : 2;
        uint64_t Azimuth_NI_71_FC : 12;
        uint64_t StdAzimuth_NI_71_FC : 8;
        uint64_t SNRdB_NI_71_FC : 8;
        uint64_t PowerDB_NI_71_FC : 8;
        uint64_t WDoppler10DB_NI_71_FC : 7;
        uint64_t Reserve1_1bit_NI_71_FC : 1;
        uint64_t WRange10DB_NI_71_FC : 8;
        uint64_t CoGDoppler_NI_71_FC : 16;
        uint64_t CoGRange_NI_71_FC : 16;
        uint64_t ValidXBeam_NI_70_FC : 1;
        uint64_t NoInfrastructure_NI_70_FC : 1;
        uint64_t Beam_NI_70_FC : 2;
        uint64_t Azimuth_NI_70_FC : 12;
        uint64_t StdAzimuth_NI_70_FC : 8;
        uint64_t SNRdB_NI_70_FC : 8;
        uint64_t PowerDB_NI_70_FC : 8;
        uint64_t WDoppler10DB_NI_70_FC : 7;
        uint64_t Reserve1_1bit_NI_70_FC : 1;
        uint64_t WRange10DB_NI_70_FC : 8;
        uint64_t CoGDoppler_NI_70_FC : 16;
        uint64_t CoGRange_NI_70_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_14_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_14_fc_data_t data;
};

union cansig_mk_non_inf_detection_14_fc__azimuth_ni_72_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_72_FC__S1 : 4;
        uint32_t Azimuth_NI_72_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_15_FC_ID       (0x0261)
#define CANMSG_MK_NON_INF_DETECTION_15_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_15_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_15_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_79_FC : 1;
        uint64_t NoInfrastructure_NI_79_FC : 1;
        uint64_t Beam_NI_79_FC : 2;
        uint64_t Azimuth_NI_79_FC : 12;
        uint64_t StdAzimuth_NI_79_FC : 8;
        uint64_t SNRdB_NI_79_FC : 8;
        uint64_t PowerDB_NI_79_FC : 8;
        uint64_t WDoppler10DB_NI_79_FC : 7;
        uint64_t Reserve1_1bit_NI_79_FC : 1;
        uint64_t WRange10DB_NI_79_FC : 8;
        uint64_t CoGDoppler_NI_79_FC : 16;
        uint64_t CoGRange_NI_79_FC : 16;
        uint64_t ValidXBeam_NI_78_FC : 1;
        uint64_t NoInfrastructure_NI_78_FC : 1;
        uint64_t Beam_NI_78_FC : 2;
        uint64_t Azimuth_NI_78_FC : 12;
        uint64_t StdAzimuth_NI_78_FC : 8;
        uint64_t SNRdB_NI_78_FC : 8;
        uint64_t PowerDB_NI_78_FC : 8;
        uint64_t WDoppler10DB_NI_78_FC : 7;
        uint64_t Reserve1_1bit_NI_78_FC : 1;
        uint64_t WRange10DB_NI_78_FC : 8;
        uint64_t CoGDoppler_NI_78_FC : 16;
        uint64_t CoGRange_NI_78_FC : 16;
        uint64_t ValidXBeam_NI_77_FC : 1;
        uint64_t NoInfrastructure_NI_77_FC : 1;
        uint64_t Beam_NI_77_FC : 2;
        uint64_t Azimuth_NI_77_FC__S1 : 4;
        uint64_t Azimuth_NI_77_FC__S0 : 8;
        uint64_t StdAzimuth_NI_77_FC : 8;
        uint64_t SNRdB_NI_77_FC : 8;
        uint64_t PowerDB_NI_77_FC : 8;
        uint64_t WDoppler10DB_NI_77_FC : 7;
        uint64_t Reserve1_1bit_NI_77_FC : 1;
        uint64_t WRange10DB_NI_77_FC : 8;
        uint64_t CoGDoppler_NI_77_FC : 16;
        uint64_t CoGRange_NI_77_FC : 16;
        uint64_t ValidXBeam_NI_76_FC : 1;
        uint64_t NoInfrastructure_NI_76_FC : 1;
        uint64_t Beam_NI_76_FC : 2;
        uint64_t Azimuth_NI_76_FC : 12;
        uint64_t StdAzimuth_NI_76_FC : 8;
        uint64_t SNRdB_NI_76_FC : 8;
        uint64_t PowerDB_NI_76_FC : 8;
        uint64_t WDoppler10DB_NI_76_FC : 7;
        uint64_t Reserve1_1bit_NI_76_FC : 1;
        uint64_t WRange10DB_NI_76_FC : 8;
        uint64_t CoGDoppler_NI_76_FC : 16;
        uint64_t CoGRange_NI_76_FC : 16;
        uint64_t ValidXBeam_NI_75_FC : 1;
        uint64_t NoInfrastructure_NI_75_FC : 1;
        uint64_t Beam_NI_75_FC : 2;
        uint64_t Azimuth_NI_75_FC : 12;
        uint64_t StdAzimuth_NI_75_FC : 8;
        uint64_t SNRdB_NI_75_FC : 8;
        uint64_t PowerDB_NI_75_FC : 8;
        uint64_t WDoppler10DB_NI_75_FC : 7;
        uint64_t Reserve1_1bit_NI_75_FC : 1;
        uint64_t WRange10DB_NI_75_FC : 8;
        uint64_t CoGDoppler_NI_75_FC : 16;
        uint64_t CoGRange_NI_75_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_15_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_15_fc_data_t data;
};

union cansig_mk_non_inf_detection_15_fc__azimuth_ni_77_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_77_FC__S1 : 4;
        uint32_t Azimuth_NI_77_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_16_FC_ID       (0x0262)
#define CANMSG_MK_NON_INF_DETECTION_16_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_16_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_16_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_84_FC : 1;
        uint64_t NoInfrastructure_NI_84_FC : 1;
        uint64_t Beam_NI_84_FC : 2;
        uint64_t Azimuth_NI_84_FC : 12;
        uint64_t StdAzimuth_NI_84_FC : 8;
        uint64_t SNRdB_NI_84_FC : 8;
        uint64_t PowerDB_NI_84_FC : 8;
        uint64_t WDoppler10DB_NI_84_FC : 7;
        uint64_t Reserve1_1bit_NI_84_FC : 1;
        uint64_t WRange10DB_NI_84_FC : 8;
        uint64_t CoGDoppler_NI_84_FC : 16;
        uint64_t CoGRange_NI_84_FC : 16;
        uint64_t ValidXBeam_NI_83_FC : 1;
        uint64_t NoInfrastructure_NI_83_FC : 1;
        uint64_t Beam_NI_83_FC : 2;
        uint64_t Azimuth_NI_83_FC : 12;
        uint64_t StdAzimuth_NI_83_FC : 8;
        uint64_t SNRdB_NI_83_FC : 8;
        uint64_t PowerDB_NI_83_FC : 8;
        uint64_t WDoppler10DB_NI_83_FC : 7;
        uint64_t Reserve1_1bit_NI_83_FC : 1;
        uint64_t WRange10DB_NI_83_FC : 8;
        uint64_t CoGDoppler_NI_83_FC : 16;
        uint64_t CoGRange_NI_83_FC : 16;
        uint64_t ValidXBeam_NI_82_FC : 1;
        uint64_t NoInfrastructure_NI_82_FC : 1;
        uint64_t Beam_NI_82_FC : 2;
        uint64_t Azimuth_NI_82_FC__S1 : 4;
        uint64_t Azimuth_NI_82_FC__S0 : 8;
        uint64_t StdAzimuth_NI_82_FC : 8;
        uint64_t SNRdB_NI_82_FC : 8;
        uint64_t PowerDB_NI_82_FC : 8;
        uint64_t WDoppler10DB_NI_82_FC : 7;
        uint64_t Reserve1_1bit_NI_82_FC : 1;
        uint64_t WRange10DB_NI_82_FC : 8;
        uint64_t CoGDoppler_NI_82_FC : 16;
        uint64_t CoGRange_NI_82_FC : 16;
        uint64_t ValidXBeam_NI_81_FC : 1;
        uint64_t NoInfrastructure_NI_81_FC : 1;
        uint64_t Beam_NI_81_FC : 2;
        uint64_t Azimuth_NI_81_FC : 12;
        uint64_t StdAzimuth_NI_81_FC : 8;
        uint64_t SNRdB_NI_81_FC : 8;
        uint64_t PowerDB_NI_81_FC : 8;
        uint64_t WDoppler10DB_NI_81_FC : 7;
        uint64_t Reserve1_1bit_NI_81_FC : 1;
        uint64_t WRange10DB_NI_81_FC : 8;
        uint64_t CoGDoppler_NI_81_FC : 16;
        uint64_t CoGRange_NI_81_FC : 16;
        uint64_t ValidXBeam_NI_80_FC : 1;
        uint64_t NoInfrastructure_NI_80_FC : 1;
        uint64_t Beam_NI_80_FC : 2;
        uint64_t Azimuth_NI_80_FC : 12;
        uint64_t StdAzimuth_NI_80_FC : 8;
        uint64_t SNRdB_NI_80_FC : 8;
        uint64_t PowerDB_NI_80_FC : 8;
        uint64_t WDoppler10DB_NI_80_FC : 7;
        uint64_t Reserve1_1bit_NI_80_FC : 1;
        uint64_t WRange10DB_NI_80_FC : 8;
        uint64_t CoGDoppler_NI_80_FC : 16;
        uint64_t CoGRange_NI_80_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_16_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_16_fc_data_t data;
};

union cansig_mk_non_inf_detection_16_fc__azimuth_ni_82_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_82_FC__S1 : 4;
        uint32_t Azimuth_NI_82_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_17_FC_ID       (0x0263)
#define CANMSG_MK_NON_INF_DETECTION_17_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_17_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_17_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_89_FC : 1;
        uint64_t NoInfrastructure_NI_89_FC : 1;
        uint64_t Beam_NI_89_FC : 2;
        uint64_t Azimuth_NI_89_FC : 12;
        uint64_t StdAzimuth_NI_89_FC : 8;
        uint64_t SNRdB_NI_89_FC : 8;
        uint64_t PowerDB_NI_89_FC : 8;
        uint64_t WDoppler10DB_NI_89_FC : 7;
        uint64_t Reserve1_1bit_NI_89_FC : 1;
        uint64_t WRange10DB_NI_89_FC : 8;
        uint64_t CoGDoppler_NI_89_FC : 16;
        uint64_t CoGRange_NI_89_FC : 16;
        uint64_t ValidXBeam_NI_88_FC : 1;
        uint64_t NoInfrastructure_NI_88_FC : 1;
        uint64_t Beam_NI_88_FC : 2;
        uint64_t Azimuth_NI_88_FC : 12;
        uint64_t StdAzimuth_NI_88_FC : 8;
        uint64_t SNRdB_NI_88_FC : 8;
        uint64_t PowerDB_NI_88_FC : 8;
        uint64_t WDoppler10DB_NI_88_FC : 7;
        uint64_t Reserve1_1bit_NI_88_FC : 1;
        uint64_t WRange10DB_NI_88_FC : 8;
        uint64_t CoGDoppler_NI_88_FC : 16;
        uint64_t CoGRange_NI_88_FC : 16;
        uint64_t ValidXBeam_NI_87_FC : 1;
        uint64_t NoInfrastructure_NI_87_FC : 1;
        uint64_t Beam_NI_87_FC : 2;
        uint64_t Azimuth_NI_87_FC__S1 : 4;
        uint64_t Azimuth_NI_87_FC__S0 : 8;
        uint64_t StdAzimuth_NI_87_FC : 8;
        uint64_t SNRdB_NI_87_FC : 8;
        uint64_t PowerDB_NI_87_FC : 8;
        uint64_t WDoppler10DB_NI_87_FC : 7;
        uint64_t Reserve1_1bit_NI_87_FC : 1;
        uint64_t WRange10DB_NI_87_FC : 8;
        uint64_t CoGDoppler_NI_87_FC : 16;
        uint64_t CoGRange_NI_87_FC : 16;
        uint64_t ValidXBeam_NI_86_FC : 1;
        uint64_t NoInfrastructure_NI_86_FC : 1;
        uint64_t Beam_NI_86_FC : 2;
        uint64_t Azimuth_NI_86_FC : 12;
        uint64_t StdAzimuth_NI_86_FC : 8;
        uint64_t SNRdB_NI_86_FC : 8;
        uint64_t PowerDB_NI_86_FC : 8;
        uint64_t WDoppler10DB_NI_86_FC : 7;
        uint64_t Reserve1_1bit_NI_86_FC : 1;
        uint64_t WRange10DB_NI_86_FC : 8;
        uint64_t CoGDoppler_NI_86_FC : 16;
        uint64_t CoGRange_NI_86_FC : 16;
        uint64_t ValidXBeam_NI_85_FC : 1;
        uint64_t NoInfrastructure_NI_85_FC : 1;
        uint64_t Beam_NI_85_FC : 2;
        uint64_t Azimuth_NI_85_FC : 12;
        uint64_t StdAzimuth_NI_85_FC : 8;
        uint64_t SNRdB_NI_85_FC : 8;
        uint64_t PowerDB_NI_85_FC : 8;
        uint64_t WDoppler10DB_NI_85_FC : 7;
        uint64_t Reserve1_1bit_NI_85_FC : 1;
        uint64_t WRange10DB_NI_85_FC : 8;
        uint64_t CoGDoppler_NI_85_FC : 16;
        uint64_t CoGRange_NI_85_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_17_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_17_fc_data_t data;
};

union cansig_mk_non_inf_detection_17_fc__azimuth_ni_87_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_87_FC__S1 : 4;
        uint32_t Azimuth_NI_87_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_18_FC_ID       (0x0264)
#define CANMSG_MK_NON_INF_DETECTION_18_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_18_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_18_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_94_FC : 1;
        uint64_t NoInfrastructure_NI_94_FC : 1;
        uint64_t Beam_NI_94_FC : 2;
        uint64_t Azimuth_NI_94_FC : 12;
        uint64_t StdAzimuth_NI_94_FC : 8;
        uint64_t SNRdB_NI_94_FC : 8;
        uint64_t PowerDB_NI_94_FC : 8;
        uint64_t WDoppler10DB_NI_94_FC : 7;
        uint64_t Reserve1_1bit_NI_94_FC : 1;
        uint64_t WRange10DB_NI_94_FC : 8;
        uint64_t CoGDoppler_NI_94_FC : 16;
        uint64_t CoGRange_NI_94_FC : 16;
        uint64_t ValidXBeam_NI_93_FC : 1;
        uint64_t NoInfrastructure_NI_93_FC : 1;
        uint64_t Beam_NI_93_FC : 2;
        uint64_t Azimuth_NI_93_FC : 12;
        uint64_t StdAzimuth_NI_93_FC : 8;
        uint64_t SNRdB_NI_93_FC : 8;
        uint64_t PowerDB_NI_93_FC : 8;
        uint64_t WDoppler10DB_NI_93_FC : 7;
        uint64_t Reserve1_1bit_NI_93_FC : 1;
        uint64_t WRange10DB_NI_93_FC : 8;
        uint64_t CoGDoppler_NI_93_FC : 16;
        uint64_t CoGRange_NI_93_FC : 16;
        uint64_t ValidXBeam_NI_92_FC : 1;
        uint64_t NoInfrastructure_NI_92_FC : 1;
        uint64_t Beam_NI_92_FC : 2;
        uint64_t Azimuth_NI_92_FC__S1 : 4;
        uint64_t Azimuth_NI_92_FC__S0 : 8;
        uint64_t StdAzimuth_NI_92_FC : 8;
        uint64_t SNRdB_NI_92_FC : 8;
        uint64_t PowerDB_NI_92_FC : 8;
        uint64_t WDoppler10DB_NI_92_FC : 7;
        uint64_t Reserve1_1bit_NI_92_FC : 1;
        uint64_t WRange10DB_NI_92_FC : 8;
        uint64_t CoGDoppler_NI_92_FC : 16;
        uint64_t CoGRange_NI_92_FC : 16;
        uint64_t ValidXBeam_NI_91_FC : 1;
        uint64_t NoInfrastructure_NI_91_FC : 1;
        uint64_t Beam_NI_91_FC : 2;
        uint64_t Azimuth_NI_91_FC : 12;
        uint64_t StdAzimuth_NI_91_FC : 8;
        uint64_t SNRdB_NI_91_FC : 8;
        uint64_t PowerDB_NI_91_FC : 8;
        uint64_t WDoppler10DB_NI_91_FC : 7;
        uint64_t Reserve1_1bit_NI_91_FC : 1;
        uint64_t WRange10DB_NI_91_FC : 8;
        uint64_t CoGDoppler_NI_91_FC : 16;
        uint64_t CoGRange_NI_91_FC : 16;
        uint64_t ValidXBeam_NI_90_FC : 1;
        uint64_t NoInfrastructure_NI_90_FC : 1;
        uint64_t Beam_NI_90_FC : 2;
        uint64_t Azimuth_NI_90_FC : 12;
        uint64_t StdAzimuth_NI_90_FC : 8;
        uint64_t SNRdB_NI_90_FC : 8;
        uint64_t PowerDB_NI_90_FC : 8;
        uint64_t WDoppler10DB_NI_90_FC : 7;
        uint64_t Reserve1_1bit_NI_90_FC : 1;
        uint64_t WRange10DB_NI_90_FC : 8;
        uint64_t CoGDoppler_NI_90_FC : 16;
        uint64_t CoGRange_NI_90_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_18_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_18_fc_data_t data;
};

union cansig_mk_non_inf_detection_18_fc__azimuth_ni_92_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_92_FC__S1 : 4;
        uint32_t Azimuth_NI_92_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_19_FC_ID       (0x0265)
#define CANMSG_MK_NON_INF_DETECTION_19_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_19_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_19_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_99_FC : 1;
        uint64_t NoInfrastructure_NI_99_FC : 1;
        uint64_t Beam_NI_99_FC : 2;
        uint64_t Azimuth_NI_99_FC : 12;
        uint64_t StdAzimuth_NI_99_FC : 8;
        uint64_t SNRdB_NI_99_FC : 8;
        uint64_t PowerDB_NI_99_FC : 8;
        uint64_t WDoppler10DB_NI_99_FC : 7;
        uint64_t Reserve1_1bit_NI_99_FC : 1;
        uint64_t WRange10DB_NI_99_FC : 8;
        uint64_t CoGDoppler_NI_99_FC : 16;
        uint64_t CoGRange_NI_99_FC : 16;
        uint64_t ValidXBeam_NI_98_FC : 1;
        uint64_t NoInfrastructure_NI_98_FC : 1;
        uint64_t Beam_NI_98_FC : 2;
        uint64_t Azimuth_NI_98_FC : 12;
        uint64_t StdAzimuth_NI_98_FC : 8;
        uint64_t SNRdB_NI_98_FC : 8;
        uint64_t PowerDB_NI_98_FC : 8;
        uint64_t WDoppler10DB_NI_98_FC : 7;
        uint64_t Reserve1_1bit_NI_98_FC : 1;
        uint64_t WRange10DB_NI_98_FC : 8;
        uint64_t CoGDoppler_NI_98_FC : 16;
        uint64_t CoGRange_NI_98_FC : 16;
        uint64_t ValidXBeam_NI_97_FC : 1;
        uint64_t NoInfrastructure_NI_97_FC : 1;
        uint64_t Beam_NI_97_FC : 2;
        uint64_t Azimuth_NI_97_FC__S1 : 4;
        uint64_t Azimuth_NI_97_FC__S0 : 8;
        uint64_t StdAzimuth_NI_97_FC : 8;
        uint64_t SNRdB_NI_97_FC : 8;
        uint64_t PowerDB_NI_97_FC : 8;
        uint64_t WDoppler10DB_NI_97_FC : 7;
        uint64_t Reserve1_1bit_NI_97_FC : 1;
        uint64_t WRange10DB_NI_97_FC : 8;
        uint64_t CoGDoppler_NI_97_FC : 16;
        uint64_t CoGRange_NI_97_FC : 16;
        uint64_t ValidXBeam_NI_96_FC : 1;
        uint64_t NoInfrastructure_NI_96_FC : 1;
        uint64_t Beam_NI_96_FC : 2;
        uint64_t Azimuth_NI_96_FC : 12;
        uint64_t StdAzimuth_NI_96_FC : 8;
        uint64_t SNRdB_NI_96_FC : 8;
        uint64_t PowerDB_NI_96_FC : 8;
        uint64_t WDoppler10DB_NI_96_FC : 7;
        uint64_t Reserve1_1bit_NI_96_FC : 1;
        uint64_t WRange10DB_NI_96_FC : 8;
        uint64_t CoGDoppler_NI_96_FC : 16;
        uint64_t CoGRange_NI_96_FC : 16;
        uint64_t ValidXBeam_NI_95_FC : 1;
        uint64_t NoInfrastructure_NI_95_FC : 1;
        uint64_t Beam_NI_95_FC : 2;
        uint64_t Azimuth_NI_95_FC : 12;
        uint64_t StdAzimuth_NI_95_FC : 8;
        uint64_t SNRdB_NI_95_FC : 8;
        uint64_t PowerDB_NI_95_FC : 8;
        uint64_t WDoppler10DB_NI_95_FC : 7;
        uint64_t Reserve1_1bit_NI_95_FC : 1;
        uint64_t WRange10DB_NI_95_FC : 8;
        uint64_t CoGDoppler_NI_95_FC : 16;
        uint64_t CoGRange_NI_95_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_19_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_19_fc_data_t data;
};

union cansig_mk_non_inf_detection_19_fc__azimuth_ni_97_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_97_FC__S1 : 4;
        uint32_t Azimuth_NI_97_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_1_FC_ID        (0x0253)
#define CANMSG_MK_NON_INF_DETECTION_1_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_1_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_1_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_9_FC : 1;
        uint64_t NoInfrastructure_NI_9_FC : 1;
        uint64_t Beam_NI_9_FC : 2;
        uint64_t Azimuth_NI_9_FC : 12;
        uint64_t StdAzimuth_NI_9_FC : 8;
        uint64_t SNRdB_NI_9_FC : 8;
        uint64_t PowerDB_NI_9_FC : 8;
        uint64_t WDoppler10DB_NI_9_FC : 7;
        uint64_t Reserve1_1bit_NI_9_FC : 1;
        uint64_t WRange10DB_NI_9_FC : 8;
        uint64_t CoGDoppler_NI_9_FC : 16;
        uint64_t CoGRange_NI_9_FC : 16;
        uint64_t ValidXBeam_NI_8_FC : 1;
        uint64_t NoInfrastructure_NI_8_FC : 1;
        uint64_t Beam_NI_8_FC : 2;
        uint64_t Azimuth_NI_8_FC : 12;
        uint64_t StdAzimuth_NI_8_FC : 8;
        uint64_t SNRdB_NI_8_FC : 8;
        uint64_t PowerDB_NI_8_FC : 8;
        uint64_t WDoppler10DB_NI_8_FC : 7;
        uint64_t Reserve1_1bit_NI_8_FC : 1;
        uint64_t WRange10DB_NI_8_FC : 8;
        uint64_t CoGDoppler_NI_8_FC : 16;
        uint64_t CoGRange_NI_8_FC : 16;
        uint64_t ValidXBeam_NI_7_FC : 1;
        uint64_t NoInfrastructure_NI_7_FC : 1;
        uint64_t Beam_NI_7_FC : 2;
        uint64_t Azimuth_NI_7_FC__S1 : 4;
        uint64_t Azimuth_NI_7_FC__S0 : 8;
        uint64_t StdAzimuth_NI_7_FC : 8;
        uint64_t SNRdB_NI_7_FC : 8;
        uint64_t PowerDB_NI_7_FC : 8;
        uint64_t WDoppler10DB_NI_7_FC : 7;
        uint64_t Reserve1_1bit_NI_7_FC : 1;
        uint64_t WRange10DB_NI_7_FC : 8;
        uint64_t CoGDoppler_NI_7_FC : 16;
        uint64_t CoGRange_NI_7_FC : 16;
        uint64_t ValidXBeam_NI_6_FC : 1;
        uint64_t NoInfrastructure_NI_6_FC : 1;
        uint64_t Beam_NI_6_FC : 2;
        uint64_t Azimuth_NI_6_FC : 12;
        uint64_t StdAzimuth_NI_6_FC : 8;
        uint64_t SNRdB_NI_6_FC : 8;
        uint64_t PowerDB_NI_6_FC : 8;
        uint64_t WDoppler10DB_NI_6_FC : 7;
        uint64_t Reserve1_1bit_NI_6_FC : 1;
        uint64_t WRange10DB_NI_6_FC : 8;
        uint64_t CoGDoppler_NI_6_FC : 16;
        uint64_t CoGRange_NI_6_FC : 16;
        uint64_t ValidXBeam_NI_5_FC : 1;
        uint64_t NoInfrastructure_NI_5_FC : 1;
        uint64_t Beam_NI_5_FC : 2;
        uint64_t Azimuth_NI_5_FC : 12;
        uint64_t StdAzimuth_NI_5_FC : 8;
        uint64_t SNRdB_NI_5_FC : 8;
        uint64_t PowerDB_NI_5_FC : 8;
        uint64_t WDoppler10DB_NI_5_FC : 7;
        uint64_t Reserve1_1bit_NI_5_FC : 1;
        uint64_t WRange10DB_NI_5_FC : 8;
        uint64_t CoGDoppler_NI_5_FC : 16;
        uint64_t CoGRange_NI_5_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_1_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_1_fc_data_t data;
};

union cansig_mk_non_inf_detection_1_fc__azimuth_ni_7_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_7_FC__S1 : 4;
        uint32_t Azimuth_NI_7_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_20_FC_ID       (0x0266)
#define CANMSG_MK_NON_INF_DETECTION_20_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_20_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_20_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_104_FC : 1;
        uint64_t NoInfrastructure_NI_104_FC : 1;
        uint64_t Beam_NI_104_FC : 2;
        uint64_t Azimuth_NI_104_FC : 12;
        uint64_t StdAzimuth_NI_104_FC : 8;
        uint64_t SNRdB_NI_104_FC : 8;
        uint64_t PowerDB_NI_104_FC : 8;
        uint64_t WDoppler10DB_NI_104_FC : 7;
        uint64_t Reserve1_1bit_NI_104_FC : 1;
        uint64_t WRange10DB_NI_104_FC : 8;
        uint64_t CoGDoppler_NI_104_FC : 16;
        uint64_t CoGRange_NI_104_FC : 16;
        uint64_t ValidXBeam_NI_103_FC : 1;
        uint64_t NoInfrastructure_NI_103_FC : 1;
        uint64_t Beam_NI_103_FC : 2;
        uint64_t Azimuth_NI_103_FC : 12;
        uint64_t StdAzimuth_NI_103_FC : 8;
        uint64_t SNRdB_NI_103_FC : 8;
        uint64_t PowerDB_NI_103_FC : 8;
        uint64_t WDoppler10DB_NI_103_FC : 7;
        uint64_t Reserve1_1bit_NI_103_FC : 1;
        uint64_t WRange10DB_NI_103_FC : 8;
        uint64_t CoGDoppler_NI_103_FC : 16;
        uint64_t CoGRange_NI_103_FC : 16;
        uint64_t ValidXBeam_NI_102_FC : 1;
        uint64_t NoInfrastructure_NI_102_FC : 1;
        uint64_t Beam_NI_102_FC : 2;
        uint64_t Azimuth_NI_102_FC__S1 : 4;
        uint64_t Azimuth_NI_102_FC__S0 : 8;
        uint64_t StdAzimuth_NI_102_FC : 8;
        uint64_t SNRdB_NI_102_FC : 8;
        uint64_t PowerDB_NI_102_FC : 8;
        uint64_t WDoppler10DB_NI_102_FC : 7;
        uint64_t Reserve1_1bit_NI_102_FC : 1;
        uint64_t WRange10DB_NI_102_FC : 8;
        uint64_t CoGDoppler_NI_102_FC : 16;
        uint64_t CoGRange_NI_102_FC : 16;
        uint64_t ValidXBeam_NI_101_FC : 1;
        uint64_t NoInfrastructure_NI_101_FC : 1;
        uint64_t Beam_NI_101_FC : 2;
        uint64_t Azimuth_NI_101_FC : 12;
        uint64_t StdAzimuth_NI_101_FC : 8;
        uint64_t SNRdB_NI_101_FC : 8;
        uint64_t PowerDB_NI_101_FC : 8;
        uint64_t WDoppler10DB_NI_101_FC : 7;
        uint64_t Reserve1_1bit_NI_101_FC : 1;
        uint64_t WRange10DB_NI_101_FC : 8;
        uint64_t CoGDoppler_NI_101_FC : 16;
        uint64_t CoGRange_NI_101_FC : 16;
        uint64_t ValidXBeam_NI_100_FC : 1;
        uint64_t NoInfrastructure_NI_100_FC : 1;
        uint64_t Beam_NI_100_FC : 2;
        uint64_t Azimuth_NI_100_FC : 12;
        uint64_t StdAzimuth_NI_100_FC : 8;
        uint64_t SNRdB_NI_100_FC : 8;
        uint64_t PowerDB_NI_100_FC : 8;
        uint64_t WDoppler10DB_NI_100_FC : 7;
        uint64_t Reserve1_1bit_NI_100_FC : 1;
        uint64_t WRange10DB_NI_100_FC : 8;
        uint64_t CoGDoppler_NI_100_FC : 16;
        uint64_t CoGRange_NI_100_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_20_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_20_fc_data_t data;
};

union cansig_mk_non_inf_detection_20_fc__azimuth_ni_102_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_102_FC__S1 : 4;
        uint32_t Azimuth_NI_102_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_21_FC_ID       (0x0267)
#define CANMSG_MK_NON_INF_DETECTION_21_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_21_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_21_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_109_FC : 1;
        uint64_t NoInfrastructure_NI_109_FC : 1;
        uint64_t Beam_NI_109_FC : 2;
        uint64_t Azimuth_NI_109_FC : 12;
        uint64_t StdAzimuth_NI_109_FC : 8;
        uint64_t SNRdB_NI_109_FC : 8;
        uint64_t PowerDB_NI_109_FC : 8;
        uint64_t WDoppler10DB_NI_109_FC : 7;
        uint64_t Reserve1_1bit_NI_109_FC : 1;
        uint64_t WRange10DB_NI_109_FC : 8;
        uint64_t CoGDoppler_NI_109_FC : 16;
        uint64_t CoGRange_NI_109_FC : 16;
        uint64_t ValidXBeam_NI_108_FC : 1;
        uint64_t NoInfrastructure_NI_108_FC : 1;
        uint64_t Beam_NI_108_FC : 2;
        uint64_t Azimuth_NI_108_FC : 12;
        uint64_t StdAzimuth_NI_108_FC : 8;
        uint64_t SNRdB_NI_108_FC : 8;
        uint64_t PowerDB_NI_108_FC : 8;
        uint64_t WDoppler10DB_NI_108_FC : 7;
        uint64_t Reserve1_1bit_NI_108_FC : 1;
        uint64_t WRange10DB_NI_108_FC : 8;
        uint64_t CoGDoppler_NI_108_FC : 16;
        uint64_t CoGRange_NI_108_FC : 16;
        uint64_t ValidXBeam_NI_107_FC : 1;
        uint64_t NoInfrastructure_NI_107_FC : 1;
        uint64_t Beam_NI_107_FC : 2;
        uint64_t Azimuth_NI_107_FC__S1 : 4;
        uint64_t Azimuth_NI_107_FC__S0 : 8;
        uint64_t StdAzimuth_NI_107_FC : 8;
        uint64_t SNRdB_NI_107_FC : 8;
        uint64_t PowerDB_NI_107_FC : 8;
        uint64_t WDoppler10DB_NI_107_FC : 7;
        uint64_t Reserve1_1bit_NI_107_FC : 1;
        uint64_t WRange10DB_NI_107_FC : 8;
        uint64_t CoGDoppler_NI_107_FC : 16;
        uint64_t CoGRange_NI_107_FC : 16;
        uint64_t ValidXBeam_NI_106_FC : 1;
        uint64_t NoInfrastructure_NI_106_FC : 1;
        uint64_t Beam_NI_106_FC : 2;
        uint64_t Azimuth_NI_106_FC : 12;
        uint64_t StdAzimuth_NI_106_FC : 8;
        uint64_t SNRdB_NI_106_FC : 8;
        uint64_t PowerDB_NI_106_FC : 8;
        uint64_t WDoppler10DB_NI_106_FC : 7;
        uint64_t Reserve1_1bit_NI_106_FC : 1;
        uint64_t WRange10DB_NI_106_FC : 8;
        uint64_t CoGDoppler_NI_106_FC : 16;
        uint64_t CoGRange_NI_106_FC : 16;
        uint64_t ValidXBeam_NI_105_FC : 1;
        uint64_t NoInfrastructure_NI_105_FC : 1;
        uint64_t Beam_NI_105_FC : 2;
        uint64_t Azimuth_NI_105_FC : 12;
        uint64_t StdAzimuth_NI_105_FC : 8;
        uint64_t SNRdB_NI_105_FC : 8;
        uint64_t PowerDB_NI_105_FC : 8;
        uint64_t WDoppler10DB_NI_105_FC : 7;
        uint64_t Reserve1_1bit_NI_105_FC : 1;
        uint64_t WRange10DB_NI_105_FC : 8;
        uint64_t CoGDoppler_NI_105_FC : 16;
        uint64_t CoGRange_NI_105_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_21_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_21_fc_data_t data;
};

union cansig_mk_non_inf_detection_21_fc__azimuth_ni_107_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_107_FC__S1 : 4;
        uint32_t Azimuth_NI_107_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_22_FC_ID       (0x0268)
#define CANMSG_MK_NON_INF_DETECTION_22_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_22_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_22_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_114_FC : 1;
        uint64_t NoInfrastructure_NI_114_FC : 1;
        uint64_t Beam_NI_114_FC : 2;
        uint64_t Azimuth_NI_114_FC : 12;
        uint64_t StdAzimuth_NI_114_FC : 8;
        uint64_t SNRdB_NI_114_FC : 8;
        uint64_t PowerDB_NI_114_FC : 8;
        uint64_t WDoppler10DB_NI_114_FC : 7;
        uint64_t Reserve1_1bit_NI_114_FC : 1;
        uint64_t WRange10DB_NI_114_FC : 8;
        uint64_t CoGDoppler_NI_114_FC : 16;
        uint64_t CoGRange_NI_114_FC : 16;
        uint64_t ValidXBeam_NI_113_FC : 1;
        uint64_t NoInfrastructure_NI_113_FC : 1;
        uint64_t Beam_NI_113_FC : 2;
        uint64_t Azimuth_NI_113_FC : 12;
        uint64_t StdAzimuth_NI_113_FC : 8;
        uint64_t SNRdB_NI_113_FC : 8;
        uint64_t PowerDB_NI_113_FC : 8;
        uint64_t WDoppler10DB_NI_113_FC : 7;
        uint64_t Reserve1_1bit_NI_113_FC : 1;
        uint64_t WRange10DB_NI_113_FC : 8;
        uint64_t CoGDoppler_NI_113_FC : 16;
        uint64_t CoGRange_NI_113_FC : 16;
        uint64_t ValidXBeam_NI_112_FC : 1;
        uint64_t NoInfrastructure_NI_112_FC : 1;
        uint64_t Beam_NI_112_FC : 2;
        uint64_t Azimuth_NI_112_FC__S1 : 4;
        uint64_t Azimuth_NI_112_FC__S0 : 8;
        uint64_t StdAzimuth_NI_112_FC : 8;
        uint64_t SNRdB_NI_112_FC : 8;
        uint64_t PowerDB_NI_112_FC : 8;
        uint64_t WDoppler10DB_NI_112_FC : 7;
        uint64_t Reserve1_1bit_NI_112_FC : 1;
        uint64_t WRange10DB_NI_112_FC : 8;
        uint64_t CoGDoppler_NI_112_FC : 16;
        uint64_t CoGRange_NI_112_FC : 16;
        uint64_t ValidXBeam_NI_111_FC : 1;
        uint64_t NoInfrastructure_NI_111_FC : 1;
        uint64_t Beam_NI_111_FC : 2;
        uint64_t Azimuth_NI_111_FC : 12;
        uint64_t StdAzimuth_NI_111_FC : 8;
        uint64_t SNRdB_NI_111_FC : 8;
        uint64_t PowerDB_NI_111_FC : 8;
        uint64_t WDoppler10DB_NI_111_FC : 7;
        uint64_t Reserve1_1bit_NI_111_FC : 1;
        uint64_t WRange10DB_NI_111_FC : 8;
        uint64_t CoGDoppler_NI_111_FC : 16;
        uint64_t CoGRange_NI_111_FC : 16;
        uint64_t ValidXBeam_NI_110_FC : 1;
        uint64_t NoInfrastructure_NI_110_FC : 1;
        uint64_t Beam_NI_110_FC : 2;
        uint64_t Azimuth_NI_110_FC : 12;
        uint64_t StdAzimuth_NI_110_FC : 8;
        uint64_t SNRdB_NI_110_FC : 8;
        uint64_t PowerDB_NI_110_FC : 8;
        uint64_t WDoppler10DB_NI_110_FC : 7;
        uint64_t Reserve1_1bit_NI_110_FC : 1;
        uint64_t WRange10DB_NI_110_FC : 8;
        uint64_t CoGDoppler_NI_110_FC : 16;
        uint64_t CoGRange_NI_110_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_22_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_22_fc_data_t data;
};

union cansig_mk_non_inf_detection_22_fc__azimuth_ni_112_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_112_FC__S1 : 4;
        uint32_t Azimuth_NI_112_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_23_FC_ID       (0x0269)
#define CANMSG_MK_NON_INF_DETECTION_23_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_23_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_23_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_119_FC : 1;
        uint64_t NoInfrastructure_NI_119_FC : 1;
        uint64_t Beam_NI_119_FC : 2;
        uint64_t Azimuth_NI_119_FC : 12;
        uint64_t StdAzimuth_NI_119_FC : 8;
        uint64_t SNRdB_NI_119_FC : 8;
        uint64_t PowerDB_NI_119_FC : 8;
        uint64_t WDoppler10DB_NI_119_FC : 7;
        uint64_t Reserve1_1bit_NI_119_FC : 1;
        uint64_t WRange10DB_NI_119_FC : 8;
        uint64_t CoGDoppler_NI_119_FC : 16;
        uint64_t CoGRange_NI_119_FC : 16;
        uint64_t ValidXBeam_NI_118_FC : 1;
        uint64_t NoInfrastructure_NI_118_FC : 1;
        uint64_t Beam_NI_118_FC : 2;
        uint64_t Azimuth_NI_118_FC : 12;
        uint64_t StdAzimuth_NI_118_FC : 8;
        uint64_t SNRdB_NI_118_FC : 8;
        uint64_t PowerDB_NI_118_FC : 8;
        uint64_t WDoppler10DB_NI_118_FC : 7;
        uint64_t Reserve1_1bit_NI_118_FC : 1;
        uint64_t WRange10DB_NI_118_FC : 8;
        uint64_t CoGDoppler_NI_118_FC : 16;
        uint64_t CoGRange_NI_118_FC : 16;
        uint64_t ValidXBeam_NI_117_FC : 1;
        uint64_t NoInfrastructure_NI_117_FC : 1;
        uint64_t Beam_NI_117_FC : 2;
        uint64_t Azimuth_NI_117_FC__S1 : 4;
        uint64_t Azimuth_NI_117_FC__S0 : 8;
        uint64_t StdAzimuth_NI_117_FC : 8;
        uint64_t SNRdB_NI_117_FC : 8;
        uint64_t PowerDB_NI_117_FC : 8;
        uint64_t WDoppler10DB_NI_117_FC : 7;
        uint64_t Reserve1_1bit_NI_117_FC : 1;
        uint64_t WRange10DB_NI_117_FC : 8;
        uint64_t CoGDoppler_NI_117_FC : 16;
        uint64_t CoGRange_NI_117_FC : 16;
        uint64_t ValidXBeam_NI_116_FC : 1;
        uint64_t NoInfrastructure_NI_116_FC : 1;
        uint64_t Beam_NI_116_FC : 2;
        uint64_t Azimuth_NI_116_FC : 12;
        uint64_t StdAzimuth_NI_116_FC : 8;
        uint64_t SNRdB_NI_116_FC : 8;
        uint64_t PowerDB_NI_116_FC : 8;
        uint64_t WDoppler10DB_NI_116_FC : 7;
        uint64_t Reserve1_1bit_NI_116_FC : 1;
        uint64_t WRange10DB_NI_116_FC : 8;
        uint64_t CoGDoppler_NI_116_FC : 16;
        uint64_t CoGRange_NI_116_FC : 16;
        uint64_t ValidXBeam_NI_115_FC : 1;
        uint64_t NoInfrastructure_NI_115_FC : 1;
        uint64_t Beam_NI_115_FC : 2;
        uint64_t Azimuth_NI_115_FC : 12;
        uint64_t StdAzimuth_NI_115_FC : 8;
        uint64_t SNRdB_NI_115_FC : 8;
        uint64_t PowerDB_NI_115_FC : 8;
        uint64_t WDoppler10DB_NI_115_FC : 7;
        uint64_t Reserve1_1bit_NI_115_FC : 1;
        uint64_t WRange10DB_NI_115_FC : 8;
        uint64_t CoGDoppler_NI_115_FC : 16;
        uint64_t CoGRange_NI_115_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_23_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_23_fc_data_t data;
};

union cansig_mk_non_inf_detection_23_fc__azimuth_ni_117_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_117_FC__S1 : 4;
        uint32_t Azimuth_NI_117_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_24_FC_ID       (0x026A)
#define CANMSG_MK_NON_INF_DETECTION_24_FC_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_24_FC_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_24_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_124_FC : 1;
        uint64_t NoInfrastructure_NI_124_FC : 1;
        uint64_t Beam_NI_124_FC : 2;
        uint64_t Azimuth_NI_124_FC : 12;
        uint64_t StdAzimuth_NI_124_FC : 8;
        uint64_t SNRdB_NI_124_FC : 8;
        uint64_t PowerDB_NI_124_FC : 8;
        uint64_t WDoppler10DB_NI_124_FC : 7;
        uint64_t Reserve1_1bit_NI_124_FC : 1;
        uint64_t WRange10DB_NI_124_FC : 8;
        uint64_t CoGDoppler_NI_124_FC : 16;
        uint64_t CoGRange_NI_124_FC : 16;
        uint64_t ValidXBeam_NI_123_FC : 1;
        uint64_t NoInfrastructure_NI_123_FC : 1;
        uint64_t Beam_NI_123_FC : 2;
        uint64_t Azimuth_NI_123_FC : 12;
        uint64_t StdAzimuth_NI_123_FC : 8;
        uint64_t SNRdB_NI_123_FC : 8;
        uint64_t PowerDB_NI_123_FC : 8;
        uint64_t WDoppler10DB_NI_123_FC : 7;
        uint64_t Reserve1_1bit_NI_123_FC : 1;
        uint64_t WRange10DB_NI_123_FC : 8;
        uint64_t CoGDoppler_NI_123_FC : 16;
        uint64_t CoGRange_NI_123_FC : 16;
        uint64_t ValidXBeam_NI_122_FC : 1;
        uint64_t NoInfrastructure_NI_122_FC : 1;
        uint64_t Beam_NI_122_FC : 2;
        uint64_t Azimuth_NI_122_FC__S1 : 4;
        uint64_t Azimuth_NI_122_FC__S0 : 8;
        uint64_t StdAzimuth_NI_122_FC : 8;
        uint64_t SNRdB_NI_122_FC : 8;
        uint64_t PowerDB_NI_122_FC : 8;
        uint64_t WDoppler10DB_NI_122_FC : 7;
        uint64_t Reserve1_1bit_NI_122_FC : 1;
        uint64_t WRange10DB_NI_122_FC : 8;
        uint64_t CoGDoppler_NI_122_FC : 16;
        uint64_t CoGRange_NI_122_FC : 16;
        uint64_t ValidXBeam_NI_121_FC : 1;
        uint64_t NoInfrastructure_NI_121_FC : 1;
        uint64_t Beam_NI_121_FC : 2;
        uint64_t Azimuth_NI_121_FC : 12;
        uint64_t StdAzimuth_NI_121_FC : 8;
        uint64_t SNRdB_NI_121_FC : 8;
        uint64_t PowerDB_NI_121_FC : 8;
        uint64_t WDoppler10DB_NI_121_FC : 7;
        uint64_t Reserve1_1bit_NI_121_FC : 1;
        uint64_t WRange10DB_NI_121_FC : 8;
        uint64_t CoGDoppler_NI_121_FC : 16;
        uint64_t CoGRange_NI_121_FC : 16;
        uint64_t ValidXBeam_NI_120_FC : 1;
        uint64_t NoInfrastructure_NI_120_FC : 1;
        uint64_t Beam_NI_120_FC : 2;
        uint64_t Azimuth_NI_120_FC : 12;
        uint64_t StdAzimuth_NI_120_FC : 8;
        uint64_t SNRdB_NI_120_FC : 8;
        uint64_t PowerDB_NI_120_FC : 8;
        uint64_t WDoppler10DB_NI_120_FC : 7;
        uint64_t Reserve1_1bit_NI_120_FC : 1;
        uint64_t WRange10DB_NI_120_FC : 8;
        uint64_t CoGDoppler_NI_120_FC : 16;
        uint64_t CoGRange_NI_120_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_24_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_24_fc_data_t data;
};

union cansig_mk_non_inf_detection_24_fc__azimuth_ni_122_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_122_FC__S1 : 4;
        uint32_t Azimuth_NI_122_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_2_FC_ID        (0x0254)
#define CANMSG_MK_NON_INF_DETECTION_2_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_2_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_2_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_14_FC : 1;
        uint64_t NoInfrastructure_NI_14_FC : 1;
        uint64_t Beam_NI_14_FC : 2;
        uint64_t Azimuth_NI_14_FC : 12;
        uint64_t StdAzimuth_NI_14_FC : 8;
        uint64_t SNRdB_NI_14_FC : 8;
        uint64_t PowerDB_NI_14_FC : 8;
        uint64_t WDoppler10DB_NI_14_FC : 7;
        uint64_t Reserve1_1bit_NI_14_FC : 1;
        uint64_t WRange10DB_NI_14_FC : 8;
        uint64_t CoGDoppler_NI_14_FC : 16;
        uint64_t CoGRange_NI_14_FC : 16;
        uint64_t ValidXBeam_NI_13_FC : 1;
        uint64_t NoInfrastructure_NI_13_FC : 1;
        uint64_t Beam_NI_13_FC : 2;
        uint64_t Azimuth_NI_13_FC : 12;
        uint64_t StdAzimuth_NI_13_FC : 8;
        uint64_t SNRdB_NI_13_FC : 8;
        uint64_t PowerDB_NI_13_FC : 8;
        uint64_t WDoppler10DB_NI_13_FC : 7;
        uint64_t Reserve1_1bit_NI_13_FC : 1;
        uint64_t WRange10DB_NI_13_FC : 8;
        uint64_t CoGDoppler_NI_13_FC : 16;
        uint64_t CoGRange_NI_13_FC : 16;
        uint64_t ValidXBeam_NI_12_FC : 1;
        uint64_t NoInfrastructure_NI_12_FC : 1;
        uint64_t Beam_NI_12_FC : 2;
        uint64_t Azimuth_NI_12_FC__S1 : 4;
        uint64_t Azimuth_NI_12_FC__S0 : 8;
        uint64_t StdAzimuth_NI_12_FC : 8;
        uint64_t SNRdB_NI_12_FC : 8;
        uint64_t PowerDB_NI_12_FC : 8;
        uint64_t WDoppler10DB_NI_12_FC : 7;
        uint64_t Reserve1_1bit_NI_12_FC : 1;
        uint64_t WRange10DB_NI_12_FC : 8;
        uint64_t CoGDoppler_NI_12_FC : 16;
        uint64_t CoGRange_NI_12_FC : 16;
        uint64_t ValidXBeam_NI_11_FC : 1;
        uint64_t NoInfrastructure_NI_11_FC : 1;
        uint64_t Beam_NI_11_FC : 2;
        uint64_t Azimuth_NI_11_FC : 12;
        uint64_t StdAzimuth_NI_11_FC : 8;
        uint64_t SNRdB_NI_11_FC : 8;
        uint64_t PowerDB_NI_11_FC : 8;
        uint64_t WDoppler10DB_NI_11_FC : 7;
        uint64_t Reserve1_1bit_NI_11_FC : 1;
        uint64_t WRange10DB_NI_11_FC : 8;
        uint64_t CoGDoppler_NI_11_FC : 16;
        uint64_t CoGRange_NI_11_FC : 16;
        uint64_t ValidXBeam_NI_10_FC : 1;
        uint64_t NoInfrastructure_NI_10_FC : 1;
        uint64_t Beam_NI_10_FC : 2;
        uint64_t Azimuth_NI_10_FC : 12;
        uint64_t StdAzimuth_NI_10_FC : 8;
        uint64_t SNRdB_NI_10_FC : 8;
        uint64_t PowerDB_NI_10_FC : 8;
        uint64_t WDoppler10DB_NI_10_FC : 7;
        uint64_t Reserve1_1bit_NI_10_FC : 1;
        uint64_t WRange10DB_NI_10_FC : 8;
        uint64_t CoGDoppler_NI_10_FC : 16;
        uint64_t CoGRange_NI_10_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_2_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_2_fc_data_t data;
};

union cansig_mk_non_inf_detection_2_fc__azimuth_ni_12_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_12_FC__S1 : 4;
        uint32_t Azimuth_NI_12_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_3_FC_ID        (0x0255)
#define CANMSG_MK_NON_INF_DETECTION_3_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_3_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_3_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_19_FC : 1;
        uint64_t NoInfrastructure_NI_19_FC : 1;
        uint64_t Beam_NI_19_FC : 2;
        uint64_t Azimuth_NI_19_FC : 12;
        uint64_t StdAzimuth_NI_19_FC : 8;
        uint64_t SNRdB_NI_19_FC : 8;
        uint64_t PowerDB_NI_19_FC : 8;
        uint64_t WDoppler10DB_NI_19_FC : 7;
        uint64_t Reserve1_1bit_NI_19_FC : 1;
        uint64_t WRange10DB_NI_19_FC : 8;
        uint64_t CoGDoppler_NI_19_FC : 16;
        uint64_t CoGRange_NI_19_FC : 16;
        uint64_t ValidXBeam_NI_18_FC : 1;
        uint64_t NoInfrastructure_NI_18_FC : 1;
        uint64_t Beam_NI_18_FC : 2;
        uint64_t Azimuth_NI_18_FC : 12;
        uint64_t StdAzimuth_NI_18_FC : 8;
        uint64_t SNRdB_NI_18_FC : 8;
        uint64_t PowerDB_NI_18_FC : 8;
        uint64_t WDoppler10DB_NI_18_FC : 7;
        uint64_t Reserve1_1bit_NI_18_FC : 1;
        uint64_t WRange10DB_NI_18_FC : 8;
        uint64_t CoGDoppler_NI_18_FC : 16;
        uint64_t CoGRange_NI_18_FC : 16;
        uint64_t ValidXBeam_NI_17_FC : 1;
        uint64_t NoInfrastructure_NI_17_FC : 1;
        uint64_t Beam_NI_17_FC : 2;
        uint64_t Azimuth_NI_17_FC__S1 : 4;
        uint64_t Azimuth_NI_17_FC__S0 : 8;
        uint64_t StdAzimuth_NI_17_FC : 8;
        uint64_t SNRdB_NI_17_FC : 8;
        uint64_t PowerDB_NI_17_FC : 8;
        uint64_t WDoppler10DB_NI_17_FC : 7;
        uint64_t Reserve1_1bit_NI_17_FC : 1;
        uint64_t WRange10DB_NI_17_FC : 8;
        uint64_t CoGDoppler_NI_17_FC : 16;
        uint64_t CoGRange_NI_17_FC : 16;
        uint64_t ValidXBeam_NI_16_FC : 1;
        uint64_t NoInfrastructure_NI_16_FC : 1;
        uint64_t Beam_NI_16_FC : 2;
        uint64_t Azimuth_NI_16_FC : 12;
        uint64_t StdAzimuth_NI_16_FC : 8;
        uint64_t SNRdB_NI_16_FC : 8;
        uint64_t PowerDB_NI_16_FC : 8;
        uint64_t WDoppler10DB_NI_16_FC : 7;
        uint64_t Reserve1_1bit_NI_16_FC : 1;
        uint64_t WRange10DB_NI_16_FC : 8;
        uint64_t CoGDoppler_NI_16_FC : 16;
        uint64_t CoGRange_NI_16_FC : 16;
        uint64_t ValidXBeam_NI_15_FC : 1;
        uint64_t NoInfrastructure_NI_15_FC : 1;
        uint64_t Beam_NI_15_FC : 2;
        uint64_t Azimuth_NI_15_FC : 12;
        uint64_t StdAzimuth_NI_15_FC : 8;
        uint64_t SNRdB_NI_15_FC : 8;
        uint64_t PowerDB_NI_15_FC : 8;
        uint64_t WDoppler10DB_NI_15_FC : 7;
        uint64_t Reserve1_1bit_NI_15_FC : 1;
        uint64_t WRange10DB_NI_15_FC : 8;
        uint64_t CoGDoppler_NI_15_FC : 16;
        uint64_t CoGRange_NI_15_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_3_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_3_fc_data_t data;
};

union cansig_mk_non_inf_detection_3_fc__azimuth_ni_17_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_17_FC__S1 : 4;
        uint32_t Azimuth_NI_17_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_4_FC_ID        (0x0256)
#define CANMSG_MK_NON_INF_DETECTION_4_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_4_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_4_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_24_FC : 1;
        uint64_t NoInfrastructure_NI_24_FC : 1;
        uint64_t Beam_NI_24_FC : 2;
        uint64_t Azimuth_NI_24_FC : 12;
        uint64_t StdAzimuth_NI_24_FC : 8;
        uint64_t SNRdB_NI_24_FC : 8;
        uint64_t PowerDB_NI_24_FC : 8;
        uint64_t WDoppler10DB_NI_24_FC : 7;
        uint64_t Reserve1_1bit_NI_24_FC : 1;
        uint64_t WRange10DB_NI_24_FC : 8;
        uint64_t CoGDoppler_NI_24_FC : 16;
        uint64_t CoGRange_NI_24_FC : 16;
        uint64_t ValidXBeam_NI_23_FC : 1;
        uint64_t NoInfrastructure_NI_23_FC : 1;
        uint64_t Beam_NI_23_FC : 2;
        uint64_t Azimuth_NI_23_FC : 12;
        uint64_t StdAzimuth_NI_23_FC : 8;
        uint64_t SNRdB_NI_23_FC : 8;
        uint64_t PowerDB_NI_23_FC : 8;
        uint64_t WDoppler10DB_NI_23_FC : 7;
        uint64_t Reserve1_1bit_NI_23_FC : 1;
        uint64_t WRange10DB_NI_23_FC : 8;
        uint64_t CoGDoppler_NI_23_FC : 16;
        uint64_t CoGRange_NI_23_FC : 16;
        uint64_t ValidXBeam_NI_22_FC : 1;
        uint64_t NoInfrastructure_NI_22_FC : 1;
        uint64_t Beam_NI_22_FC : 2;
        uint64_t Azimuth_NI_22_FC__S1 : 4;
        uint64_t Azimuth_NI_22_FC__S0 : 8;
        uint64_t StdAzimuth_NI_22_FC : 8;
        uint64_t SNRdB_NI_22_FC : 8;
        uint64_t PowerDB_NI_22_FC : 8;
        uint64_t WDoppler10DB_NI_22_FC : 7;
        uint64_t Reserve1_1bit_NI_22_FC : 1;
        uint64_t WRange10DB_NI_22_FC : 8;
        uint64_t CoGDoppler_NI_22_FC : 16;
        uint64_t CoGRange_NI_22_FC : 16;
        uint64_t ValidXBeam_NI_21_FC : 1;
        uint64_t NoInfrastructure_NI_21_FC : 1;
        uint64_t Beam_NI_21_FC : 2;
        uint64_t Azimuth_NI_21_FC : 12;
        uint64_t StdAzimuth_NI_21_FC : 8;
        uint64_t SNRdB_NI_21_FC : 8;
        uint64_t PowerDB_NI_21_FC : 8;
        uint64_t WDoppler10DB_NI_21_FC : 7;
        uint64_t Reserve1_1bit_NI_21_FC : 1;
        uint64_t WRange10DB_NI_21_FC : 8;
        uint64_t CoGDoppler_NI_21_FC : 16;
        uint64_t CoGRange_NI_21_FC : 16;
        uint64_t ValidXBeam_NI_20_FC : 1;
        uint64_t NoInfrastructure_NI_20_FC : 1;
        uint64_t Beam_NI_20_FC : 2;
        uint64_t Azimuth_NI_20_FC : 12;
        uint64_t StdAzimuth_NI_20_FC : 8;
        uint64_t SNRdB_NI_20_FC : 8;
        uint64_t PowerDB_NI_20_FC : 8;
        uint64_t WDoppler10DB_NI_20_FC : 7;
        uint64_t Reserve1_1bit_NI_20_FC : 1;
        uint64_t WRange10DB_NI_20_FC : 8;
        uint64_t CoGDoppler_NI_20_FC : 16;
        uint64_t CoGRange_NI_20_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_4_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_4_fc_data_t data;
};

union cansig_mk_non_inf_detection_4_fc__azimuth_ni_22_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_22_FC__S1 : 4;
        uint32_t Azimuth_NI_22_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_5_FC_ID        (0x0257)
#define CANMSG_MK_NON_INF_DETECTION_5_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_5_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_5_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_29_FC : 1;
        uint64_t NoInfrastructure_NI_29_FC : 1;
        uint64_t Beam_NI_29_FC : 2;
        uint64_t Azimuth_NI_29_FC : 12;
        uint64_t StdAzimuth_NI_29_FC : 8;
        uint64_t SNRdB_NI_29_FC : 8;
        uint64_t PowerDB_NI_29_FC : 8;
        uint64_t WDoppler10DB_NI_29_FC : 7;
        uint64_t Reserve1_1bit_NI_29_FC : 1;
        uint64_t WRange10DB_NI_29_FC : 8;
        uint64_t CoGDoppler_NI_29_FC : 16;
        uint64_t CoGRange_NI_29_FC : 16;
        uint64_t ValidXBeam_NI_28_FC : 1;
        uint64_t NoInfrastructure_NI_28_FC : 1;
        uint64_t Beam_NI_28_FC : 2;
        uint64_t Azimuth_NI_28_FC : 12;
        uint64_t StdAzimuth_NI_28_FC : 8;
        uint64_t SNRdB_NI_28_FC : 8;
        uint64_t PowerDB_NI_28_FC : 8;
        uint64_t WDoppler10DB_NI_28_FC : 7;
        uint64_t Reserve1_1bit_NI_28_FC : 1;
        uint64_t WRange10DB_NI_28_FC : 8;
        uint64_t CoGDoppler_NI_28_FC : 16;
        uint64_t CoGRange_NI_28_FC : 16;
        uint64_t ValidXBeam_NI_27_FC : 1;
        uint64_t NoInfrastructure_NI_27_FC : 1;
        uint64_t Beam_NI_27_FC : 2;
        uint64_t Azimuth_NI_27_FC__S1 : 4;
        uint64_t Azimuth_NI_27_FC__S0 : 8;
        uint64_t StdAzimuth_NI_27_FC : 8;
        uint64_t SNRdB_NI_27_FC : 8;
        uint64_t PowerDB_NI_27_FC : 8;
        uint64_t WDoppler10DB_NI_27_FC : 7;
        uint64_t Reserve1_1bit_NI_27_FC : 1;
        uint64_t WRange10DB_NI_27_FC : 8;
        uint64_t CoGDoppler_NI_27_FC : 16;
        uint64_t CoGRange_NI_27_FC : 16;
        uint64_t ValidXBeam_NI_26_FC : 1;
        uint64_t NoInfrastructure_NI_26_FC : 1;
        uint64_t Beam_NI_26_FC : 2;
        uint64_t Azimuth_NI_26_FC : 12;
        uint64_t StdAzimuth_NI_26_FC : 8;
        uint64_t SNRdB_NI_26_FC : 8;
        uint64_t PowerDB_NI_26_FC : 8;
        uint64_t WDoppler10DB_NI_26_FC : 7;
        uint64_t Reserve1_1bit_NI_26_FC : 1;
        uint64_t WRange10DB_NI_26_FC : 8;
        uint64_t CoGDoppler_NI_26_FC : 16;
        uint64_t CoGRange_NI_26_FC : 16;
        uint64_t ValidXBeam_NI_25_FC : 1;
        uint64_t NoInfrastructure_NI_25_FC : 1;
        uint64_t Beam_NI_25_FC : 2;
        uint64_t Azimuth_NI_25_FC : 12;
        uint64_t StdAzimuth_NI_25_FC : 8;
        uint64_t SNRdB_NI_25_FC : 8;
        uint64_t PowerDB_NI_25_FC : 8;
        uint64_t WDoppler10DB_NI_25_FC : 7;
        uint64_t Reserve1_1bit_NI_25_FC : 1;
        uint64_t WRange10DB_NI_25_FC : 8;
        uint64_t CoGDoppler_NI_25_FC : 16;
        uint64_t CoGRange_NI_25_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_5_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_5_fc_data_t data;
};

union cansig_mk_non_inf_detection_5_fc__azimuth_ni_27_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_27_FC__S1 : 4;
        uint32_t Azimuth_NI_27_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_6_FC_ID        (0x0258)
#define CANMSG_MK_NON_INF_DETECTION_6_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_6_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_6_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_34_FC : 1;
        uint64_t NoInfrastructure_NI_34_FC : 1;
        uint64_t Beam_NI_34_FC : 2;
        uint64_t Azimuth_NI_34_FC : 12;
        uint64_t StdAzimuth_NI_34_FC : 8;
        uint64_t SNRdB_NI_34_FC : 8;
        uint64_t PowerDB_NI_34_FC : 8;
        uint64_t WDoppler10DB_NI_34_FC : 7;
        uint64_t Reserve1_1bit_NI_34_FC : 1;
        uint64_t WRange10DB_NI_34_FC : 8;
        uint64_t CoGDoppler_NI_34_FC : 16;
        uint64_t CoGRange_NI_34_FC : 16;
        uint64_t ValidXBeam_NI_33_FC : 1;
        uint64_t NoInfrastructure_NI_33_FC : 1;
        uint64_t Beam_NI_33_FC : 2;
        uint64_t Azimuth_NI_33_FC : 12;
        uint64_t StdAzimuth_NI_33_FC : 8;
        uint64_t SNRdB_NI_33_FC : 8;
        uint64_t PowerDB_NI_33_FC : 8;
        uint64_t WDoppler10DB_NI_33_FC : 7;
        uint64_t Reserve1_1bit_NI_33_FC : 1;
        uint64_t WRange10DB_NI_33_FC : 8;
        uint64_t CoGDoppler_NI_33_FC : 16;
        uint64_t CoGRange_NI_33_FC : 16;
        uint64_t ValidXBeam_NI_32_FC : 1;
        uint64_t NoInfrastructure_NI_32_FC : 1;
        uint64_t Beam_NI_32_FC : 2;
        uint64_t Azimuth_NI_32_FC__S1 : 4;
        uint64_t Azimuth_NI_32_FC__S0 : 8;
        uint64_t StdAzimuth_NI_32_FC : 8;
        uint64_t SNRdB_NI_32_FC : 8;
        uint64_t PowerDB_NI_32_FC : 8;
        uint64_t WDoppler10DB_NI_32_FC : 7;
        uint64_t Reserve1_1bit_NI_32_FC : 1;
        uint64_t WRange10DB_NI_32_FC : 8;
        uint64_t CoGDoppler_NI_32_FC : 16;
        uint64_t CoGRange_NI_32_FC : 16;
        uint64_t ValidXBeam_NI_31_FC : 1;
        uint64_t NoInfrastructure_NI_31_FC : 1;
        uint64_t Beam_NI_31_FC : 2;
        uint64_t Azimuth_NI_31_FC : 12;
        uint64_t StdAzimuth_NI_31_FC : 8;
        uint64_t SNRdB_NI_31_FC : 8;
        uint64_t PowerDB_NI_31_FC : 8;
        uint64_t WDoppler10DB_NI_31_FC : 7;
        uint64_t Reserve1_1bit_NI_31_FC : 1;
        uint64_t WRange10DB_NI_31_FC : 8;
        uint64_t CoGDoppler_NI_31_FC : 16;
        uint64_t CoGRange_NI_31_FC : 16;
        uint64_t ValidXBeam_NI_30_FC : 1;
        uint64_t NoInfrastructure_NI_30_FC : 1;
        uint64_t Beam_NI_30_FC : 2;
        uint64_t Azimuth_NI_30_FC : 12;
        uint64_t StdAzimuth_NI_30_FC : 8;
        uint64_t SNRdB_NI_30_FC : 8;
        uint64_t PowerDB_NI_30_FC : 8;
        uint64_t WDoppler10DB_NI_30_FC : 7;
        uint64_t Reserve1_1bit_NI_30_FC : 1;
        uint64_t WRange10DB_NI_30_FC : 8;
        uint64_t CoGDoppler_NI_30_FC : 16;
        uint64_t CoGRange_NI_30_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_6_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_6_fc_data_t data;
};

union cansig_mk_non_inf_detection_6_fc__azimuth_ni_32_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_32_FC__S1 : 4;
        uint32_t Azimuth_NI_32_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_7_FC_ID        (0x0259)
#define CANMSG_MK_NON_INF_DETECTION_7_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_7_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_7_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_39_FC : 1;
        uint64_t NoInfrastructure_NI_39_FC : 1;
        uint64_t Beam_NI_39_FC : 2;
        uint64_t Azimuth_NI_39_FC : 12;
        uint64_t StdAzimuth_NI_39_FC : 8;
        uint64_t SNRdB_NI_39_FC : 8;
        uint64_t PowerDB_NI_39_FC : 8;
        uint64_t WDoppler10DB_NI_39_FC : 7;
        uint64_t Reserve1_1bit_NI_39_FC : 1;
        uint64_t WRange10DB_NI_39_FC : 8;
        uint64_t CoGDoppler_NI_39_FC : 16;
        uint64_t CoGRange_NI_39_FC : 16;
        uint64_t ValidXBeam_NI_38_FC : 1;
        uint64_t NoInfrastructure_NI_38_FC : 1;
        uint64_t Beam_NI_38_FC : 2;
        uint64_t Azimuth_NI_38_FC : 12;
        uint64_t StdAzimuth_NI_38_FC : 8;
        uint64_t SNRdB_NI_38_FC : 8;
        uint64_t PowerDB_NI_38_FC : 8;
        uint64_t WDoppler10DB_NI_38_FC : 7;
        uint64_t Reserve1_1bit_NI_38_FC : 1;
        uint64_t WRange10DB_NI_38_FC : 8;
        uint64_t CoGDoppler_NI_38_FC : 16;
        uint64_t CoGRange_NI_38_FC : 16;
        uint64_t ValidXBeam_NI_37_FC : 1;
        uint64_t NoInfrastructure_NI_37_FC : 1;
        uint64_t Beam_NI_37_FC : 2;
        uint64_t Azimuth_NI_37_FC__S1 : 4;
        uint64_t Azimuth_NI_37_FC__S0 : 8;
        uint64_t StdAzimuth_NI_37_FC : 8;
        uint64_t SNRdB_NI_37_FC : 8;
        uint64_t PowerDB_NI_37_FC : 8;
        uint64_t WDoppler10DB_NI_37_FC : 7;
        uint64_t Reserve1_1bit_NI_37_FC : 1;
        uint64_t WRange10DB_NI_37_FC : 8;
        uint64_t CoGDoppler_NI_37_FC : 16;
        uint64_t CoGRange_NI_37_FC : 16;
        uint64_t ValidXBeam_NI_36_FC : 1;
        uint64_t NoInfrastructure_NI_36_FC : 1;
        uint64_t Beam_NI_36_FC : 2;
        uint64_t Azimuth_NI_36_FC : 12;
        uint64_t StdAzimuth_NI_36_FC : 8;
        uint64_t SNRdB_NI_36_FC : 8;
        uint64_t PowerDB_NI_36_FC : 8;
        uint64_t WDoppler10DB_NI_36_FC : 7;
        uint64_t Reserve1_1bit_NI_36_FC : 1;
        uint64_t WRange10DB_NI_36_FC : 8;
        uint64_t CoGDoppler_NI_36_FC : 16;
        uint64_t CoGRange_NI_36_FC : 16;
        uint64_t ValidXBeam_NI_35_FC : 1;
        uint64_t NoInfrastructure_NI_35_FC : 1;
        uint64_t Beam_NI_35_FC : 2;
        uint64_t Azimuth_NI_35_FC : 12;
        uint64_t StdAzimuth_NI_35_FC : 8;
        uint64_t SNRdB_NI_35_FC : 8;
        uint64_t PowerDB_NI_35_FC : 8;
        uint64_t WDoppler10DB_NI_35_FC : 7;
        uint64_t Reserve1_1bit_NI_35_FC : 1;
        uint64_t WRange10DB_NI_35_FC : 8;
        uint64_t CoGDoppler_NI_35_FC : 16;
        uint64_t CoGRange_NI_35_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_7_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_7_fc_data_t data;
};

union cansig_mk_non_inf_detection_7_fc__azimuth_ni_37_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_37_FC__S1 : 4;
        uint32_t Azimuth_NI_37_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_8_FC_ID        (0x025A)
#define CANMSG_MK_NON_INF_DETECTION_8_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_8_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_8_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_44_FC : 1;
        uint64_t NoInfrastructure_NI_44_FC : 1;
        uint64_t Beam_NI_44_FC : 2;
        uint64_t Azimuth_NI_44_FC : 12;
        uint64_t StdAzimuth_NI_44_FC : 8;
        uint64_t SNRdB_NI_44_FC : 8;
        uint64_t PowerDB_NI_44_FC : 8;
        uint64_t WDoppler10DB_NI_44_FC : 7;
        uint64_t Reserve1_1bit_NI_44_FC : 1;
        uint64_t WRange10DB_NI_44_FC : 8;
        uint64_t CoGDoppler_NI_44_FC : 16;
        uint64_t CoGRange_NI_44_FC : 16;
        uint64_t ValidXBeam_NI_43_FC : 1;
        uint64_t NoInfrastructure_NI_43_FC : 1;
        uint64_t Beam_NI_43_FC : 2;
        uint64_t Azimuth_NI_43_FC : 12;
        uint64_t StdAzimuth_NI_43_FC : 8;
        uint64_t SNRdB_NI_43_FC : 8;
        uint64_t PowerDB_NI_43_FC : 8;
        uint64_t WDoppler10DB_NI_43_FC : 7;
        uint64_t Reserve1_1bit_NI_43_FC : 1;
        uint64_t WRange10DB_NI_43_FC : 8;
        uint64_t CoGDoppler_NI_43_FC : 16;
        uint64_t CoGRange_NI_43_FC : 16;
        uint64_t ValidXBeam_NI_42_FC : 1;
        uint64_t NoInfrastructure_NI_42_FC : 1;
        uint64_t Beam_NI_42_FC : 2;
        uint64_t Azimuth_NI_42_FC__S1 : 4;
        uint64_t Azimuth_NI_42_FC__S0 : 8;
        uint64_t StdAzimuth_NI_42_FC : 8;
        uint64_t SNRdB_NI_42_FC : 8;
        uint64_t PowerDB_NI_42_FC : 8;
        uint64_t WDoppler10DB_NI_42_FC : 7;
        uint64_t Reserve1_1bit_NI_42_FC : 1;
        uint64_t WRange10DB_NI_42_FC : 8;
        uint64_t CoGDoppler_NI_42_FC : 16;
        uint64_t CoGRange_NI_42_FC : 16;
        uint64_t ValidXBeam_NI_41_FC : 1;
        uint64_t NoInfrastructure_NI_41_FC : 1;
        uint64_t Beam_NI_41_FC : 2;
        uint64_t Azimuth_NI_41_FC : 12;
        uint64_t StdAzimuth_NI_41_FC : 8;
        uint64_t SNRdB_NI_41_FC : 8;
        uint64_t PowerDB_NI_41_FC : 8;
        uint64_t WDoppler10DB_NI_41_FC : 7;
        uint64_t Reserve1_1bit_NI_41_FC : 1;
        uint64_t WRange10DB_NI_41_FC : 8;
        uint64_t CoGDoppler_NI_41_FC : 16;
        uint64_t CoGRange_NI_41_FC : 16;
        uint64_t ValidXBeam_NI_40_FC : 1;
        uint64_t NoInfrastructure_NI_40_FC : 1;
        uint64_t Beam_NI_40_FC : 2;
        uint64_t Azimuth_NI_40_FC : 12;
        uint64_t StdAzimuth_NI_40_FC : 8;
        uint64_t SNRdB_NI_40_FC : 8;
        uint64_t PowerDB_NI_40_FC : 8;
        uint64_t WDoppler10DB_NI_40_FC : 7;
        uint64_t Reserve1_1bit_NI_40_FC : 1;
        uint64_t WRange10DB_NI_40_FC : 8;
        uint64_t CoGDoppler_NI_40_FC : 16;
        uint64_t CoGRange_NI_40_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_8_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_8_fc_data_t data;
};

union cansig_mk_non_inf_detection_8_fc__azimuth_ni_42_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_42_FC__S1 : 4;
        uint32_t Azimuth_NI_42_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_9_FC_ID        (0x025B)
#define CANMSG_MK_NON_INF_DETECTION_9_FC_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_9_FC_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_9_fc_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_49_FC : 1;
        uint64_t NoInfrastructure_NI_49_FC : 1;
        uint64_t Beam_NI_49_FC : 2;
        uint64_t Azimuth_NI_49_FC : 12;
        uint64_t StdAzimuth_NI_49_FC : 8;
        uint64_t SNRdB_NI_49_FC : 8;
        uint64_t PowerDB_NI_49_FC : 8;
        uint64_t WDoppler10DB_NI_49_FC : 7;
        uint64_t Reserve1_1bit_NI_49_FC : 1;
        uint64_t WRange10DB_NI_49_FC : 8;
        uint64_t CoGDoppler_NI_49_FC : 16;
        uint64_t CoGRange_NI_49_FC : 16;
        uint64_t ValidXBeam_NI_48_FC : 1;
        uint64_t NoInfrastructure_NI_48_FC : 1;
        uint64_t Beam_NI_48_FC : 2;
        uint64_t Azimuth_NI_48_FC : 12;
        uint64_t StdAzimuth_NI_48_FC : 8;
        uint64_t SNRdB_NI_48_FC : 8;
        uint64_t PowerDB_NI_48_FC : 8;
        uint64_t WDoppler10DB_NI_48_FC : 7;
        uint64_t Reserve1_1bit_NI_48_FC : 1;
        uint64_t WRange10DB_NI_48_FC : 8;
        uint64_t CoGDoppler_NI_48_FC : 16;
        uint64_t CoGRange_NI_48_FC : 16;
        uint64_t ValidXBeam_NI_47_FC : 1;
        uint64_t NoInfrastructure_NI_47_FC : 1;
        uint64_t Beam_NI_47_FC : 2;
        uint64_t Azimuth_NI_47_FC__S1 : 4;
        uint64_t Azimuth_NI_47_FC__S0 : 8;
        uint64_t StdAzimuth_NI_47_FC : 8;
        uint64_t SNRdB_NI_47_FC : 8;
        uint64_t PowerDB_NI_47_FC : 8;
        uint64_t WDoppler10DB_NI_47_FC : 7;
        uint64_t Reserve1_1bit_NI_47_FC : 1;
        uint64_t WRange10DB_NI_47_FC : 8;
        uint64_t CoGDoppler_NI_47_FC : 16;
        uint64_t CoGRange_NI_47_FC : 16;
        uint64_t ValidXBeam_NI_46_FC : 1;
        uint64_t NoInfrastructure_NI_46_FC : 1;
        uint64_t Beam_NI_46_FC : 2;
        uint64_t Azimuth_NI_46_FC : 12;
        uint64_t StdAzimuth_NI_46_FC : 8;
        uint64_t SNRdB_NI_46_FC : 8;
        uint64_t PowerDB_NI_46_FC : 8;
        uint64_t WDoppler10DB_NI_46_FC : 7;
        uint64_t Reserve1_1bit_NI_46_FC : 1;
        uint64_t WRange10DB_NI_46_FC : 8;
        uint64_t CoGDoppler_NI_46_FC : 16;
        uint64_t CoGRange_NI_46_FC : 16;
        uint64_t ValidXBeam_NI_45_FC : 1;
        uint64_t NoInfrastructure_NI_45_FC : 1;
        uint64_t Beam_NI_45_FC : 2;
        uint64_t Azimuth_NI_45_FC : 12;
        uint64_t StdAzimuth_NI_45_FC : 8;
        uint64_t SNRdB_NI_45_FC : 8;
        uint64_t PowerDB_NI_45_FC : 8;
        uint64_t WDoppler10DB_NI_45_FC : 7;
        uint64_t Reserve1_1bit_NI_45_FC : 1;
        uint64_t WRange10DB_NI_45_FC : 8;
        uint64_t CoGDoppler_NI_45_FC : 16;
        uint64_t CoGRange_NI_45_FC : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_9_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_9_fc_data_t data;
};

union cansig_mk_non_inf_detection_9_fc__azimuth_ni_47_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_47_FC__S1 : 4;
        uint32_t Azimuth_NI_47_FC__S0 : 8;
    } fields;
};

#define CANMSG_MK_TARGET_DETECTION_HEADER_FC_ID    (0x0251)
#define CANMSG_MK_TARGET_DETECTION_HEADER_FC_DLC   (48)
#define CANMSG_MK_TARGET_DETECTION_HEADER_FC_MIN_DLC (48)
union canmsg_mk_target_detection_header_fc_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t InterferenceExceeded_FC : 8;
        uint64_t PAD2 : 8;
        uint64_t numFreespaceDetections_FC : 2;
        uint64_t DetectionListVersion_FC : 6;
        uint64_t numInfrastructureDetected_FC : 8;
        uint64_t numNonInfraDetected_FC : 8;
        uint64_t numCmplxValPerDetectionBeam3_FC : 8;
        uint64_t numCmplxValPerDetectionBeam2_FC : 8;
        uint64_t numCmplxValPerDetectionBeam1_FC : 8;
        uint64_t UnambiguousVelMeas3_FC : 8;
        uint64_t UnambiguousVelMeas2_FC : 8;
        uint64_t UnambiguousVelMeas1_FC : 8;
        uint64_t FC1MHz3_FC : 16;
        uint64_t FC1MHz2_FC : 16;
        uint64_t FC1MHz1_FC : 16;
        uint64_t HostYawEst_FC : 16;
        uint64_t HostAccelLatEst_FC : 16;
        uint64_t HostAccelLongEst_FC : 16;
        uint64_t HostVelEst_FC : 16;
        uint64_t BW100KHz3_FC : 16;
        uint64_t BW100KHz2_FC : 16;
        uint64_t BW100KHz1_FC : 16;
        uint64_t TimeStamp_FC : 32;
        uint64_t CycleNumber_FC : 32;
    } signals;
};

struct canmsg_mk_target_detection_header_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_target_detection_header_fc_data_t data;
};

//=====================================================================================//


#include "parser_typedef.h"
#pragma once
//=====================================================================================//
#define CANMSG_MK_NON_INF_DETECTION_0_FL_ID        (0x0202)
#define CANMSG_MK_NON_INF_DETECTION_0_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_0_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_0_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_4_FL : 1;
        uint64_t NoInfrastructure_NI_4_FL : 1;
        uint64_t Beam_NI_4_FL : 2;
        uint64_t Azimuth_NI_4_FL : 12;
        uint64_t StdAzimuth_NI_4_FL : 8;
        uint64_t SNRdB_NI_4_FL : 8;
        uint64_t PowerDB_NI_4_FL : 8;
        uint64_t WDoppler10DB_NI_4_FL : 7;
        uint64_t Reserve1_1bit_NI_4_FL : 1;
        uint64_t WRange10DB_NI_4_FL : 8;
        uint64_t CoGDoppler_NI_4_FL : 16;
        uint64_t CoGRange_NI_4_FL : 16;
        uint64_t ValidXBeam_NI_3_FL : 1;
        uint64_t NoInfrastructure_NI_3_FL : 1;
        uint64_t Beam_NI_3_FL : 2;
        uint64_t Azimuth_NI_3_FL : 12;
        uint64_t StdAzimuth_NI_3_FL : 8;
        uint64_t SNRdB_NI_3_FL : 8;
        uint64_t PowerDB_NI_3_FL : 8;
        uint64_t WDoppler10DB_NI_3_FL : 7;
        uint64_t Reserve1_1bit_NI_3_FL : 1;
        uint64_t WRange10DB_NI_3_FL : 8;
        uint64_t CoGDoppler_NI_3_FL : 16;
        uint64_t CoGRange_NI_3_FL : 16;
        uint64_t ValidXBeam_NI_2_FL : 1;
        uint64_t NoInfrastructure_NI_2_FL : 1;
        uint64_t Beam_NI_2_FL : 2;
        uint64_t Azimuth_NI_2_FL__S1 : 4;
        uint64_t Azimuth_NI_2_FL__S0 : 8;
        uint64_t StdAzimuth_NI_2_FL : 8;
        uint64_t SNRdB_NI_2_FL : 8;
        uint64_t PowerDB_NI_2_FL : 8;
        uint64_t WDoppler10DB_NI_2_FL : 7;
        uint64_t Reserve1_1bit_NI_2_FL : 1;
        uint64_t WRange10DB_NI_2_FL : 8;
        uint64_t CoGDoppler_NI_2_FL : 16;
        uint64_t CoGRange_NI_2_FL : 16;
        uint64_t ValidXBeam_NI_1_FL : 1;
        uint64_t NoInfrastructure_NI_1_FL : 1;
        uint64_t Beam_NI_1_FL : 2;
        uint64_t Azimuth_NI_1_FL : 12;
        uint64_t StdAzimuth_NI_1_FL : 8;
        uint64_t SNRdB_NI_1_FL : 8;
        uint64_t PowerDB_NI_1_FL : 8;
        uint64_t WDoppler10DB_NI_1_FL : 7;
        uint64_t Reserve1_1bit_NI_1_FL : 1;
        uint64_t WRange10DB_NI_1_FL : 8;
        uint64_t CoGDoppler_NI_1_FL : 16;
        uint64_t CoGRange_NI_1_FL : 16;
        uint64_t ValidXBeam_NI_0_FL : 1;
        uint64_t NoInfrastructure_NI_0_FL : 1;
        uint64_t Beam_NI_0_FL : 2;
        uint64_t Azimuth_NI_0_FL : 12;
        uint64_t StdAzimuth_NI_0_FL : 8;
        uint64_t SNRdB_NI_0_FL : 8;
        uint64_t PowerDB_NI_0_FL : 8;
        uint64_t WDoppler10DB_NI_0_FL : 7;
        uint64_t Reserve1_1bit_NI_0_FL : 1;
        uint64_t WRange10DB_NI_0_FL : 8;
        uint64_t CoGDoppler_NI_0_FL : 16;
        uint64_t CoGRange_NI_0_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_0_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_0_fl_data_t data;
};

union cansig_mk_non_inf_detection_0_fl__azimuth_ni_2_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_2_FL__S1 : 4;
        uint32_t Azimuth_NI_2_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_10_FL_ID       (0x020C)
#define CANMSG_MK_NON_INF_DETECTION_10_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_10_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_10_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_54_FL : 1;
        uint64_t NoInfrastructure_NI_54_FL : 1;
        uint64_t Beam_NI_54_FL : 2;
        uint64_t Azimuth_NI_54_FL : 12;
        uint64_t StdAzimuth_NI_54_FL : 8;
        uint64_t SNRdB_NI_54_FL : 8;
        uint64_t PowerDB_NI_54_FL : 8;
        uint64_t WDoppler10DB_NI_54_FL : 7;
        uint64_t Reserve1_1bit_NI_54_FL : 1;
        uint64_t WRange10DB_NI_54_FL : 8;
        uint64_t CoGDoppler_NI_54_FL : 16;
        uint64_t CoGRange_NI_54_FL : 16;
        uint64_t ValidXBeam_NI_53_FL : 1;
        uint64_t NoInfrastructure_NI_53_FL : 1;
        uint64_t Beam_NI_53_FL : 2;
        uint64_t Azimuth_NI_53_FL : 12;
        uint64_t StdAzimuth_NI_53_FL : 8;
        uint64_t SNRdB_NI_53_FL : 8;
        uint64_t PowerDB_NI_53_FL : 8;
        uint64_t WDoppler10DB_NI_53_FL : 7;
        uint64_t Reserve1_1bit_NI_53_FL : 1;
        uint64_t WRange10DB_NI_53_FL : 8;
        uint64_t CoGDoppler_NI_53_FL : 16;
        uint64_t CoGRange_NI_53_FL : 16;
        uint64_t ValidXBeam_NI_52_FL : 1;
        uint64_t NoInfrastructure_NI_52_FL : 1;
        uint64_t Beam_NI_52_FL : 2;
        uint64_t Azimuth_NI_52_FL__S1 : 4;
        uint64_t Azimuth_NI_52_FL__S0 : 8;
        uint64_t StdAzimuth_NI_52_FL : 8;
        uint64_t SNRdB_NI_52_FL : 8;
        uint64_t PowerDB_NI_52_FL : 8;
        uint64_t WDoppler10DB_NI_52_FL : 7;
        uint64_t Reserve1_1bit_NI_52_FL : 1;
        uint64_t WRange10DB_NI_52_FL : 8;
        uint64_t CoGDoppler_NI_52_FL : 16;
        uint64_t CoGRange_NI_52_FL : 16;
        uint64_t ValidXBeam_NI_51_FL : 1;
        uint64_t NoInfrastructure_NI_51_FL : 1;
        uint64_t Beam_NI_51_FL : 2;
        uint64_t Azimuth_NI_51_FL : 12;
        uint64_t StdAzimuth_NI_51_FL : 8;
        uint64_t SNRdB_NI_51_FL : 8;
        uint64_t PowerDB_NI_51_FL : 8;
        uint64_t WDoppler10DB_NI_51_FL : 7;
        uint64_t Reserve1_1bit_NI_51_FL : 1;
        uint64_t WRange10DB_NI_51_FL : 8;
        uint64_t CoGDoppler_NI_51_FL : 16;
        uint64_t CoGRange_NI_51_FL : 16;
        uint64_t ValidXBeam_NI_50_FL : 1;
        uint64_t NoInfrastructure_NI_50_FL : 1;
        uint64_t Beam_NI_50_FL : 2;
        uint64_t Azimuth_NI_50_FL : 12;
        uint64_t StdAzimuth_NI_50_FL : 8;
        uint64_t SNRdB_NI_50_FL : 8;
        uint64_t PowerDB_NI_50_FL : 8;
        uint64_t WDoppler10DB_NI_50_FL : 7;
        uint64_t Reserve1_1bit_NI_50_FL : 1;
        uint64_t WRange10DB_NI_50_FL : 8;
        uint64_t CoGDoppler_NI_50_FL : 16;
        uint64_t CoGRange_NI_50_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_10_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_10_fl_data_t data;
};

union cansig_mk_non_inf_detection_10_fl__azimuth_ni_52_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_52_FL__S1 : 4;
        uint32_t Azimuth_NI_52_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_11_FL_ID       (0x020D)
#define CANMSG_MK_NON_INF_DETECTION_11_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_11_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_11_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_59_FL : 1;
        uint64_t NoInfrastructure_NI_59_FL : 1;
        uint64_t Beam_NI_59_FL : 2;
        uint64_t Azimuth_NI_59_FL : 12;
        uint64_t StdAzimuth_NI_59_FL : 8;
        uint64_t SNRdB_NI_59_FL : 8;
        uint64_t PowerDB_NI_59_FL : 8;
        uint64_t WDoppler10DB_NI_59_FL : 7;
        uint64_t Reserve1_1bit_NI_59_FL : 1;
        uint64_t WRange10DB_NI_59_FL : 8;
        uint64_t CoGDoppler_NI_59_FL : 16;
        uint64_t CoGRange_NI_59_FL : 16;
        uint64_t ValidXBeam_NI_58_FL : 1;
        uint64_t NoInfrastructure_NI_58_FL : 1;
        uint64_t Beam_NI_58_FL : 2;
        uint64_t Azimuth_NI_58_FL : 12;
        uint64_t StdAzimuth_NI_58_FL : 8;
        uint64_t SNRdB_NI_58_FL : 8;
        uint64_t PowerDB_NI_58_FL : 8;
        uint64_t WDoppler10DB_NI_58_FL : 7;
        uint64_t Reserve1_1bit_NI_58_FL : 1;
        uint64_t WRange10DB_NI_58_FL : 8;
        uint64_t CoGDoppler_NI_58_FL : 16;
        uint64_t CoGRange_NI_58_FL : 16;
        uint64_t ValidXBeam_NI_57_FL : 1;
        uint64_t NoInfrastructure_NI_57_FL : 1;
        uint64_t Beam_NI_57_FL : 2;
        uint64_t Azimuth_NI_57_FL__S1 : 4;
        uint64_t Azimuth_NI_57_FL__S0 : 8;
        uint64_t StdAzimuth_NI_57_FL : 8;
        uint64_t SNRdB_NI_57_FL : 8;
        uint64_t PowerDB_NI_57_FL : 8;
        uint64_t WDoppler10DB_NI_57_FL : 7;
        uint64_t Reserve1_1bit_NI_57_FL : 1;
        uint64_t WRange10DB_NI_57_FL : 8;
        uint64_t CoGDoppler_NI_57_FL : 16;
        uint64_t CoGRange_NI_57_FL : 16;
        uint64_t ValidXBeam_NI_56_FL : 1;
        uint64_t NoInfrastructure_NI_56_FL : 1;
        uint64_t Beam_NI_56_FL : 2;
        uint64_t Azimuth_NI_56_FL : 12;
        uint64_t StdAzimuth_NI_56_FL : 8;
        uint64_t SNRdB_NI_56_FL : 8;
        uint64_t PowerDB_NI_56_FL : 8;
        uint64_t WDoppler10DB_NI_56_FL : 7;
        uint64_t Reserve1_1bit_NI_56_FL : 1;
        uint64_t WRange10DB_NI_56_FL : 8;
        uint64_t CoGDoppler_NI_56_FL : 16;
        uint64_t CoGRange_NI_56_FL : 16;
        uint64_t ValidXBeam_NI_55_FL : 1;
        uint64_t NoInfrastructure_NI_55_FL : 1;
        uint64_t Beam_NI_55_FL : 2;
        uint64_t Azimuth_NI_55_FL : 12;
        uint64_t StdAzimuth_NI_55_FL : 8;
        uint64_t SNRdB_NI_55_FL : 8;
        uint64_t PowerDB_NI_55_FL : 8;
        uint64_t WDoppler10DB_NI_55_FL : 7;
        uint64_t Reserve1_1bit_NI_55_FL : 1;
        uint64_t WRange10DB_NI_55_FL : 8;
        uint64_t CoGDoppler_NI_55_FL : 16;
        uint64_t CoGRange_NI_55_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_11_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_11_fl_data_t data;
};

union cansig_mk_non_inf_detection_11_fl__azimuth_ni_57_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_57_FL__S1 : 4;
        uint32_t Azimuth_NI_57_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_12_FL_ID       (0x020E)
#define CANMSG_MK_NON_INF_DETECTION_12_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_12_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_12_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_64_FL : 1;
        uint64_t NoInfrastructure_NI_64_FL : 1;
        uint64_t Beam_NI_64_FL : 2;
        uint64_t Azimuth_NI_64_FL : 12;
        uint64_t StdAzimuth_NI_64_FL : 8;
        uint64_t SNRdB_NI_64_FL : 8;
        uint64_t PowerDB_NI_64_FL : 8;
        uint64_t WDoppler10DB_NI_64_FL : 7;
        uint64_t Reserve1_1bit_NI_64_FL : 1;
        uint64_t WRange10DB_NI_64_FL : 8;
        uint64_t CoGDoppler_NI_64_FL : 16;
        uint64_t CoGRange_NI_64_FL : 16;
        uint64_t ValidXBeam_NI_63_FL : 1;
        uint64_t NoInfrastructure_NI_63_FL : 1;
        uint64_t Beam_NI_63_FL : 2;
        uint64_t Azimuth_NI_63_FL : 12;
        uint64_t StdAzimuth_NI_63_FL : 8;
        uint64_t SNRdB_NI_63_FL : 8;
        uint64_t PowerDB_NI_63_FL : 8;
        uint64_t WDoppler10DB_NI_63_FL : 7;
        uint64_t Reserve1_1bit_NI_63_FL : 1;
        uint64_t WRange10DB_NI_63_FL : 8;
        uint64_t CoGDoppler_NI_63_FL : 16;
        uint64_t CoGRange_NI_63_FL : 16;
        uint64_t ValidXBeam_NI_62_FL : 1;
        uint64_t NoInfrastructure_NI_62_FL : 1;
        uint64_t Beam_NI_62_FL : 2;
        uint64_t Azimuth_NI_62_FL__S1 : 4;
        uint64_t Azimuth_NI_62_FL__S0 : 8;
        uint64_t StdAzimuth_NI_62_FL : 8;
        uint64_t SNRdB_NI_62_FL : 8;
        uint64_t PowerDB_NI_62_FL : 8;
        uint64_t WDoppler10DB_NI_62_FL : 7;
        uint64_t Reserve1_1bit_NI_62_FL : 1;
        uint64_t WRange10DB_NI_62_FL : 8;
        uint64_t CoGDoppler_NI_62_FL : 16;
        uint64_t CoGRange_NI_62_FL : 16;
        uint64_t ValidXBeam_NI_61_FL : 1;
        uint64_t NoInfrastructure_NI_61_FL : 1;
        uint64_t Beam_NI_61_FL : 2;
        uint64_t Azimuth_NI_61_FL : 12;
        uint64_t StdAzimuth_NI_61_FL : 8;
        uint64_t SNRdB_NI_61_FL : 8;
        uint64_t PowerDB_NI_61_FL : 8;
        uint64_t WDoppler10DB_NI_61_FL : 7;
        uint64_t Reserve1_1bit_NI_61_FL : 1;
        uint64_t WRange10DB_NI_61_FL : 8;
        uint64_t CoGDoppler_NI_61_FL : 16;
        uint64_t CoGRange_NI_61_FL : 16;
        uint64_t ValidXBeam_NI_60_FL : 1;
        uint64_t NoInfrastructure_NI_60_FL : 1;
        uint64_t Beam_NI_60_FL : 2;
        uint64_t Azimuth_NI_60_FL : 12;
        uint64_t StdAzimuth_NI_60_FL : 8;
        uint64_t SNRdB_NI_60_FL : 8;
        uint64_t PowerDB_NI_60_FL : 8;
        uint64_t WDoppler10DB_NI_60_FL : 7;
        uint64_t Reserve1_1bit_NI_60_FL : 1;
        uint64_t WRange10DB_NI_60_FL : 8;
        uint64_t CoGDoppler_NI_60_FL : 16;
        uint64_t CoGRange_NI_60_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_12_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_12_fl_data_t data;
};

union cansig_mk_non_inf_detection_12_fl__azimuth_ni_62_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_62_FL__S1 : 4;
        uint32_t Azimuth_NI_62_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_13_FL_ID       (0x020F)
#define CANMSG_MK_NON_INF_DETECTION_13_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_13_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_13_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_69_FL : 1;
        uint64_t NoInfrastructure_NI_69_FL : 1;
        uint64_t Beam_NI_69_FL : 2;
        uint64_t Azimuth_NI_69_FL : 12;
        uint64_t StdAzimuth_NI_69_FL : 8;
        uint64_t SNRdB_NI_69_FL : 8;
        uint64_t PowerDB_NI_69_FL : 8;
        uint64_t WDoppler10DB_NI_69_FL : 7;
        uint64_t Reserve1_1bit_NI_69_FL : 1;
        uint64_t WRange10DB_NI_69_FL : 8;
        uint64_t CoGDoppler_NI_69_FL : 16;
        uint64_t CoGRange_NI_69_FL : 16;
        uint64_t ValidXBeam_NI_68_FL : 1;
        uint64_t NoInfrastructure_NI_68_FL : 1;
        uint64_t Beam_NI_68_FL : 2;
        uint64_t Azimuth_NI_68_FL : 12;
        uint64_t StdAzimuth_NI_68_FL : 8;
        uint64_t SNRdB_NI_68_FL : 8;
        uint64_t PowerDB_NI_68_FL : 8;
        uint64_t WDoppler10DB_NI_68_FL : 7;
        uint64_t Reserve1_1bit_NI_68_FL : 1;
        uint64_t WRange10DB_NI_68_FL : 8;
        uint64_t CoGDoppler_NI_68_FL : 16;
        uint64_t CoGRange_NI_68_FL : 16;
        uint64_t ValidXBeam_NI_67_FL : 1;
        uint64_t NoInfrastructure_NI_67_FL : 1;
        uint64_t Beam_NI_67_FL : 2;
        uint64_t Azimuth_NI_67_FL__S1 : 4;
        uint64_t Azimuth_NI_67_FL__S0 : 8;
        uint64_t StdAzimuth_NI_67_FL : 8;
        uint64_t SNRdB_NI_67_FL : 8;
        uint64_t PowerDB_NI_67_FL : 8;
        uint64_t WDoppler10DB_NI_67_FL : 7;
        uint64_t Reserve1_1bit_NI_67_FL : 1;
        uint64_t WRange10DB_NI_67_FL : 8;
        uint64_t CoGDoppler_NI_67_FL : 16;
        uint64_t CoGRange_NI_67_FL : 16;
        uint64_t ValidXBeam_NI_66_FL : 1;
        uint64_t NoInfrastructure_NI_66_FL : 1;
        uint64_t Beam_NI_66_FL : 2;
        uint64_t Azimuth_NI_66_FL : 12;
        uint64_t StdAzimuth_NI_66_FL : 8;
        uint64_t SNRdB_NI_66_FL : 8;
        uint64_t PowerDB_NI_66_FL : 8;
        uint64_t WDoppler10DB_NI_66_FL : 7;
        uint64_t Reserve1_1bit_NI_66_FL : 1;
        uint64_t WRange10DB_NI_66_FL : 8;
        uint64_t CoGDoppler_NI_66_FL : 16;
        uint64_t CoGRange_NI_66_FL : 16;
        uint64_t ValidXBeam_NI_65_FL : 1;
        uint64_t NoInfrastructure_NI_65_FL : 1;
        uint64_t Beam_NI_65_FL : 2;
        uint64_t Azimuth_NI_65_FL : 12;
        uint64_t StdAzimuth_NI_65_FL : 8;
        uint64_t SNRdB_NI_65_FL : 8;
        uint64_t PowerDB_NI_65_FL : 8;
        uint64_t WDoppler10DB_NI_65_FL : 7;
        uint64_t Reserve1_1bit_NI_65_FL : 1;
        uint64_t WRange10DB_NI_65_FL : 8;
        uint64_t CoGDoppler_NI_65_FL : 16;
        uint64_t CoGRange_NI_65_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_13_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_13_fl_data_t data;
};

union cansig_mk_non_inf_detection_13_fl__azimuth_ni_67_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_67_FL__S1 : 4;
        uint32_t Azimuth_NI_67_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_14_FL_ID       (0x0210)
#define CANMSG_MK_NON_INF_DETECTION_14_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_14_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_14_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_74_FL : 1;
        uint64_t NoInfrastructure_NI_74_FL : 1;
        uint64_t Beam_NI_74_FL : 2;
        uint64_t Azimuth_NI_74_FL : 12;
        uint64_t StdAzimuth_NI_74_FL : 8;
        uint64_t SNRdB_NI_74_FL : 8;
        uint64_t PowerDB_NI_74_FL : 8;
        uint64_t WDoppler10DB_NI_74_FL : 7;
        uint64_t Reserve1_1bit_NI_74_FL : 1;
        uint64_t WRange10DB_NI_74_FL : 8;
        uint64_t CoGDoppler_NI_74_FL : 16;
        uint64_t CoGRange_NI_74_FL : 16;
        uint64_t ValidXBeam_NI_73_FL : 1;
        uint64_t NoInfrastructure_NI_73_FL : 1;
        uint64_t Beam_NI_73_FL : 2;
        uint64_t Azimuth_NI_73_FL : 12;
        uint64_t StdAzimuth_NI_73_FL : 8;
        uint64_t SNRdB_NI_73_FL : 8;
        uint64_t PowerDB_NI_73_FL : 8;
        uint64_t WDoppler10DB_NI_73_FL : 7;
        uint64_t Reserve1_1bit_NI_73_FL : 1;
        uint64_t WRange10DB_NI_73_FL : 8;
        uint64_t CoGDoppler_NI_73_FL : 16;
        uint64_t CoGRange_NI_73_FL : 16;
        uint64_t ValidXBeam_NI_72_FL : 1;
        uint64_t NoInfrastructure_NI_72_FL : 1;
        uint64_t Beam_NI_72_FL : 2;
        uint64_t Azimuth_NI_72_FL__S1 : 4;
        uint64_t Azimuth_NI_72_FL__S0 : 8;
        uint64_t StdAzimuth_NI_72_FL : 8;
        uint64_t SNRdB_NI_72_FL : 8;
        uint64_t PowerDB_NI_72_FL : 8;
        uint64_t WDoppler10DB_NI_72_FL : 7;
        uint64_t Reserve1_1bit_NI_72_FL : 1;
        uint64_t WRange10DB_NI_72_FL : 8;
        uint64_t CoGDoppler_NI_72_FL : 16;
        uint64_t CoGRange_NI_72_FL : 16;
        uint64_t ValidXBeam_NI_71_FL : 1;
        uint64_t NoInfrastructure_NI_71_FL : 1;
        uint64_t Beam_NI_71_FL : 2;
        uint64_t Azimuth_NI_71_FL : 12;
        uint64_t StdAzimuth_NI_71_FL : 8;
        uint64_t SNRdB_NI_71_FL : 8;
        uint64_t PowerDB_NI_71_FL : 8;
        uint64_t WDoppler10DB_NI_71_FL : 7;
        uint64_t Reserve1_1bit_NI_71_FL : 1;
        uint64_t WRange10DB_NI_71_FL : 8;
        uint64_t CoGDoppler_NI_71_FL : 16;
        uint64_t CoGRange_NI_71_FL : 16;
        uint64_t ValidXBeam_NI_70_FL : 1;
        uint64_t NoInfrastructure_NI_70_FL : 1;
        uint64_t Beam_NI_70_FL : 2;
        uint64_t Azimuth_NI_70_FL : 12;
        uint64_t StdAzimuth_NI_70_FL : 8;
        uint64_t SNRdB_NI_70_FL : 8;
        uint64_t PowerDB_NI_70_FL : 8;
        uint64_t WDoppler10DB_NI_70_FL : 7;
        uint64_t Reserve1_1bit_NI_70_FL : 1;
        uint64_t WRange10DB_NI_70_FL : 8;
        uint64_t CoGDoppler_NI_70_FL : 16;
        uint64_t CoGRange_NI_70_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_14_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_14_fl_data_t data;
};

union cansig_mk_non_inf_detection_14_fl__azimuth_ni_72_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_72_FL__S1 : 4;
        uint32_t Azimuth_NI_72_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_15_FL_ID       (0x0211)
#define CANMSG_MK_NON_INF_DETECTION_15_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_15_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_15_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_79_FL : 1;
        uint64_t NoInfrastructure_NI_79_FL : 1;
        uint64_t Beam_NI_79_FL : 2;
        uint64_t Azimuth_NI_79_FL : 12;
        uint64_t StdAzimuth_NI_79_FL : 8;
        uint64_t SNRdB_NI_79_FL : 8;
        uint64_t PowerDB_NI_79_FL : 8;
        uint64_t WDoppler10DB_NI_79_FL : 7;
        uint64_t Reserve1_1bit_NI_79_FL : 1;
        uint64_t WRange10DB_NI_79_FL : 8;
        uint64_t CoGDoppler_NI_79_FL : 16;
        uint64_t CoGRange_NI_79_FL : 16;
        uint64_t ValidXBeam_NI_78_FL : 1;
        uint64_t NoInfrastructure_NI_78_FL : 1;
        uint64_t Beam_NI_78_FL : 2;
        uint64_t Azimuth_NI_78_FL : 12;
        uint64_t StdAzimuth_NI_78_FL : 8;
        uint64_t SNRdB_NI_78_FL : 8;
        uint64_t PowerDB_NI_78_FL : 8;
        uint64_t WDoppler10DB_NI_78_FL : 7;
        uint64_t Reserve1_1bit_NI_78_FL : 1;
        uint64_t WRange10DB_NI_78_FL : 8;
        uint64_t CoGDoppler_NI_78_FL : 16;
        uint64_t CoGRange_NI_78_FL : 16;
        uint64_t ValidXBeam_NI_77_FL : 1;
        uint64_t NoInfrastructure_NI_77_FL : 1;
        uint64_t Beam_NI_77_FL : 2;
        uint64_t Azimuth_NI_77_FL__S1 : 4;
        uint64_t Azimuth_NI_77_FL__S0 : 8;
        uint64_t StdAzimuth_NI_77_FL : 8;
        uint64_t SNRdB_NI_77_FL : 8;
        uint64_t PowerDB_NI_77_FL : 8;
        uint64_t WDoppler10DB_NI_77_FL : 7;
        uint64_t Reserve1_1bit_NI_77_FL : 1;
        uint64_t WRange10DB_NI_77_FL : 8;
        uint64_t CoGDoppler_NI_77_FL : 16;
        uint64_t CoGRange_NI_77_FL : 16;
        uint64_t ValidXBeam_NI_76_FL : 1;
        uint64_t NoInfrastructure_NI_76_FL : 1;
        uint64_t Beam_NI_76_FL : 2;
        uint64_t Azimuth_NI_76_FL : 12;
        uint64_t StdAzimuth_NI_76_FL : 8;
        uint64_t SNRdB_NI_76_FL : 8;
        uint64_t PowerDB_NI_76_FL : 8;
        uint64_t WDoppler10DB_NI_76_FL : 7;
        uint64_t Reserve1_1bit_NI_76_FL : 1;
        uint64_t WRange10DB_NI_76_FL : 8;
        uint64_t CoGDoppler_NI_76_FL : 16;
        uint64_t CoGRange_NI_76_FL : 16;
        uint64_t ValidXBeam_NI_75_FL : 1;
        uint64_t NoInfrastructure_NI_75_FL : 1;
        uint64_t Beam_NI_75_FL : 2;
        uint64_t Azimuth_NI_75_FL : 12;
        uint64_t StdAzimuth_NI_75_FL : 8;
        uint64_t SNRdB_NI_75_FL : 8;
        uint64_t PowerDB_NI_75_FL : 8;
        uint64_t WDoppler10DB_NI_75_FL : 7;
        uint64_t Reserve1_1bit_NI_75_FL : 1;
        uint64_t WRange10DB_NI_75_FL : 8;
        uint64_t CoGDoppler_NI_75_FL : 16;
        uint64_t CoGRange_NI_75_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_15_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_15_fl_data_t data;
};

union cansig_mk_non_inf_detection_15_fl__azimuth_ni_77_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_77_FL__S1 : 4;
        uint32_t Azimuth_NI_77_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_16_FL_ID       (0x0212)
#define CANMSG_MK_NON_INF_DETECTION_16_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_16_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_16_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_84_FL : 1;
        uint64_t NoInfrastructure_NI_84_FL : 1;
        uint64_t Beam_NI_84_FL : 2;
        uint64_t Azimuth_NI_84_FL : 12;
        uint64_t StdAzimuth_NI_84_FL : 8;
        uint64_t SNRdB_NI_84_FL : 8;
        uint64_t PowerDB_NI_84_FL : 8;
        uint64_t WDoppler10DB_NI_84_FL : 7;
        uint64_t Reserve1_1bit_NI_84_FL : 1;
        uint64_t WRange10DB_NI_84_FL : 8;
        uint64_t CoGDoppler_NI_84_FL : 16;
        uint64_t CoGRange_NI_84_FL : 16;
        uint64_t ValidXBeam_NI_83_FL : 1;
        uint64_t NoInfrastructure_NI_83_FL : 1;
        uint64_t Beam_NI_83_FL : 2;
        uint64_t Azimuth_NI_83_FL : 12;
        uint64_t StdAzimuth_NI_83_FL : 8;
        uint64_t SNRdB_NI_83_FL : 8;
        uint64_t PowerDB_NI_83_FL : 8;
        uint64_t WDoppler10DB_NI_83_FL : 7;
        uint64_t Reserve1_1bit_NI_83_FL : 1;
        uint64_t WRange10DB_NI_83_FL : 8;
        uint64_t CoGDoppler_NI_83_FL : 16;
        uint64_t CoGRange_NI_83_FL : 16;
        uint64_t ValidXBeam_NI_82_FL : 1;
        uint64_t NoInfrastructure_NI_82_FL : 1;
        uint64_t Beam_NI_82_FL : 2;
        uint64_t Azimuth_NI_82_FL__S1 : 4;
        uint64_t Azimuth_NI_82_FL__S0 : 8;
        uint64_t StdAzimuth_NI_82_FL : 8;
        uint64_t SNRdB_NI_82_FL : 8;
        uint64_t PowerDB_NI_82_FL : 8;
        uint64_t WDoppler10DB_NI_82_FL : 7;
        uint64_t Reserve1_1bit_NI_82_FL : 1;
        uint64_t WRange10DB_NI_82_FL : 8;
        uint64_t CoGDoppler_NI_82_FL : 16;
        uint64_t CoGRange_NI_82_FL : 16;
        uint64_t ValidXBeam_NI_81_FL : 1;
        uint64_t NoInfrastructure_NI_81_FL : 1;
        uint64_t Beam_NI_81_FL : 2;
        uint64_t Azimuth_NI_81_FL : 12;
        uint64_t StdAzimuth_NI_81_FL : 8;
        uint64_t SNRdB_NI_81_FL : 8;
        uint64_t PowerDB_NI_81_FL : 8;
        uint64_t WDoppler10DB_NI_81_FL : 7;
        uint64_t Reserve1_1bit_NI_81_FL : 1;
        uint64_t WRange10DB_NI_81_FL : 8;
        uint64_t CoGDoppler_NI_81_FL : 16;
        uint64_t CoGRange_NI_81_FL : 16;
        uint64_t ValidXBeam_NI_80_FL : 1;
        uint64_t NoInfrastructure_NI_80_FL : 1;
        uint64_t Beam_NI_80_FL : 2;
        uint64_t Azimuth_NI_80_FL : 12;
        uint64_t StdAzimuth_NI_80_FL : 8;
        uint64_t SNRdB_NI_80_FL : 8;
        uint64_t PowerDB_NI_80_FL : 8;
        uint64_t WDoppler10DB_NI_80_FL : 7;
        uint64_t Reserve1_1bit_NI_80_FL : 1;
        uint64_t WRange10DB_NI_80_FL : 8;
        uint64_t CoGDoppler_NI_80_FL : 16;
        uint64_t CoGRange_NI_80_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_16_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_16_fl_data_t data;
};

union cansig_mk_non_inf_detection_16_fl__azimuth_ni_82_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_82_FL__S1 : 4;
        uint32_t Azimuth_NI_82_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_17_FL_ID       (0x0213)
#define CANMSG_MK_NON_INF_DETECTION_17_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_17_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_17_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_89_FL : 1;
        uint64_t NoInfrastructure_NI_89_FL : 1;
        uint64_t Beam_NI_89_FL : 2;
        uint64_t Azimuth_NI_89_FL : 12;
        uint64_t StdAzimuth_NI_89_FL : 8;
        uint64_t SNRdB_NI_89_FL : 8;
        uint64_t PowerDB_NI_89_FL : 8;
        uint64_t WDoppler10DB_NI_89_FL : 7;
        uint64_t Reserve1_1bit_NI_89_FL : 1;
        uint64_t WRange10DB_NI_89_FL : 8;
        uint64_t CoGDoppler_NI_89_FL : 16;
        uint64_t CoGRange_NI_89_FL : 16;
        uint64_t ValidXBeam_NI_88_FL : 1;
        uint64_t NoInfrastructure_NI_88_FL : 1;
        uint64_t Beam_NI_88_FL : 2;
        uint64_t Azimuth_NI_88_FL : 12;
        uint64_t StdAzimuth_NI_88_FL : 8;
        uint64_t SNRdB_NI_88_FL : 8;
        uint64_t PowerDB_NI_88_FL : 8;
        uint64_t WDoppler10DB_NI_88_FL : 7;
        uint64_t Reserve1_1bit_NI_88_FL : 1;
        uint64_t WRange10DB_NI_88_FL : 8;
        uint64_t CoGDoppler_NI_88_FL : 16;
        uint64_t CoGRange_NI_88_FL : 16;
        uint64_t ValidXBeam_NI_87_FL : 1;
        uint64_t NoInfrastructure_NI_87_FL : 1;
        uint64_t Beam_NI_87_FL : 2;
        uint64_t Azimuth_NI_87_FL__S1 : 4;
        uint64_t Azimuth_NI_87_FL__S0 : 8;
        uint64_t StdAzimuth_NI_87_FL : 8;
        uint64_t SNRdB_NI_87_FL : 8;
        uint64_t PowerDB_NI_87_FL : 8;
        uint64_t WDoppler10DB_NI_87_FL : 7;
        uint64_t Reserve1_1bit_NI_87_FL : 1;
        uint64_t WRange10DB_NI_87_FL : 8;
        uint64_t CoGDoppler_NI_87_FL : 16;
        uint64_t CoGRange_NI_87_FL : 16;
        uint64_t ValidXBeam_NI_86_FL : 1;
        uint64_t NoInfrastructure_NI_86_FL : 1;
        uint64_t Beam_NI_86_FL : 2;
        uint64_t Azimuth_NI_86_FL : 12;
        uint64_t StdAzimuth_NI_86_FL : 8;
        uint64_t SNRdB_NI_86_FL : 8;
        uint64_t PowerDB_NI_86_FL : 8;
        uint64_t WDoppler10DB_NI_86_FL : 7;
        uint64_t Reserve1_1bit_NI_86_FL : 1;
        uint64_t WRange10DB_NI_86_FL : 8;
        uint64_t CoGDoppler_NI_86_FL : 16;
        uint64_t CoGRange_NI_86_FL : 16;
        uint64_t ValidXBeam_NI_85_FL : 1;
        uint64_t NoInfrastructure_NI_85_FL : 1;
        uint64_t Beam_NI_85_FL : 2;
        uint64_t Azimuth_NI_85_FL : 12;
        uint64_t StdAzimuth_NI_85_FL : 8;
        uint64_t SNRdB_NI_85_FL : 8;
        uint64_t PowerDB_NI_85_FL : 8;
        uint64_t WDoppler10DB_NI_85_FL : 7;
        uint64_t Reserve1_1bit_NI_85_FL : 1;
        uint64_t WRange10DB_NI_85_FL : 8;
        uint64_t CoGDoppler_NI_85_FL : 16;
        uint64_t CoGRange_NI_85_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_17_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_17_fl_data_t data;
};

union cansig_mk_non_inf_detection_17_fl__azimuth_ni_87_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_87_FL__S1 : 4;
        uint32_t Azimuth_NI_87_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_18_FL_ID       (0x0214)
#define CANMSG_MK_NON_INF_DETECTION_18_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_18_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_18_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_94_FL : 1;
        uint64_t NoInfrastructure_NI_94_FL : 1;
        uint64_t Beam_NI_94_FL : 2;
        uint64_t Azimuth_NI_94_FL : 12;
        uint64_t StdAzimuth_NI_94_FL : 8;
        uint64_t SNRdB_NI_94_FL : 8;
        uint64_t PowerDB_NI_94_FL : 8;
        uint64_t WDoppler10DB_NI_94_FL : 7;
        uint64_t Reserve1_1bit_NI_94_FL : 1;
        uint64_t WRange10DB_NI_94_FL : 8;
        uint64_t CoGDoppler_NI_94_FL : 16;
        uint64_t CoGRange_NI_94_FL : 16;
        uint64_t ValidXBeam_NI_93_FL : 1;
        uint64_t NoInfrastructure_NI_93_FL : 1;
        uint64_t Beam_NI_93_FL : 2;
        uint64_t Azimuth_NI_93_FL : 12;
        uint64_t StdAzimuth_NI_93_FL : 8;
        uint64_t SNRdB_NI_93_FL : 8;
        uint64_t PowerDB_NI_93_FL : 8;
        uint64_t WDoppler10DB_NI_93_FL : 7;
        uint64_t Reserve1_1bit_NI_93_FL : 1;
        uint64_t WRange10DB_NI_93_FL : 8;
        uint64_t CoGDoppler_NI_93_FL : 16;
        uint64_t CoGRange_NI_93_FL : 16;
        uint64_t ValidXBeam_NI_92_FL : 1;
        uint64_t NoInfrastructure_NI_92_FL : 1;
        uint64_t Beam_NI_92_FL : 2;
        uint64_t Azimuth_NI_92_FL__S1 : 4;
        uint64_t Azimuth_NI_92_FL__S0 : 8;
        uint64_t StdAzimuth_NI_92_FL : 8;
        uint64_t SNRdB_NI_92_FL : 8;
        uint64_t PowerDB_NI_92_FL : 8;
        uint64_t WDoppler10DB_NI_92_FL : 7;
        uint64_t Reserve1_1bit_NI_92_FL : 1;
        uint64_t WRange10DB_NI_92_FL : 8;
        uint64_t CoGDoppler_NI_92_FL : 16;
        uint64_t CoGRange_NI_92_FL : 16;
        uint64_t ValidXBeam_NI_91_FL : 1;
        uint64_t NoInfrastructure_NI_91_FL : 1;
        uint64_t Beam_NI_91_FL : 2;
        uint64_t Azimuth_NI_91_FL : 12;
        uint64_t StdAzimuth_NI_91_FL : 8;
        uint64_t SNRdB_NI_91_FL : 8;
        uint64_t PowerDB_NI_91_FL : 8;
        uint64_t WDoppler10DB_NI_91_FL : 7;
        uint64_t Reserve1_1bit_NI_91_FL : 1;
        uint64_t WRange10DB_NI_91_FL : 8;
        uint64_t CoGDoppler_NI_91_FL : 16;
        uint64_t CoGRange_NI_91_FL : 16;
        uint64_t ValidXBeam_NI_90_FL : 1;
        uint64_t NoInfrastructure_NI_90_FL : 1;
        uint64_t Beam_NI_90_FL : 2;
        uint64_t Azimuth_NI_90_FL : 12;
        uint64_t StdAzimuth_NI_90_FL : 8;
        uint64_t SNRdB_NI_90_FL : 8;
        uint64_t PowerDB_NI_90_FL : 8;
        uint64_t WDoppler10DB_NI_90_FL : 7;
        uint64_t Reserve1_1bit_NI_90_FL : 1;
        uint64_t WRange10DB_NI_90_FL : 8;
        uint64_t CoGDoppler_NI_90_FL : 16;
        uint64_t CoGRange_NI_90_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_18_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_18_fl_data_t data;
};

union cansig_mk_non_inf_detection_18_fl__azimuth_ni_92_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_92_FL__S1 : 4;
        uint32_t Azimuth_NI_92_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_19_FL_ID       (0x0215)
#define CANMSG_MK_NON_INF_DETECTION_19_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_19_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_19_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_99_FL : 1;
        uint64_t NoInfrastructure_NI_99_FL : 1;
        uint64_t Beam_NI_99_FL : 2;
        uint64_t Azimuth_NI_99_FL : 12;
        uint64_t StdAzimuth_NI_99_FL : 8;
        uint64_t SNRdB_NI_99_FL : 8;
        uint64_t PowerDB_NI_99_FL : 8;
        uint64_t WDoppler10DB_NI_99_FL : 7;
        uint64_t Reserve1_1bit_NI_99_FL : 1;
        uint64_t WRange10DB_NI_99_FL : 8;
        uint64_t CoGDoppler_NI_99_FL : 16;
        uint64_t CoGRange_NI_99_FL : 16;
        uint64_t ValidXBeam_NI_98_FL : 1;
        uint64_t NoInfrastructure_NI_98_FL : 1;
        uint64_t Beam_NI_98_FL : 2;
        uint64_t Azimuth_NI_98_FL : 12;
        uint64_t StdAzimuth_NI_98_FL : 8;
        uint64_t SNRdB_NI_98_FL : 8;
        uint64_t PowerDB_NI_98_FL : 8;
        uint64_t WDoppler10DB_NI_98_FL : 7;
        uint64_t Reserve1_1bit_NI_98_FL : 1;
        uint64_t WRange10DB_NI_98_FL : 8;
        uint64_t CoGDoppler_NI_98_FL : 16;
        uint64_t CoGRange_NI_98_FL : 16;
        uint64_t ValidXBeam_NI_97_FL : 1;
        uint64_t NoInfrastructure_NI_97_FL : 1;
        uint64_t Beam_NI_97_FL : 2;
        uint64_t Azimuth_NI_97_FL__S1 : 4;
        uint64_t Azimuth_NI_97_FL__S0 : 8;
        uint64_t StdAzimuth_NI_97_FL : 8;
        uint64_t SNRdB_NI_97_FL : 8;
        uint64_t PowerDB_NI_97_FL : 8;
        uint64_t WDoppler10DB_NI_97_FL : 7;
        uint64_t Reserve1_1bit_NI_97_FL : 1;
        uint64_t WRange10DB_NI_97_FL : 8;
        uint64_t CoGDoppler_NI_97_FL : 16;
        uint64_t CoGRange_NI_97_FL : 16;
        uint64_t ValidXBeam_NI_96_FL : 1;
        uint64_t NoInfrastructure_NI_96_FL : 1;
        uint64_t Beam_NI_96_FL : 2;
        uint64_t Azimuth_NI_96_FL : 12;
        uint64_t StdAzimuth_NI_96_FL : 8;
        uint64_t SNRdB_NI_96_FL : 8;
        uint64_t PowerDB_NI_96_FL : 8;
        uint64_t WDoppler10DB_NI_96_FL : 7;
        uint64_t Reserve1_1bit_NI_96_FL : 1;
        uint64_t WRange10DB_NI_96_FL : 8;
        uint64_t CoGDoppler_NI_96_FL : 16;
        uint64_t CoGRange_NI_96_FL : 16;
        uint64_t ValidXBeam_NI_95_FL : 1;
        uint64_t NoInfrastructure_NI_95_FL : 1;
        uint64_t Beam_NI_95_FL : 2;
        uint64_t Azimuth_NI_95_FL : 12;
        uint64_t StdAzimuth_NI_95_FL : 8;
        uint64_t SNRdB_NI_95_FL : 8;
        uint64_t PowerDB_NI_95_FL : 8;
        uint64_t WDoppler10DB_NI_95_FL : 7;
        uint64_t Reserve1_1bit_NI_95_FL : 1;
        uint64_t WRange10DB_NI_95_FL : 8;
        uint64_t CoGDoppler_NI_95_FL : 16;
        uint64_t CoGRange_NI_95_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_19_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_19_fl_data_t data;
};

union cansig_mk_non_inf_detection_19_fl__azimuth_ni_97_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_97_FL__S1 : 4;
        uint32_t Azimuth_NI_97_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_1_FL_ID        (0x0203)
#define CANMSG_MK_NON_INF_DETECTION_1_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_1_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_1_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_9_FL : 1;
        uint64_t NoInfrastructure_NI_9_FL : 1;
        uint64_t Beam_NI_9_FL : 2;
        uint64_t Azimuth_NI_9_FL : 12;
        uint64_t StdAzimuth_NI_9_FL : 8;
        uint64_t SNRdB_NI_9_FL : 8;
        uint64_t PowerDB_NI_9_FL : 8;
        uint64_t WDoppler10DB_NI_9_FL : 7;
        uint64_t Reserve1_1bit_NI_9_FL : 1;
        uint64_t WRange10DB_NI_9_FL : 8;
        uint64_t CoGDoppler_NI_9_FL : 16;
        uint64_t CoGRange_NI_9_FL : 16;
        uint64_t ValidXBeam_NI_8_FL : 1;
        uint64_t NoInfrastructure_NI_8_FL : 1;
        uint64_t Beam_NI_8_FL : 2;
        uint64_t Azimuth_NI_8_FL : 12;
        uint64_t StdAzimuth_NI_8_FL : 8;
        uint64_t SNRdB_NI_8_FL : 8;
        uint64_t PowerDB_NI_8_FL : 8;
        uint64_t WDoppler10DB_NI_8_FL : 7;
        uint64_t Reserve1_1bit_NI_8_FL : 1;
        uint64_t WRange10DB_NI_8_FL : 8;
        uint64_t CoGDoppler_NI_8_FL : 16;
        uint64_t CoGRange_NI_8_FL : 16;
        uint64_t ValidXBeam_NI_7_FL : 1;
        uint64_t NoInfrastructure_NI_7_FL : 1;
        uint64_t Beam_NI_7_FL : 2;
        uint64_t Azimuth_NI_7_FL__S1 : 4;
        uint64_t Azimuth_NI_7_FL__S0 : 8;
        uint64_t StdAzimuth_NI_7_FL : 8;
        uint64_t SNRdB_NI_7_FL : 8;
        uint64_t PowerDB_NI_7_FL : 8;
        uint64_t WDoppler10DB_NI_7_FL : 7;
        uint64_t Reserve1_1bit_NI_7_FL : 1;
        uint64_t WRange10DB_NI_7_FL : 8;
        uint64_t CoGDoppler_NI_7_FL : 16;
        uint64_t CoGRange_NI_7_FL : 16;
        uint64_t ValidXBeam_NI_6_FL : 1;
        uint64_t NoInfrastructure_NI_6_FL : 1;
        uint64_t Beam_NI_6_FL : 2;
        uint64_t Azimuth_NI_6_FL : 12;
        uint64_t StdAzimuth_NI_6_FL : 8;
        uint64_t SNRdB_NI_6_FL : 8;
        uint64_t PowerDB_NI_6_FL : 8;
        uint64_t WDoppler10DB_NI_6_FL : 7;
        uint64_t Reserve1_1bit_NI_6_FL : 1;
        uint64_t WRange10DB_NI_6_FL : 8;
        uint64_t CoGDoppler_NI_6_FL : 16;
        uint64_t CoGRange_NI_6_FL : 16;
        uint64_t ValidXBeam_NI_5_FL : 1;
        uint64_t NoInfrastructure_NI_5_FL : 1;
        uint64_t Beam_NI_5_FL : 2;
        uint64_t Azimuth_NI_5_FL : 12;
        uint64_t StdAzimuth_NI_5_FL : 8;
        uint64_t SNRdB_NI_5_FL : 8;
        uint64_t PowerDB_NI_5_FL : 8;
        uint64_t WDoppler10DB_NI_5_FL : 7;
        uint64_t Reserve1_1bit_NI_5_FL : 1;
        uint64_t WRange10DB_NI_5_FL : 8;
        uint64_t CoGDoppler_NI_5_FL : 16;
        uint64_t CoGRange_NI_5_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_1_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_1_fl_data_t data;
};

union cansig_mk_non_inf_detection_1_fl__azimuth_ni_7_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_7_FL__S1 : 4;
        uint32_t Azimuth_NI_7_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_20_FL_ID       (0x0216)
#define CANMSG_MK_NON_INF_DETECTION_20_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_20_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_20_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_104_FL : 1;
        uint64_t NoInfrastructure_NI_104_FL : 1;
        uint64_t Beam_NI_104_FL : 2;
        uint64_t Azimuth_NI_104_FL : 12;
        uint64_t StdAzimuth_NI_104_FL : 8;
        uint64_t SNRdB_NI_104_FL : 8;
        uint64_t PowerDB_NI_104_FL : 8;
        uint64_t WDoppler10DB_NI_104_FL : 7;
        uint64_t Reserve1_1bit_NI_104_FL : 1;
        uint64_t WRange10DB_NI_104_FL : 8;
        uint64_t CoGDoppler_NI_104_FL : 16;
        uint64_t CoGRange_NI_104_FL : 16;
        uint64_t ValidXBeam_NI_103_FL : 1;
        uint64_t NoInfrastructure_NI_103_FL : 1;
        uint64_t Beam_NI_103_FL : 2;
        uint64_t Azimuth_NI_103_FL : 12;
        uint64_t StdAzimuth_NI_103_FL : 8;
        uint64_t SNRdB_NI_103_FL : 8;
        uint64_t PowerDB_NI_103_FL : 8;
        uint64_t WDoppler10DB_NI_103_FL : 7;
        uint64_t Reserve1_1bit_NI_103_FL : 1;
        uint64_t WRange10DB_NI_103_FL : 8;
        uint64_t CoGDoppler_NI_103_FL : 16;
        uint64_t CoGRange_NI_103_FL : 16;
        uint64_t ValidXBeam_NI_102_FL : 1;
        uint64_t NoInfrastructure_NI_102_FL : 1;
        uint64_t Beam_NI_102_FL : 2;
        uint64_t Azimuth_NI_102_FL__S1 : 4;
        uint64_t Azimuth_NI_102_FL__S0 : 8;
        uint64_t StdAzimuth_NI_102_FL : 8;
        uint64_t SNRdB_NI_102_FL : 8;
        uint64_t PowerDB_NI_102_FL : 8;
        uint64_t WDoppler10DB_NI_102_FL : 7;
        uint64_t Reserve1_1bit_NI_102_FL : 1;
        uint64_t WRange10DB_NI_102_FL : 8;
        uint64_t CoGDoppler_NI_102_FL : 16;
        uint64_t CoGRange_NI_102_FL : 16;
        uint64_t ValidXBeam_NI_101_FL : 1;
        uint64_t NoInfrastructure_NI_101_FL : 1;
        uint64_t Beam_NI_101_FL : 2;
        uint64_t Azimuth_NI_101_FL : 12;
        uint64_t StdAzimuth_NI_101_FL : 8;
        uint64_t SNRdB_NI_101_FL : 8;
        uint64_t PowerDB_NI_101_FL : 8;
        uint64_t WDoppler10DB_NI_101_FL : 7;
        uint64_t Reserve1_1bit_NI_101_FL : 1;
        uint64_t WRange10DB_NI_101_FL : 8;
        uint64_t CoGDoppler_NI_101_FL : 16;
        uint64_t CoGRange_NI_101_FL : 16;
        uint64_t ValidXBeam_NI_100_FL : 1;
        uint64_t NoInfrastructure_NI_100_FL : 1;
        uint64_t Beam_NI_100_FL : 2;
        uint64_t Azimuth_NI_100_FL : 12;
        uint64_t StdAzimuth_NI_100_FL : 8;
        uint64_t SNRdB_NI_100_FL : 8;
        uint64_t PowerDB_NI_100_FL : 8;
        uint64_t WDoppler10DB_NI_100_FL : 7;
        uint64_t Reserve1_1bit_NI_100_FL : 1;
        uint64_t WRange10DB_NI_100_FL : 8;
        uint64_t CoGDoppler_NI_100_FL : 16;
        uint64_t CoGRange_NI_100_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_20_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_20_fl_data_t data;
};

union cansig_mk_non_inf_detection_20_fl__azimuth_ni_102_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_102_FL__S1 : 4;
        uint32_t Azimuth_NI_102_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_21_FL_ID       (0x0217)
#define CANMSG_MK_NON_INF_DETECTION_21_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_21_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_21_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_109_FL : 1;
        uint64_t NoInfrastructure_NI_109_FL : 1;
        uint64_t Beam_NI_109_FL : 2;
        uint64_t Azimuth_NI_109_FL : 12;
        uint64_t StdAzimuth_NI_109_FL : 8;
        uint64_t SNRdB_NI_109_FL : 8;
        uint64_t PowerDB_NI_109_FL : 8;
        uint64_t WDoppler10DB_NI_109_FL : 7;
        uint64_t Reserve1_1bit_NI_109_FL : 1;
        uint64_t WRange10DB_NI_109_FL : 8;
        uint64_t CoGDoppler_NI_109_FL : 16;
        uint64_t CoGRange_NI_109_FL : 16;
        uint64_t ValidXBeam_NI_108_FL : 1;
        uint64_t NoInfrastructure_NI_108_FL : 1;
        uint64_t Beam_NI_108_FL : 2;
        uint64_t Azimuth_NI_108_FL : 12;
        uint64_t StdAzimuth_NI_108_FL : 8;
        uint64_t SNRdB_NI_108_FL : 8;
        uint64_t PowerDB_NI_108_FL : 8;
        uint64_t WDoppler10DB_NI_108_FL : 7;
        uint64_t Reserve1_1bit_NI_108_FL : 1;
        uint64_t WRange10DB_NI_108_FL : 8;
        uint64_t CoGDoppler_NI_108_FL : 16;
        uint64_t CoGRange_NI_108_FL : 16;
        uint64_t ValidXBeam_NI_107_FL : 1;
        uint64_t NoInfrastructure_NI_107_FL : 1;
        uint64_t Beam_NI_107_FL : 2;
        uint64_t Azimuth_NI_107_FL__S1 : 4;
        uint64_t Azimuth_NI_107_FL__S0 : 8;
        uint64_t StdAzimuth_NI_107_FL : 8;
        uint64_t SNRdB_NI_107_FL : 8;
        uint64_t PowerDB_NI_107_FL : 8;
        uint64_t WDoppler10DB_NI_107_FL : 7;
        uint64_t Reserve1_1bit_NI_107_FL : 1;
        uint64_t WRange10DB_NI_107_FL : 8;
        uint64_t CoGDoppler_NI_107_FL : 16;
        uint64_t CoGRange_NI_107_FL : 16;
        uint64_t ValidXBeam_NI_106_FL : 1;
        uint64_t NoInfrastructure_NI_106_FL : 1;
        uint64_t Beam_NI_106_FL : 2;
        uint64_t Azimuth_NI_106_FL : 12;
        uint64_t StdAzimuth_NI_106_FL : 8;
        uint64_t SNRdB_NI_106_FL : 8;
        uint64_t PowerDB_NI_106_FL : 8;
        uint64_t WDoppler10DB_NI_106_FL : 7;
        uint64_t Reserve1_1bit_NI_106_FL : 1;
        uint64_t WRange10DB_NI_106_FL : 8;
        uint64_t CoGDoppler_NI_106_FL : 16;
        uint64_t CoGRange_NI_106_FL : 16;
        uint64_t ValidXBeam_NI_105_FL : 1;
        uint64_t NoInfrastructure_NI_105_FL : 1;
        uint64_t Beam_NI_105_FL : 2;
        uint64_t Azimuth_NI_105_FL : 12;
        uint64_t StdAzimuth_NI_105_FL : 8;
        uint64_t SNRdB_NI_105_FL : 8;
        uint64_t PowerDB_NI_105_FL : 8;
        uint64_t WDoppler10DB_NI_105_FL : 7;
        uint64_t Reserve1_1bit_NI_105_FL : 1;
        uint64_t WRange10DB_NI_105_FL : 8;
        uint64_t CoGDoppler_NI_105_FL : 16;
        uint64_t CoGRange_NI_105_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_21_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_21_fl_data_t data;
};

union cansig_mk_non_inf_detection_21_fl__azimuth_ni_107_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_107_FL__S1 : 4;
        uint32_t Azimuth_NI_107_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_22_FL_ID       (0x0218)
#define CANMSG_MK_NON_INF_DETECTION_22_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_22_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_22_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_114_FL : 1;
        uint64_t NoInfrastructure_NI_114_FL : 1;
        uint64_t Beam_NI_114_FL : 2;
        uint64_t Azimuth_NI_114_FL : 12;
        uint64_t StdAzimuth_NI_114_FL : 8;
        uint64_t SNRdB_NI_114_FL : 8;
        uint64_t PowerDB_NI_114_FL : 8;
        uint64_t WDoppler10DB_NI_114_FL : 7;
        uint64_t Reserve1_1bit_NI_114_FL : 1;
        uint64_t WRange10DB_NI_114_FL : 8;
        uint64_t CoGDoppler_NI_114_FL : 16;
        uint64_t CoGRange_NI_114_FL : 16;
        uint64_t ValidXBeam_NI_113_FL : 1;
        uint64_t NoInfrastructure_NI_113_FL : 1;
        uint64_t Beam_NI_113_FL : 2;
        uint64_t Azimuth_NI_113_FL : 12;
        uint64_t StdAzimuth_NI_113_FL : 8;
        uint64_t SNRdB_NI_113_FL : 8;
        uint64_t PowerDB_NI_113_FL : 8;
        uint64_t WDoppler10DB_NI_113_FL : 7;
        uint64_t Reserve1_1bit_NI_113_FL : 1;
        uint64_t WRange10DB_NI_113_FL : 8;
        uint64_t CoGDoppler_NI_113_FL : 16;
        uint64_t CoGRange_NI_113_FL : 16;
        uint64_t ValidXBeam_NI_112_FL : 1;
        uint64_t NoInfrastructure_NI_112_FL : 1;
        uint64_t Beam_NI_112_FL : 2;
        uint64_t Azimuth_NI_112_FL__S1 : 4;
        uint64_t Azimuth_NI_112_FL__S0 : 8;
        uint64_t StdAzimuth_NI_112_FL : 8;
        uint64_t SNRdB_NI_112_FL : 8;
        uint64_t PowerDB_NI_112_FL : 8;
        uint64_t WDoppler10DB_NI_112_FL : 7;
        uint64_t Reserve1_1bit_NI_112_FL : 1;
        uint64_t WRange10DB_NI_112_FL : 8;
        uint64_t CoGDoppler_NI_112_FL : 16;
        uint64_t CoGRange_NI_112_FL : 16;
        uint64_t ValidXBeam_NI_111_FL : 1;
        uint64_t NoInfrastructure_NI_111_FL : 1;
        uint64_t Beam_NI_111_FL : 2;
        uint64_t Azimuth_NI_111_FL : 12;
        uint64_t StdAzimuth_NI_111_FL : 8;
        uint64_t SNRdB_NI_111_FL : 8;
        uint64_t PowerDB_NI_111_FL : 8;
        uint64_t WDoppler10DB_NI_111_FL : 7;
        uint64_t Reserve1_1bit_NI_111_FL : 1;
        uint64_t WRange10DB_NI_111_FL : 8;
        uint64_t CoGDoppler_NI_111_FL : 16;
        uint64_t CoGRange_NI_111_FL : 16;
        uint64_t ValidXBeam_NI_110_FL : 1;
        uint64_t NoInfrastructure_NI_110_FL : 1;
        uint64_t Beam_NI_110_FL : 2;
        uint64_t Azimuth_NI_110_FL : 12;
        uint64_t StdAzimuth_NI_110_FL : 8;
        uint64_t SNRdB_NI_110_FL : 8;
        uint64_t PowerDB_NI_110_FL : 8;
        uint64_t WDoppler10DB_NI_110_FL : 7;
        uint64_t Reserve1_1bit_NI_110_FL : 1;
        uint64_t WRange10DB_NI_110_FL : 8;
        uint64_t CoGDoppler_NI_110_FL : 16;
        uint64_t CoGRange_NI_110_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_22_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_22_fl_data_t data;
};

union cansig_mk_non_inf_detection_22_fl__azimuth_ni_112_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_112_FL__S1 : 4;
        uint32_t Azimuth_NI_112_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_23_FL_ID       (0x0219)
#define CANMSG_MK_NON_INF_DETECTION_23_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_23_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_23_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_119_FL : 1;
        uint64_t NoInfrastructure_NI_119_FL : 1;
        uint64_t Beam_NI_119_FL : 2;
        uint64_t Azimuth_NI_119_FL : 12;
        uint64_t StdAzimuth_NI_119_FL : 8;
        uint64_t SNRdB_NI_119_FL : 8;
        uint64_t PowerDB_NI_119_FL : 8;
        uint64_t WDoppler10DB_NI_119_FL : 7;
        uint64_t Reserve1_1bit_NI_119_FL : 1;
        uint64_t WRange10DB_NI_119_FL : 8;
        uint64_t CoGDoppler_NI_119_FL : 16;
        uint64_t CoGRange_NI_119_FL : 16;
        uint64_t ValidXBeam_NI_118_FL : 1;
        uint64_t NoInfrastructure_NI_118_FL : 1;
        uint64_t Beam_NI_118_FL : 2;
        uint64_t Azimuth_NI_118_FL : 12;
        uint64_t StdAzimuth_NI_118_FL : 8;
        uint64_t SNRdB_NI_118_FL : 8;
        uint64_t PowerDB_NI_118_FL : 8;
        uint64_t WDoppler10DB_NI_118_FL : 7;
        uint64_t Reserve1_1bit_NI_118_FL : 1;
        uint64_t WRange10DB_NI_118_FL : 8;
        uint64_t CoGDoppler_NI_118_FL : 16;
        uint64_t CoGRange_NI_118_FL : 16;
        uint64_t ValidXBeam_NI_117_FL : 1;
        uint64_t NoInfrastructure_NI_117_FL : 1;
        uint64_t Beam_NI_117_FL : 2;
        uint64_t Azimuth_NI_117_FL__S1 : 4;
        uint64_t Azimuth_NI_117_FL__S0 : 8;
        uint64_t StdAzimuth_NI_117_FL : 8;
        uint64_t SNRdB_NI_117_FL : 8;
        uint64_t PowerDB_NI_117_FL : 8;
        uint64_t WDoppler10DB_NI_117_FL : 7;
        uint64_t Reserve1_1bit_NI_117_FL : 1;
        uint64_t WRange10DB_NI_117_FL : 8;
        uint64_t CoGDoppler_NI_117_FL : 16;
        uint64_t CoGRange_NI_117_FL : 16;
        uint64_t ValidXBeam_NI_116_FL : 1;
        uint64_t NoInfrastructure_NI_116_FL : 1;
        uint64_t Beam_NI_116_FL : 2;
        uint64_t Azimuth_NI_116_FL : 12;
        uint64_t StdAzimuth_NI_116_FL : 8;
        uint64_t SNRdB_NI_116_FL : 8;
        uint64_t PowerDB_NI_116_FL : 8;
        uint64_t WDoppler10DB_NI_116_FL : 7;
        uint64_t Reserve1_1bit_NI_116_FL : 1;
        uint64_t WRange10DB_NI_116_FL : 8;
        uint64_t CoGDoppler_NI_116_FL : 16;
        uint64_t CoGRange_NI_116_FL : 16;
        uint64_t ValidXBeam_NI_115_FL : 1;
        uint64_t NoInfrastructure_NI_115_FL : 1;
        uint64_t Beam_NI_115_FL : 2;
        uint64_t Azimuth_NI_115_FL : 12;
        uint64_t StdAzimuth_NI_115_FL : 8;
        uint64_t SNRdB_NI_115_FL : 8;
        uint64_t PowerDB_NI_115_FL : 8;
        uint64_t WDoppler10DB_NI_115_FL : 7;
        uint64_t Reserve1_1bit_NI_115_FL : 1;
        uint64_t WRange10DB_NI_115_FL : 8;
        uint64_t CoGDoppler_NI_115_FL : 16;
        uint64_t CoGRange_NI_115_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_23_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_23_fl_data_t data;
};

union cansig_mk_non_inf_detection_23_fl__azimuth_ni_117_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_117_FL__S1 : 4;
        uint32_t Azimuth_NI_117_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_24_FL_ID       (0x021A)
#define CANMSG_MK_NON_INF_DETECTION_24_FL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_24_FL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_24_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_124_FL : 1;
        uint64_t NoInfrastructure_NI_124_FL : 1;
        uint64_t Beam_NI_124_FL : 2;
        uint64_t Azimuth_NI_124_FL : 12;
        uint64_t StdAzimuth_NI_124_FL : 8;
        uint64_t SNRdB_NI_124_FL : 8;
        uint64_t PowerDB_NI_124_FL : 8;
        uint64_t WDoppler10DB_NI_124_FL : 7;
        uint64_t Reserve1_1bit_NI_124_FL : 1;
        uint64_t WRange10DB_NI_124_FL : 8;
        uint64_t CoGDoppler_NI_124_FL : 16;
        uint64_t CoGRange_NI_124_FL : 16;
        uint64_t ValidXBeam_NI_123_FL : 1;
        uint64_t NoInfrastructure_NI_123_FL : 1;
        uint64_t Beam_NI_123_FL : 2;
        uint64_t Azimuth_NI_123_FL : 12;
        uint64_t StdAzimuth_NI_123_FL : 8;
        uint64_t SNRdB_NI_123_FL : 8;
        uint64_t PowerDB_NI_123_FL : 8;
        uint64_t WDoppler10DB_NI_123_FL : 7;
        uint64_t Reserve1_1bit_NI_123_FL : 1;
        uint64_t WRange10DB_NI_123_FL : 8;
        uint64_t CoGDoppler_NI_123_FL : 16;
        uint64_t CoGRange_NI_123_FL : 16;
        uint64_t ValidXBeam_NI_122_FL : 1;
        uint64_t NoInfrastructure_NI_122_FL : 1;
        uint64_t Beam_NI_122_FL : 2;
        uint64_t Azimuth_NI_122_FL__S1 : 4;
        uint64_t Azimuth_NI_122_FL__S0 : 8;
        uint64_t StdAzimuth_NI_122_FL : 8;
        uint64_t SNRdB_NI_122_FL : 8;
        uint64_t PowerDB_NI_122_FL : 8;
        uint64_t WDoppler10DB_NI_122_FL : 7;
        uint64_t Reserve1_1bit_NI_122_FL : 1;
        uint64_t WRange10DB_NI_122_FL : 8;
        uint64_t CoGDoppler_NI_122_FL : 16;
        uint64_t CoGRange_NI_122_FL : 16;
        uint64_t ValidXBeam_NI_121_FL : 1;
        uint64_t NoInfrastructure_NI_121_FL : 1;
        uint64_t Beam_NI_121_FL : 2;
        uint64_t Azimuth_NI_121_FL : 12;
        uint64_t StdAzimuth_NI_121_FL : 8;
        uint64_t SNRdB_NI_121_FL : 8;
        uint64_t PowerDB_NI_121_FL : 8;
        uint64_t WDoppler10DB_NI_121_FL : 7;
        uint64_t Reserve1_1bit_NI_121_FL : 1;
        uint64_t WRange10DB_NI_121_FL : 8;
        uint64_t CoGDoppler_NI_121_FL : 16;
        uint64_t CoGRange_NI_121_FL : 16;
        uint64_t ValidXBeam_NI_120_FL : 1;
        uint64_t NoInfrastructure_NI_120_FL : 1;
        uint64_t Beam_NI_120_FL : 2;
        uint64_t Azimuth_NI_120_FL : 12;
        uint64_t StdAzimuth_NI_120_FL : 8;
        uint64_t SNRdB_NI_120_FL : 8;
        uint64_t PowerDB_NI_120_FL : 8;
        uint64_t WDoppler10DB_NI_120_FL : 7;
        uint64_t Reserve1_1bit_NI_120_FL : 1;
        uint64_t WRange10DB_NI_120_FL : 8;
        uint64_t CoGDoppler_NI_120_FL : 16;
        uint64_t CoGRange_NI_120_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_24_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_24_fl_data_t data;
};

union cansig_mk_non_inf_detection_24_fl__azimuth_ni_122_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_122_FL__S1 : 4;
        uint32_t Azimuth_NI_122_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_2_FL_ID        (0x0204)
#define CANMSG_MK_NON_INF_DETECTION_2_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_2_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_2_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_14_FL : 1;
        uint64_t NoInfrastructure_NI_14_FL : 1;
        uint64_t Beam_NI_14_FL : 2;
        uint64_t Azimuth_NI_14_FL : 12;
        uint64_t StdAzimuth_NI_14_FL : 8;
        uint64_t SNRdB_NI_14_FL : 8;
        uint64_t PowerDB_NI_14_FL : 8;
        uint64_t WDoppler10DB_NI_14_FL : 7;
        uint64_t Reserve1_1bit_NI_14_FL : 1;
        uint64_t WRange10DB_NI_14_FL : 8;
        uint64_t CoGDoppler_NI_14_FL : 16;
        uint64_t CoGRange_NI_14_FL : 16;
        uint64_t ValidXBeam_NI_13_FL : 1;
        uint64_t NoInfrastructure_NI_13_FL : 1;
        uint64_t Beam_NI_13_FL : 2;
        uint64_t Azimuth_NI_13_FL : 12;
        uint64_t StdAzimuth_NI_13_FL : 8;
        uint64_t SNRdB_NI_13_FL : 8;
        uint64_t PowerDB_NI_13_FL : 8;
        uint64_t WDoppler10DB_NI_13_FL : 7;
        uint64_t Reserve1_1bit_NI_13_FL : 1;
        uint64_t WRange10DB_NI_13_FL : 8;
        uint64_t CoGDoppler_NI_13_FL : 16;
        uint64_t CoGRange_NI_13_FL : 16;
        uint64_t ValidXBeam_NI_12_FL : 1;
        uint64_t NoInfrastructure_NI_12_FL : 1;
        uint64_t Beam_NI_12_FL : 2;
        uint64_t Azimuth_NI_12_FL__S1 : 4;
        uint64_t Azimuth_NI_12_FL__S0 : 8;
        uint64_t StdAzimuth_NI_12_FL : 8;
        uint64_t SNRdB_NI_12_FL : 8;
        uint64_t PowerDB_NI_12_FL : 8;
        uint64_t WDoppler10DB_NI_12_FL : 7;
        uint64_t Reserve1_1bit_NI_12_FL : 1;
        uint64_t WRange10DB_NI_12_FL : 8;
        uint64_t CoGDoppler_NI_12_FL : 16;
        uint64_t CoGRange_NI_12_FL : 16;
        uint64_t ValidXBeam_NI_11_FL : 1;
        uint64_t NoInfrastructure_NI_11_FL : 1;
        uint64_t Beam_NI_11_FL : 2;
        uint64_t Azimuth_NI_11_FL : 12;
        uint64_t StdAzimuth_NI_11_FL : 8;
        uint64_t SNRdB_NI_11_FL : 8;
        uint64_t PowerDB_NI_11_FL : 8;
        uint64_t WDoppler10DB_NI_11_FL : 7;
        uint64_t Reserve1_1bit_NI_11_FL : 1;
        uint64_t WRange10DB_NI_11_FL : 8;
        uint64_t CoGDoppler_NI_11_FL : 16;
        uint64_t CoGRange_NI_11_FL : 16;
        uint64_t ValidXBeam_NI_10_FL : 1;
        uint64_t NoInfrastructure_NI_10_FL : 1;
        uint64_t Beam_NI_10_FL : 2;
        uint64_t Azimuth_NI_10_FL : 12;
        uint64_t StdAzimuth_NI_10_FL : 8;
        uint64_t SNRdB_NI_10_FL : 8;
        uint64_t PowerDB_NI_10_FL : 8;
        uint64_t WDoppler10DB_NI_10_FL : 7;
        uint64_t Reserve1_1bit_NI_10_FL : 1;
        uint64_t WRange10DB_NI_10_FL : 8;
        uint64_t CoGDoppler_NI_10_FL : 16;
        uint64_t CoGRange_NI_10_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_2_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_2_fl_data_t data;
};

union cansig_mk_non_inf_detection_2_fl__azimuth_ni_12_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_12_FL__S1 : 4;
        uint32_t Azimuth_NI_12_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_3_FL_ID        (0x0205)
#define CANMSG_MK_NON_INF_DETECTION_3_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_3_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_3_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_19_FL : 1;
        uint64_t NoInfrastructure_NI_19_FL : 1;
        uint64_t Beam_NI_19_FL : 2;
        uint64_t Azimuth_NI_19_FL : 12;
        uint64_t StdAzimuth_NI_19_FL : 8;
        uint64_t SNRdB_NI_19_FL : 8;
        uint64_t PowerDB_NI_19_FL : 8;
        uint64_t WDoppler10DB_NI_19_FL : 7;
        uint64_t Reserve1_1bit_NI_19_FL : 1;
        uint64_t WRange10DB_NI_19_FL : 8;
        uint64_t CoGDoppler_NI_19_FL : 16;
        uint64_t CoGRange_NI_19_FL : 16;
        uint64_t ValidXBeam_NI_18_FL : 1;
        uint64_t NoInfrastructure_NI_18_FL : 1;
        uint64_t Beam_NI_18_FL : 2;
        uint64_t Azimuth_NI_18_FL : 12;
        uint64_t StdAzimuth_NI_18_FL : 8;
        uint64_t SNRdB_NI_18_FL : 8;
        uint64_t PowerDB_NI_18_FL : 8;
        uint64_t WDoppler10DB_NI_18_FL : 7;
        uint64_t Reserve1_1bit_NI_18_FL : 1;
        uint64_t WRange10DB_NI_18_FL : 8;
        uint64_t CoGDoppler_NI_18_FL : 16;
        uint64_t CoGRange_NI_18_FL : 16;
        uint64_t ValidXBeam_NI_17_FL : 1;
        uint64_t NoInfrastructure_NI_17_FL : 1;
        uint64_t Beam_NI_17_FL : 2;
        uint64_t Azimuth_NI_17_FL__S1 : 4;
        uint64_t Azimuth_NI_17_FL__S0 : 8;
        uint64_t StdAzimuth_NI_17_FL : 8;
        uint64_t SNRdB_NI_17_FL : 8;
        uint64_t PowerDB_NI_17_FL : 8;
        uint64_t WDoppler10DB_NI_17_FL : 7;
        uint64_t Reserve1_1bit_NI_17_FL : 1;
        uint64_t WRange10DB_NI_17_FL : 8;
        uint64_t CoGDoppler_NI_17_FL : 16;
        uint64_t CoGRange_NI_17_FL : 16;
        uint64_t ValidXBeam_NI_16_FL : 1;
        uint64_t NoInfrastructure_NI_16_FL : 1;
        uint64_t Beam_NI_16_FL : 2;
        uint64_t Azimuth_NI_16_FL : 12;
        uint64_t StdAzimuth_NI_16_FL : 8;
        uint64_t SNRdB_NI_16_FL : 8;
        uint64_t PowerDB_NI_16_FL : 8;
        uint64_t WDoppler10DB_NI_16_FL : 7;
        uint64_t Reserve1_1bit_NI_16_FL : 1;
        uint64_t WRange10DB_NI_16_FL : 8;
        uint64_t CoGDoppler_NI_16_FL : 16;
        uint64_t CoGRange_NI_16_FL : 16;
        uint64_t ValidXBeam_NI_15_FL : 1;
        uint64_t NoInfrastructure_NI_15_FL : 1;
        uint64_t Beam_NI_15_FL : 2;
        uint64_t Azimuth_NI_15_FL : 12;
        uint64_t StdAzimuth_NI_15_FL : 8;
        uint64_t SNRdB_NI_15_FL : 8;
        uint64_t PowerDB_NI_15_FL : 8;
        uint64_t WDoppler10DB_NI_15_FL : 7;
        uint64_t Reserve1_1bit_NI_15_FL : 1;
        uint64_t WRange10DB_NI_15_FL : 8;
        uint64_t CoGDoppler_NI_15_FL : 16;
        uint64_t CoGRange_NI_15_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_3_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_3_fl_data_t data;
};

union cansig_mk_non_inf_detection_3_fl__azimuth_ni_17_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_17_FL__S1 : 4;
        uint32_t Azimuth_NI_17_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_4_FL_ID        (0x0206)
#define CANMSG_MK_NON_INF_DETECTION_4_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_4_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_4_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_24_FL : 1;
        uint64_t NoInfrastructure_NI_24_FL : 1;
        uint64_t Beam_NI_24_FL : 2;
        uint64_t Azimuth_NI_24_FL : 12;
        uint64_t StdAzimuth_NI_24_FL : 8;
        uint64_t SNRdB_NI_24_FL : 8;
        uint64_t PowerDB_NI_24_FL : 8;
        uint64_t WDoppler10DB_NI_24_FL : 7;
        uint64_t Reserve1_1bit_NI_24_FL : 1;
        uint64_t WRange10DB_NI_24_FL : 8;
        uint64_t CoGDoppler_NI_24_FL : 16;
        uint64_t CoGRange_NI_24_FL : 16;
        uint64_t ValidXBeam_NI_23_FL : 1;
        uint64_t NoInfrastructure_NI_23_FL : 1;
        uint64_t Beam_NI_23_FL : 2;
        uint64_t Azimuth_NI_23_FL : 12;
        uint64_t StdAzimuth_NI_23_FL : 8;
        uint64_t SNRdB_NI_23_FL : 8;
        uint64_t PowerDB_NI_23_FL : 8;
        uint64_t WDoppler10DB_NI_23_FL : 7;
        uint64_t Reserve1_1bit_NI_23_FL : 1;
        uint64_t WRange10DB_NI_23_FL : 8;
        uint64_t CoGDoppler_NI_23_FL : 16;
        uint64_t CoGRange_NI_23_FL : 16;
        uint64_t ValidXBeam_NI_22_FL : 1;
        uint64_t NoInfrastructure_NI_22_FL : 1;
        uint64_t Beam_NI_22_FL : 2;
        uint64_t Azimuth_NI_22_FL__S1 : 4;
        uint64_t Azimuth_NI_22_FL__S0 : 8;
        uint64_t StdAzimuth_NI_22_FL : 8;
        uint64_t SNRdB_NI_22_FL : 8;
        uint64_t PowerDB_NI_22_FL : 8;
        uint64_t WDoppler10DB_NI_22_FL : 7;
        uint64_t Reserve1_1bit_NI_22_FL : 1;
        uint64_t WRange10DB_NI_22_FL : 8;
        uint64_t CoGDoppler_NI_22_FL : 16;
        uint64_t CoGRange_NI_22_FL : 16;
        uint64_t ValidXBeam_NI_21_FL : 1;
        uint64_t NoInfrastructure_NI_21_FL : 1;
        uint64_t Beam_NI_21_FL : 2;
        uint64_t Azimuth_NI_21_FL : 12;
        uint64_t StdAzimuth_NI_21_FL : 8;
        uint64_t SNRdB_NI_21_FL : 8;
        uint64_t PowerDB_NI_21_FL : 8;
        uint64_t WDoppler10DB_NI_21_FL : 7;
        uint64_t Reserve1_1bit_NI_21_FL : 1;
        uint64_t WRange10DB_NI_21_FL : 8;
        uint64_t CoGDoppler_NI_21_FL : 16;
        uint64_t CoGRange_NI_21_FL : 16;
        uint64_t ValidXBeam_NI_20_FL : 1;
        uint64_t NoInfrastructure_NI_20_FL : 1;
        uint64_t Beam_NI_20_FL : 2;
        uint64_t Azimuth_NI_20_FL : 12;
        uint64_t StdAzimuth_NI_20_FL : 8;
        uint64_t SNRdB_NI_20_FL : 8;
        uint64_t PowerDB_NI_20_FL : 8;
        uint64_t WDoppler10DB_NI_20_FL : 7;
        uint64_t Reserve1_1bit_NI_20_FL : 1;
        uint64_t WRange10DB_NI_20_FL : 8;
        uint64_t CoGDoppler_NI_20_FL : 16;
        uint64_t CoGRange_NI_20_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_4_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_4_fl_data_t data;
};

union cansig_mk_non_inf_detection_4_fl__azimuth_ni_22_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_22_FL__S1 : 4;
        uint32_t Azimuth_NI_22_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_5_FL_ID        (0x0207)
#define CANMSG_MK_NON_INF_DETECTION_5_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_5_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_5_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_29_FL : 1;
        uint64_t NoInfrastructure_NI_29_FL : 1;
        uint64_t Beam_NI_29_FL : 2;
        uint64_t Azimuth_NI_29_FL : 12;
        uint64_t StdAzimuth_NI_29_FL : 8;
        uint64_t SNRdB_NI_29_FL : 8;
        uint64_t PowerDB_NI_29_FL : 8;
        uint64_t WDoppler10DB_NI_29_FL : 7;
        uint64_t Reserve1_1bit_NI_29_FL : 1;
        uint64_t WRange10DB_NI_29_FL : 8;
        uint64_t CoGDoppler_NI_29_FL : 16;
        uint64_t CoGRange_NI_29_FL : 16;
        uint64_t ValidXBeam_NI_28_FL : 1;
        uint64_t NoInfrastructure_NI_28_FL : 1;
        uint64_t Beam_NI_28_FL : 2;
        uint64_t Azimuth_NI_28_FL : 12;
        uint64_t StdAzimuth_NI_28_FL : 8;
        uint64_t SNRdB_NI_28_FL : 8;
        uint64_t PowerDB_NI_28_FL : 8;
        uint64_t WDoppler10DB_NI_28_FL : 7;
        uint64_t Reserve1_1bit_NI_28_FL : 1;
        uint64_t WRange10DB_NI_28_FL : 8;
        uint64_t CoGDoppler_NI_28_FL : 16;
        uint64_t CoGRange_NI_28_FL : 16;
        uint64_t ValidXBeam_NI_27_FL : 1;
        uint64_t NoInfrastructure_NI_27_FL : 1;
        uint64_t Beam_NI_27_FL : 2;
        uint64_t Azimuth_NI_27_FL__S1 : 4;
        uint64_t Azimuth_NI_27_FL__S0 : 8;
        uint64_t StdAzimuth_NI_27_FL : 8;
        uint64_t SNRdB_NI_27_FL : 8;
        uint64_t PowerDB_NI_27_FL : 8;
        uint64_t WDoppler10DB_NI_27_FL : 7;
        uint64_t Reserve1_1bit_NI_27_FL : 1;
        uint64_t WRange10DB_NI_27_FL : 8;
        uint64_t CoGDoppler_NI_27_FL : 16;
        uint64_t CoGRange_NI_27_FL : 16;
        uint64_t ValidXBeam_NI_26_FL : 1;
        uint64_t NoInfrastructure_NI_26_FL : 1;
        uint64_t Beam_NI_26_FL : 2;
        uint64_t Azimuth_NI_26_FL : 12;
        uint64_t StdAzimuth_NI_26_FL : 8;
        uint64_t SNRdB_NI_26_FL : 8;
        uint64_t PowerDB_NI_26_FL : 8;
        uint64_t WDoppler10DB_NI_26_FL : 7;
        uint64_t Reserve1_1bit_NI_26_FL : 1;
        uint64_t WRange10DB_NI_26_FL : 8;
        uint64_t CoGDoppler_NI_26_FL : 16;
        uint64_t CoGRange_NI_26_FL : 16;
        uint64_t ValidXBeam_NI_25_FL : 1;
        uint64_t NoInfrastructure_NI_25_FL : 1;
        uint64_t Beam_NI_25_FL : 2;
        uint64_t Azimuth_NI_25_FL : 12;
        uint64_t StdAzimuth_NI_25_FL : 8;
        uint64_t SNRdB_NI_25_FL : 8;
        uint64_t PowerDB_NI_25_FL : 8;
        uint64_t WDoppler10DB_NI_25_FL : 7;
        uint64_t Reserve1_1bit_NI_25_FL : 1;
        uint64_t WRange10DB_NI_25_FL : 8;
        uint64_t CoGDoppler_NI_25_FL : 16;
        uint64_t CoGRange_NI_25_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_5_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_5_fl_data_t data;
};

union cansig_mk_non_inf_detection_5_fl__azimuth_ni_27_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_27_FL__S1 : 4;
        uint32_t Azimuth_NI_27_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_6_FL_ID        (0x0208)
#define CANMSG_MK_NON_INF_DETECTION_6_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_6_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_6_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_34_FL : 1;
        uint64_t NoInfrastructure_NI_34_FL : 1;
        uint64_t Beam_NI_34_FL : 2;
        uint64_t Azimuth_NI_34_FL : 12;
        uint64_t StdAzimuth_NI_34_FL : 8;
        uint64_t SNRdB_NI_34_FL : 8;
        uint64_t PowerDB_NI_34_FL : 8;
        uint64_t WDoppler10DB_NI_34_FL : 7;
        uint64_t Reserve1_1bit_NI_34_FL : 1;
        uint64_t WRange10DB_NI_34_FL : 8;
        uint64_t CoGDoppler_NI_34_FL : 16;
        uint64_t CoGRange_NI_34_FL : 16;
        uint64_t ValidXBeam_NI_33_FL : 1;
        uint64_t NoInfrastructure_NI_33_FL : 1;
        uint64_t Beam_NI_33_FL : 2;
        uint64_t Azimuth_NI_33_FL : 12;
        uint64_t StdAzimuth_NI_33_FL : 8;
        uint64_t SNRdB_NI_33_FL : 8;
        uint64_t PowerDB_NI_33_FL : 8;
        uint64_t WDoppler10DB_NI_33_FL : 7;
        uint64_t Reserve1_1bit_NI_33_FL : 1;
        uint64_t WRange10DB_NI_33_FL : 8;
        uint64_t CoGDoppler_NI_33_FL : 16;
        uint64_t CoGRange_NI_33_FL : 16;
        uint64_t ValidXBeam_NI_32_FL : 1;
        uint64_t NoInfrastructure_NI_32_FL : 1;
        uint64_t Beam_NI_32_FL : 2;
        uint64_t Azimuth_NI_32_FL__S1 : 4;
        uint64_t Azimuth_NI_32_FL__S0 : 8;
        uint64_t StdAzimuth_NI_32_FL : 8;
        uint64_t SNRdB_NI_32_FL : 8;
        uint64_t PowerDB_NI_32_FL : 8;
        uint64_t WDoppler10DB_NI_32_FL : 7;
        uint64_t Reserve1_1bit_NI_32_FL : 1;
        uint64_t WRange10DB_NI_32_FL : 8;
        uint64_t CoGDoppler_NI_32_FL : 16;
        uint64_t CoGRange_NI_32_FL : 16;
        uint64_t ValidXBeam_NI_31_FL : 1;
        uint64_t NoInfrastructure_NI_31_FL : 1;
        uint64_t Beam_NI_31_FL : 2;
        uint64_t Azimuth_NI_31_FL : 12;
        uint64_t StdAzimuth_NI_31_FL : 8;
        uint64_t SNRdB_NI_31_FL : 8;
        uint64_t PowerDB_NI_31_FL : 8;
        uint64_t WDoppler10DB_NI_31_FL : 7;
        uint64_t Reserve1_1bit_NI_31_FL : 1;
        uint64_t WRange10DB_NI_31_FL : 8;
        uint64_t CoGDoppler_NI_31_FL : 16;
        uint64_t CoGRange_NI_31_FL : 16;
        uint64_t ValidXBeam_NI_30_FL : 1;
        uint64_t NoInfrastructure_NI_30_FL : 1;
        uint64_t Beam_NI_30_FL : 2;
        uint64_t Azimuth_NI_30_FL : 12;
        uint64_t StdAzimuth_NI_30_FL : 8;
        uint64_t SNRdB_NI_30_FL : 8;
        uint64_t PowerDB_NI_30_FL : 8;
        uint64_t WDoppler10DB_NI_30_FL : 7;
        uint64_t Reserve1_1bit_NI_30_FL : 1;
        uint64_t WRange10DB_NI_30_FL : 8;
        uint64_t CoGDoppler_NI_30_FL : 16;
        uint64_t CoGRange_NI_30_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_6_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_6_fl_data_t data;
};

union cansig_mk_non_inf_detection_6_fl__azimuth_ni_32_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_32_FL__S1 : 4;
        uint32_t Azimuth_NI_32_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_7_FL_ID        (0x0209)
#define CANMSG_MK_NON_INF_DETECTION_7_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_7_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_7_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_39_FL : 1;
        uint64_t NoInfrastructure_NI_39_FL : 1;
        uint64_t Beam_NI_39_FL : 2;
        uint64_t Azimuth_NI_39_FL : 12;
        uint64_t StdAzimuth_NI_39_FL : 8;
        uint64_t SNRdB_NI_39_FL : 8;
        uint64_t PowerDB_NI_39_FL : 8;
        uint64_t WDoppler10DB_NI_39_FL : 7;
        uint64_t Reserve1_1bit_NI_39_FL : 1;
        uint64_t WRange10DB_NI_39_FL : 8;
        uint64_t CoGDoppler_NI_39_FL : 16;
        uint64_t CoGRange_NI_39_FL : 16;
        uint64_t ValidXBeam_NI_38_FL : 1;
        uint64_t NoInfrastructure_NI_38_FL : 1;
        uint64_t Beam_NI_38_FL : 2;
        uint64_t Azimuth_NI_38_FL : 12;
        uint64_t StdAzimuth_NI_38_FL : 8;
        uint64_t SNRdB_NI_38_FL : 8;
        uint64_t PowerDB_NI_38_FL : 8;
        uint64_t WDoppler10DB_NI_38_FL : 7;
        uint64_t Reserve1_1bit_NI_38_FL : 1;
        uint64_t WRange10DB_NI_38_FL : 8;
        uint64_t CoGDoppler_NI_38_FL : 16;
        uint64_t CoGRange_NI_38_FL : 16;
        uint64_t ValidXBeam_NI_37_FL : 1;
        uint64_t NoInfrastructure_NI_37_FL : 1;
        uint64_t Beam_NI_37_FL : 2;
        uint64_t Azimuth_NI_37_FL__S1 : 4;
        uint64_t Azimuth_NI_37_FL__S0 : 8;
        uint64_t StdAzimuth_NI_37_FL : 8;
        uint64_t SNRdB_NI_37_FL : 8;
        uint64_t PowerDB_NI_37_FL : 8;
        uint64_t WDoppler10DB_NI_37_FL : 7;
        uint64_t Reserve1_1bit_NI_37_FL : 1;
        uint64_t WRange10DB_NI_37_FL : 8;
        uint64_t CoGDoppler_NI_37_FL : 16;
        uint64_t CoGRange_NI_37_FL : 16;
        uint64_t ValidXBeam_NI_36_FL : 1;
        uint64_t NoInfrastructure_NI_36_FL : 1;
        uint64_t Beam_NI_36_FL : 2;
        uint64_t Azimuth_NI_36_FL : 12;
        uint64_t StdAzimuth_NI_36_FL : 8;
        uint64_t SNRdB_NI_36_FL : 8;
        uint64_t PowerDB_NI_36_FL : 8;
        uint64_t WDoppler10DB_NI_36_FL : 7;
        uint64_t Reserve1_1bit_NI_36_FL : 1;
        uint64_t WRange10DB_NI_36_FL : 8;
        uint64_t CoGDoppler_NI_36_FL : 16;
        uint64_t CoGRange_NI_36_FL : 16;
        uint64_t ValidXBeam_NI_35_FL : 1;
        uint64_t NoInfrastructure_NI_35_FL : 1;
        uint64_t Beam_NI_35_FL : 2;
        uint64_t Azimuth_NI_35_FL : 12;
        uint64_t StdAzimuth_NI_35_FL : 8;
        uint64_t SNRdB_NI_35_FL : 8;
        uint64_t PowerDB_NI_35_FL : 8;
        uint64_t WDoppler10DB_NI_35_FL : 7;
        uint64_t Reserve1_1bit_NI_35_FL : 1;
        uint64_t WRange10DB_NI_35_FL : 8;
        uint64_t CoGDoppler_NI_35_FL : 16;
        uint64_t CoGRange_NI_35_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_7_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_7_fl_data_t data;
};

union cansig_mk_non_inf_detection_7_fl__azimuth_ni_37_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_37_FL__S1 : 4;
        uint32_t Azimuth_NI_37_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_8_FL_ID        (0x020A)
#define CANMSG_MK_NON_INF_DETECTION_8_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_8_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_8_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_44_FL : 1;
        uint64_t NoInfrastructure_NI_44_FL : 1;
        uint64_t Beam_NI_44_FL : 2;
        uint64_t Azimuth_NI_44_FL : 12;
        uint64_t StdAzimuth_NI_44_FL : 8;
        uint64_t SNRdB_NI_44_FL : 8;
        uint64_t PowerDB_NI_44_FL : 8;
        uint64_t WDoppler10DB_NI_44_FL : 7;
        uint64_t Reserve1_1bit_NI_44_FL : 1;
        uint64_t WRange10DB_NI_44_FL : 8;
        uint64_t CoGDoppler_NI_44_FL : 16;
        uint64_t CoGRange_NI_44_FL : 16;
        uint64_t ValidXBeam_NI_43_FL : 1;
        uint64_t NoInfrastructure_NI_43_FL : 1;
        uint64_t Beam_NI_43_FL : 2;
        uint64_t Azimuth_NI_43_FL : 12;
        uint64_t StdAzimuth_NI_43_FL : 8;
        uint64_t SNRdB_NI_43_FL : 8;
        uint64_t PowerDB_NI_43_FL : 8;
        uint64_t WDoppler10DB_NI_43_FL : 7;
        uint64_t Reserve1_1bit_NI_43_FL : 1;
        uint64_t WRange10DB_NI_43_FL : 8;
        uint64_t CoGDoppler_NI_43_FL : 16;
        uint64_t CoGRange_NI_43_FL : 16;
        uint64_t ValidXBeam_NI_42_FL : 1;
        uint64_t NoInfrastructure_NI_42_FL : 1;
        uint64_t Beam_NI_42_FL : 2;
        uint64_t Azimuth_NI_42_FL__S1 : 4;
        uint64_t Azimuth_NI_42_FL__S0 : 8;
        uint64_t StdAzimuth_NI_42_FL : 8;
        uint64_t SNRdB_NI_42_FL : 8;
        uint64_t PowerDB_NI_42_FL : 8;
        uint64_t WDoppler10DB_NI_42_FL : 7;
        uint64_t Reserve1_1bit_NI_42_FL : 1;
        uint64_t WRange10DB_NI_42_FL : 8;
        uint64_t CoGDoppler_NI_42_FL : 16;
        uint64_t CoGRange_NI_42_FL : 16;
        uint64_t ValidXBeam_NI_41_FL : 1;
        uint64_t NoInfrastructure_NI_41_FL : 1;
        uint64_t Beam_NI_41_FL : 2;
        uint64_t Azimuth_NI_41_FL : 12;
        uint64_t StdAzimuth_NI_41_FL : 8;
        uint64_t SNRdB_NI_41_FL : 8;
        uint64_t PowerDB_NI_41_FL : 8;
        uint64_t WDoppler10DB_NI_41_FL : 7;
        uint64_t Reserve1_1bit_NI_41_FL : 1;
        uint64_t WRange10DB_NI_41_FL : 8;
        uint64_t CoGDoppler_NI_41_FL : 16;
        uint64_t CoGRange_NI_41_FL : 16;
        uint64_t ValidXBeam_NI_40_FL : 1;
        uint64_t NoInfrastructure_NI_40_FL : 1;
        uint64_t Beam_NI_40_FL : 2;
        uint64_t Azimuth_NI_40_FL : 12;
        uint64_t StdAzimuth_NI_40_FL : 8;
        uint64_t SNRdB_NI_40_FL : 8;
        uint64_t PowerDB_NI_40_FL : 8;
        uint64_t WDoppler10DB_NI_40_FL : 7;
        uint64_t Reserve1_1bit_NI_40_FL : 1;
        uint64_t WRange10DB_NI_40_FL : 8;
        uint64_t CoGDoppler_NI_40_FL : 16;
        uint64_t CoGRange_NI_40_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_8_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_8_fl_data_t data;
};

union cansig_mk_non_inf_detection_8_fl__azimuth_ni_42_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_42_FL__S1 : 4;
        uint32_t Azimuth_NI_42_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_9_FL_ID        (0x020B)
#define CANMSG_MK_NON_INF_DETECTION_9_FL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_9_FL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_9_fl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_49_FL : 1;
        uint64_t NoInfrastructure_NI_49_FL : 1;
        uint64_t Beam_NI_49_FL : 2;
        uint64_t Azimuth_NI_49_FL : 12;
        uint64_t StdAzimuth_NI_49_FL : 8;
        uint64_t SNRdB_NI_49_FL : 8;
        uint64_t PowerDB_NI_49_FL : 8;
        uint64_t WDoppler10DB_NI_49_FL : 7;
        uint64_t Reserve1_1bit_NI_49_FL : 1;
        uint64_t WRange10DB_NI_49_FL : 8;
        uint64_t CoGDoppler_NI_49_FL : 16;
        uint64_t CoGRange_NI_49_FL : 16;
        uint64_t ValidXBeam_NI_48_FL : 1;
        uint64_t NoInfrastructure_NI_48_FL : 1;
        uint64_t Beam_NI_48_FL : 2;
        uint64_t Azimuth_NI_48_FL : 12;
        uint64_t StdAzimuth_NI_48_FL : 8;
        uint64_t SNRdB_NI_48_FL : 8;
        uint64_t PowerDB_NI_48_FL : 8;
        uint64_t WDoppler10DB_NI_48_FL : 7;
        uint64_t Reserve1_1bit_NI_48_FL : 1;
        uint64_t WRange10DB_NI_48_FL : 8;
        uint64_t CoGDoppler_NI_48_FL : 16;
        uint64_t CoGRange_NI_48_FL : 16;
        uint64_t ValidXBeam_NI_47_FL : 1;
        uint64_t NoInfrastructure_NI_47_FL : 1;
        uint64_t Beam_NI_47_FL : 2;
        uint64_t Azimuth_NI_47_FL__S1 : 4;
        uint64_t Azimuth_NI_47_FL__S0 : 8;
        uint64_t StdAzimuth_NI_47_FL : 8;
        uint64_t SNRdB_NI_47_FL : 8;
        uint64_t PowerDB_NI_47_FL : 8;
        uint64_t WDoppler10DB_NI_47_FL : 7;
        uint64_t Reserve1_1bit_NI_47_FL : 1;
        uint64_t WRange10DB_NI_47_FL : 8;
        uint64_t CoGDoppler_NI_47_FL : 16;
        uint64_t CoGRange_NI_47_FL : 16;
        uint64_t ValidXBeam_NI_46_FL : 1;
        uint64_t NoInfrastructure_NI_46_FL : 1;
        uint64_t Beam_NI_46_FL : 2;
        uint64_t Azimuth_NI_46_FL : 12;
        uint64_t StdAzimuth_NI_46_FL : 8;
        uint64_t SNRdB_NI_46_FL : 8;
        uint64_t PowerDB_NI_46_FL : 8;
        uint64_t WDoppler10DB_NI_46_FL : 7;
        uint64_t Reserve1_1bit_NI_46_FL : 1;
        uint64_t WRange10DB_NI_46_FL : 8;
        uint64_t CoGDoppler_NI_46_FL : 16;
        uint64_t CoGRange_NI_46_FL : 16;
        uint64_t ValidXBeam_NI_45_FL : 1;
        uint64_t NoInfrastructure_NI_45_FL : 1;
        uint64_t Beam_NI_45_FL : 2;
        uint64_t Azimuth_NI_45_FL : 12;
        uint64_t StdAzimuth_NI_45_FL : 8;
        uint64_t SNRdB_NI_45_FL : 8;
        uint64_t PowerDB_NI_45_FL : 8;
        uint64_t WDoppler10DB_NI_45_FL : 7;
        uint64_t Reserve1_1bit_NI_45_FL : 1;
        uint64_t WRange10DB_NI_45_FL : 8;
        uint64_t CoGDoppler_NI_45_FL : 16;
        uint64_t CoGRange_NI_45_FL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_9_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_9_fl_data_t data;
};

union cansig_mk_non_inf_detection_9_fl__azimuth_ni_47_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_47_FL__S1 : 4;
        uint32_t Azimuth_NI_47_FL__S0 : 8;
    } fields;
};

#define CANMSG_MK_TARGET_DETECTION_HEADER_FL_ID    (0x0201)
#define CANMSG_MK_TARGET_DETECTION_HEADER_FL_DLC   (48)
#define CANMSG_MK_TARGET_DETECTION_HEADER_FL_MIN_DLC (48)
union canmsg_mk_target_detection_header_fl_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t InterferenceExceeded_FL : 8;
        uint64_t PAD2 : 8;
        uint64_t numFreespaceDetections_FL : 2;
        uint64_t DetectionListVersion_FL : 6;
        uint64_t numInfrastructureDetected_FL : 8;
        uint64_t numNonInfraDetected_FL : 8;
        uint64_t numCmplxValPerDetectionBeam3_FL : 8;
        uint64_t numCmplxValPerDetectionBeam2_FL : 8;
        uint64_t numCmplxValPerDetectionBeam1_FL : 8;
        uint64_t UnambiguousVelMeas3_FL : 8;
        uint64_t UnambiguousVelMeas2_FL : 8;
        uint64_t UnambiguousVelMeas1_FL : 8;
        uint64_t FC1MHz3_FL : 16;
        uint64_t FC1MHz2_FL : 16;
        uint64_t FC1MHz1_FL : 16;
        uint64_t HostYawEst_FL : 16;
        uint64_t HostAccelLatEst_FL : 16;
        uint64_t HostAccelLongEst_FL : 16;
        uint64_t HostVelEst_FL : 16;
        uint64_t BW100KHz3_FL : 16;
        uint64_t BW100KHz2_FL : 16;
        uint64_t BW100KHz1_FL : 16;
        uint64_t TimeStamp_FL : 32;
        uint64_t CycleNumber_FL : 32;
    } signals;
};

struct canmsg_mk_target_detection_header_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_target_detection_header_fl_data_t data;
};

//=====================================================================================//


#include "parser_typedef.h"
#pragma once

#if 0
//--------------//
extern void CAN_SetCanParserLockHandler(fpParserLockHandler_t func);
extern void CAN_SetCanParserUnlockHandler(fpParserUnlockHandler_t func);
extern void CAN_SetMsgCounterCalcHandler(fpMsgCounterCalcHandler_t func);
extern void CAN_SetMsgChecksumCalcHandler(fpMsgChecksumCalcHandler_t func);
extern void CAN_SetMsgChecksumVerityHandler(fpMsgChecksumVerityHandler_t func);
extern void CAN_SetMsgChangeHandler(fpMsgChangeHandler_t func);
extern void CAN_SetMsgChecksumErrorHandler(fpMsgChecksumErrorHandler_t func);
extern void CAN_SetMsgCounterErrorHandler(fpMsgCounterErrorHandler_t func);
extern void CAN_SetMsgTimeoutHandler(fpMsgTimeoutHandler_t func);
extern void CAN_SetMsgDlcHandler(fpMsgDlcHandler_t func);
extern void CAN_SetMsgOutRangeHandler(fpMsgOutRangeHandler_t func);
extern void CAN_SetSigChangeHandler(fpSigChangeHandler_t func);
extern void CAN_SetSigOnWriteHandler(fpSigOnWriteHandler_t func);
extern void CAN_SetSignalChangedHook(fpSignalChangedHook_t func);
extern void CAN_SetSignalSetCallBack(fpSignalTriggerCallBack_t func);
extern void CAN_SetSignalGetCallBack(fpSignalTriggerCallBack_t func);
//=====================================================================================//
extern struct veh_message *CAN_RxMessageList[27];

extern struct veh_message *CAN_TxMessageList[1];

extern struct veh_signal* CAN_ALL_Signal_Array[];
#endif
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Beam_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Beam_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Beam_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Beam_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Beam_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_0_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_1_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_2_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_3_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_4_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Beam_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Beam_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Beam_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Beam_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Beam_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_50_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_51_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_52_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_53_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_54_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Beam_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Beam_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Beam_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Beam_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Beam_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_55_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_56_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_57_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_58_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_59_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Beam_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Beam_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Beam_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Beam_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Beam_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_60_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_61_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_62_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_63_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_64_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Beam_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Beam_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Beam_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Beam_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Beam_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_65_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_66_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_67_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_68_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_69_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Beam_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Beam_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Beam_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Beam_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Beam_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_70_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_71_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_72_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_73_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_74_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Beam_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Beam_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Beam_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Beam_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Beam_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_75_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_76_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_77_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_78_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_79_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Beam_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Beam_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Beam_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Beam_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Beam_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_80_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_81_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_82_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_83_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_84_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Beam_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Beam_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Beam_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Beam_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Beam_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_85_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_86_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_87_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_88_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_89_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Beam_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Beam_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Beam_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Beam_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Beam_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_90_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_91_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_92_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_93_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_94_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Beam_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Beam_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Beam_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Beam_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Beam_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_95_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_96_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_97_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_98_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_99_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Beam_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Beam_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Beam_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Beam_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Beam_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_5_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_6_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_7_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_8_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_9_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Beam_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Beam_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Beam_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Beam_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Beam_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_100_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_101_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_102_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_103_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_104_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Beam_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Beam_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Beam_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Beam_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Beam_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_105_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_106_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_107_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_108_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_109_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Beam_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Beam_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Beam_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Beam_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Beam_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_110_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_111_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_112_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_113_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_114_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Beam_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Beam_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Beam_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Beam_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Beam_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_115_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_116_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_117_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_118_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_119_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Beam_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Beam_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Beam_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Beam_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Beam_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_120_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_121_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_122_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_123_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_124_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Beam_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Beam_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Beam_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Beam_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Beam_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_10_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_11_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_12_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_13_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_14_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Beam_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Beam_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Beam_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Beam_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Beam_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_15_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_16_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_17_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_18_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_19_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Beam_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Beam_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Beam_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Beam_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Beam_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_20_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_21_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_22_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_23_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_24_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Beam_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Beam_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Beam_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Beam_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Beam_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_25_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_26_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_27_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_28_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_29_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Beam_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Beam_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Beam_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Beam_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Beam_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_30_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_31_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_32_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_33_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_34_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Beam_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Beam_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Beam_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Beam_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Beam_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_35_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_36_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_37_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_38_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_39_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Beam_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Beam_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Beam_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Beam_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Beam_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_40_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_41_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_42_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_43_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_44_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Beam_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Beam_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Beam_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Beam_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Beam_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_45_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_46_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_47_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_48_FL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_49_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__BW100KHz1_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__BW100KHz2_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__BW100KHz3_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__CycleNumber_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__DetectionListVersion_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__FC1MHz1_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__FC1MHz2_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__FC1MHz3_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__HostAccelLatEst_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__HostAccelLongEst_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__HostVelEst_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__HostYawEst_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__InterferenceExceeded_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__TimeStamp_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__UnambiguousVelMeas1_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__UnambiguousVelMeas2_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__UnambiguousVelMeas3_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__numCmplxValPerDetectionBeam1_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__numCmplxValPerDetectionBeam2_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__numCmplxValPerDetectionBeam3_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__numFreespaceDetections_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__numInfrastructureDetected_FL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_FL__numNonInfraDetected_FL_g;

extern void CAN_SetRawMessage_MK_TARGET_DETECTION_HEADER_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_0_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_1_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_2_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_3_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_4_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_5_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_6_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_7_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_8_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_9_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_10_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_11_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_12_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_13_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_14_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_15_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_16_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_17_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_18_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_19_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_20_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_21_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_22_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_23_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_24_FL(uint8_t *values, uint32_t length);

extern void CAN_ResetMessage_MK_TARGET_DETECTION_HEADER_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_0_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_1_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_2_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_3_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_4_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_5_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_6_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_7_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_8_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_9_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_10_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_11_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_12_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_13_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_14_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_15_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_16_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_17_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_18_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_19_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_20_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_21_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_22_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_23_FL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_24_FL(enum reset_flg flags);

#if 0
extern void CAN_MessageElapseTime(int bus_id, int time_ms, int restart);
#endif


extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_0_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_10_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_11_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_12_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_13_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_14_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_15_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_16_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_17_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_18_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_19_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_1_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_20_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_21_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_22_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_23_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_24_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_2_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_3_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_4_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_5_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_6_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_7_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_8_FL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_9_FL(void);
extern void CAN_Message_RDLock_MK_TARGET_DETECTION_HEADER_FL(void);

extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_0_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_10_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_11_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_12_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_13_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_14_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_15_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_16_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_17_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_18_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_19_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_1_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_20_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_21_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_22_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_23_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_24_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_2_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_3_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_4_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_5_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_6_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_7_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_8_FL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_9_FL(void);
extern void CAN_Message_WRLock_MK_TARGET_DETECTION_HEADER_FL(void);

extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_0_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_10_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_11_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_12_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_13_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_14_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_15_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_16_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_17_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_18_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_19_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_1_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_20_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_21_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_22_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_23_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_24_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_2_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_3_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_4_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_5_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_6_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_7_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_8_FL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_9_FL(void);
extern void CAN_Message_Unlock_MK_TARGET_DETECTION_HEADER_FL(void);



extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Azimuth_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Beam_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Beam_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Beam_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Beam_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Beam_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGDoppler_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__CoGRange_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__NoInfrastructure_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__PowerDB_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__Reserve1_1bit_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__SNRdB_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__StdAzimuth_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__ValidXBeam_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WDoppler10DB_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_0_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_FL__WRange10DB_NI_4_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Azimuth_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Beam_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Beam_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Beam_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Beam_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Beam_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGDoppler_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__CoGRange_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__NoInfrastructure_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__PowerDB_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__Reserve1_1bit_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__SNRdB_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__StdAzimuth_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__ValidXBeam_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WDoppler10DB_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_50_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_51_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_52_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_53_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_FL__WRange10DB_NI_54_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Azimuth_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Beam_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Beam_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Beam_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Beam_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Beam_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGDoppler_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__CoGRange_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__NoInfrastructure_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__PowerDB_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__Reserve1_1bit_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__SNRdB_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__StdAzimuth_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__ValidXBeam_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WDoppler10DB_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_55_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_56_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_57_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_58_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_FL__WRange10DB_NI_59_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Azimuth_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Beam_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Beam_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Beam_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Beam_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Beam_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGDoppler_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__CoGRange_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__NoInfrastructure_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__PowerDB_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__Reserve1_1bit_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__SNRdB_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__StdAzimuth_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__ValidXBeam_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WDoppler10DB_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_60_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_61_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_62_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_63_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_FL__WRange10DB_NI_64_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Azimuth_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Beam_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Beam_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Beam_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Beam_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Beam_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGDoppler_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__CoGRange_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__NoInfrastructure_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__PowerDB_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__Reserve1_1bit_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__SNRdB_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__StdAzimuth_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__ValidXBeam_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WDoppler10DB_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_65_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_66_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_67_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_68_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_FL__WRange10DB_NI_69_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Azimuth_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Beam_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Beam_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Beam_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Beam_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Beam_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGDoppler_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__CoGRange_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__NoInfrastructure_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__PowerDB_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__Reserve1_1bit_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__SNRdB_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__StdAzimuth_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__ValidXBeam_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WDoppler10DB_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_70_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_71_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_72_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_73_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_FL__WRange10DB_NI_74_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Azimuth_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Beam_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Beam_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Beam_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Beam_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Beam_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGDoppler_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__CoGRange_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__NoInfrastructure_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__PowerDB_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__Reserve1_1bit_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__SNRdB_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__StdAzimuth_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__ValidXBeam_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WDoppler10DB_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_75_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_76_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_77_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_78_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_FL__WRange10DB_NI_79_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Azimuth_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Beam_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Beam_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Beam_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Beam_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Beam_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGDoppler_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__CoGRange_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__NoInfrastructure_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__PowerDB_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__Reserve1_1bit_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__SNRdB_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__StdAzimuth_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__ValidXBeam_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WDoppler10DB_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_80_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_81_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_82_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_83_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_FL__WRange10DB_NI_84_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Azimuth_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Beam_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Beam_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Beam_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Beam_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Beam_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGDoppler_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__CoGRange_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__NoInfrastructure_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__PowerDB_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__Reserve1_1bit_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__SNRdB_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__StdAzimuth_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__ValidXBeam_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WDoppler10DB_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_85_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_86_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_87_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_88_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_FL__WRange10DB_NI_89_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Azimuth_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Beam_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Beam_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Beam_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Beam_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Beam_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGDoppler_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__CoGRange_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__NoInfrastructure_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__PowerDB_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__Reserve1_1bit_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__SNRdB_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__StdAzimuth_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__ValidXBeam_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WDoppler10DB_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_90_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_91_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_92_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_93_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_FL__WRange10DB_NI_94_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Azimuth_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Beam_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Beam_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Beam_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Beam_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Beam_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGDoppler_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__CoGRange_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__NoInfrastructure_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__PowerDB_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__Reserve1_1bit_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__SNRdB_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__StdAzimuth_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__ValidXBeam_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WDoppler10DB_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_95_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_96_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_97_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_98_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_FL__WRange10DB_NI_99_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Azimuth_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Beam_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Beam_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Beam_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Beam_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Beam_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGDoppler_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__CoGRange_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__NoInfrastructure_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__PowerDB_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__Reserve1_1bit_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__SNRdB_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__StdAzimuth_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__ValidXBeam_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WDoppler10DB_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_5_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_6_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_7_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_8_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_FL__WRange10DB_NI_9_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Azimuth_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Beam_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Beam_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Beam_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Beam_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Beam_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGDoppler_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__CoGRange_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__NoInfrastructure_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__PowerDB_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__Reserve1_1bit_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__SNRdB_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__StdAzimuth_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__ValidXBeam_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WDoppler10DB_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_100_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_101_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_102_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_103_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_FL__WRange10DB_NI_104_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Azimuth_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Beam_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Beam_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Beam_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Beam_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Beam_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGDoppler_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__CoGRange_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__NoInfrastructure_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__PowerDB_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__Reserve1_1bit_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__SNRdB_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__StdAzimuth_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__ValidXBeam_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WDoppler10DB_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_105_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_106_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_107_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_108_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_FL__WRange10DB_NI_109_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Azimuth_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Beam_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Beam_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Beam_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Beam_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Beam_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGDoppler_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__CoGRange_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__NoInfrastructure_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__PowerDB_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__Reserve1_1bit_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__SNRdB_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__StdAzimuth_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__ValidXBeam_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WDoppler10DB_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_110_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_111_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_112_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_113_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_FL__WRange10DB_NI_114_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Azimuth_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Beam_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Beam_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Beam_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Beam_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Beam_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGDoppler_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__CoGRange_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__NoInfrastructure_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__PowerDB_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__Reserve1_1bit_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__SNRdB_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__StdAzimuth_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__ValidXBeam_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WDoppler10DB_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_115_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_116_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_117_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_118_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_FL__WRange10DB_NI_119_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Azimuth_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Beam_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Beam_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Beam_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Beam_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Beam_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGDoppler_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__CoGRange_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__NoInfrastructure_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__PowerDB_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__Reserve1_1bit_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__SNRdB_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__StdAzimuth_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__ValidXBeam_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WDoppler10DB_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_120_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_121_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_122_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_123_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_FL__WRange10DB_NI_124_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Azimuth_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Beam_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Beam_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Beam_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Beam_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Beam_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGDoppler_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__CoGRange_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__NoInfrastructure_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__PowerDB_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__Reserve1_1bit_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__SNRdB_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__StdAzimuth_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__ValidXBeam_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WDoppler10DB_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_10_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_11_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_12_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_13_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_FL__WRange10DB_NI_14_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Azimuth_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Beam_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Beam_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Beam_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Beam_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Beam_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGDoppler_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__CoGRange_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__NoInfrastructure_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__PowerDB_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__Reserve1_1bit_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__SNRdB_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__StdAzimuth_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__ValidXBeam_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WDoppler10DB_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_15_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_16_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_17_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_18_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_FL__WRange10DB_NI_19_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Azimuth_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Beam_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Beam_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Beam_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Beam_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Beam_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGDoppler_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__CoGRange_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__NoInfrastructure_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__PowerDB_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__Reserve1_1bit_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__SNRdB_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__StdAzimuth_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__ValidXBeam_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WDoppler10DB_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_20_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_21_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_22_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_23_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_FL__WRange10DB_NI_24_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Azimuth_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Beam_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Beam_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Beam_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Beam_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Beam_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGDoppler_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__CoGRange_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__NoInfrastructure_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__PowerDB_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__Reserve1_1bit_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__SNRdB_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__StdAzimuth_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__ValidXBeam_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WDoppler10DB_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_25_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_26_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_27_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_28_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_FL__WRange10DB_NI_29_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Azimuth_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Beam_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Beam_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Beam_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Beam_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Beam_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGDoppler_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__CoGRange_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__NoInfrastructure_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__PowerDB_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__Reserve1_1bit_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__SNRdB_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__StdAzimuth_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__ValidXBeam_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WDoppler10DB_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_30_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_31_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_32_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_33_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_FL__WRange10DB_NI_34_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Azimuth_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Beam_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Beam_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Beam_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Beam_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Beam_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGDoppler_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__CoGRange_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__NoInfrastructure_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__PowerDB_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__Reserve1_1bit_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__SNRdB_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__StdAzimuth_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__ValidXBeam_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WDoppler10DB_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_35_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_36_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_37_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_38_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_FL__WRange10DB_NI_39_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Azimuth_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Beam_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Beam_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Beam_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Beam_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Beam_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGDoppler_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__CoGRange_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__NoInfrastructure_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__PowerDB_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__Reserve1_1bit_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__SNRdB_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__StdAzimuth_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__ValidXBeam_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WDoppler10DB_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_40_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_41_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_42_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_43_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_FL__WRange10DB_NI_44_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Azimuth_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Beam_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Beam_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Beam_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Beam_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Beam_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGDoppler_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__CoGRange_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__NoInfrastructure_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__PowerDB_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__Reserve1_1bit_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__SNRdB_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__StdAzimuth_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__ValidXBeam_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WDoppler10DB_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_45_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_46_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_47_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_48_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_FL__WRange10DB_NI_49_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__BW100KHz1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__BW100KHz2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__BW100KHz3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__CycleNumber_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__DetectionListVersion_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__FC1MHz1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__FC1MHz2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__FC1MHz3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__HostAccelLatEst_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__HostAccelLongEst_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__HostVelEst_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__HostYawEst_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__InterferenceExceeded_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__TimeStamp_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__UnambiguousVelMeas1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__UnambiguousVelMeas2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__UnambiguousVelMeas3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__numCmplxValPerDetectionBeam1_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__numCmplxValPerDetectionBeam2_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__numCmplxValPerDetectionBeam3_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__numFreespaceDetections_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__numInfrastructureDetected_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_FL__numNonInfraDetected_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);

extern void MK_FL_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
#if 0
extern void CAN_PARSER_Init(void);
extern void CAN_PARSER_MSG_Init(struct veh_message *p_message_list[]);
#endif

//=====================================================================================//



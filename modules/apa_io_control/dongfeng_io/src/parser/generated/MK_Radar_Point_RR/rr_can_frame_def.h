#include "parser_typedef.h"
#pragma once
//=====================================================================================//
#define CANMSG_MK_NON_INF_DETECTION_0_RR_ID        (0x0182)
#define CANMSG_MK_NON_INF_DETECTION_0_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_0_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_0_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_4_RR : 1;
        uint64_t NoInfrastructure_NI_4_RR : 1;
        uint64_t Beam_NI_4_RR : 2;
        uint64_t Azimuth_NI_4_RR : 12;
        uint64_t StdAzimuth_NI_4_RR : 8;
        uint64_t SNRdB_NI_4_RR : 8;
        uint64_t PowerDB_NI_4_RR : 8;
        uint64_t WDoppler10DB_NI_4_RR : 7;
        uint64_t Reserve1_1bit_NI_4_RR : 1;
        uint64_t WRange10DB_NI_4_RR : 8;
        uint64_t CoGDoppler_NI_4_RR : 16;
        uint64_t CoGRange_NI_4_RR : 16;
        uint64_t ValidXBeam_NI_3_RR : 1;
        uint64_t NoInfrastructure_NI_3_RR : 1;
        uint64_t Beam_NI_3_RR : 2;
        uint64_t Azimuth_NI_3_RR : 12;
        uint64_t StdAzimuth_NI_3_RR : 8;
        uint64_t SNRdB_NI_3_RR : 8;
        uint64_t PowerDB_NI_3_RR : 8;
        uint64_t WDoppler10DB_NI_3_RR : 7;
        uint64_t Reserve1_1bit_NI_3_RR : 1;
        uint64_t WRange10DB_NI_3_RR : 8;
        uint64_t CoGDoppler_NI_3_RR : 16;
        uint64_t CoGRange_NI_3_RR : 16;
        uint64_t ValidXBeam_NI_2_RR : 1;
        uint64_t NoInfrastructure_NI_2_RR : 1;
        uint64_t Beam_NI_2_RR : 2;
        uint64_t Azimuth_NI_2_RR__S1 : 4;
        uint64_t Azimuth_NI_2_RR__S0 : 8;
        uint64_t StdAzimuth_NI_2_RR : 8;
        uint64_t SNRdB_NI_2_RR : 8;
        uint64_t PowerDB_NI_2_RR : 8;
        uint64_t WDoppler10DB_NI_2_RR : 7;
        uint64_t Reserve1_1bit_NI_2_RR : 1;
        uint64_t WRange10DB_NI_2_RR : 8;
        uint64_t CoGDoppler_NI_2_RR : 16;
        uint64_t CoGRange_NI_2_RR : 16;
        uint64_t ValidXBeam_NI_1_RR : 1;
        uint64_t NoInfrastructure_NI_1_RR : 1;
        uint64_t Beam_NI_1_RR : 2;
        uint64_t Azimuth_NI_1_RR : 12;
        uint64_t StdAzimuth_NI_1_RR : 8;
        uint64_t SNRdB_NI_1_RR : 8;
        uint64_t PowerDB_NI_1_RR : 8;
        uint64_t WDoppler10DB_NI_1_RR : 7;
        uint64_t Reserve1_1bit_NI_1_RR : 1;
        uint64_t WRange10DB_NI_1_RR : 8;
        uint64_t CoGDoppler_NI_1_RR : 16;
        uint64_t CoGRange_NI_1_RR : 16;
        uint64_t ValidXBeam_NI_0_RR : 1;
        uint64_t NoInfrastructure_NI_0_RR : 1;
        uint64_t Beam_NI_0_RR : 2;
        uint64_t Azimuth_NI_0_RR : 12;
        uint64_t StdAzimuth_NI_0_RR : 8;
        uint64_t SNRdB_NI_0_RR : 8;
        uint64_t PowerDB_NI_0_RR : 8;
        uint64_t WDoppler10DB_NI_0_RR : 7;
        uint64_t Reserve1_1bit_NI_0_RR : 1;
        uint64_t WRange10DB_NI_0_RR : 8;
        uint64_t CoGDoppler_NI_0_RR : 16;
        uint64_t CoGRange_NI_0_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_0_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_0_rr_data_t data;
};

union cansig_mk_non_inf_detection_0_rr__azimuth_ni_2_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_2_RR__S1 : 4;
        uint32_t Azimuth_NI_2_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_10_RR_ID       (0x018C)
#define CANMSG_MK_NON_INF_DETECTION_10_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_10_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_10_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_54_RR : 1;
        uint64_t NoInfrastructure_NI_54_RR : 1;
        uint64_t Beam_NI_54_RR : 2;
        uint64_t Azimuth_NI_54_RR : 12;
        uint64_t StdAzimuth_NI_54_RR : 8;
        uint64_t SNRdB_NI_54_RR : 8;
        uint64_t PowerDB_NI_54_RR : 8;
        uint64_t WDoppler10DB_NI_54_RR : 7;
        uint64_t Reserve1_1bit_NI_54_RR : 1;
        uint64_t WRange10DB_NI_54_RR : 8;
        uint64_t CoGDoppler_NI_54_RR : 16;
        uint64_t CoGRange_NI_54_RR : 16;
        uint64_t ValidXBeam_NI_53_RR : 1;
        uint64_t NoInfrastructure_NI_53_RR : 1;
        uint64_t Beam_NI_53_RR : 2;
        uint64_t Azimuth_NI_53_RR : 12;
        uint64_t StdAzimuth_NI_53_RR : 8;
        uint64_t SNRdB_NI_53_RR : 8;
        uint64_t PowerDB_NI_53_RR : 8;
        uint64_t WDoppler10DB_NI_53_RR : 7;
        uint64_t Reserve1_1bit_NI_53_RR : 1;
        uint64_t WRange10DB_NI_53_RR : 8;
        uint64_t CoGDoppler_NI_53_RR : 16;
        uint64_t CoGRange_NI_53_RR : 16;
        uint64_t ValidXBeam_NI_52_RR : 1;
        uint64_t NoInfrastructure_NI_52_RR : 1;
        uint64_t Beam_NI_52_RR : 2;
        uint64_t Azimuth_NI_52_RR__S1 : 4;
        uint64_t Azimuth_NI_52_RR__S0 : 8;
        uint64_t StdAzimuth_NI_52_RR : 8;
        uint64_t SNRdB_NI_52_RR : 8;
        uint64_t PowerDB_NI_52_RR : 8;
        uint64_t WDoppler10DB_NI_52_RR : 7;
        uint64_t Reserve1_1bit_NI_52_RR : 1;
        uint64_t WRange10DB_NI_52_RR : 8;
        uint64_t CoGDoppler_NI_52_RR : 16;
        uint64_t CoGRange_NI_52_RR : 16;
        uint64_t ValidXBeam_NI_51_RR : 1;
        uint64_t NoInfrastructure_NI_51_RR : 1;
        uint64_t Beam_NI_51_RR : 2;
        uint64_t Azimuth_NI_51_RR : 12;
        uint64_t StdAzimuth_NI_51_RR : 8;
        uint64_t SNRdB_NI_51_RR : 8;
        uint64_t PowerDB_NI_51_RR : 8;
        uint64_t WDoppler10DB_NI_51_RR : 7;
        uint64_t Reserve1_1bit_NI_51_RR : 1;
        uint64_t WRange10DB_NI_51_RR : 8;
        uint64_t CoGDoppler_NI_51_RR : 16;
        uint64_t CoGRange_NI_51_RR : 16;
        uint64_t ValidXBeam_NI_50_RR : 1;
        uint64_t NoInfrastructure_NI_50_RR : 1;
        uint64_t Beam_NI_50_RR : 2;
        uint64_t Azimuth_NI_50_RR : 12;
        uint64_t StdAzimuth_NI_50_RR : 8;
        uint64_t SNRdB_NI_50_RR : 8;
        uint64_t PowerDB_NI_50_RR : 8;
        uint64_t WDoppler10DB_NI_50_RR : 7;
        uint64_t Reserve1_1bit_NI_50_RR : 1;
        uint64_t WRange10DB_NI_50_RR : 8;
        uint64_t CoGDoppler_NI_50_RR : 16;
        uint64_t CoGRange_NI_50_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_10_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_10_rr_data_t data;
};

union cansig_mk_non_inf_detection_10_rr__azimuth_ni_52_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_52_RR__S1 : 4;
        uint32_t Azimuth_NI_52_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_11_RR_ID       (0x018D)
#define CANMSG_MK_NON_INF_DETECTION_11_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_11_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_11_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_59_RR : 1;
        uint64_t NoInfrastructure_NI_59_RR : 1;
        uint64_t Beam_NI_59_RR : 2;
        uint64_t Azimuth_NI_59_RR : 12;
        uint64_t StdAzimuth_NI_59_RR : 8;
        uint64_t SNRdB_NI_59_RR : 8;
        uint64_t PowerDB_NI_59_RR : 8;
        uint64_t WDoppler10DB_NI_59_RR : 7;
        uint64_t Reserve1_1bit_NI_59_RR : 1;
        uint64_t WRange10DB_NI_59_RR : 8;
        uint64_t CoGDoppler_NI_59_RR : 16;
        uint64_t CoGRange_NI_59_RR : 16;
        uint64_t ValidXBeam_NI_58_RR : 1;
        uint64_t NoInfrastructure_NI_58_RR : 1;
        uint64_t Beam_NI_58_RR : 2;
        uint64_t Azimuth_NI_58_RR : 12;
        uint64_t StdAzimuth_NI_58_RR : 8;
        uint64_t SNRdB_NI_58_RR : 8;
        uint64_t PowerDB_NI_58_RR : 8;
        uint64_t WDoppler10DB_NI_58_RR : 7;
        uint64_t Reserve1_1bit_NI_58_RR : 1;
        uint64_t WRange10DB_NI_58_RR : 8;
        uint64_t CoGDoppler_NI_58_RR : 16;
        uint64_t CoGRange_NI_58_RR : 16;
        uint64_t ValidXBeam_NI_57_RR : 1;
        uint64_t NoInfrastructure_NI_57_RR : 1;
        uint64_t Beam_NI_57_RR : 2;
        uint64_t Azimuth_NI_57_RR__S1 : 4;
        uint64_t Azimuth_NI_57_RR__S0 : 8;
        uint64_t StdAzimuth_NI_57_RR : 8;
        uint64_t SNRdB_NI_57_RR : 8;
        uint64_t PowerDB_NI_57_RR : 8;
        uint64_t WDoppler10DB_NI_57_RR : 7;
        uint64_t Reserve1_1bit_NI_57_RR : 1;
        uint64_t WRange10DB_NI_57_RR : 8;
        uint64_t CoGDoppler_NI_57_RR : 16;
        uint64_t CoGRange_NI_57_RR : 16;
        uint64_t ValidXBeam_NI_56_RR : 1;
        uint64_t NoInfrastructure_NI_56_RR : 1;
        uint64_t Beam_NI_56_RR : 2;
        uint64_t Azimuth_NI_56_RR : 12;
        uint64_t StdAzimuth_NI_56_RR : 8;
        uint64_t SNRdB_NI_56_RR : 8;
        uint64_t PowerDB_NI_56_RR : 8;
        uint64_t WDoppler10DB_NI_56_RR : 7;
        uint64_t Reserve1_1bit_NI_56_RR : 1;
        uint64_t WRange10DB_NI_56_RR : 8;
        uint64_t CoGDoppler_NI_56_RR : 16;
        uint64_t CoGRange_NI_56_RR : 16;
        uint64_t ValidXBeam_NI_55_RR : 1;
        uint64_t NoInfrastructure_NI_55_RR : 1;
        uint64_t Beam_NI_55_RR : 2;
        uint64_t Azimuth_NI_55_RR : 12;
        uint64_t StdAzimuth_NI_55_RR : 8;
        uint64_t SNRdB_NI_55_RR : 8;
        uint64_t PowerDB_NI_55_RR : 8;
        uint64_t WDoppler10DB_NI_55_RR : 7;
        uint64_t Reserve1_1bit_NI_55_RR : 1;
        uint64_t WRange10DB_NI_55_RR : 8;
        uint64_t CoGDoppler_NI_55_RR : 16;
        uint64_t CoGRange_NI_55_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_11_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_11_rr_data_t data;
};

union cansig_mk_non_inf_detection_11_rr__azimuth_ni_57_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_57_RR__S1 : 4;
        uint32_t Azimuth_NI_57_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_12_RR_ID       (0x018E)
#define CANMSG_MK_NON_INF_DETECTION_12_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_12_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_12_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_64_RR : 1;
        uint64_t NoInfrastructure_NI_64_RR : 1;
        uint64_t Beam_NI_64_RR : 2;
        uint64_t Azimuth_NI_64_RR : 12;
        uint64_t StdAzimuth_NI_64_RR : 8;
        uint64_t SNRdB_NI_64_RR : 8;
        uint64_t PowerDB_NI_64_RR : 8;
        uint64_t WDoppler10DB_NI_64_RR : 7;
        uint64_t Reserve1_1bit_NI_64_RR : 1;
        uint64_t WRange10DB_NI_64_RR : 8;
        uint64_t CoGDoppler_NI_64_RR : 16;
        uint64_t CoGRange_NI_64_RR : 16;
        uint64_t ValidXBeam_NI_63_RR : 1;
        uint64_t NoInfrastructure_NI_63_RR : 1;
        uint64_t Beam_NI_63_RR : 2;
        uint64_t Azimuth_NI_63_RR : 12;
        uint64_t StdAzimuth_NI_63_RR : 8;
        uint64_t SNRdB_NI_63_RR : 8;
        uint64_t PowerDB_NI_63_RR : 8;
        uint64_t WDoppler10DB_NI_63_RR : 7;
        uint64_t Reserve1_1bit_NI_63_RR : 1;
        uint64_t WRange10DB_NI_63_RR : 8;
        uint64_t CoGDoppler_NI_63_RR : 16;
        uint64_t CoGRange_NI_63_RR : 16;
        uint64_t ValidXBeam_NI_62_RR : 1;
        uint64_t NoInfrastructure_NI_62_RR : 1;
        uint64_t Beam_NI_62_RR : 2;
        uint64_t Azimuth_NI_62_RR__S1 : 4;
        uint64_t Azimuth_NI_62_RR__S0 : 8;
        uint64_t StdAzimuth_NI_62_RR : 8;
        uint64_t SNRdB_NI_62_RR : 8;
        uint64_t PowerDB_NI_62_RR : 8;
        uint64_t WDoppler10DB_NI_62_RR : 7;
        uint64_t Reserve1_1bit_NI_62_RR : 1;
        uint64_t WRange10DB_NI_62_RR : 8;
        uint64_t CoGDoppler_NI_62_RR : 16;
        uint64_t CoGRange_NI_62_RR : 16;
        uint64_t ValidXBeam_NI_61_RR : 1;
        uint64_t NoInfrastructure_NI_61_RR : 1;
        uint64_t Beam_NI_61_RR : 2;
        uint64_t Azimuth_NI_61_RR : 12;
        uint64_t StdAzimuth_NI_61_RR : 8;
        uint64_t SNRdB_NI_61_RR : 8;
        uint64_t PowerDB_NI_61_RR : 8;
        uint64_t WDoppler10DB_NI_61_RR : 7;
        uint64_t Reserve1_1bit_NI_61_RR : 1;
        uint64_t WRange10DB_NI_61_RR : 8;
        uint64_t CoGDoppler_NI_61_RR : 16;
        uint64_t CoGRange_NI_61_RR : 16;
        uint64_t ValidXBeam_NI_60_RR : 1;
        uint64_t NoInfrastructure_NI_60_RR : 1;
        uint64_t Beam_NI_60_RR : 2;
        uint64_t Azimuth_NI_60_RR : 12;
        uint64_t StdAzimuth_NI_60_RR : 8;
        uint64_t SNRdB_NI_60_RR : 8;
        uint64_t PowerDB_NI_60_RR : 8;
        uint64_t WDoppler10DB_NI_60_RR : 7;
        uint64_t Reserve1_1bit_NI_60_RR : 1;
        uint64_t WRange10DB_NI_60_RR : 8;
        uint64_t CoGDoppler_NI_60_RR : 16;
        uint64_t CoGRange_NI_60_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_12_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_12_rr_data_t data;
};

union cansig_mk_non_inf_detection_12_rr__azimuth_ni_62_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_62_RR__S1 : 4;
        uint32_t Azimuth_NI_62_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_13_RR_ID       (0x018F)
#define CANMSG_MK_NON_INF_DETECTION_13_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_13_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_13_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_69_RR : 1;
        uint64_t NoInfrastructure_NI_69_RR : 1;
        uint64_t Beam_NI_69_RR : 2;
        uint64_t Azimuth_NI_69_RR : 12;
        uint64_t StdAzimuth_NI_69_RR : 8;
        uint64_t SNRdB_NI_69_RR : 8;
        uint64_t PowerDB_NI_69_RR : 8;
        uint64_t WDoppler10DB_NI_69_RR : 7;
        uint64_t Reserve1_1bit_NI_69_RR : 1;
        uint64_t WRange10DB_NI_69_RR : 8;
        uint64_t CoGDoppler_NI_69_RR : 16;
        uint64_t CoGRange_NI_69_RR : 16;
        uint64_t ValidXBeam_NI_68_RR : 1;
        uint64_t NoInfrastructure_NI_68_RR : 1;
        uint64_t Beam_NI_68_RR : 2;
        uint64_t Azimuth_NI_68_RR : 12;
        uint64_t StdAzimuth_NI_68_RR : 8;
        uint64_t SNRdB_NI_68_RR : 8;
        uint64_t PowerDB_NI_68_RR : 8;
        uint64_t WDoppler10DB_NI_68_RR : 7;
        uint64_t Reserve1_1bit_NI_68_RR : 1;
        uint64_t WRange10DB_NI_68_RR : 8;
        uint64_t CoGDoppler_NI_68_RR : 16;
        uint64_t CoGRange_NI_68_RR : 16;
        uint64_t ValidXBeam_NI_67_RR : 1;
        uint64_t NoInfrastructure_NI_67_RR : 1;
        uint64_t Beam_NI_67_RR : 2;
        uint64_t Azimuth_NI_67_RR__S1 : 4;
        uint64_t Azimuth_NI_67_RR__S0 : 8;
        uint64_t StdAzimuth_NI_67_RR : 8;
        uint64_t SNRdB_NI_67_RR : 8;
        uint64_t PowerDB_NI_67_RR : 8;
        uint64_t WDoppler10DB_NI_67_RR : 7;
        uint64_t Reserve1_1bit_NI_67_RR : 1;
        uint64_t WRange10DB_NI_67_RR : 8;
        uint64_t CoGDoppler_NI_67_RR : 16;
        uint64_t CoGRange_NI_67_RR : 16;
        uint64_t ValidXBeam_NI_66_RR : 1;
        uint64_t NoInfrastructure_NI_66_RR : 1;
        uint64_t Beam_NI_66_RR : 2;
        uint64_t Azimuth_NI_66_RR : 12;
        uint64_t StdAzimuth_NI_66_RR : 8;
        uint64_t SNRdB_NI_66_RR : 8;
        uint64_t PowerDB_NI_66_RR : 8;
        uint64_t WDoppler10DB_NI_66_RR : 7;
        uint64_t Reserve1_1bit_NI_66_RR : 1;
        uint64_t WRange10DB_NI_66_RR : 8;
        uint64_t CoGDoppler_NI_66_RR : 16;
        uint64_t CoGRange_NI_66_RR : 16;
        uint64_t ValidXBeam_NI_65_RR : 1;
        uint64_t NoInfrastructure_NI_65_RR : 1;
        uint64_t Beam_NI_65_RR : 2;
        uint64_t Azimuth_NI_65_RR : 12;
        uint64_t StdAzimuth_NI_65_RR : 8;
        uint64_t SNRdB_NI_65_RR : 8;
        uint64_t PowerDB_NI_65_RR : 8;
        uint64_t WDoppler10DB_NI_65_RR : 7;
        uint64_t Reserve1_1bit_NI_65_RR : 1;
        uint64_t WRange10DB_NI_65_RR : 8;
        uint64_t CoGDoppler_NI_65_RR : 16;
        uint64_t CoGRange_NI_65_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_13_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_13_rr_data_t data;
};

union cansig_mk_non_inf_detection_13_rr__azimuth_ni_67_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_67_RR__S1 : 4;
        uint32_t Azimuth_NI_67_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_14_RR_ID       (0x0190)
#define CANMSG_MK_NON_INF_DETECTION_14_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_14_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_14_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_74_RR : 1;
        uint64_t NoInfrastructure_NI_74_RR : 1;
        uint64_t Beam_NI_74_RR : 2;
        uint64_t Azimuth_NI_74_RR : 12;
        uint64_t StdAzimuth_NI_74_RR : 8;
        uint64_t SNRdB_NI_74_RR : 8;
        uint64_t PowerDB_NI_74_RR : 8;
        uint64_t WDoppler10DB_NI_74_RR : 7;
        uint64_t Reserve1_1bit_NI_74_RR : 1;
        uint64_t WRange10DB_NI_74_RR : 8;
        uint64_t CoGDoppler_NI_74_RR : 16;
        uint64_t CoGRange_NI_74_RR : 16;
        uint64_t ValidXBeam_NI_73_RR : 1;
        uint64_t NoInfrastructure_NI_73_RR : 1;
        uint64_t Beam_NI_73_RR : 2;
        uint64_t Azimuth_NI_73_RR : 12;
        uint64_t StdAzimuth_NI_73_RR : 8;
        uint64_t SNRdB_NI_73_RR : 8;
        uint64_t PowerDB_NI_73_RR : 8;
        uint64_t WDoppler10DB_NI_73_RR : 7;
        uint64_t Reserve1_1bit_NI_73_RR : 1;
        uint64_t WRange10DB_NI_73_RR : 8;
        uint64_t CoGDoppler_NI_73_RR : 16;
        uint64_t CoGRange_NI_73_RR : 16;
        uint64_t ValidXBeam_NI_72_RR : 1;
        uint64_t NoInfrastructure_NI_72_RR : 1;
        uint64_t Beam_NI_72_RR : 2;
        uint64_t Azimuth_NI_72_RR__S1 : 4;
        uint64_t Azimuth_NI_72_RR__S0 : 8;
        uint64_t StdAzimuth_NI_72_RR : 8;
        uint64_t SNRdB_NI_72_RR : 8;
        uint64_t PowerDB_NI_72_RR : 8;
        uint64_t WDoppler10DB_NI_72_RR : 7;
        uint64_t Reserve1_1bit_NI_72_RR : 1;
        uint64_t WRange10DB_NI_72_RR : 8;
        uint64_t CoGDoppler_NI_72_RR : 16;
        uint64_t CoGRange_NI_72_RR : 16;
        uint64_t ValidXBeam_NI_71_RR : 1;
        uint64_t NoInfrastructure_NI_71_RR : 1;
        uint64_t Beam_NI_71_RR : 2;
        uint64_t Azimuth_NI_71_RR : 12;
        uint64_t StdAzimuth_NI_71_RR : 8;
        uint64_t SNRdB_NI_71_RR : 8;
        uint64_t PowerDB_NI_71_RR : 8;
        uint64_t WDoppler10DB_NI_71_RR : 7;
        uint64_t Reserve1_1bit_NI_71_RR : 1;
        uint64_t WRange10DB_NI_71_RR : 8;
        uint64_t CoGDoppler_NI_71_RR : 16;
        uint64_t CoGRange_NI_71_RR : 16;
        uint64_t ValidXBeam_NI_70_RR : 1;
        uint64_t NoInfrastructure_NI_70_RR : 1;
        uint64_t Beam_NI_70_RR : 2;
        uint64_t Azimuth_NI_70_RR : 12;
        uint64_t StdAzimuth_NI_70_RR : 8;
        uint64_t SNRdB_NI_70_RR : 8;
        uint64_t PowerDB_NI_70_RR : 8;
        uint64_t WDoppler10DB_NI_70_RR : 7;
        uint64_t Reserve1_1bit_NI_70_RR : 1;
        uint64_t WRange10DB_NI_70_RR : 8;
        uint64_t CoGDoppler_NI_70_RR : 16;
        uint64_t CoGRange_NI_70_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_14_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_14_rr_data_t data;
};

union cansig_mk_non_inf_detection_14_rr__azimuth_ni_72_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_72_RR__S1 : 4;
        uint32_t Azimuth_NI_72_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_15_RR_ID       (0x0191)
#define CANMSG_MK_NON_INF_DETECTION_15_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_15_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_15_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_79_RR : 1;
        uint64_t NoInfrastructure_NI_79_RR : 1;
        uint64_t Beam_NI_79_RR : 2;
        uint64_t Azimuth_NI_79_RR : 12;
        uint64_t StdAzimuth_NI_79_RR : 8;
        uint64_t SNRdB_NI_79_RR : 8;
        uint64_t PowerDB_NI_79_RR : 8;
        uint64_t WDoppler10DB_NI_79_RR : 7;
        uint64_t Reserve1_1bit_NI_79_RR : 1;
        uint64_t WRange10DB_NI_79_RR : 8;
        uint64_t CoGDoppler_NI_79_RR : 16;
        uint64_t CoGRange_NI_79_RR : 16;
        uint64_t ValidXBeam_NI_78_RR : 1;
        uint64_t NoInfrastructure_NI_78_RR : 1;
        uint64_t Beam_NI_78_RR : 2;
        uint64_t Azimuth_NI_78_RR : 12;
        uint64_t StdAzimuth_NI_78_RR : 8;
        uint64_t SNRdB_NI_78_RR : 8;
        uint64_t PowerDB_NI_78_RR : 8;
        uint64_t WDoppler10DB_NI_78_RR : 7;
        uint64_t Reserve1_1bit_NI_78_RR : 1;
        uint64_t WRange10DB_NI_78_RR : 8;
        uint64_t CoGDoppler_NI_78_RR : 16;
        uint64_t CoGRange_NI_78_RR : 16;
        uint64_t ValidXBeam_NI_77_RR : 1;
        uint64_t NoInfrastructure_NI_77_RR : 1;
        uint64_t Beam_NI_77_RR : 2;
        uint64_t Azimuth_NI_77_RR__S1 : 4;
        uint64_t Azimuth_NI_77_RR__S0 : 8;
        uint64_t StdAzimuth_NI_77_RR : 8;
        uint64_t SNRdB_NI_77_RR : 8;
        uint64_t PowerDB_NI_77_RR : 8;
        uint64_t WDoppler10DB_NI_77_RR : 7;
        uint64_t Reserve1_1bit_NI_77_RR : 1;
        uint64_t WRange10DB_NI_77_RR : 8;
        uint64_t CoGDoppler_NI_77_RR : 16;
        uint64_t CoGRange_NI_77_RR : 16;
        uint64_t ValidXBeam_NI_76_RR : 1;
        uint64_t NoInfrastructure_NI_76_RR : 1;
        uint64_t Beam_NI_76_RR : 2;
        uint64_t Azimuth_NI_76_RR : 12;
        uint64_t StdAzimuth_NI_76_RR : 8;
        uint64_t SNRdB_NI_76_RR : 8;
        uint64_t PowerDB_NI_76_RR : 8;
        uint64_t WDoppler10DB_NI_76_RR : 7;
        uint64_t Reserve1_1bit_NI_76_RR : 1;
        uint64_t WRange10DB_NI_76_RR : 8;
        uint64_t CoGDoppler_NI_76_RR : 16;
        uint64_t CoGRange_NI_76_RR : 16;
        uint64_t ValidXBeam_NI_75_RR : 1;
        uint64_t NoInfrastructure_NI_75_RR : 1;
        uint64_t Beam_NI_75_RR : 2;
        uint64_t Azimuth_NI_75_RR : 12;
        uint64_t StdAzimuth_NI_75_RR : 8;
        uint64_t SNRdB_NI_75_RR : 8;
        uint64_t PowerDB_NI_75_RR : 8;
        uint64_t WDoppler10DB_NI_75_RR : 7;
        uint64_t Reserve1_1bit_NI_75_RR : 1;
        uint64_t WRange10DB_NI_75_RR : 8;
        uint64_t CoGDoppler_NI_75_RR : 16;
        uint64_t CoGRange_NI_75_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_15_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_15_rr_data_t data;
};

union cansig_mk_non_inf_detection_15_rr__azimuth_ni_77_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_77_RR__S1 : 4;
        uint32_t Azimuth_NI_77_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_16_RR_ID       (0x0192)
#define CANMSG_MK_NON_INF_DETECTION_16_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_16_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_16_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_84_RR : 1;
        uint64_t NoInfrastructure_NI_84_RR : 1;
        uint64_t Beam_NI_84_RR : 2;
        uint64_t Azimuth_NI_84_RR : 12;
        uint64_t StdAzimuth_NI_84_RR : 8;
        uint64_t SNRdB_NI_84_RR : 8;
        uint64_t PowerDB_NI_84_RR : 8;
        uint64_t WDoppler10DB_NI_84_RR : 7;
        uint64_t Reserve1_1bit_NI_84_RR : 1;
        uint64_t WRange10DB_NI_84_RR : 8;
        uint64_t CoGDoppler_NI_84_RR : 16;
        uint64_t CoGRange_NI_84_RR : 16;
        uint64_t ValidXBeam_NI_83_RR : 1;
        uint64_t NoInfrastructure_NI_83_RR : 1;
        uint64_t Beam_NI_83_RR : 2;
        uint64_t Azimuth_NI_83_RR : 12;
        uint64_t StdAzimuth_NI_83_RR : 8;
        uint64_t SNRdB_NI_83_RR : 8;
        uint64_t PowerDB_NI_83_RR : 8;
        uint64_t WDoppler10DB_NI_83_RR : 7;
        uint64_t Reserve1_1bit_NI_83_RR : 1;
        uint64_t WRange10DB_NI_83_RR : 8;
        uint64_t CoGDoppler_NI_83_RR : 16;
        uint64_t CoGRange_NI_83_RR : 16;
        uint64_t ValidXBeam_NI_82_RR : 1;
        uint64_t NoInfrastructure_NI_82_RR : 1;
        uint64_t Beam_NI_82_RR : 2;
        uint64_t Azimuth_NI_82_RR__S1 : 4;
        uint64_t Azimuth_NI_82_RR__S0 : 8;
        uint64_t StdAzimuth_NI_82_RR : 8;
        uint64_t SNRdB_NI_82_RR : 8;
        uint64_t PowerDB_NI_82_RR : 8;
        uint64_t WDoppler10DB_NI_82_RR : 7;
        uint64_t Reserve1_1bit_NI_82_RR : 1;
        uint64_t WRange10DB_NI_82_RR : 8;
        uint64_t CoGDoppler_NI_82_RR : 16;
        uint64_t CoGRange_NI_82_RR : 16;
        uint64_t ValidXBeam_NI_81_RR : 1;
        uint64_t NoInfrastructure_NI_81_RR : 1;
        uint64_t Beam_NI_81_RR : 2;
        uint64_t Azimuth_NI_81_RR : 12;
        uint64_t StdAzimuth_NI_81_RR : 8;
        uint64_t SNRdB_NI_81_RR : 8;
        uint64_t PowerDB_NI_81_RR : 8;
        uint64_t WDoppler10DB_NI_81_RR : 7;
        uint64_t Reserve1_1bit_NI_81_RR : 1;
        uint64_t WRange10DB_NI_81_RR : 8;
        uint64_t CoGDoppler_NI_81_RR : 16;
        uint64_t CoGRange_NI_81_RR : 16;
        uint64_t ValidXBeam_NI_80_RR : 1;
        uint64_t NoInfrastructure_NI_80_RR : 1;
        uint64_t Beam_NI_80_RR : 2;
        uint64_t Azimuth_NI_80_RR : 12;
        uint64_t StdAzimuth_NI_80_RR : 8;
        uint64_t SNRdB_NI_80_RR : 8;
        uint64_t PowerDB_NI_80_RR : 8;
        uint64_t WDoppler10DB_NI_80_RR : 7;
        uint64_t Reserve1_1bit_NI_80_RR : 1;
        uint64_t WRange10DB_NI_80_RR : 8;
        uint64_t CoGDoppler_NI_80_RR : 16;
        uint64_t CoGRange_NI_80_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_16_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_16_rr_data_t data;
};

union cansig_mk_non_inf_detection_16_rr__azimuth_ni_82_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_82_RR__S1 : 4;
        uint32_t Azimuth_NI_82_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_17_RR_ID       (0x0193)
#define CANMSG_MK_NON_INF_DETECTION_17_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_17_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_17_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_89_RR : 1;
        uint64_t NoInfrastructure_NI_89_RR : 1;
        uint64_t Beam_NI_89_RR : 2;
        uint64_t Azimuth_NI_89_RR : 12;
        uint64_t StdAzimuth_NI_89_RR : 8;
        uint64_t SNRdB_NI_89_RR : 8;
        uint64_t PowerDB_NI_89_RR : 8;
        uint64_t WDoppler10DB_NI_89_RR : 7;
        uint64_t Reserve1_1bit_NI_89_RR : 1;
        uint64_t WRange10DB_NI_89_RR : 8;
        uint64_t CoGDoppler_NI_89_RR : 16;
        uint64_t CoGRange_NI_89_RR : 16;
        uint64_t ValidXBeam_NI_88_RR : 1;
        uint64_t NoInfrastructure_NI_88_RR : 1;
        uint64_t Beam_NI_88_RR : 2;
        uint64_t Azimuth_NI_88_RR : 12;
        uint64_t StdAzimuth_NI_88_RR : 8;
        uint64_t SNRdB_NI_88_RR : 8;
        uint64_t PowerDB_NI_88_RR : 8;
        uint64_t WDoppler10DB_NI_88_RR : 7;
        uint64_t Reserve1_1bit_NI_88_RR : 1;
        uint64_t WRange10DB_NI_88_RR : 8;
        uint64_t CoGDoppler_NI_88_RR : 16;
        uint64_t CoGRange_NI_88_RR : 16;
        uint64_t ValidXBeam_NI_87_RR : 1;
        uint64_t NoInfrastructure_NI_87_RR : 1;
        uint64_t Beam_NI_87_RR : 2;
        uint64_t Azimuth_NI_87_RR__S1 : 4;
        uint64_t Azimuth_NI_87_RR__S0 : 8;
        uint64_t StdAzimuth_NI_87_RR : 8;
        uint64_t SNRdB_NI_87_RR : 8;
        uint64_t PowerDB_NI_87_RR : 8;
        uint64_t WDoppler10DB_NI_87_RR : 7;
        uint64_t Reserve1_1bit_NI_87_RR : 1;
        uint64_t WRange10DB_NI_87_RR : 8;
        uint64_t CoGDoppler_NI_87_RR : 16;
        uint64_t CoGRange_NI_87_RR : 16;
        uint64_t ValidXBeam_NI_86_RR : 1;
        uint64_t NoInfrastructure_NI_86_RR : 1;
        uint64_t Beam_NI_86_RR : 2;
        uint64_t Azimuth_NI_86_RR : 12;
        uint64_t StdAzimuth_NI_86_RR : 8;
        uint64_t SNRdB_NI_86_RR : 8;
        uint64_t PowerDB_NI_86_RR : 8;
        uint64_t WDoppler10DB_NI_86_RR : 7;
        uint64_t Reserve1_1bit_NI_86_RR : 1;
        uint64_t WRange10DB_NI_86_RR : 8;
        uint64_t CoGDoppler_NI_86_RR : 16;
        uint64_t CoGRange_NI_86_RR : 16;
        uint64_t ValidXBeam_NI_85_RR : 1;
        uint64_t NoInfrastructure_NI_85_RR : 1;
        uint64_t Beam_NI_85_RR : 2;
        uint64_t Azimuth_NI_85_RR : 12;
        uint64_t StdAzimuth_NI_85_RR : 8;
        uint64_t SNRdB_NI_85_RR : 8;
        uint64_t PowerDB_NI_85_RR : 8;
        uint64_t WDoppler10DB_NI_85_RR : 7;
        uint64_t Reserve1_1bit_NI_85_RR : 1;
        uint64_t WRange10DB_NI_85_RR : 8;
        uint64_t CoGDoppler_NI_85_RR : 16;
        uint64_t CoGRange_NI_85_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_17_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_17_rr_data_t data;
};

union cansig_mk_non_inf_detection_17_rr__azimuth_ni_87_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_87_RR__S1 : 4;
        uint32_t Azimuth_NI_87_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_18_RR_ID       (0x0194)
#define CANMSG_MK_NON_INF_DETECTION_18_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_18_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_18_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_94_RR : 1;
        uint64_t NoInfrastructure_NI_94_RR : 1;
        uint64_t Beam_NI_94_RR : 2;
        uint64_t Azimuth_NI_94_RR : 12;
        uint64_t StdAzimuth_NI_94_RR : 8;
        uint64_t SNRdB_NI_94_RR : 8;
        uint64_t PowerDB_NI_94_RR : 8;
        uint64_t WDoppler10DB_NI_94_RR : 7;
        uint64_t Reserve1_1bit_NI_94_RR : 1;
        uint64_t WRange10DB_NI_94_RR : 8;
        uint64_t CoGDoppler_NI_94_RR : 16;
        uint64_t CoGRange_NI_94_RR : 16;
        uint64_t ValidXBeam_NI_93_RR : 1;
        uint64_t NoInfrastructure_NI_93_RR : 1;
        uint64_t Beam_NI_93_RR : 2;
        uint64_t Azimuth_NI_93_RR : 12;
        uint64_t StdAzimuth_NI_93_RR : 8;
        uint64_t SNRdB_NI_93_RR : 8;
        uint64_t PowerDB_NI_93_RR : 8;
        uint64_t WDoppler10DB_NI_93_RR : 7;
        uint64_t Reserve1_1bit_NI_93_RR : 1;
        uint64_t WRange10DB_NI_93_RR : 8;
        uint64_t CoGDoppler_NI_93_RR : 16;
        uint64_t CoGRange_NI_93_RR : 16;
        uint64_t ValidXBeam_NI_92_RR : 1;
        uint64_t NoInfrastructure_NI_92_RR : 1;
        uint64_t Beam_NI_92_RR : 2;
        uint64_t Azimuth_NI_92_RR__S1 : 4;
        uint64_t Azimuth_NI_92_RR__S0 : 8;
        uint64_t StdAzimuth_NI_92_RR : 8;
        uint64_t SNRdB_NI_92_RR : 8;
        uint64_t PowerDB_NI_92_RR : 8;
        uint64_t WDoppler10DB_NI_92_RR : 7;
        uint64_t Reserve1_1bit_NI_92_RR : 1;
        uint64_t WRange10DB_NI_92_RR : 8;
        uint64_t CoGDoppler_NI_92_RR : 16;
        uint64_t CoGRange_NI_92_RR : 16;
        uint64_t ValidXBeam_NI_91_RR : 1;
        uint64_t NoInfrastructure_NI_91_RR : 1;
        uint64_t Beam_NI_91_RR : 2;
        uint64_t Azimuth_NI_91_RR : 12;
        uint64_t StdAzimuth_NI_91_RR : 8;
        uint64_t SNRdB_NI_91_RR : 8;
        uint64_t PowerDB_NI_91_RR : 8;
        uint64_t WDoppler10DB_NI_91_RR : 7;
        uint64_t Reserve1_1bit_NI_91_RR : 1;
        uint64_t WRange10DB_NI_91_RR : 8;
        uint64_t CoGDoppler_NI_91_RR : 16;
        uint64_t CoGRange_NI_91_RR : 16;
        uint64_t ValidXBeam_NI_90_RR : 1;
        uint64_t NoInfrastructure_NI_90_RR : 1;
        uint64_t Beam_NI_90_RR : 2;
        uint64_t Azimuth_NI_90_RR : 12;
        uint64_t StdAzimuth_NI_90_RR : 8;
        uint64_t SNRdB_NI_90_RR : 8;
        uint64_t PowerDB_NI_90_RR : 8;
        uint64_t WDoppler10DB_NI_90_RR : 7;
        uint64_t Reserve1_1bit_NI_90_RR : 1;
        uint64_t WRange10DB_NI_90_RR : 8;
        uint64_t CoGDoppler_NI_90_RR : 16;
        uint64_t CoGRange_NI_90_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_18_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_18_rr_data_t data;
};

union cansig_mk_non_inf_detection_18_rr__azimuth_ni_92_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_92_RR__S1 : 4;
        uint32_t Azimuth_NI_92_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_19_RR_ID       (0x0195)
#define CANMSG_MK_NON_INF_DETECTION_19_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_19_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_19_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_99_RR : 1;
        uint64_t NoInfrastructure_NI_99_RR : 1;
        uint64_t Beam_NI_99_RR : 2;
        uint64_t Azimuth_NI_99_RR : 12;
        uint64_t StdAzimuth_NI_99_RR : 8;
        uint64_t SNRdB_NI_99_RR : 8;
        uint64_t PowerDB_NI_99_RR : 8;
        uint64_t WDoppler10DB_NI_99_RR : 7;
        uint64_t Reserve1_1bit_NI_99_RR : 1;
        uint64_t WRange10DB_NI_99_RR : 8;
        uint64_t CoGDoppler_NI_99_RR : 16;
        uint64_t CoGRange_NI_99_RR : 16;
        uint64_t ValidXBeam_NI_98_RR : 1;
        uint64_t NoInfrastructure_NI_98_RR : 1;
        uint64_t Beam_NI_98_RR : 2;
        uint64_t Azimuth_NI_98_RR : 12;
        uint64_t StdAzimuth_NI_98_RR : 8;
        uint64_t SNRdB_NI_98_RR : 8;
        uint64_t PowerDB_NI_98_RR : 8;
        uint64_t WDoppler10DB_NI_98_RR : 7;
        uint64_t Reserve1_1bit_NI_98_RR : 1;
        uint64_t WRange10DB_NI_98_RR : 8;
        uint64_t CoGDoppler_NI_98_RR : 16;
        uint64_t CoGRange_NI_98_RR : 16;
        uint64_t ValidXBeam_NI_97_RR : 1;
        uint64_t NoInfrastructure_NI_97_RR : 1;
        uint64_t Beam_NI_97_RR : 2;
        uint64_t Azimuth_NI_97_RR__S1 : 4;
        uint64_t Azimuth_NI_97_RR__S0 : 8;
        uint64_t StdAzimuth_NI_97_RR : 8;
        uint64_t SNRdB_NI_97_RR : 8;
        uint64_t PowerDB_NI_97_RR : 8;
        uint64_t WDoppler10DB_NI_97_RR : 7;
        uint64_t Reserve1_1bit_NI_97_RR : 1;
        uint64_t WRange10DB_NI_97_RR : 8;
        uint64_t CoGDoppler_NI_97_RR : 16;
        uint64_t CoGRange_NI_97_RR : 16;
        uint64_t ValidXBeam_NI_96_RR : 1;
        uint64_t NoInfrastructure_NI_96_RR : 1;
        uint64_t Beam_NI_96_RR : 2;
        uint64_t Azimuth_NI_96_RR : 12;
        uint64_t StdAzimuth_NI_96_RR : 8;
        uint64_t SNRdB_NI_96_RR : 8;
        uint64_t PowerDB_NI_96_RR : 8;
        uint64_t WDoppler10DB_NI_96_RR : 7;
        uint64_t Reserve1_1bit_NI_96_RR : 1;
        uint64_t WRange10DB_NI_96_RR : 8;
        uint64_t CoGDoppler_NI_96_RR : 16;
        uint64_t CoGRange_NI_96_RR : 16;
        uint64_t ValidXBeam_NI_95_RR : 1;
        uint64_t NoInfrastructure_NI_95_RR : 1;
        uint64_t Beam_NI_95_RR : 2;
        uint64_t Azimuth_NI_95_RR : 12;
        uint64_t StdAzimuth_NI_95_RR : 8;
        uint64_t SNRdB_NI_95_RR : 8;
        uint64_t PowerDB_NI_95_RR : 8;
        uint64_t WDoppler10DB_NI_95_RR : 7;
        uint64_t Reserve1_1bit_NI_95_RR : 1;
        uint64_t WRange10DB_NI_95_RR : 8;
        uint64_t CoGDoppler_NI_95_RR : 16;
        uint64_t CoGRange_NI_95_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_19_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_19_rr_data_t data;
};

union cansig_mk_non_inf_detection_19_rr__azimuth_ni_97_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_97_RR__S1 : 4;
        uint32_t Azimuth_NI_97_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_1_RR_ID        (0x0183)
#define CANMSG_MK_NON_INF_DETECTION_1_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_1_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_1_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_9_RR : 1;
        uint64_t NoInfrastructure_NI_9_RR : 1;
        uint64_t Beam_NI_9_RR : 2;
        uint64_t Azimuth_NI_9_RR : 12;
        uint64_t StdAzimuth_NI_9_RR : 8;
        uint64_t SNRdB_NI_9_RR : 8;
        uint64_t PowerDB_NI_9_RR : 8;
        uint64_t WDoppler10DB_NI_9_RR : 7;
        uint64_t Reserve1_1bit_NI_9_RR : 1;
        uint64_t WRange10DB_NI_9_RR : 8;
        uint64_t CoGDoppler_NI_9_RR : 16;
        uint64_t CoGRange_NI_9_RR : 16;
        uint64_t ValidXBeam_NI_8_RR : 1;
        uint64_t NoInfrastructure_NI_8_RR : 1;
        uint64_t Beam_NI_8_RR : 2;
        uint64_t Azimuth_NI_8_RR : 12;
        uint64_t StdAzimuth_NI_8_RR : 8;
        uint64_t SNRdB_NI_8_RR : 8;
        uint64_t PowerDB_NI_8_RR : 8;
        uint64_t WDoppler10DB_NI_8_RR : 7;
        uint64_t Reserve1_1bit_NI_8_RR : 1;
        uint64_t WRange10DB_NI_8_RR : 8;
        uint64_t CoGDoppler_NI_8_RR : 16;
        uint64_t CoGRange_NI_8_RR : 16;
        uint64_t ValidXBeam_NI_7_RR : 1;
        uint64_t NoInfrastructure_NI_7_RR : 1;
        uint64_t Beam_NI_7_RR : 2;
        uint64_t Azimuth_NI_7_RR__S1 : 4;
        uint64_t Azimuth_NI_7_RR__S0 : 8;
        uint64_t StdAzimuth_NI_7_RR : 8;
        uint64_t SNRdB_NI_7_RR : 8;
        uint64_t PowerDB_NI_7_RR : 8;
        uint64_t WDoppler10DB_NI_7_RR : 7;
        uint64_t Reserve1_1bit_NI_7_RR : 1;
        uint64_t WRange10DB_NI_7_RR : 8;
        uint64_t CoGDoppler_NI_7_RR : 16;
        uint64_t CoGRange_NI_7_RR : 16;
        uint64_t ValidXBeam_NI_6_RR : 1;
        uint64_t NoInfrastructure_NI_6_RR : 1;
        uint64_t Beam_NI_6_RR : 2;
        uint64_t Azimuth_NI_6_RR : 12;
        uint64_t StdAzimuth_NI_6_RR : 8;
        uint64_t SNRdB_NI_6_RR : 8;
        uint64_t PowerDB_NI_6_RR : 8;
        uint64_t WDoppler10DB_NI_6_RR : 7;
        uint64_t Reserve1_1bit_NI_6_RR : 1;
        uint64_t WRange10DB_NI_6_RR : 8;
        uint64_t CoGDoppler_NI_6_RR : 16;
        uint64_t CoGRange_NI_6_RR : 16;
        uint64_t ValidXBeam_NI_5_RR : 1;
        uint64_t NoInfrastructure_NI_5_RR : 1;
        uint64_t Beam_NI_5_RR : 2;
        uint64_t Azimuth_NI_5_RR : 12;
        uint64_t StdAzimuth_NI_5_RR : 8;
        uint64_t SNRdB_NI_5_RR : 8;
        uint64_t PowerDB_NI_5_RR : 8;
        uint64_t WDoppler10DB_NI_5_RR : 7;
        uint64_t Reserve1_1bit_NI_5_RR : 1;
        uint64_t WRange10DB_NI_5_RR : 8;
        uint64_t CoGDoppler_NI_5_RR : 16;
        uint64_t CoGRange_NI_5_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_1_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_1_rr_data_t data;
};

union cansig_mk_non_inf_detection_1_rr__azimuth_ni_7_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_7_RR__S1 : 4;
        uint32_t Azimuth_NI_7_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_20_RR_ID       (0x0196)
#define CANMSG_MK_NON_INF_DETECTION_20_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_20_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_20_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_104_RR : 1;
        uint64_t NoInfrastructure_NI_104_RR : 1;
        uint64_t Beam_NI_104_RR : 2;
        uint64_t Azimuth_NI_104_RR : 12;
        uint64_t StdAzimuth_NI_104_RR : 8;
        uint64_t SNRdB_NI_104_RR : 8;
        uint64_t PowerDB_NI_104_RR : 8;
        uint64_t WDoppler10DB_NI_104_RR : 7;
        uint64_t Reserve1_1bit_NI_104_RR : 1;
        uint64_t WRange10DB_NI_104_RR : 8;
        uint64_t CoGDoppler_NI_104_RR : 16;
        uint64_t CoGRange_NI_104_RR : 16;
        uint64_t ValidXBeam_NI_103_RR : 1;
        uint64_t NoInfrastructure_NI_103_RR : 1;
        uint64_t Beam_NI_103_RR : 2;
        uint64_t Azimuth_NI_103_RR : 12;
        uint64_t StdAzimuth_NI_103_RR : 8;
        uint64_t SNRdB_NI_103_RR : 8;
        uint64_t PowerDB_NI_103_RR : 8;
        uint64_t WDoppler10DB_NI_103_RR : 7;
        uint64_t Reserve1_1bit_NI_103_RR : 1;
        uint64_t WRange10DB_NI_103_RR : 8;
        uint64_t CoGDoppler_NI_103_RR : 16;
        uint64_t CoGRange_NI_103_RR : 16;
        uint64_t ValidXBeam_NI_102_RR : 1;
        uint64_t NoInfrastructure_NI_102_RR : 1;
        uint64_t Beam_NI_102_RR : 2;
        uint64_t Azimuth_NI_102_RR__S1 : 4;
        uint64_t Azimuth_NI_102_RR__S0 : 8;
        uint64_t StdAzimuth_NI_102_RR : 8;
        uint64_t SNRdB_NI_102_RR : 8;
        uint64_t PowerDB_NI_102_RR : 8;
        uint64_t WDoppler10DB_NI_102_RR : 7;
        uint64_t Reserve1_1bit_NI_102_RR : 1;
        uint64_t WRange10DB_NI_102_RR : 8;
        uint64_t CoGDoppler_NI_102_RR : 16;
        uint64_t CoGRange_NI_102_RR : 16;
        uint64_t ValidXBeam_NI_101_RR : 1;
        uint64_t NoInfrastructure_NI_101_RR : 1;
        uint64_t Beam_NI_101_RR : 2;
        uint64_t Azimuth_NI_101_RR : 12;
        uint64_t StdAzimuth_NI_101_RR : 8;
        uint64_t SNRdB_NI_101_RR : 8;
        uint64_t PowerDB_NI_101_RR : 8;
        uint64_t WDoppler10DB_NI_101_RR : 7;
        uint64_t Reserve1_1bit_NI_101_RR : 1;
        uint64_t WRange10DB_NI_101_RR : 8;
        uint64_t CoGDoppler_NI_101_RR : 16;
        uint64_t CoGRange_NI_101_RR : 16;
        uint64_t ValidXBeam_NI_100_RR : 1;
        uint64_t NoInfrastructure_NI_100_RR : 1;
        uint64_t Beam_NI_100_RR : 2;
        uint64_t Azimuth_NI_100_RR : 12;
        uint64_t StdAzimuth_NI_100_RR : 8;
        uint64_t SNRdB_NI_100_RR : 8;
        uint64_t PowerDB_NI_100_RR : 8;
        uint64_t WDoppler10DB_NI_100_RR : 7;
        uint64_t Reserve1_1bit_NI_100_RR : 1;
        uint64_t WRange10DB_NI_100_RR : 8;
        uint64_t CoGDoppler_NI_100_RR : 16;
        uint64_t CoGRange_NI_100_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_20_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_20_rr_data_t data;
};

union cansig_mk_non_inf_detection_20_rr__azimuth_ni_102_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_102_RR__S1 : 4;
        uint32_t Azimuth_NI_102_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_21_RR_ID       (0x0197)
#define CANMSG_MK_NON_INF_DETECTION_21_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_21_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_21_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_109_RR : 1;
        uint64_t NoInfrastructure_NI_109_RR : 1;
        uint64_t Beam_NI_109_RR : 2;
        uint64_t Azimuth_NI_109_RR : 12;
        uint64_t StdAzimuth_NI_109_RR : 8;
        uint64_t SNRdB_NI_109_RR : 8;
        uint64_t PowerDB_NI_109_RR : 8;
        uint64_t WDoppler10DB_NI_109_RR : 7;
        uint64_t Reserve1_1bit_NI_109_RR : 1;
        uint64_t WRange10DB_NI_109_RR : 8;
        uint64_t CoGDoppler_NI_109_RR : 16;
        uint64_t CoGRange_NI_109_RR : 16;
        uint64_t ValidXBeam_NI_108_RR : 1;
        uint64_t NoInfrastructure_NI_108_RR : 1;
        uint64_t Beam_NI_108_RR : 2;
        uint64_t Azimuth_NI_108_RR : 12;
        uint64_t StdAzimuth_NI_108_RR : 8;
        uint64_t SNRdB_NI_108_RR : 8;
        uint64_t PowerDB_NI_108_RR : 8;
        uint64_t WDoppler10DB_NI_108_RR : 7;
        uint64_t Reserve1_1bit_NI_108_RR : 1;
        uint64_t WRange10DB_NI_108_RR : 8;
        uint64_t CoGDoppler_NI_108_RR : 16;
        uint64_t CoGRange_NI_108_RR : 16;
        uint64_t ValidXBeam_NI_107_RR : 1;
        uint64_t NoInfrastructure_NI_107_RR : 1;
        uint64_t Beam_NI_107_RR : 2;
        uint64_t Azimuth_NI_107_RR__S1 : 4;
        uint64_t Azimuth_NI_107_RR__S0 : 8;
        uint64_t StdAzimuth_NI_107_RR : 8;
        uint64_t SNRdB_NI_107_RR : 8;
        uint64_t PowerDB_NI_107_RR : 8;
        uint64_t WDoppler10DB_NI_107_RR : 7;
        uint64_t Reserve1_1bit_NI_107_RR : 1;
        uint64_t WRange10DB_NI_107_RR : 8;
        uint64_t CoGDoppler_NI_107_RR : 16;
        uint64_t CoGRange_NI_107_RR : 16;
        uint64_t ValidXBeam_NI_106_RR : 1;
        uint64_t NoInfrastructure_NI_106_RR : 1;
        uint64_t Beam_NI_106_RR : 2;
        uint64_t Azimuth_NI_106_RR : 12;
        uint64_t StdAzimuth_NI_106_RR : 8;
        uint64_t SNRdB_NI_106_RR : 8;
        uint64_t PowerDB_NI_106_RR : 8;
        uint64_t WDoppler10DB_NI_106_RR : 7;
        uint64_t Reserve1_1bit_NI_106_RR : 1;
        uint64_t WRange10DB_NI_106_RR : 8;
        uint64_t CoGDoppler_NI_106_RR : 16;
        uint64_t CoGRange_NI_106_RR : 16;
        uint64_t ValidXBeam_NI_105_RR : 1;
        uint64_t NoInfrastructure_NI_105_RR : 1;
        uint64_t Beam_NI_105_RR : 2;
        uint64_t Azimuth_NI_105_RR : 12;
        uint64_t StdAzimuth_NI_105_RR : 8;
        uint64_t SNRdB_NI_105_RR : 8;
        uint64_t PowerDB_NI_105_RR : 8;
        uint64_t WDoppler10DB_NI_105_RR : 7;
        uint64_t Reserve1_1bit_NI_105_RR : 1;
        uint64_t WRange10DB_NI_105_RR : 8;
        uint64_t CoGDoppler_NI_105_RR : 16;
        uint64_t CoGRange_NI_105_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_21_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_21_rr_data_t data;
};

union cansig_mk_non_inf_detection_21_rr__azimuth_ni_107_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_107_RR__S1 : 4;
        uint32_t Azimuth_NI_107_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_22_RR_ID       (0x0198)
#define CANMSG_MK_NON_INF_DETECTION_22_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_22_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_22_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_114_RR : 1;
        uint64_t NoInfrastructure_NI_114_RR : 1;
        uint64_t Beam_NI_114_RR : 2;
        uint64_t Azimuth_NI_114_RR : 12;
        uint64_t StdAzimuth_NI_114_RR : 8;
        uint64_t SNRdB_NI_114_RR : 8;
        uint64_t PowerDB_NI_114_RR : 8;
        uint64_t WDoppler10DB_NI_114_RR : 7;
        uint64_t Reserve1_1bit_NI_114_RR : 1;
        uint64_t WRange10DB_NI_114_RR : 8;
        uint64_t CoGDoppler_NI_114_RR : 16;
        uint64_t CoGRange_NI_114_RR : 16;
        uint64_t ValidXBeam_NI_113_RR : 1;
        uint64_t NoInfrastructure_NI_113_RR : 1;
        uint64_t Beam_NI_113_RR : 2;
        uint64_t Azimuth_NI_113_RR : 12;
        uint64_t StdAzimuth_NI_113_RR : 8;
        uint64_t SNRdB_NI_113_RR : 8;
        uint64_t PowerDB_NI_113_RR : 8;
        uint64_t WDoppler10DB_NI_113_RR : 7;
        uint64_t Reserve1_1bit_NI_113_RR : 1;
        uint64_t WRange10DB_NI_113_RR : 8;
        uint64_t CoGDoppler_NI_113_RR : 16;
        uint64_t CoGRange_NI_113_RR : 16;
        uint64_t ValidXBeam_NI_112_RR : 1;
        uint64_t NoInfrastructure_NI_112_RR : 1;
        uint64_t Beam_NI_112_RR : 2;
        uint64_t Azimuth_NI_112_RR__S1 : 4;
        uint64_t Azimuth_NI_112_RR__S0 : 8;
        uint64_t StdAzimuth_NI_112_RR : 8;
        uint64_t SNRdB_NI_112_RR : 8;
        uint64_t PowerDB_NI_112_RR : 8;
        uint64_t WDoppler10DB_NI_112_RR : 7;
        uint64_t Reserve1_1bit_NI_112_RR : 1;
        uint64_t WRange10DB_NI_112_RR : 8;
        uint64_t CoGDoppler_NI_112_RR : 16;
        uint64_t CoGRange_NI_112_RR : 16;
        uint64_t ValidXBeam_NI_111_RR : 1;
        uint64_t NoInfrastructure_NI_111_RR : 1;
        uint64_t Beam_NI_111_RR : 2;
        uint64_t Azimuth_NI_111_RR : 12;
        uint64_t StdAzimuth_NI_111_RR : 8;
        uint64_t SNRdB_NI_111_RR : 8;
        uint64_t PowerDB_NI_111_RR : 8;
        uint64_t WDoppler10DB_NI_111_RR : 7;
        uint64_t Reserve1_1bit_NI_111_RR : 1;
        uint64_t WRange10DB_NI_111_RR : 8;
        uint64_t CoGDoppler_NI_111_RR : 16;
        uint64_t CoGRange_NI_111_RR : 16;
        uint64_t ValidXBeam_NI_110_RR : 1;
        uint64_t NoInfrastructure_NI_110_RR : 1;
        uint64_t Beam_NI_110_RR : 2;
        uint64_t Azimuth_NI_110_RR : 12;
        uint64_t StdAzimuth_NI_110_RR : 8;
        uint64_t SNRdB_NI_110_RR : 8;
        uint64_t PowerDB_NI_110_RR : 8;
        uint64_t WDoppler10DB_NI_110_RR : 7;
        uint64_t Reserve1_1bit_NI_110_RR : 1;
        uint64_t WRange10DB_NI_110_RR : 8;
        uint64_t CoGDoppler_NI_110_RR : 16;
        uint64_t CoGRange_NI_110_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_22_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_22_rr_data_t data;
};

union cansig_mk_non_inf_detection_22_rr__azimuth_ni_112_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_112_RR__S1 : 4;
        uint32_t Azimuth_NI_112_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_23_RR_ID       (0x0199)
#define CANMSG_MK_NON_INF_DETECTION_23_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_23_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_23_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_119_RR : 1;
        uint64_t NoInfrastructure_NI_119_RR : 1;
        uint64_t Beam_NI_119_RR : 2;
        uint64_t Azimuth_NI_119_RR : 12;
        uint64_t StdAzimuth_NI_119_RR : 8;
        uint64_t SNRdB_NI_119_RR : 8;
        uint64_t PowerDB_NI_119_RR : 8;
        uint64_t WDoppler10DB_NI_119_RR : 7;
        uint64_t Reserve1_1bit_NI_119_RR : 1;
        uint64_t WRange10DB_NI_119_RR : 8;
        uint64_t CoGDoppler_NI_119_RR : 16;
        uint64_t CoGRange_NI_119_RR : 16;
        uint64_t ValidXBeam_NI_118_RR : 1;
        uint64_t NoInfrastructure_NI_118_RR : 1;
        uint64_t Beam_NI_118_RR : 2;
        uint64_t Azimuth_NI_118_RR : 12;
        uint64_t StdAzimuth_NI_118_RR : 8;
        uint64_t SNRdB_NI_118_RR : 8;
        uint64_t PowerDB_NI_118_RR : 8;
        uint64_t WDoppler10DB_NI_118_RR : 7;
        uint64_t Reserve1_1bit_NI_118_RR : 1;
        uint64_t WRange10DB_NI_118_RR : 8;
        uint64_t CoGDoppler_NI_118_RR : 16;
        uint64_t CoGRange_NI_118_RR : 16;
        uint64_t ValidXBeam_NI_117_RR : 1;
        uint64_t NoInfrastructure_NI_117_RR : 1;
        uint64_t Beam_NI_117_RR : 2;
        uint64_t Azimuth_NI_117_RR__S1 : 4;
        uint64_t Azimuth_NI_117_RR__S0 : 8;
        uint64_t StdAzimuth_NI_117_RR : 8;
        uint64_t SNRdB_NI_117_RR : 8;
        uint64_t PowerDB_NI_117_RR : 8;
        uint64_t WDoppler10DB_NI_117_RR : 7;
        uint64_t Reserve1_1bit_NI_117_RR : 1;
        uint64_t WRange10DB_NI_117_RR : 8;
        uint64_t CoGDoppler_NI_117_RR : 16;
        uint64_t CoGRange_NI_117_RR : 16;
        uint64_t ValidXBeam_NI_116_RR : 1;
        uint64_t NoInfrastructure_NI_116_RR : 1;
        uint64_t Beam_NI_116_RR : 2;
        uint64_t Azimuth_NI_116_RR : 12;
        uint64_t StdAzimuth_NI_116_RR : 8;
        uint64_t SNRdB_NI_116_RR : 8;
        uint64_t PowerDB_NI_116_RR : 8;
        uint64_t WDoppler10DB_NI_116_RR : 7;
        uint64_t Reserve1_1bit_NI_116_RR : 1;
        uint64_t WRange10DB_NI_116_RR : 8;
        uint64_t CoGDoppler_NI_116_RR : 16;
        uint64_t CoGRange_NI_116_RR : 16;
        uint64_t ValidXBeam_NI_115_RR : 1;
        uint64_t NoInfrastructure_NI_115_RR : 1;
        uint64_t Beam_NI_115_RR : 2;
        uint64_t Azimuth_NI_115_RR : 12;
        uint64_t StdAzimuth_NI_115_RR : 8;
        uint64_t SNRdB_NI_115_RR : 8;
        uint64_t PowerDB_NI_115_RR : 8;
        uint64_t WDoppler10DB_NI_115_RR : 7;
        uint64_t Reserve1_1bit_NI_115_RR : 1;
        uint64_t WRange10DB_NI_115_RR : 8;
        uint64_t CoGDoppler_NI_115_RR : 16;
        uint64_t CoGRange_NI_115_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_23_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_23_rr_data_t data;
};

union cansig_mk_non_inf_detection_23_rr__azimuth_ni_117_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_117_RR__S1 : 4;
        uint32_t Azimuth_NI_117_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_24_RR_ID       (0x019A)
#define CANMSG_MK_NON_INF_DETECTION_24_RR_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_24_RR_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_24_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_124_RR : 1;
        uint64_t NoInfrastructure_NI_124_RR : 1;
        uint64_t Beam_NI_124_RR : 2;
        uint64_t Azimuth_NI_124_RR : 12;
        uint64_t StdAzimuth_NI_124_RR : 8;
        uint64_t SNRdB_NI_124_RR : 8;
        uint64_t PowerDB_NI_124_RR : 8;
        uint64_t WDoppler10DB_NI_124_RR : 7;
        uint64_t Reserve1_1bit_NI_124_RR : 1;
        uint64_t WRange10DB_NI_124_RR : 8;
        uint64_t CoGDoppler_NI_124_RR : 16;
        uint64_t CoGRange_NI_124_RR : 16;
        uint64_t ValidXBeam_NI_123_RR : 1;
        uint64_t NoInfrastructure_NI_123_RR : 1;
        uint64_t Beam_NI_123_RR : 2;
        uint64_t Azimuth_NI_123_RR : 12;
        uint64_t StdAzimuth_NI_123_RR : 8;
        uint64_t SNRdB_NI_123_RR : 8;
        uint64_t PowerDB_NI_123_RR : 8;
        uint64_t WDoppler10DB_NI_123_RR : 7;
        uint64_t Reserve1_1bit_NI_123_RR : 1;
        uint64_t WRange10DB_NI_123_RR : 8;
        uint64_t CoGDoppler_NI_123_RR : 16;
        uint64_t CoGRange_NI_123_RR : 16;
        uint64_t ValidXBeam_NI_122_RR : 1;
        uint64_t NoInfrastructure_NI_122_RR : 1;
        uint64_t Beam_NI_122_RR : 2;
        uint64_t Azimuth_NI_122_RR__S1 : 4;
        uint64_t Azimuth_NI_122_RR__S0 : 8;
        uint64_t StdAzimuth_NI_122_RR : 8;
        uint64_t SNRdB_NI_122_RR : 8;
        uint64_t PowerDB_NI_122_RR : 8;
        uint64_t WDoppler10DB_NI_122_RR : 7;
        uint64_t Reserve1_1bit_NI_122_RR : 1;
        uint64_t WRange10DB_NI_122_RR : 8;
        uint64_t CoGDoppler_NI_122_RR : 16;
        uint64_t CoGRange_NI_122_RR : 16;
        uint64_t ValidXBeam_NI_121_RR : 1;
        uint64_t NoInfrastructure_NI_121_RR : 1;
        uint64_t Beam_NI_121_RR : 2;
        uint64_t Azimuth_NI_121_RR : 12;
        uint64_t StdAzimuth_NI_121_RR : 8;
        uint64_t SNRdB_NI_121_RR : 8;
        uint64_t PowerDB_NI_121_RR : 8;
        uint64_t WDoppler10DB_NI_121_RR : 7;
        uint64_t Reserve1_1bit_NI_121_RR : 1;
        uint64_t WRange10DB_NI_121_RR : 8;
        uint64_t CoGDoppler_NI_121_RR : 16;
        uint64_t CoGRange_NI_121_RR : 16;
        uint64_t ValidXBeam_NI_120_RR : 1;
        uint64_t NoInfrastructure_NI_120_RR : 1;
        uint64_t Beam_NI_120_RR : 2;
        uint64_t Azimuth_NI_120_RR : 12;
        uint64_t StdAzimuth_NI_120_RR : 8;
        uint64_t SNRdB_NI_120_RR : 8;
        uint64_t PowerDB_NI_120_RR : 8;
        uint64_t WDoppler10DB_NI_120_RR : 7;
        uint64_t Reserve1_1bit_NI_120_RR : 1;
        uint64_t WRange10DB_NI_120_RR : 8;
        uint64_t CoGDoppler_NI_120_RR : 16;
        uint64_t CoGRange_NI_120_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_24_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_24_rr_data_t data;
};

union cansig_mk_non_inf_detection_24_rr__azimuth_ni_122_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_122_RR__S1 : 4;
        uint32_t Azimuth_NI_122_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_2_RR_ID        (0x0184)
#define CANMSG_MK_NON_INF_DETECTION_2_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_2_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_2_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_14_RR : 1;
        uint64_t NoInfrastructure_NI_14_RR : 1;
        uint64_t Beam_NI_14_RR : 2;
        uint64_t Azimuth_NI_14_RR : 12;
        uint64_t StdAzimuth_NI_14_RR : 8;
        uint64_t SNRdB_NI_14_RR : 8;
        uint64_t PowerDB_NI_14_RR : 8;
        uint64_t WDoppler10DB_NI_14_RR : 7;
        uint64_t Reserve1_1bit_NI_14_RR : 1;
        uint64_t WRange10DB_NI_14_RR : 8;
        uint64_t CoGDoppler_NI_14_RR : 16;
        uint64_t CoGRange_NI_14_RR : 16;
        uint64_t ValidXBeam_NI_13_RR : 1;
        uint64_t NoInfrastructure_NI_13_RR : 1;
        uint64_t Beam_NI_13_RR : 2;
        uint64_t Azimuth_NI_13_RR : 12;
        uint64_t StdAzimuth_NI_13_RR : 8;
        uint64_t SNRdB_NI_13_RR : 8;
        uint64_t PowerDB_NI_13_RR : 8;
        uint64_t WDoppler10DB_NI_13_RR : 7;
        uint64_t Reserve1_1bit_NI_13_RR : 1;
        uint64_t WRange10DB_NI_13_RR : 8;
        uint64_t CoGDoppler_NI_13_RR : 16;
        uint64_t CoGRange_NI_13_RR : 16;
        uint64_t ValidXBeam_NI_12_RR : 1;
        uint64_t NoInfrastructure_NI_12_RR : 1;
        uint64_t Beam_NI_12_RR : 2;
        uint64_t Azimuth_NI_12_RR__S1 : 4;
        uint64_t Azimuth_NI_12_RR__S0 : 8;
        uint64_t StdAzimuth_NI_12_RR : 8;
        uint64_t SNRdB_NI_12_RR : 8;
        uint64_t PowerDB_NI_12_RR : 8;
        uint64_t WDoppler10DB_NI_12_RR : 7;
        uint64_t Reserve1_1bit_NI_12_RR : 1;
        uint64_t WRange10DB_NI_12_RR : 8;
        uint64_t CoGDoppler_NI_12_RR : 16;
        uint64_t CoGRange_NI_12_RR : 16;
        uint64_t ValidXBeam_NI_11_RR : 1;
        uint64_t NoInfrastructure_NI_11_RR : 1;
        uint64_t Beam_NI_11_RR : 2;
        uint64_t Azimuth_NI_11_RR : 12;
        uint64_t StdAzimuth_NI_11_RR : 8;
        uint64_t SNRdB_NI_11_RR : 8;
        uint64_t PowerDB_NI_11_RR : 8;
        uint64_t WDoppler10DB_NI_11_RR : 7;
        uint64_t Reserve1_1bit_NI_11_RR : 1;
        uint64_t WRange10DB_NI_11_RR : 8;
        uint64_t CoGDoppler_NI_11_RR : 16;
        uint64_t CoGRange_NI_11_RR : 16;
        uint64_t ValidXBeam_NI_10_RR : 1;
        uint64_t NoInfrastructure_NI_10_RR : 1;
        uint64_t Beam_NI_10_RR : 2;
        uint64_t Azimuth_NI_10_RR : 12;
        uint64_t StdAzimuth_NI_10_RR : 8;
        uint64_t SNRdB_NI_10_RR : 8;
        uint64_t PowerDB_NI_10_RR : 8;
        uint64_t WDoppler10DB_NI_10_RR : 7;
        uint64_t Reserve1_1bit_NI_10_RR : 1;
        uint64_t WRange10DB_NI_10_RR : 8;
        uint64_t CoGDoppler_NI_10_RR : 16;
        uint64_t CoGRange_NI_10_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_2_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_2_rr_data_t data;
};

union cansig_mk_non_inf_detection_2_rr__azimuth_ni_12_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_12_RR__S1 : 4;
        uint32_t Azimuth_NI_12_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_3_RR_ID        (0x0185)
#define CANMSG_MK_NON_INF_DETECTION_3_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_3_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_3_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_19_RR : 1;
        uint64_t NoInfrastructure_NI_19_RR : 1;
        uint64_t Beam_NI_19_RR : 2;
        uint64_t Azimuth_NI_19_RR : 12;
        uint64_t StdAzimuth_NI_19_RR : 8;
        uint64_t SNRdB_NI_19_RR : 8;
        uint64_t PowerDB_NI_19_RR : 8;
        uint64_t WDoppler10DB_NI_19_RR : 7;
        uint64_t Reserve1_1bit_NI_19_RR : 1;
        uint64_t WRange10DB_NI_19_RR : 8;
        uint64_t CoGDoppler_NI_19_RR : 16;
        uint64_t CoGRange_NI_19_RR : 16;
        uint64_t ValidXBeam_NI_18_RR : 1;
        uint64_t NoInfrastructure_NI_18_RR : 1;
        uint64_t Beam_NI_18_RR : 2;
        uint64_t Azimuth_NI_18_RR : 12;
        uint64_t StdAzimuth_NI_18_RR : 8;
        uint64_t SNRdB_NI_18_RR : 8;
        uint64_t PowerDB_NI_18_RR : 8;
        uint64_t WDoppler10DB_NI_18_RR : 7;
        uint64_t Reserve1_1bit_NI_18_RR : 1;
        uint64_t WRange10DB_NI_18_RR : 8;
        uint64_t CoGDoppler_NI_18_RR : 16;
        uint64_t CoGRange_NI_18_RR : 16;
        uint64_t ValidXBeam_NI_17_RR : 1;
        uint64_t NoInfrastructure_NI_17_RR : 1;
        uint64_t Beam_NI_17_RR : 2;
        uint64_t Azimuth_NI_17_RR__S1 : 4;
        uint64_t Azimuth_NI_17_RR__S0 : 8;
        uint64_t StdAzimuth_NI_17_RR : 8;
        uint64_t SNRdB_NI_17_RR : 8;
        uint64_t PowerDB_NI_17_RR : 8;
        uint64_t WDoppler10DB_NI_17_RR : 7;
        uint64_t Reserve1_1bit_NI_17_RR : 1;
        uint64_t WRange10DB_NI_17_RR : 8;
        uint64_t CoGDoppler_NI_17_RR : 16;
        uint64_t CoGRange_NI_17_RR : 16;
        uint64_t ValidXBeam_NI_16_RR : 1;
        uint64_t NoInfrastructure_NI_16_RR : 1;
        uint64_t Beam_NI_16_RR : 2;
        uint64_t Azimuth_NI_16_RR : 12;
        uint64_t StdAzimuth_NI_16_RR : 8;
        uint64_t SNRdB_NI_16_RR : 8;
        uint64_t PowerDB_NI_16_RR : 8;
        uint64_t WDoppler10DB_NI_16_RR : 7;
        uint64_t Reserve1_1bit_NI_16_RR : 1;
        uint64_t WRange10DB_NI_16_RR : 8;
        uint64_t CoGDoppler_NI_16_RR : 16;
        uint64_t CoGRange_NI_16_RR : 16;
        uint64_t ValidXBeam_NI_15_RR : 1;
        uint64_t NoInfrastructure_NI_15_RR : 1;
        uint64_t Beam_NI_15_RR : 2;
        uint64_t Azimuth_NI_15_RR : 12;
        uint64_t StdAzimuth_NI_15_RR : 8;
        uint64_t SNRdB_NI_15_RR : 8;
        uint64_t PowerDB_NI_15_RR : 8;
        uint64_t WDoppler10DB_NI_15_RR : 7;
        uint64_t Reserve1_1bit_NI_15_RR : 1;
        uint64_t WRange10DB_NI_15_RR : 8;
        uint64_t CoGDoppler_NI_15_RR : 16;
        uint64_t CoGRange_NI_15_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_3_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_3_rr_data_t data;
};

union cansig_mk_non_inf_detection_3_rr__azimuth_ni_17_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_17_RR__S1 : 4;
        uint32_t Azimuth_NI_17_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_4_RR_ID        (0x0186)
#define CANMSG_MK_NON_INF_DETECTION_4_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_4_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_4_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_24_RR : 1;
        uint64_t NoInfrastructure_NI_24_RR : 1;
        uint64_t Beam_NI_24_RR : 2;
        uint64_t Azimuth_NI_24_RR : 12;
        uint64_t StdAzimuth_NI_24_RR : 8;
        uint64_t SNRdB_NI_24_RR : 8;
        uint64_t PowerDB_NI_24_RR : 8;
        uint64_t WDoppler10DB_NI_24_RR : 7;
        uint64_t Reserve1_1bit_NI_24_RR : 1;
        uint64_t WRange10DB_NI_24_RR : 8;
        uint64_t CoGDoppler_NI_24_RR : 16;
        uint64_t CoGRange_NI_24_RR : 16;
        uint64_t ValidXBeam_NI_23_RR : 1;
        uint64_t NoInfrastructure_NI_23_RR : 1;
        uint64_t Beam_NI_23_RR : 2;
        uint64_t Azimuth_NI_23_RR : 12;
        uint64_t StdAzimuth_NI_23_RR : 8;
        uint64_t SNRdB_NI_23_RR : 8;
        uint64_t PowerDB_NI_23_RR : 8;
        uint64_t WDoppler10DB_NI_23_RR : 7;
        uint64_t Reserve1_1bit_NI_23_RR : 1;
        uint64_t WRange10DB_NI_23_RR : 8;
        uint64_t CoGDoppler_NI_23_RR : 16;
        uint64_t CoGRange_NI_23_RR : 16;
        uint64_t ValidXBeam_NI_22_RR : 1;
        uint64_t NoInfrastructure_NI_22_RR : 1;
        uint64_t Beam_NI_22_RR : 2;
        uint64_t Azimuth_NI_22_RR__S1 : 4;
        uint64_t Azimuth_NI_22_RR__S0 : 8;
        uint64_t StdAzimuth_NI_22_RR : 8;
        uint64_t SNRdB_NI_22_RR : 8;
        uint64_t PowerDB_NI_22_RR : 8;
        uint64_t WDoppler10DB_NI_22_RR : 7;
        uint64_t Reserve1_1bit_NI_22_RR : 1;
        uint64_t WRange10DB_NI_22_RR : 8;
        uint64_t CoGDoppler_NI_22_RR : 16;
        uint64_t CoGRange_NI_22_RR : 16;
        uint64_t ValidXBeam_NI_21_RR : 1;
        uint64_t NoInfrastructure_NI_21_RR : 1;
        uint64_t Beam_NI_21_RR : 2;
        uint64_t Azimuth_NI_21_RR : 12;
        uint64_t StdAzimuth_NI_21_RR : 8;
        uint64_t SNRdB_NI_21_RR : 8;
        uint64_t PowerDB_NI_21_RR : 8;
        uint64_t WDoppler10DB_NI_21_RR : 7;
        uint64_t Reserve1_1bit_NI_21_RR : 1;
        uint64_t WRange10DB_NI_21_RR : 8;
        uint64_t CoGDoppler_NI_21_RR : 16;
        uint64_t CoGRange_NI_21_RR : 16;
        uint64_t ValidXBeam_NI_20_RR : 1;
        uint64_t NoInfrastructure_NI_20_RR : 1;
        uint64_t Beam_NI_20_RR : 2;
        uint64_t Azimuth_NI_20_RR : 12;
        uint64_t StdAzimuth_NI_20_RR : 8;
        uint64_t SNRdB_NI_20_RR : 8;
        uint64_t PowerDB_NI_20_RR : 8;
        uint64_t WDoppler10DB_NI_20_RR : 7;
        uint64_t Reserve1_1bit_NI_20_RR : 1;
        uint64_t WRange10DB_NI_20_RR : 8;
        uint64_t CoGDoppler_NI_20_RR : 16;
        uint64_t CoGRange_NI_20_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_4_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_4_rr_data_t data;
};

union cansig_mk_non_inf_detection_4_rr__azimuth_ni_22_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_22_RR__S1 : 4;
        uint32_t Azimuth_NI_22_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_5_RR_ID        (0x0187)
#define CANMSG_MK_NON_INF_DETECTION_5_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_5_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_5_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_29_RR : 1;
        uint64_t NoInfrastructure_NI_29_RR : 1;
        uint64_t Beam_NI_29_RR : 2;
        uint64_t Azimuth_NI_29_RR : 12;
        uint64_t StdAzimuth_NI_29_RR : 8;
        uint64_t SNRdB_NI_29_RR : 8;
        uint64_t PowerDB_NI_29_RR : 8;
        uint64_t WDoppler10DB_NI_29_RR : 7;
        uint64_t Reserve1_1bit_NI_29_RR : 1;
        uint64_t WRange10DB_NI_29_RR : 8;
        uint64_t CoGDoppler_NI_29_RR : 16;
        uint64_t CoGRange_NI_29_RR : 16;
        uint64_t ValidXBeam_NI_28_RR : 1;
        uint64_t NoInfrastructure_NI_28_RR : 1;
        uint64_t Beam_NI_28_RR : 2;
        uint64_t Azimuth_NI_28_RR : 12;
        uint64_t StdAzimuth_NI_28_RR : 8;
        uint64_t SNRdB_NI_28_RR : 8;
        uint64_t PowerDB_NI_28_RR : 8;
        uint64_t WDoppler10DB_NI_28_RR : 7;
        uint64_t Reserve1_1bit_NI_28_RR : 1;
        uint64_t WRange10DB_NI_28_RR : 8;
        uint64_t CoGDoppler_NI_28_RR : 16;
        uint64_t CoGRange_NI_28_RR : 16;
        uint64_t ValidXBeam_NI_27_RR : 1;
        uint64_t NoInfrastructure_NI_27_RR : 1;
        uint64_t Beam_NI_27_RR : 2;
        uint64_t Azimuth_NI_27_RR__S1 : 4;
        uint64_t Azimuth_NI_27_RR__S0 : 8;
        uint64_t StdAzimuth_NI_27_RR : 8;
        uint64_t SNRdB_NI_27_RR : 8;
        uint64_t PowerDB_NI_27_RR : 8;
        uint64_t WDoppler10DB_NI_27_RR : 7;
        uint64_t Reserve1_1bit_NI_27_RR : 1;
        uint64_t WRange10DB_NI_27_RR : 8;
        uint64_t CoGDoppler_NI_27_RR : 16;
        uint64_t CoGRange_NI_27_RR : 16;
        uint64_t ValidXBeam_NI_26_RR : 1;
        uint64_t NoInfrastructure_NI_26_RR : 1;
        uint64_t Beam_NI_26_RR : 2;
        uint64_t Azimuth_NI_26_RR : 12;
        uint64_t StdAzimuth_NI_26_RR : 8;
        uint64_t SNRdB_NI_26_RR : 8;
        uint64_t PowerDB_NI_26_RR : 8;
        uint64_t WDoppler10DB_NI_26_RR : 7;
        uint64_t Reserve1_1bit_NI_26_RR : 1;
        uint64_t WRange10DB_NI_26_RR : 8;
        uint64_t CoGDoppler_NI_26_RR : 16;
        uint64_t CoGRange_NI_26_RR : 16;
        uint64_t ValidXBeam_NI_25_RR : 1;
        uint64_t NoInfrastructure_NI_25_RR : 1;
        uint64_t Beam_NI_25_RR : 2;
        uint64_t Azimuth_NI_25_RR : 12;
        uint64_t StdAzimuth_NI_25_RR : 8;
        uint64_t SNRdB_NI_25_RR : 8;
        uint64_t PowerDB_NI_25_RR : 8;
        uint64_t WDoppler10DB_NI_25_RR : 7;
        uint64_t Reserve1_1bit_NI_25_RR : 1;
        uint64_t WRange10DB_NI_25_RR : 8;
        uint64_t CoGDoppler_NI_25_RR : 16;
        uint64_t CoGRange_NI_25_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_5_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_5_rr_data_t data;
};

union cansig_mk_non_inf_detection_5_rr__azimuth_ni_27_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_27_RR__S1 : 4;
        uint32_t Azimuth_NI_27_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_6_RR_ID        (0x0188)
#define CANMSG_MK_NON_INF_DETECTION_6_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_6_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_6_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_34_RR : 1;
        uint64_t NoInfrastructure_NI_34_RR : 1;
        uint64_t Beam_NI_34_RR : 2;
        uint64_t Azimuth_NI_34_RR : 12;
        uint64_t StdAzimuth_NI_34_RR : 8;
        uint64_t SNRdB_NI_34_RR : 8;
        uint64_t PowerDB_NI_34_RR : 8;
        uint64_t WDoppler10DB_NI_34_RR : 7;
        uint64_t Reserve1_1bit_NI_34_RR : 1;
        uint64_t WRange10DB_NI_34_RR : 8;
        uint64_t CoGDoppler_NI_34_RR : 16;
        uint64_t CoGRange_NI_34_RR : 16;
        uint64_t ValidXBeam_NI_33_RR : 1;
        uint64_t NoInfrastructure_NI_33_RR : 1;
        uint64_t Beam_NI_33_RR : 2;
        uint64_t Azimuth_NI_33_RR : 12;
        uint64_t StdAzimuth_NI_33_RR : 8;
        uint64_t SNRdB_NI_33_RR : 8;
        uint64_t PowerDB_NI_33_RR : 8;
        uint64_t WDoppler10DB_NI_33_RR : 7;
        uint64_t Reserve1_1bit_NI_33_RR : 1;
        uint64_t WRange10DB_NI_33_RR : 8;
        uint64_t CoGDoppler_NI_33_RR : 16;
        uint64_t CoGRange_NI_33_RR : 16;
        uint64_t ValidXBeam_NI_32_RR : 1;
        uint64_t NoInfrastructure_NI_32_RR : 1;
        uint64_t Beam_NI_32_RR : 2;
        uint64_t Azimuth_NI_32_RR__S1 : 4;
        uint64_t Azimuth_NI_32_RR__S0 : 8;
        uint64_t StdAzimuth_NI_32_RR : 8;
        uint64_t SNRdB_NI_32_RR : 8;
        uint64_t PowerDB_NI_32_RR : 8;
        uint64_t WDoppler10DB_NI_32_RR : 7;
        uint64_t Reserve1_1bit_NI_32_RR : 1;
        uint64_t WRange10DB_NI_32_RR : 8;
        uint64_t CoGDoppler_NI_32_RR : 16;
        uint64_t CoGRange_NI_32_RR : 16;
        uint64_t ValidXBeam_NI_31_RR : 1;
        uint64_t NoInfrastructure_NI_31_RR : 1;
        uint64_t Beam_NI_31_RR : 2;
        uint64_t Azimuth_NI_31_RR : 12;
        uint64_t StdAzimuth_NI_31_RR : 8;
        uint64_t SNRdB_NI_31_RR : 8;
        uint64_t PowerDB_NI_31_RR : 8;
        uint64_t WDoppler10DB_NI_31_RR : 7;
        uint64_t Reserve1_1bit_NI_31_RR : 1;
        uint64_t WRange10DB_NI_31_RR : 8;
        uint64_t CoGDoppler_NI_31_RR : 16;
        uint64_t CoGRange_NI_31_RR : 16;
        uint64_t ValidXBeam_NI_30_RR : 1;
        uint64_t NoInfrastructure_NI_30_RR : 1;
        uint64_t Beam_NI_30_RR : 2;
        uint64_t Azimuth_NI_30_RR : 12;
        uint64_t StdAzimuth_NI_30_RR : 8;
        uint64_t SNRdB_NI_30_RR : 8;
        uint64_t PowerDB_NI_30_RR : 8;
        uint64_t WDoppler10DB_NI_30_RR : 7;
        uint64_t Reserve1_1bit_NI_30_RR : 1;
        uint64_t WRange10DB_NI_30_RR : 8;
        uint64_t CoGDoppler_NI_30_RR : 16;
        uint64_t CoGRange_NI_30_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_6_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_6_rr_data_t data;
};

union cansig_mk_non_inf_detection_6_rr__azimuth_ni_32_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_32_RR__S1 : 4;
        uint32_t Azimuth_NI_32_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_7_RR_ID        (0x0189)
#define CANMSG_MK_NON_INF_DETECTION_7_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_7_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_7_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_39_RR : 1;
        uint64_t NoInfrastructure_NI_39_RR : 1;
        uint64_t Beam_NI_39_RR : 2;
        uint64_t Azimuth_NI_39_RR : 12;
        uint64_t StdAzimuth_NI_39_RR : 8;
        uint64_t SNRdB_NI_39_RR : 8;
        uint64_t PowerDB_NI_39_RR : 8;
        uint64_t WDoppler10DB_NI_39_RR : 7;
        uint64_t Reserve1_1bit_NI_39_RR : 1;
        uint64_t WRange10DB_NI_39_RR : 8;
        uint64_t CoGDoppler_NI_39_RR : 16;
        uint64_t CoGRange_NI_39_RR : 16;
        uint64_t ValidXBeam_NI_38_RR : 1;
        uint64_t NoInfrastructure_NI_38_RR : 1;
        uint64_t Beam_NI_38_RR : 2;
        uint64_t Azimuth_NI_38_RR : 12;
        uint64_t StdAzimuth_NI_38_RR : 8;
        uint64_t SNRdB_NI_38_RR : 8;
        uint64_t PowerDB_NI_38_RR : 8;
        uint64_t WDoppler10DB_NI_38_RR : 7;
        uint64_t Reserve1_1bit_NI_38_RR : 1;
        uint64_t WRange10DB_NI_38_RR : 8;
        uint64_t CoGDoppler_NI_38_RR : 16;
        uint64_t CoGRange_NI_38_RR : 16;
        uint64_t ValidXBeam_NI_37_RR : 1;
        uint64_t NoInfrastructure_NI_37_RR : 1;
        uint64_t Beam_NI_37_RR : 2;
        uint64_t Azimuth_NI_37_RR__S1 : 4;
        uint64_t Azimuth_NI_37_RR__S0 : 8;
        uint64_t StdAzimuth_NI_37_RR : 8;
        uint64_t SNRdB_NI_37_RR : 8;
        uint64_t PowerDB_NI_37_RR : 8;
        uint64_t WDoppler10DB_NI_37_RR : 7;
        uint64_t Reserve1_1bit_NI_37_RR : 1;
        uint64_t WRange10DB_NI_37_RR : 8;
        uint64_t CoGDoppler_NI_37_RR : 16;
        uint64_t CoGRange_NI_37_RR : 16;
        uint64_t ValidXBeam_NI_36_RR : 1;
        uint64_t NoInfrastructure_NI_36_RR : 1;
        uint64_t Beam_NI_36_RR : 2;
        uint64_t Azimuth_NI_36_RR : 12;
        uint64_t StdAzimuth_NI_36_RR : 8;
        uint64_t SNRdB_NI_36_RR : 8;
        uint64_t PowerDB_NI_36_RR : 8;
        uint64_t WDoppler10DB_NI_36_RR : 7;
        uint64_t Reserve1_1bit_NI_36_RR : 1;
        uint64_t WRange10DB_NI_36_RR : 8;
        uint64_t CoGDoppler_NI_36_RR : 16;
        uint64_t CoGRange_NI_36_RR : 16;
        uint64_t ValidXBeam_NI_35_RR : 1;
        uint64_t NoInfrastructure_NI_35_RR : 1;
        uint64_t Beam_NI_35_RR : 2;
        uint64_t Azimuth_NI_35_RR : 12;
        uint64_t StdAzimuth_NI_35_RR : 8;
        uint64_t SNRdB_NI_35_RR : 8;
        uint64_t PowerDB_NI_35_RR : 8;
        uint64_t WDoppler10DB_NI_35_RR : 7;
        uint64_t Reserve1_1bit_NI_35_RR : 1;
        uint64_t WRange10DB_NI_35_RR : 8;
        uint64_t CoGDoppler_NI_35_RR : 16;
        uint64_t CoGRange_NI_35_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_7_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_7_rr_data_t data;
};

union cansig_mk_non_inf_detection_7_rr__azimuth_ni_37_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_37_RR__S1 : 4;
        uint32_t Azimuth_NI_37_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_8_RR_ID        (0x018A)
#define CANMSG_MK_NON_INF_DETECTION_8_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_8_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_8_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_44_RR : 1;
        uint64_t NoInfrastructure_NI_44_RR : 1;
        uint64_t Beam_NI_44_RR : 2;
        uint64_t Azimuth_NI_44_RR : 12;
        uint64_t StdAzimuth_NI_44_RR : 8;
        uint64_t SNRdB_NI_44_RR : 8;
        uint64_t PowerDB_NI_44_RR : 8;
        uint64_t WDoppler10DB_NI_44_RR : 7;
        uint64_t Reserve1_1bit_NI_44_RR : 1;
        uint64_t WRange10DB_NI_44_RR : 8;
        uint64_t CoGDoppler_NI_44_RR : 16;
        uint64_t CoGRange_NI_44_RR : 16;
        uint64_t ValidXBeam_NI_43_RR : 1;
        uint64_t NoInfrastructure_NI_43_RR : 1;
        uint64_t Beam_NI_43_RR : 2;
        uint64_t Azimuth_NI_43_RR : 12;
        uint64_t StdAzimuth_NI_43_RR : 8;
        uint64_t SNRdB_NI_43_RR : 8;
        uint64_t PowerDB_NI_43_RR : 8;
        uint64_t WDoppler10DB_NI_43_RR : 7;
        uint64_t Reserve1_1bit_NI_43_RR : 1;
        uint64_t WRange10DB_NI_43_RR : 8;
        uint64_t CoGDoppler_NI_43_RR : 16;
        uint64_t CoGRange_NI_43_RR : 16;
        uint64_t ValidXBeam_NI_42_RR : 1;
        uint64_t NoInfrastructure_NI_42_RR : 1;
        uint64_t Beam_NI_42_RR : 2;
        uint64_t Azimuth_NI_42_RR__S1 : 4;
        uint64_t Azimuth_NI_42_RR__S0 : 8;
        uint64_t StdAzimuth_NI_42_RR : 8;
        uint64_t SNRdB_NI_42_RR : 8;
        uint64_t PowerDB_NI_42_RR : 8;
        uint64_t WDoppler10DB_NI_42_RR : 7;
        uint64_t Reserve1_1bit_NI_42_RR : 1;
        uint64_t WRange10DB_NI_42_RR : 8;
        uint64_t CoGDoppler_NI_42_RR : 16;
        uint64_t CoGRange_NI_42_RR : 16;
        uint64_t ValidXBeam_NI_41_RR : 1;
        uint64_t NoInfrastructure_NI_41_RR : 1;
        uint64_t Beam_NI_41_RR : 2;
        uint64_t Azimuth_NI_41_RR : 12;
        uint64_t StdAzimuth_NI_41_RR : 8;
        uint64_t SNRdB_NI_41_RR : 8;
        uint64_t PowerDB_NI_41_RR : 8;
        uint64_t WDoppler10DB_NI_41_RR : 7;
        uint64_t Reserve1_1bit_NI_41_RR : 1;
        uint64_t WRange10DB_NI_41_RR : 8;
        uint64_t CoGDoppler_NI_41_RR : 16;
        uint64_t CoGRange_NI_41_RR : 16;
        uint64_t ValidXBeam_NI_40_RR : 1;
        uint64_t NoInfrastructure_NI_40_RR : 1;
        uint64_t Beam_NI_40_RR : 2;
        uint64_t Azimuth_NI_40_RR : 12;
        uint64_t StdAzimuth_NI_40_RR : 8;
        uint64_t SNRdB_NI_40_RR : 8;
        uint64_t PowerDB_NI_40_RR : 8;
        uint64_t WDoppler10DB_NI_40_RR : 7;
        uint64_t Reserve1_1bit_NI_40_RR : 1;
        uint64_t WRange10DB_NI_40_RR : 8;
        uint64_t CoGDoppler_NI_40_RR : 16;
        uint64_t CoGRange_NI_40_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_8_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_8_rr_data_t data;
};

union cansig_mk_non_inf_detection_8_rr__azimuth_ni_42_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_42_RR__S1 : 4;
        uint32_t Azimuth_NI_42_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_9_RR_ID        (0x018B)
#define CANMSG_MK_NON_INF_DETECTION_9_RR_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_9_RR_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_9_rr_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_49_RR : 1;
        uint64_t NoInfrastructure_NI_49_RR : 1;
        uint64_t Beam_NI_49_RR : 2;
        uint64_t Azimuth_NI_49_RR : 12;
        uint64_t StdAzimuth_NI_49_RR : 8;
        uint64_t SNRdB_NI_49_RR : 8;
        uint64_t PowerDB_NI_49_RR : 8;
        uint64_t WDoppler10DB_NI_49_RR : 7;
        uint64_t Reserve1_1bit_NI_49_RR : 1;
        uint64_t WRange10DB_NI_49_RR : 8;
        uint64_t CoGDoppler_NI_49_RR : 16;
        uint64_t CoGRange_NI_49_RR : 16;
        uint64_t ValidXBeam_NI_48_RR : 1;
        uint64_t NoInfrastructure_NI_48_RR : 1;
        uint64_t Beam_NI_48_RR : 2;
        uint64_t Azimuth_NI_48_RR : 12;
        uint64_t StdAzimuth_NI_48_RR : 8;
        uint64_t SNRdB_NI_48_RR : 8;
        uint64_t PowerDB_NI_48_RR : 8;
        uint64_t WDoppler10DB_NI_48_RR : 7;
        uint64_t Reserve1_1bit_NI_48_RR : 1;
        uint64_t WRange10DB_NI_48_RR : 8;
        uint64_t CoGDoppler_NI_48_RR : 16;
        uint64_t CoGRange_NI_48_RR : 16;
        uint64_t ValidXBeam_NI_47_RR : 1;
        uint64_t NoInfrastructure_NI_47_RR : 1;
        uint64_t Beam_NI_47_RR : 2;
        uint64_t Azimuth_NI_47_RR__S1 : 4;
        uint64_t Azimuth_NI_47_RR__S0 : 8;
        uint64_t StdAzimuth_NI_47_RR : 8;
        uint64_t SNRdB_NI_47_RR : 8;
        uint64_t PowerDB_NI_47_RR : 8;
        uint64_t WDoppler10DB_NI_47_RR : 7;
        uint64_t Reserve1_1bit_NI_47_RR : 1;
        uint64_t WRange10DB_NI_47_RR : 8;
        uint64_t CoGDoppler_NI_47_RR : 16;
        uint64_t CoGRange_NI_47_RR : 16;
        uint64_t ValidXBeam_NI_46_RR : 1;
        uint64_t NoInfrastructure_NI_46_RR : 1;
        uint64_t Beam_NI_46_RR : 2;
        uint64_t Azimuth_NI_46_RR : 12;
        uint64_t StdAzimuth_NI_46_RR : 8;
        uint64_t SNRdB_NI_46_RR : 8;
        uint64_t PowerDB_NI_46_RR : 8;
        uint64_t WDoppler10DB_NI_46_RR : 7;
        uint64_t Reserve1_1bit_NI_46_RR : 1;
        uint64_t WRange10DB_NI_46_RR : 8;
        uint64_t CoGDoppler_NI_46_RR : 16;
        uint64_t CoGRange_NI_46_RR : 16;
        uint64_t ValidXBeam_NI_45_RR : 1;
        uint64_t NoInfrastructure_NI_45_RR : 1;
        uint64_t Beam_NI_45_RR : 2;
        uint64_t Azimuth_NI_45_RR : 12;
        uint64_t StdAzimuth_NI_45_RR : 8;
        uint64_t SNRdB_NI_45_RR : 8;
        uint64_t PowerDB_NI_45_RR : 8;
        uint64_t WDoppler10DB_NI_45_RR : 7;
        uint64_t Reserve1_1bit_NI_45_RR : 1;
        uint64_t WRange10DB_NI_45_RR : 8;
        uint64_t CoGDoppler_NI_45_RR : 16;
        uint64_t CoGRange_NI_45_RR : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_9_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_9_rr_data_t data;
};

union cansig_mk_non_inf_detection_9_rr__azimuth_ni_47_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_47_RR__S1 : 4;
        uint32_t Azimuth_NI_47_RR__S0 : 8;
    } fields;
};

#define CANMSG_MK_TARGET_DETECTION_HEADER_RR_ID    (0x0181)
#define CANMSG_MK_TARGET_DETECTION_HEADER_RR_DLC   (48)
#define CANMSG_MK_TARGET_DETECTION_HEADER_RR_MIN_DLC (48)
union canmsg_mk_target_detection_header_rr_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t InterferenceExceeded_RR : 8;
        uint64_t PAD2 : 8;
        uint64_t numFreespaceDetections_RR : 2;
        uint64_t DetectionListVersion_RR : 6;
        uint64_t numInfrastructureDetected_RR : 8;
        uint64_t numNonInfraDetected_RR : 8;
        uint64_t numCmplxValPerDetectionBeam3_RR : 8;
        uint64_t numCmplxValPerDetectionBeam2_RR : 8;
        uint64_t numCmplxValPerDetectionBeam1_RR : 8;
        uint64_t UnambiguousVelMeas3_RR : 8;
        uint64_t UnambiguousVelMeas2_RR : 8;
        uint64_t UnambiguousVelMeas1_RR : 8;
        uint64_t FC1MHz3_RR : 16;
        uint64_t FC1MHz2_RR : 16;
        uint64_t FC1MHz1_RR : 16;
        uint64_t HostYawEst_RR : 16;
        uint64_t HostAccelLatEst_RR : 16;
        uint64_t HostAccelLongEst_RR : 16;
        uint64_t HostVelEst_RR : 16;
        uint64_t BW100KHz3_RR : 16;
        uint64_t BW100KHz2_RR : 16;
        uint64_t BW100KHz1_RR : 16;
        uint64_t TimeStamp_RR : 32;
        uint64_t CycleNumber_RR : 32;
    } signals;
};

struct canmsg_mk_target_detection_header_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_target_detection_header_rr_data_t data;
};

//=====================================================================================//


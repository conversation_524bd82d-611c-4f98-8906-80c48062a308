#include "parser_typedef.h"
#pragma once

#if 0
//--------------//
extern void CAN_SetCanParserLockHandler(fpParserLockHandler_t func);
extern void CAN_SetCanParserUnlockHandler(fpParserUnlockHandler_t func);
extern void CAN_SetMsgCounterCalcHandler(fpMsgCounterCalcHandler_t func);
extern void CAN_SetMsgChecksumCalcHandler(fpMsgChecksumCalcHandler_t func);
extern void CAN_SetMsgChecksumVerityHandler(fpMsgChecksumVerityHandler_t func);
extern void CAN_SetMsgChangeHandler(fpMsgChangeHandler_t func);
extern void CAN_SetMsgChecksumErrorHandler(fpMsgChecksumErrorHandler_t func);
extern void CAN_SetMsgCounterErrorHandler(fpMsgCounterErrorHandler_t func);
extern void CAN_SetMsgTimeoutHandler(fpMsgTimeoutHandler_t func);
extern void CAN_SetMsgDlcHandler(fpMsgDlcHandler_t func);
extern void CAN_SetMsgOutRangeHandler(fpMsgOutRangeHandler_t func);
extern void CAN_SetSigChangeHandler(fpSigChangeHandler_t func);
extern void CAN_SetSigOnWriteHandler(fpSigOnWriteHandler_t func);
extern void CAN_SetSignalChangedHook(fpSignalChangedHook_t func);
extern void CAN_SetSignalSetCallBack(fpSignalTriggerCallBack_t func);
extern void CAN_SetSignalGetCallBack(fpSignalTriggerCallBack_t func);
//=====================================================================================//
extern struct veh_message *CAN_RxMessageList[27];

extern struct veh_message *CAN_TxMessageList[1];

extern struct veh_signal* CAN_ALL_Signal_Array[];
#endif
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Beam_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Beam_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Beam_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Beam_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Beam_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_0_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_1_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_2_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_3_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_4_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Beam_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Beam_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Beam_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Beam_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Beam_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_50_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_51_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_52_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_53_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_54_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Beam_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Beam_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Beam_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Beam_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Beam_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_55_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_56_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_57_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_58_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_59_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Beam_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Beam_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Beam_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Beam_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Beam_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_60_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_61_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_62_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_63_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_64_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Beam_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Beam_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Beam_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Beam_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Beam_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_65_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_66_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_67_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_68_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_69_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Beam_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Beam_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Beam_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Beam_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Beam_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_70_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_71_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_72_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_73_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_74_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Beam_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Beam_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Beam_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Beam_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Beam_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_75_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_76_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_77_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_78_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_79_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Beam_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Beam_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Beam_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Beam_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Beam_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_80_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_81_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_82_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_83_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_84_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Beam_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Beam_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Beam_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Beam_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Beam_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_85_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_86_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_87_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_88_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_89_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Beam_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Beam_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Beam_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Beam_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Beam_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_90_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_91_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_92_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_93_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_94_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Beam_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Beam_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Beam_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Beam_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Beam_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_95_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_96_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_97_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_98_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_99_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Beam_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Beam_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Beam_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Beam_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Beam_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_5_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_6_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_7_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_8_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_9_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Beam_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Beam_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Beam_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Beam_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Beam_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_100_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_101_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_102_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_103_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_104_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Beam_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Beam_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Beam_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Beam_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Beam_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_105_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_106_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_107_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_108_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_109_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Beam_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Beam_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Beam_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Beam_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Beam_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_110_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_111_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_112_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_113_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_114_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Beam_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Beam_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Beam_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Beam_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Beam_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_115_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_116_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_117_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_118_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_119_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Beam_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Beam_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Beam_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Beam_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Beam_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_120_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_121_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_122_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_123_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_124_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Beam_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Beam_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Beam_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Beam_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Beam_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_10_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_11_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_12_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_13_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_14_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Beam_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Beam_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Beam_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Beam_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Beam_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_15_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_16_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_17_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_18_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_19_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Beam_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Beam_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Beam_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Beam_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Beam_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_20_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_21_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_22_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_23_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_24_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Beam_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Beam_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Beam_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Beam_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Beam_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_25_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_26_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_27_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_28_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_29_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Beam_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Beam_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Beam_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Beam_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Beam_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_30_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_31_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_32_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_33_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_34_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Beam_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Beam_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Beam_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Beam_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Beam_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_35_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_36_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_37_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_38_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_39_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Beam_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Beam_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Beam_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Beam_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Beam_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_40_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_41_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_42_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_43_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_44_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Beam_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Beam_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Beam_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Beam_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Beam_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_45_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_46_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_47_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_48_RR_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_49_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__BW100KHz1_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__BW100KHz2_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__BW100KHz3_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__CycleNumber_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__DetectionListVersion_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__FC1MHz1_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__FC1MHz2_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__FC1MHz3_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__HostAccelLatEst_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__HostAccelLongEst_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__HostVelEst_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__HostYawEst_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__InterferenceExceeded_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__TimeStamp_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__UnambiguousVelMeas1_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__UnambiguousVelMeas2_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__UnambiguousVelMeas3_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__numCmplxValPerDetectionBeam1_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__numCmplxValPerDetectionBeam2_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__numCmplxValPerDetectionBeam3_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__numFreespaceDetections_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__numInfrastructureDetected_RR_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RR__numNonInfraDetected_RR_g;

extern void CAN_SetRawMessage_MK_TARGET_DETECTION_HEADER_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_0_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_1_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_2_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_3_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_4_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_5_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_6_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_7_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_8_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_9_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_10_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_11_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_12_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_13_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_14_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_15_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_16_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_17_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_18_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_19_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_20_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_21_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_22_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_23_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_24_RR(uint8_t *values, uint32_t length);

extern void CAN_ResetMessage_MK_TARGET_DETECTION_HEADER_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_0_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_1_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_2_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_3_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_4_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_5_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_6_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_7_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_8_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_9_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_10_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_11_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_12_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_13_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_14_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_15_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_16_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_17_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_18_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_19_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_20_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_21_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_22_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_23_RR(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_24_RR(enum reset_flg flags);

#if 0
extern void CAN_MessageElapseTime(int bus_id, int time_ms, int restart);
#endif


extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_0_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_10_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_11_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_12_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_13_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_14_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_15_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_16_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_17_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_18_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_19_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_1_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_20_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_21_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_22_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_23_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_24_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_2_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_3_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_4_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_5_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_6_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_7_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_8_RR(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_9_RR(void);
extern void CAN_Message_RDLock_MK_TARGET_DETECTION_HEADER_RR(void);

extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_0_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_10_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_11_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_12_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_13_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_14_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_15_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_16_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_17_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_18_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_19_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_1_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_20_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_21_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_22_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_23_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_24_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_2_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_3_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_4_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_5_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_6_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_7_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_8_RR(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_9_RR(void);
extern void CAN_Message_WRLock_MK_TARGET_DETECTION_HEADER_RR(void);

extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_0_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_10_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_11_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_12_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_13_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_14_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_15_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_16_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_17_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_18_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_19_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_1_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_20_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_21_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_22_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_23_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_24_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_2_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_3_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_4_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_5_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_6_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_7_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_8_RR(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_9_RR(void);
extern void CAN_Message_Unlock_MK_TARGET_DETECTION_HEADER_RR(void);



extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Azimuth_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Beam_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Beam_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Beam_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Beam_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Beam_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGDoppler_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__CoGRange_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__NoInfrastructure_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__PowerDB_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__Reserve1_1bit_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__SNRdB_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__StdAzimuth_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__ValidXBeam_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WDoppler10DB_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_0_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RR__WRange10DB_NI_4_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Azimuth_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Beam_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Beam_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Beam_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Beam_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Beam_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGDoppler_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__CoGRange_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__NoInfrastructure_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__PowerDB_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__Reserve1_1bit_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__SNRdB_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__StdAzimuth_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__ValidXBeam_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WDoppler10DB_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_50_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_51_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_52_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_53_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RR__WRange10DB_NI_54_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Azimuth_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Beam_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Beam_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Beam_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Beam_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Beam_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGDoppler_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__CoGRange_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__NoInfrastructure_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__PowerDB_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__Reserve1_1bit_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__SNRdB_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__StdAzimuth_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__ValidXBeam_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WDoppler10DB_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_55_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_56_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_57_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_58_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RR__WRange10DB_NI_59_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Azimuth_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Beam_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Beam_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Beam_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Beam_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Beam_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGDoppler_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__CoGRange_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__NoInfrastructure_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__PowerDB_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__Reserve1_1bit_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__SNRdB_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__StdAzimuth_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__ValidXBeam_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WDoppler10DB_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_60_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_61_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_62_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_63_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RR__WRange10DB_NI_64_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Azimuth_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Beam_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Beam_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Beam_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Beam_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Beam_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGDoppler_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__CoGRange_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__NoInfrastructure_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__PowerDB_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__Reserve1_1bit_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__SNRdB_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__StdAzimuth_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__ValidXBeam_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WDoppler10DB_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_65_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_66_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_67_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_68_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RR__WRange10DB_NI_69_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Azimuth_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Beam_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Beam_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Beam_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Beam_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Beam_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGDoppler_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__CoGRange_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__NoInfrastructure_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__PowerDB_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__Reserve1_1bit_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__SNRdB_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__StdAzimuth_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__ValidXBeam_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WDoppler10DB_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_70_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_71_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_72_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_73_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RR__WRange10DB_NI_74_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Azimuth_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Beam_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Beam_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Beam_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Beam_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Beam_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGDoppler_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__CoGRange_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__NoInfrastructure_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__PowerDB_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__Reserve1_1bit_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__SNRdB_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__StdAzimuth_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__ValidXBeam_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WDoppler10DB_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_75_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_76_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_77_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_78_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RR__WRange10DB_NI_79_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Azimuth_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Beam_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Beam_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Beam_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Beam_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Beam_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGDoppler_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__CoGRange_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__NoInfrastructure_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__PowerDB_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__Reserve1_1bit_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__SNRdB_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__StdAzimuth_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__ValidXBeam_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WDoppler10DB_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_80_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_81_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_82_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_83_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RR__WRange10DB_NI_84_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Azimuth_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Beam_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Beam_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Beam_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Beam_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Beam_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGDoppler_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__CoGRange_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__NoInfrastructure_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__PowerDB_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__Reserve1_1bit_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__SNRdB_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__StdAzimuth_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__ValidXBeam_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WDoppler10DB_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_85_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_86_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_87_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_88_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RR__WRange10DB_NI_89_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Azimuth_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Beam_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Beam_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Beam_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Beam_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Beam_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGDoppler_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__CoGRange_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__NoInfrastructure_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__PowerDB_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__Reserve1_1bit_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__SNRdB_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__StdAzimuth_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__ValidXBeam_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WDoppler10DB_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_90_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_91_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_92_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_93_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RR__WRange10DB_NI_94_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Azimuth_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Beam_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Beam_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Beam_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Beam_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Beam_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGDoppler_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__CoGRange_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__NoInfrastructure_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__PowerDB_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__Reserve1_1bit_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__SNRdB_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__StdAzimuth_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__ValidXBeam_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WDoppler10DB_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_95_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_96_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_97_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_98_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RR__WRange10DB_NI_99_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Azimuth_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Beam_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Beam_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Beam_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Beam_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Beam_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGDoppler_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__CoGRange_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__NoInfrastructure_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__PowerDB_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__Reserve1_1bit_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__SNRdB_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__StdAzimuth_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__ValidXBeam_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WDoppler10DB_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_5_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_6_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_7_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_8_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RR__WRange10DB_NI_9_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Azimuth_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Beam_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Beam_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Beam_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Beam_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Beam_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGDoppler_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__CoGRange_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__NoInfrastructure_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__PowerDB_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__Reserve1_1bit_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__SNRdB_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__StdAzimuth_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__ValidXBeam_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WDoppler10DB_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_100_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_101_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_102_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_103_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RR__WRange10DB_NI_104_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Azimuth_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Beam_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Beam_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Beam_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Beam_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Beam_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGDoppler_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__CoGRange_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__NoInfrastructure_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__PowerDB_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__Reserve1_1bit_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__SNRdB_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__StdAzimuth_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__ValidXBeam_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WDoppler10DB_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_105_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_106_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_107_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_108_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RR__WRange10DB_NI_109_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Azimuth_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Beam_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Beam_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Beam_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Beam_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Beam_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGDoppler_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__CoGRange_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__NoInfrastructure_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__PowerDB_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__Reserve1_1bit_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__SNRdB_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__StdAzimuth_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__ValidXBeam_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WDoppler10DB_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_110_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_111_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_112_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_113_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RR__WRange10DB_NI_114_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Azimuth_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Beam_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Beam_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Beam_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Beam_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Beam_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGDoppler_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__CoGRange_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__NoInfrastructure_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__PowerDB_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__Reserve1_1bit_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__SNRdB_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__StdAzimuth_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__ValidXBeam_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WDoppler10DB_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_115_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_116_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_117_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_118_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RR__WRange10DB_NI_119_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Azimuth_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Beam_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Beam_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Beam_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Beam_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Beam_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGDoppler_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__CoGRange_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__NoInfrastructure_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__PowerDB_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__Reserve1_1bit_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__SNRdB_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__StdAzimuth_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__ValidXBeam_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WDoppler10DB_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_120_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_121_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_122_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_123_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RR__WRange10DB_NI_124_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Azimuth_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Beam_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Beam_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Beam_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Beam_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Beam_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGDoppler_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__CoGRange_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__NoInfrastructure_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__PowerDB_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__Reserve1_1bit_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__SNRdB_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__StdAzimuth_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__ValidXBeam_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WDoppler10DB_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_10_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_11_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_12_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_13_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RR__WRange10DB_NI_14_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Azimuth_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Beam_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Beam_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Beam_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Beam_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Beam_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGDoppler_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__CoGRange_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__NoInfrastructure_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__PowerDB_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__Reserve1_1bit_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__SNRdB_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__StdAzimuth_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__ValidXBeam_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WDoppler10DB_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_15_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_16_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_17_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_18_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RR__WRange10DB_NI_19_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Azimuth_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Beam_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Beam_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Beam_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Beam_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Beam_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGDoppler_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__CoGRange_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__NoInfrastructure_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__PowerDB_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__Reserve1_1bit_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__SNRdB_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__StdAzimuth_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__ValidXBeam_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WDoppler10DB_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_20_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_21_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_22_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_23_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RR__WRange10DB_NI_24_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Azimuth_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Beam_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Beam_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Beam_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Beam_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Beam_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGDoppler_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__CoGRange_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__NoInfrastructure_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__PowerDB_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__Reserve1_1bit_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__SNRdB_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__StdAzimuth_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__ValidXBeam_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WDoppler10DB_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_25_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_26_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_27_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_28_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RR__WRange10DB_NI_29_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Azimuth_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Beam_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Beam_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Beam_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Beam_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Beam_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGDoppler_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__CoGRange_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__NoInfrastructure_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__PowerDB_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__Reserve1_1bit_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__SNRdB_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__StdAzimuth_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__ValidXBeam_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WDoppler10DB_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_30_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_31_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_32_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_33_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RR__WRange10DB_NI_34_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Azimuth_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Beam_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Beam_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Beam_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Beam_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Beam_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGDoppler_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__CoGRange_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__NoInfrastructure_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__PowerDB_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__Reserve1_1bit_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__SNRdB_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__StdAzimuth_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__ValidXBeam_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WDoppler10DB_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_35_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_36_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_37_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_38_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RR__WRange10DB_NI_39_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Azimuth_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Beam_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Beam_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Beam_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Beam_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Beam_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGDoppler_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__CoGRange_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__NoInfrastructure_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__PowerDB_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__Reserve1_1bit_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__SNRdB_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__StdAzimuth_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__ValidXBeam_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WDoppler10DB_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_40_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_41_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_42_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_43_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RR__WRange10DB_NI_44_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Azimuth_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Beam_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Beam_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Beam_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Beam_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Beam_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGDoppler_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__CoGRange_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__NoInfrastructure_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__PowerDB_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__Reserve1_1bit_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__SNRdB_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__StdAzimuth_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__ValidXBeam_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WDoppler10DB_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_45_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_46_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_47_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_48_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RR__WRange10DB_NI_49_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__BW100KHz1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__BW100KHz2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__BW100KHz3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__CycleNumber_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__DetectionListVersion_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__FC1MHz1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__FC1MHz2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__FC1MHz3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__HostAccelLatEst_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__HostAccelLongEst_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__HostVelEst_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__HostYawEst_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__InterferenceExceeded_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__TimeStamp_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__UnambiguousVelMeas1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__UnambiguousVelMeas2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__UnambiguousVelMeas3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__numCmplxValPerDetectionBeam1_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__numCmplxValPerDetectionBeam2_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__numCmplxValPerDetectionBeam3_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__numFreespaceDetections_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__numInfrastructureDetected_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RR__numNonInfraDetected_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);

extern void MK_RR_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
#if 0
extern void CAN_PARSER_Init(void);
extern void CAN_PARSER_MSG_Init(struct veh_message *p_message_list[]);
#endif

//=====================================================================================//



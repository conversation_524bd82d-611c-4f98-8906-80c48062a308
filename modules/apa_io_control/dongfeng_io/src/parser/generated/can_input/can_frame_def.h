#include "parser_typedef.h"
#pragma once
//=====================================================================================//
#define CANMSG_EPS_166_ID                          (0x0166)
#define CANMSG_EPS_166_DLC                         (8)
#define CANMSG_EPS_166_MIN_DLC                     (8)
union canmsg_eps_166_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 43;
        uint64_t EPS_LKA_DriverOverrideSt : 1;
        uint64_t EPS_LKA_TorqueLimitationSt : 1;
        uint64_t EPS_LKASt : 3;
        uint64_t EPS_LKAOverlayCurrent : 16;
    } signals;
};

struct canmsg_eps_166_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_eps_166_data_t data;
};

#define CANMSG_EPS_167_ID                          (0x0167)
#define CANMSG_EPS_167_DLC                         (8)
#define CANMSG_EPS_167_MIN_DLC                     (8)
union canmsg_eps_167_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 42;
        uint64_t EPS_DriverInitializedESASt : 1;
        uint64_t EPS_ESA_DriverOverrideSt : 1;
        uint64_t EPS_ESA_TorqueLimitationSt : 1;
        uint64_t EPS_ESASt : 3;
        uint64_t EPS_ESACurrentOverlay : 16;
    } signals;
};

struct canmsg_eps_167_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_eps_167_data_t data;
};

#define CANMSG_EPS_17A_ID                          (0x017A)
#define CANMSG_EPS_17A_DLC                         (8)
#define CANMSG_EPS_17A_MIN_DLC                     (8)
union canmsg_eps_17a_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t EPS_CheckSum17A : 8;
        uint64_t EPS_RollingCount17A : 4;
        uint64_t EPS_SteeringTorqueValue : 12;
        uint64_t ICAN_17A_Reserve_2 : 8;
        uint64_t EPS_SteeringAngle : 16;
        uint64_t ICAN_17A_Reserve_1 : 5;
        uint64_t EPS_SteeringHoldSt : 1;
        uint64_t EPS_SteeringAngleFlag : 1;
        uint64_t EPS_SteeringTorqueSensorSt : 1;
        uint64_t ICAN_17A_Reserve_0 : 8;
    } signals;
};

struct canmsg_eps_17a_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_eps_17a_data_t data;
};

#define CANMSG_EPS_A5_ID                           (0x00A5)
#define CANMSG_EPS_A5_DLC                          (8)
#define CANMSG_EPS_A5_MIN_DLC                      (8)
union canmsg_eps_a5_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t TAS_SAS_CheckSumA5 : 8;
        uint64_t TAS_SAS_RollingCountA5 : 4;
        uint64_t ICAN_0A5_Reserve_0 : 25;
        uint64_t TAS_SAS_TrimmingSt : 1;
        uint64_t TAS_SAS_CalibratedSt : 1;
        uint64_t TAS_SAS_SASFailureSt : 1;
        uint64_t TAS_SAS_SteeringRotSpd : 8;
        uint64_t TAS_SAS_SteeringAngle : 16;
    } signals;
};

struct canmsg_eps_a5_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_eps_a5_data_t data;
};

#define CANMSG_EPS_B0_ID                           (0x00B0)
#define CANMSG_EPS_B0_DLC                          (8)
#define CANMSG_EPS_B0_MIN_DLC                      (8)
union canmsg_eps_b0_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 40;
        uint64_t EPS_APAProhibitedResaon : 3;
        uint64_t EPS_APAFuncModeSt : 2;
        uint64_t EPS_APASt : 2;
        uint64_t ICAN_0B0_Reserve_1 : 1;
        uint64_t EPS_APAChecksumB0 : 8;
        uint64_t EPS_APARollingCountB0 : 4;
        uint64_t ICAN_0B0_Reserve_0 : 4;
    } signals;
};

struct canmsg_eps_b0_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_eps_b0_data_t data;
};

#define CANMSG_IBC_101_ID                          (0x0101)
#define CANMSG_IBC_101_DLC                         (12)
#define CANMSG_IBC_101_MIN_DLC                     (12)
union canmsg_ibc_101_data_t
{
    uint8_t buffer[12];
    struct
    {
        uint64_t Checksum101 : 8;
        uint64_t RollingCounter101 : 4;
        uint64_t ICAN_101_Reserve_3__S1 : 20;
        uint64_t ICAN_101_Reserve_3__S0 : 2;
        uint64_t IBC_wheelSpeedFRValid : 1;
        uint64_t IBC_wheelSpeedFR : 13;
        uint64_t ICAN_101_Reserve_2 : 2;
        uint64_t IBC_wheelSpeedFLValid : 1;
        uint64_t IBC_wheelSpeedFL : 13;
        uint64_t ICAN_101_Reserve_1 : 2;
        uint64_t IBC_wheelSpeedRRValid : 1;
        uint64_t IBC_wheelSpeedRR : 13;
        uint64_t ICAN_101_Reserve_0 : 2;
        uint64_t IBC_wheelSpeedRLValid : 1;
        uint64_t IBC_wheelSpeedRL : 13;
    }__attribute__((packed)) signals;
};

struct canmsg_ibc_101_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_101_data_t data;
};

union cansig_ibc_101__ican_101_reserve_3_data_t
{
    uint32_t val;
    struct
    {
        uint32_t ICAN_101_Reserve_3__S1 : 20;
        uint32_t ICAN_101_Reserve_3__S0 : 2;
    } fields;
};

#define CANMSG_IBC_11D_ID                          (0x011D)
#define CANMSG_IBC_11D_DLC                         (8)
#define CANMSG_IBC_11D_MIN_DLC                     (7)
union canmsg_ibc_11d_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 12;
        uint64_t IBC_PedalTravelSensor : 8;
        uint64_t IBC_PedalTravelSensorSt : 1;
        uint64_t PAD1 : 19;
        uint64_t IBC_PlungerPressure : 12;
    } signals;
};

struct canmsg_ibc_11d_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_11d_data_t data;
};

#define CANMSG_IBC_12C_ID                          (0x012C)
#define CANMSG_IBC_12C_DLC                         (8)
#define CANMSG_IBC_12C_MIN_DLC                     (9)
union canmsg_ibc_12c_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum12C : 8;
        uint64_t RollingCounter12C : 4;
        uint64_t ICAN_12C_Reserve_4 : 6;
        uint64_t IBC_PbcActuatorLeftSt : 3;
        uint64_t IBC_PbcActuatorRightSt : 3;
        uint64_t IBC_SlopHighWarn : 2;
        uint64_t ICAN_12C_Reserve_3 : 2;
        uint64_t IBC_EPBErrorSt : 2;
        uint64_t IBC_Slope : 6;
        uint64_t IBC_SlopeSt : 1;
        uint64_t ICAN_12C_Reserve_2 : 13;
        uint64_t IBC_EPBSt : 3;
        uint64_t IBC_EPBSwValiditySt : 1;
        uint64_t IBC_EPBSwSt : 2;
        uint64_t ICAN_12C_Reserve_1 : 6;
        uint64_t IBC_EPBCruiseControlCancelSt : 1;
        uint64_t ICAN_12C_Reserve_0 : 3;
    } signals;
};

struct canmsg_ibc_12c_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_12c_data_t data;
};

#define CANMSG_IBC_133_ID                          (0x0133)
#define CANMSG_IBC_133_DLC                         (8)
#define CANMSG_IBC_133_MIN_DLC                     (8)
union canmsg_ibc_133_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum133 : 8;
        uint64_t RollingCounter133 : 4;
        uint64_t IBC_EBPActiveSt : 1;
        uint64_t ICAN_133_Reserve_4 : 2;
        uint64_t IBC_SCMActiveSt : 1;
        uint64_t ICAN_133_Reserve_3 : 3;
        uint64_t IBC_CSTSt : 2;
        uint64_t ICAN_133_Reserve_2 : 19;
        uint64_t IBC_BrkFricTotAtWhlsTorq : 14;
        uint64_t IBC_FWAWarn : 2;
        uint64_t ICAN_133_Reserve_1 : 1;
        uint64_t IBC_BrakePedalSt : 2;
        uint64_t ICAN_133_Reserve_0 : 2;
        uint64_t IBC_SystemSt : 3;
    } signals;
};

struct canmsg_ibc_133_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_133_data_t data;
};

#define CANMSG_IBC_143_ID                          (0x0143)
#define CANMSG_IBC_143_DLC                         (8)
#define CANMSG_IBC_143_MIN_DLC                     (8)
union canmsg_ibc_143_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum143 : 8;
        uint64_t RollingCounter143 : 4;
        uint64_t ICAN_143_Reserve_2 : 8;
        uint64_t IBC_APAGearReqActiveSt : 1;
        uint64_t IBC_TargetGearReq : 3;
        uint64_t ICAN_143_Reserve_1 : 2;
        uint64_t IBC_WHEEL_TRQ_APATReq : 14;
        uint64_t IBC_LSMCtrlFaultSt : 4;
        uint64_t IBC_APABrakSysLongictlSt : 4;
        uint64_t IBC_VlcInterTargetAx : 8;
        uint64_t IBC_APABrakeModeSt : 2;
        uint64_t IBC_PDCUCtrlReq : 1;
        uint64_t ICAN_143_Reserve_0 : 5;
    } signals;
};

struct canmsg_ibc_143_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_143_data_t data;
};

#define CANMSG_IBC_172_ID                          (0x0172)
#define CANMSG_IBC_172_DLC                         (12)
#define CANMSG_IBC_172_MIN_DLC                     (12)
union canmsg_ibc_172_data_t
{
    uint8_t buffer[12];
    struct
    {
        uint64_t Checksum172 : 8;
        uint64_t RollingCounter172 : 4;
        uint64_t ICAN_172_Reserve_0 : 4;
        uint64_t IBC_WheelDirectionRRSt : 2;
        uint64_t IBC_WheelDirectionRLSt : 2;
        uint64_t IBC_WheelDirectionRRValid : 1;
        uint64_t IBC_WheelDirectionRLValid : 1;
        uint64_t IBC_SumOfEdgeRRWSSValid : 1;
        uint64_t IBC_SumOfEdgeRLWSSValid : 1;
        uint64_t IBC_SumOfEdgeRRWSS__S1 : 8;
        uint64_t IBC_SumOfEdgeRRWSS__S0 : 8;
        uint64_t IBC_SumOfEdgeRLWSS : 16;
        uint64_t IBC_WheelDirectionFRSt : 2;
        uint64_t IBC_WheelDirectionFLSt : 2;
        uint64_t IBC_WheelDirectionFRValid : 1;
        uint64_t IBC_WheelDirectionFLValid : 1;
        uint64_t IBC_SumOfEdgeFRWSSValid : 1;
        uint64_t IBC_SumOfEdgeFLWSSValid : 1;
        uint64_t IBC_SumOfEdgeFRWSS : 16;
        uint64_t IBC_SumOfEdgeFLWSS : 16;
    }__attribute__((packed)) signals;
};

struct canmsg_ibc_172_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_172_data_t data;
};

union cansig_ibc_172__ibc_sumofedgerrwss_data_t
{
    uint32_t val;
    struct
    {
        uint32_t IBC_SumOfEdgeRRWSS__S1 : 8;
        uint32_t IBC_SumOfEdgeRRWSS__S0 : 8;
    } fields;
};

#define CANMSG_IBC_A2_ID                           (0x00A2)
#define CANMSG_IBC_A2_DLC                          (8)
#define CANMSG_IBC_A2_MIN_DLC                      (8)
union canmsg_ibc_a2_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum0A2 : 8;
        uint64_t RollingCounter0A2 : 4;
        uint64_t ICAN_0A2_Reserve_4 : 2;
        uint64_t IBC_VehicleHoldSt : 1;
        uint64_t ICAN_0A2_Reserve_3 : 1;
        uint64_t IBC_ESCFaultSt : 1;
        uint64_t IBC_HAZActiveSt : 1;
        uint64_t ICAN_0A2_Reserve_2 : 1;
        uint64_t IBC_HBAActiveSt : 1;
        uint64_t IBC_DTCActiveSt : 1;
        uint64_t IBC_ESCLampDisp : 2;
        uint64_t ICAN_0A2_Reserve_1 : 1;
        uint64_t IBC_HDCSt : 3;
        uint64_t IBC_HHCActiveSt : 1;
        uint64_t IBC_HHCAvailableSt : 1;
        uint64_t IBC_EBDFaultSt : 1;
        uint64_t IBC_EBDActiveSt : 1;
        uint64_t IBC_ESCoffSt : 1;
        uint64_t IBC_VehicleDrivingDirection : 2;
        uint64_t IBC_VehicleStandstillSt : 2;
        uint64_t IBC_VehicleStandstillValid : 1;
        uint64_t IBC_ModeSt : 1;
        uint64_t IBC_BrakelightReq : 1;
        uint64_t ICAN_0A2_Reserve_0 : 1;
        uint64_t IBC_VDCActiveSt : 1;
        uint64_t IBC_TCSActiveSt : 1;
        uint64_t IBC_AVHSt : 2;
        uint64_t IBC_AVHAvailableSt : 1;
        uint64_t IBC_CDPActiveSt : 1;
        uint64_t IBC_CDPAvailableSt : 1;
        uint64_t IBC_HDCAvailableSt : 1;
        uint64_t IBC_ABSActiveSt : 1;
        uint64_t IBC_ABSFaultStSt : 1;
        uint64_t IBC_VehicleSpeedValid : 1;
        uint64_t IBC_VehicleSpeed : 13;
    } signals;
};

struct canmsg_ibc_a2_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ibc_a2_data_t data;
};

#define CANMSG_IVI_182_ID                          (0x0182)
#define CANMSG_IVI_182_DLC                         (8)
#define CANMSG_IVI_182_MIN_DLC                     (8)
union canmsg_ivi_182_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 44;
        uint64_t HAD_CAMERA_TrqMotorMinLimit : 10;
        uint64_t HAD_CAMERA_TrqMotorMaxLimit : 10;
    } signals;
};

struct canmsg_ivi_182_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ivi_182_data_t data;
};

#define CANMSG_IVI_183_ID                          (0x0183)
#define CANMSG_IVI_183_DLC                         (8)
#define CANMSG_IVI_183_MIN_DLC                     (8)
union canmsg_ivi_183_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t CheckSum183 : 8;
        uint64_t RollingCounte183 : 4;
        uint64_t PAD0 : 9;
        uint64_t HAD_CAMERA_ESATorqueReq : 11;
        uint64_t PAD1 : 1;
        uint64_t HAD_CAMERA_ESAReq : 1;
        uint64_t HAD_CAMERA_LKAAngleReq : 14;
        uint64_t PAD2 : 1;
        uint64_t HAD_CAMERA_LKATorqFactReq : 7;
        uint64_t HAD_CAMERA_LKAActivation : 2;
        uint64_t HAD_CAMERA_LKASt : 3;
        uint64_t HAD_CAMERA_ESASt : 3;
    } signals;
};

struct canmsg_ivi_183_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ivi_183_data_t data;
};

#define CANMSG_IVI_1F3_ID                          (0x01F3)
#define CANMSG_IVI_1F3_DLC                         (16)
#define CANMSG_IVI_1F3_MIN_DLC                     (16)
union canmsg_ivi_1f3_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t HAD_CAMERA_Checksum1F3 : 8;
        uint64_t HAD_CAMERA_RollingCount1F3 : 4;
        uint64_t HAD_CAMERA_AWBAlertReq : 2;
        uint64_t PAD0 : 15;
        uint64_t HAD_CAMERA_LKAdbRem : 11;
        uint64_t HAD_CAMERA_FCWSt : 3;
        uint64_t HAD_CAMERA_HBAReq : 1;
        uint64_t HAD_CAMERA_ACCAccelModeSt : 5;
        uint64_t HAD_CAMERA_ACCObjFlagSt : 1;
        uint64_t HAD_CAMERA_ACCInternalSt : 6;
        uint64_t HAD_CAMERA_ACCObjectTime__S1 : 8;
        uint64_t HAD_CAMERA_ACCObjectTime__S0 : 16;
        uint64_t HAD_CAMERA_ACCObjectID : 8;
        uint64_t HAD_CAMERA_PowerWhlTqActReq : 2;
        uint64_t HAD_CAMERA_ACCTorqueReq : 14;
        uint64_t PAD1 : 1;
        uint64_t HAD_CAMERA_DecelCtrlReq : 1;
        uint64_t HAD_CAMERA_DriveOffReq : 1;
        uint64_t HAD_CAMERA_PrefillReq : 1;
        uint64_t HAD_CAMERA_AEBBrakeSt : 1;
        uint64_t HAD_CAMERA_DeceltTypeSt : 2;
        uint64_t HAD_CAMERA_IBCHoldReq : 1;
        uint64_t HAD_CAMERA_DecelSetReq : 8;
        uint64_t HAD_CAMERA_ACCEPBReq : 1;
        uint64_t HAD_CAMERA_AEBSt : 3;
        uint64_t HAD_CAMERA_AEBdbRem : 3;
        uint64_t HAD_CAMERA_BrakeReq : 1;
    } signals;
};

struct canmsg_ivi_1f3_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ivi_1f3_data_t data;
};

union cansig_ivi_1f3__had_camera_accobjecttime_data_t
{
    uint32_t val;
    struct
    {
        uint32_t HAD_CAMERA_ACCObjectTime__S1 : 8;
        uint32_t HAD_CAMERA_ACCObjectTime__S0 : 16;
    } fields;
};

#define CANMSG_IVI_1F5_ID                          (0x01F5)
#define CANMSG_IVI_1F5_DLC                         (8)
#define CANMSG_IVI_1F5_MIN_DLC                     (8)
union canmsg_ivi_1f5_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 37;
        uint64_t HAD_CAMERA_TargetPosSt : 3;
        uint64_t HAD_CAMERA_ACCLateralDst : 8;
        uint64_t HAD_CAMERA_ACCLongitudialDst : 8;
        uint64_t HAD_CAMERA_HMITargetDisp : 1;
        uint64_t HAD_CAMERA_FDMSt : 3;
        uint64_t HAD_CAMERA_ACCSt : 4;
    } signals;
};

struct canmsg_ivi_1f5_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ivi_1f5_data_t data;
};

#define CANMSG_IVI_3A7_ID                          (0x03A7)
#define CANMSG_IVI_3A7_DLC                         (20)
#define CANMSG_IVI_3A7_MIN_DLC                     (20)
union canmsg_ivi_3a7_data_t
{
    uint8_t buffer[20];
    struct
    {
        uint64_t IVI_ImuChecksum : 8;
        uint64_t IVI_ImuRoll : 4;
        uint64_t IVI_ImuSt : 2;
        uint64_t PAD0 : 26;
        uint64_t IVI_GyroZ : 20;
        uint64_t IVI_AccelZ : 20;
        uint64_t IVI_GyroY__S1 : 16;
        uint64_t IVI_GyroY__S0 : 4;
        uint64_t IVI_AccelY : 20;
        uint64_t IVI_GyroX : 20;
        uint64_t IVI_AccelX : 20;
    } signals;
};

struct canmsg_ivi_3a7_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_ivi_3a7_data_t data;
};

union cansig_ivi_3a7__ivi_gyroy_data_t
{
    uint32_t val;
    struct
    {
        uint32_t IVI_GyroY__S1 : 16;
        uint32_t IVI_GyroY__S0 : 4;
    } fields;
};

#define CANMSG_OBJ_EOT_FC_ID                       (0x0340)
#define CANMSG_OBJ_EOT_FC_DLC                      (4)
#define CANMSG_OBJ_EOT_FC_MIN_DLC                  (4)
union canmsg_obj_eot_fc_data_t
{
    uint8_t buffer[4];
    struct
    {
        uint64_t MCC_FC : 32;
    }__attribute__((packed)) signals;
};

struct canmsg_obj_eot_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_eot_fc_data_t data;
};

#define CANMSG_OBJ_EOT_FL_ID                       (0x0040)
#define CANMSG_OBJ_EOT_FL_DLC                      (4)
#define CANMSG_OBJ_EOT_FL_MIN_DLC                  (4)
union canmsg_obj_eot_fl_data_t
{
    uint8_t buffer[4];
    struct
    {
        uint64_t MCC_FL : 32;
    }__attribute__((packed)) signals;
};

struct canmsg_obj_eot_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_eot_fl_data_t data;
};

#define CANMSG_OBJ_EOT_FR_ID                       (0x0240)
#define CANMSG_OBJ_EOT_FR_DLC                      (4)
#define CANMSG_OBJ_EOT_FR_MIN_DLC                  (4)
union canmsg_obj_eot_fr_data_t
{
    uint8_t buffer[4];
    struct
    {
        uint64_t MCC_FR : 32;
    }__attribute__((packed)) signals;
};

struct canmsg_obj_eot_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_eot_fr_data_t data;
};

#define CANMSG_OBJ_EOT_RL_ID                       (0x0440)
#define CANMSG_OBJ_EOT_RL_DLC                      (4)
#define CANMSG_OBJ_EOT_RL_MIN_DLC                  (4)
union canmsg_obj_eot_rl_data_t
{
    uint8_t buffer[4];
    struct
    {
        uint64_t MCC_RL : 32;
    }__attribute__((packed)) signals;
};

struct canmsg_obj_eot_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_eot_rl_data_t data;
};

#define CANMSG_OBJ_EOT_RR_ID                       (0x0640)
#define CANMSG_OBJ_EOT_RR_DLC                      (4)
#define CANMSG_OBJ_EOT_RR_MIN_DLC                  (4)
union canmsg_obj_eot_rr_data_t
{
    uint8_t buffer[4];
    struct
    {
        uint64_t MCC_RR : 32;
    }__attribute__((packed)) signals;
};

struct canmsg_obj_eot_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_eot_rr_data_t data;
};

#define CANMSG_OBJ_HEADER_FC_ID                    (0x0341)
#define CANMSG_OBJ_HEADER_FC_DLC                   (16)
#define CANMSG_OBJ_HEADER_FC_MIN_DLC               (16)
union canmsg_obj_header_fc_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t CalibrationModeFlag_FC : 8;
        uint64_t Duration_Tme_POST_Process_FC : 16;
        uint64_t Valid_FC : 2;
        uint64_t NumObjects_FC : 6;
        uint64_t OBJ_CycleNumber_FC__S1 : 16;
        uint64_t OBJ_CycleNumber_FC__S0 : 16;
        uint64_t OBJ_TimeStamp_FC : 32;
        uint64_t Version_FC : 16;
    } signals;
};

struct canmsg_obj_header_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_header_fc_data_t data;
};

union cansig_obj_header_fc__obj_cyclenumber_fc_data_t
{
    uint32_t val;
    struct
    {
        uint32_t OBJ_CycleNumber_FC__S1 : 16;
        uint32_t OBJ_CycleNumber_FC__S0 : 16;
    } fields;
};

#define CANMSG_OBJ_HEADER_FL_ID                    (0x0041)
#define CANMSG_OBJ_HEADER_FL_DLC                   (16)
#define CANMSG_OBJ_HEADER_FL_MIN_DLC               (16)
union canmsg_obj_header_fl_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t CalibrationModeFlag_FL : 8;
        uint64_t Duration_Tme_POST_Process_FL : 16;
        uint64_t Valid_FL : 2;
        uint64_t NumObjects_FL : 6;
        uint64_t OBJ_CycleNumber_FL__S1 : 16;
        uint64_t OBJ_CycleNumber_FL__S0 : 16;
        uint64_t OBJ_TimeStamp_FL : 32;
        uint64_t Version_FL : 16;
    } signals;
};

struct canmsg_obj_header_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_header_fl_data_t data;
};

union cansig_obj_header_fl__obj_cyclenumber_fl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t OBJ_CycleNumber_FL__S1 : 16;
        uint32_t OBJ_CycleNumber_FL__S0 : 16;
    } fields;
};

#define CANMSG_OBJ_HEADER_FR_ID                    (0x0241)
#define CANMSG_OBJ_HEADER_FR_DLC                   (16)
#define CANMSG_OBJ_HEADER_FR_MIN_DLC               (16)
union canmsg_obj_header_fr_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t CalibrationModeFlag_FR : 8;
        uint64_t Duration_Tme_POST_Process_FR : 16;
        uint64_t Valid_FR : 2;
        uint64_t NumObjects_FR : 6;
        uint64_t OBJ_CycleNumber_FR__S1 : 16;
        uint64_t OBJ_CycleNumber_FR__S0 : 16;
        uint64_t OBJ_TimeStamp_FR : 32;
        uint64_t Version_FR : 16;
    } signals;
};

struct canmsg_obj_header_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_header_fr_data_t data;
};

union cansig_obj_header_fr__obj_cyclenumber_fr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t OBJ_CycleNumber_FR__S1 : 16;
        uint32_t OBJ_CycleNumber_FR__S0 : 16;
    } fields;
};

#define CANMSG_OBJ_HEADER_RL_ID                    (0x0441)
#define CANMSG_OBJ_HEADER_RL_DLC                   (16)
#define CANMSG_OBJ_HEADER_RL_MIN_DLC               (16)
union canmsg_obj_header_rl_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t CalibrationModeFlag_RL : 8;
        uint64_t Duration_Tme_POST_Process_RL : 16;
        uint64_t Valid_RL : 2;
        uint64_t NumObjects_RL : 6;
        uint64_t OBJ_CycleNumber_RL__S1 : 16;
        uint64_t OBJ_CycleNumber_RL__S0 : 16;
        uint64_t OBJ_TimeStamp_RL : 32;
        uint64_t Version_RL : 16;
    } signals;
};

struct canmsg_obj_header_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_header_rl_data_t data;
};

union cansig_obj_header_rl__obj_cyclenumber_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t OBJ_CycleNumber_RL__S1 : 16;
        uint32_t OBJ_CycleNumber_RL__S0 : 16;
    } fields;
};

#define CANMSG_OBJ_HEADER_RR_ID                    (0x0641)
#define CANMSG_OBJ_HEADER_RR_DLC                   (16)
#define CANMSG_OBJ_HEADER_RR_MIN_DLC               (16)
union canmsg_obj_header_rr_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t CalibrationModeFlag_RR : 8;
        uint64_t Duration_Tme_POST_Process_RR : 16;
        uint64_t Valid_RR : 2;
        uint64_t NumObjects_RR : 6;
        uint64_t OBJ_CycleNumber_RR__S1 : 16;
        uint64_t OBJ_CycleNumber_RR__S0 : 16;
        uint64_t OBJ_TimeStamp_RR : 32;
        uint64_t Version_RR : 16;
    } signals;
};

struct canmsg_obj_header_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_header_rr_data_t data;
};

union cansig_obj_header_rr__obj_cyclenumber_rr_data_t
{
    uint32_t val;
    struct
    {
        uint32_t OBJ_CycleNumber_RR__S1 : 16;
        uint32_t OBJ_CycleNumber_RR__S0 : 16;
    } fields;
};

#define CANMSG_OBJ_LIST_YY_FC_ID                   (0x0342)
#define CANMSG_OBJ_LIST_YY_FC_DLC                  (48)
#define CANMSG_OBJ_LIST_YY_FC_MIN_DLC              (48)
union canmsg_obj_list_yy_fc_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 16;
        uint64_t Cov_v_YY_FC : 16;
        uint64_t Cov_a_YY_FC : 16;
        uint64_t Cov_xy_YY_FC : 16;
        uint64_t Cov_y_YY_FC : 16;
        uint64_t Cov_x_YY_FC : 16;
        uint64_t Cov_YawRate_YY_FC : 16;
        uint64_t YawRate_rad_s_YY_FC : 16;
        uint64_t Cov_Orientation_YY_FC : 16;
        uint64_t Orientation_rad_YY_FC : 16;
        uint64_t ExistenceProbability_YY_FC : 8;
        uint64_t TrackAge_YY_FC : 8;
        uint64_t Cov_Half_Width_YY_FC : 16;
        uint64_t Width_YY_FC : 16;
        uint64_t Cov_Half_FCength_YY_FC : 16;
        uint64_t Length_YY_FC : 16;
        uint64_t v_m_s_YY_FC : 16;
        uint64_t a_m_s_s_YY_FC : 16;
        uint64_t y_m_YY_FC : 16;
        uint64_t x_m_YY_FC : 16;
        uint64_t Object_ID_YY_FC : 8;
        uint64_t Frame_ID_YY_FC : 8;
    } signals;
};

struct canmsg_obj_list_yy_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_list_yy_fc_data_t data;
};

#define CANMSG_OBJ_LIST_YY_FL_ID                   (0x0042)
#define CANMSG_OBJ_LIST_YY_FL_DLC                  (48)
#define CANMSG_OBJ_LIST_YY_FL_MIN_DLC              (48)
union canmsg_obj_list_yy_fl_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 16;
        uint64_t Cov_v_YY_FL : 16;
        uint64_t Cov_a_YY_FL : 16;
        uint64_t Cov_xy_YY_FL : 16;
        uint64_t Cov_y_YY_FL : 16;
        uint64_t Cov_x_YY_FL : 16;
        uint64_t Cov_YawRate_YY_FL : 16;
        uint64_t YawRate_rad_s_YY_FL : 16;
        uint64_t Cov_Orientation_YY_FL : 16;
        uint64_t Orientation_rad_YY_FL : 16;
        uint64_t ExistenceProbability_YY_FL : 8;
        uint64_t TrackAge_YY_FL : 8;
        uint64_t Cov_Half_Width_YY_FL : 16;
        uint64_t Width_YY_FL : 16;
        uint64_t Cov_Half_Length_YY_FL : 16;
        uint64_t Length_YY_FL : 16;
        uint64_t v_m_s_YY_FL : 16;
        uint64_t a_m_s_s_YY_FL : 16;
        uint64_t y_m_YY_FL : 16;
        uint64_t x_m_YY_FL : 16;
        uint64_t Object_ID_YY_FL : 8;
        uint64_t Frame_ID_YY_FL : 8;
    } signals;
};

struct canmsg_obj_list_yy_fl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_list_yy_fl_data_t data;
};

#define CANMSG_OBJ_LIST_YY_FR_ID                   (0x0242)
#define CANMSG_OBJ_LIST_YY_FR_DLC                  (48)
#define CANMSG_OBJ_LIST_YY_FR_MIN_DLC              (48)
union canmsg_obj_list_yy_fr_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 16;
        uint64_t Cov_v_YY_FR : 16;
        uint64_t Cov_a_YY_FR : 16;
        uint64_t Cov_xy_YY_FR : 16;
        uint64_t Cov_y_YY_FR : 16;
        uint64_t Cov_x_YY_FR : 16;
        uint64_t Cov_YawRate_YY_FR : 16;
        uint64_t YawRate_rad_s_YY_FR : 16;
        uint64_t Cov_Orientation_YY_FR : 16;
        uint64_t Orientation_rad_YY_FR : 16;
        uint64_t ExistenceProbability_YY_FR : 8;
        uint64_t TrackAge_YY_FR : 8;
        uint64_t Cov_Half_Width_YY_FR : 16;
        uint64_t Width_YY_FR : 16;
        uint64_t Cov_Half_Length_YY_FR : 16;
        uint64_t Length_YY_FR : 16;
        uint64_t v_m_s_YY_FR : 16;
        uint64_t a_m_s_s_YY_FR : 16;
        uint64_t y_m_YY_FR : 16;
        uint64_t x_m_YY_FR : 16;
        uint64_t Object_ID_YY_FR : 8;
        uint64_t Frame_ID_YY_FR : 8;
    } signals;
};

struct canmsg_obj_list_yy_fr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_list_yy_fr_data_t data;
};

#define CANMSG_OBJ_LIST_YY_RL_ID                   (0x0442)
#define CANMSG_OBJ_LIST_YY_RL_DLC                  (48)
#define CANMSG_OBJ_LIST_YY_RL_MIN_DLC              (48)
union canmsg_obj_list_yy_rl_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 16;
        uint64_t Cov_v_YY_RL : 16;
        uint64_t Cov_a_YY_RL : 16;
        uint64_t Cov_xy_YY_RL : 16;
        uint64_t Cov_y_YY_RL : 16;
        uint64_t Cov_x_YY_RL : 16;
        uint64_t Cov_YawRate_YY_RL : 16;
        uint64_t YawRate_rad_s_YY_RL : 16;
        uint64_t Cov_Orientation_YY_RL : 16;
        uint64_t Orientation_rad_YY_RL : 16;
        uint64_t ExistenceProbability_YY_RL : 8;
        uint64_t TrackAge_YY_RL : 8;
        uint64_t Cov_Half_Width_YY_RL : 16;
        uint64_t Width_YY_RL : 16;
        uint64_t Cov_Half_Length_YY_RL : 16;
        uint64_t Length_YY_RL : 16;
        uint64_t v_m_s_YY_RL : 16;
        uint64_t a_m_s_s_YY_RL : 16;
        uint64_t y_m_YY_RL : 16;
        uint64_t x_m_YY_RL : 16;
        uint64_t Object_ID_YY_RL : 8;
        uint64_t Frame_ID_YY_RL : 8;
    } signals;
};

struct canmsg_obj_list_yy_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_list_yy_rl_data_t data;
};

#define CANMSG_OBJ_LIST_YY_RR_ID                   (0x0642)
#define CANMSG_OBJ_LIST_YY_RR_DLC                  (48)
#define CANMSG_OBJ_LIST_YY_RR_MIN_DLC              (48)
union canmsg_obj_list_yy_rr_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 16;
        uint64_t Cov_v_YY_RR : 16;
        uint64_t Cov_a_YY_RR : 16;
        uint64_t Cov_xy_YY_RR : 16;
        uint64_t Cov_y_YY_RR : 16;
        uint64_t Cov_x_YY_RR : 16;
        uint64_t Cov_YawRate_YY_RR : 16;
        uint64_t YawRate_rad_s_YY_RR : 16;
        uint64_t Cov_Orientation_YY_RR : 16;
        uint64_t Orientation_rad_YY_RR : 16;
        uint64_t ExistenceProbability_YY_RR : 8;
        uint64_t TrackAge_YY_RR : 8;
        uint64_t Cov_Half_Width_YY_RR : 16;
        uint64_t Width_YY_RR : 16;
        uint64_t Cov_Half_Length_YY_RR : 16;
        uint64_t Length_YY_RR : 16;
        uint64_t v_m_s_YY_RR : 16;
        uint64_t a_m_s_s_YY_RR : 16;
        uint64_t y_m_YY_RR : 16;
        uint64_t x_m_YY_RR : 16;
        uint64_t Object_ID_YY_RR : 8;
        uint64_t Frame_ID_YY_RR : 8;
    } signals;
};

struct canmsg_obj_list_yy_rr_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_obj_list_yy_rr_data_t data;
};

#define CANMSG_PDCU_1C8_ID                         (0x01C8)
#define CANMSG_PDCU_1C8_DLC                        (8)
#define CANMSG_PDCU_1C8_MIN_DLC                    (8)
union canmsg_pdcu_1c8_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum1C8 : 8;
        uint64_t RollingCounter1C8 : 4;
        uint64_t ICAN_1C8_Reserve_1 : 19;
        uint64_t PDCU_AccelPedalValid : 1;
        uint64_t ICAN_1C8_Reserve_0 : 16;
        uint64_t PDCU_AccelPedalSt : 10;
        uint64_t PDCU_TargetGear : 2;
        uint64_t PDCU_ActualGear : 2;
        uint64_t PDCU_TargetGearValid : 1;
        uint64_t PDCU_ActualGearValid : 1;
    } signals;
};

struct canmsg_pdcu_1c8_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_pdcu_1c8_data_t data;
};

#define CANMSG_PDCU_364_ID                         (0x0364)
#define CANMSG_PDCU_364_DLC                        (8)
#define CANMSG_PDCU_364_MIN_DLC                    (3)
union canmsg_pdcu_364_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 12;
        uint64_t PDCU_ChrgCnctSt : 2;
        uint64_t PAD1 : 2;
        uint64_t PDCU_ChrgSt : 3;
    } signals;
};

struct canmsg_pdcu_364_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_pdcu_364_data_t data;
};

#define CANMSG_PDCU_37E_ID                         (0x037E)
#define CANMSG_PDCU_37E_DLC                        (8)
#define CANMSG_PDCU_37E_MIN_DLC                    (7)
union canmsg_pdcu_37e_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 32;
        uint64_t PDCU_VirtAccrPedPosSt : 8;
        uint64_t PAD1 : 8;
        uint64_t PDCU_VirtAccrPedPosnValid : 1;
    } signals;
};

struct canmsg_pdcu_37e_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_pdcu_37e_data_t data;
};

#define CANMSG_PDCU_FC_ID                          (0x00FC)
#define CANMSG_PDCU_FC_DLC                         (32)
#define CANMSG_PDCU_FC_MIN_DLC                     (32)
union canmsg_pdcu_fc_data_t
{
    uint8_t buffer[32];
    struct
    {
        uint64_t Checksum0FC : 8;
        uint64_t RollingCounter0FC : 4;
        uint64_t PDCU_MotRegenTrqInDTotal : 12;
        uint64_t ICAN_0FC_Reserve_4 : 7;
        uint64_t PDCU_RAWhlTqMax : 13;
        uint64_t PDCU_RAWhlTqRegenMax : 12;
        uint64_t PDCU_rearMotRpmValid : 1;
        uint64_t PDCU_RAWhlTqRegenMaxValid : 1;
        uint64_t PDCU_RAWhlTqAct__S1 : 6;
        uint64_t PDCU_RAWhlTqAct__S0 : 8;
        uint64_t ICAN_0FC_Reserve_3 : 16;
        uint64_t PDCU_rearMotRpm : 16;
        uint64_t PDCU_frontMotorRpmValid : 1;
        uint64_t PDCU_FAWhlTqRegenMaxValid : 1;
        uint64_t PDCU_FAWhlTqAct : 14;
        uint64_t ICAN_0FC_Reserve_2__S1 : 8;
        uint64_t ICAN_0FC_Reserve_2__S0 : 8;
        uint64_t PDCU_FrontMotorRpm : 16;
        uint64_t PDCU_FAWhlTqRegenMax : 12;
        uint64_t PDCU_FAWhlTqMax : 13;
        uint64_t PDCU_MotRegenTrqInDTotalVld : 1;
        uint64_t ICAN_0FC_Reserve_1 : 4;
        uint64_t PDCU_OverrideSt : 1;
        uint64_t PDCU_AccPedalVirtlValid : 1;
        uint64_t PDCU_AccelPedalVirtualSt__S1 : 8;
        uint64_t PDCU_AccelPedalVirtualSt__S0 : 2;
        uint64_t ICAN_0FC_Reserve_0 : 62;
    } signals;
};

struct canmsg_pdcu_fc_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_pdcu_fc_data_t data;
};

union cansig_pdcu_fc__pdcu_rawhltqact_data_t
{
    uint32_t val;
    struct
    {
        uint32_t PDCU_RAWhlTqAct__S1 : 6;
        uint32_t PDCU_RAWhlTqAct__S0 : 8;
    } fields;
};

union cansig_pdcu_fc__ican_0fc_reserve_2_data_t
{
    uint32_t val;
    struct
    {
        uint32_t ICAN_0FC_Reserve_2__S1 : 8;
        uint32_t ICAN_0FC_Reserve_2__S0 : 8;
    } fields;
};

union cansig_pdcu_fc__pdcu_accelpedalvirtualst_data_t
{
    uint32_t val;
    struct
    {
        uint32_t PDCU_AccelPedalVirtualSt__S1 : 8;
        uint32_t PDCU_AccelPedalVirtualSt__S0 : 2;
    } fields;
};

#define CANMSG_PDCU_FE_ID                          (0x00FE)
#define CANMSG_PDCU_FE_DLC                         (8)
#define CANMSG_PDCU_FE_MIN_DLC                     (8)
union canmsg_pdcu_fe_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum0FE : 8;
        uint64_t RollingCounter0FE : 4;
        uint64_t PDCU_EPWTSt : 2;
        uint64_t ICAN_0FE_Reserve_3 : 10;
        uint64_t PDCU_BMS_SOCCalculate : 8;
        uint64_t ICAN_0FE_Reserve_2 : 6;
        uint64_t PDCU_BrakePedalSt : 1;
        uint64_t ICAN_0FE_Reserve_1 : 9;
        uint64_t PDCU_ShiftLvlPosnSt : 4;
        uint64_t PDCU_DriveModeSt : 4;
        uint64_t PDCU_DriveReadySt : 1;
        uint64_t ICAN_0FE_Reserve_0 : 7;
    } signals;
};

struct canmsg_pdcu_fe_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_pdcu_fe_data_t data;
};

#define CANMSG_PDCU_FF_ID                          (0x00FF)
#define CANMSG_PDCU_FF_DLC                         (12)
#define CANMSG_PDCU_FF_MIN_DLC                     (12)
union canmsg_pdcu_ff_data_t
{
    uint8_t buffer[12];
    struct
    {
        uint64_t Checksum0FF : 8;
        uint64_t RollingCounter0FF : 4;
        uint64_t PDCU_ACCResponseSt : 3;
        uint64_t ICAN_0FF_Reserve_2 : 1;
        uint64_t PDCU_TotalCoastRegnTqTarget : 14;
        uint64_t PDCU_TotalCoastRegnTqTrgVald : 1;
        uint64_t ICAN_0FF_Reserve_1__S1 : 1;
        uint64_t ICAN_0FF_Reserve_1__S0 : 2;
        uint64_t PDCU_MCUR_InverterWorkSt : 3;
        uint64_t PDCU_MCUF_InverterWorkSt : 3;
        uint64_t PDCU_ACCcontrolAvailableSt : 1;
        uint64_t PDCU_WhetherACCReqRealizedSt : 1;
        uint64_t PDCU_RealizedPowertrainWhlTq : 14;
        uint64_t PDCU_AxleTorqueSt : 1;
        uint64_t ICAN_0FF_Reserve_0 : 1;
        uint64_t PDCU_TargetDriverTorq : 14;
        uint64_t PDCU_MaxVehicleWhlTq : 14;
        uint64_t PDCU_DriveAsiStt_APA : 2;
        uint64_t EMS_PowertrainControlSt : 2;
        uint64_t PDCU_ShiftQuitReason : 2;
        uint64_t PDCU_ShiftAvaibleSt : 1;
        uint64_t PDCU_ACCFunIhibitionReq : 1;
        uint64_t PDCU_DriveAsiSttACCSt : 2;
    }__attribute__((packed)) signals;
};

struct canmsg_pdcu_ff_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_pdcu_ff_data_t data;
};

union cansig_pdcu_ff__ican_0ff_reserve_1_data_t
{
    uint32_t val;
    struct
    {
        uint32_t ICAN_0FF_Reserve_1__S1 : 1;
        uint32_t ICAN_0FF_Reserve_1__S0 : 2;
    } fields;
};

#define CANMSG_SGW_C_119_ID                        (0x0119)
#define CANMSG_SGW_C_119_DLC                       (8)
#define CANMSG_SGW_C_119_MIN_DLC                   (8)
union canmsg_sgw_c_119_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t CheckSum119 : 8;
        uint64_t RollingCounter119 : 4;
        uint64_t ACU_LongitudAccelerationSt : 12;
        uint64_t ICAN_119_Reserve_0 : 8;
        uint64_t ACU_LateralAccelarationSt : 12;
        uint64_t ACU_LongitdAcclerValid : 1;
        uint64_t ACU_SensorCalSt : 1;
        uint64_t ACU_LateralAccelareValid : 1;
        uint64_t ACU_YawrateValiditySt : 1;
        uint64_t ACU_YawRateSt : 16;
    } signals;
};

struct canmsg_sgw_c_119_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_119_data_t data;
};

#define CANMSG_SGW_C_23A_ID                        (0x023A)
#define CANMSG_SGW_C_23A_DLC                       (8)
#define CANMSG_SGW_C_23A_MIN_DLC                   (8)
union canmsg_sgw_c_23a_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 4;
        uint64_t VIUL_AutoFrontWiper : 2;
        uint64_t VIUL_BackDoorOpenSwFbSt : 1;
        uint64_t VIUL_BackDoorUnlockReq : 1;
        uint64_t PAD1 : 8;
        uint64_t VIUL_FrontWiperWorkSt : 2;
        uint64_t PAD2 : 4;
        uint64_t VIUL_AUTOSwSt : 1;
        uint64_t VIUL_HazardSwrSt : 1;
        uint64_t PAD3 : 10;
        uint64_t VIUL_EngHoodUnlockWarn : 1;
        uint64_t PAD4 : 9;
        uint64_t VIUL_LeftTurnSwSt : 2;
        uint64_t VIUL_RightTurnSwrSt : 2;
        uint64_t VIUL_LowBeamSt : 1;
        uint64_t VIUL_BackFogLampSt : 1;
        uint64_t VIUL_FrontFogLampSt : 1;
        uint64_t VIUL_LFDoorSwSt : 1;
        uint64_t VIUL_RFDoorSwSt : 1;
        uint64_t VIUL_RRDoorSwSt : 1;
        uint64_t VIUL_LRDoorSwSt : 1;
        uint64_t PAD5 : 1;
        uint64_t VIUL_TrunkSt : 1;
        uint64_t VIUL_LetfligthSt : 1;
        uint64_t VIUL_LetfligthFaultSt : 1;
        uint64_t VIUL_RightligthSt : 1;
        uint64_t VIUL_RightligthFaultSt : 1;
        uint64_t VIUL_PosLampSt : 1;
        uint64_t VIUL_HazardLampSt : 1;
        uint64_t VIUL_HighBeamSt : 1;
    } signals;
};

struct canmsg_sgw_c_23a_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_23a_data_t data;
};

#define CANMSG_SGW_C_30A_ID                        (0x030A)
#define CANMSG_SGW_C_30A_DLC                       (8)
#define CANMSG_SGW_C_30A_MIN_DLC                   (8)
union canmsg_sgw_c_30a_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t Checksum30A : 8;
        uint64_t RollingCounter30A : 4;
        uint64_t ICAN_30A_Reserve_2 : 36;
        uint64_t OTA_EstimatedUpgradeTime : 7;
        uint64_t ICAN_30A_Reserve_1 : 1;
        uint64_t OTA_ModeSt : 1;
        uint64_t ICAN_30A_Reserve_0 : 7;
    } signals;
};

struct canmsg_sgw_c_30a_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_30a_data_t data;
};

#define CANMSG_SGW_C_30C_ID                        (0x030C)
#define CANMSG_SGW_C_30C_DLC                       (8)
#define CANMSG_SGW_C_30C_MIN_DLC                   (8)
union canmsg_sgw_c_30c_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 48;
        uint64_t ACU_DriverBeltSwSigSt : 2;
        uint64_t PAD1 : 6;
        uint64_t ACU_CrashoutSt : 2;
    } signals;
};

struct canmsg_sgw_c_30c_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_30c_data_t data;
};

#define CANMSG_SGW_C_320_ID                        (0x0320)
#define CANMSG_SGW_C_320_DLC                       (8)
#define CANMSG_SGW_C_320_MIN_DLC                   (8)
union canmsg_sgw_c_320_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 40;
        uint64_t IC_TotalOdmeter : 20;
    } signals;
};

struct canmsg_sgw_c_320_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_320_data_t data;
};

#define CANMSG_SGW_C_322_ID                        (0x0322)
#define CANMSG_SGW_C_322_DLC                       (8)
#define CANMSG_SGW_C_322_MIN_DLC                   (8)
union canmsg_sgw_c_322_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 56;
        uint64_t VIUR_AC_AmbTempSt : 8;
    } signals;
};

struct canmsg_sgw_c_322_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_322_data_t data;
};

#define CANMSG_SGW_C_33C_ID                        (0x033C)
#define CANMSG_SGW_C_33C_DLC                       (8)
#define CANMSG_SGW_C_33C_MIN_DLC                   (8)
union canmsg_sgw_c_33c_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 20;
        uint64_t VIUL_CHMSLSt : 2;
        uint64_t VIUL_BrakeLampSt : 2;
        uint64_t PAD1 : 30;
        uint64_t VIUL_FrontWiperSwSt : 2;
        uint64_t VIUL_IgnitionSt : 2;
        uint64_t VIUL_DoorLockSt : 1;
    } signals;
};

struct canmsg_sgw_c_33c_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_33c_data_t data;
};

#define CANMSG_SGW_C_33D_ID                        (0x033D)
#define CANMSG_SGW_C_33D_DLC                       (8)
#define CANMSG_SGW_C_33D_MIN_DLC                   (7)
union canmsg_sgw_c_33d_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 48;
        uint64_t VIUL_IHBCFunEn : 1;
    } signals;
};

struct canmsg_sgw_c_33d_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_33d_data_t data;
};

#define CANMSG_SGW_C_341_ID                        (0x0341)
#define CANMSG_SGW_C_341_DLC                       (8)
#define CANMSG_SGW_C_341_MIN_DLC                   (8)
union canmsg_sgw_c_341_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 54;
        uint64_t VIUL_BattVoltSt : 10;
    } signals;
};

struct canmsg_sgw_c_341_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_341_data_t data;
};

#define CANMSG_SGW_C_346_ID                        (0x0346)
#define CANMSG_SGW_C_346_DLC                       (8)
#define CANMSG_SGW_C_346_MIN_DLC                   (6)
union canmsg_sgw_c_346_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 28;
        uint64_t VIUL_PEPS_RKECommand2 : 4;
        uint64_t PAD1 : 11;
        uint64_t VIUL_PEPS_EngineStartMode : 3;
    } signals;
};

struct canmsg_sgw_c_346_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_346_data_t data;
};

#define CANMSG_SGW_C_365_ID                        (0x0365)
#define CANMSG_SGW_C_365_DLC                       (8)
#define CANMSG_SGW_C_365_MIN_DLC                   (3)
union canmsg_sgw_c_365_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 22;
        uint64_t IVI_WashCarModeReq : 2;
    } signals;
};

struct canmsg_sgw_c_365_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_365_data_t data;
};

#define CANMSG_SGW_C_367_ID                        (0x0367)
#define CANMSG_SGW_C_367_DLC                       (8)
#define CANMSG_SGW_C_367_MIN_DLC                   (6)
union canmsg_sgw_c_367_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 46;
        uint64_t IVI_HazardLampCtrlReq : 2;
    } signals;
};

struct canmsg_sgw_c_367_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_367_data_t data;
};

#define CANMSG_SGW_C_369_ID                        (0x0369)
#define CANMSG_SGW_C_369_DLC                       (8)
#define CANMSG_SGW_C_369_MIN_DLC                   (8)
union canmsg_sgw_c_369_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 48;
        uint64_t IC_VehSpd : 16;
    } signals;
};

struct canmsg_sgw_c_369_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_369_data_t data;
};

#define CANMSG_SGW_C_3F0_ID                        (0x03F0)
#define CANMSG_SGW_C_3F0_DLC                       (8)
#define CANMSG_SGW_C_3F0_MIN_DLC                   (8)
union canmsg_sgw_c_3f0_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 43;
        uint64_t DKM_BLEAPPRPAParkReq : 1;
        uint64_t PAD1 : 4;
        uint64_t DKM_BLEConnectSt : 3;
        uint64_t PAD2 : 3;
        uint64_t DKM_BLEAPPAPAMoveReq : 2;
        uint64_t PAD3 : 1;
        uint64_t DKM_BLEAPAModeSelectReq : 4;
        uint64_t DKM_BLEAPASwReq : 1;
    } signals;
};

struct canmsg_sgw_c_3f0_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_3f0_data_t data;
};

#define CANMSG_SGW_C_40_ID                         (0x0040)
#define CANMSG_SGW_C_40_DLC                        (2)
#define CANMSG_SGW_C_40_MIN_DLC                    (2)
union canmsg_sgw_c_40_data_t
{
    uint8_t buffer[2];
    struct
    {
        uint64_t VIUL_PEPS_DoorLockReq : 2;
        uint64_t PAD0 : 9;
        uint64_t VIUL_PEPS_WeclomeunLockReq : 1;
        uint64_t VIUL_PEPS_RKECommand : 4;
    }__attribute__((packed)) signals;
};

struct canmsg_sgw_c_40_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_40_data_t data;
};

#define CANMSG_SGW_C_4AF_ID                        (0x04AF)
#define CANMSG_SGW_C_4AF_DLC                       (8)
#define CANMSG_SGW_C_4AF_MIN_DLC                   (8)
union canmsg_sgw_c_4af_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 48;
        uint64_t CWB_TurnSwReq : 3;
        uint64_t CWB_RearWiperSwReq : 2;
        uint64_t CWB_FrontWiperSwReq : 3;
        uint64_t PAD1 : 2;
        uint64_t CWB_PreWashingSwReq : 2;
        uint64_t CWB_DimmingSwReq : 2;
    } signals;
};

struct canmsg_sgw_c_4af_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_4af_data_t data;
};

#define CANMSG_SGW_C_4C0_ID                        (0x04C0)
#define CANMSG_SGW_C_4C0_DLC                       (16)
#define CANMSG_SGW_C_4C0_MIN_DLC                   (16)
union canmsg_sgw_c_4c0_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 24;
        uint64_t IVI_APAFunModuleEnIndicate : 1;
        uint64_t IVI_TRFunModuleEnIndicate : 1;
        uint64_t PAD1 : 1;
        uint64_t IVI_TRSwRecoverSwReq : 2;
        uint64_t IVI_NaviDistanceSt : 11;
        uint64_t IVI_RequestAVMReq : 8;
        uint64_t IVI_NaviTurnReq : 7;
        uint64_t PAD2 : 1;
        uint64_t IVI_AVMCalibrateStartReq : 8;
        uint64_t IVI_APAModeSelectReq : 4;
        uint64_t IVI_NaviInterConnectSt : 2;
        uint64_t IVI_APASwReq : 2;
        uint64_t IVI_AVMBodyColorReq : 4;
        uint64_t IVI_AVMSteerWhlActiveReq : 1;
        uint64_t IVI_AVMPerspectiveChassisSwReq : 1;
        uint64_t IVI_TRSwReq : 2;
        uint64_t IVI_AVM2D3DModeReq : 1;
        uint64_t IVI_AVMUltrasonicRadarDispReq : 1;
        uint64_t IVI_AVMUltrasonicRadarActiveReq : 1;
        uint64_t IVI_AVMLineSetSwReq : 1;
        uint64_t IVI_AVMPathModeReq : 2;
        uint64_t IVI_AVMHardTouchSwReq : 1;
        uint64_t IVI_TouchButtonPressReq : 1;
        uint64_t IVI_TouchYCoordinate : 16;
        uint64_t IVI_TouchXCoordinate : 16;
        uint64_t IVI_AVMActivationReq : 2;
        uint64_t IVI_PuposeKeyReq : 2;
        uint64_t IVI_ScreenSt : 2;
        uint64_t IVI_SideviewCtrlReq : 2;
    } signals;
};

struct canmsg_sgw_c_4c0_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_4c0_data_t data;
};

#define CANMSG_SGW_C_4C1_ID                        (0x04C1)
#define CANMSG_SGW_C_4C1_DLC                       (16)
#define CANMSG_SGW_C_4C1_MIN_DLC                   (14)
union canmsg_sgw_c_4c1_data_t
{
    uint8_t buffer[16];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t IVI_RearCrossSwReq : 2;
        uint64_t PAD1 : 6;
        uint64_t IVI_FrontCrossSwReq : 2;
        uint64_t IVI_ESAActivationSwReq : 2;
        uint64_t IVI_ISASpeedDevSetReq : 2;
        uint64_t IVI_ACCFollowStpTimeSetReq : 2;
        uint64_t IVI_RCWActivationReq : 3;
        uint64_t IVI_FrontCrossmodeReq : 2;
        uint64_t PAD2 : 19;
        uint64_t IVI_RearCrossModeReq : 2;
        uint64_t IVI_BSDLCWActivationReq : 2;
        uint64_t IVI_DOWActivationReq : 2;
        uint64_t PAD3 : 2;
        uint64_t IVI_BSDAutoCaliActivationReq : 1;
        uint64_t IVI_TLCModeSelectionReq : 2;
        uint64_t IVI_JAAlertSwReq : 2;
        uint64_t IVI_LKAmainSwReq : 1;
        uint64_t IVI_ISAModeReq : 2;
        uint64_t IVI_AEBSwReq : 2;
        uint64_t IVI_FCWSwReq : 2;
        uint64_t IVI_FCWSensitivitySetReq : 2;
        uint64_t IVI_SASwReq : 2;
        uint64_t IVI_FDMSwReq : 2;
        uint64_t IVI_LSSSwReq : 2;
        uint64_t IVI_LKAModeReq : 2;
        uint64_t IVI_AutoPilotSwReq : 2;
        uint64_t IVI_ADASSpeedLimit : 5;
        uint64_t IVI_TSRSwReq : 2;
        uint64_t IVI_DriveModeRembSwReq : 1;
        uint64_t IVI_P2PSwReq : 2;
        uint64_t IVI_P2PLCCModeReq : 2;
        uint64_t IVI_P2PRemideModeReq : 2;
        uint64_t IVI_AvoidanceSwReq : 2;
        uint64_t IVI_ELKSwReq : 2;
    } signals;
};

struct canmsg_sgw_c_4c1_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_4c1_data_t data;
};

#define CANMSG_SGW_C_4DA_ID                        (0x04DA)
#define CANMSG_SGW_C_4DA_DLC                       (8)
#define CANMSG_SGW_C_4DA_MIN_DLC                   (8)
union canmsg_sgw_c_4da_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 16;
        uint64_t TBOX_TimeYear : 8;
        uint64_t TBOX_TimeSecond : 6;
        uint64_t PAD1 : 2;
        uint64_t TBOX_TimeMinute : 6;
        uint64_t PAD2 : 2;
        uint64_t TBOX_TimeHour : 5;
        uint64_t PAD3 : 3;
        uint64_t TBOX_TimeDate : 5;
        uint64_t PAD4 : 2;
        uint64_t TBOX_TimeValid : 1;
        uint64_t PAD5 : 4;
        uint64_t TBOX_TimeMonth : 4;
    } signals;
};

struct canmsg_sgw_c_4da_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_4da_data_t data;
};

#define CANMSG_SGW_C_710_ID                        (0x0710)
#define CANMSG_SGW_C_710_DLC                       (12)
#define CANMSG_SGW_C_710_MIN_DLC                   (12)
union canmsg_sgw_c_710_data_t
{
    uint8_t buffer[12];
    struct
    {
        uint64_t CheckSum710 : 8;
        uint64_t RollingCounter710 : 4;
        uint64_t HIL_VerticalAccelSt : 12;
        uint64_t HIL_LateralAccelSt__S1 : 8;
        uint64_t HIL_LateralAccelSt__S0 : 4;
        uint64_t HIL_LongitudeAccelSt : 12;
        uint64_t HIL_RollRateSt : 16;
        uint64_t HIL_PitchRateSt : 16;
        uint64_t HIL_YawRateSt : 16;
    }__attribute__((packed)) signals;
};

struct canmsg_sgw_c_710_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_710_data_t data;
};

union cansig_sgw_c_710__hil_lateralaccelst_data_t
{
    uint32_t val;
    struct
    {
        uint32_t HIL_LateralAccelSt__S1 : 8;
        uint32_t HIL_LateralAccelSt__S0 : 4;
    } fields;
};

#define CANMSG_SGW_C_83_ID                         (0x0083)
#define CANMSG_SGW_C_83_DLC                        (8)
#define CANMSG_SGW_C_83_MIN_DLC                    (8)
union canmsg_sgw_c_83_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 48;
        uint64_t EMS_EngSpd : 16;
    } signals;
};

struct canmsg_sgw_c_83_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_c_83_data_t data;
};

#define CANMSG_SGW_I_304_ID                        (0x0304)
#define CANMSG_SGW_I_304_DLC                       (8)
#define CANMSG_SGW_I_304_MIN_DLC                   (8)
union canmsg_sgw_i_304_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t PAD0 : 21;
        uint64_t POT_BackDoorOpenTurnIdicator : 3;
        uint64_t POT_BackDoorHeightSetSt : 7;
        uint64_t PAD1 : 25;
        uint64_t POT_BackDoorPosSt : 2;
        uint64_t PAD2 : 2;
        uint64_t POT_BackDoorOpenFailWarn : 1;
        uint64_t POT_BackDoorCloseFailWarn : 1;
        uint64_t POT_BackDoorAntiplayWarn : 1;
    } signals;
};

struct canmsg_sgw_i_304_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_i_304_data_t data;
};

#define CANMSG_SGW_I_30C_ID                        (0x030C)
#define CANMSG_SGW_I_30C_DLC                       (8)
#define CANMSG_SGW_I_30C_MIN_DLC                   (8)
union canmsg_sgw_i_30c_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t CheckSum30C : 8;
        uint64_t RollingCounter30C : 4;
        uint64_t PAD0 : 20;
        uint64_t ACU_RRBeltSt : 2;
        uint64_t PAD1 : 4;
        uint64_t ACU_PassengerSeatSt : 2;
        uint64_t PAD2 : 6;
        uint64_t ACU_RearMiddleBeltSt : 2;
        uint64_t ACU_DriverBeltSwSigSt : 2;
        uint64_t ACU_PassengerBeltSt : 2;
        uint64_t ACU_RLBeltSt : 2;
        uint64_t PAD3 : 2;
        uint64_t ACU_CrashoutSt : 2;
        uint64_t ACU_AirbagLampSt : 2;
    } signals;
};

struct canmsg_sgw_i_30c_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_i_30c_data_t data;
};

#define CANMSG_SGW_I_33D_ID                        (0x033D)
#define CANMSG_SGW_I_33D_DLC                       (8)
#define CANMSG_SGW_I_33D_MIN_DLC                   (8)
union canmsg_sgw_i_33d_data_t
{
    uint8_t buffer[8];
    struct
    {
        uint64_t VIUL_ALBrighenessSt : 4;
        uint64_t PAD0 : 4;
        uint64_t VIUL_ParkingSayHiPatternFbSt : 3;
        uint64_t VIUL_RainfallClosWinFunSt : 1;
        uint64_t VIUL_ALFunSt : 1;
        uint64_t VIUL_LockLampPatternFbSt : 3;
        uint64_t VIUL_ALColorSetFbSt : 8;
        uint64_t VIUL_BLSSignalWarn : 1;
        uint64_t VIUL_TWLampFunSt : 1;
        uint64_t VIUL_TWUnLockFunSt : 1;
        uint64_t VIUL_RainSnowModeSt : 1;
        uint64_t VIUL_RomanticModeSt : 1;
        uint64_t VIUL_SmokeModeSt : 1;
        uint64_t VIUL_ParkingModeSt : 1;
        uint64_t VIUL_AntDisEnSt : 1;
        uint64_t VIUL_AutoUnlockFunSt : 1;
        uint64_t VIUL_LockGapWinFunSt : 1;
        uint64_t VIUL_AutoMirrorFunSt : 1;
        uint64_t VIUL_TailgateDoorLockFunSt : 1;
        uint64_t VIUL_VoiceRoofCtrSt : 1;
        uint64_t VIUL_GearReverseRearWipFunSt : 1;
        uint64_t VIUL_UnlockWelcomeLampSt : 1;
        uint64_t VIUL_IHBCFunSt : 1;
        uint64_t PAD1 : 4;
        uint64_t VIUL_UnlockLampPatternFbSt : 3;
        uint64_t VIUL_AutoWiperEnFbSt : 1;
        uint64_t VIUL_IHBCFunEn : 1;
        uint64_t VIUL_IHBCStausInd : 1;
        uint64_t VIUL_ForbidVehStartEnSt : 2;
        uint64_t VIUL_LockCmdWinFunSt : 2;
        uint64_t VIUL_ParkingSayHiSetSt : 2;
        uint64_t PAD2 : 1;
        uint64_t VIUL_CourtesyLightsSwSt : 2;
        uint64_t VIUL_WashModeRearMirrorFbSt : 1;
        uint64_t VIUL_WashCarModeARFbSt : 1;
        uint64_t VIUL_ARDoorFunSt : 1;
    } signals;
};

struct canmsg_sgw_i_33d_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_sgw_i_33d_data_t data;
};

//=====================================================================================//


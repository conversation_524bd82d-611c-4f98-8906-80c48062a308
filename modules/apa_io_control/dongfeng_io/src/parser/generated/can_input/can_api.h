#include "parser_typedef.h"
#pragma once
//--------------//
extern void CAN_SetCanParserLockHandler(fpParserLockHandler_t func);
extern void CAN_SetCanParserUnlockHandler(fpParserUnlockHandler_t func);
extern void CAN_SetMsgCounterCalcHandler(fpMsgCounterCalcHandler_t func);
extern void CAN_SetMsgChecksumCalcHandler(fpMsgChecksumCalcHandler_t func);
extern void CAN_SetMsgChecksumVerityHandler(fpMsgChecksumVerityHandler_t func);
extern void CAN_SetMsgChangeHandler(fpMsgChangeHandler_t func);
extern void CAN_SetMsgChecksumErrorHandler(fpMsgChecksumErrorHandler_t func);
extern void CAN_SetMsgCounterErrorHandler(fpMsgCounterErrorHandler_t func);
extern void CAN_SetMsgTimeoutHandler(fpMsgTimeoutHandler_t func);
extern void CAN_SetMsgDlcHandler(fpMsgDlcHandler_t func);
extern void CAN_SetMsgOutRangeHandler(fpMsgOutRangeHandler_t func);
extern void CAN_SetSigChangeHandler(fpSigChangeHandler_t func);
extern void CAN_SetSigOnWriteHandler(fpSigOnWriteHandler_t func);
extern void CAN_SetSignalChangedHook(fpSignalChangedHook_t func);
extern void CAN_SetSignalSetCallBack(fpSignalTriggerCallBack_t func);
extern void CAN_SetSignalGetCallBack(fpSignalTriggerCallBack_t func);
//=====================================================================================//
extern struct veh_message *CAN_RxMessageList[58];

extern struct veh_message *CAN_TxMessageList[6];

extern struct veh_signal* CAN_ALL_Signal_Array[];
extern struct veh_signal CANSIG_EPS_166__EPS_LKAOverlayCurrent_g;
extern struct veh_signal CANSIG_EPS_166__EPS_LKASt_g;
extern struct veh_signal CANSIG_EPS_166__EPS_LKA_DriverOverrideSt_g;
extern struct veh_signal CANSIG_EPS_166__EPS_LKA_TorqueLimitationSt_g;
extern struct veh_signal CANSIG_EPS_167__EPS_DriverInitializedESASt_g;
extern struct veh_signal CANSIG_EPS_167__EPS_ESACurrentOverlay_g;
extern struct veh_signal CANSIG_EPS_167__EPS_ESASt_g;
extern struct veh_signal CANSIG_EPS_167__EPS_ESA_DriverOverrideSt_g;
extern struct veh_signal CANSIG_EPS_167__EPS_ESA_TorqueLimitationSt_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_CheckSum17A_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_RollingCount17A_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_SteeringAngle_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_SteeringAngleFlag_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_SteeringHoldSt_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_SteeringTorqueSensorSt_g;
extern struct veh_signal CANSIG_EPS_17A__EPS_SteeringTorqueValue_g;
extern struct veh_signal CANSIG_EPS_17A__ICAN_17A_Reserve_0_g;
extern struct veh_signal CANSIG_EPS_17A__ICAN_17A_Reserve_1_g;
extern struct veh_signal CANSIG_EPS_17A__ICAN_17A_Reserve_2_g;
extern struct veh_signal CANSIG_EPS_A5__ICAN_0A5_Reserve_0_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_CalibratedSt_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_CheckSumA5_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_RollingCountA5_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_SASFailureSt_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_SteeringAngle_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_SteeringRotSpd_g;
extern struct veh_signal CANSIG_EPS_A5__TAS_SAS_TrimmingSt_g;
extern struct veh_signal CANSIG_EPS_B0__EPS_APAChecksumB0_g;
extern struct veh_signal CANSIG_EPS_B0__EPS_APAFuncModeSt_g;
extern struct veh_signal CANSIG_EPS_B0__EPS_APAProhibitedResaon_g;
extern struct veh_signal CANSIG_EPS_B0__EPS_APARollingCountB0_g;
extern struct veh_signal CANSIG_EPS_B0__EPS_APASt_g;
extern struct veh_signal CANSIG_EPS_B0__ICAN_0B0_Reserve_0_g;
extern struct veh_signal CANSIG_EPS_B0__ICAN_0B0_Reserve_1_g;
extern struct veh_signal CANSIG_IBC_101__Checksum101_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedFL_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedFLValid_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedFR_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedFRValid_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedRL_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedRLValid_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedRR_g;
extern struct veh_signal CANSIG_IBC_101__IBC_wheelSpeedRRValid_g;
extern struct veh_signal CANSIG_IBC_101__ICAN_101_Reserve_0_g;
extern struct veh_signal CANSIG_IBC_101__ICAN_101_Reserve_1_g;
extern struct veh_signal CANSIG_IBC_101__ICAN_101_Reserve_2_g;
extern struct veh_signal CANSIG_IBC_101__ICAN_101_Reserve_3_g;
extern struct veh_signal CANSIG_IBC_101__RollingCounter101_g;
extern struct veh_signal CANSIG_IBC_11D__IBC_PedalTravelSensor_g;
extern struct veh_signal CANSIG_IBC_11D__IBC_PedalTravelSensorSt_g;
extern struct veh_signal CANSIG_IBC_11D__IBC_PlungerPressure_g;
extern struct veh_signal CANSIG_IBC_12C__Checksum12C_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_EPBCruiseControlCancelSt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_EPBErrorSt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_EPBSt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_EPBSwSt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_EPBSwValiditySt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_PbcActuatorLeftSt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_PbcActuatorRightSt_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_SlopHighWarn_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_Slope_g;
extern struct veh_signal CANSIG_IBC_12C__IBC_SlopeSt_g;
extern struct veh_signal CANSIG_IBC_12C__ICAN_12C_Reserve_0_g;
extern struct veh_signal CANSIG_IBC_12C__ICAN_12C_Reserve_1_g;
extern struct veh_signal CANSIG_IBC_12C__ICAN_12C_Reserve_2_g;
extern struct veh_signal CANSIG_IBC_12C__ICAN_12C_Reserve_3_g;
extern struct veh_signal CANSIG_IBC_12C__ICAN_12C_Reserve_4_g;
extern struct veh_signal CANSIG_IBC_12C__RollingCounter12C_g;
extern struct veh_signal CANSIG_IBC_133__Checksum133_g;
extern struct veh_signal CANSIG_IBC_133__IBC_BrakePedalSt_g;
extern struct veh_signal CANSIG_IBC_133__IBC_BrkFricTotAtWhlsTorq_g;
extern struct veh_signal CANSIG_IBC_133__IBC_CSTSt_g;
extern struct veh_signal CANSIG_IBC_133__IBC_EBPActiveSt_g;
extern struct veh_signal CANSIG_IBC_133__IBC_FWAWarn_g;
extern struct veh_signal CANSIG_IBC_133__IBC_SCMActiveSt_g;
extern struct veh_signal CANSIG_IBC_133__IBC_SystemSt_g;
extern struct veh_signal CANSIG_IBC_133__ICAN_133_Reserve_0_g;
extern struct veh_signal CANSIG_IBC_133__ICAN_133_Reserve_1_g;
extern struct veh_signal CANSIG_IBC_133__ICAN_133_Reserve_2_g;
extern struct veh_signal CANSIG_IBC_133__ICAN_133_Reserve_3_g;
extern struct veh_signal CANSIG_IBC_133__ICAN_133_Reserve_4_g;
extern struct veh_signal CANSIG_IBC_133__RollingCounter133_g;
extern struct veh_signal CANSIG_IBC_143__Checksum143_g;
extern struct veh_signal CANSIG_IBC_143__IBC_APABrakSysLongictlSt_g;
extern struct veh_signal CANSIG_IBC_143__IBC_APABrakeModeSt_g;
extern struct veh_signal CANSIG_IBC_143__IBC_APAGearReqActiveSt_g;
extern struct veh_signal CANSIG_IBC_143__IBC_LSMCtrlFaultSt_g;
extern struct veh_signal CANSIG_IBC_143__IBC_PDCUCtrlReq_g;
extern struct veh_signal CANSIG_IBC_143__IBC_TargetGearReq_g;
extern struct veh_signal CANSIG_IBC_143__IBC_VlcInterTargetAx_g;
extern struct veh_signal CANSIG_IBC_143__IBC_WHEEL_TRQ_APATReq_g;
extern struct veh_signal CANSIG_IBC_143__ICAN_143_Reserve_0_g;
extern struct veh_signal CANSIG_IBC_143__ICAN_143_Reserve_1_g;
extern struct veh_signal CANSIG_IBC_143__ICAN_143_Reserve_2_g;
extern struct veh_signal CANSIG_IBC_143__RollingCounter143_g;
extern struct veh_signal CANSIG_IBC_172__Checksum172_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeFLWSS_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeFLWSSValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeFRWSS_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeFRWSSValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeRLWSS_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeRLWSSValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeRRWSS_g;
extern struct veh_signal CANSIG_IBC_172__IBC_SumOfEdgeRRWSSValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionFLSt_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionFLValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionFRSt_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionFRValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionRLSt_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionRLValid_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionRRSt_g;
extern struct veh_signal CANSIG_IBC_172__IBC_WheelDirectionRRValid_g;
extern struct veh_signal CANSIG_IBC_172__ICAN_172_Reserve_0_g;
extern struct veh_signal CANSIG_IBC_172__RollingCounter172_g;
extern struct veh_signal CANSIG_IBC_A2__Checksum0A2_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_ABSActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_ABSFaultStSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_AVHAvailableSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_AVHSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_BrakelightReq_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_CDPActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_CDPAvailableSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_DTCActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_EBDActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_EBDFaultSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_ESCFaultSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_ESCLampDisp_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_ESCoffSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_HAZActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_HBAActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_HDCAvailableSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_HDCSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_HHCActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_HHCAvailableSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_ModeSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_TCSActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VDCActiveSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VehicleDrivingDirection_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VehicleHoldSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VehicleSpeed_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VehicleSpeedValid_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VehicleStandstillSt_g;
extern struct veh_signal CANSIG_IBC_A2__IBC_VehicleStandstillValid_g;
extern struct veh_signal CANSIG_IBC_A2__ICAN_0A2_Reserve_0_g;
extern struct veh_signal CANSIG_IBC_A2__ICAN_0A2_Reserve_1_g;
extern struct veh_signal CANSIG_IBC_A2__ICAN_0A2_Reserve_2_g;
extern struct veh_signal CANSIG_IBC_A2__ICAN_0A2_Reserve_3_g;
extern struct veh_signal CANSIG_IBC_A2__ICAN_0A2_Reserve_4_g;
extern struct veh_signal CANSIG_IBC_A2__RollingCounter0A2_g;
extern struct veh_signal CANSIG_IVI_182__HAD_CAMERA_TrqMotorMaxLimit_g;
extern struct veh_signal CANSIG_IVI_182__HAD_CAMERA_TrqMotorMinLimit_g;
extern struct veh_signal CANSIG_IVI_183__CheckSum183_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_ESAReq_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_ESASt_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_ESATorqueReq_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_LKAActivation_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_LKAAngleReq_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_LKASt_g;
extern struct veh_signal CANSIG_IVI_183__HAD_CAMERA_LKATorqFactReq_g;
extern struct veh_signal CANSIG_IVI_183__RollingCounte183_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCAccelModeSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCEPBReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCInternalSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCObjFlagSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCObjectID_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCObjectTime_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_ACCTorqueReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_AEBBrakeSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_AEBSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_AEBdbRem_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_AWBAlertReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_BrakeReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_Checksum1F3_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_DecelCtrlReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_DecelSetReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_DeceltTypeSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_DriveOffReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_FCWSt_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_HBAReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_IBCHoldReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_LKAdbRem_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_PowerWhlTqActReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_PrefillReq_g;
extern struct veh_signal CANSIG_IVI_1F3__HAD_CAMERA_RollingCount1F3_g;
extern struct veh_signal CANSIG_IVI_1F5__HAD_CAMERA_ACCLateralDst_g;
extern struct veh_signal CANSIG_IVI_1F5__HAD_CAMERA_ACCLongitudialDst_g;
extern struct veh_signal CANSIG_IVI_1F5__HAD_CAMERA_ACCSt_g;
extern struct veh_signal CANSIG_IVI_1F5__HAD_CAMERA_FDMSt_g;
extern struct veh_signal CANSIG_IVI_1F5__HAD_CAMERA_HMITargetDisp_g;
extern struct veh_signal CANSIG_IVI_1F5__HAD_CAMERA_TargetPosSt_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_AccelX_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_AccelY_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_AccelZ_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_GyroX_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_GyroY_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_GyroZ_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_ImuChecksum_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_ImuRoll_g;
extern struct veh_signal CANSIG_IVI_3A7__IVI_ImuSt_g;
extern struct veh_signal CANSIG_OBJ_EoT_FC__MCC_FC_g;
extern struct veh_signal CANSIG_OBJ_EoT_FL__MCC_FL_g;
extern struct veh_signal CANSIG_OBJ_EoT_FR__MCC_FR_g;
extern struct veh_signal CANSIG_OBJ_EoT_RL__MCC_RL_g;
extern struct veh_signal CANSIG_OBJ_EoT_RR__MCC_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__CalibrationModeFlag_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__Duration_Tme_POST_Process_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__NumObjects_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__OBJ_CycleNumber_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__OBJ_TimeStamp_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__Valid_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FC__Version_FC_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__CalibrationModeFlag_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__Duration_Tme_POST_Process_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__NumObjects_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__OBJ_CycleNumber_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__OBJ_TimeStamp_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__Valid_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FL__Version_FL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__CalibrationModeFlag_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__Duration_Tme_POST_Process_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__NumObjects_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__OBJ_CycleNumber_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__OBJ_TimeStamp_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__Valid_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_FR__Version_FR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__CalibrationModeFlag_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__Duration_Tme_POST_Process_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__NumObjects_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__OBJ_CycleNumber_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__OBJ_TimeStamp_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__Valid_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RL__Version_RL_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__CalibrationModeFlag_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__Duration_Tme_POST_Process_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__NumObjects_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__OBJ_CycleNumber_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__OBJ_TimeStamp_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__Valid_RR_g;
extern struct veh_signal CANSIG_OBJ_HEADER_RR__Version_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_Half_FCength_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_Half_Width_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_Orientation_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_YawRate_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_a_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_v_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_x_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_xy_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Cov_y_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__ExistenceProbability_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Frame_ID_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Length_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Object_ID_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Orientation_rad_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__TrackAge_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__Width_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__YawRate_rad_s_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__a_m_s_s_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__v_m_s_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__x_m_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FC__y_m_YY_FC_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_Half_Length_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_Half_Width_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_Orientation_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_YawRate_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_a_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_v_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_x_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_xy_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Cov_y_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__ExistenceProbability_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Frame_ID_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Length_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Object_ID_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Orientation_rad_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__TrackAge_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__Width_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__YawRate_rad_s_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__a_m_s_s_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__v_m_s_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__x_m_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FL__y_m_YY_FL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_Half_Length_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_Half_Width_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_Orientation_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_YawRate_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_a_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_v_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_x_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_xy_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Cov_y_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__ExistenceProbability_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Frame_ID_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Length_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Object_ID_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Orientation_rad_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__TrackAge_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__Width_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__YawRate_rad_s_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__a_m_s_s_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__v_m_s_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__x_m_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_FR__y_m_YY_FR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_Half_Length_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_Half_Width_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_Orientation_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_YawRate_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_a_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_v_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_x_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_xy_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Cov_y_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__ExistenceProbability_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Frame_ID_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Length_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Object_ID_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Orientation_rad_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__TrackAge_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__Width_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__YawRate_rad_s_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__a_m_s_s_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__v_m_s_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__x_m_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RL__y_m_YY_RL_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_Half_Length_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_Half_Width_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_Orientation_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_YawRate_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_a_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_v_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_x_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_xy_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Cov_y_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__ExistenceProbability_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Frame_ID_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Length_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Object_ID_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Orientation_rad_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__TrackAge_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__Width_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__YawRate_rad_s_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__a_m_s_s_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__v_m_s_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__x_m_YY_RR_g;
extern struct veh_signal CANSIG_OBJ_LIST_YY_RR__y_m_YY_RR_g;
extern struct veh_signal CANSIG_PDCU_1C8__Checksum1C8_g;
extern struct veh_signal CANSIG_PDCU_1C8__ICAN_1C8_Reserve_0_g;
extern struct veh_signal CANSIG_PDCU_1C8__ICAN_1C8_Reserve_1_g;
extern struct veh_signal CANSIG_PDCU_1C8__PDCU_AccelPedalSt_g;
extern struct veh_signal CANSIG_PDCU_1C8__PDCU_AccelPedalValid_g;
extern struct veh_signal CANSIG_PDCU_1C8__PDCU_ActualGear_g;
extern struct veh_signal CANSIG_PDCU_1C8__PDCU_ActualGearValid_g;
extern struct veh_signal CANSIG_PDCU_1C8__PDCU_TargetGear_g;
extern struct veh_signal CANSIG_PDCU_1C8__PDCU_TargetGearValid_g;
extern struct veh_signal CANSIG_PDCU_1C8__RollingCounter1C8_g;
extern struct veh_signal CANSIG_PDCU_364__PDCU_ChrgCnctSt_g;
extern struct veh_signal CANSIG_PDCU_364__PDCU_ChrgSt_g;
extern struct veh_signal CANSIG_PDCU_37E__PDCU_VirtAccrPedPosSt_g;
extern struct veh_signal CANSIG_PDCU_37E__PDCU_VirtAccrPedPosnValid_g;
extern struct veh_signal CANSIG_PDCU_FC__Checksum0FC_g;
extern struct veh_signal CANSIG_PDCU_FC__ICAN_0FC_Reserve_0_g;
extern struct veh_signal CANSIG_PDCU_FC__ICAN_0FC_Reserve_1_g;
extern struct veh_signal CANSIG_PDCU_FC__ICAN_0FC_Reserve_2_g;
extern struct veh_signal CANSIG_PDCU_FC__ICAN_0FC_Reserve_3_g;
extern struct veh_signal CANSIG_PDCU_FC__ICAN_0FC_Reserve_4_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_AccPedalVirtlValid_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_AccelPedalVirtualSt_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_FAWhlTqAct_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_FAWhlTqMax_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_FAWhlTqRegenMax_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_FAWhlTqRegenMaxValid_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_FrontMotorRpm_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_MotRegenTrqInDTotal_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_MotRegenTrqInDTotalVld_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_OverrideSt_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_RAWhlTqAct_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_RAWhlTqMax_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_RAWhlTqRegenMax_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_RAWhlTqRegenMaxValid_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_frontMotorRpmValid_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_rearMotRpm_g;
extern struct veh_signal CANSIG_PDCU_FC__PDCU_rearMotRpmValid_g;
extern struct veh_signal CANSIG_PDCU_FC__RollingCounter0FC_g;
extern struct veh_signal CANSIG_PDCU_FE__Checksum0FE_g;
extern struct veh_signal CANSIG_PDCU_FE__ICAN_0FE_Reserve_0_g;
extern struct veh_signal CANSIG_PDCU_FE__ICAN_0FE_Reserve_1_g;
extern struct veh_signal CANSIG_PDCU_FE__ICAN_0FE_Reserve_2_g;
extern struct veh_signal CANSIG_PDCU_FE__ICAN_0FE_Reserve_3_g;
extern struct veh_signal CANSIG_PDCU_FE__PDCU_BMS_SOCCalculate_g;
extern struct veh_signal CANSIG_PDCU_FE__PDCU_BrakePedalSt_g;
extern struct veh_signal CANSIG_PDCU_FE__PDCU_DriveModeSt_g;
extern struct veh_signal CANSIG_PDCU_FE__PDCU_DriveReadySt_g;
extern struct veh_signal CANSIG_PDCU_FE__PDCU_EPWTSt_g;
extern struct veh_signal CANSIG_PDCU_FE__PDCU_ShiftLvlPosnSt_g;
extern struct veh_signal CANSIG_PDCU_FE__RollingCounter0FE_g;
extern struct veh_signal CANSIG_PDCU_FF__Checksum0FF_g;
extern struct veh_signal CANSIG_PDCU_FF__EMS_PowertrainControlSt_g;
extern struct veh_signal CANSIG_PDCU_FF__ICAN_0FF_Reserve_0_g;
extern struct veh_signal CANSIG_PDCU_FF__ICAN_0FF_Reserve_1_g;
extern struct veh_signal CANSIG_PDCU_FF__ICAN_0FF_Reserve_2_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_ACCFunIhibitionReq_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_ACCResponseSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_ACCcontrolAvailableSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_AxleTorqueSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_DriveAsiSttACCSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_DriveAsiStt_APA_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_MCUF_InverterWorkSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_MCUR_InverterWorkSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_MaxVehicleWhlTq_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_RealizedPowertrainWhlTq_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_ShiftAvaibleSt_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_ShiftQuitReason_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_TargetDriverTorq_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_TotalCoastRegnTqTarget_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_TotalCoastRegnTqTrgVald_g;
extern struct veh_signal CANSIG_PDCU_FF__PDCU_WhetherACCReqRealizedSt_g;
extern struct veh_signal CANSIG_PDCU_FF__RollingCounter0FF_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_LateralAccelarationSt_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_LateralAccelareValid_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_LongitdAcclerValid_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_LongitudAccelerationSt_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_SensorCalSt_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_YawRateSt_g;
extern struct veh_signal CANSIG_SGW_C_119__ACU_YawrateValiditySt_g;
extern struct veh_signal CANSIG_SGW_C_119__CheckSum119_g;
extern struct veh_signal CANSIG_SGW_C_119__ICAN_119_Reserve_0_g;
extern struct veh_signal CANSIG_SGW_C_119__RollingCounter119_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_AUTOSwSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_AutoFrontWiper_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_BackDoorOpenSwFbSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_BackDoorUnlockReq_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_BackFogLampSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_EngHoodUnlockWarn_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_FrontFogLampSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_FrontWiperWorkSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_HazardLampSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_HazardSwrSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_HighBeamSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_LFDoorSwSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_LRDoorSwSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_LeftTurnSwSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_LetfligthFaultSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_LetfligthSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_LowBeamSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_PosLampSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_RFDoorSwSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_RRDoorSwSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_RightTurnSwrSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_RightligthFaultSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_RightligthSt_g;
extern struct veh_signal CANSIG_SGW_C_23A__VIUL_TrunkSt_g;
extern struct veh_signal CANSIG_SGW_C_30A__Checksum30A_g;
extern struct veh_signal CANSIG_SGW_C_30A__ICAN_30A_Reserve_0_g;
extern struct veh_signal CANSIG_SGW_C_30A__ICAN_30A_Reserve_1_g;
extern struct veh_signal CANSIG_SGW_C_30A__ICAN_30A_Reserve_2_g;
extern struct veh_signal CANSIG_SGW_C_30A__OTA_EstimatedUpgradeTime_g;
extern struct veh_signal CANSIG_SGW_C_30A__OTA_ModeSt_g;
extern struct veh_signal CANSIG_SGW_C_30A__RollingCounter30A_g;
extern struct veh_signal CANSIG_SGW_C_30C__ACU_CrashoutSt_g;
extern struct veh_signal CANSIG_SGW_C_30C__ACU_DriverBeltSwSigSt_g;
extern struct veh_signal CANSIG_SGW_C_320__IC_TotalOdmeter_g;
extern struct veh_signal CANSIG_SGW_C_322__VIUR_AC_AmbTempSt_g;
extern struct veh_signal CANSIG_SGW_C_33C__VIUL_BrakeLampSt_g;
extern struct veh_signal CANSIG_SGW_C_33C__VIUL_CHMSLSt_g;
extern struct veh_signal CANSIG_SGW_C_33C__VIUL_DoorLockSt_g;
extern struct veh_signal CANSIG_SGW_C_33C__VIUL_FrontWiperSwSt_g;
extern struct veh_signal CANSIG_SGW_C_33C__VIUL_IgnitionSt_g;
extern struct veh_signal CANSIG_SGW_C_33D__VIUL_IHBCFunEn_g;
extern struct veh_signal CANSIG_SGW_C_341__VIUL_BattVoltSt_g;
extern struct veh_signal CANSIG_SGW_C_346__VIUL_PEPS_EngineStartMode_g;
extern struct veh_signal CANSIG_SGW_C_346__VIUL_PEPS_RKECommand2_g;
extern struct veh_signal CANSIG_SGW_C_365__IVI_WashCarModeReq_g;
extern struct veh_signal CANSIG_SGW_C_367__IVI_HazardLampCtrlReq_g;
extern struct veh_signal CANSIG_SGW_C_369__IC_VehSpd_g;
extern struct veh_signal CANSIG_SGW_C_3F0__DKM_BLEAPAModeSelectReq_g;
extern struct veh_signal CANSIG_SGW_C_3F0__DKM_BLEAPASwReq_g;
extern struct veh_signal CANSIG_SGW_C_3F0__DKM_BLEAPPAPAMoveReq_g;
extern struct veh_signal CANSIG_SGW_C_3F0__DKM_BLEAPPRPAParkReq_g;
extern struct veh_signal CANSIG_SGW_C_3F0__DKM_BLEConnectSt_g;
extern struct veh_signal CANSIG_SGW_C_40__VIUL_PEPS_DoorLockReq_g;
extern struct veh_signal CANSIG_SGW_C_40__VIUL_PEPS_RKECommand_g;
extern struct veh_signal CANSIG_SGW_C_40__VIUL_PEPS_WeclomeunLockReq_g;
extern struct veh_signal CANSIG_SGW_C_4AF__CWB_DimmingSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4AF__CWB_FrontWiperSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4AF__CWB_PreWashingSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4AF__CWB_RearWiperSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4AF__CWB_TurnSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_APAFunModuleEnIndicate_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_APAModeSelectReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_APASwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVM2D3DModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMActivationReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMBodyColorReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMCalibrateStartReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMHardTouchSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMLineSetSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMPathModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMPerspectiveChassisSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMSteerWhlActiveReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMUltrasonicRadarActiveReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_AVMUltrasonicRadarDispReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_NaviDistanceSt_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_NaviInterConnectSt_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_NaviTurnReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_PuposeKeyReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_RequestAVMReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_ScreenSt_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_SideviewCtrlReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_TRFunModuleEnIndicate_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_TRSwRecoverSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_TRSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_TouchButtonPressReq_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_TouchXCoordinate_g;
extern struct veh_signal CANSIG_SGW_C_4C0__IVI_TouchYCoordinate_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_ACCFollowStpTimeSetReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_ADASSpeedLimit_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_AEBSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_AutoPilotSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_AvoidanceSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_BSDAutoCaliActivationReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_BSDLCWActivationReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_DOWActivationReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_DriveModeRembSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_ELKSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_ESAActivationSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_FCWSensitivitySetReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_FCWSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_FDMSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_FrontCrossSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_FrontCrossmodeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_ISAModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_ISASpeedDevSetReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_JAAlertSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_LKAModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_LKAmainSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_LSSSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_P2PLCCModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_P2PRemideModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_P2PSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_RCWActivationReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_RearCrossModeReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_RearCrossSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_SASwReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_TLCModeSelectionReq_g;
extern struct veh_signal CANSIG_SGW_C_4C1__IVI_TSRSwReq_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeDate_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeHour_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeMinute_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeMonth_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeSecond_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeValid_g;
extern struct veh_signal CANSIG_SGW_C_4DA__TBOX_TimeYear_g;
extern struct veh_signal CANSIG_SGW_C_710__CheckSum710_g;
extern struct veh_signal CANSIG_SGW_C_710__HIL_LateralAccelSt_g;
extern struct veh_signal CANSIG_SGW_C_710__HIL_LongitudeAccelSt_g;
extern struct veh_signal CANSIG_SGW_C_710__HIL_PitchRateSt_g;
extern struct veh_signal CANSIG_SGW_C_710__HIL_RollRateSt_g;
extern struct veh_signal CANSIG_SGW_C_710__HIL_VerticalAccelSt_g;
extern struct veh_signal CANSIG_SGW_C_710__HIL_YawRateSt_g;
extern struct veh_signal CANSIG_SGW_C_710__RollingCounter710_g;
extern struct veh_signal CANSIG_SGW_C_83__EMS_EngSpd_g;
extern struct veh_signal CANSIG_SGW_I_304__POT_BackDoorAntiplayWarn_g;
extern struct veh_signal CANSIG_SGW_I_304__POT_BackDoorCloseFailWarn_g;
extern struct veh_signal CANSIG_SGW_I_304__POT_BackDoorHeightSetSt_g;
extern struct veh_signal CANSIG_SGW_I_304__POT_BackDoorOpenFailWarn_g;
extern struct veh_signal CANSIG_SGW_I_304__POT_BackDoorOpenTurnIdicator_g;
extern struct veh_signal CANSIG_SGW_I_304__POT_BackDoorPosSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_AirbagLampSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_CrashoutSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_DriverBeltSwSigSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_PassengerBeltSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_PassengerSeatSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_RLBeltSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_RRBeltSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__ACU_RearMiddleBeltSt_g;
extern struct veh_signal CANSIG_SGW_I_30C__CheckSum30C_g;
extern struct veh_signal CANSIG_SGW_I_30C__RollingCounter30C_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ALBrighenessSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ALColorSetFbSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ALFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ARDoorFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_AntDisEnSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_AutoMirrorFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_AutoUnlockFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_AutoWiperEnFbSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_BLSSignalWarn_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_CourtesyLightsSwSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ForbidVehStartEnSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_GearReverseRearWipFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_IHBCFunEn_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_IHBCFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_IHBCStausInd_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_LockCmdWinFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_LockGapWinFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_LockLampPatternFbSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ParkingModeSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ParkingSayHiPatternFbSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_ParkingSayHiSetSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_RainSnowModeSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_RainfallClosWinFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_RomanticModeSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_SmokeModeSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_TWLampFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_TWUnLockFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_TailgateDoorLockFunSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_UnlockLampPatternFbSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_UnlockWelcomeLampSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_VoiceRoofCtrSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_WashCarModeARFbSt_g;
extern struct veh_signal CANSIG_SGW_I_33D__VIUL_WashModeRearMirrorFbSt_g;

extern void CAN_SetRawMessage_OBJ_EoT_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_40(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_HEADER_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_LIST_YY_FL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_83(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_A2(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_EPS_A5(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_EPS_B0(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_PDCU_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_PDCU_FE(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_PDCU_FF(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_101(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_119(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_11D(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_12C(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_133(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_143(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_EPS_166(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_EPS_167(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_IBC_172(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_EPS_17A(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_PDCU_1C8(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_23A(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_EoT_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_HEADER_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_LIST_YY_FR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_I_304(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_30A(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_I_30C(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_30C(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_320(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_322(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_33C(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_I_33D(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_33D(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_EoT_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_HEADER_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_341(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_LIST_YY_FC(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_346(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_PDCU_364(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_365(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_367(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_369(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_PDCU_37E(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_3F0(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_EoT_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_HEADER_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_LIST_YY_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_4AF(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_4C0(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_4C1(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_4DA(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_EoT_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_HEADER_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_OBJ_LIST_YY_RR(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_SGW_C_710(uint8_t *values, uint32_t length);

extern void CAN_ResetMessage_OBJ_EoT_FL(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_40(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_HEADER_FL(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_LIST_YY_FL(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_83(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_A2(enum reset_flg flags);
extern void CAN_ResetMessage_EPS_A5(enum reset_flg flags);
extern void CAN_ResetMessage_EPS_B0(enum reset_flg flags);
extern void CAN_ResetMessage_PDCU_FC(enum reset_flg flags);
extern void CAN_ResetMessage_PDCU_FE(enum reset_flg flags);
extern void CAN_ResetMessage_PDCU_FF(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_101(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_119(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_11D(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_12C(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_133(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_143(enum reset_flg flags);
extern void CAN_ResetMessage_EPS_166(enum reset_flg flags);
extern void CAN_ResetMessage_EPS_167(enum reset_flg flags);
extern void CAN_ResetMessage_IBC_172(enum reset_flg flags);
extern void CAN_ResetMessage_EPS_17A(enum reset_flg flags);
extern void CAN_ResetMessage_PDCU_1C8(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_23A(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_EoT_FR(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_HEADER_FR(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_LIST_YY_FR(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_I_304(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_30A(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_I_30C(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_30C(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_320(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_322(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_33C(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_I_33D(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_33D(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_EoT_FC(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_HEADER_FC(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_341(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_LIST_YY_FC(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_346(enum reset_flg flags);
extern void CAN_ResetMessage_PDCU_364(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_365(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_367(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_369(enum reset_flg flags);
extern void CAN_ResetMessage_PDCU_37E(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_3F0(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_EoT_RL(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_HEADER_RL(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_LIST_YY_RL(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_4AF(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_4C0(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_4C1(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_4DA(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_EoT_RR(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_HEADER_RR(enum reset_flg flags);
extern void CAN_ResetMessage_OBJ_LIST_YY_RR(enum reset_flg flags);
extern void CAN_ResetMessage_SGW_C_710(enum reset_flg flags);

extern void CAN_MessageElapseTime(int bus_id, int time_ms, int restart);


extern void CAN_Message_RDLock_EPS_166(void);
extern void CAN_Message_RDLock_EPS_167(void);
extern void CAN_Message_RDLock_EPS_17A(void);
extern void CAN_Message_RDLock_EPS_A5(void);
extern void CAN_Message_RDLock_EPS_B0(void);
extern void CAN_Message_RDLock_IBC_101(void);
extern void CAN_Message_RDLock_IBC_11D(void);
extern void CAN_Message_RDLock_IBC_12C(void);
extern void CAN_Message_RDLock_IBC_133(void);
extern void CAN_Message_RDLock_IBC_143(void);
extern void CAN_Message_RDLock_IBC_172(void);
extern void CAN_Message_RDLock_IBC_A2(void);
extern void CAN_Message_RDLock_IVI_182(void);
extern void CAN_Message_RDLock_IVI_183(void);
extern void CAN_Message_RDLock_IVI_1F3(void);
extern void CAN_Message_RDLock_IVI_1F5(void);
extern void CAN_Message_RDLock_IVI_3A7(void);
extern void CAN_Message_RDLock_OBJ_EoT_FC(void);
extern void CAN_Message_RDLock_OBJ_EoT_FL(void);
extern void CAN_Message_RDLock_OBJ_EoT_FR(void);
extern void CAN_Message_RDLock_OBJ_EoT_RL(void);
extern void CAN_Message_RDLock_OBJ_EoT_RR(void);
extern void CAN_Message_RDLock_OBJ_HEADER_FC(void);
extern void CAN_Message_RDLock_OBJ_HEADER_FL(void);
extern void CAN_Message_RDLock_OBJ_HEADER_FR(void);
extern void CAN_Message_RDLock_OBJ_HEADER_RL(void);
extern void CAN_Message_RDLock_OBJ_HEADER_RR(void);
extern void CAN_Message_RDLock_OBJ_LIST_YY_FC(void);
extern void CAN_Message_RDLock_OBJ_LIST_YY_FL(void);
extern void CAN_Message_RDLock_OBJ_LIST_YY_FR(void);
extern void CAN_Message_RDLock_OBJ_LIST_YY_RL(void);
extern void CAN_Message_RDLock_OBJ_LIST_YY_RR(void);
extern void CAN_Message_RDLock_PDCU_1C8(void);
extern void CAN_Message_RDLock_PDCU_364(void);
extern void CAN_Message_RDLock_PDCU_37E(void);
extern void CAN_Message_RDLock_PDCU_FC(void);
extern void CAN_Message_RDLock_PDCU_FE(void);
extern void CAN_Message_RDLock_PDCU_FF(void);
extern void CAN_Message_RDLock_SGW_C_119(void);
extern void CAN_Message_RDLock_SGW_C_23A(void);
extern void CAN_Message_RDLock_SGW_C_30A(void);
extern void CAN_Message_RDLock_SGW_C_30C(void);
extern void CAN_Message_RDLock_SGW_C_320(void);
extern void CAN_Message_RDLock_SGW_C_322(void);
extern void CAN_Message_RDLock_SGW_C_33C(void);
extern void CAN_Message_RDLock_SGW_C_33D(void);
extern void CAN_Message_RDLock_SGW_C_341(void);
extern void CAN_Message_RDLock_SGW_C_346(void);
extern void CAN_Message_RDLock_SGW_C_365(void);
extern void CAN_Message_RDLock_SGW_C_367(void);
extern void CAN_Message_RDLock_SGW_C_369(void);
extern void CAN_Message_RDLock_SGW_C_3F0(void);
extern void CAN_Message_RDLock_SGW_C_40(void);
extern void CAN_Message_RDLock_SGW_C_4AF(void);
extern void CAN_Message_RDLock_SGW_C_4C0(void);
extern void CAN_Message_RDLock_SGW_C_4C1(void);
extern void CAN_Message_RDLock_SGW_C_4DA(void);
extern void CAN_Message_RDLock_SGW_C_710(void);
extern void CAN_Message_RDLock_SGW_C_83(void);
extern void CAN_Message_RDLock_SGW_I_304(void);
extern void CAN_Message_RDLock_SGW_I_30C(void);
extern void CAN_Message_RDLock_SGW_I_33D(void);

extern void CAN_Message_WRLock_EPS_166(void);
extern void CAN_Message_WRLock_EPS_167(void);
extern void CAN_Message_WRLock_EPS_17A(void);
extern void CAN_Message_WRLock_EPS_A5(void);
extern void CAN_Message_WRLock_EPS_B0(void);
extern void CAN_Message_WRLock_IBC_101(void);
extern void CAN_Message_WRLock_IBC_11D(void);
extern void CAN_Message_WRLock_IBC_12C(void);
extern void CAN_Message_WRLock_IBC_133(void);
extern void CAN_Message_WRLock_IBC_143(void);
extern void CAN_Message_WRLock_IBC_172(void);
extern void CAN_Message_WRLock_IBC_A2(void);
extern void CAN_Message_WRLock_IVI_182(void);
extern void CAN_Message_WRLock_IVI_183(void);
extern void CAN_Message_WRLock_IVI_1F3(void);
extern void CAN_Message_WRLock_IVI_1F5(void);
extern void CAN_Message_WRLock_IVI_3A7(void);
extern void CAN_Message_WRLock_OBJ_EoT_FC(void);
extern void CAN_Message_WRLock_OBJ_EoT_FL(void);
extern void CAN_Message_WRLock_OBJ_EoT_FR(void);
extern void CAN_Message_WRLock_OBJ_EoT_RL(void);
extern void CAN_Message_WRLock_OBJ_EoT_RR(void);
extern void CAN_Message_WRLock_OBJ_HEADER_FC(void);
extern void CAN_Message_WRLock_OBJ_HEADER_FL(void);
extern void CAN_Message_WRLock_OBJ_HEADER_FR(void);
extern void CAN_Message_WRLock_OBJ_HEADER_RL(void);
extern void CAN_Message_WRLock_OBJ_HEADER_RR(void);
extern void CAN_Message_WRLock_OBJ_LIST_YY_FC(void);
extern void CAN_Message_WRLock_OBJ_LIST_YY_FL(void);
extern void CAN_Message_WRLock_OBJ_LIST_YY_FR(void);
extern void CAN_Message_WRLock_OBJ_LIST_YY_RL(void);
extern void CAN_Message_WRLock_OBJ_LIST_YY_RR(void);
extern void CAN_Message_WRLock_PDCU_1C8(void);
extern void CAN_Message_WRLock_PDCU_364(void);
extern void CAN_Message_WRLock_PDCU_37E(void);
extern void CAN_Message_WRLock_PDCU_FC(void);
extern void CAN_Message_WRLock_PDCU_FE(void);
extern void CAN_Message_WRLock_PDCU_FF(void);
extern void CAN_Message_WRLock_SGW_C_119(void);
extern void CAN_Message_WRLock_SGW_C_23A(void);
extern void CAN_Message_WRLock_SGW_C_30A(void);
extern void CAN_Message_WRLock_SGW_C_30C(void);
extern void CAN_Message_WRLock_SGW_C_320(void);
extern void CAN_Message_WRLock_SGW_C_322(void);
extern void CAN_Message_WRLock_SGW_C_33C(void);
extern void CAN_Message_WRLock_SGW_C_33D(void);
extern void CAN_Message_WRLock_SGW_C_341(void);
extern void CAN_Message_WRLock_SGW_C_346(void);
extern void CAN_Message_WRLock_SGW_C_365(void);
extern void CAN_Message_WRLock_SGW_C_367(void);
extern void CAN_Message_WRLock_SGW_C_369(void);
extern void CAN_Message_WRLock_SGW_C_3F0(void);
extern void CAN_Message_WRLock_SGW_C_40(void);
extern void CAN_Message_WRLock_SGW_C_4AF(void);
extern void CAN_Message_WRLock_SGW_C_4C0(void);
extern void CAN_Message_WRLock_SGW_C_4C1(void);
extern void CAN_Message_WRLock_SGW_C_4DA(void);
extern void CAN_Message_WRLock_SGW_C_710(void);
extern void CAN_Message_WRLock_SGW_C_83(void);
extern void CAN_Message_WRLock_SGW_I_304(void);
extern void CAN_Message_WRLock_SGW_I_30C(void);
extern void CAN_Message_WRLock_SGW_I_33D(void);

extern void CAN_Message_Unlock_EPS_166(void);
extern void CAN_Message_Unlock_EPS_167(void);
extern void CAN_Message_Unlock_EPS_17A(void);
extern void CAN_Message_Unlock_EPS_A5(void);
extern void CAN_Message_Unlock_EPS_B0(void);
extern void CAN_Message_Unlock_IBC_101(void);
extern void CAN_Message_Unlock_IBC_11D(void);
extern void CAN_Message_Unlock_IBC_12C(void);
extern void CAN_Message_Unlock_IBC_133(void);
extern void CAN_Message_Unlock_IBC_143(void);
extern void CAN_Message_Unlock_IBC_172(void);
extern void CAN_Message_Unlock_IBC_A2(void);
extern void CAN_Message_Unlock_IVI_182(void);
extern void CAN_Message_Unlock_IVI_183(void);
extern void CAN_Message_Unlock_IVI_1F3(void);
extern void CAN_Message_Unlock_IVI_1F5(void);
extern void CAN_Message_Unlock_IVI_3A7(void);
extern void CAN_Message_Unlock_OBJ_EoT_FC(void);
extern void CAN_Message_Unlock_OBJ_EoT_FL(void);
extern void CAN_Message_Unlock_OBJ_EoT_FR(void);
extern void CAN_Message_Unlock_OBJ_EoT_RL(void);
extern void CAN_Message_Unlock_OBJ_EoT_RR(void);
extern void CAN_Message_Unlock_OBJ_HEADER_FC(void);
extern void CAN_Message_Unlock_OBJ_HEADER_FL(void);
extern void CAN_Message_Unlock_OBJ_HEADER_FR(void);
extern void CAN_Message_Unlock_OBJ_HEADER_RL(void);
extern void CAN_Message_Unlock_OBJ_HEADER_RR(void);
extern void CAN_Message_Unlock_OBJ_LIST_YY_FC(void);
extern void CAN_Message_Unlock_OBJ_LIST_YY_FL(void);
extern void CAN_Message_Unlock_OBJ_LIST_YY_FR(void);
extern void CAN_Message_Unlock_OBJ_LIST_YY_RL(void);
extern void CAN_Message_Unlock_OBJ_LIST_YY_RR(void);
extern void CAN_Message_Unlock_PDCU_1C8(void);
extern void CAN_Message_Unlock_PDCU_364(void);
extern void CAN_Message_Unlock_PDCU_37E(void);
extern void CAN_Message_Unlock_PDCU_FC(void);
extern void CAN_Message_Unlock_PDCU_FE(void);
extern void CAN_Message_Unlock_PDCU_FF(void);
extern void CAN_Message_Unlock_SGW_C_119(void);
extern void CAN_Message_Unlock_SGW_C_23A(void);
extern void CAN_Message_Unlock_SGW_C_30A(void);
extern void CAN_Message_Unlock_SGW_C_30C(void);
extern void CAN_Message_Unlock_SGW_C_320(void);
extern void CAN_Message_Unlock_SGW_C_322(void);
extern void CAN_Message_Unlock_SGW_C_33C(void);
extern void CAN_Message_Unlock_SGW_C_33D(void);
extern void CAN_Message_Unlock_SGW_C_341(void);
extern void CAN_Message_Unlock_SGW_C_346(void);
extern void CAN_Message_Unlock_SGW_C_365(void);
extern void CAN_Message_Unlock_SGW_C_367(void);
extern void CAN_Message_Unlock_SGW_C_369(void);
extern void CAN_Message_Unlock_SGW_C_3F0(void);
extern void CAN_Message_Unlock_SGW_C_40(void);
extern void CAN_Message_Unlock_SGW_C_4AF(void);
extern void CAN_Message_Unlock_SGW_C_4C0(void);
extern void CAN_Message_Unlock_SGW_C_4C1(void);
extern void CAN_Message_Unlock_SGW_C_4DA(void);
extern void CAN_Message_Unlock_SGW_C_710(void);
extern void CAN_Message_Unlock_SGW_C_83(void);
extern void CAN_Message_Unlock_SGW_I_304(void);
extern void CAN_Message_Unlock_SGW_I_30C(void);
extern void CAN_Message_Unlock_SGW_I_33D(void);



extern enum signal_status CAN_GetSignal_EPS_166__EPS_LKAOverlayCurrent(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_166__EPS_LKASt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_166__EPS_LKA_DriverOverrideSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_166__EPS_LKA_TorqueLimitationSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_167__EPS_DriverInitializedESASt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_167__EPS_ESACurrentOverlay(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_167__EPS_ESASt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_167__EPS_ESA_DriverOverrideSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_167__EPS_ESA_TorqueLimitationSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_CheckSum17A(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_RollingCount17A(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_SteeringAngle(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_SteeringAngleFlag(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_SteeringHoldSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_SteeringTorqueSensorSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__EPS_SteeringTorqueValue(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__ICAN_17A_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__ICAN_17A_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_17A__ICAN_17A_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__ICAN_0A5_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_CalibratedSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_CheckSumA5(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_RollingCountA5(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_SASFailureSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_SteeringAngle(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_SteeringRotSpd(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_A5__TAS_SAS_TrimmingSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__EPS_APAChecksumB0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__EPS_APAFuncModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__EPS_APAProhibitedResaon(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__EPS_APARollingCountB0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__EPS_APASt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__ICAN_0B0_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_EPS_B0__ICAN_0B0_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__Checksum101(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedFL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedFLValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedFR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedFRValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedRL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedRLValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedRR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__IBC_wheelSpeedRRValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__ICAN_101_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__ICAN_101_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__ICAN_101_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__ICAN_101_Reserve_3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_101__RollingCounter101(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_11D__IBC_PedalTravelSensor(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_11D__IBC_PedalTravelSensorSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_11D__IBC_PlungerPressure(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__Checksum12C(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_EPBCruiseControlCancelSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_EPBErrorSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_EPBSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_EPBSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_EPBSwValiditySt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_PbcActuatorLeftSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_PbcActuatorRightSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_SlopHighWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_Slope(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__IBC_SlopeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__ICAN_12C_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__ICAN_12C_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__ICAN_12C_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__ICAN_12C_Reserve_3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__ICAN_12C_Reserve_4(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_12C__RollingCounter12C(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__Checksum133(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_BrakePedalSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_BrkFricTotAtWhlsTorq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_CSTSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_EBPActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_FWAWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_SCMActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__IBC_SystemSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__ICAN_133_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__ICAN_133_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__ICAN_133_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__ICAN_133_Reserve_3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__ICAN_133_Reserve_4(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_133__RollingCounter133(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__Checksum143(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_APABrakSysLongictlSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_APABrakeModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_APAGearReqActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_LSMCtrlFaultSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_PDCUCtrlReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_TargetGearReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_VlcInterTargetAx(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__IBC_WHEEL_TRQ_APATReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__ICAN_143_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__ICAN_143_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__ICAN_143_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_143__RollingCounter143(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__Checksum172(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeFLWSS(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeFLWSSValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeFRWSS(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeFRWSSValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeRLWSS(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeRLWSSValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeRRWSS(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_SumOfEdgeRRWSSValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionFLSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionFLValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionFRSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionFRValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionRLSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionRLValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionRRSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__IBC_WheelDirectionRRValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__ICAN_172_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_172__RollingCounter172(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__Checksum0A2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_ABSActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_ABSFaultStSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_AVHAvailableSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_AVHSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_BrakelightReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_CDPActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_CDPAvailableSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_DTCActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_EBDActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_EBDFaultSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_ESCFaultSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_ESCLampDisp(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_ESCoffSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_HAZActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_HBAActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_HDCAvailableSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_HDCSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_HHCActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_HHCAvailableSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_ModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_TCSActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VDCActiveSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VehicleDrivingDirection(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VehicleHoldSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VehicleSpeed(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VehicleSpeedValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VehicleStandstillSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__IBC_VehicleStandstillValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__ICAN_0A2_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__ICAN_0A2_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__ICAN_0A2_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__ICAN_0A2_Reserve_3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__ICAN_0A2_Reserve_4(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IBC_A2__RollingCounter0A2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_182__HAD_CAMERA_TrqMotorMaxLimit(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_182__HAD_CAMERA_TrqMotorMinLimit(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__CheckSum183(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_ESAReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_ESASt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_ESATorqueReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_LKAActivation(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_LKAAngleReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_LKASt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__HAD_CAMERA_LKATorqFactReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_183__RollingCounte183(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCAccelModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCEPBReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCInternalSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCObjFlagSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCObjectID(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCObjectTime(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_ACCTorqueReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_AEBBrakeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_AEBSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_AEBdbRem(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_AWBAlertReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_BrakeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_Checksum1F3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_DecelCtrlReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_DecelSetReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_DeceltTypeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_DriveOffReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_FCWSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_HBAReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_IBCHoldReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_LKAdbRem(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_PowerWhlTqActReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_PrefillReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F3__HAD_CAMERA_RollingCount1F3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F5__HAD_CAMERA_ACCLateralDst(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F5__HAD_CAMERA_ACCLongitudialDst(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F5__HAD_CAMERA_ACCSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F5__HAD_CAMERA_FDMSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F5__HAD_CAMERA_HMITargetDisp(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_1F5__HAD_CAMERA_TargetPosSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_AccelX(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_AccelY(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_AccelZ(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_GyroX(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_GyroY(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_GyroZ(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_ImuChecksum(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_ImuRoll(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_IVI_3A7__IVI_ImuSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_EoT_FC__MCC_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_EoT_FL__MCC_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_EoT_FR__MCC_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_EoT_RL__MCC_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_EoT_RR__MCC_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__CalibrationModeFlag_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__Duration_Tme_POST_Process_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__NumObjects_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__OBJ_CycleNumber_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__OBJ_TimeStamp_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__Valid_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FC__Version_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__CalibrationModeFlag_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__Duration_Tme_POST_Process_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__NumObjects_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__OBJ_CycleNumber_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__OBJ_TimeStamp_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__Valid_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FL__Version_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__CalibrationModeFlag_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__Duration_Tme_POST_Process_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__NumObjects_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__OBJ_CycleNumber_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__OBJ_TimeStamp_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__Valid_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_FR__Version_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__CalibrationModeFlag_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__Duration_Tme_POST_Process_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__NumObjects_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__OBJ_CycleNumber_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__OBJ_TimeStamp_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__Valid_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RL__Version_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__CalibrationModeFlag_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__Duration_Tme_POST_Process_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__NumObjects_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__OBJ_CycleNumber_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__OBJ_TimeStamp_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__Valid_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_HEADER_RR__Version_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_Half_FCength_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_Half_Width_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_Orientation_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_YawRate_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_a_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_v_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_x_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_xy_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Cov_y_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__ExistenceProbability_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Frame_ID_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Length_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Object_ID_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Orientation_rad_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__TrackAge_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__Width_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__YawRate_rad_s_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__a_m_s_s_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__v_m_s_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__x_m_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FC__y_m_YY_FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_Half_Length_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_Half_Width_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_Orientation_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_YawRate_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_a_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_v_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_x_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_xy_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Cov_y_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__ExistenceProbability_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Frame_ID_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Length_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Object_ID_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Orientation_rad_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__TrackAge_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__Width_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__YawRate_rad_s_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__a_m_s_s_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__v_m_s_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__x_m_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FL__y_m_YY_FL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_Half_Length_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_Half_Width_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_Orientation_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_YawRate_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_a_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_v_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_x_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_xy_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Cov_y_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__ExistenceProbability_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Frame_ID_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Length_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Object_ID_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Orientation_rad_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__TrackAge_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__Width_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__YawRate_rad_s_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__a_m_s_s_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__v_m_s_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__x_m_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_FR__y_m_YY_FR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_Half_Length_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_Half_Width_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_Orientation_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_YawRate_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_a_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_v_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_x_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_xy_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Cov_y_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__ExistenceProbability_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Frame_ID_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Length_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Object_ID_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Orientation_rad_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__TrackAge_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__Width_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__YawRate_rad_s_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__a_m_s_s_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__v_m_s_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__x_m_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RL__y_m_YY_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_Half_Length_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_Half_Width_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_Orientation_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_YawRate_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_a_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_v_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_x_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_xy_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Cov_y_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__ExistenceProbability_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Frame_ID_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Length_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Object_ID_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Orientation_rad_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__TrackAge_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__Width_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__YawRate_rad_s_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__a_m_s_s_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__v_m_s_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__x_m_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_OBJ_LIST_YY_RR__y_m_YY_RR(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__Checksum1C8(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__ICAN_1C8_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__ICAN_1C8_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__PDCU_AccelPedalSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__PDCU_AccelPedalValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__PDCU_ActualGear(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__PDCU_ActualGearValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__PDCU_TargetGear(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__PDCU_TargetGearValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_1C8__RollingCounter1C8(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_364__PDCU_ChrgCnctSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_364__PDCU_ChrgSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_37E__PDCU_VirtAccrPedPosSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_37E__PDCU_VirtAccrPedPosnValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__Checksum0FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__ICAN_0FC_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__ICAN_0FC_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__ICAN_0FC_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__ICAN_0FC_Reserve_3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__ICAN_0FC_Reserve_4(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_AccPedalVirtlValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_AccelPedalVirtualSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_FAWhlTqAct(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_FAWhlTqMax(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_FAWhlTqRegenMax(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_FAWhlTqRegenMaxValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_FrontMotorRpm(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_MotRegenTrqInDTotal(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_MotRegenTrqInDTotalVld(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_OverrideSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_RAWhlTqAct(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_RAWhlTqMax(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_RAWhlTqRegenMax(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_RAWhlTqRegenMaxValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_frontMotorRpmValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_rearMotRpm(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__PDCU_rearMotRpmValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FC__RollingCounter0FC(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__Checksum0FE(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__ICAN_0FE_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__ICAN_0FE_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__ICAN_0FE_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__ICAN_0FE_Reserve_3(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__PDCU_BMS_SOCCalculate(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__PDCU_BrakePedalSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__PDCU_DriveModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__PDCU_DriveReadySt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__PDCU_EPWTSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__PDCU_ShiftLvlPosnSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FE__RollingCounter0FE(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__Checksum0FF(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__EMS_PowertrainControlSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__ICAN_0FF_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__ICAN_0FF_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__ICAN_0FF_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_ACCFunIhibitionReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_ACCResponseSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_ACCcontrolAvailableSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_AxleTorqueSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_DriveAsiSttACCSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_DriveAsiStt_APA(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_MCUF_InverterWorkSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_MCUR_InverterWorkSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_MaxVehicleWhlTq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_RealizedPowertrainWhlTq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_ShiftAvaibleSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_ShiftQuitReason(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_TargetDriverTorq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_TotalCoastRegnTqTarget(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_TotalCoastRegnTqTrgVald(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__PDCU_WhetherACCReqRealizedSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_PDCU_FF__RollingCounter0FF(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_LateralAccelarationSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_LateralAccelareValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_LongitdAcclerValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_LongitudAccelerationSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_SensorCalSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_YawRateSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ACU_YawrateValiditySt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__CheckSum119(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__ICAN_119_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_119__RollingCounter119(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_AUTOSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_AutoFrontWiper(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_BackDoorOpenSwFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_BackDoorUnlockReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_BackFogLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_EngHoodUnlockWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_FrontFogLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_FrontWiperWorkSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_HazardLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_HazardSwrSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_HighBeamSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_LFDoorSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_LRDoorSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_LeftTurnSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_LetfligthFaultSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_LetfligthSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_LowBeamSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_PosLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_RFDoorSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_RRDoorSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_RightTurnSwrSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_RightligthFaultSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_RightligthSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_23A__VIUL_TrunkSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__Checksum30A(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__ICAN_30A_Reserve_0(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__ICAN_30A_Reserve_1(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__ICAN_30A_Reserve_2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__OTA_EstimatedUpgradeTime(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__OTA_ModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30A__RollingCounter30A(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30C__ACU_CrashoutSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_30C__ACU_DriverBeltSwSigSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_320__IC_TotalOdmeter(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_322__VIUR_AC_AmbTempSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_33C__VIUL_BrakeLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_33C__VIUL_CHMSLSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_33C__VIUL_DoorLockSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_33C__VIUL_FrontWiperSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_33C__VIUL_IgnitionSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_33D__VIUL_IHBCFunEn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_341__VIUL_BattVoltSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_346__VIUL_PEPS_EngineStartMode(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_346__VIUL_PEPS_RKECommand2(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_365__IVI_WashCarModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_367__IVI_HazardLampCtrlReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_369__IC_VehSpd(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_3F0__DKM_BLEAPAModeSelectReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_3F0__DKM_BLEAPASwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_3F0__DKM_BLEAPPAPAMoveReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_3F0__DKM_BLEAPPRPAParkReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_3F0__DKM_BLEConnectSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_40__VIUL_PEPS_DoorLockReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_40__VIUL_PEPS_RKECommand(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_40__VIUL_PEPS_WeclomeunLockReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4AF__CWB_DimmingSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4AF__CWB_FrontWiperSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4AF__CWB_PreWashingSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4AF__CWB_RearWiperSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4AF__CWB_TurnSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_APAFunModuleEnIndicate(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_APAModeSelectReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_APASwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVM2D3DModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMActivationReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMBodyColorReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMCalibrateStartReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMHardTouchSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMLineSetSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMPathModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMPerspectiveChassisSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMSteerWhlActiveReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMUltrasonicRadarActiveReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_AVMUltrasonicRadarDispReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_NaviDistanceSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_NaviInterConnectSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_NaviTurnReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_PuposeKeyReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_RequestAVMReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_ScreenSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_SideviewCtrlReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_TRFunModuleEnIndicate(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_TRSwRecoverSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_TRSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_TouchButtonPressReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_TouchXCoordinate(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C0__IVI_TouchYCoordinate(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_ACCFollowStpTimeSetReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_ADASSpeedLimit(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_AEBSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_AutoPilotSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_AvoidanceSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_BSDAutoCaliActivationReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_BSDLCWActivationReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_DOWActivationReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_DriveModeRembSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_ELKSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_ESAActivationSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_FCWSensitivitySetReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_FCWSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_FDMSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_FrontCrossSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_FrontCrossmodeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_ISAModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_ISASpeedDevSetReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_JAAlertSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_LKAModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_LKAmainSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_LSSSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_P2PLCCModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_P2PRemideModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_P2PSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_RCWActivationReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_RearCrossModeReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_RearCrossSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_SASwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_TLCModeSelectionReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4C1__IVI_TSRSwReq(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeDate(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeHour(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeMinute(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeMonth(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeSecond(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeValid(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_4DA__TBOX_TimeYear(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__CheckSum710(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__HIL_LateralAccelSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__HIL_LongitudeAccelSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__HIL_PitchRateSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__HIL_RollRateSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__HIL_VerticalAccelSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__HIL_YawRateSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_710__RollingCounter710(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_C_83__EMS_EngSpd(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_304__POT_BackDoorAntiplayWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_304__POT_BackDoorCloseFailWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_304__POT_BackDoorHeightSetSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_304__POT_BackDoorOpenFailWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_304__POT_BackDoorOpenTurnIdicator(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_304__POT_BackDoorPosSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_AirbagLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_CrashoutSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_DriverBeltSwSigSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_PassengerBeltSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_PassengerSeatSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_RLBeltSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_RRBeltSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__ACU_RearMiddleBeltSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__CheckSum30C(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_30C__RollingCounter30C(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ALBrighenessSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ALColorSetFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ALFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ARDoorFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_AntDisEnSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_AutoMirrorFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_AutoUnlockFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_AutoWiperEnFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_BLSSignalWarn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_CourtesyLightsSwSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ForbidVehStartEnSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_GearReverseRearWipFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_IHBCFunEn(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_IHBCFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_IHBCStausInd(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_LockCmdWinFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_LockGapWinFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_LockLampPatternFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ParkingModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ParkingSayHiPatternFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_ParkingSayHiSetSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_RainSnowModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_RainfallClosWinFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_RomanticModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_SmokeModeSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_TWLampFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_TWUnLockFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_TailgateDoorLockFunSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_UnlockLampPatternFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_UnlockWelcomeLampSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_VoiceRoofCtrSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_WashCarModeARFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_SGW_I_33D__VIUL_WashModeRearMirrorFbSt(union veh_signal_value *physical_value, union veh_signal_value *raw_value);

extern void CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
extern void CAN_PARSER_Init(void);
extern void CAN_PARSER_MSG_Init(struct veh_message *p_message_list[]);

//=====================================================================================//



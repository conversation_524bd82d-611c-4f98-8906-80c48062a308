#include "parser_typedef.h"
#pragma once
//=====================================================================================//
enum can_ecu_id
{
    eCAN_ECU_ADC = 0,
    eCAN_ECU_AMP = 1,
    eCAN_ECU_BSD_M = 2,
    eCAN_ECU_BSD_S = 3,
    eCAN_ECU_CMS = 4,
    eCAN_ECU_EPS = 5,
    eCAN_ECU_FC = 6,
    eCAN_ECU_FL = 7,
    eCAN_ECU_FR = 8,
    eCAN_ECU_GW = 9,
    eCAN_ECU_HUD = 10,
    eCAN_ECU_IBC = 11,
    eCAN_ECU_IVI = 12,
    eCAN_ECU_MLMR = 13,
    eCAN_ECU_PDCU = 14,
    eCAN_ECU_RL = 15,
    eCAN_ECU_RR = 16,
    eCAN_ECU_SGW = 17,
    eCAN_ECU_SGW_C = 18,
    eCAN_ECU_SGW_I = 19,
    eCAN_ECU_T_BOX = 20,
    eCAN_ECU_W_HUD = 21,
    /////,
    eCAN_ECU_NUM = 22,
};

#define CANMSG_EPS_166                                           (0x000000|(UID_CLASS_CAN<<4))
#define CANMSG_EPS_167                                           (0x000100|(UID_CLASS_CAN<<4))
#define CANMSG_EPS_17A                                           (0x000200|(UID_CLASS_CAN<<4))
#define CANMSG_EPS_A5                                            (0x000300|(UID_CLASS_CAN<<4))
#define CANMSG_EPS_B0                                            (0x000400|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_101                                           (0x000500|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_11D                                           (0x000600|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_12C                                           (0x000700|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_133                                           (0x000800|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_143                                           (0x000900|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_172                                           (0x000A00|(UID_CLASS_CAN<<4))
#define CANMSG_IBC_A2                                            (0x000B00|(UID_CLASS_CAN<<4))
#define CANMSG_IVI_182                                           (0x000C00|(UID_CLASS_CAN<<4))
#define CANMSG_IVI_183                                           (0x000D00|(UID_CLASS_CAN<<4))
#define CANMSG_IVI_1F3                                           (0x000E00|(UID_CLASS_CAN<<4))
#define CANMSG_IVI_1F5                                           (0x000F00|(UID_CLASS_CAN<<4))
#define CANMSG_IVI_3A7                                           (0x001000|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_EOT_FC                                        (0x001100|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_EOT_FL                                        (0x001200|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_EOT_FR                                        (0x001300|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_EOT_RL                                        (0x001400|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_EOT_RR                                        (0x001500|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_HEADER_FC                                     (0x001600|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_HEADER_FL                                     (0x001700|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_HEADER_FR                                     (0x001800|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_HEADER_RL                                     (0x001900|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_HEADER_RR                                     (0x001A00|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_LIST_YY_FC                                    (0x001B00|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_LIST_YY_FL                                    (0x001C00|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_LIST_YY_FR                                    (0x001D00|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_LIST_YY_RL                                    (0x001E00|(UID_CLASS_CAN<<4))
#define CANMSG_OBJ_LIST_YY_RR                                    (0x001F00|(UID_CLASS_CAN<<4))
#define CANMSG_PDCU_1C8                                          (0x002000|(UID_CLASS_CAN<<4))
#define CANMSG_PDCU_364                                          (0x002100|(UID_CLASS_CAN<<4))
#define CANMSG_PDCU_37E                                          (0x002200|(UID_CLASS_CAN<<4))
#define CANMSG_PDCU_FC                                           (0x002300|(UID_CLASS_CAN<<4))
#define CANMSG_PDCU_FE                                           (0x002400|(UID_CLASS_CAN<<4))
#define CANMSG_PDCU_FF                                           (0x002500|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_119                                         (0x002600|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_23A                                         (0x002700|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_30A                                         (0x002800|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_30C                                         (0x002900|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_320                                         (0x002A00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_322                                         (0x002B00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_33C                                         (0x002C00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_33D                                         (0x002D00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_341                                         (0x002E00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_346                                         (0x002F00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_365                                         (0x003000|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_367                                         (0x003100|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_369                                         (0x003200|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_3F0                                         (0x003300|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_40                                          (0x003400|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_4AF                                         (0x003500|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_4C0                                         (0x003600|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_4C1                                         (0x003700|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_4DA                                         (0x003800|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_710                                         (0x003900|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_C_83                                          (0x003A00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_I_304                                         (0x003B00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_I_30C                                         (0x003C00|(UID_CLASS_CAN<<4))
#define CANMSG_SGW_I_33D                                         (0x003D00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_166__EPS_LKAOVERLAYCURRENT                    (0x003E00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_166__EPS_LKAST                                (0x003F00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_166__EPS_LKA_DRIVEROVERRIDEST                 (0x004000|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_166__EPS_LKA_TORQUELIMITATIONST               (0x004100|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_167__EPS_DRIVERINITIALIZEDESAST               (0x004200|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_167__EPS_ESACURRENTOVERLAY                    (0x004300|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_167__EPS_ESAST                                (0x004400|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_167__EPS_ESA_DRIVEROVERRIDEST                 (0x004500|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_167__EPS_ESA_TORQUELIMITATIONST               (0x004600|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_CHECKSUM17A                          (0x004700|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_ROLLINGCOUNT17A                      (0x004800|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_STEERINGANGLE                        (0x004900|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_STEERINGANGLEFLAG                    (0x004A00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_STEERINGHOLDST                       (0x004B00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_STEERINGTORQUESENSORST               (0x004C00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__EPS_STEERINGTORQUEVALUE                  (0x004D00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__ICAN_17A_RESERVE_0                       (0x004E00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__ICAN_17A_RESERVE_1                       (0x004F00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_17A__ICAN_17A_RESERVE_2                       (0x005000|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__ICAN_0A5_RESERVE_0                        (0x005100|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_CALIBRATEDST                      (0x005200|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_CHECKSUMA5                        (0x005300|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_ROLLINGCOUNTA5                    (0x005400|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_SASFAILUREST                      (0x005500|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_STEERINGANGLE                     (0x005600|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_STEERINGROTSPD                    (0x005700|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_A5__TAS_SAS_TRIMMINGST                        (0x005800|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__EPS_APACHECKSUMB0                         (0x005900|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__EPS_APAFUNCMODEST                         (0x005A00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__EPS_APAPROHIBITEDRESAON                   (0x005B00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__EPS_APAROLLINGCOUNTB0                     (0x005C00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__EPS_APAST                                 (0x005D00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__ICAN_0B0_RESERVE_0                        (0x005E00|(UID_CLASS_CAN<<4))
#define CANSIG_EPS_B0__ICAN_0B0_RESERVE_1                        (0x005F00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__CHECKSUM101                              (0x006000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDFL                         (0x006100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDFLVALID                    (0x006200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDFR                         (0x006300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDFRVALID                    (0x006400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDRL                         (0x006500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDRLVALID                    (0x006600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDRR                         (0x006700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__IBC_WHEELSPEEDRRVALID                    (0x006800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__ICAN_101_RESERVE_0                       (0x006900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__ICAN_101_RESERVE_1                       (0x006A00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__ICAN_101_RESERVE_2                       (0x006B00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__ICAN_101_RESERVE_3                       (0x006C00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_101__ROLLINGCOUNTER101                        (0x006D00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_11D__IBC_PEDALTRAVELSENSOR                    (0x006E00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_11D__IBC_PEDALTRAVELSENSORST                  (0x006F00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_11D__IBC_PLUNGERPRESSURE                      (0x007000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__CHECKSUM12C                              (0x007100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_EPBCRUISECONTROLCANCELST             (0x007200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_EPBERRORST                           (0x007300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_EPBST                                (0x007400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_EPBSWST                              (0x007500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_EPBSWVALIDITYST                      (0x007600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_PBCACTUATORLEFTST                    (0x007700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_PBCACTUATORRIGHTST                   (0x007800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_SLOPHIGHWARN                         (0x007900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_SLOPE                                (0x007A00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__IBC_SLOPEST                              (0x007B00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__ICAN_12C_RESERVE_0                       (0x007C00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__ICAN_12C_RESERVE_1                       (0x007D00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__ICAN_12C_RESERVE_2                       (0x007E00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__ICAN_12C_RESERVE_3                       (0x007F00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__ICAN_12C_RESERVE_4                       (0x008000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_12C__ROLLINGCOUNTER12C                        (0x008100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__CHECKSUM133                              (0x008200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_BRAKEPEDALST                         (0x008300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_BRKFRICTOTATWHLSTORQ                 (0x008400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_CSTST                                (0x008500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_EBPACTIVEST                          (0x008600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_FWAWARN                              (0x008700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_SCMACTIVEST                          (0x008800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__IBC_SYSTEMST                             (0x008900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__ICAN_133_RESERVE_0                       (0x008A00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__ICAN_133_RESERVE_1                       (0x008B00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__ICAN_133_RESERVE_2                       (0x008C00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__ICAN_133_RESERVE_3                       (0x008D00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__ICAN_133_RESERVE_4                       (0x008E00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_133__ROLLINGCOUNTER133                        (0x008F00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__CHECKSUM143                              (0x009000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_APABRAKSYSLONGICTLST                 (0x009100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_APABRAKEMODEST                       (0x009200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_APAGEARREQACTIVEST                   (0x009300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_LSMCTRLFAULTST                       (0x009400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_PDCUCTRLREQ                          (0x009500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_TARGETGEARREQ                        (0x009600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_VLCINTERTARGETAX                     (0x009700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__IBC_WHEEL_TRQ_APATREQ                    (0x009800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__ICAN_143_RESERVE_0                       (0x009900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__ICAN_143_RESERVE_1                       (0x009A00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__ICAN_143_RESERVE_2                       (0x009B00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_143__ROLLINGCOUNTER143                        (0x009C00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__CHECKSUM172                              (0x009D00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGEFLWSS                       (0x009E00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGEFLWSSVALID                  (0x009F00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGEFRWSS                       (0x00A000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGEFRWSSVALID                  (0x00A100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGERLWSS                       (0x00A200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGERLWSSVALID                  (0x00A300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGERRWSS                       (0x00A400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_SUMOFEDGERRWSSVALID                  (0x00A500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONFLST                   (0x00A600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONFLVALID                (0x00A700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONFRST                   (0x00A800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONFRVALID                (0x00A900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONRLST                   (0x00AA00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONRLVALID                (0x00AB00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONRRST                   (0x00AC00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__IBC_WHEELDIRECTIONRRVALID                (0x00AD00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__ICAN_172_RESERVE_0                       (0x00AE00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_172__ROLLINGCOUNTER172                        (0x00AF00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__CHECKSUM0A2                               (0x00B000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_ABSACTIVEST                           (0x00B100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_ABSFAULTSTST                          (0x00B200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_AVHAVAILABLEST                        (0x00B300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_AVHST                                 (0x00B400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_BRAKELIGHTREQ                         (0x00B500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_CDPACTIVEST                           (0x00B600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_CDPAVAILABLEST                        (0x00B700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_DTCACTIVEST                           (0x00B800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_EBDACTIVEST                           (0x00B900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_EBDFAULTST                            (0x00BA00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_ESCFAULTST                            (0x00BB00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_ESCLAMPDISP                           (0x00BC00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_ESCOFFST                              (0x00BD00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_HAZACTIVEST                           (0x00BE00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_HBAACTIVEST                           (0x00BF00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_HDCAVAILABLEST                        (0x00C000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_HDCST                                 (0x00C100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_HHCACTIVEST                           (0x00C200|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_HHCAVAILABLEST                        (0x00C300|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_MODEST                                (0x00C400|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_TCSACTIVEST                           (0x00C500|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VDCACTIVEST                           (0x00C600|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VEHICLEDRIVINGDIRECTION               (0x00C700|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VEHICLEHOLDST                         (0x00C800|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VEHICLESPEED                          (0x00C900|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VEHICLESPEEDVALID                     (0x00CA00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VEHICLESTANDSTILLST                   (0x00CB00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__IBC_VEHICLESTANDSTILLVALID                (0x00CC00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__ICAN_0A2_RESERVE_0                        (0x00CD00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__ICAN_0A2_RESERVE_1                        (0x00CE00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__ICAN_0A2_RESERVE_2                        (0x00CF00|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__ICAN_0A2_RESERVE_3                        (0x00D000|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__ICAN_0A2_RESERVE_4                        (0x00D100|(UID_CLASS_CAN<<4))
#define CANSIG_IBC_A2__ROLLINGCOUNTER0A2                         (0x00D200|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_182__HAD_CAMERA_TRQMOTORMAXLIMIT              (0x00D300|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_182__HAD_CAMERA_TRQMOTORMINLIMIT              (0x00D400|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__CHECKSUM183                              (0x00D500|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_ESAREQ                        (0x00D600|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_ESAST                         (0x00D700|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_ESATORQUEREQ                  (0x00D800|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_LKAACTIVATION                 (0x00D900|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_LKAANGLEREQ                   (0x00DA00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_LKAST                         (0x00DB00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__HAD_CAMERA_LKATORQFACTREQ                (0x00DC00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_183__ROLLINGCOUNTE183                         (0x00DD00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCACCELMODEST                (0x00DE00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCEPBREQ                     (0x00DF00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCINTERNALST                 (0x00E000|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCOBJFLAGST                  (0x00E100|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCOBJECTID                   (0x00E200|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCOBJECTTIME                 (0x00E300|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ACCTORQUEREQ                  (0x00E400|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_AEBBRAKEST                    (0x00E500|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_AEBST                         (0x00E600|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_AEBDBREM                      (0x00E700|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_AWBALERTREQ                   (0x00E800|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_BRAKEREQ                      (0x00E900|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_CHECKSUM1F3                   (0x00EA00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_DECELCTRLREQ                  (0x00EB00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_DECELSETREQ                   (0x00EC00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_DECELTTYPEST                  (0x00ED00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_DRIVEOFFREQ                   (0x00EE00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_FCWST                         (0x00EF00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_HBAREQ                        (0x00F000|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_IBCHOLDREQ                    (0x00F100|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_LKADBREM                      (0x00F200|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_POWERWHLTQACTREQ              (0x00F300|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_PREFILLREQ                    (0x00F400|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F3__HAD_CAMERA_ROLLINGCOUNT1F3               (0x00F500|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F5__HAD_CAMERA_ACCLATERALDST                 (0x00F600|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F5__HAD_CAMERA_ACCLONGITUDIALDST             (0x00F700|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F5__HAD_CAMERA_ACCST                         (0x00F800|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F5__HAD_CAMERA_FDMST                         (0x00F900|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F5__HAD_CAMERA_HMITARGETDISP                 (0x00FA00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_1F5__HAD_CAMERA_TARGETPOSST                   (0x00FB00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_ACCELX                               (0x00FC00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_ACCELY                               (0x00FD00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_ACCELZ                               (0x00FE00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_GYROX                                (0x00FF00|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_GYROY                                (0x010000|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_GYROZ                                (0x010100|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_IMUCHECKSUM                          (0x010200|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_IMUROLL                              (0x010300|(UID_CLASS_CAN<<4))
#define CANSIG_IVI_3A7__IVI_IMUST                                (0x010400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_EOT_FC__MCC_FC                                (0x010500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_EOT_FL__MCC_FL                                (0x010600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_EOT_FR__MCC_FR                                (0x010700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_EOT_RL__MCC_RL                                (0x010800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_EOT_RR__MCC_RR                                (0x010900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__CALIBRATIONMODEFLAG_FC             (0x010A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__DURATION_TME_POST_PROCESS_FC       (0x010B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__NUMOBJECTS_FC                      (0x010C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__OBJ_CYCLENUMBER_FC                 (0x010D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__OBJ_TIMESTAMP_FC                   (0x010E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__VALID_FC                           (0x010F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FC__VERSION_FC                         (0x011000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__CALIBRATIONMODEFLAG_FL             (0x011100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__DURATION_TME_POST_PROCESS_FL       (0x011200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__NUMOBJECTS_FL                      (0x011300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__OBJ_CYCLENUMBER_FL                 (0x011400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__OBJ_TIMESTAMP_FL                   (0x011500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__VALID_FL                           (0x011600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FL__VERSION_FL                         (0x011700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__CALIBRATIONMODEFLAG_FR             (0x011800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__DURATION_TME_POST_PROCESS_FR       (0x011900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__NUMOBJECTS_FR                      (0x011A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__OBJ_CYCLENUMBER_FR                 (0x011B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__OBJ_TIMESTAMP_FR                   (0x011C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__VALID_FR                           (0x011D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_FR__VERSION_FR                         (0x011E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__CALIBRATIONMODEFLAG_RL             (0x011F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__DURATION_TME_POST_PROCESS_RL       (0x012000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__NUMOBJECTS_RL                      (0x012100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__OBJ_CYCLENUMBER_RL                 (0x012200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__OBJ_TIMESTAMP_RL                   (0x012300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__VALID_RL                           (0x012400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RL__VERSION_RL                         (0x012500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__CALIBRATIONMODEFLAG_RR             (0x012600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__DURATION_TME_POST_PROCESS_RR       (0x012700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__NUMOBJECTS_RR                      (0x012800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__OBJ_CYCLENUMBER_RR                 (0x012900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__OBJ_TIMESTAMP_RR                   (0x012A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__VALID_RR                           (0x012B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_HEADER_RR__VERSION_RR                         (0x012C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_HALF_FCENGTH_YY_FC            (0x012D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_HALF_WIDTH_YY_FC              (0x012E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_ORIENTATION_YY_FC             (0x012F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_YAWRATE_YY_FC                 (0x013000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_A_YY_FC                       (0x013100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_V_YY_FC                       (0x013200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_X_YY_FC                       (0x013300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_XY_YY_FC                      (0x013400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__COV_Y_YY_FC                       (0x013500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__EXISTENCEPROBABILITY_YY_FC        (0x013600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__FRAME_ID_YY_FC                    (0x013700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__LENGTH_YY_FC                      (0x013800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__OBJECT_ID_YY_FC                   (0x013900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__ORIENTATION_RAD_YY_FC             (0x013A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__TRACKAGE_YY_FC                    (0x013B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__WIDTH_YY_FC                       (0x013C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__YAWRATE_RAD_S_YY_FC               (0x013D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__A_M_S_S_YY_FC                     (0x013E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__V_M_S_YY_FC                       (0x013F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__X_M_YY_FC                         (0x014000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FC__Y_M_YY_FC                         (0x014100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_HALF_LENGTH_YY_FL             (0x014200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_HALF_WIDTH_YY_FL              (0x014300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_ORIENTATION_YY_FL             (0x014400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_YAWRATE_YY_FL                 (0x014500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_A_YY_FL                       (0x014600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_V_YY_FL                       (0x014700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_X_YY_FL                       (0x014800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_XY_YY_FL                      (0x014900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__COV_Y_YY_FL                       (0x014A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__EXISTENCEPROBABILITY_YY_FL        (0x014B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__FRAME_ID_YY_FL                    (0x014C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__LENGTH_YY_FL                      (0x014D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__OBJECT_ID_YY_FL                   (0x014E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__ORIENTATION_RAD_YY_FL             (0x014F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__TRACKAGE_YY_FL                    (0x015000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__WIDTH_YY_FL                       (0x015100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__YAWRATE_RAD_S_YY_FL               (0x015200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__A_M_S_S_YY_FL                     (0x015300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__V_M_S_YY_FL                       (0x015400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__X_M_YY_FL                         (0x015500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FL__Y_M_YY_FL                         (0x015600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_HALF_LENGTH_YY_FR             (0x015700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_HALF_WIDTH_YY_FR              (0x015800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_ORIENTATION_YY_FR             (0x015900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_YAWRATE_YY_FR                 (0x015A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_A_YY_FR                       (0x015B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_V_YY_FR                       (0x015C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_X_YY_FR                       (0x015D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_XY_YY_FR                      (0x015E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__COV_Y_YY_FR                       (0x015F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__EXISTENCEPROBABILITY_YY_FR        (0x016000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__FRAME_ID_YY_FR                    (0x016100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__LENGTH_YY_FR                      (0x016200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__OBJECT_ID_YY_FR                   (0x016300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__ORIENTATION_RAD_YY_FR             (0x016400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__TRACKAGE_YY_FR                    (0x016500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__WIDTH_YY_FR                       (0x016600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__YAWRATE_RAD_S_YY_FR               (0x016700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__A_M_S_S_YY_FR                     (0x016800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__V_M_S_YY_FR                       (0x016900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__X_M_YY_FR                         (0x016A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_FR__Y_M_YY_FR                         (0x016B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_HALF_LENGTH_YY_RL             (0x016C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_HALF_WIDTH_YY_RL              (0x016D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_ORIENTATION_YY_RL             (0x016E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_YAWRATE_YY_RL                 (0x016F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_A_YY_RL                       (0x017000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_V_YY_RL                       (0x017100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_X_YY_RL                       (0x017200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_XY_YY_RL                      (0x017300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__COV_Y_YY_RL                       (0x017400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__EXISTENCEPROBABILITY_YY_RL        (0x017500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__FRAME_ID_YY_RL                    (0x017600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__LENGTH_YY_RL                      (0x017700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__OBJECT_ID_YY_RL                   (0x017800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__ORIENTATION_RAD_YY_RL             (0x017900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__TRACKAGE_YY_RL                    (0x017A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__WIDTH_YY_RL                       (0x017B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__YAWRATE_RAD_S_YY_RL               (0x017C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__A_M_S_S_YY_RL                     (0x017D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__V_M_S_YY_RL                       (0x017E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__X_M_YY_RL                         (0x017F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RL__Y_M_YY_RL                         (0x018000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_HALF_LENGTH_YY_RR             (0x018100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_HALF_WIDTH_YY_RR              (0x018200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_ORIENTATION_YY_RR             (0x018300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_YAWRATE_YY_RR                 (0x018400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_A_YY_RR                       (0x018500|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_V_YY_RR                       (0x018600|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_X_YY_RR                       (0x018700|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_XY_YY_RR                      (0x018800|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__COV_Y_YY_RR                       (0x018900|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__EXISTENCEPROBABILITY_YY_RR        (0x018A00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__FRAME_ID_YY_RR                    (0x018B00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__LENGTH_YY_RR                      (0x018C00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__OBJECT_ID_YY_RR                   (0x018D00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__ORIENTATION_RAD_YY_RR             (0x018E00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__TRACKAGE_YY_RR                    (0x018F00|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__WIDTH_YY_RR                       (0x019000|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__YAWRATE_RAD_S_YY_RR               (0x019100|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__A_M_S_S_YY_RR                     (0x019200|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__V_M_S_YY_RR                       (0x019300|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__X_M_YY_RR                         (0x019400|(UID_CLASS_CAN<<4))
#define CANSIG_OBJ_LIST_YY_RR__Y_M_YY_RR                         (0x019500|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__CHECKSUM1C8                             (0x019600|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__ICAN_1C8_RESERVE_0                      (0x019700|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__ICAN_1C8_RESERVE_1                      (0x019800|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__PDCU_ACCELPEDALST                       (0x019900|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__PDCU_ACCELPEDALVALID                    (0x019A00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__PDCU_ACTUALGEAR                         (0x019B00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__PDCU_ACTUALGEARVALID                    (0x019C00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__PDCU_TARGETGEAR                         (0x019D00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__PDCU_TARGETGEARVALID                    (0x019E00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_1C8__ROLLINGCOUNTER1C8                       (0x019F00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_364__PDCU_CHRGCNCTST                         (0x01A000|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_364__PDCU_CHRGST                             (0x01A100|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_37E__PDCU_VIRTACCRPEDPOSST                   (0x01A200|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_37E__PDCU_VIRTACCRPEDPOSNVALID               (0x01A300|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__CHECKSUM0FC                              (0x01A400|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__ICAN_0FC_RESERVE_0                       (0x01A500|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__ICAN_0FC_RESERVE_1                       (0x01A600|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__ICAN_0FC_RESERVE_2                       (0x01A700|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__ICAN_0FC_RESERVE_3                       (0x01A800|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__ICAN_0FC_RESERVE_4                       (0x01A900|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_ACCPEDALVIRTLVALID                  (0x01AA00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_ACCELPEDALVIRTUALST                 (0x01AB00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_FAWHLTQACT                          (0x01AC00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_FAWHLTQMAX                          (0x01AD00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_FAWHLTQREGENMAX                     (0x01AE00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_FAWHLTQREGENMAXVALID                (0x01AF00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_FRONTMOTORRPM                       (0x01B000|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_MOTREGENTRQINDTOTAL                 (0x01B100|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_MOTREGENTRQINDTOTALVLD              (0x01B200|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_OVERRIDEST                          (0x01B300|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_RAWHLTQACT                          (0x01B400|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_RAWHLTQMAX                          (0x01B500|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_RAWHLTQREGENMAX                     (0x01B600|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_RAWHLTQREGENMAXVALID                (0x01B700|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_FRONTMOTORRPMVALID                  (0x01B800|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_REARMOTRPM                          (0x01B900|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__PDCU_REARMOTRPMVALID                     (0x01BA00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FC__ROLLINGCOUNTER0FC                        (0x01BB00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__CHECKSUM0FE                              (0x01BC00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__ICAN_0FE_RESERVE_0                       (0x01BD00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__ICAN_0FE_RESERVE_1                       (0x01BE00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__ICAN_0FE_RESERVE_2                       (0x01BF00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__ICAN_0FE_RESERVE_3                       (0x01C000|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__PDCU_BMS_SOCCALCULATE                    (0x01C100|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__PDCU_BRAKEPEDALST                        (0x01C200|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__PDCU_DRIVEMODEST                         (0x01C300|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__PDCU_DRIVEREADYST                        (0x01C400|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__PDCU_EPWTST                              (0x01C500|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__PDCU_SHIFTLVLPOSNST                      (0x01C600|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FE__ROLLINGCOUNTER0FE                        (0x01C700|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__CHECKSUM0FF                              (0x01C800|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__EMS_POWERTRAINCONTROLST                  (0x01C900|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__ICAN_0FF_RESERVE_0                       (0x01CA00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__ICAN_0FF_RESERVE_1                       (0x01CB00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__ICAN_0FF_RESERVE_2                       (0x01CC00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_ACCFUNIHIBITIONREQ                  (0x01CD00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_ACCRESPONSEST                       (0x01CE00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_ACCCONTROLAVAILABLEST               (0x01CF00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_AXLETORQUEST                        (0x01D000|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_DRIVEASISTTACCST                    (0x01D100|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_DRIVEASISTT_APA                     (0x01D200|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_MCUF_INVERTERWORKST                 (0x01D300|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_MCUR_INVERTERWORKST                 (0x01D400|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_MAXVEHICLEWHLTQ                     (0x01D500|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_REALIZEDPOWERTRAINWHLTQ             (0x01D600|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_SHIFTAVAIBLEST                      (0x01D700|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_SHIFTQUITREASON                     (0x01D800|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_TARGETDRIVERTORQ                    (0x01D900|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_TOTALCOASTREGNTQTARGET              (0x01DA00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_TOTALCOASTREGNTQTRGVALD             (0x01DB00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__PDCU_WHETHERACCREQREALIZEDST             (0x01DC00|(UID_CLASS_CAN<<4))
#define CANSIG_PDCU_FF__ROLLINGCOUNTER0FF                        (0x01DD00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_LATERALACCELARATIONST              (0x01DE00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_LATERALACCELAREVALID               (0x01DF00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_LONGITDACCLERVALID                 (0x01E000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_LONGITUDACCELERATIONST             (0x01E100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_SENSORCALST                        (0x01E200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_YAWRATEST                          (0x01E300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ACU_YAWRATEVALIDITYST                  (0x01E400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__CHECKSUM119                            (0x01E500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ICAN_119_RESERVE_0                     (0x01E600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_119__ROLLINGCOUNTER119                      (0x01E700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_AUTOSWST                          (0x01E800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_AUTOFRONTWIPER                    (0x01E900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_BACKDOOROPENSWFBST                (0x01EA00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_BACKDOORUNLOCKREQ                 (0x01EB00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_BACKFOGLAMPST                     (0x01EC00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_ENGHOODUNLOCKWARN                 (0x01ED00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_FRONTFOGLAMPST                    (0x01EE00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_FRONTWIPERWORKST                  (0x01EF00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_HAZARDLAMPST                      (0x01F000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_HAZARDSWRST                       (0x01F100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_HIGHBEAMST                        (0x01F200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_LFDOORSWST                        (0x01F300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_LRDOORSWST                        (0x01F400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_LEFTTURNSWST                      (0x01F500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_LETFLIGTHFAULTST                  (0x01F600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_LETFLIGTHST                       (0x01F700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_LOWBEAMST                         (0x01F800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_POSLAMPST                         (0x01F900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_RFDOORSWST                        (0x01FA00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_RRDOORSWST                        (0x01FB00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_RIGHTTURNSWRST                    (0x01FC00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_RIGHTLIGTHFAULTST                 (0x01FD00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_RIGHTLIGTHST                      (0x01FE00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_23A__VIUL_TRUNKST                           (0x01FF00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__CHECKSUM30A                            (0x020000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__ICAN_30A_RESERVE_0                     (0x020100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__ICAN_30A_RESERVE_1                     (0x020200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__ICAN_30A_RESERVE_2                     (0x020300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__OTA_ESTIMATEDUPGRADETIME               (0x020400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__OTA_MODEST                             (0x020500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30A__ROLLINGCOUNTER30A                      (0x020600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30C__ACU_CRASHOUTST                         (0x020700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_30C__ACU_DRIVERBELTSWSIGST                  (0x020800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_320__IC_TOTALODMETER                        (0x020900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_322__VIUR_AC_AMBTEMPST                      (0x020A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_33C__VIUL_BRAKELAMPST                       (0x020B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_33C__VIUL_CHMSLST                           (0x020C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_33C__VIUL_DOORLOCKST                        (0x020D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_33C__VIUL_FRONTWIPERSWST                    (0x020E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_33C__VIUL_IGNITIONST                        (0x020F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_33D__VIUL_IHBCFUNEN                         (0x021000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_341__VIUL_BATTVOLTST                        (0x021100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_346__VIUL_PEPS_ENGINESTARTMODE              (0x021200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_346__VIUL_PEPS_RKECOMMAND2                  (0x021300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_365__IVI_WASHCARMODEREQ                     (0x021400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_367__IVI_HAZARDLAMPCTRLREQ                  (0x021500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_369__IC_VEHSPD                              (0x021600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_3F0__DKM_BLEAPAMODESELECTREQ                (0x021700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_3F0__DKM_BLEAPASWREQ                        (0x021800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_3F0__DKM_BLEAPPAPAMOVEREQ                   (0x021900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_3F0__DKM_BLEAPPRPAPARKREQ                   (0x021A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_3F0__DKM_BLECONNECTST                       (0x021B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_40__VIUL_PEPS_DOORLOCKREQ                   (0x021C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_40__VIUL_PEPS_RKECOMMAND                    (0x021D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_40__VIUL_PEPS_WECLOMEUNLOCKREQ              (0x021E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4AF__CWB_DIMMINGSWREQ                       (0x021F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4AF__CWB_FRONTWIPERSWREQ                    (0x022000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4AF__CWB_PREWASHINGSWREQ                    (0x022100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4AF__CWB_REARWIPERSWREQ                     (0x022200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4AF__CWB_TURNSWREQ                          (0x022300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_APAFUNMODULEENINDICATE             (0x022400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_APAMODESELECTREQ                   (0x022500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_APASWREQ                           (0x022600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVM2D3DMODEREQ                     (0x022700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMACTIVATIONREQ                   (0x022800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMBODYCOLORREQ                    (0x022900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMCALIBRATESTARTREQ               (0x022A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMHARDTOUCHSWREQ                  (0x022B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMLINESETSWREQ                    (0x022C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMPATHMODEREQ                     (0x022D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMPERSPECTIVECHASSISSWREQ         (0x022E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMSTEERWHLACTIVEREQ               (0x022F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMULTRASONICRADARACTIVEREQ        (0x023000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_AVMULTRASONICRADARDISPREQ          (0x023100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_NAVIDISTANCEST                     (0x023200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_NAVIINTERCONNECTST                 (0x023300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_NAVITURNREQ                        (0x023400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_PUPOSEKEYREQ                       (0x023500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_REQUESTAVMREQ                      (0x023600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_SCREENST                           (0x023700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_SIDEVIEWCTRLREQ                    (0x023800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_TRFUNMODULEENINDICATE              (0x023900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_TRSWRECOVERSWREQ                   (0x023A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_TRSWREQ                            (0x023B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_TOUCHBUTTONPRESSREQ                (0x023C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_TOUCHXCOORDINATE                   (0x023D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C0__IVI_TOUCHYCOORDINATE                   (0x023E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_ACCFOLLOWSTPTIMESETREQ             (0x023F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_ADASSPEEDLIMIT                     (0x024000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_AEBSWREQ                           (0x024100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_AUTOPILOTSWREQ                     (0x024200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_AVOIDANCESWREQ                     (0x024300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_BSDAUTOCALIACTIVATIONREQ           (0x024400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_BSDLCWACTIVATIONREQ                (0x024500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_DOWACTIVATIONREQ                   (0x024600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_DRIVEMODEREMBSWREQ                 (0x024700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_ELKSWREQ                           (0x024800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_ESAACTIVATIONSWREQ                 (0x024900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_FCWSENSITIVITYSETREQ               (0x024A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_FCWSWREQ                           (0x024B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_FDMSWREQ                           (0x024C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_FRONTCROSSSWREQ                    (0x024D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_FRONTCROSSMODEREQ                  (0x024E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_ISAMODEREQ                         (0x024F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_ISASPEEDDEVSETREQ                  (0x025000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_JAALERTSWREQ                       (0x025100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_LKAMODEREQ                         (0x025200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_LKAMAINSWREQ                       (0x025300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_LSSSWREQ                           (0x025400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_P2PLCCMODEREQ                      (0x025500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_P2PREMIDEMODEREQ                   (0x025600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_P2PSWREQ                           (0x025700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_RCWACTIVATIONREQ                   (0x025800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_REARCROSSMODEREQ                   (0x025900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_REARCROSSSWREQ                     (0x025A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_SASWREQ                            (0x025B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_TLCMODESELECTIONREQ                (0x025C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4C1__IVI_TSRSWREQ                           (0x025D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMEDATE                          (0x025E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMEHOUR                          (0x025F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMEMINUTE                        (0x026000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMEMONTH                         (0x026100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMESECOND                        (0x026200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMEVALID                         (0x026300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_4DA__TBOX_TIMEYEAR                          (0x026400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__CHECKSUM710                            (0x026500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__HIL_LATERALACCELST                     (0x026600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__HIL_LONGITUDEACCELST                   (0x026700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__HIL_PITCHRATEST                        (0x026800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__HIL_ROLLRATEST                         (0x026900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__HIL_VERTICALACCELST                    (0x026A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__HIL_YAWRATEST                          (0x026B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_710__ROLLINGCOUNTER710                      (0x026C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_C_83__EMS_ENGSPD                              (0x026D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_304__POT_BACKDOORANTIPLAYWARN               (0x026E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_304__POT_BACKDOORCLOSEFAILWARN              (0x026F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_304__POT_BACKDOORHEIGHTSETST                (0x027000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_304__POT_BACKDOOROPENFAILWARN               (0x027100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_304__POT_BACKDOOROPENTURNIDICATOR           (0x027200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_304__POT_BACKDOORPOSST                      (0x027300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_AIRBAGLAMPST                       (0x027400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_CRASHOUTST                         (0x027500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_DRIVERBELTSWSIGST                  (0x027600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_PASSENGERBELTST                    (0x027700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_PASSENGERSEATST                    (0x027800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_RLBELTST                           (0x027900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_RRBELTST                           (0x027A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ACU_REARMIDDLEBELTST                   (0x027B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__CHECKSUM30C                            (0x027C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_30C__ROLLINGCOUNTER30C                      (0x027D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_ALBRIGHENESSST                    (0x027E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_ALCOLORSETFBST                    (0x027F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_ALFUNST                           (0x028000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_ARDOORFUNST                       (0x028100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_ANTDISENST                        (0x028200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_AUTOMIRRORFUNST                   (0x028300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_AUTOUNLOCKFUNST                   (0x028400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_AUTOWIPERENFBST                   (0x028500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_BLSSIGNALWARN                     (0x028600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_COURTESYLIGHTSSWST                (0x028700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_FORBIDVEHSTARTENST                (0x028800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_GEARREVERSEREARWIPFUNST           (0x028900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_IHBCFUNEN                         (0x028A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_IHBCFUNST                         (0x028B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_IHBCSTAUSIND                      (0x028C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_LOCKCMDWINFUNST                   (0x028D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_LOCKGAPWINFUNST                   (0x028E00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_LOCKLAMPPATTERNFBST               (0x028F00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_PARKINGMODEST                     (0x029000|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_PARKINGSAYHIPATTERNFBST           (0x029100|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_PARKINGSAYHISETST                 (0x029200|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_RAINSNOWMODEST                    (0x029300|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_RAINFALLCLOSWINFUNST              (0x029400|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_ROMANTICMODEST                    (0x029500|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_SMOKEMODEST                       (0x029600|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_TWLAMPFUNST                       (0x029700|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_TWUNLOCKFUNST                     (0x029800|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_TAILGATEDOORLOCKFUNST             (0x029900|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_UNLOCKLAMPPATTERNFBST             (0x029A00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_UNLOCKWELCOMELAMPST               (0x029B00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_VOICEROOFCTRST                    (0x029C00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_WASHCARMODEARFBST                 (0x029D00|(UID_CLASS_CAN<<4))
#define CANSIG_SGW_I_33D__VIUL_WASHMODEREARMIRRORFBST            (0x029E00|(UID_CLASS_CAN<<4))
#define CAN_UNIQUE_ID_DUMMY                                      (0x029F00|(UID_CLASS_CAN<<4))
#define NUM_OF_CAN_UNIQUE_ID                                     672

enum canmsg_frametype {
    CANMSG_FRAMETYPE_DEFAULT         = 0,
    CANMSG_FRAMETYPE_STANDARDCAN     = 0,
    CANMSG_FRAMETYPE_EXTENDEDCAN     = 1,
    CANMSG_FRAMETYPE_RESERVED_2      = 2,
    CANMSG_FRAMETYPE_RESERVED_3      = 3,
    CANMSG_FRAMETYPE_RESERVED_4      = 4,
    CANMSG_FRAMETYPE_RESERVED_5      = 5,
    CANMSG_FRAMETYPE_RESERVED_6      = 6,
    CANMSG_FRAMETYPE_RESERVED_7      = 7,
    CANMSG_FRAMETYPE_RESERVED_8      = 8,
    CANMSG_FRAMETYPE_RESERVED_9      = 9,
    CANMSG_FRAMETYPE_RESERVED_10     = 10,
    CANMSG_FRAMETYPE_RESERVED_11     = 11,
    CANMSG_FRAMETYPE_RESERVED_12     = 12,
    CANMSG_FRAMETYPE_RESERVED_13     = 13,
    CANMSG_FRAMETYPE_STANDARDCAN_FD  = 14,
    CANMSG_FRAMETYPE_EXTENDEDCAN_FD  = 15,
};

enum canmsg_sendtype {
    CANMSG_SENDTYPE_CYCLIC           = 0,
    CANMSG_SENDTYPE_NOTUSED_1        = 1,
    CANMSG_SENDTYPE_NOTUSED_2        = 2,
    CANMSG_SENDTYPE_NOTUSED_3        = 3,
    CANMSG_SENDTYPE_NOTUSED_4        = 4,
    CANMSG_SENDTYPE_NOTUSED_5        = 5,
    CANMSG_SENDTYPE_NOTUSED_6        = 6,
    CANMSG_SENDTYPE_IFACTIVE         = 7,
    CANMSG_SENDTYPE_NOMSGSENDTYPE    = 8,
    CANMSG_SENDTYPE_NOTUSED_9        = 9,
    CANMSG_SENDTYPE_VECTOR_LEERSTRING = 10,
};

enum cansig_sendtype {
    CANSIG_SENDTYPE_CYCLIC                   = 0,
    CANSIG_SENDTYPE_ONWRITE                  = 1,
    CANSIG_SENDTYPE_ONWRITEWITHREPETITION    = 2,
    CANSIG_SENDTYPE_ONCHANGE                 = 3,
    CANSIG_SENDTYPE_ONCHANGEWITHREPETITION   = 4,
    CANSIG_SENDTYPE_IFACTIVE                 = 5,
    CANSIG_SENDTYPE_IFACTIVEWITHREPETITION   = 6,
    CANSIG_SENDTYPE_NOSIGSENDTYPE            = 7,
    CANSIG_SENDTYPE_NOTUSED_8                = 8,
    CANSIG_SENDTYPE_NOTUSED_9                = 9,
    CANSIG_SENDTYPE_NOTUSED_10               = 10,
    CANSIG_SENDTYPE_NOTUSED_11               = 11,
    CANSIG_SENDTYPE_NOTUSED_12               = 12,
};

//=====================================================================================//


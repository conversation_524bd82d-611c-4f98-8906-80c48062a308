#include "parser_typedef.h"

bool PARSER_AccordantEndianFrameChangeCheck(uint8_t *bytes1, uint8_t *bytes2, int Length)
{
    for(int i=0; i<Length; i++)
    {
        if (bytes1[i] != bytes2[i])
        {
            return true;
        }
    }
    return false;
}

bool PARSER_ReversedEndianFrameChangeCheck(uint8_t *bytes1, uint8_t *bytes2, int Length)
{
    for(int i=0; i<Length; i++)
    {
        if (bytes1[i] != bytes2[Length-i-1])
        {
            return true;
        }
    }
    return false;
}

void PARSER_AccordantEndianFrameFill(uint8_t *dst, uint8_t *src, int Length)
{
    for(int i=0; i<Length; i++)
    {
        dst[i] = src[i];
    }
}

void PARSER_ReversedEndianFrameFill(uint8_t *dst, uint8_t *src, int Length)
{
    for(int i=0; i<Length; i++)
    {
        dst[i] = src[Length-i-1];
    }
}

void PARSER_FrameProcess(struct veh_message *p_message_list[], uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes, uint32_t msg_num)
{
    int IndexBegin = 0;
    int IndexEnd = msg_num - 1;
    int IndexCurrent;
    while(1)
    {
        if ((IndexEnd - IndexBegin) ==0)
        {
            return;
        }
        else if ((IndexEnd - IndexBegin) == 1)
        {
            IndexCurrent = IndexBegin;
            break;
        }
        IndexCurrent = IndexBegin + (IndexEnd - IndexBegin)/2;
        if (frame_id < p_message_list[IndexCurrent]->msg_id)
        {
            IndexEnd = IndexCurrent;
        }
        else if (frame_id > p_message_list[IndexCurrent]->msg_id)
        {
            IndexBegin = IndexCurrent+1;
        }
        else
        {
            break;
        }
    }
    if (frame_id == p_message_list[IndexCurrent]->msg_id)
    {
        p_message_list[IndexCurrent]->ops.wrlock();
        p_message_list[IndexCurrent]->ops.fill(frame_bytes, frame_dlc);
        p_message_list[IndexCurrent]->ops.unlock();
    }
}

void PARSER_CANMSG_CounterIncrease(uint8_t *cnter)
{
    uint8_t tmpU8 = *cnter + 1;
    *cnter = tmpU8%16;
}

uint8_t PARSER_CANMSG_ChecksumCalculate(uint8_t *data, uint8_t len)
{
    uint8_t checksum = 0;

    for (uint8_t cnt=0; cnt<len; cnt++)
    {
        checksum += data[cnt];
    }
    return (~checksum);
}

bool PARSER_CANMSG_ChecksumVerify(uint8_t *data, uint8_t len, uint8_t checksum)
{
    uint8_t sum = 0;

    for (uint8_t cnt=0; cnt<len; cnt++)
    {
        sum += data[cnt];
    }
    sum = ~sum;
    return (sum == checksum);
}

int PARSER_CANMSG_RdLock(pthread_rwlock_t *rwlock)
{
    return pthread_rwlock_rdlock(rwlock);
}

int PARSER_CANMSG_WrLock(pthread_rwlock_t *rwlock)
{
    return pthread_rwlock_wrlock(rwlock);
}

int PARSER_CANMSG_Unlock(pthread_rwlock_t *rwlock)
{
    return pthread_rwlock_unlock(rwlock);
}

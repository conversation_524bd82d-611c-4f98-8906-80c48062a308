#ifndef __PARSER_TYPEDEF_H__
#define __PARSER_TYPEDEF_H__

#include <stdbool.h>
#include <stdint.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <pthread.h>
//=============================================================//
#ifndef MAX
#define  MAX(a, b)                                  (((a) > (b)) ? (a) : (b))
#endif

#ifndef MIN
#define  MIN(a, b)                                  (((a) < (b)) ? (a) : (b))
#endif
#define ALIGNMENT_UP(x,size)	(((x)+(size)-1)/(size)*(size))
#define ALIGNMENT_DOWN(x,size)	((x)/(size)*(size))
//ALIGNMENT_UP(start_address, sizeof(uint32_t));
//ALIGNMENT_UP(start_address, sizeof(uint64_t));
//--------------------------------------------------------//
enum uid_class {
    UID_CLASS_CAN = 0,
    UID_CLASS_LIN = 1,
    UID_CLASS_META= 2,
    //-----//
    NUM_OF_UID_CLASS,
};
///////
enum reset_flg {
    RESET_FLG_CONTEXT   = 1<<0,
    RESET_FLG_FRAME     = 1<<1,
};
///////
#define MSG_CHANGE          0
#define MSG_TIMEOUT         1
#define MSG_CHKERR          2
#define MSG_CNTERR          3
#define SIG_CHANGE          4
#define SIG_ONWRITE         5
#define META_EVT            6
//--------------------------------------------------------//
enum msg_direction {
    eMsgDirection_Tx,
    eMsgDirection_Rx,
    eMsgDirection_RTx,
};

typedef enum signal_value_type
{
    eSigValType_int64_t,
    eSigValType_uint64_t,
    eSigValType_int32_t,
    eSigValType_uint32_t,
    eSigValType_float,
} SignalValueType_t;

typedef enum signal_status
{
    eSigStatus_Ok,
    eSigStatus_Error,
    eSigStatus_Unknown,
} SignalStatus_t;

typedef enum signal_change_type
{
    eSigChangeType_Tx = 0,
    eSigChangeType_Rx = 1,
} SignalChangeType_t;

typedef union veh_signal_value {
    uint8_t  buffer[8];
    int64_t  val_int64_t;
    uint64_t val_uint64_t;
    int32_t  val_int32_t;
    uint32_t val_uint32_t;
    float    val_float;
    uint8_t* val_data;
} VehSignalValue_t;

typedef struct veh_signal_agent {
    uint32_t msg_id:16;
    uint32_t matrix_start_bit:16;
    union veh_signal_value Value;
} Veh_Signal_Agent_t;

typedef struct veh_message_context
{
    uint32_t is_rx_first:1;
} VehMessageContext;

typedef struct veh_message {
    uint32_t msg_id;
    uint32_t unique_id;
    uint32_t bus_id:8;          //canbus_id or linbus_id
    uint32_t transmitter:8;     //can_ecu_id or lin_ecu_id
    uint32_t msg_dlc:8;
    uint32_t msg_dlc_min:8;
    /////
    uint32_t buffer_size:16;    //缓冲区大小
    uint32_t num_of_buffer:16;  //缓冲区个数(多路复用时会有多个)
    uint8_t *buffer_start;      //缓冲区起始地址
    struct {
        void (*fill)(uint8_t *values, uint32_t length);
        void (*reset)(enum reset_flg flags);
        void (*rdlock)(void);
        void (*wrlock)(void);
        void (*unlock)(void);
    } ops;
    /////
    union {
        struct {
            uint32_t timeout_ms:16;
            uint32_t timeout_ms_count:16;
            /////
            uint32_t CycleTime:16;
            uint32_t CycleTimeFast:16;
            uint32_t DelayTime:16;
            uint32_t StartDelayTime:16;
            uint32_t NrOfRepetition:16;
            uint32_t SendType:8;        //enum canmsg_sendtype
            uint32_t FrameType:8;       //enum canmsg_frametype
            /////
            uint32_t direction:4;
            uint32_t out_range_flag:1;
        } can;
        struct {
            uint32_t timeout_cycle:8;
            /////
            uint32_t direction:4;
            uint32_t out_range_flag:1;
        } lin;
    } PrivInfo;
    /////
    void *signal_array_addr;
    void *msg_sta;//add by daniel 2020-10-16
    pthread_rwlock_t rwlock;
} Veh_Message_t;

typedef struct veh_signal_triggerinfo {
    struct veh_message *owner_msg;
    uint32_t start_bit:16;
    uint32_t num_of_bits:16;
    uint64_t raw_value;
    uint64_t raw_value_old;
    union {
        struct {
            uint32_t mul_index:16;          //多路复用组索引值(CacheEnable时会使用)
            uint32_t mul_value:16;          //多路复用组值(CacheDisable时会使用)
            uint32_t is_mul_selector:1;     //是否为多路复用选择器
            uint32_t is_mul_signal:1;       //是否为多路复用信号
            uint32_t send_type:8;           //enum cansig_sendtype
        } can;
    } PrivInfo;
} Veh_Signal_TransInfo_t;
//--------------------------------------------------------//
typedef void (*fpSignalSetFunction_t)(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
typedef enum signal_status (*fpSignalGetFunction_t)(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
typedef uint64_t (*fpSignalCalcFunction_t)(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
typedef bool (*fpSignalGetIsOutOfRangeFunction_t)();
//--------------------------------------------------------//
typedef struct veh_signal {
    uint32_t unique_id;
    // uint32_t value_type:8;  //enum signal_value_type
    uint32_t change_type:8; //enum signal_change_type
    uint32_t status:8;      //enum signal_status
    uint32_t lincan_gating_maxnum:8;
    uint32_t canlin_gating_maxnum:8;
    uint32_t cancan_gating_maxnum:8;
    uint32_t is_timeout:1;
    uint32_t orig_start_bit;
    uint32_t sig_length;
    char* sig_name;
    char* sig_type;
    char* msg_name;
    void *p_veh_message;
    //------------------------------------------//
    struct {
        fpSignalSetFunction_t   *fpSetterList;
    } LinCanGating;
    struct {
        fpSignalSetFunction_t   *fpSetterList;
    } CanLinGating;
    struct {
        fpSignalSetFunction_t   *fpSetterList;
    } CanCanGating;
    struct {
        fpSignalSetFunction_t   fpSetter;
    } SetValue;
    struct {
        fpSignalGetFunction_t   fpGetter;
    } GetValue;
    fpSignalCalcFunction_t calc;
    //------------------------//
    void *custom_action;
    //------------------------//
    struct veh_signal *next_signal;
    fpSignalGetIsOutOfRangeFunction_t GetIsValueOutOfRange;
} Veh_Signal_t;

//--------------------------------------//
struct lin_schedule_item {
    uint32_t delay_ms;
    struct veh_message *msg;
    struct lin_schedule_item *next;
    uint32_t timeout_cycle_count:8;
    uint32_t transmit_done:1;
};
struct lin_schedule_table {
    uint32_t delay_ms_count;
    struct lin_schedule_item *cur_item;
    struct lin_schedule_item *start_item;
};
//--------------------------------------//
union parser_can_id_filter {
    uint32_t Value;
    struct {
        uint32_t bIdentifier:11;
        uint32_t bMask:11;
        uint32_t bBusId:8;
        uint32_t :2;
    } Fields;
};
//------------------------------------------------//
typedef void (*fpParserLockHandler_t)(void);
typedef void (*fpParserUnlockHandler_t)(void);
//--------------//
typedef void (*fpMsgCounterCalcHandler_t)(uint32_t *counter);
typedef void (*fpMsgChecksumCalcHandler_t)(uint8_t *buffer, int length);
typedef bool (*fpMsgChecksumVerityHandler_t)(struct veh_message *message, uint8_t *buffer, int length);
//--------------//
typedef void (*fpMsgChangeHandler_t)(struct veh_message *message, int type);
typedef void (*fpMsgChecksumErrorHandler_t)(struct veh_message *message, int type);
typedef void (*fpMsgCounterErrorHandler_t)(struct veh_message *message, int type);
typedef void (*fpMsgTimeoutHandler_t)(struct veh_message *message, int type);
//--------------//
typedef bool (*fpMsgDlcHandler_t)(struct veh_message *message, uint32_t *length);
typedef bool (*fpMsgOutRangeHandler_t)(struct veh_message *message);
//--------------//
typedef void (*fpSigChangeHandler_t)(struct veh_signal *signal, int type);
typedef void (*fpSigOnWriteHandler_t)(struct veh_signal *signal, int type);
//--------------//
typedef bool (*fpSignalChangedHook_t)(uint64_t old_value, uint64_t new_value);
typedef void (*fpSignalTriggerCallBack_t)(struct veh_signal_triggerinfo *trigger_info);
typedef void (*fpMessageTransferCallBack_t)(struct veh_message *message);
//====================================================//
#endif

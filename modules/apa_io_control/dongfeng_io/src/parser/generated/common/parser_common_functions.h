#pragma once
#include "parser_typedef.h"

extern bool PARSER_AccordantEndianFrameChangeCheck(uint8_t *bytes1, uint8_t *bytes2, int Length);
extern bool PARSER_ReversedEndianFrameChangeCheck(uint8_t *bytes1, uint8_t *bytes2, int Length);
extern void PARSER_AccordantEndianFrameFill(uint8_t *dst, uint8_t *src, int Length);
extern void PARSER_ReversedEndianFrameFill(uint8_t *dst, uint8_t *src, int Length);
void PARSER_FrameProcess(struct veh_message *p_message_list[], uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes, uint32_t msg_num);
extern void PARSER_CANMSG_CounterIncrease(uint8_t *cnter);
extern uint8_t PARSER_CANMSG_ChecksumCalculate(uint8_t *data, uint8_t len);
extern bool PARSER_CANMSG_ChecksumVerify(uint8_t *data, uint8_t len, uint8_t checksum);

extern int PARSER_CANMSG_RdLock(pthread_rwlock_t *rwlock);
extern int PARSER_CANMSG_WrLock(pthread_rwlock_t *rwlock);
extern int PARSER_CANMSG_Unlock(pthread_rwlock_t *rwlock);


#include "parser_typedef.h"
#pragma once

#if 0
//--------------//
extern void CAN_SetCanParserLockHandler(fpParserLockHandler_t func);
extern void CAN_SetCanParserUnlockHandler(fpParserUnlockHandler_t func);
extern void CAN_SetMsgCounterCalcHandler(fpMsgCounterCalcHandler_t func);
extern void CAN_SetMsgChecksumCalcHandler(fpMsgChecksumCalcHandler_t func);
extern void CAN_SetMsgChecksumVerityHandler(fpMsgChecksumVerityHandler_t func);
extern void CAN_SetMsgChangeHandler(fpMsgChangeHandler_t func);
extern void CAN_SetMsgChecksumErrorHandler(fpMsgChecksumErrorHandler_t func);
extern void CAN_SetMsgCounterErrorHandler(fpMsgCounterErrorHandler_t func);
extern void CAN_SetMsgTimeoutHandler(fpMsgTimeoutHandler_t func);
extern void CAN_SetMsgDlcHandler(fpMsgDlcHandler_t func);
extern void CAN_SetMsgOutRangeHandler(fpMsgOutRangeHandler_t func);
extern void CAN_SetSigChangeHandler(fpSigChangeHandler_t func);
extern void CAN_SetSigOnWriteHandler(fpSigOnWriteHandler_t func);
extern void CAN_SetSignalChangedHook(fpSignalChangedHook_t func);
extern void CAN_SetSignalSetCallBack(fpSignalTriggerCallBack_t func);
extern void CAN_SetSignalGetCallBack(fpSignalTriggerCallBack_t func);
//=====================================================================================//
extern struct veh_message *CAN_RxMessageList[27];

extern struct veh_message *CAN_TxMessageList[1];

extern struct veh_signal* CAN_ALL_Signal_Array[];
#endif
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Beam_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Beam_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Beam_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Beam_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Beam_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_0_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_1_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_2_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_3_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_4_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Beam_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Beam_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Beam_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Beam_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Beam_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_50_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_51_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_52_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_53_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_54_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Beam_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Beam_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Beam_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Beam_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Beam_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_55_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_56_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_57_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_58_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_59_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Beam_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Beam_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Beam_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Beam_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Beam_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_60_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_61_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_62_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_63_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_64_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Beam_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Beam_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Beam_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Beam_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Beam_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_65_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_66_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_67_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_68_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_69_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Beam_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Beam_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Beam_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Beam_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Beam_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_70_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_71_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_72_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_73_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_74_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Beam_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Beam_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Beam_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Beam_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Beam_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_75_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_76_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_77_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_78_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_79_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Beam_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Beam_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Beam_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Beam_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Beam_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_80_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_81_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_82_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_83_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_84_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Beam_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Beam_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Beam_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Beam_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Beam_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_85_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_86_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_87_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_88_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_89_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Beam_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Beam_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Beam_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Beam_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Beam_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_90_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_91_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_92_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_93_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_94_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Beam_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Beam_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Beam_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Beam_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Beam_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_95_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_96_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_97_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_98_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_99_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Beam_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Beam_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Beam_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Beam_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Beam_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_5_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_6_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_7_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_8_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_9_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Beam_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Beam_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Beam_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Beam_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Beam_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_100_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_101_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_102_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_103_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_104_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Beam_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Beam_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Beam_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Beam_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Beam_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_105_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_106_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_107_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_108_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_109_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Beam_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Beam_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Beam_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Beam_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Beam_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_110_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_111_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_112_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_113_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_114_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Beam_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Beam_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Beam_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Beam_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Beam_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_115_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_116_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_117_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_118_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_119_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Beam_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Beam_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Beam_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Beam_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Beam_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_120_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_121_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_122_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_123_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_124_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Beam_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Beam_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Beam_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Beam_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Beam_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_10_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_11_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_12_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_13_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_14_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Beam_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Beam_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Beam_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Beam_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Beam_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_15_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_16_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_17_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_18_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_19_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Beam_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Beam_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Beam_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Beam_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Beam_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_20_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_21_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_22_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_23_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_24_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Beam_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Beam_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Beam_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Beam_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Beam_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_25_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_26_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_27_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_28_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_29_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Beam_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Beam_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Beam_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Beam_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Beam_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_30_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_31_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_32_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_33_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_34_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Beam_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Beam_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Beam_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Beam_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Beam_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_35_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_36_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_37_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_38_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_39_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Beam_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Beam_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Beam_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Beam_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Beam_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_40_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_41_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_42_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_43_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_44_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Beam_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Beam_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Beam_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Beam_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Beam_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_45_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_46_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_47_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_48_RL_g;
extern struct veh_signal CANSIG_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_49_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__BW100KHz1_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__BW100KHz2_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__BW100KHz3_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__CycleNumber_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__DetectionListVersion_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__FC1MHz1_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__FC1MHz2_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__FC1MHz3_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__HostAccelLatEst_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__HostAccelLongEst_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__HostVelEst_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__HostYawEst_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__InterferenceExceeded_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__TimeStamp_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__UnambiguousVelMeas1_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__UnambiguousVelMeas2_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__UnambiguousVelMeas3_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__numCmplxValPerDetectionBeam1_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__numCmplxValPerDetectionBeam2_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__numCmplxValPerDetectionBeam3_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__numFreespaceDetections_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__numInfrastructureDetected_RL_g;
extern struct veh_signal CANSIG_MK_TARGET_DETECTION_HEADER_RL__numNonInfraDetected_RL_g;

extern void CAN_SetRawMessage_MK_TARGET_DETECTION_HEADER_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_0_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_1_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_2_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_3_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_4_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_5_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_6_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_7_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_8_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_9_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_10_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_11_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_12_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_13_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_14_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_15_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_16_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_17_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_18_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_19_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_20_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_21_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_22_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_23_RL(uint8_t *values, uint32_t length);
extern void CAN_SetRawMessage_MK_NON_INF_DETECTION_24_RL(uint8_t *values, uint32_t length);

extern void CAN_ResetMessage_MK_TARGET_DETECTION_HEADER_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_0_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_1_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_2_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_3_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_4_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_5_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_6_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_7_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_8_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_9_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_10_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_11_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_12_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_13_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_14_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_15_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_16_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_17_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_18_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_19_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_20_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_21_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_22_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_23_RL(enum reset_flg flags);
extern void CAN_ResetMessage_MK_NON_INF_DETECTION_24_RL(enum reset_flg flags);

#if 0
extern void CAN_MessageElapseTime(int bus_id, int time_ms, int restart);
#endif


extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_0_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_10_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_11_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_12_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_13_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_14_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_15_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_16_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_17_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_18_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_19_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_1_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_20_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_21_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_22_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_23_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_24_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_2_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_3_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_4_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_5_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_6_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_7_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_8_RL(void);
extern void CAN_Message_RDLock_MK_NON_INF_DETECTION_9_RL(void);
extern void CAN_Message_RDLock_MK_TARGET_DETECTION_HEADER_RL(void);

extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_0_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_10_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_11_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_12_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_13_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_14_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_15_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_16_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_17_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_18_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_19_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_1_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_20_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_21_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_22_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_23_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_24_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_2_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_3_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_4_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_5_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_6_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_7_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_8_RL(void);
extern void CAN_Message_WRLock_MK_NON_INF_DETECTION_9_RL(void);
extern void CAN_Message_WRLock_MK_TARGET_DETECTION_HEADER_RL(void);

extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_0_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_10_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_11_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_12_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_13_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_14_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_15_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_16_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_17_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_18_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_19_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_1_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_20_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_21_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_22_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_23_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_24_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_2_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_3_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_4_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_5_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_6_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_7_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_8_RL(void);
extern void CAN_Message_Unlock_MK_NON_INF_DETECTION_9_RL(void);
extern void CAN_Message_Unlock_MK_TARGET_DETECTION_HEADER_RL(void);



extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Azimuth_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Beam_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Beam_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Beam_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Beam_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Beam_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGDoppler_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__CoGRange_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__NoInfrastructure_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__PowerDB_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__Reserve1_1bit_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__SNRdB_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__StdAzimuth_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__ValidXBeam_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WDoppler10DB_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_0_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_0_RL__WRange10DB_NI_4_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Azimuth_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Beam_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Beam_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Beam_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Beam_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Beam_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGDoppler_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__CoGRange_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__NoInfrastructure_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__PowerDB_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__Reserve1_1bit_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__SNRdB_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__StdAzimuth_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__ValidXBeam_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WDoppler10DB_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_50_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_51_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_52_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_53_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_10_RL__WRange10DB_NI_54_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Azimuth_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Beam_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Beam_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Beam_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Beam_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Beam_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGDoppler_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__CoGRange_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__NoInfrastructure_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__PowerDB_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__Reserve1_1bit_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__SNRdB_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__StdAzimuth_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__ValidXBeam_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WDoppler10DB_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_55_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_56_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_57_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_58_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_11_RL__WRange10DB_NI_59_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Azimuth_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Beam_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Beam_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Beam_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Beam_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Beam_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGDoppler_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__CoGRange_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__NoInfrastructure_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__PowerDB_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__Reserve1_1bit_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__SNRdB_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__StdAzimuth_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__ValidXBeam_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WDoppler10DB_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_60_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_61_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_62_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_63_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_12_RL__WRange10DB_NI_64_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Azimuth_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Beam_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Beam_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Beam_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Beam_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Beam_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGDoppler_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__CoGRange_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__NoInfrastructure_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__PowerDB_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__Reserve1_1bit_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__SNRdB_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__StdAzimuth_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__ValidXBeam_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WDoppler10DB_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_65_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_66_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_67_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_68_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_13_RL__WRange10DB_NI_69_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Azimuth_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Beam_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Beam_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Beam_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Beam_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Beam_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGDoppler_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__CoGRange_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__NoInfrastructure_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__PowerDB_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__Reserve1_1bit_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__SNRdB_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__StdAzimuth_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__ValidXBeam_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WDoppler10DB_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_70_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_71_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_72_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_73_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_14_RL__WRange10DB_NI_74_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Azimuth_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Beam_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Beam_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Beam_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Beam_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Beam_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGDoppler_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__CoGRange_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__NoInfrastructure_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__PowerDB_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__Reserve1_1bit_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__SNRdB_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__StdAzimuth_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__ValidXBeam_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WDoppler10DB_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_75_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_76_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_77_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_78_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_15_RL__WRange10DB_NI_79_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Azimuth_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Beam_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Beam_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Beam_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Beam_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Beam_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGDoppler_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__CoGRange_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__NoInfrastructure_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__PowerDB_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__Reserve1_1bit_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__SNRdB_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__StdAzimuth_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__ValidXBeam_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WDoppler10DB_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_80_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_81_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_82_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_83_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_16_RL__WRange10DB_NI_84_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Azimuth_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Beam_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Beam_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Beam_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Beam_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Beam_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGDoppler_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__CoGRange_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__NoInfrastructure_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__PowerDB_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__Reserve1_1bit_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__SNRdB_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__StdAzimuth_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__ValidXBeam_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WDoppler10DB_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_85_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_86_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_87_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_88_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_17_RL__WRange10DB_NI_89_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Azimuth_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Beam_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Beam_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Beam_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Beam_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Beam_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGDoppler_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__CoGRange_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__NoInfrastructure_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__PowerDB_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__Reserve1_1bit_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__SNRdB_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__StdAzimuth_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__ValidXBeam_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WDoppler10DB_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_90_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_91_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_92_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_93_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_18_RL__WRange10DB_NI_94_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Azimuth_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Beam_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Beam_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Beam_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Beam_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Beam_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGDoppler_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__CoGRange_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__NoInfrastructure_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__PowerDB_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__Reserve1_1bit_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__SNRdB_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__StdAzimuth_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__ValidXBeam_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WDoppler10DB_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_95_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_96_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_97_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_98_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_19_RL__WRange10DB_NI_99_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Azimuth_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Beam_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Beam_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Beam_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Beam_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Beam_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGDoppler_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__CoGRange_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__NoInfrastructure_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__PowerDB_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__Reserve1_1bit_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__SNRdB_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__StdAzimuth_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__ValidXBeam_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WDoppler10DB_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_5_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_6_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_7_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_8_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_1_RL__WRange10DB_NI_9_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Azimuth_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Beam_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Beam_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Beam_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Beam_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Beam_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGDoppler_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__CoGRange_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__NoInfrastructure_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__PowerDB_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__Reserve1_1bit_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__SNRdB_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__StdAzimuth_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__ValidXBeam_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WDoppler10DB_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_100_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_101_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_102_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_103_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_20_RL__WRange10DB_NI_104_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Azimuth_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Beam_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Beam_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Beam_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Beam_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Beam_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGDoppler_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__CoGRange_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__NoInfrastructure_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__PowerDB_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__Reserve1_1bit_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__SNRdB_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__StdAzimuth_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__ValidXBeam_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WDoppler10DB_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_105_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_106_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_107_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_108_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_21_RL__WRange10DB_NI_109_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Azimuth_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Beam_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Beam_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Beam_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Beam_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Beam_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGDoppler_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__CoGRange_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__NoInfrastructure_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__PowerDB_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__Reserve1_1bit_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__SNRdB_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__StdAzimuth_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__ValidXBeam_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WDoppler10DB_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_110_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_111_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_112_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_113_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_22_RL__WRange10DB_NI_114_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Azimuth_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Beam_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Beam_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Beam_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Beam_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Beam_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGDoppler_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__CoGRange_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__NoInfrastructure_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__PowerDB_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__Reserve1_1bit_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__SNRdB_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__StdAzimuth_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__ValidXBeam_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WDoppler10DB_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_115_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_116_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_117_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_118_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_23_RL__WRange10DB_NI_119_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Azimuth_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Beam_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Beam_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Beam_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Beam_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Beam_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGDoppler_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__CoGRange_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__NoInfrastructure_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__PowerDB_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__Reserve1_1bit_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__SNRdB_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__StdAzimuth_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__ValidXBeam_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WDoppler10DB_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_120_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_121_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_122_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_123_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_24_RL__WRange10DB_NI_124_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Azimuth_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Beam_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Beam_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Beam_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Beam_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Beam_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGDoppler_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__CoGRange_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__NoInfrastructure_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__PowerDB_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__Reserve1_1bit_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__SNRdB_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__StdAzimuth_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__ValidXBeam_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WDoppler10DB_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_10_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_11_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_12_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_13_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_2_RL__WRange10DB_NI_14_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Azimuth_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Beam_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Beam_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Beam_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Beam_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Beam_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGDoppler_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__CoGRange_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__NoInfrastructure_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__PowerDB_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__Reserve1_1bit_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__SNRdB_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__StdAzimuth_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__ValidXBeam_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WDoppler10DB_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_15_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_16_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_17_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_18_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_3_RL__WRange10DB_NI_19_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Azimuth_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Beam_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Beam_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Beam_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Beam_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Beam_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGDoppler_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__CoGRange_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__NoInfrastructure_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__PowerDB_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__Reserve1_1bit_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__SNRdB_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__StdAzimuth_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__ValidXBeam_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WDoppler10DB_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_20_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_21_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_22_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_23_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_4_RL__WRange10DB_NI_24_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Azimuth_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Beam_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Beam_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Beam_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Beam_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Beam_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGDoppler_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__CoGRange_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__NoInfrastructure_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__PowerDB_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__Reserve1_1bit_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__SNRdB_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__StdAzimuth_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__ValidXBeam_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WDoppler10DB_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_25_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_26_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_27_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_28_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_5_RL__WRange10DB_NI_29_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Azimuth_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Beam_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Beam_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Beam_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Beam_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Beam_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGDoppler_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__CoGRange_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__NoInfrastructure_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__PowerDB_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__Reserve1_1bit_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__SNRdB_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__StdAzimuth_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__ValidXBeam_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WDoppler10DB_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_30_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_31_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_32_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_33_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_6_RL__WRange10DB_NI_34_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Azimuth_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Beam_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Beam_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Beam_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Beam_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Beam_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGDoppler_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__CoGRange_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__NoInfrastructure_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__PowerDB_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__Reserve1_1bit_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__SNRdB_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__StdAzimuth_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__ValidXBeam_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WDoppler10DB_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_35_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_36_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_37_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_38_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_7_RL__WRange10DB_NI_39_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Azimuth_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Beam_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Beam_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Beam_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Beam_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Beam_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGDoppler_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__CoGRange_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__NoInfrastructure_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__PowerDB_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__Reserve1_1bit_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__SNRdB_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__StdAzimuth_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__ValidXBeam_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WDoppler10DB_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_40_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_41_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_42_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_43_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_8_RL__WRange10DB_NI_44_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Azimuth_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Beam_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Beam_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Beam_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Beam_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Beam_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGDoppler_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__CoGRange_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__NoInfrastructure_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__PowerDB_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__Reserve1_1bit_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__SNRdB_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__StdAzimuth_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__ValidXBeam_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WDoppler10DB_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_45_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_46_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_47_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_48_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_NON_INF_DETECTION_9_RL__WRange10DB_NI_49_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__BW100KHz1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__BW100KHz2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__BW100KHz3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__CycleNumber_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__DetectionListVersion_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__FC1MHz1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__FC1MHz2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__FC1MHz3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__HostAccelLatEst_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__HostAccelLongEst_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__HostVelEst_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__HostYawEst_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__InterferenceExceeded_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__TimeStamp_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__UnambiguousVelMeas1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__UnambiguousVelMeas2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__UnambiguousVelMeas3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__numCmplxValPerDetectionBeam1_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__numCmplxValPerDetectionBeam2_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__numCmplxValPerDetectionBeam3_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__numFreespaceDetections_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__numInfrastructureDetected_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);
extern enum signal_status CAN_GetSignal_MK_TARGET_DETECTION_HEADER_RL__numNonInfraDetected_RL(union veh_signal_value *physical_value, union veh_signal_value *raw_value);

extern void MK_RL_CAN_PARSER_FrameHandler(uint32_t bus_id, uint32_t frame_id, uint8_t frame_dlc, uint8_t *frame_bytes);
#if 0
extern void CAN_PARSER_Init(void);
extern void CAN_PARSER_MSG_Init(struct veh_message *p_message_list[]);
#endif

//=====================================================================================//



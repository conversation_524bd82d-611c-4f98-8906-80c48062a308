#include "parser_typedef.h"
#pragma once
//=====================================================================================//
#define CANMSG_MK_NON_INF_DETECTION_0_RL_ID        (0x0102)
#define CANMSG_MK_NON_INF_DETECTION_0_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_0_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_0_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_4_RL : 1;
        uint64_t NoInfrastructure_NI_4_RL : 1;
        uint64_t Beam_NI_4_RL : 2;
        uint64_t Azimuth_NI_4_RL : 12;
        uint64_t StdAzimuth_NI_4_RL : 8;
        uint64_t SNRdB_NI_4_RL : 8;
        uint64_t PowerDB_NI_4_RL : 8;
        uint64_t WDoppler10DB_NI_4_RL : 7;
        uint64_t Reserve1_1bit_NI_4_RL : 1;
        uint64_t WRange10DB_NI_4_RL : 8;
        uint64_t CoGDoppler_NI_4_RL : 16;
        uint64_t CoGRange_NI_4_RL : 16;
        uint64_t ValidXBeam_NI_3_RL : 1;
        uint64_t NoInfrastructure_NI_3_RL : 1;
        uint64_t Beam_NI_3_RL : 2;
        uint64_t Azimuth_NI_3_RL : 12;
        uint64_t StdAzimuth_NI_3_RL : 8;
        uint64_t SNRdB_NI_3_RL : 8;
        uint64_t PowerDB_NI_3_RL : 8;
        uint64_t WDoppler10DB_NI_3_RL : 7;
        uint64_t Reserve1_1bit_NI_3_RL : 1;
        uint64_t WRange10DB_NI_3_RL : 8;
        uint64_t CoGDoppler_NI_3_RL : 16;
        uint64_t CoGRange_NI_3_RL : 16;
        uint64_t ValidXBeam_NI_2_RL : 1;
        uint64_t NoInfrastructure_NI_2_RL : 1;
        uint64_t Beam_NI_2_RL : 2;
        uint64_t Azimuth_NI_2_RL__S1 : 4;
        uint64_t Azimuth_NI_2_RL__S0 : 8;
        uint64_t StdAzimuth_NI_2_RL : 8;
        uint64_t SNRdB_NI_2_RL : 8;
        uint64_t PowerDB_NI_2_RL : 8;
        uint64_t WDoppler10DB_NI_2_RL : 7;
        uint64_t Reserve1_1bit_NI_2_RL : 1;
        uint64_t WRange10DB_NI_2_RL : 8;
        uint64_t CoGDoppler_NI_2_RL : 16;
        uint64_t CoGRange_NI_2_RL : 16;
        uint64_t ValidXBeam_NI_1_RL : 1;
        uint64_t NoInfrastructure_NI_1_RL : 1;
        uint64_t Beam_NI_1_RL : 2;
        uint64_t Azimuth_NI_1_RL : 12;
        uint64_t StdAzimuth_NI_1_RL : 8;
        uint64_t SNRdB_NI_1_RL : 8;
        uint64_t PowerDB_NI_1_RL : 8;
        uint64_t WDoppler10DB_NI_1_RL : 7;
        uint64_t Reserve1_1bit_NI_1_RL : 1;
        uint64_t WRange10DB_NI_1_RL : 8;
        uint64_t CoGDoppler_NI_1_RL : 16;
        uint64_t CoGRange_NI_1_RL : 16;
        uint64_t ValidXBeam_NI_0_RL : 1;
        uint64_t NoInfrastructure_NI_0_RL : 1;
        uint64_t Beam_NI_0_RL : 2;
        uint64_t Azimuth_NI_0_RL : 12;
        uint64_t StdAzimuth_NI_0_RL : 8;
        uint64_t SNRdB_NI_0_RL : 8;
        uint64_t PowerDB_NI_0_RL : 8;
        uint64_t WDoppler10DB_NI_0_RL : 7;
        uint64_t Reserve1_1bit_NI_0_RL : 1;
        uint64_t WRange10DB_NI_0_RL : 8;
        uint64_t CoGDoppler_NI_0_RL : 16;
        uint64_t CoGRange_NI_0_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_0_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_0_rl_data_t data;
};

union cansig_mk_non_inf_detection_0_rl__azimuth_ni_2_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_2_RL__S1 : 4;
        uint32_t Azimuth_NI_2_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_10_RL_ID       (0x010C)
#define CANMSG_MK_NON_INF_DETECTION_10_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_10_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_10_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_54_RL : 1;
        uint64_t NoInfrastructure_NI_54_RL : 1;
        uint64_t Beam_NI_54_RL : 2;
        uint64_t Azimuth_NI_54_RL : 12;
        uint64_t StdAzimuth_NI_54_RL : 8;
        uint64_t SNRdB_NI_54_RL : 8;
        uint64_t PowerDB_NI_54_RL : 8;
        uint64_t WDoppler10DB_NI_54_RL : 7;
        uint64_t Reserve1_1bit_NI_54_RL : 1;
        uint64_t WRange10DB_NI_54_RL : 8;
        uint64_t CoGDoppler_NI_54_RL : 16;
        uint64_t CoGRange_NI_54_RL : 16;
        uint64_t ValidXBeam_NI_53_RL : 1;
        uint64_t NoInfrastructure_NI_53_RL : 1;
        uint64_t Beam_NI_53_RL : 2;
        uint64_t Azimuth_NI_53_RL : 12;
        uint64_t StdAzimuth_NI_53_RL : 8;
        uint64_t SNRdB_NI_53_RL : 8;
        uint64_t PowerDB_NI_53_RL : 8;
        uint64_t WDoppler10DB_NI_53_RL : 7;
        uint64_t Reserve1_1bit_NI_53_RL : 1;
        uint64_t WRange10DB_NI_53_RL : 8;
        uint64_t CoGDoppler_NI_53_RL : 16;
        uint64_t CoGRange_NI_53_RL : 16;
        uint64_t ValidXBeam_NI_52_RL : 1;
        uint64_t NoInfrastructure_NI_52_RL : 1;
        uint64_t Beam_NI_52_RL : 2;
        uint64_t Azimuth_NI_52_RL__S1 : 4;
        uint64_t Azimuth_NI_52_RL__S0 : 8;
        uint64_t StdAzimuth_NI_52_RL : 8;
        uint64_t SNRdB_NI_52_RL : 8;
        uint64_t PowerDB_NI_52_RL : 8;
        uint64_t WDoppler10DB_NI_52_RL : 7;
        uint64_t Reserve1_1bit_NI_52_RL : 1;
        uint64_t WRange10DB_NI_52_RL : 8;
        uint64_t CoGDoppler_NI_52_RL : 16;
        uint64_t CoGRange_NI_52_RL : 16;
        uint64_t ValidXBeam_NI_51_RL : 1;
        uint64_t NoInfrastructure_NI_51_RL : 1;
        uint64_t Beam_NI_51_RL : 2;
        uint64_t Azimuth_NI_51_RL : 12;
        uint64_t StdAzimuth_NI_51_RL : 8;
        uint64_t SNRdB_NI_51_RL : 8;
        uint64_t PowerDB_NI_51_RL : 8;
        uint64_t WDoppler10DB_NI_51_RL : 7;
        uint64_t Reserve1_1bit_NI_51_RL : 1;
        uint64_t WRange10DB_NI_51_RL : 8;
        uint64_t CoGDoppler_NI_51_RL : 16;
        uint64_t CoGRange_NI_51_RL : 16;
        uint64_t ValidXBeam_NI_50_RL : 1;
        uint64_t NoInfrastructure_NI_50_RL : 1;
        uint64_t Beam_NI_50_RL : 2;
        uint64_t Azimuth_NI_50_RL : 12;
        uint64_t StdAzimuth_NI_50_RL : 8;
        uint64_t SNRdB_NI_50_RL : 8;
        uint64_t PowerDB_NI_50_RL : 8;
        uint64_t WDoppler10DB_NI_50_RL : 7;
        uint64_t Reserve1_1bit_NI_50_RL : 1;
        uint64_t WRange10DB_NI_50_RL : 8;
        uint64_t CoGDoppler_NI_50_RL : 16;
        uint64_t CoGRange_NI_50_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_10_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_10_rl_data_t data;
};

union cansig_mk_non_inf_detection_10_rl__azimuth_ni_52_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_52_RL__S1 : 4;
        uint32_t Azimuth_NI_52_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_11_RL_ID       (0x010D)
#define CANMSG_MK_NON_INF_DETECTION_11_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_11_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_11_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_59_RL : 1;
        uint64_t NoInfrastructure_NI_59_RL : 1;
        uint64_t Beam_NI_59_RL : 2;
        uint64_t Azimuth_NI_59_RL : 12;
        uint64_t StdAzimuth_NI_59_RL : 8;
        uint64_t SNRdB_NI_59_RL : 8;
        uint64_t PowerDB_NI_59_RL : 8;
        uint64_t WDoppler10DB_NI_59_RL : 7;
        uint64_t Reserve1_1bit_NI_59_RL : 1;
        uint64_t WRange10DB_NI_59_RL : 8;
        uint64_t CoGDoppler_NI_59_RL : 16;
        uint64_t CoGRange_NI_59_RL : 16;
        uint64_t ValidXBeam_NI_58_RL : 1;
        uint64_t NoInfrastructure_NI_58_RL : 1;
        uint64_t Beam_NI_58_RL : 2;
        uint64_t Azimuth_NI_58_RL : 12;
        uint64_t StdAzimuth_NI_58_RL : 8;
        uint64_t SNRdB_NI_58_RL : 8;
        uint64_t PowerDB_NI_58_RL : 8;
        uint64_t WDoppler10DB_NI_58_RL : 7;
        uint64_t Reserve1_1bit_NI_58_RL : 1;
        uint64_t WRange10DB_NI_58_RL : 8;
        uint64_t CoGDoppler_NI_58_RL : 16;
        uint64_t CoGRange_NI_58_RL : 16;
        uint64_t ValidXBeam_NI_57_RL : 1;
        uint64_t NoInfrastructure_NI_57_RL : 1;
        uint64_t Beam_NI_57_RL : 2;
        uint64_t Azimuth_NI_57_RL__S1 : 4;
        uint64_t Azimuth_NI_57_RL__S0 : 8;
        uint64_t StdAzimuth_NI_57_RL : 8;
        uint64_t SNRdB_NI_57_RL : 8;
        uint64_t PowerDB_NI_57_RL : 8;
        uint64_t WDoppler10DB_NI_57_RL : 7;
        uint64_t Reserve1_1bit_NI_57_RL : 1;
        uint64_t WRange10DB_NI_57_RL : 8;
        uint64_t CoGDoppler_NI_57_RL : 16;
        uint64_t CoGRange_NI_57_RL : 16;
        uint64_t ValidXBeam_NI_56_RL : 1;
        uint64_t NoInfrastructure_NI_56_RL : 1;
        uint64_t Beam_NI_56_RL : 2;
        uint64_t Azimuth_NI_56_RL : 12;
        uint64_t StdAzimuth_NI_56_RL : 8;
        uint64_t SNRdB_NI_56_RL : 8;
        uint64_t PowerDB_NI_56_RL : 8;
        uint64_t WDoppler10DB_NI_56_RL : 7;
        uint64_t Reserve1_1bit_NI_56_RL : 1;
        uint64_t WRange10DB_NI_56_RL : 8;
        uint64_t CoGDoppler_NI_56_RL : 16;
        uint64_t CoGRange_NI_56_RL : 16;
        uint64_t ValidXBeam_NI_55_RL : 1;
        uint64_t NoInfrastructure_NI_55_RL : 1;
        uint64_t Beam_NI_55_RL : 2;
        uint64_t Azimuth_NI_55_RL : 12;
        uint64_t StdAzimuth_NI_55_RL : 8;
        uint64_t SNRdB_NI_55_RL : 8;
        uint64_t PowerDB_NI_55_RL : 8;
        uint64_t WDoppler10DB_NI_55_RL : 7;
        uint64_t Reserve1_1bit_NI_55_RL : 1;
        uint64_t WRange10DB_NI_55_RL : 8;
        uint64_t CoGDoppler_NI_55_RL : 16;
        uint64_t CoGRange_NI_55_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_11_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_11_rl_data_t data;
};

union cansig_mk_non_inf_detection_11_rl__azimuth_ni_57_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_57_RL__S1 : 4;
        uint32_t Azimuth_NI_57_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_12_RL_ID       (0x010E)
#define CANMSG_MK_NON_INF_DETECTION_12_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_12_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_12_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_64_RL : 1;
        uint64_t NoInfrastructure_NI_64_RL : 1;
        uint64_t Beam_NI_64_RL : 2;
        uint64_t Azimuth_NI_64_RL : 12;
        uint64_t StdAzimuth_NI_64_RL : 8;
        uint64_t SNRdB_NI_64_RL : 8;
        uint64_t PowerDB_NI_64_RL : 8;
        uint64_t WDoppler10DB_NI_64_RL : 7;
        uint64_t Reserve1_1bit_NI_64_RL : 1;
        uint64_t WRange10DB_NI_64_RL : 8;
        uint64_t CoGDoppler_NI_64_RL : 16;
        uint64_t CoGRange_NI_64_RL : 16;
        uint64_t ValidXBeam_NI_63_RL : 1;
        uint64_t NoInfrastructure_NI_63_RL : 1;
        uint64_t Beam_NI_63_RL : 2;
        uint64_t Azimuth_NI_63_RL : 12;
        uint64_t StdAzimuth_NI_63_RL : 8;
        uint64_t SNRdB_NI_63_RL : 8;
        uint64_t PowerDB_NI_63_RL : 8;
        uint64_t WDoppler10DB_NI_63_RL : 7;
        uint64_t Reserve1_1bit_NI_63_RL : 1;
        uint64_t WRange10DB_NI_63_RL : 8;
        uint64_t CoGDoppler_NI_63_RL : 16;
        uint64_t CoGRange_NI_63_RL : 16;
        uint64_t ValidXBeam_NI_62_RL : 1;
        uint64_t NoInfrastructure_NI_62_RL : 1;
        uint64_t Beam_NI_62_RL : 2;
        uint64_t Azimuth_NI_62_RL__S1 : 4;
        uint64_t Azimuth_NI_62_RL__S0 : 8;
        uint64_t StdAzimuth_NI_62_RL : 8;
        uint64_t SNRdB_NI_62_RL : 8;
        uint64_t PowerDB_NI_62_RL : 8;
        uint64_t WDoppler10DB_NI_62_RL : 7;
        uint64_t Reserve1_1bit_NI_62_RL : 1;
        uint64_t WRange10DB_NI_62_RL : 8;
        uint64_t CoGDoppler_NI_62_RL : 16;
        uint64_t CoGRange_NI_62_RL : 16;
        uint64_t ValidXBeam_NI_61_RL : 1;
        uint64_t NoInfrastructure_NI_61_RL : 1;
        uint64_t Beam_NI_61_RL : 2;
        uint64_t Azimuth_NI_61_RL : 12;
        uint64_t StdAzimuth_NI_61_RL : 8;
        uint64_t SNRdB_NI_61_RL : 8;
        uint64_t PowerDB_NI_61_RL : 8;
        uint64_t WDoppler10DB_NI_61_RL : 7;
        uint64_t Reserve1_1bit_NI_61_RL : 1;
        uint64_t WRange10DB_NI_61_RL : 8;
        uint64_t CoGDoppler_NI_61_RL : 16;
        uint64_t CoGRange_NI_61_RL : 16;
        uint64_t ValidXBeam_NI_60_RL : 1;
        uint64_t NoInfrastructure_NI_60_RL : 1;
        uint64_t Beam_NI_60_RL : 2;
        uint64_t Azimuth_NI_60_RL : 12;
        uint64_t StdAzimuth_NI_60_RL : 8;
        uint64_t SNRdB_NI_60_RL : 8;
        uint64_t PowerDB_NI_60_RL : 8;
        uint64_t WDoppler10DB_NI_60_RL : 7;
        uint64_t Reserve1_1bit_NI_60_RL : 1;
        uint64_t WRange10DB_NI_60_RL : 8;
        uint64_t CoGDoppler_NI_60_RL : 16;
        uint64_t CoGRange_NI_60_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_12_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_12_rl_data_t data;
};

union cansig_mk_non_inf_detection_12_rl__azimuth_ni_62_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_62_RL__S1 : 4;
        uint32_t Azimuth_NI_62_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_13_RL_ID       (0x010F)
#define CANMSG_MK_NON_INF_DETECTION_13_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_13_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_13_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_69_RL : 1;
        uint64_t NoInfrastructure_NI_69_RL : 1;
        uint64_t Beam_NI_69_RL : 2;
        uint64_t Azimuth_NI_69_RL : 12;
        uint64_t StdAzimuth_NI_69_RL : 8;
        uint64_t SNRdB_NI_69_RL : 8;
        uint64_t PowerDB_NI_69_RL : 8;
        uint64_t WDoppler10DB_NI_69_RL : 7;
        uint64_t Reserve1_1bit_NI_69_RL : 1;
        uint64_t WRange10DB_NI_69_RL : 8;
        uint64_t CoGDoppler_NI_69_RL : 16;
        uint64_t CoGRange_NI_69_RL : 16;
        uint64_t ValidXBeam_NI_68_RL : 1;
        uint64_t NoInfrastructure_NI_68_RL : 1;
        uint64_t Beam_NI_68_RL : 2;
        uint64_t Azimuth_NI_68_RL : 12;
        uint64_t StdAzimuth_NI_68_RL : 8;
        uint64_t SNRdB_NI_68_RL : 8;
        uint64_t PowerDB_NI_68_RL : 8;
        uint64_t WDoppler10DB_NI_68_RL : 7;
        uint64_t Reserve1_1bit_NI_68_RL : 1;
        uint64_t WRange10DB_NI_68_RL : 8;
        uint64_t CoGDoppler_NI_68_RL : 16;
        uint64_t CoGRange_NI_68_RL : 16;
        uint64_t ValidXBeam_NI_67_RL : 1;
        uint64_t NoInfrastructure_NI_67_RL : 1;
        uint64_t Beam_NI_67_RL : 2;
        uint64_t Azimuth_NI_67_RL__S1 : 4;
        uint64_t Azimuth_NI_67_RL__S0 : 8;
        uint64_t StdAzimuth_NI_67_RL : 8;
        uint64_t SNRdB_NI_67_RL : 8;
        uint64_t PowerDB_NI_67_RL : 8;
        uint64_t WDoppler10DB_NI_67_RL : 7;
        uint64_t Reserve1_1bit_NI_67_RL : 1;
        uint64_t WRange10DB_NI_67_RL : 8;
        uint64_t CoGDoppler_NI_67_RL : 16;
        uint64_t CoGRange_NI_67_RL : 16;
        uint64_t ValidXBeam_NI_66_RL : 1;
        uint64_t NoInfrastructure_NI_66_RL : 1;
        uint64_t Beam_NI_66_RL : 2;
        uint64_t Azimuth_NI_66_RL : 12;
        uint64_t StdAzimuth_NI_66_RL : 8;
        uint64_t SNRdB_NI_66_RL : 8;
        uint64_t PowerDB_NI_66_RL : 8;
        uint64_t WDoppler10DB_NI_66_RL : 7;
        uint64_t Reserve1_1bit_NI_66_RL : 1;
        uint64_t WRange10DB_NI_66_RL : 8;
        uint64_t CoGDoppler_NI_66_RL : 16;
        uint64_t CoGRange_NI_66_RL : 16;
        uint64_t ValidXBeam_NI_65_RL : 1;
        uint64_t NoInfrastructure_NI_65_RL : 1;
        uint64_t Beam_NI_65_RL : 2;
        uint64_t Azimuth_NI_65_RL : 12;
        uint64_t StdAzimuth_NI_65_RL : 8;
        uint64_t SNRdB_NI_65_RL : 8;
        uint64_t PowerDB_NI_65_RL : 8;
        uint64_t WDoppler10DB_NI_65_RL : 7;
        uint64_t Reserve1_1bit_NI_65_RL : 1;
        uint64_t WRange10DB_NI_65_RL : 8;
        uint64_t CoGDoppler_NI_65_RL : 16;
        uint64_t CoGRange_NI_65_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_13_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_13_rl_data_t data;
};

union cansig_mk_non_inf_detection_13_rl__azimuth_ni_67_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_67_RL__S1 : 4;
        uint32_t Azimuth_NI_67_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_14_RL_ID       (0x0110)
#define CANMSG_MK_NON_INF_DETECTION_14_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_14_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_14_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_74_RL : 1;
        uint64_t NoInfrastructure_NI_74_RL : 1;
        uint64_t Beam_NI_74_RL : 2;
        uint64_t Azimuth_NI_74_RL : 12;
        uint64_t StdAzimuth_NI_74_RL : 8;
        uint64_t SNRdB_NI_74_RL : 8;
        uint64_t PowerDB_NI_74_RL : 8;
        uint64_t WDoppler10DB_NI_74_RL : 7;
        uint64_t Reserve1_1bit_NI_74_RL : 1;
        uint64_t WRange10DB_NI_74_RL : 8;
        uint64_t CoGDoppler_NI_74_RL : 16;
        uint64_t CoGRange_NI_74_RL : 16;
        uint64_t ValidXBeam_NI_73_RL : 1;
        uint64_t NoInfrastructure_NI_73_RL : 1;
        uint64_t Beam_NI_73_RL : 2;
        uint64_t Azimuth_NI_73_RL : 12;
        uint64_t StdAzimuth_NI_73_RL : 8;
        uint64_t SNRdB_NI_73_RL : 8;
        uint64_t PowerDB_NI_73_RL : 8;
        uint64_t WDoppler10DB_NI_73_RL : 7;
        uint64_t Reserve1_1bit_NI_73_RL : 1;
        uint64_t WRange10DB_NI_73_RL : 8;
        uint64_t CoGDoppler_NI_73_RL : 16;
        uint64_t CoGRange_NI_73_RL : 16;
        uint64_t ValidXBeam_NI_72_RL : 1;
        uint64_t NoInfrastructure_NI_72_RL : 1;
        uint64_t Beam_NI_72_RL : 2;
        uint64_t Azimuth_NI_72_RL__S1 : 4;
        uint64_t Azimuth_NI_72_RL__S0 : 8;
        uint64_t StdAzimuth_NI_72_RL : 8;
        uint64_t SNRdB_NI_72_RL : 8;
        uint64_t PowerDB_NI_72_RL : 8;
        uint64_t WDoppler10DB_NI_72_RL : 7;
        uint64_t Reserve1_1bit_NI_72_RL : 1;
        uint64_t WRange10DB_NI_72_RL : 8;
        uint64_t CoGDoppler_NI_72_RL : 16;
        uint64_t CoGRange_NI_72_RL : 16;
        uint64_t ValidXBeam_NI_71_RL : 1;
        uint64_t NoInfrastructure_NI_71_RL : 1;
        uint64_t Beam_NI_71_RL : 2;
        uint64_t Azimuth_NI_71_RL : 12;
        uint64_t StdAzimuth_NI_71_RL : 8;
        uint64_t SNRdB_NI_71_RL : 8;
        uint64_t PowerDB_NI_71_RL : 8;
        uint64_t WDoppler10DB_NI_71_RL : 7;
        uint64_t Reserve1_1bit_NI_71_RL : 1;
        uint64_t WRange10DB_NI_71_RL : 8;
        uint64_t CoGDoppler_NI_71_RL : 16;
        uint64_t CoGRange_NI_71_RL : 16;
        uint64_t ValidXBeam_NI_70_RL : 1;
        uint64_t NoInfrastructure_NI_70_RL : 1;
        uint64_t Beam_NI_70_RL : 2;
        uint64_t Azimuth_NI_70_RL : 12;
        uint64_t StdAzimuth_NI_70_RL : 8;
        uint64_t SNRdB_NI_70_RL : 8;
        uint64_t PowerDB_NI_70_RL : 8;
        uint64_t WDoppler10DB_NI_70_RL : 7;
        uint64_t Reserve1_1bit_NI_70_RL : 1;
        uint64_t WRange10DB_NI_70_RL : 8;
        uint64_t CoGDoppler_NI_70_RL : 16;
        uint64_t CoGRange_NI_70_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_14_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_14_rl_data_t data;
};

union cansig_mk_non_inf_detection_14_rl__azimuth_ni_72_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_72_RL__S1 : 4;
        uint32_t Azimuth_NI_72_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_15_RL_ID       (0x0111)
#define CANMSG_MK_NON_INF_DETECTION_15_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_15_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_15_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_79_RL : 1;
        uint64_t NoInfrastructure_NI_79_RL : 1;
        uint64_t Beam_NI_79_RL : 2;
        uint64_t Azimuth_NI_79_RL : 12;
        uint64_t StdAzimuth_NI_79_RL : 8;
        uint64_t SNRdB_NI_79_RL : 8;
        uint64_t PowerDB_NI_79_RL : 8;
        uint64_t WDoppler10DB_NI_79_RL : 7;
        uint64_t Reserve1_1bit_NI_79_RL : 1;
        uint64_t WRange10DB_NI_79_RL : 8;
        uint64_t CoGDoppler_NI_79_RL : 16;
        uint64_t CoGRange_NI_79_RL : 16;
        uint64_t ValidXBeam_NI_78_RL : 1;
        uint64_t NoInfrastructure_NI_78_RL : 1;
        uint64_t Beam_NI_78_RL : 2;
        uint64_t Azimuth_NI_78_RL : 12;
        uint64_t StdAzimuth_NI_78_RL : 8;
        uint64_t SNRdB_NI_78_RL : 8;
        uint64_t PowerDB_NI_78_RL : 8;
        uint64_t WDoppler10DB_NI_78_RL : 7;
        uint64_t Reserve1_1bit_NI_78_RL : 1;
        uint64_t WRange10DB_NI_78_RL : 8;
        uint64_t CoGDoppler_NI_78_RL : 16;
        uint64_t CoGRange_NI_78_RL : 16;
        uint64_t ValidXBeam_NI_77_RL : 1;
        uint64_t NoInfrastructure_NI_77_RL : 1;
        uint64_t Beam_NI_77_RL : 2;
        uint64_t Azimuth_NI_77_RL__S1 : 4;
        uint64_t Azimuth_NI_77_RL__S0 : 8;
        uint64_t StdAzimuth_NI_77_RL : 8;
        uint64_t SNRdB_NI_77_RL : 8;
        uint64_t PowerDB_NI_77_RL : 8;
        uint64_t WDoppler10DB_NI_77_RL : 7;
        uint64_t Reserve1_1bit_NI_77_RL : 1;
        uint64_t WRange10DB_NI_77_RL : 8;
        uint64_t CoGDoppler_NI_77_RL : 16;
        uint64_t CoGRange_NI_77_RL : 16;
        uint64_t ValidXBeam_NI_76_RL : 1;
        uint64_t NoInfrastructure_NI_76_RL : 1;
        uint64_t Beam_NI_76_RL : 2;
        uint64_t Azimuth_NI_76_RL : 12;
        uint64_t StdAzimuth_NI_76_RL : 8;
        uint64_t SNRdB_NI_76_RL : 8;
        uint64_t PowerDB_NI_76_RL : 8;
        uint64_t WDoppler10DB_NI_76_RL : 7;
        uint64_t Reserve1_1bit_NI_76_RL : 1;
        uint64_t WRange10DB_NI_76_RL : 8;
        uint64_t CoGDoppler_NI_76_RL : 16;
        uint64_t CoGRange_NI_76_RL : 16;
        uint64_t ValidXBeam_NI_75_RL : 1;
        uint64_t NoInfrastructure_NI_75_RL : 1;
        uint64_t Beam_NI_75_RL : 2;
        uint64_t Azimuth_NI_75_RL : 12;
        uint64_t StdAzimuth_NI_75_RL : 8;
        uint64_t SNRdB_NI_75_RL : 8;
        uint64_t PowerDB_NI_75_RL : 8;
        uint64_t WDoppler10DB_NI_75_RL : 7;
        uint64_t Reserve1_1bit_NI_75_RL : 1;
        uint64_t WRange10DB_NI_75_RL : 8;
        uint64_t CoGDoppler_NI_75_RL : 16;
        uint64_t CoGRange_NI_75_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_15_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_15_rl_data_t data;
};

union cansig_mk_non_inf_detection_15_rl__azimuth_ni_77_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_77_RL__S1 : 4;
        uint32_t Azimuth_NI_77_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_16_RL_ID       (0x0112)
#define CANMSG_MK_NON_INF_DETECTION_16_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_16_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_16_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_84_RL : 1;
        uint64_t NoInfrastructure_NI_84_RL : 1;
        uint64_t Beam_NI_84_RL : 2;
        uint64_t Azimuth_NI_84_RL : 12;
        uint64_t StdAzimuth_NI_84_RL : 8;
        uint64_t SNRdB_NI_84_RL : 8;
        uint64_t PowerDB_NI_84_RL : 8;
        uint64_t WDoppler10DB_NI_84_RL : 7;
        uint64_t Reserve1_1bit_NI_84_RL : 1;
        uint64_t WRange10DB_NI_84_RL : 8;
        uint64_t CoGDoppler_NI_84_RL : 16;
        uint64_t CoGRange_NI_84_RL : 16;
        uint64_t ValidXBeam_NI_83_RL : 1;
        uint64_t NoInfrastructure_NI_83_RL : 1;
        uint64_t Beam_NI_83_RL : 2;
        uint64_t Azimuth_NI_83_RL : 12;
        uint64_t StdAzimuth_NI_83_RL : 8;
        uint64_t SNRdB_NI_83_RL : 8;
        uint64_t PowerDB_NI_83_RL : 8;
        uint64_t WDoppler10DB_NI_83_RL : 7;
        uint64_t Reserve1_1bit_NI_83_RL : 1;
        uint64_t WRange10DB_NI_83_RL : 8;
        uint64_t CoGDoppler_NI_83_RL : 16;
        uint64_t CoGRange_NI_83_RL : 16;
        uint64_t ValidXBeam_NI_82_RL : 1;
        uint64_t NoInfrastructure_NI_82_RL : 1;
        uint64_t Beam_NI_82_RL : 2;
        uint64_t Azimuth_NI_82_RL__S1 : 4;
        uint64_t Azimuth_NI_82_RL__S0 : 8;
        uint64_t StdAzimuth_NI_82_RL : 8;
        uint64_t SNRdB_NI_82_RL : 8;
        uint64_t PowerDB_NI_82_RL : 8;
        uint64_t WDoppler10DB_NI_82_RL : 7;
        uint64_t Reserve1_1bit_NI_82_RL : 1;
        uint64_t WRange10DB_NI_82_RL : 8;
        uint64_t CoGDoppler_NI_82_RL : 16;
        uint64_t CoGRange_NI_82_RL : 16;
        uint64_t ValidXBeam_NI_81_RL : 1;
        uint64_t NoInfrastructure_NI_81_RL : 1;
        uint64_t Beam_NI_81_RL : 2;
        uint64_t Azimuth_NI_81_RL : 12;
        uint64_t StdAzimuth_NI_81_RL : 8;
        uint64_t SNRdB_NI_81_RL : 8;
        uint64_t PowerDB_NI_81_RL : 8;
        uint64_t WDoppler10DB_NI_81_RL : 7;
        uint64_t Reserve1_1bit_NI_81_RL : 1;
        uint64_t WRange10DB_NI_81_RL : 8;
        uint64_t CoGDoppler_NI_81_RL : 16;
        uint64_t CoGRange_NI_81_RL : 16;
        uint64_t ValidXBeam_NI_80_RL : 1;
        uint64_t NoInfrastructure_NI_80_RL : 1;
        uint64_t Beam_NI_80_RL : 2;
        uint64_t Azimuth_NI_80_RL : 12;
        uint64_t StdAzimuth_NI_80_RL : 8;
        uint64_t SNRdB_NI_80_RL : 8;
        uint64_t PowerDB_NI_80_RL : 8;
        uint64_t WDoppler10DB_NI_80_RL : 7;
        uint64_t Reserve1_1bit_NI_80_RL : 1;
        uint64_t WRange10DB_NI_80_RL : 8;
        uint64_t CoGDoppler_NI_80_RL : 16;
        uint64_t CoGRange_NI_80_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_16_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_16_rl_data_t data;
};

union cansig_mk_non_inf_detection_16_rl__azimuth_ni_82_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_82_RL__S1 : 4;
        uint32_t Azimuth_NI_82_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_17_RL_ID       (0x0113)
#define CANMSG_MK_NON_INF_DETECTION_17_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_17_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_17_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_89_RL : 1;
        uint64_t NoInfrastructure_NI_89_RL : 1;
        uint64_t Beam_NI_89_RL : 2;
        uint64_t Azimuth_NI_89_RL : 12;
        uint64_t StdAzimuth_NI_89_RL : 8;
        uint64_t SNRdB_NI_89_RL : 8;
        uint64_t PowerDB_NI_89_RL : 8;
        uint64_t WDoppler10DB_NI_89_RL : 7;
        uint64_t Reserve1_1bit_NI_89_RL : 1;
        uint64_t WRange10DB_NI_89_RL : 8;
        uint64_t CoGDoppler_NI_89_RL : 16;
        uint64_t CoGRange_NI_89_RL : 16;
        uint64_t ValidXBeam_NI_88_RL : 1;
        uint64_t NoInfrastructure_NI_88_RL : 1;
        uint64_t Beam_NI_88_RL : 2;
        uint64_t Azimuth_NI_88_RL : 12;
        uint64_t StdAzimuth_NI_88_RL : 8;
        uint64_t SNRdB_NI_88_RL : 8;
        uint64_t PowerDB_NI_88_RL : 8;
        uint64_t WDoppler10DB_NI_88_RL : 7;
        uint64_t Reserve1_1bit_NI_88_RL : 1;
        uint64_t WRange10DB_NI_88_RL : 8;
        uint64_t CoGDoppler_NI_88_RL : 16;
        uint64_t CoGRange_NI_88_RL : 16;
        uint64_t ValidXBeam_NI_87_RL : 1;
        uint64_t NoInfrastructure_NI_87_RL : 1;
        uint64_t Beam_NI_87_RL : 2;
        uint64_t Azimuth_NI_87_RL__S1 : 4;
        uint64_t Azimuth_NI_87_RL__S0 : 8;
        uint64_t StdAzimuth_NI_87_RL : 8;
        uint64_t SNRdB_NI_87_RL : 8;
        uint64_t PowerDB_NI_87_RL : 8;
        uint64_t WDoppler10DB_NI_87_RL : 7;
        uint64_t Reserve1_1bit_NI_87_RL : 1;
        uint64_t WRange10DB_NI_87_RL : 8;
        uint64_t CoGDoppler_NI_87_RL : 16;
        uint64_t CoGRange_NI_87_RL : 16;
        uint64_t ValidXBeam_NI_86_RL : 1;
        uint64_t NoInfrastructure_NI_86_RL : 1;
        uint64_t Beam_NI_86_RL : 2;
        uint64_t Azimuth_NI_86_RL : 12;
        uint64_t StdAzimuth_NI_86_RL : 8;
        uint64_t SNRdB_NI_86_RL : 8;
        uint64_t PowerDB_NI_86_RL : 8;
        uint64_t WDoppler10DB_NI_86_RL : 7;
        uint64_t Reserve1_1bit_NI_86_RL : 1;
        uint64_t WRange10DB_NI_86_RL : 8;
        uint64_t CoGDoppler_NI_86_RL : 16;
        uint64_t CoGRange_NI_86_RL : 16;
        uint64_t ValidXBeam_NI_85_RL : 1;
        uint64_t NoInfrastructure_NI_85_RL : 1;
        uint64_t Beam_NI_85_RL : 2;
        uint64_t Azimuth_NI_85_RL : 12;
        uint64_t StdAzimuth_NI_85_RL : 8;
        uint64_t SNRdB_NI_85_RL : 8;
        uint64_t PowerDB_NI_85_RL : 8;
        uint64_t WDoppler10DB_NI_85_RL : 7;
        uint64_t Reserve1_1bit_NI_85_RL : 1;
        uint64_t WRange10DB_NI_85_RL : 8;
        uint64_t CoGDoppler_NI_85_RL : 16;
        uint64_t CoGRange_NI_85_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_17_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_17_rl_data_t data;
};

union cansig_mk_non_inf_detection_17_rl__azimuth_ni_87_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_87_RL__S1 : 4;
        uint32_t Azimuth_NI_87_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_18_RL_ID       (0x0114)
#define CANMSG_MK_NON_INF_DETECTION_18_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_18_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_18_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_94_RL : 1;
        uint64_t NoInfrastructure_NI_94_RL : 1;
        uint64_t Beam_NI_94_RL : 2;
        uint64_t Azimuth_NI_94_RL : 12;
        uint64_t StdAzimuth_NI_94_RL : 8;
        uint64_t SNRdB_NI_94_RL : 8;
        uint64_t PowerDB_NI_94_RL : 8;
        uint64_t WDoppler10DB_NI_94_RL : 7;
        uint64_t Reserve1_1bit_NI_94_RL : 1;
        uint64_t WRange10DB_NI_94_RL : 8;
        uint64_t CoGDoppler_NI_94_RL : 16;
        uint64_t CoGRange_NI_94_RL : 16;
        uint64_t ValidXBeam_NI_93_RL : 1;
        uint64_t NoInfrastructure_NI_93_RL : 1;
        uint64_t Beam_NI_93_RL : 2;
        uint64_t Azimuth_NI_93_RL : 12;
        uint64_t StdAzimuth_NI_93_RL : 8;
        uint64_t SNRdB_NI_93_RL : 8;
        uint64_t PowerDB_NI_93_RL : 8;
        uint64_t WDoppler10DB_NI_93_RL : 7;
        uint64_t Reserve1_1bit_NI_93_RL : 1;
        uint64_t WRange10DB_NI_93_RL : 8;
        uint64_t CoGDoppler_NI_93_RL : 16;
        uint64_t CoGRange_NI_93_RL : 16;
        uint64_t ValidXBeam_NI_92_RL : 1;
        uint64_t NoInfrastructure_NI_92_RL : 1;
        uint64_t Beam_NI_92_RL : 2;
        uint64_t Azimuth_NI_92_RL__S1 : 4;
        uint64_t Azimuth_NI_92_RL__S0 : 8;
        uint64_t StdAzimuth_NI_92_RL : 8;
        uint64_t SNRdB_NI_92_RL : 8;
        uint64_t PowerDB_NI_92_RL : 8;
        uint64_t WDoppler10DB_NI_92_RL : 7;
        uint64_t Reserve1_1bit_NI_92_RL : 1;
        uint64_t WRange10DB_NI_92_RL : 8;
        uint64_t CoGDoppler_NI_92_RL : 16;
        uint64_t CoGRange_NI_92_RL : 16;
        uint64_t ValidXBeam_NI_91_RL : 1;
        uint64_t NoInfrastructure_NI_91_RL : 1;
        uint64_t Beam_NI_91_RL : 2;
        uint64_t Azimuth_NI_91_RL : 12;
        uint64_t StdAzimuth_NI_91_RL : 8;
        uint64_t SNRdB_NI_91_RL : 8;
        uint64_t PowerDB_NI_91_RL : 8;
        uint64_t WDoppler10DB_NI_91_RL : 7;
        uint64_t Reserve1_1bit_NI_91_RL : 1;
        uint64_t WRange10DB_NI_91_RL : 8;
        uint64_t CoGDoppler_NI_91_RL : 16;
        uint64_t CoGRange_NI_91_RL : 16;
        uint64_t ValidXBeam_NI_90_RL : 1;
        uint64_t NoInfrastructure_NI_90_RL : 1;
        uint64_t Beam_NI_90_RL : 2;
        uint64_t Azimuth_NI_90_RL : 12;
        uint64_t StdAzimuth_NI_90_RL : 8;
        uint64_t SNRdB_NI_90_RL : 8;
        uint64_t PowerDB_NI_90_RL : 8;
        uint64_t WDoppler10DB_NI_90_RL : 7;
        uint64_t Reserve1_1bit_NI_90_RL : 1;
        uint64_t WRange10DB_NI_90_RL : 8;
        uint64_t CoGDoppler_NI_90_RL : 16;
        uint64_t CoGRange_NI_90_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_18_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_18_rl_data_t data;
};

union cansig_mk_non_inf_detection_18_rl__azimuth_ni_92_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_92_RL__S1 : 4;
        uint32_t Azimuth_NI_92_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_19_RL_ID       (0x0115)
#define CANMSG_MK_NON_INF_DETECTION_19_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_19_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_19_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_99_RL : 1;
        uint64_t NoInfrastructure_NI_99_RL : 1;
        uint64_t Beam_NI_99_RL : 2;
        uint64_t Azimuth_NI_99_RL : 12;
        uint64_t StdAzimuth_NI_99_RL : 8;
        uint64_t SNRdB_NI_99_RL : 8;
        uint64_t PowerDB_NI_99_RL : 8;
        uint64_t WDoppler10DB_NI_99_RL : 7;
        uint64_t Reserve1_1bit_NI_99_RL : 1;
        uint64_t WRange10DB_NI_99_RL : 8;
        uint64_t CoGDoppler_NI_99_RL : 16;
        uint64_t CoGRange_NI_99_RL : 16;
        uint64_t ValidXBeam_NI_98_RL : 1;
        uint64_t NoInfrastructure_NI_98_RL : 1;
        uint64_t Beam_NI_98_RL : 2;
        uint64_t Azimuth_NI_98_RL : 12;
        uint64_t StdAzimuth_NI_98_RL : 8;
        uint64_t SNRdB_NI_98_RL : 8;
        uint64_t PowerDB_NI_98_RL : 8;
        uint64_t WDoppler10DB_NI_98_RL : 7;
        uint64_t Reserve1_1bit_NI_98_RL : 1;
        uint64_t WRange10DB_NI_98_RL : 8;
        uint64_t CoGDoppler_NI_98_RL : 16;
        uint64_t CoGRange_NI_98_RL : 16;
        uint64_t ValidXBeam_NI_97_RL : 1;
        uint64_t NoInfrastructure_NI_97_RL : 1;
        uint64_t Beam_NI_97_RL : 2;
        uint64_t Azimuth_NI_97_RL__S1 : 4;
        uint64_t Azimuth_NI_97_RL__S0 : 8;
        uint64_t StdAzimuth_NI_97_RL : 8;
        uint64_t SNRdB_NI_97_RL : 8;
        uint64_t PowerDB_NI_97_RL : 8;
        uint64_t WDoppler10DB_NI_97_RL : 7;
        uint64_t Reserve1_1bit_NI_97_RL : 1;
        uint64_t WRange10DB_NI_97_RL : 8;
        uint64_t CoGDoppler_NI_97_RL : 16;
        uint64_t CoGRange_NI_97_RL : 16;
        uint64_t ValidXBeam_NI_96_RL : 1;
        uint64_t NoInfrastructure_NI_96_RL : 1;
        uint64_t Beam_NI_96_RL : 2;
        uint64_t Azimuth_NI_96_RL : 12;
        uint64_t StdAzimuth_NI_96_RL : 8;
        uint64_t SNRdB_NI_96_RL : 8;
        uint64_t PowerDB_NI_96_RL : 8;
        uint64_t WDoppler10DB_NI_96_RL : 7;
        uint64_t Reserve1_1bit_NI_96_RL : 1;
        uint64_t WRange10DB_NI_96_RL : 8;
        uint64_t CoGDoppler_NI_96_RL : 16;
        uint64_t CoGRange_NI_96_RL : 16;
        uint64_t ValidXBeam_NI_95_RL : 1;
        uint64_t NoInfrastructure_NI_95_RL : 1;
        uint64_t Beam_NI_95_RL : 2;
        uint64_t Azimuth_NI_95_RL : 12;
        uint64_t StdAzimuth_NI_95_RL : 8;
        uint64_t SNRdB_NI_95_RL : 8;
        uint64_t PowerDB_NI_95_RL : 8;
        uint64_t WDoppler10DB_NI_95_RL : 7;
        uint64_t Reserve1_1bit_NI_95_RL : 1;
        uint64_t WRange10DB_NI_95_RL : 8;
        uint64_t CoGDoppler_NI_95_RL : 16;
        uint64_t CoGRange_NI_95_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_19_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_19_rl_data_t data;
};

union cansig_mk_non_inf_detection_19_rl__azimuth_ni_97_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_97_RL__S1 : 4;
        uint32_t Azimuth_NI_97_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_1_RL_ID        (0x0103)
#define CANMSG_MK_NON_INF_DETECTION_1_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_1_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_1_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_9_RL : 1;
        uint64_t NoInfrastructure_NI_9_RL : 1;
        uint64_t Beam_NI_9_RL : 2;
        uint64_t Azimuth_NI_9_RL : 12;
        uint64_t StdAzimuth_NI_9_RL : 8;
        uint64_t SNRdB_NI_9_RL : 8;
        uint64_t PowerDB_NI_9_RL : 8;
        uint64_t WDoppler10DB_NI_9_RL : 7;
        uint64_t Reserve1_1bit_NI_9_RL : 1;
        uint64_t WRange10DB_NI_9_RL : 8;
        uint64_t CoGDoppler_NI_9_RL : 16;
        uint64_t CoGRange_NI_9_RL : 16;
        uint64_t ValidXBeam_NI_8_RL : 1;
        uint64_t NoInfrastructure_NI_8_RL : 1;
        uint64_t Beam_NI_8_RL : 2;
        uint64_t Azimuth_NI_8_RL : 12;
        uint64_t StdAzimuth_NI_8_RL : 8;
        uint64_t SNRdB_NI_8_RL : 8;
        uint64_t PowerDB_NI_8_RL : 8;
        uint64_t WDoppler10DB_NI_8_RL : 7;
        uint64_t Reserve1_1bit_NI_8_RL : 1;
        uint64_t WRange10DB_NI_8_RL : 8;
        uint64_t CoGDoppler_NI_8_RL : 16;
        uint64_t CoGRange_NI_8_RL : 16;
        uint64_t ValidXBeam_NI_7_RL : 1;
        uint64_t NoInfrastructure_NI_7_RL : 1;
        uint64_t Beam_NI_7_RL : 2;
        uint64_t Azimuth_NI_7_RL__S1 : 4;
        uint64_t Azimuth_NI_7_RL__S0 : 8;
        uint64_t StdAzimuth_NI_7_RL : 8;
        uint64_t SNRdB_NI_7_RL : 8;
        uint64_t PowerDB_NI_7_RL : 8;
        uint64_t WDoppler10DB_NI_7_RL : 7;
        uint64_t Reserve1_1bit_NI_7_RL : 1;
        uint64_t WRange10DB_NI_7_RL : 8;
        uint64_t CoGDoppler_NI_7_RL : 16;
        uint64_t CoGRange_NI_7_RL : 16;
        uint64_t ValidXBeam_NI_6_RL : 1;
        uint64_t NoInfrastructure_NI_6_RL : 1;
        uint64_t Beam_NI_6_RL : 2;
        uint64_t Azimuth_NI_6_RL : 12;
        uint64_t StdAzimuth_NI_6_RL : 8;
        uint64_t SNRdB_NI_6_RL : 8;
        uint64_t PowerDB_NI_6_RL : 8;
        uint64_t WDoppler10DB_NI_6_RL : 7;
        uint64_t Reserve1_1bit_NI_6_RL : 1;
        uint64_t WRange10DB_NI_6_RL : 8;
        uint64_t CoGDoppler_NI_6_RL : 16;
        uint64_t CoGRange_NI_6_RL : 16;
        uint64_t ValidXBeam_NI_5_RL : 1;
        uint64_t NoInfrastructure_NI_5_RL : 1;
        uint64_t Beam_NI_5_RL : 2;
        uint64_t Azimuth_NI_5_RL : 12;
        uint64_t StdAzimuth_NI_5_RL : 8;
        uint64_t SNRdB_NI_5_RL : 8;
        uint64_t PowerDB_NI_5_RL : 8;
        uint64_t WDoppler10DB_NI_5_RL : 7;
        uint64_t Reserve1_1bit_NI_5_RL : 1;
        uint64_t WRange10DB_NI_5_RL : 8;
        uint64_t CoGDoppler_NI_5_RL : 16;
        uint64_t CoGRange_NI_5_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_1_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_1_rl_data_t data;
};

union cansig_mk_non_inf_detection_1_rl__azimuth_ni_7_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_7_RL__S1 : 4;
        uint32_t Azimuth_NI_7_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_20_RL_ID       (0x0116)
#define CANMSG_MK_NON_INF_DETECTION_20_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_20_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_20_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_104_RL : 1;
        uint64_t NoInfrastructure_NI_104_RL : 1;
        uint64_t Beam_NI_104_RL : 2;
        uint64_t Azimuth_NI_104_RL : 12;
        uint64_t StdAzimuth_NI_104_RL : 8;
        uint64_t SNRdB_NI_104_RL : 8;
        uint64_t PowerDB_NI_104_RL : 8;
        uint64_t WDoppler10DB_NI_104_RL : 7;
        uint64_t Reserve1_1bit_NI_104_RL : 1;
        uint64_t WRange10DB_NI_104_RL : 8;
        uint64_t CoGDoppler_NI_104_RL : 16;
        uint64_t CoGRange_NI_104_RL : 16;
        uint64_t ValidXBeam_NI_103_RL : 1;
        uint64_t NoInfrastructure_NI_103_RL : 1;
        uint64_t Beam_NI_103_RL : 2;
        uint64_t Azimuth_NI_103_RL : 12;
        uint64_t StdAzimuth_NI_103_RL : 8;
        uint64_t SNRdB_NI_103_RL : 8;
        uint64_t PowerDB_NI_103_RL : 8;
        uint64_t WDoppler10DB_NI_103_RL : 7;
        uint64_t Reserve1_1bit_NI_103_RL : 1;
        uint64_t WRange10DB_NI_103_RL : 8;
        uint64_t CoGDoppler_NI_103_RL : 16;
        uint64_t CoGRange_NI_103_RL : 16;
        uint64_t ValidXBeam_NI_102_RL : 1;
        uint64_t NoInfrastructure_NI_102_RL : 1;
        uint64_t Beam_NI_102_RL : 2;
        uint64_t Azimuth_NI_102_RL__S1 : 4;
        uint64_t Azimuth_NI_102_RL__S0 : 8;
        uint64_t StdAzimuth_NI_102_RL : 8;
        uint64_t SNRdB_NI_102_RL : 8;
        uint64_t PowerDB_NI_102_RL : 8;
        uint64_t WDoppler10DB_NI_102_RL : 7;
        uint64_t Reserve1_1bit_NI_102_RL : 1;
        uint64_t WRange10DB_NI_102_RL : 8;
        uint64_t CoGDoppler_NI_102_RL : 16;
        uint64_t CoGRange_NI_102_RL : 16;
        uint64_t ValidXBeam_NI_101_RL : 1;
        uint64_t NoInfrastructure_NI_101_RL : 1;
        uint64_t Beam_NI_101_RL : 2;
        uint64_t Azimuth_NI_101_RL : 12;
        uint64_t StdAzimuth_NI_101_RL : 8;
        uint64_t SNRdB_NI_101_RL : 8;
        uint64_t PowerDB_NI_101_RL : 8;
        uint64_t WDoppler10DB_NI_101_RL : 7;
        uint64_t Reserve1_1bit_NI_101_RL : 1;
        uint64_t WRange10DB_NI_101_RL : 8;
        uint64_t CoGDoppler_NI_101_RL : 16;
        uint64_t CoGRange_NI_101_RL : 16;
        uint64_t ValidXBeam_NI_100_RL : 1;
        uint64_t NoInfrastructure_NI_100_RL : 1;
        uint64_t Beam_NI_100_RL : 2;
        uint64_t Azimuth_NI_100_RL : 12;
        uint64_t StdAzimuth_NI_100_RL : 8;
        uint64_t SNRdB_NI_100_RL : 8;
        uint64_t PowerDB_NI_100_RL : 8;
        uint64_t WDoppler10DB_NI_100_RL : 7;
        uint64_t Reserve1_1bit_NI_100_RL : 1;
        uint64_t WRange10DB_NI_100_RL : 8;
        uint64_t CoGDoppler_NI_100_RL : 16;
        uint64_t CoGRange_NI_100_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_20_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_20_rl_data_t data;
};

union cansig_mk_non_inf_detection_20_rl__azimuth_ni_102_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_102_RL__S1 : 4;
        uint32_t Azimuth_NI_102_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_21_RL_ID       (0x0117)
#define CANMSG_MK_NON_INF_DETECTION_21_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_21_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_21_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_109_RL : 1;
        uint64_t NoInfrastructure_NI_109_RL : 1;
        uint64_t Beam_NI_109_RL : 2;
        uint64_t Azimuth_NI_109_RL : 12;
        uint64_t StdAzimuth_NI_109_RL : 8;
        uint64_t SNRdB_NI_109_RL : 8;
        uint64_t PowerDB_NI_109_RL : 8;
        uint64_t WDoppler10DB_NI_109_RL : 7;
        uint64_t Reserve1_1bit_NI_109_RL : 1;
        uint64_t WRange10DB_NI_109_RL : 8;
        uint64_t CoGDoppler_NI_109_RL : 16;
        uint64_t CoGRange_NI_109_RL : 16;
        uint64_t ValidXBeam_NI_108_RL : 1;
        uint64_t NoInfrastructure_NI_108_RL : 1;
        uint64_t Beam_NI_108_RL : 2;
        uint64_t Azimuth_NI_108_RL : 12;
        uint64_t StdAzimuth_NI_108_RL : 8;
        uint64_t SNRdB_NI_108_RL : 8;
        uint64_t PowerDB_NI_108_RL : 8;
        uint64_t WDoppler10DB_NI_108_RL : 7;
        uint64_t Reserve1_1bit_NI_108_RL : 1;
        uint64_t WRange10DB_NI_108_RL : 8;
        uint64_t CoGDoppler_NI_108_RL : 16;
        uint64_t CoGRange_NI_108_RL : 16;
        uint64_t ValidXBeam_NI_107_RL : 1;
        uint64_t NoInfrastructure_NI_107_RL : 1;
        uint64_t Beam_NI_107_RL : 2;
        uint64_t Azimuth_NI_107_RL__S1 : 4;
        uint64_t Azimuth_NI_107_RL__S0 : 8;
        uint64_t StdAzimuth_NI_107_RL : 8;
        uint64_t SNRdB_NI_107_RL : 8;
        uint64_t PowerDB_NI_107_RL : 8;
        uint64_t WDoppler10DB_NI_107_RL : 7;
        uint64_t Reserve1_1bit_NI_107_RL : 1;
        uint64_t WRange10DB_NI_107_RL : 8;
        uint64_t CoGDoppler_NI_107_RL : 16;
        uint64_t CoGRange_NI_107_RL : 16;
        uint64_t ValidXBeam_NI_106_RL : 1;
        uint64_t NoInfrastructure_NI_106_RL : 1;
        uint64_t Beam_NI_106_RL : 2;
        uint64_t Azimuth_NI_106_RL : 12;
        uint64_t StdAzimuth_NI_106_RL : 8;
        uint64_t SNRdB_NI_106_RL : 8;
        uint64_t PowerDB_NI_106_RL : 8;
        uint64_t WDoppler10DB_NI_106_RL : 7;
        uint64_t Reserve1_1bit_NI_106_RL : 1;
        uint64_t WRange10DB_NI_106_RL : 8;
        uint64_t CoGDoppler_NI_106_RL : 16;
        uint64_t CoGRange_NI_106_RL : 16;
        uint64_t ValidXBeam_NI_105_RL : 1;
        uint64_t NoInfrastructure_NI_105_RL : 1;
        uint64_t Beam_NI_105_RL : 2;
        uint64_t Azimuth_NI_105_RL : 12;
        uint64_t StdAzimuth_NI_105_RL : 8;
        uint64_t SNRdB_NI_105_RL : 8;
        uint64_t PowerDB_NI_105_RL : 8;
        uint64_t WDoppler10DB_NI_105_RL : 7;
        uint64_t Reserve1_1bit_NI_105_RL : 1;
        uint64_t WRange10DB_NI_105_RL : 8;
        uint64_t CoGDoppler_NI_105_RL : 16;
        uint64_t CoGRange_NI_105_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_21_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_21_rl_data_t data;
};

union cansig_mk_non_inf_detection_21_rl__azimuth_ni_107_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_107_RL__S1 : 4;
        uint32_t Azimuth_NI_107_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_22_RL_ID       (0x0118)
#define CANMSG_MK_NON_INF_DETECTION_22_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_22_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_22_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_114_RL : 1;
        uint64_t NoInfrastructure_NI_114_RL : 1;
        uint64_t Beam_NI_114_RL : 2;
        uint64_t Azimuth_NI_114_RL : 12;
        uint64_t StdAzimuth_NI_114_RL : 8;
        uint64_t SNRdB_NI_114_RL : 8;
        uint64_t PowerDB_NI_114_RL : 8;
        uint64_t WDoppler10DB_NI_114_RL : 7;
        uint64_t Reserve1_1bit_NI_114_RL : 1;
        uint64_t WRange10DB_NI_114_RL : 8;
        uint64_t CoGDoppler_NI_114_RL : 16;
        uint64_t CoGRange_NI_114_RL : 16;
        uint64_t ValidXBeam_NI_113_RL : 1;
        uint64_t NoInfrastructure_NI_113_RL : 1;
        uint64_t Beam_NI_113_RL : 2;
        uint64_t Azimuth_NI_113_RL : 12;
        uint64_t StdAzimuth_NI_113_RL : 8;
        uint64_t SNRdB_NI_113_RL : 8;
        uint64_t PowerDB_NI_113_RL : 8;
        uint64_t WDoppler10DB_NI_113_RL : 7;
        uint64_t Reserve1_1bit_NI_113_RL : 1;
        uint64_t WRange10DB_NI_113_RL : 8;
        uint64_t CoGDoppler_NI_113_RL : 16;
        uint64_t CoGRange_NI_113_RL : 16;
        uint64_t ValidXBeam_NI_112_RL : 1;
        uint64_t NoInfrastructure_NI_112_RL : 1;
        uint64_t Beam_NI_112_RL : 2;
        uint64_t Azimuth_NI_112_RL__S1 : 4;
        uint64_t Azimuth_NI_112_RL__S0 : 8;
        uint64_t StdAzimuth_NI_112_RL : 8;
        uint64_t SNRdB_NI_112_RL : 8;
        uint64_t PowerDB_NI_112_RL : 8;
        uint64_t WDoppler10DB_NI_112_RL : 7;
        uint64_t Reserve1_1bit_NI_112_RL : 1;
        uint64_t WRange10DB_NI_112_RL : 8;
        uint64_t CoGDoppler_NI_112_RL : 16;
        uint64_t CoGRange_NI_112_RL : 16;
        uint64_t ValidXBeam_NI_111_RL : 1;
        uint64_t NoInfrastructure_NI_111_RL : 1;
        uint64_t Beam_NI_111_RL : 2;
        uint64_t Azimuth_NI_111_RL : 12;
        uint64_t StdAzimuth_NI_111_RL : 8;
        uint64_t SNRdB_NI_111_RL : 8;
        uint64_t PowerDB_NI_111_RL : 8;
        uint64_t WDoppler10DB_NI_111_RL : 7;
        uint64_t Reserve1_1bit_NI_111_RL : 1;
        uint64_t WRange10DB_NI_111_RL : 8;
        uint64_t CoGDoppler_NI_111_RL : 16;
        uint64_t CoGRange_NI_111_RL : 16;
        uint64_t ValidXBeam_NI_110_RL : 1;
        uint64_t NoInfrastructure_NI_110_RL : 1;
        uint64_t Beam_NI_110_RL : 2;
        uint64_t Azimuth_NI_110_RL : 12;
        uint64_t StdAzimuth_NI_110_RL : 8;
        uint64_t SNRdB_NI_110_RL : 8;
        uint64_t PowerDB_NI_110_RL : 8;
        uint64_t WDoppler10DB_NI_110_RL : 7;
        uint64_t Reserve1_1bit_NI_110_RL : 1;
        uint64_t WRange10DB_NI_110_RL : 8;
        uint64_t CoGDoppler_NI_110_RL : 16;
        uint64_t CoGRange_NI_110_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_22_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_22_rl_data_t data;
};

union cansig_mk_non_inf_detection_22_rl__azimuth_ni_112_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_112_RL__S1 : 4;
        uint32_t Azimuth_NI_112_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_23_RL_ID       (0x0119)
#define CANMSG_MK_NON_INF_DETECTION_23_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_23_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_23_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_119_RL : 1;
        uint64_t NoInfrastructure_NI_119_RL : 1;
        uint64_t Beam_NI_119_RL : 2;
        uint64_t Azimuth_NI_119_RL : 12;
        uint64_t StdAzimuth_NI_119_RL : 8;
        uint64_t SNRdB_NI_119_RL : 8;
        uint64_t PowerDB_NI_119_RL : 8;
        uint64_t WDoppler10DB_NI_119_RL : 7;
        uint64_t Reserve1_1bit_NI_119_RL : 1;
        uint64_t WRange10DB_NI_119_RL : 8;
        uint64_t CoGDoppler_NI_119_RL : 16;
        uint64_t CoGRange_NI_119_RL : 16;
        uint64_t ValidXBeam_NI_118_RL : 1;
        uint64_t NoInfrastructure_NI_118_RL : 1;
        uint64_t Beam_NI_118_RL : 2;
        uint64_t Azimuth_NI_118_RL : 12;
        uint64_t StdAzimuth_NI_118_RL : 8;
        uint64_t SNRdB_NI_118_RL : 8;
        uint64_t PowerDB_NI_118_RL : 8;
        uint64_t WDoppler10DB_NI_118_RL : 7;
        uint64_t Reserve1_1bit_NI_118_RL : 1;
        uint64_t WRange10DB_NI_118_RL : 8;
        uint64_t CoGDoppler_NI_118_RL : 16;
        uint64_t CoGRange_NI_118_RL : 16;
        uint64_t ValidXBeam_NI_117_RL : 1;
        uint64_t NoInfrastructure_NI_117_RL : 1;
        uint64_t Beam_NI_117_RL : 2;
        uint64_t Azimuth_NI_117_RL__S1 : 4;
        uint64_t Azimuth_NI_117_RL__S0 : 8;
        uint64_t StdAzimuth_NI_117_RL : 8;
        uint64_t SNRdB_NI_117_RL : 8;
        uint64_t PowerDB_NI_117_RL : 8;
        uint64_t WDoppler10DB_NI_117_RL : 7;
        uint64_t Reserve1_1bit_NI_117_RL : 1;
        uint64_t WRange10DB_NI_117_RL : 8;
        uint64_t CoGDoppler_NI_117_RL : 16;
        uint64_t CoGRange_NI_117_RL : 16;
        uint64_t ValidXBeam_NI_116_RL : 1;
        uint64_t NoInfrastructure_NI_116_RL : 1;
        uint64_t Beam_NI_116_RL : 2;
        uint64_t Azimuth_NI_116_RL : 12;
        uint64_t StdAzimuth_NI_116_RL : 8;
        uint64_t SNRdB_NI_116_RL : 8;
        uint64_t PowerDB_NI_116_RL : 8;
        uint64_t WDoppler10DB_NI_116_RL : 7;
        uint64_t Reserve1_1bit_NI_116_RL : 1;
        uint64_t WRange10DB_NI_116_RL : 8;
        uint64_t CoGDoppler_NI_116_RL : 16;
        uint64_t CoGRange_NI_116_RL : 16;
        uint64_t ValidXBeam_NI_115_RL : 1;
        uint64_t NoInfrastructure_NI_115_RL : 1;
        uint64_t Beam_NI_115_RL : 2;
        uint64_t Azimuth_NI_115_RL : 12;
        uint64_t StdAzimuth_NI_115_RL : 8;
        uint64_t SNRdB_NI_115_RL : 8;
        uint64_t PowerDB_NI_115_RL : 8;
        uint64_t WDoppler10DB_NI_115_RL : 7;
        uint64_t Reserve1_1bit_NI_115_RL : 1;
        uint64_t WRange10DB_NI_115_RL : 8;
        uint64_t CoGDoppler_NI_115_RL : 16;
        uint64_t CoGRange_NI_115_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_23_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_23_rl_data_t data;
};

union cansig_mk_non_inf_detection_23_rl__azimuth_ni_117_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_117_RL__S1 : 4;
        uint32_t Azimuth_NI_117_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_24_RL_ID       (0x011A)
#define CANMSG_MK_NON_INF_DETECTION_24_RL_DLC      (64)
#define CANMSG_MK_NON_INF_DETECTION_24_RL_MIN_DLC  (64)
union canmsg_mk_non_inf_detection_24_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_124_RL : 1;
        uint64_t NoInfrastructure_NI_124_RL : 1;
        uint64_t Beam_NI_124_RL : 2;
        uint64_t Azimuth_NI_124_RL : 12;
        uint64_t StdAzimuth_NI_124_RL : 8;
        uint64_t SNRdB_NI_124_RL : 8;
        uint64_t PowerDB_NI_124_RL : 8;
        uint64_t WDoppler10DB_NI_124_RL : 7;
        uint64_t Reserve1_1bit_NI_124_RL : 1;
        uint64_t WRange10DB_NI_124_RL : 8;
        uint64_t CoGDoppler_NI_124_RL : 16;
        uint64_t CoGRange_NI_124_RL : 16;
        uint64_t ValidXBeam_NI_123_RL : 1;
        uint64_t NoInfrastructure_NI_123_RL : 1;
        uint64_t Beam_NI_123_RL : 2;
        uint64_t Azimuth_NI_123_RL : 12;
        uint64_t StdAzimuth_NI_123_RL : 8;
        uint64_t SNRdB_NI_123_RL : 8;
        uint64_t PowerDB_NI_123_RL : 8;
        uint64_t WDoppler10DB_NI_123_RL : 7;
        uint64_t Reserve1_1bit_NI_123_RL : 1;
        uint64_t WRange10DB_NI_123_RL : 8;
        uint64_t CoGDoppler_NI_123_RL : 16;
        uint64_t CoGRange_NI_123_RL : 16;
        uint64_t ValidXBeam_NI_122_RL : 1;
        uint64_t NoInfrastructure_NI_122_RL : 1;
        uint64_t Beam_NI_122_RL : 2;
        uint64_t Azimuth_NI_122_RL__S1 : 4;
        uint64_t Azimuth_NI_122_RL__S0 : 8;
        uint64_t StdAzimuth_NI_122_RL : 8;
        uint64_t SNRdB_NI_122_RL : 8;
        uint64_t PowerDB_NI_122_RL : 8;
        uint64_t WDoppler10DB_NI_122_RL : 7;
        uint64_t Reserve1_1bit_NI_122_RL : 1;
        uint64_t WRange10DB_NI_122_RL : 8;
        uint64_t CoGDoppler_NI_122_RL : 16;
        uint64_t CoGRange_NI_122_RL : 16;
        uint64_t ValidXBeam_NI_121_RL : 1;
        uint64_t NoInfrastructure_NI_121_RL : 1;
        uint64_t Beam_NI_121_RL : 2;
        uint64_t Azimuth_NI_121_RL : 12;
        uint64_t StdAzimuth_NI_121_RL : 8;
        uint64_t SNRdB_NI_121_RL : 8;
        uint64_t PowerDB_NI_121_RL : 8;
        uint64_t WDoppler10DB_NI_121_RL : 7;
        uint64_t Reserve1_1bit_NI_121_RL : 1;
        uint64_t WRange10DB_NI_121_RL : 8;
        uint64_t CoGDoppler_NI_121_RL : 16;
        uint64_t CoGRange_NI_121_RL : 16;
        uint64_t ValidXBeam_NI_120_RL : 1;
        uint64_t NoInfrastructure_NI_120_RL : 1;
        uint64_t Beam_NI_120_RL : 2;
        uint64_t Azimuth_NI_120_RL : 12;
        uint64_t StdAzimuth_NI_120_RL : 8;
        uint64_t SNRdB_NI_120_RL : 8;
        uint64_t PowerDB_NI_120_RL : 8;
        uint64_t WDoppler10DB_NI_120_RL : 7;
        uint64_t Reserve1_1bit_NI_120_RL : 1;
        uint64_t WRange10DB_NI_120_RL : 8;
        uint64_t CoGDoppler_NI_120_RL : 16;
        uint64_t CoGRange_NI_120_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_24_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_24_rl_data_t data;
};

union cansig_mk_non_inf_detection_24_rl__azimuth_ni_122_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_122_RL__S1 : 4;
        uint32_t Azimuth_NI_122_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_2_RL_ID        (0x0104)
#define CANMSG_MK_NON_INF_DETECTION_2_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_2_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_2_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_14_RL : 1;
        uint64_t NoInfrastructure_NI_14_RL : 1;
        uint64_t Beam_NI_14_RL : 2;
        uint64_t Azimuth_NI_14_RL : 12;
        uint64_t StdAzimuth_NI_14_RL : 8;
        uint64_t SNRdB_NI_14_RL : 8;
        uint64_t PowerDB_NI_14_RL : 8;
        uint64_t WDoppler10DB_NI_14_RL : 7;
        uint64_t Reserve1_1bit_NI_14_RL : 1;
        uint64_t WRange10DB_NI_14_RL : 8;
        uint64_t CoGDoppler_NI_14_RL : 16;
        uint64_t CoGRange_NI_14_RL : 16;
        uint64_t ValidXBeam_NI_13_RL : 1;
        uint64_t NoInfrastructure_NI_13_RL : 1;
        uint64_t Beam_NI_13_RL : 2;
        uint64_t Azimuth_NI_13_RL : 12;
        uint64_t StdAzimuth_NI_13_RL : 8;
        uint64_t SNRdB_NI_13_RL : 8;
        uint64_t PowerDB_NI_13_RL : 8;
        uint64_t WDoppler10DB_NI_13_RL : 7;
        uint64_t Reserve1_1bit_NI_13_RL : 1;
        uint64_t WRange10DB_NI_13_RL : 8;
        uint64_t CoGDoppler_NI_13_RL : 16;
        uint64_t CoGRange_NI_13_RL : 16;
        uint64_t ValidXBeam_NI_12_RL : 1;
        uint64_t NoInfrastructure_NI_12_RL : 1;
        uint64_t Beam_NI_12_RL : 2;
        uint64_t Azimuth_NI_12_RL__S1 : 4;
        uint64_t Azimuth_NI_12_RL__S0 : 8;
        uint64_t StdAzimuth_NI_12_RL : 8;
        uint64_t SNRdB_NI_12_RL : 8;
        uint64_t PowerDB_NI_12_RL : 8;
        uint64_t WDoppler10DB_NI_12_RL : 7;
        uint64_t Reserve1_1bit_NI_12_RL : 1;
        uint64_t WRange10DB_NI_12_RL : 8;
        uint64_t CoGDoppler_NI_12_RL : 16;
        uint64_t CoGRange_NI_12_RL : 16;
        uint64_t ValidXBeam_NI_11_RL : 1;
        uint64_t NoInfrastructure_NI_11_RL : 1;
        uint64_t Beam_NI_11_RL : 2;
        uint64_t Azimuth_NI_11_RL : 12;
        uint64_t StdAzimuth_NI_11_RL : 8;
        uint64_t SNRdB_NI_11_RL : 8;
        uint64_t PowerDB_NI_11_RL : 8;
        uint64_t WDoppler10DB_NI_11_RL : 7;
        uint64_t Reserve1_1bit_NI_11_RL : 1;
        uint64_t WRange10DB_NI_11_RL : 8;
        uint64_t CoGDoppler_NI_11_RL : 16;
        uint64_t CoGRange_NI_11_RL : 16;
        uint64_t ValidXBeam_NI_10_RL : 1;
        uint64_t NoInfrastructure_NI_10_RL : 1;
        uint64_t Beam_NI_10_RL : 2;
        uint64_t Azimuth_NI_10_RL : 12;
        uint64_t StdAzimuth_NI_10_RL : 8;
        uint64_t SNRdB_NI_10_RL : 8;
        uint64_t PowerDB_NI_10_RL : 8;
        uint64_t WDoppler10DB_NI_10_RL : 7;
        uint64_t Reserve1_1bit_NI_10_RL : 1;
        uint64_t WRange10DB_NI_10_RL : 8;
        uint64_t CoGDoppler_NI_10_RL : 16;
        uint64_t CoGRange_NI_10_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_2_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_2_rl_data_t data;
};

union cansig_mk_non_inf_detection_2_rl__azimuth_ni_12_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_12_RL__S1 : 4;
        uint32_t Azimuth_NI_12_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_3_RL_ID        (0x0105)
#define CANMSG_MK_NON_INF_DETECTION_3_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_3_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_3_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_19_RL : 1;
        uint64_t NoInfrastructure_NI_19_RL : 1;
        uint64_t Beam_NI_19_RL : 2;
        uint64_t Azimuth_NI_19_RL : 12;
        uint64_t StdAzimuth_NI_19_RL : 8;
        uint64_t SNRdB_NI_19_RL : 8;
        uint64_t PowerDB_NI_19_RL : 8;
        uint64_t WDoppler10DB_NI_19_RL : 7;
        uint64_t Reserve1_1bit_NI_19_RL : 1;
        uint64_t WRange10DB_NI_19_RL : 8;
        uint64_t CoGDoppler_NI_19_RL : 16;
        uint64_t CoGRange_NI_19_RL : 16;
        uint64_t ValidXBeam_NI_18_RL : 1;
        uint64_t NoInfrastructure_NI_18_RL : 1;
        uint64_t Beam_NI_18_RL : 2;
        uint64_t Azimuth_NI_18_RL : 12;
        uint64_t StdAzimuth_NI_18_RL : 8;
        uint64_t SNRdB_NI_18_RL : 8;
        uint64_t PowerDB_NI_18_RL : 8;
        uint64_t WDoppler10DB_NI_18_RL : 7;
        uint64_t Reserve1_1bit_NI_18_RL : 1;
        uint64_t WRange10DB_NI_18_RL : 8;
        uint64_t CoGDoppler_NI_18_RL : 16;
        uint64_t CoGRange_NI_18_RL : 16;
        uint64_t ValidXBeam_NI_17_RL : 1;
        uint64_t NoInfrastructure_NI_17_RL : 1;
        uint64_t Beam_NI_17_RL : 2;
        uint64_t Azimuth_NI_17_RL__S1 : 4;
        uint64_t Azimuth_NI_17_RL__S0 : 8;
        uint64_t StdAzimuth_NI_17_RL : 8;
        uint64_t SNRdB_NI_17_RL : 8;
        uint64_t PowerDB_NI_17_RL : 8;
        uint64_t WDoppler10DB_NI_17_RL : 7;
        uint64_t Reserve1_1bit_NI_17_RL : 1;
        uint64_t WRange10DB_NI_17_RL : 8;
        uint64_t CoGDoppler_NI_17_RL : 16;
        uint64_t CoGRange_NI_17_RL : 16;
        uint64_t ValidXBeam_NI_16_RL : 1;
        uint64_t NoInfrastructure_NI_16_RL : 1;
        uint64_t Beam_NI_16_RL : 2;
        uint64_t Azimuth_NI_16_RL : 12;
        uint64_t StdAzimuth_NI_16_RL : 8;
        uint64_t SNRdB_NI_16_RL : 8;
        uint64_t PowerDB_NI_16_RL : 8;
        uint64_t WDoppler10DB_NI_16_RL : 7;
        uint64_t Reserve1_1bit_NI_16_RL : 1;
        uint64_t WRange10DB_NI_16_RL : 8;
        uint64_t CoGDoppler_NI_16_RL : 16;
        uint64_t CoGRange_NI_16_RL : 16;
        uint64_t ValidXBeam_NI_15_RL : 1;
        uint64_t NoInfrastructure_NI_15_RL : 1;
        uint64_t Beam_NI_15_RL : 2;
        uint64_t Azimuth_NI_15_RL : 12;
        uint64_t StdAzimuth_NI_15_RL : 8;
        uint64_t SNRdB_NI_15_RL : 8;
        uint64_t PowerDB_NI_15_RL : 8;
        uint64_t WDoppler10DB_NI_15_RL : 7;
        uint64_t Reserve1_1bit_NI_15_RL : 1;
        uint64_t WRange10DB_NI_15_RL : 8;
        uint64_t CoGDoppler_NI_15_RL : 16;
        uint64_t CoGRange_NI_15_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_3_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_3_rl_data_t data;
};

union cansig_mk_non_inf_detection_3_rl__azimuth_ni_17_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_17_RL__S1 : 4;
        uint32_t Azimuth_NI_17_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_4_RL_ID        (0x0106)
#define CANMSG_MK_NON_INF_DETECTION_4_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_4_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_4_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_24_RL : 1;
        uint64_t NoInfrastructure_NI_24_RL : 1;
        uint64_t Beam_NI_24_RL : 2;
        uint64_t Azimuth_NI_24_RL : 12;
        uint64_t StdAzimuth_NI_24_RL : 8;
        uint64_t SNRdB_NI_24_RL : 8;
        uint64_t PowerDB_NI_24_RL : 8;
        uint64_t WDoppler10DB_NI_24_RL : 7;
        uint64_t Reserve1_1bit_NI_24_RL : 1;
        uint64_t WRange10DB_NI_24_RL : 8;
        uint64_t CoGDoppler_NI_24_RL : 16;
        uint64_t CoGRange_NI_24_RL : 16;
        uint64_t ValidXBeam_NI_23_RL : 1;
        uint64_t NoInfrastructure_NI_23_RL : 1;
        uint64_t Beam_NI_23_RL : 2;
        uint64_t Azimuth_NI_23_RL : 12;
        uint64_t StdAzimuth_NI_23_RL : 8;
        uint64_t SNRdB_NI_23_RL : 8;
        uint64_t PowerDB_NI_23_RL : 8;
        uint64_t WDoppler10DB_NI_23_RL : 7;
        uint64_t Reserve1_1bit_NI_23_RL : 1;
        uint64_t WRange10DB_NI_23_RL : 8;
        uint64_t CoGDoppler_NI_23_RL : 16;
        uint64_t CoGRange_NI_23_RL : 16;
        uint64_t ValidXBeam_NI_22_RL : 1;
        uint64_t NoInfrastructure_NI_22_RL : 1;
        uint64_t Beam_NI_22_RL : 2;
        uint64_t Azimuth_NI_22_RL__S1 : 4;
        uint64_t Azimuth_NI_22_RL__S0 : 8;
        uint64_t StdAzimuth_NI_22_RL : 8;
        uint64_t SNRdB_NI_22_RL : 8;
        uint64_t PowerDB_NI_22_RL : 8;
        uint64_t WDoppler10DB_NI_22_RL : 7;
        uint64_t Reserve1_1bit_NI_22_RL : 1;
        uint64_t WRange10DB_NI_22_RL : 8;
        uint64_t CoGDoppler_NI_22_RL : 16;
        uint64_t CoGRange_NI_22_RL : 16;
        uint64_t ValidXBeam_NI_21_RL : 1;
        uint64_t NoInfrastructure_NI_21_RL : 1;
        uint64_t Beam_NI_21_RL : 2;
        uint64_t Azimuth_NI_21_RL : 12;
        uint64_t StdAzimuth_NI_21_RL : 8;
        uint64_t SNRdB_NI_21_RL : 8;
        uint64_t PowerDB_NI_21_RL : 8;
        uint64_t WDoppler10DB_NI_21_RL : 7;
        uint64_t Reserve1_1bit_NI_21_RL : 1;
        uint64_t WRange10DB_NI_21_RL : 8;
        uint64_t CoGDoppler_NI_21_RL : 16;
        uint64_t CoGRange_NI_21_RL : 16;
        uint64_t ValidXBeam_NI_20_RL : 1;
        uint64_t NoInfrastructure_NI_20_RL : 1;
        uint64_t Beam_NI_20_RL : 2;
        uint64_t Azimuth_NI_20_RL : 12;
        uint64_t StdAzimuth_NI_20_RL : 8;
        uint64_t SNRdB_NI_20_RL : 8;
        uint64_t PowerDB_NI_20_RL : 8;
        uint64_t WDoppler10DB_NI_20_RL : 7;
        uint64_t Reserve1_1bit_NI_20_RL : 1;
        uint64_t WRange10DB_NI_20_RL : 8;
        uint64_t CoGDoppler_NI_20_RL : 16;
        uint64_t CoGRange_NI_20_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_4_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_4_rl_data_t data;
};

union cansig_mk_non_inf_detection_4_rl__azimuth_ni_22_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_22_RL__S1 : 4;
        uint32_t Azimuth_NI_22_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_5_RL_ID        (0x0107)
#define CANMSG_MK_NON_INF_DETECTION_5_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_5_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_5_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_29_RL : 1;
        uint64_t NoInfrastructure_NI_29_RL : 1;
        uint64_t Beam_NI_29_RL : 2;
        uint64_t Azimuth_NI_29_RL : 12;
        uint64_t StdAzimuth_NI_29_RL : 8;
        uint64_t SNRdB_NI_29_RL : 8;
        uint64_t PowerDB_NI_29_RL : 8;
        uint64_t WDoppler10DB_NI_29_RL : 7;
        uint64_t Reserve1_1bit_NI_29_RL : 1;
        uint64_t WRange10DB_NI_29_RL : 8;
        uint64_t CoGDoppler_NI_29_RL : 16;
        uint64_t CoGRange_NI_29_RL : 16;
        uint64_t ValidXBeam_NI_28_RL : 1;
        uint64_t NoInfrastructure_NI_28_RL : 1;
        uint64_t Beam_NI_28_RL : 2;
        uint64_t Azimuth_NI_28_RL : 12;
        uint64_t StdAzimuth_NI_28_RL : 8;
        uint64_t SNRdB_NI_28_RL : 8;
        uint64_t PowerDB_NI_28_RL : 8;
        uint64_t WDoppler10DB_NI_28_RL : 7;
        uint64_t Reserve1_1bit_NI_28_RL : 1;
        uint64_t WRange10DB_NI_28_RL : 8;
        uint64_t CoGDoppler_NI_28_RL : 16;
        uint64_t CoGRange_NI_28_RL : 16;
        uint64_t ValidXBeam_NI_27_RL : 1;
        uint64_t NoInfrastructure_NI_27_RL : 1;
        uint64_t Beam_NI_27_RL : 2;
        uint64_t Azimuth_NI_27_RL__S1 : 4;
        uint64_t Azimuth_NI_27_RL__S0 : 8;
        uint64_t StdAzimuth_NI_27_RL : 8;
        uint64_t SNRdB_NI_27_RL : 8;
        uint64_t PowerDB_NI_27_RL : 8;
        uint64_t WDoppler10DB_NI_27_RL : 7;
        uint64_t Reserve1_1bit_NI_27_RL : 1;
        uint64_t WRange10DB_NI_27_RL : 8;
        uint64_t CoGDoppler_NI_27_RL : 16;
        uint64_t CoGRange_NI_27_RL : 16;
        uint64_t ValidXBeam_NI_26_RL : 1;
        uint64_t NoInfrastructure_NI_26_RL : 1;
        uint64_t Beam_NI_26_RL : 2;
        uint64_t Azimuth_NI_26_RL : 12;
        uint64_t StdAzimuth_NI_26_RL : 8;
        uint64_t SNRdB_NI_26_RL : 8;
        uint64_t PowerDB_NI_26_RL : 8;
        uint64_t WDoppler10DB_NI_26_RL : 7;
        uint64_t Reserve1_1bit_NI_26_RL : 1;
        uint64_t WRange10DB_NI_26_RL : 8;
        uint64_t CoGDoppler_NI_26_RL : 16;
        uint64_t CoGRange_NI_26_RL : 16;
        uint64_t ValidXBeam_NI_25_RL : 1;
        uint64_t NoInfrastructure_NI_25_RL : 1;
        uint64_t Beam_NI_25_RL : 2;
        uint64_t Azimuth_NI_25_RL : 12;
        uint64_t StdAzimuth_NI_25_RL : 8;
        uint64_t SNRdB_NI_25_RL : 8;
        uint64_t PowerDB_NI_25_RL : 8;
        uint64_t WDoppler10DB_NI_25_RL : 7;
        uint64_t Reserve1_1bit_NI_25_RL : 1;
        uint64_t WRange10DB_NI_25_RL : 8;
        uint64_t CoGDoppler_NI_25_RL : 16;
        uint64_t CoGRange_NI_25_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_5_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_5_rl_data_t data;
};

union cansig_mk_non_inf_detection_5_rl__azimuth_ni_27_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_27_RL__S1 : 4;
        uint32_t Azimuth_NI_27_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_6_RL_ID        (0x0108)
#define CANMSG_MK_NON_INF_DETECTION_6_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_6_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_6_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_34_RL : 1;
        uint64_t NoInfrastructure_NI_34_RL : 1;
        uint64_t Beam_NI_34_RL : 2;
        uint64_t Azimuth_NI_34_RL : 12;
        uint64_t StdAzimuth_NI_34_RL : 8;
        uint64_t SNRdB_NI_34_RL : 8;
        uint64_t PowerDB_NI_34_RL : 8;
        uint64_t WDoppler10DB_NI_34_RL : 7;
        uint64_t Reserve1_1bit_NI_34_RL : 1;
        uint64_t WRange10DB_NI_34_RL : 8;
        uint64_t CoGDoppler_NI_34_RL : 16;
        uint64_t CoGRange_NI_34_RL : 16;
        uint64_t ValidXBeam_NI_33_RL : 1;
        uint64_t NoInfrastructure_NI_33_RL : 1;
        uint64_t Beam_NI_33_RL : 2;
        uint64_t Azimuth_NI_33_RL : 12;
        uint64_t StdAzimuth_NI_33_RL : 8;
        uint64_t SNRdB_NI_33_RL : 8;
        uint64_t PowerDB_NI_33_RL : 8;
        uint64_t WDoppler10DB_NI_33_RL : 7;
        uint64_t Reserve1_1bit_NI_33_RL : 1;
        uint64_t WRange10DB_NI_33_RL : 8;
        uint64_t CoGDoppler_NI_33_RL : 16;
        uint64_t CoGRange_NI_33_RL : 16;
        uint64_t ValidXBeam_NI_32_RL : 1;
        uint64_t NoInfrastructure_NI_32_RL : 1;
        uint64_t Beam_NI_32_RL : 2;
        uint64_t Azimuth_NI_32_RL__S1 : 4;
        uint64_t Azimuth_NI_32_RL__S0 : 8;
        uint64_t StdAzimuth_NI_32_RL : 8;
        uint64_t SNRdB_NI_32_RL : 8;
        uint64_t PowerDB_NI_32_RL : 8;
        uint64_t WDoppler10DB_NI_32_RL : 7;
        uint64_t Reserve1_1bit_NI_32_RL : 1;
        uint64_t WRange10DB_NI_32_RL : 8;
        uint64_t CoGDoppler_NI_32_RL : 16;
        uint64_t CoGRange_NI_32_RL : 16;
        uint64_t ValidXBeam_NI_31_RL : 1;
        uint64_t NoInfrastructure_NI_31_RL : 1;
        uint64_t Beam_NI_31_RL : 2;
        uint64_t Azimuth_NI_31_RL : 12;
        uint64_t StdAzimuth_NI_31_RL : 8;
        uint64_t SNRdB_NI_31_RL : 8;
        uint64_t PowerDB_NI_31_RL : 8;
        uint64_t WDoppler10DB_NI_31_RL : 7;
        uint64_t Reserve1_1bit_NI_31_RL : 1;
        uint64_t WRange10DB_NI_31_RL : 8;
        uint64_t CoGDoppler_NI_31_RL : 16;
        uint64_t CoGRange_NI_31_RL : 16;
        uint64_t ValidXBeam_NI_30_RL : 1;
        uint64_t NoInfrastructure_NI_30_RL : 1;
        uint64_t Beam_NI_30_RL : 2;
        uint64_t Azimuth_NI_30_RL : 12;
        uint64_t StdAzimuth_NI_30_RL : 8;
        uint64_t SNRdB_NI_30_RL : 8;
        uint64_t PowerDB_NI_30_RL : 8;
        uint64_t WDoppler10DB_NI_30_RL : 7;
        uint64_t Reserve1_1bit_NI_30_RL : 1;
        uint64_t WRange10DB_NI_30_RL : 8;
        uint64_t CoGDoppler_NI_30_RL : 16;
        uint64_t CoGRange_NI_30_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_6_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_6_rl_data_t data;
};

union cansig_mk_non_inf_detection_6_rl__azimuth_ni_32_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_32_RL__S1 : 4;
        uint32_t Azimuth_NI_32_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_7_RL_ID        (0x0109)
#define CANMSG_MK_NON_INF_DETECTION_7_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_7_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_7_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_39_RL : 1;
        uint64_t NoInfrastructure_NI_39_RL : 1;
        uint64_t Beam_NI_39_RL : 2;
        uint64_t Azimuth_NI_39_RL : 12;
        uint64_t StdAzimuth_NI_39_RL : 8;
        uint64_t SNRdB_NI_39_RL : 8;
        uint64_t PowerDB_NI_39_RL : 8;
        uint64_t WDoppler10DB_NI_39_RL : 7;
        uint64_t Reserve1_1bit_NI_39_RL : 1;
        uint64_t WRange10DB_NI_39_RL : 8;
        uint64_t CoGDoppler_NI_39_RL : 16;
        uint64_t CoGRange_NI_39_RL : 16;
        uint64_t ValidXBeam_NI_38_RL : 1;
        uint64_t NoInfrastructure_NI_38_RL : 1;
        uint64_t Beam_NI_38_RL : 2;
        uint64_t Azimuth_NI_38_RL : 12;
        uint64_t StdAzimuth_NI_38_RL : 8;
        uint64_t SNRdB_NI_38_RL : 8;
        uint64_t PowerDB_NI_38_RL : 8;
        uint64_t WDoppler10DB_NI_38_RL : 7;
        uint64_t Reserve1_1bit_NI_38_RL : 1;
        uint64_t WRange10DB_NI_38_RL : 8;
        uint64_t CoGDoppler_NI_38_RL : 16;
        uint64_t CoGRange_NI_38_RL : 16;
        uint64_t ValidXBeam_NI_37_RL : 1;
        uint64_t NoInfrastructure_NI_37_RL : 1;
        uint64_t Beam_NI_37_RL : 2;
        uint64_t Azimuth_NI_37_RL__S1 : 4;
        uint64_t Azimuth_NI_37_RL__S0 : 8;
        uint64_t StdAzimuth_NI_37_RL : 8;
        uint64_t SNRdB_NI_37_RL : 8;
        uint64_t PowerDB_NI_37_RL : 8;
        uint64_t WDoppler10DB_NI_37_RL : 7;
        uint64_t Reserve1_1bit_NI_37_RL : 1;
        uint64_t WRange10DB_NI_37_RL : 8;
        uint64_t CoGDoppler_NI_37_RL : 16;
        uint64_t CoGRange_NI_37_RL : 16;
        uint64_t ValidXBeam_NI_36_RL : 1;
        uint64_t NoInfrastructure_NI_36_RL : 1;
        uint64_t Beam_NI_36_RL : 2;
        uint64_t Azimuth_NI_36_RL : 12;
        uint64_t StdAzimuth_NI_36_RL : 8;
        uint64_t SNRdB_NI_36_RL : 8;
        uint64_t PowerDB_NI_36_RL : 8;
        uint64_t WDoppler10DB_NI_36_RL : 7;
        uint64_t Reserve1_1bit_NI_36_RL : 1;
        uint64_t WRange10DB_NI_36_RL : 8;
        uint64_t CoGDoppler_NI_36_RL : 16;
        uint64_t CoGRange_NI_36_RL : 16;
        uint64_t ValidXBeam_NI_35_RL : 1;
        uint64_t NoInfrastructure_NI_35_RL : 1;
        uint64_t Beam_NI_35_RL : 2;
        uint64_t Azimuth_NI_35_RL : 12;
        uint64_t StdAzimuth_NI_35_RL : 8;
        uint64_t SNRdB_NI_35_RL : 8;
        uint64_t PowerDB_NI_35_RL : 8;
        uint64_t WDoppler10DB_NI_35_RL : 7;
        uint64_t Reserve1_1bit_NI_35_RL : 1;
        uint64_t WRange10DB_NI_35_RL : 8;
        uint64_t CoGDoppler_NI_35_RL : 16;
        uint64_t CoGRange_NI_35_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_7_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_7_rl_data_t data;
};

union cansig_mk_non_inf_detection_7_rl__azimuth_ni_37_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_37_RL__S1 : 4;
        uint32_t Azimuth_NI_37_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_8_RL_ID        (0x010A)
#define CANMSG_MK_NON_INF_DETECTION_8_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_8_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_8_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_44_RL : 1;
        uint64_t NoInfrastructure_NI_44_RL : 1;
        uint64_t Beam_NI_44_RL : 2;
        uint64_t Azimuth_NI_44_RL : 12;
        uint64_t StdAzimuth_NI_44_RL : 8;
        uint64_t SNRdB_NI_44_RL : 8;
        uint64_t PowerDB_NI_44_RL : 8;
        uint64_t WDoppler10DB_NI_44_RL : 7;
        uint64_t Reserve1_1bit_NI_44_RL : 1;
        uint64_t WRange10DB_NI_44_RL : 8;
        uint64_t CoGDoppler_NI_44_RL : 16;
        uint64_t CoGRange_NI_44_RL : 16;
        uint64_t ValidXBeam_NI_43_RL : 1;
        uint64_t NoInfrastructure_NI_43_RL : 1;
        uint64_t Beam_NI_43_RL : 2;
        uint64_t Azimuth_NI_43_RL : 12;
        uint64_t StdAzimuth_NI_43_RL : 8;
        uint64_t SNRdB_NI_43_RL : 8;
        uint64_t PowerDB_NI_43_RL : 8;
        uint64_t WDoppler10DB_NI_43_RL : 7;
        uint64_t Reserve1_1bit_NI_43_RL : 1;
        uint64_t WRange10DB_NI_43_RL : 8;
        uint64_t CoGDoppler_NI_43_RL : 16;
        uint64_t CoGRange_NI_43_RL : 16;
        uint64_t ValidXBeam_NI_42_RL : 1;
        uint64_t NoInfrastructure_NI_42_RL : 1;
        uint64_t Beam_NI_42_RL : 2;
        uint64_t Azimuth_NI_42_RL__S1 : 4;
        uint64_t Azimuth_NI_42_RL__S0 : 8;
        uint64_t StdAzimuth_NI_42_RL : 8;
        uint64_t SNRdB_NI_42_RL : 8;
        uint64_t PowerDB_NI_42_RL : 8;
        uint64_t WDoppler10DB_NI_42_RL : 7;
        uint64_t Reserve1_1bit_NI_42_RL : 1;
        uint64_t WRange10DB_NI_42_RL : 8;
        uint64_t CoGDoppler_NI_42_RL : 16;
        uint64_t CoGRange_NI_42_RL : 16;
        uint64_t ValidXBeam_NI_41_RL : 1;
        uint64_t NoInfrastructure_NI_41_RL : 1;
        uint64_t Beam_NI_41_RL : 2;
        uint64_t Azimuth_NI_41_RL : 12;
        uint64_t StdAzimuth_NI_41_RL : 8;
        uint64_t SNRdB_NI_41_RL : 8;
        uint64_t PowerDB_NI_41_RL : 8;
        uint64_t WDoppler10DB_NI_41_RL : 7;
        uint64_t Reserve1_1bit_NI_41_RL : 1;
        uint64_t WRange10DB_NI_41_RL : 8;
        uint64_t CoGDoppler_NI_41_RL : 16;
        uint64_t CoGRange_NI_41_RL : 16;
        uint64_t ValidXBeam_NI_40_RL : 1;
        uint64_t NoInfrastructure_NI_40_RL : 1;
        uint64_t Beam_NI_40_RL : 2;
        uint64_t Azimuth_NI_40_RL : 12;
        uint64_t StdAzimuth_NI_40_RL : 8;
        uint64_t SNRdB_NI_40_RL : 8;
        uint64_t PowerDB_NI_40_RL : 8;
        uint64_t WDoppler10DB_NI_40_RL : 7;
        uint64_t Reserve1_1bit_NI_40_RL : 1;
        uint64_t WRange10DB_NI_40_RL : 8;
        uint64_t CoGDoppler_NI_40_RL : 16;
        uint64_t CoGRange_NI_40_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_8_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_8_rl_data_t data;
};

union cansig_mk_non_inf_detection_8_rl__azimuth_ni_42_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_42_RL__S1 : 4;
        uint32_t Azimuth_NI_42_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_NON_INF_DETECTION_9_RL_ID        (0x010B)
#define CANMSG_MK_NON_INF_DETECTION_9_RL_DLC       (64)
#define CANMSG_MK_NON_INF_DETECTION_9_RL_MIN_DLC   (64)
union canmsg_mk_non_inf_detection_9_rl_data_t
{
    uint8_t buffer[64];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t ValidXBeam_NI_49_RL : 1;
        uint64_t NoInfrastructure_NI_49_RL : 1;
        uint64_t Beam_NI_49_RL : 2;
        uint64_t Azimuth_NI_49_RL : 12;
        uint64_t StdAzimuth_NI_49_RL : 8;
        uint64_t SNRdB_NI_49_RL : 8;
        uint64_t PowerDB_NI_49_RL : 8;
        uint64_t WDoppler10DB_NI_49_RL : 7;
        uint64_t Reserve1_1bit_NI_49_RL : 1;
        uint64_t WRange10DB_NI_49_RL : 8;
        uint64_t CoGDoppler_NI_49_RL : 16;
        uint64_t CoGRange_NI_49_RL : 16;
        uint64_t ValidXBeam_NI_48_RL : 1;
        uint64_t NoInfrastructure_NI_48_RL : 1;
        uint64_t Beam_NI_48_RL : 2;
        uint64_t Azimuth_NI_48_RL : 12;
        uint64_t StdAzimuth_NI_48_RL : 8;
        uint64_t SNRdB_NI_48_RL : 8;
        uint64_t PowerDB_NI_48_RL : 8;
        uint64_t WDoppler10DB_NI_48_RL : 7;
        uint64_t Reserve1_1bit_NI_48_RL : 1;
        uint64_t WRange10DB_NI_48_RL : 8;
        uint64_t CoGDoppler_NI_48_RL : 16;
        uint64_t CoGRange_NI_48_RL : 16;
        uint64_t ValidXBeam_NI_47_RL : 1;
        uint64_t NoInfrastructure_NI_47_RL : 1;
        uint64_t Beam_NI_47_RL : 2;
        uint64_t Azimuth_NI_47_RL__S1 : 4;
        uint64_t Azimuth_NI_47_RL__S0 : 8;
        uint64_t StdAzimuth_NI_47_RL : 8;
        uint64_t SNRdB_NI_47_RL : 8;
        uint64_t PowerDB_NI_47_RL : 8;
        uint64_t WDoppler10DB_NI_47_RL : 7;
        uint64_t Reserve1_1bit_NI_47_RL : 1;
        uint64_t WRange10DB_NI_47_RL : 8;
        uint64_t CoGDoppler_NI_47_RL : 16;
        uint64_t CoGRange_NI_47_RL : 16;
        uint64_t ValidXBeam_NI_46_RL : 1;
        uint64_t NoInfrastructure_NI_46_RL : 1;
        uint64_t Beam_NI_46_RL : 2;
        uint64_t Azimuth_NI_46_RL : 12;
        uint64_t StdAzimuth_NI_46_RL : 8;
        uint64_t SNRdB_NI_46_RL : 8;
        uint64_t PowerDB_NI_46_RL : 8;
        uint64_t WDoppler10DB_NI_46_RL : 7;
        uint64_t Reserve1_1bit_NI_46_RL : 1;
        uint64_t WRange10DB_NI_46_RL : 8;
        uint64_t CoGDoppler_NI_46_RL : 16;
        uint64_t CoGRange_NI_46_RL : 16;
        uint64_t ValidXBeam_NI_45_RL : 1;
        uint64_t NoInfrastructure_NI_45_RL : 1;
        uint64_t Beam_NI_45_RL : 2;
        uint64_t Azimuth_NI_45_RL : 12;
        uint64_t StdAzimuth_NI_45_RL : 8;
        uint64_t SNRdB_NI_45_RL : 8;
        uint64_t PowerDB_NI_45_RL : 8;
        uint64_t WDoppler10DB_NI_45_RL : 7;
        uint64_t Reserve1_1bit_NI_45_RL : 1;
        uint64_t WRange10DB_NI_45_RL : 8;
        uint64_t CoGDoppler_NI_45_RL : 16;
        uint64_t CoGRange_NI_45_RL : 16;
    } signals;
};

struct canmsg_mk_non_inf_detection_9_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_non_inf_detection_9_rl_data_t data;
};

union cansig_mk_non_inf_detection_9_rl__azimuth_ni_47_rl_data_t
{
    uint32_t val;
    struct
    {
        uint32_t Azimuth_NI_47_RL__S1 : 4;
        uint32_t Azimuth_NI_47_RL__S0 : 8;
    } fields;
};

#define CANMSG_MK_TARGET_DETECTION_HEADER_RL_ID    (0x0101)
#define CANMSG_MK_TARGET_DETECTION_HEADER_RL_DLC   (48)
#define CANMSG_MK_TARGET_DETECTION_HEADER_RL_MIN_DLC (48)
union canmsg_mk_target_detection_header_rl_data_t
{
    uint8_t buffer[48];
    struct
    {
        uint64_t PAD0;
        uint64_t PAD1 : 8;
        uint64_t InterferenceExceeded_RL : 8;
        uint64_t PAD2 : 8;
        uint64_t numFreespaceDetections_RL : 2;
        uint64_t DetectionListVersion_RL : 6;
        uint64_t numInfrastructureDetected_RL : 8;
        uint64_t numNonInfraDetected_RL : 8;
        uint64_t numCmplxValPerDetectionBeam3_RL : 8;
        uint64_t numCmplxValPerDetectionBeam2_RL : 8;
        uint64_t numCmplxValPerDetectionBeam1_RL : 8;
        uint64_t UnambiguousVelMeas3_RL : 8;
        uint64_t UnambiguousVelMeas2_RL : 8;
        uint64_t UnambiguousVelMeas1_RL : 8;
        uint64_t FC1MHz3_RL : 16;
        uint64_t FC1MHz2_RL : 16;
        uint64_t FC1MHz1_RL : 16;
        uint64_t HostYawEst_RL : 16;
        uint64_t HostAccelLatEst_RL : 16;
        uint64_t HostAccelLongEst_RL : 16;
        uint64_t HostVelEst_RL : 16;
        uint64_t BW100KHz3_RL : 16;
        uint64_t BW100KHz2_RL : 16;
        uint64_t BW100KHz1_RL : 16;
        uint64_t TimeStamp_RL : 32;
        uint64_t CycleNumber_RL : 32;
    } signals;
};

struct canmsg_mk_target_detection_header_rl_t
{
    struct veh_message general;
    struct veh_message_context context;
    union canmsg_mk_target_detection_header_rl_data_t data;
};

//=====================================================================================//


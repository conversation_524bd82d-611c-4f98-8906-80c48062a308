#include "common_utils.h"
#include <iomanip>
#include <ctime>
#include "cyber/common/log.h"

namespace io_server{

uint64_t GetTimeInMillis() {
  struct timeval time_val;
  gettimeofday(&time_val, NULL);
  return time_val.tv_sec * 1000 + time_val.tv_usec / 1000;
}

int ParseJsonString(const std::string& input, nlohmann::json& out_json) {
  int flag = 0;
  if (input.empty()) {
    AERROR << __func__ << " input is empty!";
    return -1;
  }

  try {
    out_json = nlohmann::json::parse(input);
  } catch (nlohmann::detail::parse_error& e) {
    AERROR << __func__ << " json parse failed " << input.c_str()  << "Parse error: " << e.what() ;
    return -1;
  } catch (nlohmann::detail::type_error& e) {
    AERROR << __func__ << " parse type err " << input.c_str()  << "Parse error: " << e.what() ;
    return -1;
  }
  return flag;
}

double GetTimeS(struct timeval* pt) {
  double N = 1000.0;
  struct timeval tv;
  if (pt == nullptr)
    gettimeofday(&tv, NULL);  // get current time
  else
    tv = *pt;
  double milliseconds = tv.tv_sec * N + tv.tv_usec / N;  // calculate milliseconds
  return milliseconds / 1000;
}


std::string GetTimeInHHMMSSmmm() {
  using namespace std::chrono;
  // get current time
  auto now = system_clock::now();
  // get number of milliseconds for the current second
  // (remainder after division into seconds)
  auto ms = duration_cast<milliseconds>(now.time_since_epoch()) % 1000;
  // convert to std::time_t in order to convert to std::tm (broken time)
  auto timer = system_clock::to_time_t(now);
  // convert to broken time
  std::tm bt = *std::localtime(&timer);

  std::ostringstream oss;
  oss << std::put_time(&bt, "%H:%M:%S");  // HH:MM:SS
  oss << '.' << std::setfill('0') << std::setw(3) << ms.count();
  return oss.str();
}

uint32_t commonGearToPDCUGear(uint32_t common_gear){
    uint32_t res = 0;
    if(static_cast<uint32_t>(CommonGearPosition::NEUTRAL) == common_gear)
    {res = static_cast<uint32_t>(PDCU_ActualGear::N);}
    else if(static_cast<uint32_t>(CommonGearPosition::DRIVING) == common_gear)
    {res = static_cast<uint32_t>(PDCU_ActualGear::D);}
    else if(static_cast<uint32_t>(CommonGearPosition::REVERSE) == common_gear)
    {res = static_cast<uint32_t>(PDCU_ActualGear::R);}
    else if(static_cast<uint32_t>(CommonGearPosition::PARKING) == common_gear)
    {res = static_cast<uint32_t>(PDCU_ActualGear::P);}
    return res;
}

uint32_t PDCUGearToCommonGear(uint32_t pdcu_gear){
    uint32_t res = 0;
    if(static_cast<uint32_t>(PDCU_ActualGear::N) == pdcu_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::NEUTRAL);}
    else if(static_cast<uint32_t>(PDCU_ActualGear::D) == pdcu_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::DRIVING);}
    else if(static_cast<uint32_t>(PDCU_ActualGear::R) == pdcu_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::REVERSE);}
    else if(static_cast<uint32_t>(PDCU_ActualGear::P) == pdcu_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::PARKING);}
    return res;
}    

uint32_t commonGearToIBCGear(uint32_t common_gear){
    uint32_t res = 0;
    if(static_cast<uint32_t>(CommonGearPosition::NONE) == common_gear)
    {res = static_cast<uint32_t>(IBC_TargetGearReq::NOREQUEST);}
    else if(static_cast<uint32_t>(CommonGearPosition::NEUTRAL) == common_gear)
    {res = static_cast<uint32_t>(IBC_TargetGearReq::N);}
    else if(static_cast<uint32_t>(CommonGearPosition::DRIVING) == common_gear)
    {res = static_cast<uint32_t>(IBC_TargetGearReq::D);}
    else if(static_cast<uint32_t>(CommonGearPosition::REVERSE) == common_gear)
    {res = static_cast<uint32_t>(IBC_TargetGearReq::R);}
    else if(static_cast<uint32_t>(CommonGearPosition::PARKING) == common_gear)
    {res = static_cast<uint32_t>(IBC_TargetGearReq::P);}
    return res;
}

uint32_t IBCGearToCommonGear(uint32_t ibc_gear){
    uint32_t res = 0;
    if(static_cast<uint32_t>(IBC_TargetGearReq::N) == ibc_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::NEUTRAL);}
    else if(static_cast<uint32_t>(IBC_TargetGearReq::D) == ibc_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::DRIVING);}
    else if(static_cast<uint32_t>(IBC_TargetGearReq::R) == ibc_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::REVERSE);}
    else if(static_cast<uint32_t>(IBC_TargetGearReq::P) == ibc_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::PARKING);}
    else
    {res = static_cast<uint32_t>(CommonGearPosition::NONE);}
    return res;
}

uint32_t commonGearToAPAGear(uint32_t common_gear){
    uint32_t res = 0;
    if(static_cast<uint32_t>(CommonGearPosition::NONE) == common_gear)
    {res = static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::NOREQUEST);}
    else if(static_cast<uint32_t>(CommonGearPosition::NEUTRAL) == common_gear)
    {res = static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::N);}
    else if(static_cast<uint32_t>(CommonGearPosition::DRIVING) == common_gear)
    {res = static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::D);}
    else if(static_cast<uint32_t>(CommonGearPosition::REVERSE) == common_gear)
    {res = static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::R);}
    else if(static_cast<uint32_t>(CommonGearPosition::PARKING) == common_gear)
    {res = static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::P);}
    return res;
}

uint32_t APAGearToCommonGear(uint32_t apa_gear){
    uint32_t res = 0;
    if(static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::N) == apa_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::NEUTRAL);}
    else if(static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::D) == apa_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::DRIVING);}
    else if(static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::R) == apa_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::REVERSE);}
    else if(static_cast<uint32_t>(HAD_APA_ReqtTargetGearPosSt::P) == apa_gear)
    {res = static_cast<uint32_t>(CommonGearPosition::PARKING);}
    else
    {res = static_cast<uint32_t>(CommonGearPosition::NONE);}
    return res;
}

int32_t APAToWMWheelDir(int32_t dir){
  if(1 == dir){
      return 1;//Forward
  }
  else if(2 == dir){
      return -1;//backward
  }
  else if(3 == dir){
      return 0;//Standstill
  }
  return dir;
}

}

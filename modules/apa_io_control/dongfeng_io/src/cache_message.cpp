#include "cache_message.h"
#include <cstring>
#include <string>
#include "vehctrl_common.h"

namespace io_server {

CacheMessage::CacheMessage(const std::string &topic, const IpcMessage &msg)
    : data_(nullptr), data_len_(0), cycle_count_(0), retain_(false), topic_(topic) {
  if (msg.data && msg.length > 0) {
    data_allocated_.resize(msg.length);
    data_ = data_allocated_.data();
    memcpy(data_, msg.data, msg.length);
    data_len_ = msg.length;
    retain_ = msg.retain;
    cycle_count_ = getCyclecount(msg.data);
  }
}

CacheMessage::CacheMessage(const std::string &topic, const uint8_t *data, uint32_t data_len,
                           bool retain)
    : data_(nullptr), data_len_(0), cycle_count_(0), retain_(false), topic_(topic) {
  if (data && data_len > 0) {
    data_allocated_.resize(data_len);
    data_ = data_allocated_.data();
    memcpy(data_, data, data_len);
    data_len_ = data_len;
    retain_ = retain;
    cycle_count_ = getCyclecount(data);
  }
}

CacheMessage::CacheMessage() : data_(nullptr), data_len_(0), cycle_count_(0), retain_(false) {}

CacheMessage::~CacheMessage() {}

CacheMessage::CacheMessage(const CacheMessage &msg) {
  data_allocated_.resize(msg.data_allocated_.size());
  data_ = data_allocated_.data();
  memcpy(data_, msg.data_, msg.data_len_);
  data_len_ = msg.data_len_;
  retain_ = msg.retain_;
  cycle_count_ = msg.cycle_count_;
  topic_ = msg.topic_;
}

CacheMessage::CacheMessage(CacheMessage &&msg) noexcept {
  data_allocated_.resize(msg.data_allocated_.size());
  data_ = data_allocated_.data();
  memcpy(data_, msg.data_, msg.data_len_);
  data_len_ = msg.data_len_;
  retain_ = msg.retain_;
  cycle_count_ = msg.cycle_count_;
  topic_ = msg.topic_;
}

CacheMessage &CacheMessage::operator=(const CacheMessage &msg) {
  if (this == &msg) {
    return *this;
  }
  data_allocated_.resize(msg.data_allocated_.size());
  data_ = data_allocated_.data();
  memcpy(data_, msg.data_, msg.data_len_);
  data_len_ = msg.data_len_;
  retain_ = msg.retain_;
  cycle_count_ = msg.cycle_count_;
  topic_ = msg.topic_;
  return *this;
}

CacheMessage &CacheMessage::operator=(CacheMessage &&msg) noexcept {
  if (this == &msg) {
    return *this;
  }
  data_allocated_.resize(msg.data_allocated_.size());
  data_ = data_allocated_.data();
  memcpy(data_, msg.data_, msg.data_len_);
  data_len_ = msg.data_len_;
  retain_ = msg.retain_;
  cycle_count_ = msg.cycle_count_;
  topic_ = msg.topic_;
  return *this;
}

IpcMessage CacheMessage::toIpcMessage() { return IpcMessage{data_len_, data_, retain_}; }

uint8_t CacheMessage::getCyclecount(const uint8_t *data) { return data[2]; }

}  // namespace io_server
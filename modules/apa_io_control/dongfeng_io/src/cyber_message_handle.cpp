#include "cyber_message_handle.h"

namespace io_server{
namespace dongfeng{

    std::atomic<uint32_t> CyberMessenger::ult_enable_{0};
    CyberMessenger::CyberMessenger(std::shared_ptr <Node> node, NodeCfg& nodecfg)
    :pNode(node),
    nodeCfg(nodecfg)
    {
        init();
        initCyberPub();
        initCyberSub();
    }

    CyberMessenger::~CyberMessenger()
    {
    }

    void CyberMessenger::enterStop(){
        io_module_mode_ = 1;
    }

    void CyberMessenger::enterStart(){
        io_module_mode_ = 0;
    }

    void CyberMessenger::enterECO(){
        io_module_mode_ = 2;
    }

    bool CyberMessenger::initUltFliterParam(){
        if(!ConfigData::Instance()->is_valid_){
            return false;
        }
        filter_window_size_ = ConfigData::Instance()->filter_window_size_;
        filter_pickup_index_ = ConfigData::Instance()->filter_pickup_index_;
        uss_valid_distance_cm_ = ConfigData::Instance()->uss_valid_distance_cm_;
        ibc_pedal_travel_  = ConfigData::Instance()->ibc_pedal_travel_;
        return true;
    }

    bool CyberMessenger::initParams(){
        if(!ConfigData::Instance()->is_valid_){
            return false;
        }
        //AIS-45502
        eco_pub_speed_threshold_  = ConfigData::Instance()->eco_pub_speed_threshold_;
        eco_no_pub_speed_threshold_  = ConfigData::Instance()->eco_no_pub_speed_threshold_;
        return true;
    }

    bool CyberMessenger::init(){
        initUltFliterParam();
        initParams();
        return true;
    }

    void CyberMessenger::initCyberPub(){
#if USE_IO_CHECK
       if(pWriterIOChassisInfoDebug == nullptr){
        auto test_chassis_info_debug = nodeCfg.getPubChannel("test_chassis_info_debug");
        pWriterIOChassisInfoDebug = pNode->CreateWriter<IOChassisInfoDebug>(test_chassis_info_debug.name);
       }
#endif
       //output
       if(pWriterImuInfoChassis == nullptr){
        auto chassis_imu = nodeCfg.getPubChannel("chassis_imu");
        pWriterImuInfoChassis = pNode->CreateWriter<Imu>(chassis_imu.name);
       }
       if(pWriterImuInfo == nullptr){
        auto imu = nodeCfg.getPubChannel("imu");
        pWriterImuInfo = pNode->CreateWriter<Imu>(imu.name);
       }
       if(pWriterAppRpaReqInfo == nullptr){
        auto appRpaReq = nodeCfg.getPubChannel("app_rpa_req");
        pWriterAppRpaReqInfo = pNode->CreateWriter<AppRpaReq>(appRpaReq.name);
       }
       if(pWriterKeyRpaReqInfo == nullptr){
        auto keyRpaReq = nodeCfg.getPubChannel("key_rpa_req");
        pWriterKeyRpaReqInfo = pNode->CreateWriter<KeyRpaReq>(keyRpaReq.name);
       }
       if(pWriterUltrasonicInfo == nullptr){
        auto ultrasonic = nodeCfg.getPubChannel("driver_ultrasonic");
        pWriterUltrasonicInfo = pNode->CreateWriter<PiUssApp2DataInfo>(ultrasonic.name);
       }
       if(pWriterSystemCheckResultInfo == nullptr){
        auto io_out_system_check_ret = nodeCfg.getPubChannel("io_out_system_check_ret");
        pWriterSystemCheckResultInfo = pNode->CreateWriter<SystemCheckResultInfo>(io_out_system_check_ret.name);
       }
       if(pWriterShakeHandStateInfo == nullptr){
        auto io_out_shake_hand_ret = nodeCfg.getPubChannel("io_out_shake_hand_ret");
        pWriterShakeHandStateInfo = pNode->CreateWriter<ShakeHandStateInfo>(io_out_shake_hand_ret.name);
       }
       if(pWriterACCShakeHandStateInfo == nullptr){
        auto io_out_acc_shake_hand_ret = nodeCfg.getPubChannel("io_out_acc_shake_hand_ret");
        pWriterACCShakeHandStateInfo = pNode->CreateWriter<ShakeHandStateInfo>(io_out_acc_shake_hand_ret.name);
       }
       if(pWriterVehicleBodyInfo == nullptr){
        auto vehicle_body_info = nodeCfg.getPubChannel("vehicle_body_info");
        pWriterVehicleBodyInfo = pNode->CreateWriter<VehicleBodyInfo>(vehicle_body_info.name);
       }
       if(pWriterChassisLatInfo == nullptr){
        auto io_out_chassisinfo_lat = nodeCfg.getPubChannel("io_out_chassisinfo_lat");
        pWriterChassisLatInfo = pNode->CreateWriter<ChassisLatInfo>(io_out_chassisinfo_lat.name);
       }
       if(pWriterChassisLongInfo == nullptr){
        auto io_out_chassisinfo_lon = nodeCfg.getPubChannel("io_out_chassisinfo_lon");
        pWriterChassisLongInfo = pNode->CreateWriter<ChassisLongInfo>(io_out_chassisinfo_lon.name);
       }
       if(pWriterWheelSpeedInfo == nullptr){
        auto io_wheelspeed = nodeCfg.getPubChannel("io_wheelspeed");
        pWriterWheelSpeedInfo = pNode->CreateWriter<WheelSpeedInfo>(io_wheelspeed.name);
       }
       if(pWriterChassisShiftGearInfo == nullptr){
        auto gear_event_info = nodeCfg.getPubChannel("io_out_chassis_gearshift");
        pWriterChassisShiftGearInfo = pNode->CreateWriter<ChassisShiftGearInfo>(gear_event_info.name);
       }
       if(pWriterVehicleSpeedInfo == nullptr){
        auto vehicle_speed_info = nodeCfg.getPubChannel("vehicle_body_info_speed");
        pWriterVehicleSpeedInfo = pNode->CreateWriter<VehicleSpeed>(vehicle_speed_info.name);
       }
       if(pWriterWashModeRearMirrorFbStInfo == nullptr){
        auto rearvmirrorfb_st_info = nodeCfg.getPubChannel("vehicle_body_info_rearvmirrorfb_st");
        apollo::cyber::proto::RoleAttributes writer_attr;
        writer_attr.set_channel_name(rearvmirrorfb_st_info.name);
        writer_attr.mutable_qos_profile()->CopyFrom(
          apollo::cyber::transport::QosProfileConf::QOS_PROFILE_PROPERTY_CHANNEL);
        pWriterWashModeRearMirrorFbStInfo = pNode->CreateWriter<WashModeRearMirrorFbSt>(writer_attr);
       }
       if(pWriterDoorSwStInfo == nullptr){
        auto doorswst_info = nodeCfg.getPubChannel("vehicle_body_info_doorswst");
        apollo::cyber::proto::RoleAttributes writer_attr;
        writer_attr.set_channel_name(doorswst_info.name);
        writer_attr.mutable_qos_profile()->CopyFrom(
          apollo::cyber::transport::QosProfileConf::QOS_PROFILE_PROPERTY_CHANNEL);
        pWriterDoorSwStInfo = pNode->CreateWriter<DoorSwSt>(writer_attr);
       }
       if(pWriterDriverBeltSwSigStInfo == nullptr){
        auto driverbelt_swsigst_info = nodeCfg.getPubChannel("vehicle_body_info_driverbelt_swsigst");
        apollo::cyber::proto::RoleAttributes writer_attr;
        writer_attr.set_channel_name(driverbelt_swsigst_info.name);
        writer_attr.mutable_qos_profile()->CopyFrom(
          apollo::cyber::transport::QosProfileConf::QOS_PROFILE_PROPERTY_CHANNEL);
        pWriterDriverBeltSwSigStInfo = pNode->CreateWriter<DriverBeltSwSigSt>(writer_attr);
       }
       if(pWriterDriveReadyStInfo == nullptr){
        auto drive_ready_st_info = nodeCfg.getPubChannel("vehicle_body_info_drive_ready_st");
        apollo::cyber::proto::RoleAttributes writer_attr;
        writer_attr.set_channel_name(drive_ready_st_info.name);
        writer_attr.mutable_qos_profile()->CopyFrom(
          apollo::cyber::transport::QosProfileConf::QOS_PROFILE_PROPERTY_CHANNEL);
        pWriterDriveReadyStInfo = pNode->CreateWriter<DriveReadySt>(writer_attr);
       }
       if(pWriterPotBackDoorPosInfo == nullptr){
        auto pot_backdoor_posst_info = nodeCfg.getPubChannel("vehicle_body_info_pot_backdoor_posst");
        apollo::cyber::proto::RoleAttributes writer_attr;
        writer_attr.set_channel_name(pot_backdoor_posst_info.name);
        writer_attr.mutable_qos_profile()->CopyFrom(
          apollo::cyber::transport::QosProfileConf::QOS_PROFILE_PROPERTY_CHANNEL);
        pWriterPotBackDoorPosInfo = pNode->CreateWriter<PotBackDoorPos>(writer_attr);
       }
       if(pWriterPedalTravelSensorInfo == nullptr){
        auto pedal_travel_sensor_info = nodeCfg.getPubChannel("vehicle_body_info_pedal_travel_sensor");
        pWriterPedalTravelSensorInfo = pNode->CreateWriter<PedalTravelSensor>(pedal_travel_sensor_info.name);
       }
       if(ExceptionHandle::Instance()->pExceptionCaptureInfo == nullptr){
        ExceptionHandle::Instance()->pExceptionCaptureInfo = pNode->CreateWriter<ExceptionPtr>("sync/channel/exception");
       }
       if(pWriterLocationGearInfo == nullptr){
        auto gear_info_for_location = nodeCfg.getPubChannel("gear_info_for_location");
        pWriterLocationGearInfo = pNode->CreateWriter<LocationGearInfo>(gear_info_for_location.name);
       }
       if(pWriterLocationStandstillInfo == nullptr){
        auto standstill_info_for_location = nodeCfg.getPubChannel("standstill_info_for_location");
        pWriterLocationStandstillInfo = pNode->CreateWriter<LocationStandstillInfo>(standstill_info_for_location.name);
       }
       if(pWriterLocationSteerAngleInfo == nullptr){
        auto steer_angle_info_for_location = nodeCfg.getPubChannel("steer_angle_info_for_location");
        pWriterLocationSteerAngleInfo = pNode->CreateWriter<LocationSteerAngleInfo>(steer_angle_info_for_location.name);
       }
       if(pWriterApaStatusAckInfo == nullptr){
        auto io_out_apa_status_ack = nodeCfg.getPubChannel("io_out_apa_status_ack");
        pWriterApaStatusAckInfo = pNode->CreateWriter<ApaStatusAck>(io_out_apa_status_ack.name);
       }
       // l2 Chassis
       if(pWriterChassis == nullptr){
        auto l2_chassis = nodeCfg.getPubChannel("l2_chassis");
        pWriterChassis = pNode->CreateWriter<Chassis>(l2_chassis.name);
       }
       //radar
       if(pWriterFareoRadar == nullptr){
        auto radar_detect = nodeCfg.getPubChannel("radar_detect");
        pWriterFareoRadar = pNode->CreateWriter<NewFareoRadarDetections>(radar_detect.name);
       }
       //radar points
       if(pWriterFareoRadarPointsFC == nullptr){
        auto radar_cloud_fc = nodeCfg.getPubChannel("radar_cloud_fc");
        pWriterFareoRadarPointsFC = pNode->CreateWriter<NewRadarPcloud>(radar_cloud_fc.name);
       }
       if(pWriterFareoRadarPointsFL == nullptr){
        auto radar_cloud_fl = nodeCfg.getPubChannel("radar_cloud_fl");
        pWriterFareoRadarPointsFL = pNode->CreateWriter<NewRadarPcloud>(radar_cloud_fl.name);
       }
       if(pWriterFareoRadarPointsFR == nullptr){
        auto radar_cloud_fr = nodeCfg.getPubChannel("radar_cloud_fr");
        pWriterFareoRadarPointsFR = pNode->CreateWriter<NewRadarPcloud>(radar_cloud_fr.name);
       }
       if(pWriterFareoRadarPointsRL == nullptr){
        auto radar_cloud_rl = nodeCfg.getPubChannel("radar_cloud_rl");
        pWriterFareoRadarPointsRL = pNode->CreateWriter<NewRadarPcloud>(radar_cloud_rl.name);
       }
       if(pWriterFareoRadarPointsRR == nullptr){
        auto radar_cloud_rr = nodeCfg.getPubChannel("radar_cloud_rr");
        pWriterFareoRadarPointsRR = pNode->CreateWriter<NewRadarPcloud>(radar_cloud_rr.name);
       }
    }

    void CyberMessenger::initCyberSub(){
        //input
        auto io_in_chassisctl = nodeCfg.getSubChannel("io_in_chassisctl");
        auto pReaderLatAndLongControlCmd = pNode->CreateReader<LatAndLongControlCmd>(io_in_chassisctl.name,
                                                                                std::bind(&CyberMessenger::LatAndLongControlCmdCallback,this,std::placeholders::_1));
        auto io_in_shake_hand_req = nodeCfg.getSubChannel("io_in_shake_hand_req");
        auto pReaderLatAndLongShakeHandCmd = pNode->CreateReader<LatAndLongShakeHandCmd>(io_in_shake_hand_req.name,
                                                                                std::bind(&CyberMessenger::LatAndLongShakeHandCmdCallback,this,std::placeholders::_1));
        auto app_rpa_resp = nodeCfg.getSubChannel("app_rpa_resp");
        auto pReaderAppRpaResp = pNode->CreateReader<AppRpaResp>(app_rpa_resp.name,
                                                                 std::bind(&CyberMessenger::AppRpaRespCallback,this,std::placeholders::_1));
        auto key_rpa_resp = nodeCfg.getSubChannel("key_rpa_resp");
        auto pReaderKeyRpaResp = pNode->CreateReader<KeyRpaResp>(key_rpa_resp.name,
                                                                 std::bind(&CyberMessenger::KeyRpaRespCallback,this,std::placeholders::_1));
        auto io_in_system_check_req = nodeCfg.getSubChannel("io_in_system_check_req");
        auto pReaderSystemCheckCmd = pNode->CreateReader<SystemCheckCmd>(io_in_system_check_req.name,
                                                                                std::bind(&CyberMessenger::SystemCheckCmdCallback,this,std::placeholders::_1));

        auto pReaderExceptionCmd = pNode->CreateReader<ExceptionPtr>("sync/channel/exception",
                                                                                std::bind(&CyberMessenger::ExceptionCmdCallback,this,std::placeholders::_1));
        
        auto decision_out_apa_status = nodeCfg.getSubChannel("decision_out_apa_status");
        auto pReaderApaStatus = pNode->CreateReader<ApaStatus>(decision_out_apa_status.name,
                                                                                std::bind(&CyberMessenger::ApaStatusCallback,this,std::placeholders::_1));
        auto io_in_trq_chassisctl = nodeCfg.getSubChannel("io_in_trq_chassisctl");
        auto pReaderLatAndLongTrqControlCmd = pNode->CreateReader<LatAndLongControlCmd>(io_in_trq_chassisctl.name,
                                                                                std::bind(&CyberMessenger::LatAndLongTrqControlCmdCallback,this,std::placeholders::_1));
        
        auto io_in_acc_shake_hand_req = nodeCfg.getSubChannel("io_in_acc_shake_hand_req");
        auto pReaderLatAndLongACCShakeHandCmd = pNode->CreateReader<LatAndLongShakeHandCmd>(io_in_acc_shake_hand_req.name,
                                                                                std::bind(&CyberMessenger::LatAndLongACCShakeHandCmdCallback,this,std::placeholders::_1));

        auto aeb_aebcontroller = nodeCfg.getSubChannel("aeb_aebcontroller");
        auto pReaderAEBController = pNode->CreateReader<AEBController>(aeb_aebcontroller.name,
                                                                                std::bind(&CyberMessenger::AEBControllerCallback,this,std::placeholders::_1));
    }


    void CyberMessenger::twoDividedFrequencyPub(){
        if(1 == io_module_pub_mode_){// l2
            ChassisPub(chassis_info_);
        }
        else{// apa
            if(2 == io_module_mode_){//eco_mode
                if(ecoModeFollowSpeedPub()){
                    PedalTravelSensorInfoPub(vehicle_body_info_);
                }
            }
            else{
                ChassisLatInfoPub(output_data_chassis_);
                ChassisLongInfoPub(output_data_chassis_);
                ChassisShiftGearInfoPub(gear_event_info_);
                PedalTravelSensorInfoPub(vehicle_body_info_);
            }
        }
    }

    void CyberMessenger::radarTargetPub(){
        if(1 == io_module_pub_mode_){// l2
            FareoRadarsPub(fareo_radars_);
        }
        else{
            FareoRadarsClear(fareo_radars_);
        }
    }

    void CyberMessenger::radarCloudPub(){
        if(1 == io_module_pub_mode_){// l2
            FareoRadarsPointsPub();
            FareoRadarPointsClear();
        }
        else{
            FareoRadarPointsClear();
        }
    }

    bool CyberMessenger::ecoModeFollowSpeedPub(){
        static bool is_pub = false;
        if(true == is_pub && 
            vehicle_body_info_.ibc_vehicle_speed > eco_no_pub_speed_threshold_){
            is_pub = false;
        }
        else if(false == is_pub && 
                    vehicle_body_info_.ibc_vehicle_speed < eco_pub_speed_threshold_){
            is_pub = true;
        }
        return is_pub;
    }

    void CyberMessenger::dataChangeToPub(){
        if(1 == io_module_pub_mode_){// l2
            // WheelSpeedInfoPub(output_data_wheel_speed_);
            ACCShakeHandStateInfoPub(output_data_shake_hand_);
            //for location measurement
            // LocationGearInfoPub(need_by_location_info_);
            // LocationStandstillInfoPub(need_by_location_info_);
            // LocationSteerAngleInfoPub(need_by_location_info_);
            ImuInfoChassisPub(output_data_imu_info_chassis_);
            ImuInfoPub(output_data_imu_info_);
            WheelSpeedInfoPub(output_data_wheel_speed_);
            UltrasonicInfoPub(ult_data_info_);
        }
        else{// apa
            if(2 == io_module_mode_){//eco_mode
                if(ecoModeFollowSpeedPub()){
                    ImuInfoPub(output_data_imu_info_);
                    AppRpaReqInfoPub(output_data_app_rpareq_info_);
                    KeyRpaReqInfoPub(output_data_key_rpareq_info_);
                    WheelSpeedInfoPub(output_data_wheel_speed_);
                    //vehicle body
                    VehicleSpeedInfoPub(vehicle_body_info_);
                    WashModeRearMirrorFbStInfoPub(vehicle_body_info_);
                    DoorSwStInfoPub(vehicle_body_info_);
                    DriverBeltSwSigStInfoPub(vehicle_body_info_);
                    DriveReadyStInfoPub(vehicle_body_info_);
                    PotBackDoorPosInfoPub(vehicle_body_info_);
                    //for location measurement
                    LocationGearInfoPub(need_by_location_info_);
                    LocationStandstillInfoPub(need_by_location_info_);
                    LocationSteerAngleInfoPub(need_by_location_info_);
                }
                else{
                    //vehicle body
                    VehicleSpeedInfoPub(vehicle_body_info_);
                }
            }
            else{
                ImuInfoChassisPub(output_data_imu_info_chassis_);
                ImuInfoPub(output_data_imu_info_);
                AppRpaReqInfoPub(output_data_app_rpareq_info_);
                KeyRpaReqInfoPub(output_data_key_rpareq_info_);
                WheelSpeedInfoPub(output_data_wheel_speed_);
                ShakeHandStateInfoPub(output_data_shake_hand_);
                UltrasonicInfoPub(ult_data_info_);
                //vehicle body
                VehicleSpeedInfoPub(vehicle_body_info_);
                WashModeRearMirrorFbStInfoPub(vehicle_body_info_);
                DoorSwStInfoPub(vehicle_body_info_);
                DriverBeltSwSigStInfoPub(vehicle_body_info_);
                DriveReadyStInfoPub(vehicle_body_info_);
                PotBackDoorPosInfoPub(vehicle_body_info_);
                //for location measurement
                LocationGearInfoPub(need_by_location_info_);
                LocationStandstillInfoPub(need_by_location_info_);
                LocationSteerAngleInfoPub(need_by_location_info_);
                //apa_status
                APAStatusAckInfoPub(apa_status_ack_info_);
            }
        }
            //vehicle body
            while (!output_data_imu_info_.soc_imu_info_queu.empty()){
                output_data_imu_info_.soc_imu_info_queu.pop();
            }
                while(!output_data_wheel_speed_.wheel_speed_info_queu.empty()){
                output_data_wheel_speed_.wheel_speed_info_queu.pop();
            }
            while (!output_data_imu_info_chassis_.imu_info_queu.empty()){
                output_data_imu_info_chassis_.imu_info_queu.pop();
            }
    }

    bool CyberMessenger::getRecordCyberInfoStr(std::string& str){
        str = std::move(cyber_str_);
        return true;
    }

    double CyberMessenger::getTimeS(struct timeval * pt)
    {
        double N = 1000.0;
        struct timeval tv;
        if(pt==nullptr)
            gettimeofday(&tv, NULL); // get current time
        else
            tv = *pt;
        double milliseconds = tv.tv_sec * N + tv.tv_usec / N; // calculate milliseconds
        return milliseconds / 1000;
    }

    void CyberMessenger::LatAndLongControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& lat_lon_control_cmd){
        StruInputLatAndLongControlCmd input_lat_lon_control_tmp;
        if(!lat_lon_control_cmd->has_data()){
            if(0 == io_module_mode_){
                AINFO<<__func__<<": not has data.";
            }
            return;
        }
        if(lat_lon_control_cmd->data().Is<SVControlData>()){
            SVControlData sv_control_data;
            lat_lon_control_cmd->data().UnpackTo(&sv_control_data);
            // input_lat_lon_control_tmp.is_driving = sv_control_data.is_driving();
            // input_lat_lon_control_tmp.is_braking = sv_control_data.is_braking();
            input_lat_lon_control_tmp.tar_velocity =  sv_control_data.tar_velocity()*3.6*100.0;//kph*100.0
            input_lat_lon_control_tmp.tar_distance = sv_control_data.tar_distance()*100.0;//cm
            input_lat_lon_control_tmp.apa_failure_brake_mode = sv_control_data.apa_failure_brake_mode();
            input_lat_lon_control_tmp.is_steering = sv_control_data.is_steering();
            input_lat_lon_control_tmp.tar_steer_angle = sv_control_data.tar_steer_angle()*10.0;//°*10.0
            input_lat_lon_control_tmp.put_gear = sv_control_data.put_gear();
            input_lat_lon_control_tmp.tar_gear = sv_control_data.tar_gear();
            input_lat_lon_control_tmp.tar_gear = commonGearToAPAGear(static_cast<uint32_t>(input_lat_lon_control_tmp.tar_gear));
        }
            input_lat_lon_control_tmp.is_update = true;

        PubVehCtrlReqUartrpcMsg(static_cast<uint8_t>(input_lat_lon_control_tmp.apa_failure_brake_mode),
                                                                static_cast<uint16_t>(input_lat_lon_control_tmp.tar_distance),
                                                                static_cast<uint16_t>(input_lat_lon_control_tmp.tar_velocity),
                                                                static_cast<uint8_t>(input_lat_lon_control_tmp.is_steering),
                                                                static_cast<int16_t>(input_lat_lon_control_tmp.tar_steer_angle),
                                                                static_cast<uint8_t>(input_lat_lon_control_tmp.put_gear),
                                                                static_cast<uint8_t>(input_lat_lon_control_tmp.tar_gear));
            if(0 == io_module_mode_){
                AINFO<<__func__
                        <<", apa_failure_brake_mode="<<static_cast<int>(input_lat_lon_control_tmp.apa_failure_brake_mode)
                        <<", tar_velocity="<<input_lat_lon_control_tmp.tar_velocity
                        <<", tar_distance="<<input_lat_lon_control_tmp.tar_distance
                        <<", is_steering="<<static_cast<int>(input_lat_lon_control_tmp.is_steering)
                        <<", tar_steer_angle="<<input_lat_lon_control_tmp.tar_steer_angle
                        <<", put_gear="<<static_cast<int>(input_lat_lon_control_tmp.put_gear)
                        <<", tar_gear="<<static_cast<int>(input_lat_lon_control_tmp.tar_gear)<<",";
            }
    }

    void CyberMessenger::LatAndLongTrqControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& lat_lon_control_cmd){
        StruInputLatAndLongTrqControlCmd input_lat_lon_control_tmp;
        if(!lat_lon_control_cmd->has_data()){
            if(0 == io_module_mode_){
                AINFO<<__func__<<": not has data.";
            }
            return;
        }
        if(lat_lon_control_cmd->data().Is<TorqueControlData>()){
            TorqueControlData trq_control_data;
            lat_lon_control_cmd->data().UnpackTo(&trq_control_data);
            input_lat_lon_control_tmp.is_driving = trq_control_data.is_driving();
            input_lat_lon_control_tmp.is_braking = trq_control_data.is_braking();
            input_lat_lon_control_tmp.tar_torque =  trq_control_data.tar_torque();
            input_lat_lon_control_tmp.tar_deceleration = trq_control_data.tar_deceleration()*100.0;// m/ss
            input_lat_lon_control_tmp.is_steering = trq_control_data.is_steering();
            input_lat_lon_control_tmp.tar_steer_angle = trq_control_data.tar_steer_angle()*10.0;//°*10.0
            input_lat_lon_control_tmp.put_gear = trq_control_data.put_gear();
            input_lat_lon_control_tmp.tar_gear = trq_control_data.tar_gear();
            input_lat_lon_control_tmp.tar_gear = commonGearToAPAGear(static_cast<uint32_t>(input_lat_lon_control_tmp.tar_gear));
            input_lat_lon_control_tmp.tar_acc = trq_control_data.tar_acc();
            input_lat_lon_control_tmp.lamp_ctl = trq_control_data.lamp_ctl();
        }
            input_lat_lon_control_tmp.is_update = true;

        PubTrqVehCtrlReqUartrpcMsg(input_lat_lon_control_tmp);
        if(0 == io_module_mode_){
            AINFO<<__func__
                    <<", is_driving="<<static_cast<int>(input_lat_lon_control_tmp.is_driving)
                    <<", is_braking="<<static_cast<int>(input_lat_lon_control_tmp.is_braking)
                    <<", tar_torque="<<input_lat_lon_control_tmp.tar_torque
                    <<", tar_deceleration="<<input_lat_lon_control_tmp.tar_deceleration
                    <<", is_steering="<<static_cast<int>(input_lat_lon_control_tmp.is_steering)
                    <<", tar_steer_angle="<<input_lat_lon_control_tmp.tar_steer_angle
                    <<", put_gear="<<static_cast<int>(input_lat_lon_control_tmp.put_gear)
                    <<", tar_gear="<<static_cast<int>(input_lat_lon_control_tmp.tar_gear)
                    <<", tar_acc="<<input_lat_lon_control_tmp.tar_acc
                    <<", lamp_ctl="<<static_cast<int>(input_lat_lon_control_tmp.lamp_ctl)<<",";
        }
    }

    void CyberMessenger::LatAndLongShakeHandCmdCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& shake_hand_cmd){
        static CommonBool::Enum activate_lat_last = CommonBool::NONE;
        static CommonBool::Enum activate_long_last = CommonBool::NONE;
        StruInputLatAndLongShakeHandCmd input_shake_hand_tmp;
        input_shake_hand_tmp.activate_lat = shake_hand_cmd->activate_lat();
        input_shake_hand_tmp.activate_long = shake_hand_cmd->activate_long();
        input_shake_hand_tmp.is_update = true;

        PubHandShakeReqUartrpcMsg(static_cast<uint8_t>(input_shake_hand_tmp.activate_lat), static_cast<uint8_t>(input_shake_hand_tmp.activate_long));
        if(0 == io_module_mode_){
            AINFO<<__func__<<", shake_hand_cmd: activate_lat="<<static_cast<int>(input_shake_hand_tmp.activate_lat)
                      <<",  activate_long="<<static_cast<int>(input_shake_hand_tmp.activate_long)<<",";
        }

    }

    void CyberMessenger::LatAndLongACCShakeHandCmdCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& shake_hand_cmd){
        StruInputLatAndLongShakeHandCmd input_shake_hand_tmp;
        input_shake_hand_tmp.activate_lat = shake_hand_cmd->activate_lat();
        input_shake_hand_tmp.activate_long = shake_hand_cmd->activate_long();
        input_shake_hand_tmp.is_update = true;
        PubACCHandShakeReqUartrpcMsg(static_cast<uint8_t>(input_shake_hand_tmp.activate_lat), static_cast<uint8_t>(input_shake_hand_tmp.activate_long));
        if(0 == io_module_mode_){
            AINFO<<__func__<<", acc_shake_hand_cmd: activate_lat="<<static_cast<int>(input_shake_hand_tmp.activate_lat)
                      <<",  activate_long="<<static_cast<int>(input_shake_hand_tmp.activate_long)<<",";
        }
    }

    void CyberMessenger::AEBControllerCallback(const std::shared_ptr <AEBController>& aeb_cmd){
        StruInputAEBController input_aeb_controller;
        input_aeb_controller.brake_prefill_status = aeb_cmd->brakeprefillstatus();
        input_aeb_controller.hba_level = aeb_cmd->hbalevel();
        input_aeb_controller.eba_status = aeb_cmd->ebastatus();
        input_aeb_controller.deceleration = aeb_cmd->deceleration();
        input_aeb_controller.ego_car_stop = aeb_cmd->egocarstop();
        input_aeb_controller.fcw_level = aeb_cmd->fcwlevel();
        PubAEBControllerUartrpcMsg(input_aeb_controller);
        if(0 == io_module_mode_){
            AINFO<<__func__<<", AEBControllerCallback: brake_prefill_status="<<static_cast<int>(input_aeb_controller.brake_prefill_status)
                      <<",  hba_level="<<static_cast<int>(input_aeb_controller.hba_level)
                      <<",  eba_status="<<static_cast<int>(input_aeb_controller.eba_status)
                      <<",  deceleration="<<static_cast<int>(input_aeb_controller.deceleration)
                      <<",  ego_car_stop="<<static_cast<int>(input_aeb_controller.ego_car_stop)
                      <<",  fcw_level="<<static_cast<int>(input_aeb_controller.fcw_level)<<",";
        }
    }

    void CyberMessenger::AppRpaRespCallback(const std::shared_ptr <AppRpaResp>& rpa_resp)
    {
        StruInputAppRpaResp rpa_resp_tmp;

        rpa_resp_tmp.mpa_ready_st = rpa_resp->mpa_ready_st();
        rpa_resp_tmp.rpa_app_disp_start_req = rpa_resp->rpa_app_disp_start_req();
        rpa_resp_tmp.remote_park_resp = rpa_resp->remote_park_resp();
        rpa_resp_tmp.mpa_park_notice = rpa_resp->mpa_park_notice();
        rpa_resp_tmp.exit_req = rpa_resp->exit_req();
        rpa_resp_tmp.parking_pode_st = rpa_resp->parking_pode_st();

        PubAppRpaRespUartrpcMsg(rpa_resp_tmp);
        if(0 == io_module_mode_){
            AINFO << __func__ <<
                 ", mpa_ready_st=" << std::to_string(rpa_resp_tmp.mpa_ready_st) <<
                 ", rpa_app_disp_start_req=" << std::to_string(rpa_resp_tmp.rpa_app_disp_start_req) <<
                 ", remote_park_resp=" << std::to_string(rpa_resp_tmp.remote_park_resp) <<
                 ", mpa_park_notice=" << std::to_string(rpa_resp_tmp.mpa_park_notice) <<
                 ", exit_req=" << std::to_string(rpa_resp_tmp.exit_req) <<
                 ", parking_pode_st=" << std::to_string(rpa_resp_tmp.parking_pode_st) <<
                 "\n";
        }
        
    }

    void CyberMessenger::KeyRpaRespCallback(const std::shared_ptr <KeyRpaResp>& rpa_resp)
    {
        StruInputKeyRpaResp rpa_resp_tmp;

        rpa_resp_tmp.rpa_app_disp_start_req = rpa_resp->rpa_app_disp_start_req();
        rpa_resp_tmp.mpa_ready_st = rpa_resp->mpa_ready_st();
        rpa_resp_tmp.parking_pode_st = rpa_resp->parking_pode_st();
        rpa_resp_tmp.exit_req = rpa_resp->exit_req();

        PubKeyRpaRespUartrpcMsg(rpa_resp_tmp);
        if(0 == io_module_mode_){
            AINFO << __func__ <<
                 ", rpa_app_disp_start_req=" << std::to_string(rpa_resp_tmp.rpa_app_disp_start_req) <<
                 ", mpa_ready_st=" << std::to_string(rpa_resp_tmp.mpa_ready_st) <<
                 ", parking_pode_st=" << std::to_string(rpa_resp_tmp.parking_pode_st) <<
                 ", exit_req=" << std::to_string(rpa_resp_tmp.exit_req) <<
                 "\n";
        }
        
    }

    void CyberMessenger::SystemCheckCmdCallback(const std::shared_ptr <SystemCheckCmd>& sys_chk_cmd){
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*sys_chk_cmd, &json_string);
        if(0 == io_module_mode_){
            AINFO<<__func__<<", sys_chk_cmd: "<<json_string.c_str();
        }

        StruInputSystemCheckCmd input_system_check;
        input_system_check.request_check = sys_chk_cmd->request_check();
        input_system_check.is_update = true;
         if(CommonBool::TRUE == input_system_check.request_check){
            SystemCheckResultInfoPub(output_data_system_check_);
        }
    }

    void CyberMessenger::ExceptionCmdCallback(const std::shared_ptr <ExceptionPtr>& exception_cmd){
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*exception_cmd, &json_string);
        if(ExceptionHandle::Instance()->IsMCUException(static_cast<uint32_t>(exception_cmd->code()))){
            if(0 == io_module_mode_){
                AINFO<<__func__<<", is mcu exception, exception_cmd: "<<json_string.c_str();
            }
            return;
        }
        if(0 == io_module_mode_){
            AINFO<<__func__<<",is not mcu exception exception_cmd: "<<json_string.c_str();
        }

        if(1 == ConfigData::Instance()->use_exception_function_){
            ExceptionHandle::Instance()->soc_exception_queue_->Enqueue(static_cast<uint32_t>(exception_cmd->code()));
        }
    }

    void CyberMessenger::ApaStatusCallback(const std::shared_ptr <ApaStatus>& apa_status_cmd){
        apa_status_t_ sig;
        static uint8_t index = 0;
        sig.index = index;
        sig.scenario_value = static_cast<uint8_t>(apa_status_cmd->scenario());
        sig.apa_status_value = static_cast<uint8_t>(apa_status_cmd->apa_status());
        if(++index > 255){
            index = 0;
        }
        PubAPAStatusMsg(sig);
        AINFO << __func__ << ": scenario=" << static_cast<uint32_t>(sig.scenario_value)
                        << ", apa_status=" << static_cast<uint32_t>(sig.apa_status_value);
    }

    void CyberMessenger::ImuInfoChassisPub(StruOutputDataImuInfoQueu& output_data_imu_info_que){
        if(0 == output_data_imu_info_que.is_update)return;
        cyber_str_ .append(", cb_chassis_imu_pub_size=").append(std::to_string(output_data_imu_info_que.imu_info_queu.size()));
        while(!output_data_imu_info_que.imu_info_queu.empty()){
            auto element = output_data_imu_info_que.imu_info_queu.front();
            Imu imu_cmd;
            static int64_t seq=0;
            auto header = imu_cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            header->set_module_name(nodeCfg.getName());
            header->set_sequence_num(seq++);
            header->set_version(0);
            imu_cmd.set_measurement_time(element.measurement_time);
            auto linear_acceleration = imu_cmd.mutable_linear_acceleration();
            linear_acceleration->set_x(element.linear_acceleration.x);
            linear_acceleration->set_y(element.linear_acceleration.y);
            linear_acceleration->set_z(element.linear_acceleration.z);
            auto angular_velocity = imu_cmd.mutable_angular_velocity();
            angular_velocity->set_x(element.angular_velocity.x);
            angular_velocity->set_y(element.angular_velocity.y);
            angular_velocity->set_z(element.angular_velocity.z);

            pWriterImuInfoChassis->Write(imu_cmd);
            output_data_imu_info_que.imu_info_queu.pop();
        }
        cyber_str_.append(", cb_chassis_imu_pub=1");
        output_data_imu_info_que.is_update = 0;
    }

    void CyberMessenger::ImuInfoPub(StruOutputDataSOCImuInfoQueu& output_data_imu_info){
        if(0 == output_data_imu_info.is_update)return;
        cyber_str_.append(", cb_soc_imu_pub_size=").append( 
                                std::to_string(output_data_imu_info_.soc_imu_info_queu.size()));
        while (!output_data_imu_info_.soc_imu_info_queu.empty()){
            auto element = output_data_imu_info_.soc_imu_info_queu.front();
            Imu imu_cmd;
            static int64_t seq=0;
            auto header = imu_cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            header->set_module_name(nodeCfg.getName());
            header->set_sequence_num(seq++);
            header->set_version(0);
            imu_cmd.set_measurement_time(element.measurement_time);
            auto linear_acceleration = imu_cmd.mutable_linear_acceleration();
            linear_acceleration->set_x(element.linear_acceleration.x);
            linear_acceleration->set_y(element.linear_acceleration.y);
            linear_acceleration->set_z(element.linear_acceleration.z);
            auto angular_velocity = imu_cmd.mutable_angular_velocity();
            angular_velocity->set_x(element.angular_velocity.x);
            angular_velocity->set_y(element.angular_velocity.y);
            angular_velocity->set_z(element.angular_velocity.z);

            pWriterImuInfo->Write(imu_cmd);
            output_data_imu_info_.soc_imu_info_queu.pop();
        }

        cyber_str_.append(", cb_raw_imu_pub=1");
        output_data_imu_info.is_update = 0;
    }

    void CyberMessenger::AppRpaReqInfoPub(StruAppRpaReqInfo& rpa_req_info){
        if(0 == rpa_req_info.is_update)return;
        AppRpaReq appRpaReq;
        static int64_t seq = 0;
        auto header = appRpaReq.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        appRpaReq.set_measurement_time(rpa_req_info.msg_SGW_C_3F0.measurement_time);
        appRpaReq.set_rpa_sw_req(rpa_req_info.msg_SGW_C_3F0.DKM_BLEAPASwReq);
        appRpaReq.set_rpa_park_req(rpa_req_info.msg_SGW_C_3F0.DKM_BLEAPPRPAParkReq);
        appRpaReq.set_rpa_mode_select_req(rpa_req_info.msg_SGW_C_3F0.DKM_BLEAPAModeSelectReq);
        appRpaReq.set_rpa_move_req(rpa_req_info.msg_SGW_C_3F0.DKM_BLEAPPAPAMoveReq);
        appRpaReq.set_rpa_ble_connect_st(rpa_req_info.msg_SGW_C_3F0.DKM_BLEConnectSt);
        
        pWriterAppRpaReqInfo->Write(appRpaReq);
        rpa_req_info.is_update = 0;
    }

    void CyberMessenger::KeyRpaReqInfoPub(StruKeyRpaReqInfo& rpa_req_info){
        if(0 == rpa_req_info.is_update)return;
        KeyRpaReq keyRpaReq;
        static int64_t seq = 0;
        auto header = keyRpaReq.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        keyRpaReq.set_measurement_time(rpa_req_info.msg_SGW_C_346.measurement_time);
        keyRpaReq.set_rke_command(rpa_req_info.msg_SGW_C_346.VIUL_PEPS_RKECommand2);
        keyRpaReq.set_engine_start_mode(rpa_req_info.msg_SGW_C_346.VIUL_PEPS_EngineStartMode);
        
        pWriterKeyRpaReqInfo->Write(keyRpaReq);
        rpa_req_info.is_update = 0;
    }

    void CyberMessenger::UltrasonicInfoPub(StruUltProbeInfo& ult_probe_info){
        if(0 == ult_probe_info.is_update)return;
        PiUssApp2DataInfo ultrasonic_cmd;
        static int64_t seq=0;
        auto header = ultrasonic_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        auto uss_data_info_de = ultrasonic_cmd.mutable_uss_datainfo()->mutable_ussdistanceinfo()->mutable_de();

        double node[12];

        node[0] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.FLS;
        node[1] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.FL;
        node[2] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.FML;
        node[3] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.FMR;
        node[4] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.FR;
        node[5] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.FRS;
        node[6] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.RLS;
        node[7] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.RL;
        node[8] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.RML;
        node[9] = ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.RMR;
        node[10] =ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.RR;
        node[11] =ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.DE.RRS;

        std::string new_io_uss_node_log_ = "new_io_uss_node_ ";
        for (int i=0; i < 12; ++i) {
            new_io_uss_node_log_ += " " +  std::to_string(node[i]);
            uls_filter_[i].SetFilterPara(filter_window_size_, filter_pickup_index_);
            if (node[i] < 180) {
                node[i] = 10000;
            }
            node[i] = uls_filter_[i].Run(node[i]);
        }
        if(0 == io_module_mode_){
            AINFO << new_io_uss_node_log_;
        }

        uss_data_info_de->set_fls(node[0]);
        uss_data_info_de->set_fl(node[1]);
        uss_data_info_de->set_fml(node[2]);
        uss_data_info_de->set_fmr(node[3]);
        uss_data_info_de->set_fr(node[4]);
        uss_data_info_de->set_frs(node[5]);

        uss_data_info_de->set_rls(node[6]);
        uss_data_info_de->set_rl(node[7]);
        uss_data_info_de->set_rml(node[8]);
        uss_data_info_de->set_rmr(node[9]);
        uss_data_info_de->set_rr(node[10]);
        uss_data_info_de->set_rrs(node[11]);

        auto uss_data_info_pdc = ultrasonic_cmd.mutable_uss_datainfo()->mutable_ussdistanceinfo()->mutable_pdc();
        uss_data_info_pdc->set_fmr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.FMR);
        uss_data_info_pdc->set_fml(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.FML);
        uss_data_info_pdc->set_fls(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.FLS);
        uss_data_info_pdc->set_fl(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.FL);
        uss_data_info_pdc->set_rls(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.RLS);
        uss_data_info_pdc->set_rl(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.RL);
        uss_data_info_pdc->set_frs(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.FRS);
        uss_data_info_pdc->set_fr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.FR);
        uss_data_info_pdc->set_rrs(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.RRS);
        uss_data_info_pdc->set_rr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.RR);
        uss_data_info_pdc->set_rmr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.RMR);
        uss_data_info_pdc->set_rml(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.PDC.RML);
        auto uss_data_info_timestamp = ultrasonic_cmd.mutable_uss_datainfo()->mutable_ussdistanceinfo()->mutable_timestamp();
        uss_data_info_timestamp->set_fmr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.FMR);
        uss_data_info_timestamp->set_fml(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.FML);
        uss_data_info_timestamp->set_fls(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.FLS);
        uss_data_info_timestamp->set_fl(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.FL);
        uss_data_info_timestamp->set_rls(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.RLS);
        uss_data_info_timestamp->set_rl(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.RL);
        uss_data_info_timestamp->set_frs(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.FRS);
        uss_data_info_timestamp->set_fr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.FR);
        uss_data_info_timestamp->set_rrs(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.RRS);
        uss_data_info_timestamp->set_rr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.RR);
        uss_data_info_timestamp->set_rmr(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.RMR);
        uss_data_info_timestamp->set_rml(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssDistanceInfo.TimeStamp.RML);

        ultrasonic_cmd.mutable_uss_datainfo()->mutable_ussslotsinfo()->Reserve(USS_Slot_SIZE);
        for(int i=0; i<USS_Slot_SIZE; i++){
            auto uss_data_info_ussslotinfo = ultrasonic_cmd.mutable_uss_datainfo()->add_ussslotsinfo();
            uss_data_info_ussslotinfo->set_ax(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Ax);
            uss_data_info_ussslotinfo->set_ay(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Ay);
            uss_data_info_ussslotinfo->set_bx(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Bx);
            uss_data_info_ussslotinfo->set_by(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].By);
            uss_data_info_ussslotinfo->set_cx(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Cx);
            uss_data_info_ussslotinfo->set_cy(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Cy);
            uss_data_info_ussslotinfo->set_dx(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Dx);
            uss_data_info_ussslotinfo->set_dy(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Dy);
            uss_data_info_ussslotinfo->set_ex(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Ex);
            uss_data_info_ussslotinfo->set_ey(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Ey);
            uss_data_info_ussslotinfo->set_fx(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Fx);
            uss_data_info_ussslotinfo->set_fy(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Fy);
            uss_data_info_ussslotinfo->set_depth(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Depth);
            uss_data_info_ussslotinfo->set_curbdis(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].CurbDis);
            uss_data_info_ussslotinfo->set_slottype(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].SlotType);
            uss_data_info_ussslotinfo->set_slotid(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].SlotID);
            uss_data_info_ussslotinfo->set_length_width(ult_probe_info.uss_data_info.ussData.USS_DataInfo.UssSlotInfo.UssSlot[i].Length_Width);
        }

        auto uss_data_info_emapinfo = ultrasonic_cmd.mutable_uss_emapinfo();
        uss_data_info_emapinfo->set_slotupdateptb_flag(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SlotUpdatePtB_Flag);
        uss_data_info_emapinfo->set_slotupdate_bx(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SlotUpdate_Bx);
        uss_data_info_emapinfo->set_slotupdate_by(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SlotUpdate_By);
        uss_data_info_emapinfo->set_slotupdateptc_flag(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SlotUpdatePtC_Flag);
        uss_data_info_emapinfo->set_slotupdate_cx(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SlotUpdate_Cx);
        uss_data_info_emapinfo->set_slotupdate_cy(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SlotUpdate_Cy);
        // uss_data_info_emapinfo->set_ultra_left_point_num(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Left_Point_Num);
        // uss_data_info_emapinfo->set_ultra_right_point_num(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Right_Point_Num);
        // for(int i=0;i<USSLane_MAX_SIZE;i++){
        //     uss_data_info_emapinfo->add_ultra_left_point1_x(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Left_Point1_x[i]);
        //     uss_data_info_emapinfo->add_ultra_left_point1_y(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Left_Point1_y[i]);
        //     uss_data_info_emapinfo->add_ultra_left_point2_x(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Left_Point2_x[i]);
        //     uss_data_info_emapinfo->add_ultra_left_point2_y(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Left_Point2_y[i]);
        //     uss_data_info_emapinfo->add_ultra_right_point1_x(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Right_Point1_x[i]);
        //     uss_data_info_emapinfo->add_ultra_right_point1_y(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Right_Point1_y[i]);
        //     uss_data_info_emapinfo->add_ultra_right_point2_x(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Right_Point2_x[i]);
        //     uss_data_info_emapinfo->add_ultra_right_point2_y(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.Ultra_Right_Point2_y[i]);
        // }
        uss_data_info_emapinfo->mutable_pdc_pose_x()->Reserve(PDC_Pose_SIZE);
        for(int i=0;i<PDC_Pose_SIZE;i++){
            uss_data_info_emapinfo->add_pdc_pose_x(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.PDC_Pose_x[i]);
            uss_data_info_emapinfo->add_pdc_pose_y(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.PDC_Pose_y[i]);
        }
        uss_data_info_emapinfo->mutable_sdg_pose_x()->Reserve(SDG_Pose_SIZE);
        for(int i=0;i<SDG_Pose_SIZE;i++){
            uss_data_info_emapinfo->add_sdg_pose_x(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SDG_Pose_x[i]);
            uss_data_info_emapinfo->add_sdg_pose_y(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.SDG_Pose_y[i]);
        }
        uss_data_info_emapinfo->mutable_objtype()->Reserve(USS_INDEX);
        for(int i=0;i<USS_INDEX;i++){
            uss_data_info_emapinfo->add_objtype(ult_probe_info.uss_data_info.ussData.USS_EMapInfo.ObjType[i]);
        }

        auto uss_data_info_jan = ultrasonic_cmd.mutable_uss_jan();
        uss_data_info_jan->set_uss_frontjam(ult_probe_info.uss_data_info.ussData.USS_Jan.USS_FrontJam);
        uss_data_info_jan->set_uss_rearjam(ult_probe_info.uss_data_info.ussData.USS_Jan.USS_RearJam);

        ultrasonic_cmd.set_measurement_time(ult_probe_info.uss_measurement_time);
        pWriterUltrasonicInfo->Write(ultrasonic_cmd);
        // std::string json_string;
        // google::protobuf::util::MessageToJsonString(ultrasonic_cmd, &json_string);
        // AINFO<<__func__<<": "<<json_string.c_str();
        ult_probe_info.is_update = 0;
    }

    void CyberMessenger::VehicleBodyInfoPub(){
        VehicleBodyInfo vb_cmd;
        pWriterVehicleBodyInfo->Write(vb_cmd);
    }

    void CyberMessenger::ShakeHandStateInfoPub(StruOutputDataShakeHand& output_data_shake_hand){
        if(0 == output_data_shake_hand.is_update)return;
        ShakeHandStateInfo sh_cmd;
        static int64_t seq=0;
        auto header = sh_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        sh_cmd.set_lat_active(output_data_shake_hand.lat_active);
        sh_cmd.set_lat_err_type(output_data_shake_hand.lat_shakehand_err_type);
        sh_cmd.set_lat_err_reason(output_data_shake_hand.lat_shakehand_err_reason);
        sh_cmd.set_long_active(output_data_shake_hand.long_active);
        sh_cmd.set_long_err_type(output_data_shake_hand.long_shakehand_err_type);
        sh_cmd.set_long_err_reason(output_data_shake_hand.long_shakehand_err_reason);
        pWriterShakeHandStateInfo->Write(sh_cmd);
        output_data_shake_hand.is_update = 0;
    }

    void CyberMessenger::ACCShakeHandStateInfoPub(StruOutputDataShakeHand& output_data_shake_hand){
        if(0 == output_data_shake_hand.is_update)return;
        ShakeHandStateInfo sh_cmd;
        static int64_t seq=0;
        auto header = sh_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        sh_cmd.set_lat_active(output_data_shake_hand.lat_active);
        sh_cmd.set_lat_err_type(output_data_shake_hand.lat_shakehand_err_type);
        sh_cmd.set_lat_err_reason(output_data_shake_hand.lat_shakehand_err_reason);
        sh_cmd.set_long_active(output_data_shake_hand.long_active);
        sh_cmd.set_long_err_type(output_data_shake_hand.long_shakehand_err_type);
        sh_cmd.set_long_err_reason(output_data_shake_hand.long_shakehand_err_reason);
        pWriterACCShakeHandStateInfo->Write(sh_cmd);
        output_data_shake_hand.is_update = 0;
    }

    void CyberMessenger::SystemCheckResultInfoPub(const StruOutputDataSystemCheck& output_data_system_check){
        SystemCheckResultInfo sc_cmd;
        static int64_t seq=0;
        auto header = sc_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        sc_cmd.set_is_check_ok(output_data_system_check.is_check_ok);
        sc_cmd.set_check_err_reason(output_data_system_check.check_err_reason);
        pWriterSystemCheckResultInfo->Write(sc_cmd);
    }

    void CyberMessenger::ChassisLatInfoPub(const StruOutputDataChassis& output_data_chassis){
        ChassisLatInfo lat_cmd;
        static int64_t seq=0;
        auto header = lat_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        auto data = lat_cmd.mutable_data();
        data->set_steering_angle(output_data_chassis.LatDa_steering_angle);
        data->set_steering_velocity(output_data_chassis.LatDa_steering_velocity);
        data->set_steering_torque(output_data_chassis.LatDa_steering_torque);

        auto state = lat_cmd.mutable_state();
        state->set_eps_valid(output_data_chassis.LatSt_eps_valid);
        state->set_eps_err_type(output_data_chassis.LatSt_eps_err_type);
        state->set_eps_err_reason(output_data_chassis.LatSt_eps_err_reason);
        pWriterChassisLatInfo->Write(lat_cmd);
        
    }

    void CyberMessenger::ChassisLongInfoPub(const StruOutputDataChassis& output_data_chassis){
        ChassisLongInfo lon_cmd;
        static int64_t seq=0;
        auto header = lon_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        auto data = lon_cmd.mutable_data();
        data->set_stand_still(output_data_chassis.LonDa_stand_still);
        data->set_esp_brakeforce(output_data_chassis.epb_sys_st);
        data->set_speed(output_data_chassis.LonDa_speed);
        data->set_lon_acceleration(output_data_chassis.LonDa_lon_acceleration);
        data->set_cur_torque(output_data_chassis.LonDa_cur_torque);
        data->set_min_torque(output_data_chassis.LonDa_min_torque);
        data->set_max_torque(output_data_chassis.LonDa_max_torque);
        data->set_gear_pos((CommonGearPosition::Enum)PDCUGearToCommonGear(output_data_chassis.LonDa_gear_pos));
        data->set_ibcu_pressure(output_data_chassis.LonDa_ibcu_pressure);
        data->set_slope(output_data_chassis.LonDa_slope);
        data->set_sv_result(output_data_chassis.LonDa_sv_result);
        data->set_brake_torque(output_data_chassis.LonDa_brake_torque);
        data->set_ack_distance(output_data_chassis.LonDa_ack_distance);
        data->set_ack_velocity(output_data_chassis.LonDa_ack_velocity);
        data->set_pdcu_accel_peadal_valid(output_data_chassis.LonDa_pdcu_accel_peadal_valid);
        data->set_pdcu_accel_peadal_st(output_data_chassis.LonDa_pdcu_accel_peadal_st);

        auto state = lon_cmd.mutable_state();
        state->set_esp_valid(output_data_chassis.LonSt_esp_valid);
        state->set_esp_err_type(output_data_chassis.LonSt_esp_err_type);
        state->set_esp_err_reason(output_data_chassis.LonSt_esp_err_reason);
        state->set_epb_valid(output_data_chassis.LonSt_epb_valid);
        state->set_epb_err_type(output_data_chassis.LonSt_epb_err_type);
        state->set_epb_err_reason(output_data_chassis.LonSt_epb_err_reason);
        state->set_vcu_valid(output_data_chassis.LonSt_vcu_valid);
        state->set_vcu_err_type(output_data_chassis.LonSt_vcu_err_type);
        state->set_vcu_err_reason(output_data_chassis.LonSt_vcu_err_reason);
        state->set_ibc_lsm_ctrl_fault_sts(output_data_chassis.LonSt_ibc_lsm_ctrl_fault_sts);

        pWriterChassisLongInfo->Write(lon_cmd);
    }

    void CyberMessenger::WheelSpeedInfoPub(StruOutputDataWheelSpeedInfoQueu& output_data_wheel_speed_que){
        if(0 == output_data_wheel_speed_que.is_update)return;
        cyber_str_.append(", cb_chassis_whispd_pub_size=").append(std::to_string(output_data_wheel_speed_que.wheel_speed_info_queu.size()));
        while(!output_data_wheel_speed_que.wheel_speed_info_queu.empty()){
            auto element = output_data_wheel_speed_que.wheel_speed_info_queu.front();
            WheelSpeedInfo speed_cmd;
            static int64_t seq=0;
            auto header = speed_cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            header->set_module_name(nodeCfg.getName());
            header->set_sequence_num(seq++);
            header->set_version(0);

            speed_cmd.set_valid(element.valid);
            speed_cmd.set_wheelspeed_fl(element.wheelspeed_fl);
            speed_cmd.set_wheelspeed_fr(element.wheelspeed_fr);
            speed_cmd.set_wheelspeed_rl(element.wheelspeed_rl);
            speed_cmd.set_wheelspeed_rr(element.wheelspeed_rr);

            speed_cmd.set_wheelsign_fl(element.wheelsign_fl);
            speed_cmd.set_wheelsign_fr(element.wheelsign_fr);
            speed_cmd.set_wheelsign_rl(element.wheelsign_rl);
            speed_cmd.set_wheelsign_rr(element.wheelsign_rr);
            speed_cmd.set_wheel_edgessum_fl(element.wheel_edgessum_fl);
            speed_cmd.set_wheel_edgessum_fr(element.wheel_edgessum_fr);
            speed_cmd.set_wheel_edgessum_rl(element.wheel_edgessum_rl);
            speed_cmd.set_wheel_edgessum_rr(element.wheel_edgessum_rr);
            speed_cmd.set_measurement_time(element.measurement_time);

            pWriterWheelSpeedInfo->Write(speed_cmd);
            output_data_wheel_speed_que.wheel_speed_info_queu.pop();
        }

        cyber_str_.append(", cb_chassis_wlspd_pub=1");
        output_data_wheel_speed_que.is_update = 0;
    }

    void CyberMessenger::ChassisShiftGearInfoPub(const StruChassisShiftGearInfo& gear_event_info){
        // if(true == gear_event_info_.gear_trans_valid){
            ChassisShiftGearInfo ge_cmd;
            static int64_t seq=0;
            auto header = ge_cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            header->set_module_name(nodeCfg.getName());
            header->set_sequence_num(seq++);
            header->set_version(0);
            CommonBool::Enum tmp_enum = CommonBool::NONE;
            if(true == gear_event_info.gear_trans_valid){
                tmp_enum = CommonBool::TRUE;
            }
            if(false == gear_event_info.gear_trans_valid){
                tmp_enum = CommonBool::FALSE;
            }
            ge_cmd.set_gear_shifted(tmp_enum);
            pWriterChassisShiftGearInfo->Write(ge_cmd);
        // }
    }

    void CyberMessenger::APAStatusAckInfoPub(StruAPAStatusAckInfo& apa_status_ack_info){
        if(0 == apa_status_ack_info.is_update)return;

        ApaStatusAck cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        cmd.set_scenario((FApaScenario::Enum)(apa_status_ack_info.scenario));
        cmd.set_apa_status((ApaStatusAck_Status)(apa_status_ack_info.apa_status));
        pWriterApaStatusAckInfo->Write(cmd);

        apa_status_ack_info.is_update = 0;
    }

    void CyberMessenger::ChassisPub(StruChassis& chassis){

        Chassis cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        cmd.set_driving_mode((DrivingMode)(chassis.driving_mode));
        cmd.set_gear_locationb((CommonGearPosition::Enum)PDCUGearToCommonGear(chassis.gear_locationb));

        cmd.mutable_vehicle_signal()->set_turn_signal((TurnSignal)(chassis.vehicle_signal.turn_signal));
        cmd.mutable_vehicle_signal()->set_high_beam(chassis.vehicle_signal.high_beam);
        cmd.mutable_vehicle_signal()->set_low_beam(chassis.vehicle_signal.low_beam);
        cmd.mutable_vehicle_signal()->set_horn(chassis.vehicle_signal.horn);
        cmd.mutable_vehicle_signal()->set_emergency_light(chassis.vehicle_signal.emergency_light);
        cmd.mutable_vehicle_signal()->set_standstill_st(chassis.vehicle_signal.standstill_st);
        cmd.mutable_vehicle_signal()->set_epb_st(chassis.vehicle_signal.epb_st);
        cmd.mutable_vehicle_signal()->set_lf_doorsw_st(chassis.vehicle_signal.lf_doorsw_st);
        cmd.mutable_vehicle_signal()->set_rf_doorsw_st(chassis.vehicle_signal.rf_doorsw_st);
        cmd.mutable_vehicle_signal()->set_rr_doorsw_st(chassis.vehicle_signal.rr_doorsw_st);
        cmd.mutable_vehicle_signal()->set_lr_doorsw_st(chassis.vehicle_signal.lr_doorsw_st);
        cmd.mutable_vehicle_signal()->set_pot_backdoor_posst(chassis.vehicle_signal.pot_backdoor_posst);
        cmd.mutable_vehicle_signal()->set_pot_enginehood_posst(chassis.vehicle_signal.pot_enginehood_posst);
        cmd.mutable_vehicle_signal()->set_driverbelt_swsig_st(chassis.vehicle_signal.driverbelt_swsig_st);
        cmd.mutable_vehicle_signal()->set_passenger_belt_st(chassis.vehicle_signal.passenger_belt_st);
        cmd.mutable_vehicle_signal()->set_rl_belt_st(chassis.vehicle_signal.rl_belt_st);
        cmd.mutable_vehicle_signal()->set_rr_belt_st(chassis.vehicle_signal.rr_belt_st);
        cmd.mutable_vehicle_signal()->set_drive_ready_st(chassis.vehicle_signal.drive_ready_st);

        cmd.mutable_vehicle_speed()->set_vehicle_speed(chassis.vehicle_speed.vehicle_speed);
        cmd.mutable_vehicle_speed()->set_wheel_speed_fl(chassis.vehicle_speed.wheel_speed_fl);
        cmd.mutable_vehicle_speed()->set_wheel_speed_fr(chassis.vehicle_speed.wheel_speed_fr);
        cmd.mutable_vehicle_speed()->set_wheel_speed_rl(chassis.vehicle_speed.wheel_speed_rl);
        cmd.mutable_vehicle_speed()->set_wheel_speed_rr(chassis.vehicle_speed.wheel_speed_rr);
        cmd.mutable_vehicle_speed()->set_instrument_vehicle_speed(chassis.vehicle_speed.instrument_vehicle_speed);

        cmd.set_maximum_user_speed(chassis.maximum_user_speed);
        cmd.set_throttle_perceptage(chassis.throttle_perceptage);
        cmd.set_brake_percentage(chassis.brake_percentage);

        cmd.mutable_acceleration()->set_longitudinal(chassis.acceleration.longitudinal);
        cmd.mutable_acceleration()->set_lateral(chassis.acceleration.lateral);

        cmd.set_yaw_rate(chassis.yaw_rate);
        cmd.mutable_steering_system()->set_steering_wheel_angle(chassis.steering_system.steering_wheel_angle);
        cmd.mutable_steering_system()->set_steering_wheel_rate(chassis.steering_system.steering_wheel_rate);
        cmd.mutable_steering_system()->set_steering_wheel_torque(chassis.steering_system.steering_wheel_torque);
        cmd.mutable_steering_system()->set_steering_angle(chassis.steering_system.steering_angle);

        cmd.set_time_gap(chassis.time_gap);
        cmd.set_cur_torque(chassis.cur_torque);
        cmd.set_slope_estimation(chassis.slope_estimation);
        cmd.set_mass_estimation(chassis.mass_estimation);

        pWriterChassis->Write(cmd);
    }

    void CyberMessenger::VehicleSpeedInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        if(0 == vehicle_body_info.speed_is_update)return;

        VehicleSpeed cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_vehicle_speed(vehicle_body_info.ibc_vehicle_speed);
        pWriterVehicleSpeedInfo->Write(cmd);

        vehicle_body_info.speed_is_update = 0;
    }

    void CyberMessenger::WashModeRearMirrorFbStInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        if(0 == vehicle_body_info.swgi_33d_is_update)return;

        WashModeRearMirrorFbSt cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_rearmirrorfb_st(vehicle_body_info.viul_rearvmirrorfb_st);
        pWriterWashModeRearMirrorFbStInfo->Write(cmd);

        std::string json_string;
        google::protobuf::util::MessageToJsonString(cmd, &json_string);
        // if(0 == io_module_mode_)
        {
            AINFO<<__func__<<", vehicle_body_info: "<<json_string.c_str();
        }

        vehicle_body_info.swgi_33d_is_update = 0;
    }

    void CyberMessenger::DoorSwStInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        if(0 == vehicle_body_info.swgc_23a_is_update)return;

        DoorSwSt cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_lf_doorsw_st(vehicle_body_info.viul_lfdoorswst);
        cmd.set_rf_doorsw_st(vehicle_body_info.viul_rfdoorswst);
        cmd.set_rr_doorsw_st(vehicle_body_info.viul_rrdoorswst);
        cmd.set_lr_doorsw_st(vehicle_body_info.viul_lrdoorswst);
        pWriterDoorSwStInfo->Write(cmd);

        std::string json_string;
        google::protobuf::util::MessageToJsonString(cmd, &json_string);
        // if(0 == io_module_mode_)
        {
            AINFO<<__func__<<", vehicle_body_info: "<<json_string.c_str();
        }
        
        vehicle_body_info.swgc_23a_is_update = 0;
    }

    void CyberMessenger::DriverBeltSwSigStInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        if(0 == vehicle_body_info.swgi_30c_is_update)return;

        DriverBeltSwSigSt cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_driverbelt_swsig_st(vehicle_body_info.acu_driverbelt_swsigst);
        pWriterDriverBeltSwSigStInfo->Write(cmd);

        std::string json_string;
        google::protobuf::util::MessageToJsonString(cmd, &json_string);
        // if(0 == io_module_mode_)
        {
            AINFO<<__func__<<", vehicle_body_info: "<<json_string.c_str();
        }
        
        vehicle_body_info.swgi_30c_is_update = 0;
    }

    void CyberMessenger::DriveReadyStInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        if(0 == vehicle_body_info.pdcu_fe_is_update)return;

        DriveReadySt cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_drive_ready_st(vehicle_body_info.pdcu_drive_ready_st);
        pWriterDriveReadyStInfo->Write(cmd);

        std::string json_string;
        google::protobuf::util::MessageToJsonString(cmd, &json_string);
        // if(0 == io_module_mode_)
        {
            AINFO<<__func__<<", vehicle_body_info: "<<json_string.c_str();
        }
        
        vehicle_body_info.pdcu_fe_is_update = 0;
    }

    void CyberMessenger::PotBackDoorPosInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        if(0 == vehicle_body_info.swgi_304_is_update)return;

        PotBackDoorPos cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_pot_backdoor_posst(vehicle_body_info.pot_backdoor_posst);
        pWriterPotBackDoorPosInfo->Write(cmd);

        std::string json_string;
        google::protobuf::util::MessageToJsonString(cmd, &json_string);
        // if(0 == io_module_mode_)
        {
            AINFO<<__func__<<", vehicle_body_info: "<<json_string.c_str();
        }
        
        vehicle_body_info.swgi_304_is_update = 0;
    }

    void CyberMessenger::PedalTravelSensorInfoPub(StruVehicleBodyInfo& vehicle_body_info){
        PedalTravelSensor cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_pedal_travel_sensor(vehicle_body_info.pedal_travel_sensor);
        cmd.set_slope(vehicle_body_info.slope);
        pWriterPedalTravelSensorInfo->Write(cmd);
    }

    //for location measurement
    void CyberMessenger::LocationGearInfoPub(StruInfoByLocation& need_by_location_info){
        if(0 == need_by_location_info.gear_is_update)return;
        LocationGearInfo cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_gear_info((CommonGearPosition::Enum)PDCUGearToCommonGear(need_by_location_info.cur_gear));
        cmd.set_measurement_time(need_by_location_info.pdcu_1c8_measurement_time);
        pWriterLocationGearInfo->Write(cmd);
        need_by_location_info.gear_is_update = 0;
    }

    void CyberMessenger::LocationStandstillInfoPub(StruInfoByLocation& need_by_location_info){
        if(0 == need_by_location_info.standstill_is_update)return;
        LocationStandstillInfo cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_standstill_info(need_by_location_info.standstill_st);
        cmd.set_measurement_time(need_by_location_info.ibc_a2_measurement_time);
        pWriterLocationStandstillInfo->Write(cmd);
        need_by_location_info.standstill_is_update = 0;
    }

    void CyberMessenger::LocationSteerAngleInfoPub(StruInfoByLocation& need_by_location_info){
        if(0 == need_by_location_info.steer_angle_is_update)return;
        LocationSteerAngleInfo cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_steer_angle_info(need_by_location_info.sas_steering_angle);
        cmd.set_measurement_time(need_by_location_info.sas_a5_measurement_time);
        pWriterLocationSteerAngleInfo->Write(cmd);
        // cyber_str_ = cyber_str_ + ", cb_sas_steering_angle=" + std::to_string(need_by_location_info.sas_steering_angle);
        need_by_location_info.steer_angle_is_update = 0;
    }

    void CyberMessenger::FareoRadarsPointsPub(){
        auto FareoRadarPointsPub = 
                    [this](std::tuple<bool, double, std::vector<StruRadarDetection> >& fareo_radars_points, 
                    int64_t& seq, std::shared_ptr< Writer<NewRadarPcloud> > writer_ptr) -> int{
            if(std::get<0>(fareo_radars_points) == false) return -1;
            NewRadarPcloud cmd;
            cmd.mutable_pts()->Reserve(std::get<2>(fareo_radars_points).size());
            auto header = cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            header->set_module_name(nodeCfg.getName());
            header->set_sequence_num(seq);
            header->set_version(0);

            for(auto elem: std::get<2>(fareo_radars_points)){
                auto radar_point = cmd.add_pts();
                radar_point->set_x(elem.CoGRange_NI_YY * std::sin(elem.Azimuth_NI_YY));
                radar_point->set_y(elem.CoGRange_NI_YY * std::cos(elem.Azimuth_NI_YY));
                // radar_point->set_z(elem.PowerDB_NI_YY);
                // radar_point->set_phase(elem.PowerDB_NI_YY);
                radar_point->set_power(elem.PowerDB_NI_YY);
                // radar_point->set_elevation(elem.PowerDB_NI_YY);
                radar_point->set_azimuth(elem.Azimuth_NI_YY);
                radar_point->set_doppler(elem.CoGDoppler_NI_YY);
                radar_point->set_range(elem.CoGRange_NI_YY);
                // radar_point->set_delta_velocity(elem.PowerDB_NI_YY);
                radar_point->set_motion_status(elem.NoInfrastructure_NI_YY);
                // radar_point->set_available_for_tracker(elem.PowerDB_NI_YY);
                // radar_point->set_cluster_idx(elem.PowerDB_NI_YY);
                // radar_point->set_annotation_cluster_idx(elem.PowerDB_NI_YY);
                // radar_point->set_annotation_class(elem.PowerDB_NI_YY);
            }
            cmd.set_measurement_time(std::get<1>(fareo_radars_points));
            writer_ptr->Write(cmd);
            // AINFO << "FareoRadarPointsPub: " <<cmd.ShortDebugString();
            std::get<0>(fareo_radars_points) = false;
            return 1;
        };

        {
            static int64_t seq_fc = 0;
            int res = FareoRadarPointsPub(fareo_radars_point_fc_, seq_fc, 
                pWriterFareoRadarPointsFC);
            if(1 == res){
                cyber_radar_str_ = cyber_radar_str_ + ", cb_fc_points_size=" + std::to_string(std::get<2>(fareo_radars_point_fc_).size())
                    + ", cb_seq_fc_points=" + std::to_string(seq_fc);
                seq_fc++;
            }
        }
        {
            static int64_t seq_fl = 0;
            int res = FareoRadarPointsPub(fareo_radars_point_fl_, seq_fl, 
                pWriterFareoRadarPointsFL);
            if(1 == res){
                cyber_radar_str_ = cyber_radar_str_ + ", cb_fl_points_size=" + std::to_string(std::get<2>(fareo_radars_point_fl_).size())
                + ", cb_seq_fl_points=" + std::to_string(seq_fl);
                seq_fl++;
            }
        }
        {
            static int64_t seq_fr = 0;
            int res = FareoRadarPointsPub(fareo_radars_point_fr_, seq_fr, 
                pWriterFareoRadarPointsFR);
            if(1 == res){
                cyber_radar_str_ = cyber_radar_str_ + ", cb_fr_points_size=" + std::to_string(std::get<2>(fareo_radars_point_fr_).size())
                + ", cb_seq_fr_points=" + std::to_string(seq_fr);
                seq_fr++;
            }
        }
        {
            static int64_t seq_rl = 0;
            int res = FareoRadarPointsPub(fareo_radars_point_rl_, seq_rl, 
                pWriterFareoRadarPointsRL);
            if(1 == res){
                cyber_radar_str_ = cyber_radar_str_ + ", cb_rl_points_size=" + std::to_string(std::get<2>(fareo_radars_point_rl_).size())
                + ", cb_seq_rl_points=" + std::to_string(seq_rl);
                seq_rl++;
            }
        }
        {
            static int64_t seq_rr = 0;
            int res = FareoRadarPointsPub(fareo_radars_point_rr_, seq_rr, 
                pWriterFareoRadarPointsRR);
            if(1 == res){
                cyber_radar_str_ = cyber_radar_str_ + ", cb_rr_points_size=" + std::to_string(std::get<2>(fareo_radars_point_rr_).size())
                + ", cb_seq_rr_points=" + std::to_string(seq_rr);
                seq_rr++;
            }
        }
    }
    
    void CyberMessenger::FareoRadarsPub(StruNewFareoRadars& fareo_radars){

        static int64_t radar_seq = 0;
        NewFareoRadarDetections cmd;
        cmd.mutable_detection_list()->Reserve(fareo_radars.radar_fc.size() + fareo_radars.radar_fl.size()
                                                + fareo_radars.radar_fr.size() + fareo_radars.radar_rl.size() + fareo_radars.radar_rr.size());
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(radar_seq++);
        header->set_version(0);

        cyber_radar_str_ = cyber_radar_str_ + ", cb_radar_seq=" + std::to_string(radar_seq) 
                                                + ", cb_fc_size=" + std::to_string(fareo_radars.radar_fc.size());
        while(!fareo_radars.radar_fc.empty()){
            auto element = fareo_radars.radar_fc.front();
            auto radar_detection = cmd.add_detection_list();
            radar_detection->set_frame_id(element.frame_id);
            radar_detection->set_object_id(element.object_id);
            radar_detection->mutable_position()->set_x(element.position.x);
            radar_detection->mutable_position()->set_y(element.position.y);
            radar_detection->set_velocity(element.velocity);
            radar_detection->set_accelerate(element.accelerate);
            radar_detection->set_length(element.length);
            radar_detection->set_length_covariance(element.length_covariance);
            radar_detection->set_width(element.width);
            radar_detection->set_width_covariance(element.width_covariance);
            radar_detection->set_track_age(element.track_age);
            radar_detection->set_exist_probablity(element.exist_probablity);
            radar_detection->set_orientation_rad(element.orientation_rad);
            radar_detection->set_orientation_covariance(element.orientation_covariance);
            radar_detection->set_yaw_rate(element.yaw_rate);
            radar_detection->set_yaw_rate_covariance(element.yaw_rate_covariance);
            for(int i = 0; i <  5; i ++){
                radar_detection->add_covariance(element.covariance[i]);
            }
            radar_detection->set_measurement_time(element.measurement_time);
            radar_detection->set_radar_name(static_cast<::rainbowdash::drivers::NewFareoRadarDetection_RadarPosition>(RadarPosition::FC));
            fareo_radars.radar_fc.pop();
        }

        cyber_radar_str_ = cyber_radar_str_ + ", cb_fl_size=" + std::to_string(fareo_radars.radar_fl.size());
        while(!fareo_radars.radar_fl.empty()){
            auto element = fareo_radars.radar_fl.front();
            auto radar_detection = cmd.add_detection_list();
            radar_detection->set_frame_id(element.frame_id);
            radar_detection->set_object_id(element.object_id);
            radar_detection->mutable_position()->set_x(element.position.x);
            radar_detection->mutable_position()->set_y(element.position.y);
            radar_detection->set_velocity(element.velocity);
            radar_detection->set_accelerate(element.accelerate);
            radar_detection->set_length(element.length);
            radar_detection->set_length_covariance(element.length_covariance);
            radar_detection->set_width(element.width);
            radar_detection->set_width_covariance(element.width_covariance);
            radar_detection->set_track_age(element.track_age);
            radar_detection->set_exist_probablity(element.exist_probablity);
            radar_detection->set_orientation_rad(element.orientation_rad);
            radar_detection->set_orientation_covariance(element.orientation_covariance);
            radar_detection->set_yaw_rate(element.yaw_rate);
            radar_detection->set_yaw_rate_covariance(element.yaw_rate_covariance);
            for(int i = 0; i <  5; i ++){
                radar_detection->add_covariance(element.covariance[i]);
            }
            radar_detection->set_measurement_time(element.measurement_time);
            radar_detection->set_radar_name(static_cast<::rainbowdash::drivers::NewFareoRadarDetection_RadarPosition>(RadarPosition::FL));
            fareo_radars.radar_fl.pop();
        }

        cyber_radar_str_ = cyber_radar_str_ + ", cb_fr_size=" + std::to_string(fareo_radars.radar_fr.size());
        while(!fareo_radars.radar_fr.empty()){
            auto element = fareo_radars.radar_fr.front();
            auto radar_detection = cmd.add_detection_list();
            radar_detection->set_frame_id(element.frame_id);
            radar_detection->set_object_id(element.object_id);
            radar_detection->mutable_position()->set_x(element.position.x);
            radar_detection->mutable_position()->set_y(element.position.y);
            radar_detection->set_velocity(element.velocity);
            radar_detection->set_accelerate(element.accelerate);
            radar_detection->set_length(element.length);
            radar_detection->set_length_covariance(element.length_covariance);
            radar_detection->set_width(element.width);
            radar_detection->set_width_covariance(element.width_covariance);
            radar_detection->set_track_age(element.track_age);
            radar_detection->set_exist_probablity(element.exist_probablity);
            radar_detection->set_orientation_rad(element.orientation_rad);
            radar_detection->set_orientation_covariance(element.orientation_covariance);
            radar_detection->set_yaw_rate(element.yaw_rate);
            radar_detection->set_yaw_rate_covariance(element.yaw_rate_covariance);
            for(int i = 0; i <  5; i ++){
                radar_detection->add_covariance(element.covariance[i]);
            }
            radar_detection->set_measurement_time(element.measurement_time);
            radar_detection->set_radar_name(static_cast<::rainbowdash::drivers::NewFareoRadarDetection_RadarPosition>(RadarPosition::FR));
            fareo_radars.radar_fr.pop();
        }

        cyber_radar_str_ = cyber_radar_str_ + ", cb_rl_size=" + std::to_string(fareo_radars.radar_rl.size());
        while(!fareo_radars.radar_rl.empty()){
            auto element = fareo_radars.radar_rl.front();
            auto radar_detection = cmd.add_detection_list();
            radar_detection->set_frame_id(element.frame_id);
            radar_detection->set_object_id(element.object_id);
            radar_detection->mutable_position()->set_x(element.position.x);
            radar_detection->mutable_position()->set_y(element.position.y);
            radar_detection->set_velocity(element.velocity);
            radar_detection->set_accelerate(element.accelerate);
            radar_detection->set_length(element.length);
            radar_detection->set_length_covariance(element.length_covariance);
            radar_detection->set_width(element.width);
            radar_detection->set_width_covariance(element.width_covariance);
            radar_detection->set_track_age(element.track_age);
            radar_detection->set_exist_probablity(element.exist_probablity);
            radar_detection->set_orientation_rad(element.orientation_rad);
            radar_detection->set_orientation_covariance(element.orientation_covariance);
            radar_detection->set_yaw_rate(element.yaw_rate);
            radar_detection->set_yaw_rate_covariance(element.yaw_rate_covariance);
            for(int i = 0; i <  5; i ++){
                radar_detection->add_covariance(element.covariance[i]);
            }
            radar_detection->set_measurement_time(element.measurement_time);
            radar_detection->set_radar_name(static_cast<::rainbowdash::drivers::NewFareoRadarDetection_RadarPosition>(RadarPosition::RL));
            fareo_radars.radar_rl.pop();
        }

        cyber_radar_str_ = cyber_radar_str_ + ", cb_rr_size=" + std::to_string(fareo_radars.radar_rr.size());
        while(!fareo_radars.radar_rr.empty()){
            auto element = fareo_radars.radar_rr.front();
            auto radar_detection = cmd.add_detection_list();
            radar_detection->set_frame_id(element.frame_id);
            radar_detection->set_object_id(element.object_id);
            radar_detection->mutable_position()->set_x(element.position.x);
            radar_detection->mutable_position()->set_y(element.position.y);
            radar_detection->set_velocity(element.velocity);
            radar_detection->set_accelerate(element.accelerate);
            radar_detection->set_length(element.length);
            radar_detection->set_length_covariance(element.length_covariance);
            radar_detection->set_width(element.width);
            radar_detection->set_width_covariance(element.width_covariance);
            radar_detection->set_track_age(element.track_age);
            radar_detection->set_exist_probablity(element.exist_probablity);
            radar_detection->set_orientation_rad(element.orientation_rad);
            radar_detection->set_orientation_covariance(element.orientation_covariance);
            radar_detection->set_yaw_rate(element.yaw_rate);
            radar_detection->set_yaw_rate_covariance(element.yaw_rate_covariance);
            for(int i = 0; i <  5; i ++){
                radar_detection->add_covariance(element.covariance[i]);
            }
            radar_detection->set_measurement_time(element.measurement_time);
            radar_detection->set_radar_name(static_cast<::rainbowdash::drivers::NewFareoRadarDetection_RadarPosition>(RadarPosition::RR));
            fareo_radars.radar_rr.pop();
        }
        
        cmd.set_measurement_time(getTimeS(nullptr));
        pWriterFareoRadar->Write(cmd);
        cyber_radar_str_.append(", radar_detection_pub=").append(std::to_string(1));
    }

    void CyberMessenger::FareoRadarsClear(StruNewFareoRadars& fareo_radars){
        auto FareoRadarPop = 
                    [this](std::queue<StruNewFareoRadar>& radar){
            while(!radar.empty()){
                // element set  but not used
                // auto element = radar.front();
                radar.front();
                radar.pop();
            }
        };

        FareoRadarPop(fareo_radars.radar_fc);
        FareoRadarPop(fareo_radars.radar_fl);
        FareoRadarPop(fareo_radars.radar_fr);
        FareoRadarPop(fareo_radars.radar_rl);
        FareoRadarPop(fareo_radars.radar_rr);
    }

    void CyberMessenger::savedSystemIdentification(
                    double slope_estimation, double mass_estimation){
        chassis_info_.slope_estimation = slope_estimation;
        chassis_info_.mass_estimation = mass_estimation;
    }

    //处理保存的topic数据，解析为cyber消息
    void CyberMessenger::processSavedTopicData(StruIOChassisInfoDebug& io_chassis_info_debug){
        if(1 == io_chassis_info_debug.imu_valid){
            output_data_imu_info_.is_update = 1;
            StruOutputDataImuInfo imu_data_tmp;
            while(!io_chassis_info_debug.soc_imu_vec.empty()){
                auto element = io_chassis_info_debug.soc_imu_vec.front();
                imu_data_tmp.measurement_time = element.imu_measurement_time;
                imu_data_tmp.linear_acceleration.x = element.linear_acceleration.x;
                imu_data_tmp.linear_acceleration.y = element.linear_acceleration.y;
                imu_data_tmp.linear_acceleration.z = element.linear_acceleration.z;
                imu_data_tmp.angular_velocity.x = element.angular_velocity.x;
                imu_data_tmp.angular_velocity.y = element.angular_velocity.y;
                imu_data_tmp.angular_velocity.z = element.angular_velocity.z;
                output_data_imu_info_.soc_imu_info_queu.emplace(imu_data_tmp);
                io_chassis_info_debug.soc_imu_vec.pop();
            }
        }

        if(1 == io_chassis_info_debug.ibc_12c_valid){
            //l2 epb_st
            if(1 == io_chassis_info_debug.ibc_epb_st){//break locked
                output_data_chassis_.epb_sys_st = CommonBool::TRUE;
                chassis_info_.vehicle_signal.epb_st = CommonBool::TRUE;
            }
            else if(0 == io_chassis_info_debug.ibc_epb_st){//released
                output_data_chassis_.epb_sys_st = CommonBool::FALSE;
                chassis_info_.vehicle_signal.epb_st = CommonBool::FALSE;
            }
            output_data_chassis_.LonDa_slope = io_chassis_info_debug.ibc_slope;
            vehicle_body_info_.slope = io_chassis_info_debug.ibc_slope;
        }
        if(1 == io_chassis_info_debug.eps_17a_valid){
            output_data_chassis_.LatDa_steering_torque = io_chassis_info_debug.eps_steering_torque_value;
            // l2 steering_system
            chassis_info_.steering_system.steering_wheel_torque = io_chassis_info_debug.eps_steering_torque_value;
        }
        if(1 == io_chassis_info_debug.sas_a5_valid){
            output_data_chassis_.LatDa_steering_angle = io_chassis_info_debug.sas_steering_angle;
            output_data_chassis_.LatDa_steering_velocity = io_chassis_info_debug.sas_steering_rotspd;

            need_by_location_info_.sas_a5_measurement_time = io_chassis_info_debug.sas_a5_measurement_time;
            need_by_location_info_.sas_steering_angle = io_chassis_info_debug.sas_steering_angle;
            need_by_location_info_.sas_steering_rotspd = io_chassis_info_debug.sas_steering_rotspd;
            need_by_location_info_.steer_angle_is_update = 1;
            // l2 steering_system
            chassis_info_.steering_system.steering_wheel_angle = io_chassis_info_debug.sas_steering_angle;
            chassis_info_.steering_system.steering_wheel_rate = io_chassis_info_debug.sas_steering_rotspd;
            // chassis_info_.steering_system.steering_angle = io_chassis_info_debug.sas_steering_angle;
        }
        if(1 == io_chassis_info_debug.pdcu_ff_valid){
            output_data_chassis_.LonDa_cur_torque = io_chassis_info_debug.pdcu_realized_powertrain_whltp;
            // l2 cur_torque
            chassis_info_.cur_torque = io_chassis_info_debug.pdcu_realized_powertrain_whltp;
        }
        if(1 == io_chassis_info_debug.swgc_119_valid){
            output_data_chassis_.LonDa_lon_acceleration = io_chassis_info_debug.acu_longitud_acceleration_st;
            StruOutputDataImuInfo imu_data_tmp;
            output_data_imu_info_chassis_.is_update = 1;
            while (!io_chassis_info_debug.swgc_119_imu_vec.empty())
            {
                auto element = io_chassis_info_debug.swgc_119_imu_vec.front();
                imu_data_tmp.measurement_time = static_cast<double>(element.swgc_119_measurement_time);
                imu_data_tmp.linear_acceleration.x = element.acu_longitud_acceleration_st;
                imu_data_tmp.linear_acceleration.y = element.acu_lateral_accelaration_st;
                imu_data_tmp.angular_velocity.z = element.acu_yaw_rate_st/180.0*3.141593;
                output_data_imu_info_chassis_.imu_info_queu.emplace(imu_data_tmp);
                io_chassis_info_debug.swgc_119_imu_vec.pop();
                // l2 acceleration yaw_rate
                chassis_info_.acceleration.longitudinal = element.acu_longitud_acceleration_st;
                chassis_info_.acceleration.lateral = element.acu_lateral_accelaration_st;
                chassis_info_.yaw_rate = element.acu_yaw_rate_st/180.0*3.141593;
            }
        }
#ifdef BUILD_HIL_TEST
        if(1 == io_chassis_info_debug.swgc_710_valid){
            output_data_imu_info_.is_update = 1;
            while(!io_chassis_info_debug.swgc_710_imu_vec.empty()){
                StruOutputDataImuInfo imu_data_tmp;
                auto element = io_chassis_info_debug.swgc_710_imu_vec.front();
                imu_data_tmp.measurement_time = element.sgwc_710_measurement_time;
                imu_data_tmp.linear_acceleration.x = element.HIL_LongitudeAccelSt;
                imu_data_tmp.linear_acceleration.y = element.HIL_LateralAccelSt;
                imu_data_tmp.linear_acceleration.z = element.HIL_VerticalAccelSt;
                imu_data_tmp.angular_velocity.x = element.HIL_RollRateSt/180.0*3.141593;
                imu_data_tmp.angular_velocity.y = element.HIL_PitchRateSt/180.0*3.141593;
                imu_data_tmp.angular_velocity.z = element.HIL_YawRateSt/180.0*3.141593;
                output_data_imu_info_.soc_imu_info_queu.emplace(imu_data_tmp);
                io_chassis_info_debug.swgc_710_imu_vec.pop();
            }
        }
#endif
        if(1 == io_chassis_info_debug.pdcu_1c8_valid){
            output_data_chassis_.LonDa_gear_pos = io_chassis_info_debug.cur_gear;
            if(0 == io_chassis_info_debug.pdcu_accel_peadal_valid){
                output_data_chassis_.LonDa_pdcu_accel_peadal_valid = CommonBool::FALSE;
            }
            else if(1 == io_chassis_info_debug.pdcu_accel_peadal_valid){
                output_data_chassis_.LonDa_pdcu_accel_peadal_valid = CommonBool::TRUE;
            }
            output_data_chassis_.LonDa_pdcu_accel_peadal_st = io_chassis_info_debug.pdcu_accel_peadal_st;

            need_by_location_info_.pdcu_1c8_measurement_time = io_chassis_info_debug.pdcu_1c8_measurement_time;
            need_by_location_info_.cur_gear = io_chassis_info_debug.cur_gear;
            need_by_location_info_.gear_is_update = 1;
            // l2 gear_locationb
            chassis_info_.gear_locationb = io_chassis_info_debug.cur_gear;
            // l2 throttle_perceptage
            chassis_info_.throttle_perceptage = io_chassis_info_debug.pdcu_accel_peadal_st;
        }
        if(1 == io_chassis_info_debug.ibc_a2_valid){
            // l2 standstill_info
             if(0 == io_chassis_info_debug.ibc_vehicle_standstill_st){
                output_data_chassis_.LonDa_stand_still = CommonBool::FALSE;
                need_by_location_info_.standstill_st = CommonBool::FALSE;
                chassis_info_.vehicle_signal.standstill_st = CommonBool::FALSE;
            }
            else if(1 == io_chassis_info_debug.ibc_vehicle_standstill_st){
                output_data_chassis_.LonDa_stand_still = CommonBool::TRUE;
                need_by_location_info_.standstill_st = CommonBool::TRUE;
                chassis_info_.vehicle_signal.standstill_st = CommonBool::TRUE;
            }
            vehicle_body_info_.ibc_vehicle_speed = io_chassis_info_debug.ibc_vehicle_speed;
            vehicle_body_info_.speed_is_update = 1;

            need_by_location_info_.ibc_a2_measurement_time = io_chassis_info_debug.ibc_a2_measurement_time;
            need_by_location_info_.standstill_is_update = 1;
            // l2 vehicle_speed
            chassis_info_.vehicle_speed.vehicle_speed = io_chassis_info_debug.ibc_vehicle_speed/3.6;
            chassis_info_.vehicle_speed.velocity_vector = chassis_info_.vehicle_speed.vehicle_speed;
            if (0x2 == io_chassis_info_debug.ibc_vehicle_driving_direction) {
                chassis_info_.vehicle_speed.velocity_vector *= -1;
            }
        }
        if(1 == io_chassis_info_debug.ibc_101_valid /* &&
            1 == io_chassis_info_debug.ibc_172_valid */){
            double _ibc_whlspd_rl = 0;
            double _ibc_whlspd_rr = 0;
            static uint32_t _ibc_whl_diriection_rl_st = 1;
            static uint32_t _ibc_whl_diriection_rr_st = 1;
            if(1 == io_chassis_info_debug.ibc_whl_diriection_rl_st || 2 == io_chassis_info_debug.ibc_whl_diriection_rl_st){
                _ibc_whl_diriection_rl_st = io_chassis_info_debug.ibc_whl_diriection_rl_st;
            } else if(3 == io_chassis_info_debug.ibc_whl_diriection_rl_st) { 
                if(3 == io_chassis_info_debug.cur_gear) {  
                    _ibc_whl_diriection_rl_st = 1;
                } else if(1 == io_chassis_info_debug.cur_gear) {
                    _ibc_whl_diriection_rl_st = 2;
                }
            }

            if(1 == io_chassis_info_debug.ibc_whl_diriection_rr_st || 2 == io_chassis_info_debug.ibc_whl_diriection_rr_st){
                _ibc_whl_diriection_rr_st = io_chassis_info_debug.ibc_whl_diriection_rr_st;
            } else if(3 == io_chassis_info_debug.ibc_whl_diriection_rr_st) {
                if(3 == io_chassis_info_debug.cur_gear) {  
                    _ibc_whl_diriection_rr_st = 1;
                } else if(1 == io_chassis_info_debug.cur_gear) {
                    _ibc_whl_diriection_rr_st = 2;
                }
            }

            //0 invalid 1 forward 2 reverse 3 stop
            if(1 == _ibc_whl_diriection_rl_st){
                _ibc_whlspd_rl = io_chassis_info_debug.ibc_whlspd_rl;
            }
            else if(2 == _ibc_whl_diriection_rl_st){
                _ibc_whlspd_rl = -io_chassis_info_debug.ibc_whlspd_rl;
            }

            if(1 == _ibc_whl_diriection_rr_st){
                _ibc_whlspd_rr = io_chassis_info_debug.ibc_whlspd_rr;
            }
            else if(2 == _ibc_whl_diriection_rr_st){
                _ibc_whlspd_rr = -io_chassis_info_debug.ibc_whlspd_rr;
            }
            output_data_chassis_.LonDa_speed = (_ibc_whlspd_rl + _ibc_whlspd_rr)/2.0/3.6;
            cyber_str_.append(", lon_data_speed=").append(std::to_string(output_data_chassis_.LonDa_speed));
            // velocity
            io_chassis_info_debug.velocity =  output_data_chassis_.LonDa_speed;
            // l2 vehicle_speed
            chassis_info_.vehicle_speed.wheel_speed_fl = io_chassis_info_debug.ibc_whlspd_fl/3.6;
            chassis_info_.vehicle_speed.wheel_speed_fr = io_chassis_info_debug.ibc_whlspd_fr/3.6;
            chassis_info_.vehicle_speed.wheel_speed_rl = io_chassis_info_debug.ibc_whlspd_rl/3.6;
            chassis_info_.vehicle_speed.wheel_speed_rr = io_chassis_info_debug.ibc_whlspd_rr/3.6;
        }


        if(1 == io_chassis_info_debug.ibc_11d_valid){
            output_data_chassis_.LonDa_ibcu_pressure = io_chassis_info_debug.ibc_plunger_pressure;

            vehicle_body_info_.pedal_travel_sensor = io_chassis_info_debug.ibc_pedal_travel_sensor;
            // l2 brake_percentage
            chassis_info_.brake_percentage = io_chassis_info_debug.ibc_pedal_travel_sensor;
            
            if(isBrakeTakeover(io_chassis_info_debug.ibc_pedal_travel_sensor) && 1 == io_chassis_info_debug.takeover_stat){
                cyber_str_.append(", cb_brake_tk=1");
                output_data_chassis_.LonSt_esp_valid = CommonBool::FALSE;
                output_data_chassis_.LonSt_esp_err_reason = EspErrReason::HUMAN_TAKE_OVER;
            }
            else{
                cyber_str_.append(", cb_brake_tk=2");
                output_data_chassis_.LonSt_esp_valid = CommonBool::TRUE;
                output_data_chassis_.LonSt_esp_err_reason = EspErrReason::NONE;
            }
        }

        if(1 == io_chassis_info_debug.eps_b0_valid){
            cyber_str_.append(", cb_eps_tk=2");
            output_data_chassis_.LatSt_eps_valid = CommonBool::TRUE;
            output_data_chassis_.LatSt_eps_err_type = CommonErrorType::NONE;
            output_data_chassis_.LatSt_eps_err_reason = EpsErrReason::NONE;
        }

        if(/* 1 == io_chassis_info_debug.ibc_101_valid && */
            1 == io_chassis_info_debug.ibc_172_valid){

            StruOutputDataWheelSpeedInfo whlspd_data_tmp;
            output_data_wheel_speed_.is_update = 1;
            while (!io_chassis_info_debug.ibc_172_pls_vec.empty())
            {
                auto element = io_chassis_info_debug.ibc_172_pls_vec.front();
                whlspd_data_tmp.valid = CommonBool::TRUE;
                whlspd_data_tmp.measurement_time =
                                                                    static_cast<double>(element.ibc_172_measurement_time);
                //不去判断101（轮速）的有效性，直接用最新的速度值
                whlspd_data_tmp.wheelspeed_fl = 
                    static_cast<double>(APAToWMWheelDir(element.ibc_whl_diriection_fl_st))*
                    io_chassis_info_debug.ibc_whlspd_fl/3.6;
                whlspd_data_tmp.wheelspeed_fr = 
                    static_cast<double>(APAToWMWheelDir(element.ibc_whl_diriection_fr_st))*
                    io_chassis_info_debug.ibc_whlspd_fr/3.6;
                whlspd_data_tmp.wheelspeed_rl = 
                    static_cast<double>(APAToWMWheelDir(element.ibc_whl_diriection_rl_st))*
                    io_chassis_info_debug.ibc_whlspd_rl/3.6;
                whlspd_data_tmp.wheelspeed_rr = 
                    static_cast<double>(APAToWMWheelDir(element.ibc_whl_diriection_rr_st))*
                    io_chassis_info_debug.ibc_whlspd_rr/3.6;

                whlspd_data_tmp.wheelsign_fl = APAToWMWheelDir(element.ibc_whl_diriection_fl_st);
                whlspd_data_tmp.wheelsign_fr = APAToWMWheelDir(element.ibc_whl_diriection_fr_st);
                whlspd_data_tmp.wheelsign_rl = APAToWMWheelDir(element.ibc_whl_diriection_rl_st);
                whlspd_data_tmp.wheelsign_rr = APAToWMWheelDir(element.ibc_whl_diriection_rr_st);

                whlspd_data_tmp.wheel_edgessum_fl = element.ibc_sum_edge_fl_wss;
                whlspd_data_tmp.wheel_edgessum_fr = element.ibc_sum_edge_fr_wss;
                whlspd_data_tmp.wheel_edgessum_rl = element.ibc_sum_edge_rl_wss;
                whlspd_data_tmp.wheel_edgessum_rr = element.ibc_sum_edge_rr_wss;
                output_data_wheel_speed_.wheel_speed_info_queu.emplace(whlspd_data_tmp);
                io_chassis_info_debug.ibc_172_pls_vec.pop();
            }
        }

         if(1 == io_chassis_info_debug.swgi_33d_valid){
            if(0 == vehicle_body_info_.swgi_33d_is_frist){
                vehicle_body_info_.swgi_33d_is_frist = 1;
                vehicle_body_info_.swgi_33d_is_update = 1;
            }
            else{
                if(vehicle_body_info_.viul_rearvmirrorfb_st != io_chassis_info_debug.viul_rearvmirrorfb_st){
                    vehicle_body_info_.swgi_33d_is_update = 1;
                }
            }
            vehicle_body_info_.viul_rearvmirrorfb_st = io_chassis_info_debug.viul_rearvmirrorfb_st;
        }

        if(1 == io_chassis_info_debug.swgc_23a_valid){
            if(0 == vehicle_body_info_.swgc_23a_is_frist){
                vehicle_body_info_.swgc_23a_is_frist = 1;
                vehicle_body_info_.swgc_23a_is_update = 1;
            }
            else{
                if(vehicle_body_info_.viul_lfdoorswst != io_chassis_info_debug.viul_lfdoorswst ||
                    vehicle_body_info_.viul_rfdoorswst != io_chassis_info_debug.viul_rfdoorswst ||
                    vehicle_body_info_.viul_rrdoorswst != io_chassis_info_debug.viul_rrdoorswst ||
                    vehicle_body_info_.viul_lrdoorswst != io_chassis_info_debug.viul_lrdoorswst ){
                    vehicle_body_info_.swgc_23a_is_update = 1;
                }
            }
            vehicle_body_info_.viul_lfdoorswst = io_chassis_info_debug.viul_lfdoorswst;
            vehicle_body_info_.viul_rfdoorswst = io_chassis_info_debug.viul_rfdoorswst;
            vehicle_body_info_.viul_rrdoorswst = io_chassis_info_debug.viul_rrdoorswst;
            vehicle_body_info_.viul_lrdoorswst = io_chassis_info_debug.viul_lrdoorswst;
            // l2 vehicle_signal
            chassis_info_.vehicle_signal.turn_signal = 0;
            chassis_info_.vehicle_signal.lf_doorsw_st = io_chassis_info_debug.viul_lfdoorswst;
            chassis_info_.vehicle_signal.rf_doorsw_st = io_chassis_info_debug.viul_rfdoorswst;
            chassis_info_.vehicle_signal.rr_doorsw_st = io_chassis_info_debug.viul_rrdoorswst;
            chassis_info_.vehicle_signal.lr_doorsw_st = io_chassis_info_debug.viul_lrdoorswst;
            chassis_info_.vehicle_signal.pot_enginehood_posst = io_chassis_info_debug.viul_enghood_unlock_warn;

            if(1 == io_chassis_info_debug.viul_left_light_st){
                chassis_info_.vehicle_signal.turn_signal = 1;
            }
            else if(1 == io_chassis_info_debug.viul_right_light_st){
                chassis_info_.vehicle_signal.turn_signal = 2;
            }
            if(1 == io_chassis_info_debug.viul_high_beam_st){
                chassis_info_.vehicle_signal.high_beam = true;
            }
            else{
                chassis_info_.vehicle_signal.high_beam = false;
            }
            if(1 == io_chassis_info_debug.viul_low_beam_st){
                chassis_info_.vehicle_signal.low_beam = true;
            }
            else{
                chassis_info_.vehicle_signal.low_beam = false;
            }
            if(1 == io_chassis_info_debug.viul_hazard_lamp_st){
                chassis_info_.vehicle_signal.emergency_light = true;
            }
            else{
                chassis_info_.vehicle_signal.emergency_light = false;
            }
            
        }

        if(1 == io_chassis_info_debug.swgi_30c_valid){
            if(0 == vehicle_body_info_.swgi_30c_is_frist){
                vehicle_body_info_.swgi_30c_is_frist = 1;
                vehicle_body_info_.swgi_30c_is_update = 1;
            }
            else{
                if(vehicle_body_info_.acu_driverbelt_swsigst != io_chassis_info_debug.acu_driverbelt_swsigst){
                    vehicle_body_info_.swgi_30c_is_update = 1;
                }
            }
            vehicle_body_info_.acu_driverbelt_swsigst = io_chassis_info_debug.acu_driverbelt_swsigst;
            // l2 vehicle_signal
            chassis_info_.vehicle_signal.driverbelt_swsig_st = io_chassis_info_debug.acu_driverbelt_swsigst;
            chassis_info_.vehicle_signal.passenger_belt_st = io_chassis_info_debug.acu_passenger_belt_st;
            chassis_info_.vehicle_signal.rl_belt_st = io_chassis_info_debug.acu_rl_belt_st;
            chassis_info_.vehicle_signal.rr_belt_st = io_chassis_info_debug.acu_rr_belt_st;
        }

         if(1 == io_chassis_info_debug.pdcu_fe_valid){
            if(0 == vehicle_body_info_.pdcu_fe_is_frist){
                vehicle_body_info_.pdcu_fe_is_frist = 1;
                vehicle_body_info_.pdcu_fe_is_update = 1;
            }
            else{
                if(vehicle_body_info_.pdcu_drive_ready_st != io_chassis_info_debug.pdcu_drive_ready_st){
                    vehicle_body_info_.pdcu_fe_is_update = 1;
                }
            }
            vehicle_body_info_.pdcu_drive_ready_st = io_chassis_info_debug.pdcu_drive_ready_st;
            // l2 vehicle_signal
            chassis_info_.vehicle_signal.drive_ready_st = io_chassis_info_debug.pdcu_drive_ready_st;
        }

        if(1 == io_chassis_info_debug.swgi_304_valid){
            if(0 == vehicle_body_info_.swgi_304_is_frist){
                vehicle_body_info_.swgi_304_is_frist = 1;
                vehicle_body_info_.swgi_304_is_update = 1;
            }
            else{
                if(vehicle_body_info_.pot_backdoor_posst != io_chassis_info_debug.pot_backdoor_posst){
                    vehicle_body_info_.swgi_304_is_update = 1;
                }
            }
            vehicle_body_info_.pot_backdoor_posst = io_chassis_info_debug.pot_backdoor_posst;
            // l2 vehicle_signal
            chassis_info_.vehicle_signal.pot_backdoor_posst = io_chassis_info_debug.pot_backdoor_posst;
        }

        if(1 == io_chassis_info_debug.ibc_143_valid){
            output_data_chassis_.LonSt_ibc_lsm_ctrl_fault_sts = 
                static_cast<IBCLSMCtrlFaultSts::Enum>(io_chassis_info_debug.ibc_lsm_ctrl_fault_st);
        }

        if(1 == io_chassis_info_debug.shakehand_valid){
            output_data_shake_hand_.is_update = 1;
            if(static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug.ibc_shakehand_sts){
                output_data_shake_hand_.long_active = CommonBool::TRUE;
                output_data_shake_hand_.long_shakehand_err_type = CommonErrorType::NONE;
                output_data_shake_hand_.long_shakehand_err_reason = LonShakeHandErrReason::NONE;
            }
            else if(static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDQUITSUS) == io_chassis_info_debug.ibc_shakehand_sts){
                output_data_shake_hand_.long_active = CommonBool::FALSE;
            }
            else{
                output_data_shake_hand_.long_active = CommonBool::NONE;
            }

            if(static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug.eps_shakehand_sts){
                output_data_shake_hand_.lat_active = CommonBool::TRUE;
                output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::NONE;
                output_data_shake_hand_.lat_shakehand_err_reason = LatShakeHandErrReason::NONE;
            }
            else if(static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDQUITSUS) == io_chassis_info_debug.eps_shakehand_sts){
                output_data_shake_hand_.lat_active = CommonBool::FALSE;
            }
            else{
                output_data_shake_hand_.lat_active = CommonBool::NONE;
            }
            // l2 driving_mode
            if(static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug.ibc_shakehand_sts &&
                static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug.eps_shakehand_sts){
                chassis_info_.driving_mode = 1;
            }
            else if (static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug.ibc_shakehand_sts){
                chassis_info_.driving_mode = 3;
            }
            else if (static_cast<uint32_t>(ShakeHandStatus::SHAKEHANDSUS) == io_chassis_info_debug.eps_shakehand_sts){
                chassis_info_.driving_mode = 2;
            }
            else{
                chassis_info_.driving_mode = 0;
            }
            
        }


        if(1 == io_chassis_info_debug.uss_valid){
            ult_data_info_.is_update = 1;
            ult_data_info_.uss_measurement_time = io_chassis_info_debug.uss_measurement_time;
            ult_data_info_.uss_data_info = io_chassis_info_debug.uss_data_info;
        }

        if(0x02 == io_chassis_info_debug.trans_result){
            gear_event_info_.gear_trans_valid = true;
        }
        else{
            gear_event_info_.gear_trans_valid = false;
        }

        if(1 == io_chassis_info_debug.msg_SGW_C_3F0_valid){
            output_data_app_rpareq_info_.is_update = 1;
            output_data_app_rpareq_info_.msg_SGW_C_3F0 = io_chassis_info_debug.msg_SGW_C_3F0;
        }

        if(1 == io_chassis_info_debug.msg_SGW_C_346_valid){
            output_data_key_rpareq_info_.is_update = 1;
            output_data_key_rpareq_info_.msg_SGW_C_346 = io_chassis_info_debug.msg_SGW_C_346;
        }

        if(1 == io_chassis_info_debug.msg_IBC_133_valid){
            output_data_chassis_.LonDa_brake_torque = io_chassis_info_debug.msg_IBC_133.IBC_BrkFricTotAtWhlsTorq;
        }

        output_data_chassis_.LonDa_sv_result = io_chassis_info_debug.sv_result;
        output_data_chassis_.LonDa_ack_distance = io_chassis_info_debug.ack_distance;
        output_data_chassis_.LonDa_ack_velocity = io_chassis_info_debug.ack_velocity;

        if(1 == io_chassis_info_debug.spa_st_valid){
            apa_status_ack_info_.is_update = 1;
            apa_status_ack_info_.scenario = io_chassis_info_debug.scenario;
            apa_status_ack_info_.apa_status = io_chassis_info_debug.apa_status;
        }

    }

    void CyberMessenger::FareoRadarPointsClear(){
        // for(auto canid: fc_can_vec_){
        std::get<0>(fareo_radars_point_fc_) = false;
        std::get<1>(fareo_radars_point_fc_) = 0.0;
        std::get<2>(fareo_radars_point_fc_).clear();
        // }

        std::get<0>(fareo_radars_point_fl_) = false;
        std::get<1>(fareo_radars_point_fl_) = 0.0;
        std::get<2>(fareo_radars_point_fl_).clear();

        std::get<0>(fareo_radars_point_fr_) = false;
        std::get<1>(fareo_radars_point_fr_) = 0.0;
        std::get<2>(fareo_radars_point_fr_).clear();

        std::get<0>(fareo_radars_point_rl_) = false;
        std::get<1>(fareo_radars_point_rl_) = 0.0;
        std::get<2>(fareo_radars_point_rl_).clear();

        std::get<0>(fareo_radars_point_rr_) = false;
        std::get<1>(fareo_radars_point_rr_) = 0.0;
        std::get<2>(fareo_radars_point_rr_).clear();
    }

#if ENABLE_RADAR_CLOUD
    void CyberMessenger::processRadarSavedTopicData(StrucNewRadarData& radar_data,
        std::vector<std::tuple<bool, uint64_t, std::unordered_map<uint32_t, std::pair<bool, StruRadarCANDetection>>>>& radar_map_data){
            //radar目标解析
        if(1 == radar_data.radar_fc_valid){
            while (!radar_data.radar_fc.empty())
            {
                auto element = radar_data.radar_fc.front();
                fareo_radars_.radar_fc.emplace(element);
                radar_data.radar_fc.pop();
            }
            fareo_radars_.radar_fc_measurement_time = radar_data.radar_fc_measurement_time;
            fareo_radars_.radar_fc_is_update = 1;
        }

        if(1 == radar_data.radar_fl_valid){
            while (!radar_data.radar_fl.empty())
            {
                auto element = radar_data.radar_fl.front();
                fareo_radars_.radar_fl.emplace(element);
                radar_data.radar_fl.pop();
            }
            fareo_radars_.radar_fl_measurement_time = radar_data.radar_fl_measurement_time;
            fareo_radars_.radar_fl_is_update = 1;
        }

        if(1 == radar_data.radar_fr_valid){
            while (!radar_data.radar_fr.empty())
            {
                auto element = radar_data.radar_fr.front();
                fareo_radars_.radar_fr.emplace(element);
                radar_data.radar_fr.pop();
            }
            fareo_radars_.radar_fr_measurement_time = radar_data.radar_fr_measurement_time;
            fareo_radars_.radar_fr_is_update = 1;
        }

        if(1 == radar_data.radar_rl_valid){
            while (!radar_data.radar_fl.empty())
            {
                auto element = radar_data.radar_fl.front();
                fareo_radars_.radar_fl.emplace(element);
                radar_data.radar_fl.pop();
            }
            fareo_radars_.radar_rl_measurement_time = radar_data.radar_rl_measurement_time;
            fareo_radars_.radar_rl_is_update = 1;
        }

        if(1 == radar_data.radar_rr_valid){
            while (!radar_data.radar_rr.empty())
            {
                auto element = radar_data.radar_rr.front();
                fareo_radars_.radar_rr.emplace(element);
                radar_data.radar_rr.pop();
            }
            fareo_radars_.radar_rr_measurement_time = radar_data.radar_rr_measurement_time;
            fareo_radars_.radar_rr_is_update = 1;
        }

        //radar点云解析
        // AINFO << __func__ << ": CoGRange_NI_0="<<std::get<2>(radar_map_data.at(RADAR_FC))[Radar_FC252].second.CoGRange_NI_0
        //         << "CoGDoppler_NI_0="<<std::get<2>(radar_map_data.at(RADAR_FC))[Radar_FC252].second.CoGDoppler_NI_0
        //         <<", first="<<std::get<2>(radar_map_data.at(RADAR_FC))[0x252].first
        //         <<", radar_map_data_first="<<std::get<0>(radar_map_data.at(RADAR_FC));
        if(std::get<0>(radar_map_data.at(RADAR_FC))){
            std::get<0>(fareo_radars_point_fc_) = true;
            std::get<1>(fareo_radars_point_fc_) = static_cast<double>(std::get<1>(radar_map_data.at(RADAR_FC)) / 1000.0);
            // fareo_radars_point_fc_.first = static_cast<double>(std::get<1>(radar_map_data.at(RADAR_FC)) / 1000.0);
            for(auto canid: fc_can_vec_){
                if(std::get<2>(radar_map_data.at(RADAR_FC))[canid].first){
                    SaveRadarPoints(std::get<2>(fareo_radars_point_fc_), std::get<2>(radar_map_data.at(RADAR_FC))[canid].second);
                }
            }
        }

        if(std::get<0>(radar_map_data.at(RADAR_FL))){
            std::get<0>(fareo_radars_point_fl_) = true;
            std::get<1>(fareo_radars_point_fl_) = static_cast<double>(std::get<1>(radar_map_data.at(RADAR_FL)) / 1000.0);
            for(auto canid: fl_can_vec_){
                if(std::get<2>(radar_map_data.at(RADAR_FL))[canid].first){
                    SaveRadarPoints(std::get<2>(fareo_radars_point_fl_), std::get<2>(radar_map_data.at(RADAR_FL))[canid].second);
                }
            }
        }
        if(std::get<0>(radar_map_data.at(RADAR_FR))){
            std::get<0>(fareo_radars_point_fr_) = true;
            std::get<1>(fareo_radars_point_fr_) = static_cast<double>(std::get<1>(radar_map_data.at(RADAR_FR)) / 1000.0);
            for(auto canid: fr_can_vec_){
                if(std::get<2>(radar_map_data.at(RADAR_FR))[canid].first){
                    SaveRadarPoints(std::get<2>(fareo_radars_point_fr_), std::get<2>(radar_map_data.at(RADAR_FR))[canid].second);
                }
            }
        }
        if(std::get<0>(radar_map_data.at(RADAR_RL))){
            std::get<0>(fareo_radars_point_rl_) = true;
            std::get<1>(fareo_radars_point_rl_) = static_cast<double>(std::get<1>(radar_map_data.at(RADAR_RL)) / 1000.0);
            for(auto canid: rl_can_vec_){
                if(std::get<2>(radar_map_data.at(RADAR_RL))[canid].first){
                    SaveRadarPoints(std::get<2>(fareo_radars_point_rl_), std::get<2>(radar_map_data.at(RADAR_RL))[canid].second);
                }
            }
        }
        if(std::get<0>(radar_map_data.at(RADAR_RR))){
            std::get<0>(fareo_radars_point_rr_) = true;
            std::get<1>(fareo_radars_point_rr_) = static_cast<double>(std::get<1>(radar_map_data.at(RADAR_RR)) / 1000.0);
            for(auto canid: rr_can_vec_){
                if(std::get<2>(radar_map_data.at(RADAR_RR))[canid].first){
                    SaveRadarPoints(std::get<2>(fareo_radars_point_rr_), std::get<2>(radar_map_data.at(RADAR_RR))[canid].second);
                }
            }
        }
    }
#else
    void CyberMessenger::processRadarSavedTopicData(StrucNewRadarData& radar_data){
            //radar目标解析
        if(1 == radar_data.radar_fc_valid){
            while (!radar_data.radar_fc.empty())
            {
                auto element = radar_data.radar_fc.front();
                fareo_radars_.radar_fc.emplace(element);
                radar_data.radar_fc.pop();
            }
            fareo_radars_.radar_fc_measurement_time = radar_data.radar_fc_measurement_time;
            fareo_radars_.radar_fc_is_update = 1;
        }

        if(1 == radar_data.radar_fl_valid){
            while (!radar_data.radar_fl.empty())
            {
                auto element = radar_data.radar_fl.front();
                fareo_radars_.radar_fl.emplace(element);
                radar_data.radar_fl.pop();
            }
            fareo_radars_.radar_fl_measurement_time = radar_data.radar_fl_measurement_time;
            fareo_radars_.radar_fl_is_update = 1;
        }

        if(1 == radar_data.radar_fr_valid){
            while (!radar_data.radar_fr.empty())
            {
                auto element = radar_data.radar_fr.front();
                fareo_radars_.radar_fr.emplace(element);
                radar_data.radar_fr.pop();
            }
            fareo_radars_.radar_fr_measurement_time = radar_data.radar_fr_measurement_time;
            fareo_radars_.radar_fr_is_update = 1;
        }

        if(1 == radar_data.radar_rl_valid){
            while (!radar_data.radar_fl.empty())
            {
                auto element = radar_data.radar_fl.front();
                fareo_radars_.radar_fl.emplace(element);
                radar_data.radar_fl.pop();
            }
            fareo_radars_.radar_rl_measurement_time = radar_data.radar_rl_measurement_time;
            fareo_radars_.radar_rl_is_update = 1;
        }

        if(1 == radar_data.radar_rr_valid){
            while (!radar_data.radar_rr.empty())
            {
                auto element = radar_data.radar_rr.front();
                fareo_radars_.radar_rr.emplace(element);
                radar_data.radar_rr.pop();
            }
            fareo_radars_.radar_rr_measurement_time = radar_data.radar_rr_measurement_time;
            fareo_radars_.radar_rr_is_update = 1;
        }
    }
#endif

    bool CyberMessenger::StruRadarDetectionIsValid(StruRadarDetection& p){
        constexpr double eps = 1e-4;
        if(std::fabs(p.CoGRange_NI_YY) >= eps || std::fabs(p.CoGDoppler_NI_YY) >= eps ||
            p.Reserve1_1bit_NI_YY != 0 || p.PowerDB_NI_YY != 0 ||
            p.SNRdB_NI_YY != 0 || std::fabs(p.StdAzimuth_NI_YY) >= eps ||
            std::fabs(p.Azimuth_NI_YY) >= eps || p.Beam_NI_YY != 0 ||
            p.NoInfrastructure_NI_YY != 0 || p.ValidXBeam_NI_YY != 0){
            return true;
        }
        return false;
    }

    bool CyberMessenger::PrintRadarDetection(StruRadarDetection& p){
        AINFO << "PrintRadarDetection: "
                    << "CoGRange_NI_YY=" << p.CoGRange_NI_YY
                    << ", CoGDoppler_NI_YY=" << p.CoGDoppler_NI_YY
                    << ", Reserve1_1bit_NI_YY=" << p.Reserve1_1bit_NI_YY
                    << ", PowerDB_NI_YY=" << p.PowerDB_NI_YY
                    << ", SNRdB_NI_YY=" << p.SNRdB_NI_YY
                    << ", StdAzimuth_NI_YY=" << p.StdAzimuth_NI_YY
                    << ", Azimuth_NI_YY=" << p.Azimuth_NI_YY
                    << ", Beam_NI_YY=" << p.Beam_NI_YY
                    << ", NoInfrastructure_NI_YY=" << p.NoInfrastructure_NI_YY
                    << ", ValidXBeam_NI_YY=" << p.ValidXBeam_NI_YY;
        return true;
    }

    void CyberMessenger::SaveRadarPoints(
            std::vector<StruRadarDetection>& points, StruRadarCANDetection& can_points){
            points.reserve(points.size() + 5);
            StruRadarDetection point;
            point.CoGRange_NI_YY = can_points.CoGRange_NI_0; //CoGRange_NI_YY
            point.CoGDoppler_NI_YY = can_points.CoGDoppler_NI_0; //CoGDoppler_NI_YY
            point.Reserve1_1bit_NI_YY = can_points.Reserve1_1bit_NI_0; //Reserve1_1bit_NI_YY
            point.PowerDB_NI_YY = can_points.PowerDB_NI_0; //PowerDB_NI_YY
            point.SNRdB_NI_YY = can_points.SNRdB_NI_0; //SNRdB_NI_YY
            point.StdAzimuth_NI_YY = can_points.StdAzimuth_NI_0; //StdAzimuth_NI_YY
            point.Azimuth_NI_YY = can_points.Azimuth_NI_0; //Azimuth_NI_YY
            point.Beam_NI_YY = can_points.Beam_NI_0; //Beam_NI_YY
            point.NoInfrastructure_NI_YY = can_points.NoInfrastructure_NI_0; //NoInfrastructure_NI_YY
            point.ValidXBeam_NI_YY = can_points.ValidXBeam_NI_0; //ValidXBeam_NI_YY
            // PrintRadarDetection(point);
            if(StruRadarDetectionIsValid(point)){
                points.emplace_back(point);
            }
            point.CoGRange_NI_YY = can_points.CoGRange_NI_1; //CoGRange_NI_YY
            point.CoGDoppler_NI_YY = can_points.CoGDoppler_NI_1; //CoGDoppler_NI_YY
            point.Reserve1_1bit_NI_YY = can_points.Reserve1_1bit_NI_1; //Reserve1_1bit_NI_YY
            point.PowerDB_NI_YY = can_points.PowerDB_NI_1; //PowerDB_NI_YY
            point.SNRdB_NI_YY = can_points.SNRdB_NI_1; //SNRdB_NI_YY
            point.StdAzimuth_NI_YY = can_points.StdAzimuth_NI_1; //StdAzimuth_NI_YY
            point.Azimuth_NI_YY = can_points.Azimuth_NI_1; //Azimuth_NI_YY
            point.Beam_NI_YY = can_points.Beam_NI_1; //Beam_NI_YY
            point.NoInfrastructure_NI_YY = can_points.NoInfrastructure_NI_1; //NoInfrastructure_NI_YY
            point.ValidXBeam_NI_YY = can_points.ValidXBeam_NI_1; //ValidXBeam_NI_YY
            if(StruRadarDetectionIsValid(point)){
                points.emplace_back(point);
            }
            point.CoGRange_NI_YY = can_points.CoGRange_NI_2; //CoGRange_NI_YY
            point.CoGDoppler_NI_YY = can_points.CoGDoppler_NI_2; //CoGDoppler_NI_YY
            point.Reserve1_1bit_NI_YY = can_points.Reserve1_1bit_NI_2; //Reserve1_1bit_NI_YY
            point.PowerDB_NI_YY = can_points.PowerDB_NI_2; //PowerDB_NI_YY
            point.SNRdB_NI_YY = can_points.SNRdB_NI_2; //SNRdB_NI_YY
            point.StdAzimuth_NI_YY = can_points.StdAzimuth_NI_2; //StdAzimuth_NI_YY
            point.Azimuth_NI_YY = can_points.Azimuth_NI_2; //Azimuth_NI_YY
            point.Beam_NI_YY = can_points.Beam_NI_2; //Beam_NI_YY
            point.NoInfrastructure_NI_YY = can_points.NoInfrastructure_NI_2; //NoInfrastructure_NI_YY
            point.ValidXBeam_NI_YY = can_points.ValidXBeam_NI_2; //ValidXBeam_NI_YY
            if(StruRadarDetectionIsValid(point)){
                points.emplace_back(point);
            }
            point.CoGRange_NI_YY = can_points.CoGRange_NI_3; //CoGRange_NI_YY
            point.CoGDoppler_NI_YY = can_points.CoGDoppler_NI_3; //CoGDoppler_NI_YY
            point.Reserve1_1bit_NI_YY = can_points.Reserve1_1bit_NI_3; //Reserve1_1bit_NI_YY
            point.PowerDB_NI_YY = can_points.PowerDB_NI_3; //PowerDB_NI_YY
            point.SNRdB_NI_YY = can_points.SNRdB_NI_3; //SNRdB_NI_YY
            point.StdAzimuth_NI_YY = can_points.StdAzimuth_NI_3; //StdAzimuth_NI_YY
            point.Azimuth_NI_YY = can_points.Azimuth_NI_3; //Azimuth_NI_YY
            point.Beam_NI_YY = can_points.Beam_NI_3; //Beam_NI_YY
            point.NoInfrastructure_NI_YY = can_points.NoInfrastructure_NI_3; //NoInfrastructure_NI_YY
            point.ValidXBeam_NI_YY = can_points.ValidXBeam_NI_3; //ValidXBeam_NI_YY
            if(StruRadarDetectionIsValid(point)){
                points.emplace_back(point);
            }
            point.CoGRange_NI_YY = can_points.CoGRange_NI_4; //CoGRange_NI_YY
            point.CoGDoppler_NI_YY = can_points.CoGDoppler_NI_4; //CoGDoppler_NI_YY
            point.Reserve1_1bit_NI_YY = can_points.Reserve1_1bit_NI_4; //Reserve1_1bit_NI_YY
            point.PowerDB_NI_YY = can_points.PowerDB_NI_4; //PowerDB_NI_YY
            point.SNRdB_NI_YY = can_points.SNRdB_NI_4; //SNRdB_NI_YY
            point.StdAzimuth_NI_YY = can_points.StdAzimuth_NI_4; //StdAzimuth_NI_YY
            point.Azimuth_NI_YY = can_points.Azimuth_NI_4; //Azimuth_NI_YY
            point.Beam_NI_YY = can_points.Beam_NI_4; //Beam_NI_YY
            point.NoInfrastructure_NI_YY = can_points.NoInfrastructure_NI_4; //NoInfrastructure_NI_YY
            point.ValidXBeam_NI_YY = can_points.ValidXBeam_NI_4; //ValidXBeam_NI_YY
            if(StruRadarDetectionIsValid(point)){
                points.emplace_back(point);
            }
    }   

    bool CyberMessenger::isBrakeTakeover(const uint32_t ibc_pedal_travel_sensor){
        if( ibc_pedal_travel_sensor > ibc_pedal_travel_){
            return true;
        }
        return false;
    }



}
}

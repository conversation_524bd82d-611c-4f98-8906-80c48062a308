#include "config_data.h"
#include <string>
#include <iostream>
#include "common_utils.h"
#include "payload_info.h"

// using namespace io_server::changan;
using namespace io_server;
std::string str = "{\"timestamp\":1688602195289, \"Accx\":0.11731588,\"Accy\":-0.320823,\"Accz\":9.923966,\"Gyrox\":0.022370555,\"Gyroy\":0.004261058,\"Gyroz\":-0.004261058}";
std::string str_tmp = "{\"extension\":null,\"relative\":false,\"time\":1688703258380322,\"valid\":true,\"value\":{\"IBC_A2\":{\"Checksum0A2\":124,\"IBC_BrakelightReq\":0,\"IBC_EBDActiveSt\":0,\"IBC_EBDFaultSt\":0,\"IBC_ESCFaultSt\":0,\"IBC_ESCoffSt\":0,\"IBC_ModeSt\":0,\"IBC_VehicleDrivingDirection\":0,\"IBC_VehicleHoldSt\":0,\"IBC_VehicleSpeed\":0.0,\"IBC_VehicleSpeedValid\":1,\"IBC_VehicleStandstillSt\":1,\"IBC_VehicleStandstillValid\":1,\"RollingCounter0A2\":0}}}";

    StruIOChassisInfoDebug io_chassis_info_debug_tmp_;

void ParseIBCA2(const PayloadInfo& payInfo){
  static long long last_time = 0.0;
  if (payInfo.valid == false) {
    AINFO << __func__ << " msg invalid!"<<" ibc_a2_valid=3";
    io_chassis_info_debug_tmp_.ibc_a2_valid = 3;
    return;
  }
  AINFO<<11<<std::endl;
  if (payInfo.time == last_time) {
    AINFO << __func__ << " time stamp not changed!"<<" ibc_a2_valid=4";
    io_chassis_info_debug_tmp_.ibc_a2_valid = 4;
    return;
  }
  AINFO<<12<<std::endl;
  uint32_t time_span = payInfo.time - last_time;
  last_time = payInfo.time;
  io_chassis_info_debug_tmp_.ibc_a2_measurement_time = static_cast<double>(last_time)/1000.0;

  auto content = payInfo.value[SUB_IBC_A2];
    AINFO<<13<<std::endl;
  io_chassis_info_debug_tmp_.ibc_vehicle_driving_direction = content["IBC_VehicleDrivingDirection"];
  AINFO<<131<<std::endl;
  io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid = content["IBC_VehicleSpeedValid"];
  AINFO<<132<<std::endl;
  if(0x1 == io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid){
    io_chassis_info_debug_tmp_.ibc_vehicle_speed = content["IBC_VehicleSpeed"];
    AINFO<<133<<std::endl;
    io_chassis_info_debug_tmp_.velocity = io_chassis_info_debug_tmp_.ibc_vehicle_speed/3.6;
    if(0x2 == io_chassis_info_debug_tmp_.ibc_vehicle_driving_direction){
      io_chassis_info_debug_tmp_.velocity = -io_chassis_info_debug_tmp_.velocity;
    }
  }else{AINFO << __func__ << ": ibc_vehicle_speed_valid is invalid, ibc_vehicle_speed_valid="<<io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid;}
  io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st_valid = content["IBC_VehicleStandstillValid"];
  AINFO<<134<<std::endl;
  if(0x1 == io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st_valid){
    io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st = content["IBC_VehicleStandstillSt"];
    AINFO<<135<<std::endl;
  }else{AINFO << __func__ << ": ibc_vehicle_standstill_st_valid is invalid, ibc_vehicle_standstill_st_valid="<<io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st_valid;}
  io_chassis_info_debug_tmp_.ibc_esc_off_st = content["IBC_ESCoffSt"];
  AINFO<<136<<std::endl;
  io_chassis_info_debug_tmp_.ibc_esc_fault_st = content["IBC_ESCFaultSt"];
  AINFO<<137<<std::endl;
  io_chassis_info_debug_tmp_.ibc_a2_valid = 1;
    AINFO<<14<<std::endl;
    auto a = content["IBC_VehicleSpeedw"].is_null();
    auto b = content["IBC_VehicleSpeed"].is_null();
    AINFO<<"a="<<a<<"b="<<b<<std::endl;
  std::string ibc_a2_str = ", ibc_a2_measurement_time="+std::to_string(io_chassis_info_debug_tmp_.ibc_a2_measurement_time)+
                                        ", ibc_a2_time_span="+std::to_string(time_span)+
                                        ", ibc_vehicle_driving_direction="+std::to_string(io_chassis_info_debug_tmp_.ibc_vehicle_driving_direction)+
                                        ", ibc_vehicle_speed_valid="+std::to_string(io_chassis_info_debug_tmp_.ibc_vehicle_speed_valid)+
                                        ", ibc_vehicle_speed="+std::to_string(content["IBC_VehicleSpeed"].get<double>())+
                                        ", ibc_vehicle_standstill_st_valid="+std::to_string(io_chassis_info_debug_tmp_.ibc_vehicle_standstill_st_valid)+
                                        ", ibc_vehicle_standstill_st="+std::to_string(content["IBC_VehicleStandstillSt"].get<uint32_t>())+
                                        ", ibc_esc_off_st="+std::to_string(io_chassis_info_debug_tmp_.ibc_esc_off_st)+
                                        ", ibc_esc_fault_st="+std::to_string(io_chassis_info_debug_tmp_.ibc_esc_fault_st);
    AINFO<<ibc_a2_str<<std::endl;
}

int main(int argc, char *argv[]) {
    nlohmann::json j;
    if (ParseJsonString(str_tmp, j)) {
        AINFO << __func__ << " parse json msg failed! "<<std::endl;
        return 0;
    }
    AINFO<<1<<std::endl;
    PayloadInfo payInfo = j.get<PayloadInfo>();
    AINFO<<2<<std::endl;
    auto key1 = payInfo.value.size();
    // auto key2 = payInfo.value.
    AINFO<<key1<<std::endl;
    // AINFO<<iter.key()<<std::endl;
    for(auto key:payInfo.value)
    {
        AINFO<<key<<std::endl;
    }
    // for(auto iter = payInfo.value.begin())
    auto iter = payInfo.value.begin();
    AINFO<<iter.key()<<std::endl;
    ParseIBCA2(payInfo);

    while(1){}
    return 0;
}
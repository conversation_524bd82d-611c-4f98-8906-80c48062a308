if(CMAKE_SYSTEM_NAME MATCHES QNX)
    if(${BUILD_8255})
        message(" apa io build 8255")
        message("------------------------------------------------------8255----------------------------------------------------")
        set(APA_IO_CONTROL "modules/apa_io_control/dongfeng_io/src")
        FILE(GLOB APA_IO_SRCS ${APA_IO_CONTROL}/*.cpp)
        FILE(GLOB CanSources ${APA_IO_CONTROL}/can/*.cpp)
        FILE(GLOB PolymSources ${APA_IO_CONTROL}/polym/*.cpp)
        FILE(GLOB ParserSources ${APA_IO_CONTROL}/parser/generated/can_input/*.c ${APA_IO_CONTROL}/parser/generated/common/*.c)
        set(PARSER_INCLUDE_DIRS ${APA_IO_CONTROL}/parser/generated/can_input ${APA_IO_CONTROL}/parser/generated/common)

        FILE(GLOB MK_POINT_PARSER_SRC
             ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_FC/*.c
             ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_FL/*.c
             ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_FR/*.c
             ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_RL/*.c
             ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_RR/*.c
        )
        set(MK_POINT_PARSER_INC
            ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_FC
            ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_FL
            ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_FR
            ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_RL
            ${APA_IO_CONTROL}/parser/generated/MK_Radar_Point_RR
        )

        if(compile_single)
            add_library(ais_apa_io SHARED ${APA_IO_SRCS} ${PolymSources} ${ParserSources} ${MK_POINT_PARSER_SRC})
            target_include_directories(ais_apa_io PUBLIC modules/apa_io_control/dongfeng_io/include modules/common/base 
                ${PARSER_INCLUDE_DIRS} 
                ${BOOST_INCLUDE_DIRS}
                ${MK_POINT_PARSER_INC}
            )
            if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
                target_compile_options(ais_apa_io PRIVATE -finstrument-functions)
                target_link_libraries(ais_apa_io profilingS)
            endif()
            target_compile_definitions(ais_apa_io PRIVATE APA_MODULE_NAME="${io_module_name}")
            target_link_libraries(ais_apa_io
                atomic glog::glog fastrtps gflags fastcdr Boost::program_options msg_ops system_identification
                ${LIBMEGAIPC_LIBRARY}
                ${LIBIOX_POSH_LIBRARY}
                ${LIBIOX_POSH_WAY_LIBRARY}
                ${LIBIOX_POS_CONFIG_LIBRARY}
                ${LIBIOX_PLATFORM_LIBRARY}
                ${LIBIOX_BINGING_LIBRRAY}
                ${LIBCYBER_LIBRARY}
                protobuf_
                ${Boost_FILESYSTEM_LIBRARY}
                ${Boost_SYSTEM_LIBRARY}
                ${SOCKET_LIB}
                timer_common
            )
            add_custom_command(
                TARGET ais_apa_io
                POST_BUILD
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
                COMMAND mv libais_apa_io.so ${PROJECT_BINARY_DIR}/output/ais_lib
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/apa_io_control.ini ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/io_server.json ${PROJECT_BINARY_DIR}/output/conf
                # COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/modules/apa_io_control/config/io/io_conf.json -o ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_conf.json ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_control_test.json ${PROJECT_BINARY_DIR}/output/conf
                COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx710_depends.py ${PROJECT_BINARY_DIR}/output/ais_lib/libais_apa_io.so -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so -o ${PROJECT_BINARY_DIR}/output/lib/
                )
        else()
            add_executable(apa_io_server ${APA_IO_SRCS} ${PolymSources} ${ParserSources} ${PROTO_CC_FILES})
            target_compile_definitions(apa_io_server PRIVATE APA_MODULE_NAME="${io_module_name}")
            if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
                target_compile_options(apa_io_server PRIVATE -finstrument-functions)
                target_link_libraries(apa_io_server profilingS)
            endif()
            target_include_directories(apa_io_server PUBLIC modules/apa_io_control/dongfeng_io/include ${PARSER_INCLUDE_DIRS} ${BOOST_INCLUDE_DIRS})
            target_link_libraries(apa_io_server
                atomic glog::glog fastrtps gflags fastcdr Boost::program_options msg_ops system_identification
                ${LIBMEGAIPC_LIBRARY}
                ${LIBIOX_POSH_LIBRARY}
                ${LIBIOX_POSH_WAY_LIBRARY}
                ${LIBIOX_POS_CONFIG_LIBRARY}
                ${LIBIOX_PLATFORM_LIBRARY}
                ${LIBIOX_BINGING_LIBRRAY}
                ${LIBCYBER_LIBRARY}
                ${PROTOBUF_LIBRARY}
                ${Boost_FILESYSTEM_LIBRARY}
                ${Boost_SYSTEM_LIBRARY}
                ${SOCKET_LIB})
            add_custom_command(
                TARGET apa_io_server
                POST_BUILD
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                COMMAND mv apa_io_server ${PROJECT_BINARY_DIR}/output/bin
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/apa_io_control.ini ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/io_server.json ${PROJECT_BINARY_DIR}/output/conf
                # COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/modules/apa_io_control/config/io/io_conf.json -o ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_conf.json ${PROJECT_BINARY_DIR}/output/conf
                COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_control_test.json ${PROJECT_BINARY_DIR}/output/conf
                )
        endif()
        add_executable(io_test_server ${APA_IO_CONTROL}/test/main_key.cpp)
        target_compile_definitions(io_test_server PRIVATE APA_MODULE_NAME="${io_module_name}")
        # if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
        #     target_compile_options(io_test_server PRIVATE -finstrument-functions)
        #     target_link_libraries(io_test_server profilingS)
        # endif()
        target_include_directories(io_test_server PUBLIC modules/apa_io_control/dongfeng_io/include modules/common/base ${PARSER_INCLUDE_DIRS})
        target_link_libraries(io_test_server
            atomic glog::glog fastrtps gflags fastcdr Boost::program_options
            ${LIBMEGAIPC_LIBRARY}
            ${LIBCYBER_LIBRARY}
            protobuf_
            # ${Boost_FILESYSTEM_LIBRARY}
            # ${Boost_SYSTEM_LIBRARY}
            ${SOCKET_LIB}
            ${LIBUUID_LIBRARY})
    else()
        message(" apa io build 8155")
        if(${CAR_CHANGAN_385})
            set(APA_IO_CONTROL "modules/apa_io_control/changan_io/src/")
            if(compile_single)
                add_library(ais_apa_io SHARED ${APA_IO_CONTROL}/io_server.cpp  ${APA_IO_CONTROL}/main.cpp)
                target_compile_definitions(ais_apa_io PRIVATE APA_MODULE_NAME="${io_module_name}")

                target_include_directories(ais_apa_io PUBLIC modules/apa_io_control/changan_io/include ${BOOST_INCLUDE_DIRS})
                if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
                    target_compile_options(ais_apa_io PRIVATE -finstrument-functions)
                    target_link_libraries(ais_apa_io profilingS)
                endif()
                target_link_libraries(ais_apa_io
                    atomic glog::glog fastrtps gflags fastcdr Boost::program_options
                    ${LIBMEGAIPC_LIBRARY}
                    ${LIBIOX_POSH_LIBRARY}
                    ${LIBIOX_POSH_WAY_LIBRARY}
                    ${LIBIOX_POS_CONFIG_LIBRARY}
                    ${LIBIOX_PLATFORM_LIBRARY}
                    ${LIBIOX_BINGING_LIBRRAY}
                    ${LIBCYBER_LIBRARY}
                    protobuf_
                    ${Boost_FILESYSTEM_LIBRARY}
                    ${Boost_SYSTEM_LIBRARY})
                add_custom_command(
                    TARGET ais_apa_io
                    POST_BUILD
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
                    COMMAND mv libais_apa_io.so ${PROJECT_BINARY_DIR}/output/ais_lib
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/changan_io/config/apa_io_control.ini ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/io_server.json ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/changan_io/config/io_conf.json ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/changan_io/config/io_control_test.json ${PROJECT_BINARY_DIR}/output/conf
                    )
            else()
                add_executable(apa_io_server ${APA_IO_CONTROL}/io_server.cpp  ${APA_IO_CONTROL}/main.cpp
                    ${PROTO_CC_FILES})
                target_compile_definitions(apa_io_server PRIVATE APA_MODULE_NAME="${io_module_name}")
                if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
                    target_compile_options(apa_io_server PRIVATE -finstrument-functions)
                    target_link_libraries(apa_io_server profilingS)
                endif()
                target_include_directories(apa_io_server PUBLIC modules/apa_io_control/changan_io/include ${BOOST_INCLUDE_DIRS})
                target_link_libraries(apa_io_server
                    atomic glog::glog fastrtps gflags fastcdr Boost::program_options
                    ${LIBMEGAIPC_LIBRARY}
                    ${LIBIOX_POSH_LIBRARY}
                    ${LIBIOX_POSH_WAY_LIBRARY}
                    ${LIBIOX_POS_CONFIG_LIBRARY}
                    ${LIBIOX_PLATFORM_LIBRARY}
                    ${LIBIOX_BINGING_LIBRRAY}
                    ${LIBCYBER_LIBRARY}
                    ${PROTOBUF_LIBRARY}
                    ${Boost_FILESYSTEM_LIBRARY}
                    ${Boost_SYSTEM_LIBRARY})
                add_custom_command(
                    TARGET apa_io_server
                    POST_BUILD
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND mv apa_io_server ${PROJECT_BINARY_DIR}/output/bin
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/changan_io/config/apa_io_control.ini ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/io_server.json ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/changan_io/config/io_conf.json ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/changan_io/config/io_control_test.json ${PROJECT_BINARY_DIR}/output/conf
                    )
            endif()
            add_executable(io_test_server ${APA_IO_CONTROL}/main_key.cpp
                ${PROTO_CC_FILES})
            target_include_directories(io_test_server PUBLIC modules/apa_io_control/changan_io/include ${BOOST_INCLUDE_DIRS})
            target_link_libraries(io_test_server
                atomic glog::glog fastrtps gflags fastcdr Boost::program_options
                ${LIBCYBER_LIBRARY}
                ${PROTOBUF_LIBRARY}
                ${Boost_FILESYSTEM_LIBRARY}
                ${Boost_SYSTEM_LIBRARY})
            #add_custom_command(
            #    TARGET io_test_server 
            #    POST_BUILD
            #    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            #    COMMAND mv io_test_server ${PROJECT_BINARY_DIR}/output/bin
            #    )
        elseif(${CAR_DONGFENG_S59})
            set(APA_IO_CONTROL "modules/apa_io_control/dongfeng_io/src")
            FILE(GLOB APA_IO_SRCS ${APA_IO_CONTROL}/*.cpp)
            FILE(GLOB CanSources ${APA_IO_CONTROL}/can/*.cpp)
            FILE(GLOB PolymSources ${APA_IO_CONTROL}/polym/*.cpp)
            FILE(GLOB ParserSources ${APA_IO_CONTROL}/parser/generated/can_input/*.c ${APA_IO_CONTROL}/parser/generated/common/*.c)
            set(PARSER_INCLUDE_DIRS ${APA_IO_CONTROL}/parser/generated/can_input ${APA_IO_CONTROL}/parser/generated/common)

            if(compile_single)
                add_library(ais_apa_io SHARED ${APA_IO_SRCS} ${PolymSources} ${ParserSources})
                target_include_directories(ais_apa_io PUBLIC modules/apa_io_control/dongfeng_io/include modules/common/base ${PARSER_INCLUDE_DIRS} ${BOOST_INCLUDE_DIRS})
                if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
                    target_compile_options(ais_apa_io PRIVATE -finstrument-functions)
                    target_link_libraries(ais_apa_io profilingS)
                endif()
                target_compile_definitions(ais_apa_io PRIVATE APA_MODULE_NAME="${io_module_name}")
                target_link_libraries(ais_apa_io
                    atomic glog::glog fastrtps gflags fastcdr Boost::program_options msg_ops
                    ${LIBMEGAIPC_LIBRARY}
                    ${LIBIOX_POSH_LIBRARY}
                    ${LIBIOX_POSH_WAY_LIBRARY}
                    ${LIBIOX_POS_CONFIG_LIBRARY}
                    ${LIBIOX_PLATFORM_LIBRARY}
                    ${LIBIOX_BINGING_LIBRRAY}
                    ${LIBCYBER_LIBRARY}
                    protobuf_
                    ${Boost_FILESYSTEM_LIBRARY}
                    ${Boost_SYSTEM_LIBRARY})
                add_custom_command(
                    TARGET ais_apa_io
                    POST_BUILD
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/ais_lib
                    COMMAND mv libais_apa_io.so ${PROJECT_BINARY_DIR}/output/ais_lib
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/apa_io_control.ini ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/io_server.json ${PROJECT_BINARY_DIR}/output/conf
                    # COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/modules/apa_io_control/config/io/io_conf.json -o ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_conf.json ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_control_test.json ${PROJECT_BINARY_DIR}/output/conf
                    )
            else()
                add_executable(apa_io_server ${APA_IO_SRCS} ${PolymSources} ${ParserSources} ${PROTO_CC_FILES})
                target_compile_definitions(apa_io_server PRIVATE APA_MODULE_NAME="${io_module_name}")
                if (CMAKE_BUILD_TYPE STREQUAL "GPerf")
                    target_compile_options(apa_io_server PRIVATE -finstrument-functions)
                    target_link_libraries(apa_io_server profilingS)
                endif()
                target_include_directories(apa_io_server PUBLIC modules/apa_io_control/dongfeng_io/include ${PARSER_INCLUDE_DIRS} ${BOOST_INCLUDE_DIRS})
                target_link_libraries(apa_io_server
                    atomic glog::glog fastrtps gflags fastcdr Boost::program_options msg_ops
                    ${LIBMEGAIPC_LIBRARY}
                    ${LIBIOX_POSH_LIBRARY}
                    ${LIBIOX_POSH_WAY_LIBRARY}
                    ${LIBIOX_POS_CONFIG_LIBRARY}
                    ${LIBIOX_PLATFORM_LIBRARY}
                    ${LIBIOX_BINGING_LIBRRAY}
                    ${LIBCYBER_LIBRARY}
                    ${PROTOBUF_LIBRARY}
                    ${Boost_FILESYSTEM_LIBRARY}
                    ${Boost_SYSTEM_LIBRARY})
                add_custom_command(
                    TARGET apa_io_server
                    POST_BUILD
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
                    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND mv apa_io_server ${PROJECT_BINARY_DIR}/output/bin
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/apa_io_control.ini ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/common/config/io_server.json ${PROJECT_BINARY_DIR}/output/conf
                    # COMMAND python3 ${PROJECT_SOURCE_DIR}/tools/strip_off_json_comment.py -i ${PROJECT_SOURCE_DIR}/modules/apa_io_control/config/io/io_conf.json -o ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_conf.json ${PROJECT_BINARY_DIR}/output/conf
                    COMMAND cp ${PROJECT_SOURCE_DIR}/modules/apa_io_control/dongfeng_io/config/io_control_test.json ${PROJECT_BINARY_DIR}/output/conf
                    )
            endif()
            add_executable(io_test_server ${APA_IO_CONTROL}/test/main_key.cpp
                ${PROTO_CC_FILES})
            target_include_directories(io_test_server PUBLIC modules/apa_io_control/dongfeng_io/include ${PARSER_INCLUDE_DIRS} ${BOOST_INCLUDE_DIRS})
            target_link_libraries(io_test_server
                atomic glog::glog fastrtps gflags fastcdr Boost::program_options
                ${LIBCYBER_LIBRARY}
                ${PROTOBUF_LIBRARY}
                ${Boost_FILESYSTEM_LIBRARY}
                ${Boost_SYSTEM_LIBRARY})
            #add_custom_command(
            #    TARGET io_test_server 
            #    POST_BUILD
            #    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
            #    COMMAND mv io_test_server ${PROJECT_BINARY_DIR}/output/bin
            #    )
            add_executable(udp_sender ${APA_IO_CONTROL}/test/udp_sender.cpp)
            target_include_directories(udp_sender PUBLIC modules/apa_io_control/dongfeng_io/include)
            target_link_libraries(udp_sender
                ${LIBMEGAIPC_LIBRARY} 
                ${LIBIOX_POSH_LIBRARY} 
                ${LIBIOX_POSH_WAY_LIBRARY} 
                ${LIBIOX_POS_CONFIG_LIBRARY} 
                ${LIBIOX_PLATFORM_LIBRARY} 
                ${LIBIOX_BINGING_LIBRRAY})
            add_custom_command(
                TARGET udp_sender 
                POST_BUILD
                COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
                COMMAND mv udp_sender ${PROJECT_BINARY_DIR}/output/bin
                )
        else()
        endif()
    endif()
endif()

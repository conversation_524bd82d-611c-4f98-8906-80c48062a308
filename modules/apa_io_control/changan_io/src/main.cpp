#include "io_server.h"
#include "modules/common/base/util/util_def.h"

using namespace io_server;

std::shared_ptr <IOServer> io_server_;

extern "C" int start(base::util::HScene scene = base::util::HScene::UNDEFINE) {
    io_server_->io_start();
    AINFO << "io_server start";
    return 0;
}

#if USE_SINGLE
extern "C" int stop() {
    io_server_->io_stop();
    AINFO << "io_server shutdown";
    return 0;
}

extern "C" int stop_all() {
    AINFO << "io_server stop_all";
    return 0;
}

extern "C" int run_apa_io_server(int argc, char *argv[]) {
#else
int main(int argc, char *argv[]) {
    apollo::cyber::Init(argv[0]);
#endif
    std::cout<<"Enter main function..."<<std::endl;
    AINFO<<"Enter main function...";
    AINFO <<"io, APA_MODULE_NAME : "<<APA_MODULE_NAME;

    adas::app_template app;
    namespace bpo = boost::program_options;
    std::cout <<"cplusplus : "<<__cplusplus<< std::endl;
    // clang-off
    app.add_options()
    ("cyber_path",bpo::value<std::string>()->default_value("config"),"specify CYBER_PATH for adas")
    ("cyber_ip",bpo::value<std::string>()->default_value("**********"),"specify CYBER_IP for adas")
    ("cyber_log",bpo::value<std::string>()->default_value("logs"),"specify CYBER_LOG for adas")
    ("deubg_enabled", bpo::value<bool>()->default_value(false), "debug default decision")
    ("log", bpo::value<bool>()->default_value(false), "debug default decision")
    ("discard", bpo::value<int>()->default_value(800), "discard limit of request")
    ("timeout", bpo::value<int>()->default_value(20), "timeout of request")
    ("update_prefix", bpo::value<std::string>()->default_value(""), "update prefix")
    ("node_name",bpo::value<std::string>()->default_value(""),"changan io node name")
    ("io_cfg",bpo::value<std::string>()->default_value("conf/apa_io_control.json"),"node config json file")
    ("node_cfg",bpo::value<std::string>()->default_value("conf/apa_io_control.json"),"node config json file")
    ("conf", bpo::value<std::string>()->default_value("conf/apa_io_control.ini"), "template");
    // clang-on
    if(const auto &ret = app.run(argc,argv,"conf") ){
        if(ret != 0){
            if(ret == 1){
                std::cout <<"show help!"<< std::endl;
                return 0;
            }
            std::cout <<"command_line or conf_file parse failed !"<< std::endl;
            return -1;
        }
    }
    auto &&config = app.configuration();
    auto cyber_path = config["cyber_path"].as<std::string>();
    auto cyber_ip = config["cyber_ip"].as<std::string>();
    auto cyber_log = config["cyber_log"].as<std::string>();
    auto node_cfg = config["node_cfg"].as<std::string>();
    auto io_cfg = config["io_cfg"].as<std::string>();
    bpo::notify(config);

    std::cout <<"cyber_path : "<<cyber_path<< std::endl;
    std::cout <<"cyber_log : "<<cyber_log<< std::endl;
    std::cout <<"cyber_ip : "<<cyber_ip<< std::endl;
    std::cout <<"node_cfg : "<<node_cfg<< std::endl;
    std::cout <<"io_cfg : "<<io_cfg<< std::endl;

    AINFO << __FUNCTION__ << "argc " << argc;

    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        return 1;
    }

    //apollo::cyber::Init(argv[0]);
    AINFO << "create node";
    std::shared_ptr <Node> node(apollo::cyber::CreateNode(nodeCfg.getName()));
    AINFO << "create io_server";
    io_server_ = std::make_shared<IOServer>(node,nodeCfg,io_cfg);
    std::cout << "Init done. Enjoy." << std::endl;

    start();

#if not USE_SINGLE
    apollo::cyber::WaitForShutdown();
#endif
    return 0;
}


#include "io_server.h"

namespace io_server{

    IOServer::IOServer(std::shared_ptr <Node> node, NodeCfg& nodecfg, string io_cfg_path)
    :pNode(node),
    api(nullptr),
    nodeCfg(nodecfg),
    io_cfg_path_(io_cfg_path){
        AINFO << "mega-ipc init";
        setTopicMessageMap();
        if(api == nullptr){
            api = megaipc::MegaIpcApi::instance().createInstance();
            api->init();
            setSub();
            api->setListener(this);
        }
        initPubMsgBaseDBC();
        AINFO << "handshake status";
        handShakeStatusInit();
        initCyberPub();
        initCyberSub();
        AINFO << "Timer start";
        pTimerControl = std::make_shared<apollo::cyber::Timer>(20,std::bind(&IOServer::timer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,this),false, "io_control");
        // pTimerControl->Start();
        pTimerProcessTopic = std::make_shared<apollo::cyber::Timer>(10,std::bind(&IOServer::timerProcessTopicHandler,this),false, "io_process");
        // pTimerProcessTopic->Start();
        pTimerDebug = std::make_shared<apollo::cyber::Timer>(50,std::bind(&IOServer::timerDebugHandler,this),false, "io_debug");
        // pTimerDebug->Start();
        if(0 == ult_enable_){
            EnableUSSBaseInit();//使能ult
            ult_enable_ = 1;
        }
    }

    IOServer::~IOServer(){

    }

    bool IOServer::praseJson(const string io_server_cfg){
        JsonParser ioServerConf(io_server_cfg);
        if (!ioServerConf.isValid()) {
            return false;
        }
        auto io_server_conf = ioServerConf.getJsonParser();

        int sub_msgs_len = io_server_conf.size();
        for(int i=0;i<sub_msgs_len;i++){
            sub_msgs_.push_back(io_server_conf.at(i));
        }
        return true;
    }

    void IOServer::setSubTopic(){
        sub_msgs_.push_back(SUB_ICMSG_650);
        sub_msgs_.push_back(SUB_ICMSG_651);
        sub_msgs_.push_back(SUB_ICMSG_652);
        sub_msgs_.push_back(SUB_ICMSG_653);
        sub_msgs_.push_back(SUB_ICMSG_654);
    }

    void IOServer::setTopicMessageMap(){
        topic_msg_map_.insert(make_pair(SUB_ICMSG_650, &IOServer::praseICMSG650));
        topic_msg_map_.insert(make_pair(SUB_ICMSG_651, &IOServer::praseICMSG651));
        topic_msg_map_.insert(make_pair(SUB_ICMSG_652, &IOServer::praseICMSG652));
        topic_msg_map_.insert(make_pair(SUB_ICMSG_653, &IOServer::praseICMSG653));
        topic_msg_map_.insert(make_pair(SUB_ICMSG_654, &IOServer::praseICMSG654));
    }

    void IOServer::setSub(){
        setSubTopic();
        int sub_msgs_len = sub_msgs_.size();
        for(int i=0;i<sub_msgs_len;i++){
            if(api->subscribe(sub_msgs_.at(i)) == false){
                std::cout<< __func__<<": Subscribe topic " << sub_msgs_.at(i).c_str() << " failed."<<std::endl;
                AINFO<< __func__<<": Subscribe topic " << sub_msgs_.at(i).c_str() << " failed.";
            }
            else{
                std::cout<< __func__<<": Subscribe topic " << sub_msgs_.at(i).c_str() << " succeed."<<std::endl;
                AINFO<< __func__<<": Subscribe topic " << sub_msgs_.at(i).c_str() << " succeed.";
            }
        }
    }

    void IOServer::onConnectStateChanged(const string &nodeId, const ConnectState &state) {
        AERROR<< __func__<<" nodeId data:  " << nodeId.data() << " , state: "<< state;
    }

    void IOServer::onMessageArrival(const string &topic, const IpcMessage &msg) {
        // AINFO<< __func__<<":  topic=" << topic.c_str() << " length="<< msg.length << " data="<< msg.data;
        if(false == ic_callback_run_){
            return;
        }
        if (topic.empty()) {
            AINFO<< __func__<<" topic is null!";
        }
        saveTopic(topic,msg);
    }

    void IOServer::saveTopic(const string &topic, const IpcMessage &msg) {
        std::unique_lock<std::mutex> sub_lock(qnx_sub_mtx_);
        string message = string((char*)msg.data, msg.length);
        topic_map_[topic] = message;
    }

    map<string, string> IOServer::getRevTopicMap(){
        std::unique_lock<std::mutex> sub_lock(qnx_sub_mtx_);
        return topic_map_;
    }

    void IOServer::onMessageDelivered(const string &msgId) {
        AINFO<< __func__<<": msgId data: " << msgId.data();
    }

    bool IOServer::megaIpcPublish(const string &topic, const string &str_msg) {
        AINFO<< __func__<<": topic="<<topic<<", content="<<str_msg;
        json j;
        struct IpcMessage ipc_msg;
        ipc_msg.length = str_msg.length();
        ipc_msg.data = (uint8_t *)str_msg.c_str();
        ipc_msg.retain = true;

        j[topic] = str_msg;

        bool rc = api->publish(topic,ipc_msg);
        if(!rc){
            return false;
        }
        return true;
    }

    void IOServer::praseAllTopic(){
        // map<string, string> map_tmp = getRevTopicMap();
        std::unique_lock<std::mutex> sub_lock(qnx_sub_mtx_,std::defer_lock);
        sub_lock.lock();
        map<string, string> map_tmp = topic_map_;
        sub_lock.unlock();
        prase_rev_topic_idx_ = 0;
        for(auto it=map_tmp.begin();it != map_tmp.end(); it++){
            prase_rev_topic_ = it->first.c_str();
            prase_rev_topic_content_ = it->second.c_str();
            auto iter = topic_msg_map_.find(it->first);
            if(iter != topic_msg_map_.end()){
                (this->*(iter->second))(it->second);
            }
            prase_rev_topic_idx_++;
        }
    }

    int IOServer::processUltDistance(int s_dis){
        int h_value = s_dis/256;
        int l_value = s_dis%256;
        char h_str[20]={0};
        char l_str[20]={0};
        sprintf(h_str, "%x", h_value);
        sprintf(l_str, "%x", l_value);
        int res = atoi(h_str)*100 + atoi(l_str);
        return res;
    }

    void IOServer::pubMegaIpcMsg601(CDC_601& cdc_601){
        nlohmann::json j_601,j_extension_601;
        j_extension_601["CDC_601/VCU_Distance_CMD"] = cdc_601.VCU_Distance_CMD;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM9"] = cdc_601.VCU_ProbeCMD_NUM9;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM10"] = cdc_601.VCU_ProbeCMD_NUM10;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM11"] = cdc_601.VCU_ProbeCMD_NUM11;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM12"] = cdc_601.VCU_ProbeCMD_NUM12;
        j_extension_601["CDC_601/VCU_Machine_NUM"] = cdc_601.VCU_Machine_NUM;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM1"] = cdc_601.VCU_ProbeCMD_NUM1;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM2"] = cdc_601.VCU_ProbeCMD_NUM2;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM3"] = cdc_601.VCU_ProbeCMD_NUM3;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM4"] = cdc_601.VCU_ProbeCMD_NUM4;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM5"] = cdc_601.VCU_ProbeCMD_NUM5;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM6"] = cdc_601.VCU_ProbeCMD_NUM6;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM7"] = cdc_601.VCU_ProbeCMD_NUM7;
        j_extension_601["CDC_601/VCU_ProbeCMD_NUM8"] = cdc_601.VCU_ProbeCMD_NUM8;

        j_601["extension"] = "";
        j_601["relative"] = false;
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms
        j_601["time"] = cur_time;
        j_601["valid"] = true;
        j_601["value"] = j_extension_601;
        string topic_601 = pub_topic_601_;
        // AINFO<<__func__<<": "<<topic_601.c_str()<<" Pub: "<<j_601.dump().c_str();
        megaIpcPublish(topic_601,j_601.dump());
    }

    void IOServer::pubUltDistance(){
        nlohmann::json j,j_extension;
        j_extension["Ult_Probe_info1"] = icmsg_654_.Ult_Probe_info1;
        j_extension["Ult_Probe_info2"] = icmsg_654_.Ult_Probe_info2;
        j_extension["Ult_Probe_info3"] = icmsg_654_.Ult_Probe_info3;
        j_extension["Ult_Probe_info4"] = icmsg_654_.Ult_Probe_info4;
        j_extension["Ult_Probe_info5"] = icmsg_654_.Ult_Probe_info5;
        j_extension["Ult_Probe_info6"] = icmsg_654_.Ult_Probe_info6;
        j_extension["Ult_Probe_info7"] = icmsg_654_.Ult_Probe_info7;
        j_extension["Ult_Probe_info8"] = icmsg_654_.Ult_Probe_info8;
        j_extension["Ult_Probe_info9"] = icmsg_654_.Ult_Probe_info9;
        j_extension["Ult_Probe_info10"] = icmsg_654_.Ult_Probe_info10;
        j_extension["Ult_Probe_info11"] = icmsg_654_.Ult_Probe_info11;
        j_extension["Ult_Probe_info12"] = icmsg_654_.Ult_Probe_info12;
        j["extension"] = j_extension;
        j["relative"] = false;
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms
        j["time"] = cur_time;
        j["type"] = "";
        j["unit"] = "";
        j["valid"] = true;
        j["value"] = 666;
        string topic = "Custom/Ult_Probe_info";
        // AINFO<<__func__<<": "<<topic.c_str()<<" Pub: "<<j.dump().c_str();
        megaIpcPublish(topic,j.dump());
    }

    int IOServer::parse_json_string(const std::string cstrInput, json& outJson){
        int flag = 0;
        if(cstrInput.empty()){
            AERROR<<__func__<<" input is empty!";
            return -1;
        }

        try {
            outJson = json::parse(cstrInput);
        } catch (nlohmann::detail::parse_error e) {
            AERROR<<__func__<<" json pars failed "<<cstrInput.c_str();
            return -1;
        } catch (nlohmann::detail::type_error e) {
            AERROR<<__func__<<" parse type err "<<cstrInput.c_str();
            return -1;
        }
        return flag;
    }

   //prase changan topic
    void IOServer::praseICMSG650(const string &msg){
        static long long last_time = 0.0;
        nlohmann::json j;
         if (parse_json_string(msg, j)) {
            AINFO<< __func__<<" parse json msg failed! ";
            icmsg_650_.valid = 2;
            return;
        }
        PayloadInfo payInfo = j.get<PayloadInfo>();
        if (payInfo.valid == false) {
            AINFO<<__func__<<" msg invalid!";
            icmsg_650_.valid = 3;
            return;
        }

        if(payInfo.time == last_time){
            AINFO<<__func__<<" time stamp not changed!";
            icmsg_650_.valid = 4;
            return;
        }
        icmsg_650_.valid = 1;

        icmsg_650_.measurement_time = payInfo.time;
        auto extension = payInfo.extension;
        icmsg_650_.INS_Current_Pos_X_Accel = extension["ICMSG_650/INS_Current_Pos_X_Accel"];
        icmsg_650_.INS_Current_Pos_X_Rate = extension["ICMSG_650/INS_Current_Pos_X_Rate"];
        icmsg_650_.INS_Current_Pos_Y_Accel = extension["ICMSG_650/INS_Current_Pos_Y_Accel"];
        icmsg_650_.INS_Current_Pos_Y_Rate = extension["ICMSG_650/INS_Current_Pos_Y_Rate"];
        icmsg_650_.INS_Current_Pos_Z_Accel = extension["ICMSG_650/INS_Current_Pos_Z_Accel"];
        icmsg_650_.INS_Current_Pos_Z_Rate = extension["ICMSG_650/INS_Current_Pos_Z_Rate"];
        icmsg_650_.INS_IMU_Time = extension["ICMSG_650/INS_IMU_Time"];
        icmsg_650_.INS_IMU_Valid = extension["ICMSG_650/INS_IMU_Valid"];
        icmsg_650_.INS_Pos_X_Accel_Bias = extension["ICMSG_650/INS_Pos_X_Accel_Bias"];
        icmsg_650_.INS_Pos_X_Rate_Bias = extension["ICMSG_650/INS_Pos_X_Rate_Bias"];
        icmsg_650_.INS_Pos_Y_Accel_Bias = extension["ICMSG_650/INS_Pos_Y_Accel_Bias"];
        icmsg_650_.INS_Pos_Y_Rate_Bias = extension["ICMSG_650/INS_Pos_Y_Rate_Bias"];
        icmsg_650_.INS_Pos_Z_Accel_Bias = extension["ICMSG_650/INS_Pos_Z_Accel_Bias"];
        icmsg_650_.INS_Pos_Z_Rate_Bias = extension["ICMSG_650/INS_Pos_Z_Rate_Bias"];
        icmsg_650_.INS_Quaternion_W = extension["ICMSG_650/INS_Quaternion_W"];
        icmsg_650_.INS_Quaternion_X = extension["ICMSG_650/INS_Quaternion_X"];
        icmsg_650_.INS_Quaternion_Y = extension["ICMSG_650/INS_Quaternion_Y"];
        icmsg_650_.INS_Quaternion_Z = extension["ICMSG_650/INS_Quaternion_Z"];
        icmsg_650_.INS_Roll_Mis_Angle_to_Veh = extension["ICMSG_650/INS_Roll_Mis_Angle_to_Veh"];
        icmsg_650_.INS_Pitch_Mis_Angle_to_Veh = extension["ICMSG_650/INS_Pitch_Mis_Angle_to_Veh"];
        icmsg_650_.INS_Yaw_Mis_Angle_to_Veh = extension["ICMSG_650/INS_Yaw_Mis_Angle_to_Veh"];
        icmsg_650_.INS_RollingCounter_146 = extension["ICMSG_650/INS_RollingCounter_146"];
        icmsg_650_.INS_TiOut = extension["ICMSG_650/INS_TiOut"];
        icmsg_650_.INS_TiLeap = extension["ICMSG_650/INS_TiLeap"];
        icmsg_650_.INS_TiBas = extension["ICMSG_650/INS_TiBas"];
        icmsg_650_.INS_CRCCheck_146 = extension["ICMSG_650/INS_CRCCheck_146"];
        icmsg_650_.ICMSG_TimeStamp = extension["ICMSG_650/ICMSG_TimeStamp"];

        last_time = payInfo.time;
    }

    void IOServer::praseICMSG651(const string &msg){
        static long long last_time = 0.0;
        nlohmann::json j;
         if (parse_json_string(msg, j)) {
            AINFO<< __func__<<" parse json msg failed! ";
            icmsg_651_.valid = 2;
            return;
        }
        PayloadInfo payInfo = j.get<PayloadInfo>();
        if (payInfo.valid == false) {
            AINFO<<__func__<<" msg invalid!";
             icmsg_651_.valid = 3;
            return;
        }

        if(payInfo.time == last_time){
            AINFO<<__func__<<" time stamp not changed!";
            icmsg_651_.valid = 4;
            return;
        }
        icmsg_651_.valid = 1;

        icmsg_651_.measurement_time = payInfo.time;
        auto extension = payInfo.extension;
        icmsg_651_.VCUAPARequestEnable = extension["ICMSG_651/VCUAPARequestEnable"];
        icmsg_651_.VCUAPAdriverInterruption = extension["ICMSG_651/VCUAPAdriverInterruption"];
        icmsg_651_.VCUAccPedShield = extension["ICMSG_651/VCUAccPedShield"];
        icmsg_651_.VcuAPATorqRequestAvailable = extension["ICMSG_651/VcuAPATorqRequestAvailable"];
        icmsg_651_.VCUShiftPostionValid = extension["ICMSG_651/VCUShiftPostionValid"];
        icmsg_651_.VcuCalcnAccrPedlPosn = extension["ICMSG_651/VcuCalcnAccrPedlPosn"];
        icmsg_651_.VcuEnyRecyclMod = extension["ICMSG_651/VcuEnyRecyclMod"];
        icmsg_651_.VcuGearPosn = extension["ICMSG_651/VcuGearPosn"];
        icmsg_651_.VcuPtTqLimMax = extension["ICMSG_651/VcuPtTqLimMax"];
        icmsg_651_.VcuComFltSts = extension["ICMSG_651/VcuComFltSts"];
        icmsg_651_.VcuCalcnAccrPedlPosnVld = extension["ICMSG_651/VcuCalcnAccrPedlPosnVld"];
        icmsg_651_.VcuPtTqLimMaxVld = extension["ICMSG_651/VcuPtTqLimMaxVld"];
        icmsg_651_.VcuPtTqLimMin = extension["ICMSG_651/VcuPtTqLimMin"];
        icmsg_651_.VcuPtTqReal = extension["ICMSG_651/VcuPtTqReal"];
        icmsg_651_.VcuPtTqLimMinVld = extension["ICMSG_651/VcuPtTqLimMinVld"];
        icmsg_651_.VcuPtTqReqAvl = extension["ICMSG_651/VcuPtTqReqAvl"];
        icmsg_651_.VcuPtTqRealVld = extension["ICMSG_651/VcuPtTqRealVld"];
        icmsg_651_.VcuVehWhlReqTq = extension["ICMSG_651/VcuVehWhlReqTq"];
        icmsg_651_.VcuVehGearPosnVld = extension["ICMSG_651/VcuVehGearPosnVld"];
        icmsg_651_.VcuShiftLvlPosn = extension["ICMSG_651/VcuShiftLvlPosn"];
        icmsg_651_.VcuRdySts = extension["ICMSG_651/VcuRdySts"];
        icmsg_651_.VcuVehWhlReqTqVld = extension["ICMSG_651/VcuVehWhlReqTqVld"];
        icmsg_651_.VcuCrcChk161 = extension["ICMSG_651/VcuCrcChk161"];
        icmsg_651_.VcuSimnEpbSwtStsVld = extension["ICMSG_651/VcuSimnEpbSwtStsVld"];
        icmsg_651_.VcuSimnEpbSwtSts = extension["ICMSG_651/VcuSimnEpbSwtSts"];
        icmsg_651_.VcuCycCntr161 = extension["ICMSG_651/VcuCycCntr161"];
        icmsg_651_.EPS_fault_state = extension["ICMSG_651/EPS_fault_state"];
        icmsg_651_.EPS_RollingCounter_170 = extension["ICMSG_651/EPS_RollingCounter_170"];
        icmsg_651_.EpsCrcChk180 = extension["ICMSG_651/EpsCrcChk180"];
        icmsg_651_.EpsSasCalSts = extension["ICMSG_651/EpsSasCalSts"];
        icmsg_651_.EpsCycCntr180 = extension["ICMSG_651/EpsCycCntr180"];
        icmsg_651_.EpsSasSteerAg = extension["ICMSG_651/EpsSasSteerAg"];
        icmsg_651_.EpsSasSteerAgVld = extension["ICMSG_651/EpsSasSteerAgVld"];
        icmsg_651_.EpsSteerAgRate = extension["ICMSG_651/EpsSteerAgRate"];
        icmsg_651_.EpsSteerAgSensFilr = extension["ICMSG_651/EpsSteerAgSensFilr"];
        icmsg_651_.VcuAccrPedlPosn = extension["ICMSG_651/VcuAccrPedlPosn"];
        icmsg_651_.GW_ADSSecOCVerifyFailureFlag = extension["ICMSG_651/GW_ADSSecOCVerifyFailureFlag"];
        icmsg_651_.VcuOvrdModReq = extension["ICMSG_651/VcuOvrdModReq"];
        icmsg_651_.VcuAccrPedlPosnVld = extension["ICMSG_651/VcuAccrPedlPosnVld"];
        icmsg_651_.INS_TiStamp = extension["ICMSG_651/INS_TiStamp"];
        icmsg_651_.INS_TIleap_Difference = extension["ICMSG_651/INS_TIleap_Difference"];
        icmsg_651_.INS_TiOut = extension["ICMSG_651/INS_TiOut"];
        icmsg_651_.INS_TiLeap = extension["ICMSG_651/INS_TiLeap"];
        icmsg_651_.INS_TiBas = extension["ICMSG_651/INS_TiBas"];
        icmsg_651_.INS_RollingCounter_271 = extension["ICMSG_651/INS_RollingCounter_271"];
        icmsg_651_.INS_CRCCheck_271 = extension["ICMSG_651/INS_CRCCheck_271"];
        icmsg_651_.ICMSG_TimeStamp = extension["ICMSG_651/ICMSG_TimeStamp"];

        last_time = payInfo.time;
    }

    void IOServer::praseICMSG652(const string &msg){
        static long long last_time = 0.0;
        nlohmann::json j;
         if (parse_json_string(msg, j)) {
            AINFO<< __func__<<" parse json msg failed! ";
            icmsg_652_.valid = 2;
            return;
        }
        PayloadInfo payInfo = j.get<PayloadInfo>();
        if (payInfo.valid == false) {
            AINFO<<__func__<<" msg invalid!";
             icmsg_652_.valid = 3;
            return;
        }

        if(payInfo.time == last_time){
            AINFO<<__func__<<" time stamp not changed!";
            icmsg_652_.valid = 4;
            return;
        }
        icmsg_652_.valid = 1;

        icmsg_652_.measurement_time = payInfo.time;
        auto extension = payInfo.extension;
        icmsg_652_.EspAbsFailr = extension["ICMSG_652/EspAbsFailr"];
        icmsg_652_.EspAbsActv = extension["ICMSG_652/EspAbsActv"];
        icmsg_652_.EspReWhlDecTarTq = extension["ICMSG_652/EspReWhlDecTarTq"];
        icmsg_652_.EspReWhlDecTarTqActv = extension["ICMSG_652/EspReWhlDecTarTqActv"];
        icmsg_652_.EspReWhlIncTarTq = extension["ICMSG_652/EspReWhlIncTarTq"];
        icmsg_652_.EspEbdFailr = extension["ICMSG_652/EspEbdFailr"];
        icmsg_652_.EspTcsActvSts = extension["ICMSG_652/EspTcsActvSts"];
        icmsg_652_.EspVdcActvSts = extension["ICMSG_652/EspVdcActvSts"];
        icmsg_652_.EspReWhlIncTarTqActv = extension["ICMSG_652/EspReWhlIncTarTqActv"];
        icmsg_652_.EspVehSpd = extension["ICMSG_652/EspVehSpd"];
        icmsg_652_.EspVehSpdVld = extension["ICMSG_652/EspVehSpdVld"];
        icmsg_652_.ESP_CRCCheck_17A = extension["ICMSG_652/ESP_CRCCheck_17A"];
        icmsg_652_.ESP_RollingCounter_17A = extension["ICMSG_652/ESP_RollingCounter_17A"];
        icmsg_652_.EPB_AchievedClampForce = extension["ICMSG_652/EPB_AchievedClampForce"];
        icmsg_652_.EPB_AchievedClampForce_Primary = extension["ICMSG_652/EPB_AchievedClampForce_Primary"];
        icmsg_652_.EPB_FailStatuss_Primary = extension["ICMSG_652/EPB_FailStatuss_Primary"];
        icmsg_652_.EPB_APArequest_Available = extension["ICMSG_652/EPB_APArequest_Available"];
        icmsg_652_.EPS_CRCCheck_17E = extension["ICMSG_652/EPS_CRCCheck_17E"];
        icmsg_652_.EPS_LatCtrlAvailabilityStatus = extension["ICMSG_652/EPS_LatCtrlAvailabilityStatus"];
        icmsg_652_.EPS_LatCtrlActive = extension["ICMSG_652/EPS_LatCtrlActive"];
        icmsg_652_.INS_Current_Pos_Heading = extension["ICMSG_652/INS_Current_Pos_Heading"];
        icmsg_652_.EPS_RollingCounter_17E = extension["ICMSG_652/EPS_RollingCounter_17E"];
        icmsg_652_.INS_Current_Pos_Heading_Accuracy = extension["ICMSG_652/INS_Current_Pos_Heading_Accuracy"];
        icmsg_652_.INS_Current_Pos_Pitch = extension["ICMSG_652/INS_Current_Pos_Pitch"];
        icmsg_652_.INS_Current_Pos_Heading_Confiden = extension["ICMSG_652/INS_Current_Pos_Heading_Confiden"];
        icmsg_652_.INS_Current_Pos_Pitch_Accuracy = extension["ICMSG_652/INS_Current_Pos_Pitch_Accuracy"];
        icmsg_652_.INS_Current_Pos_Roll = extension["ICMSG_652/INS_Current_Pos_Roll"];
        icmsg_652_.INS_Current_Pos_Pitch_Confidence = extension["ICMSG_652/INS_Current_Pos_Pitch_Confidence"];
        icmsg_652_.INS_Current_Pos_Roll_Accuracy = extension["ICMSG_652/INS_Current_Pos_Roll_Accuracy"];
        icmsg_652_.INS_Current_Pos_Roll_Confidence = extension["ICMSG_652/INS_Current_Pos_Roll_Confidence"];
        icmsg_652_.FL_wheel_vel_for_IPC = extension["ICMSG_652/FL_wheel_vel_for_IPC"];
        icmsg_652_.FR_wheel_vel_for_IPC = extension["ICMSG_652/FR_wheel_vel_for_IPC"];
        icmsg_652_.RL_wheel_vel_for_IPC = extension["ICMSG_652/RL_wheel_vel_for_IPC"];
        icmsg_652_.RR_wheel_vel_for_IPC = extension["ICMSG_652/RR_wheel_vel_for_IPC"];
        icmsg_652_.L_wheel_factor = extension["ICMSG_652/L_wheel_factor"];
        icmsg_652_.R_wheel_factor = extension["ICMSG_652/R_wheel_factor"];
        icmsg_652_.EspAutoHoldActvSts = extension["ICMSG_652/EspAutoHoldActvSts"];
        icmsg_652_.EspTcsFailr = extension["ICMSG_652/EspTcsFailr"];
        icmsg_652_.EspFctOpenSts = extension["ICMSG_652/EspFctOpenSts"];
        icmsg_652_.EspActvSts = extension["ICMSG_652/EspActvSts"];
        icmsg_652_.EspVehStandstill = extension["ICMSG_652/EspVehStandstill"];
        icmsg_652_.IBCU_ADCActiveState = extension["ICMSG_652/IBCU_ADCActiveState"];
        icmsg_652_.IBCU_CommunicationInvalid = extension["ICMSG_652/IBCU_CommunicationInvalid"];
        icmsg_652_.IBCU_ADCFullFuncAvail = extension["ICMSG_652/IBCU_ADCFullFuncAvail"];
        icmsg_652_.IBCU_ADCReducedFuncAvail = extension["ICMSG_652/IBCU_ADCReducedFuncAvail"];
        icmsg_652_.ICMSG_TimeStamp = extension["ICMSG_652/ICMSG_TimeStamp"];

        last_time = payInfo.time;
    }

    void IOServer::praseICMSG653(const string &msg){
        static long long last_time = 0.0;
        nlohmann::json j;
         if (parse_json_string(msg, j)) {
            AINFO<< __func__<<" parse json msg failed! ";
            icmsg_653_.valid = 2;
            return;
        }
        PayloadInfo payInfo = j.get<PayloadInfo>();
        if (payInfo.valid == false) {
            AINFO<<__func__<<" msg invalid!";
             icmsg_653_.valid = 3;
            return;
        }

        if(payInfo.time == last_time){
            AINFO<<__func__<<" time stamp not changed!";
            icmsg_653_.valid = 4;
            return;
        }
        icmsg_653_.valid = 1;

        icmsg_653_.measurement_time = payInfo.time;
        auto extension = payInfo.extension;
        icmsg_653_.ESP_BrakeForce = extension["ICMSG_653/ESP_BrakeForce"];
        icmsg_653_.EPS_SteeringTorque = extension["ICMSG_653/EPS_SteeringTorque"];
        icmsg_653_.ESP_APA_DriverOverride = extension["ICMSG_653/ESP_APA_DriverOverride"];
        icmsg_653_.ESP_PrefillAvailable = extension["ICMSG_653/ESP_PrefillAvailable"];
        icmsg_653_.ESP_PrefillActive = extension["ICMSG_653/ESP_PrefillActive"];
        icmsg_653_.WhlSpdFrntLePls = extension["ICMSG_653/WhlSpdFrntLePls"];
        icmsg_653_.WhlSpdFrntRiPls = extension["ICMSG_653/WhlSpdFrntRiPls"];
        icmsg_653_.WhlSpdLeFrntData = extension["ICMSG_653/WhlSpdLeFrntData"];
        icmsg_653_.WhlSpdLeReData = extension["ICMSG_653/WhlSpdLeReData"];
        icmsg_653_.WhlSpdLeFrntDir = extension["ICMSG_653/WhlSpdLeFrntDir"];
        icmsg_653_.WhlSpdLeFrntDataVld = extension["ICMSG_653/WhlSpdLeFrntDataVld"];
        icmsg_653_.WhlSpdLeReDir = extension["ICMSG_653/WhlSpdLeReDir"];
        icmsg_653_.WhlSpdLeReDataVld = extension["ICMSG_653/WhlSpdLeReDataVld"];
        icmsg_653_.WhlSpdReLePls = extension["ICMSG_653/WhlSpdReLePls"];
        icmsg_653_.WhlSpdReRiPls = extension["ICMSG_653/WhlSpdReRiPls"];
        icmsg_653_.WhlSpdRiFrntData = extension["ICMSG_653/WhlSpdRiFrntData"];
        icmsg_653_.WhlSpdRiReData = extension["ICMSG_653/WhlSpdRiReData"];
        icmsg_653_.WhlSpdRiFrntDir = extension["ICMSG_653/WhlSpdRiFrntDir"];
        icmsg_653_.WhlSpdRiFrntDataVld = extension["ICMSG_653/WhlSpdRiFrntDataVld"];
        icmsg_653_.WhlSpdRiReDir = extension["ICMSG_653/WhlSpdRiReDir"];
        icmsg_653_.WhlSpdRiReDataVld = extension["ICMSG_653/WhlSpdRiReDataVld"];
        icmsg_653_.IbcuCrcChk20B = extension["ICMSG_653/IbcuCrcChk20B"];
        icmsg_653_.IbcuCycCntr20B = extension["ICMSG_653/IbcuCycCntr20B"];
        icmsg_653_.IBCU_ADCRampOffSuspendState = extension["ICMSG_653/IBCU_ADCRampOffSuspendState"];
        icmsg_653_.IBCU_APCActiveStatus = extension["ICMSG_653/IBCU_APCActiveStatus"];
        icmsg_653_.IBCU_APCReducedFuncAvail = extension["ICMSG_653/IBCU_APCReducedFuncAvail"];
        icmsg_653_.IBCU_APCFullFuncAvail = extension["ICMSG_653/IBCU_APCFullFuncAvail"];
        icmsg_653_.EPS_ConcussAvailabilityStatus = extension["ICMSG_653/EPS_ConcussAvailabilityStatus"];
        icmsg_653_.EPS_APA_EpasFAILED = extension["ICMSG_653/EPS_APA_EpasFAILED"];
        icmsg_653_.EPS_APA_ControlFeedback = extension["ICMSG_653/EPS_APA_ControlFeedback"];
        icmsg_653_.EPS_APA_Abortfeedback = extension["ICMSG_653/EPS_APA_Abortfeedback"];
        icmsg_653_.EPS_CRCCheck_24F = extension["ICMSG_653/EPS_CRCCheck_24F"];
        icmsg_653_.EPS_ElectPowerConsumption = extension["ICMSG_653/EPS_ElectPowerConsumption"];
        icmsg_653_.EPS_IACC_abortreason = extension["ICMSG_653/EPS_IACC_abortreason"];
        icmsg_653_.EPS_SystemSt = extension["ICMSG_653/EPS_SystemSt"];
        icmsg_653_.EPS_RollingCounter_24F = extension["ICMSG_653/EPS_RollingCounter_24F"];
        icmsg_653_.EPS_ADS_ControlFeedback = extension["ICMSG_653/EPS_ADS_ControlFeedback"];
        icmsg_653_.EPS_ADASActiveMode = extension["ICMSG_653/EPS_ADASActiveMode"];
        icmsg_653_.IbBrkPedlStsGb = extension["ICMSG_653/IbBrkPedlStsGb"];
        icmsg_653_.IBCU_PFSBrakePressure = extension["ICMSG_653/IBCU_PFSBrakePressure"];
        icmsg_653_.IbBrkPedlStsGbVld = extension["ICMSG_653/IbBrkPedlStsGbVld"];
        icmsg_653_.IBCU_PlungerBrakePressure = extension["ICMSG_653/IBCU_PlungerBrakePressure"];
        icmsg_653_.IBCU_PlungerBrakePressureValid = extension["ICMSG_653/IBCU_PlungerBrakePressureValid"];
        icmsg_653_.IbCrcChk2C2 = extension["ICMSG_653/IbCrcChk2C2"];
        icmsg_653_.IbCycCntr2C2 = extension["ICMSG_653/IbCycCntr2C2"];
        icmsg_653_.EpbStsPrimary = extension["ICMSG_653/EpbStsPrimary"];
        icmsg_653_.EpbSts = extension["ICMSG_653/EpbSts"];
        icmsg_653_.EpbFailrSts = extension["ICMSG_653/EpbFailrSts"];
        icmsg_653_.EspCrcChk3C2 = extension["ICMSG_653/EspCrcChk3C2"];
        icmsg_653_.EspCycCntr3C2 = extension["ICMSG_653/EspCycCntr3C2"];
        icmsg_653_.INS_Wheel_Scale_factor = extension["ICMSG_653/INS_Wheel_Scale_factor"];
        icmsg_653_.EspEpbReqAvl = extension["ICMSG_653/EspEpbReqAvl"];
        icmsg_653_.ICMSG_TimeStamp = extension["ICMSG_653/ICMSG_TimeStamp"];

        last_time = payInfo.time;
    }

    void IOServer::praseICMSG654(const string &msg){
        static long long last_time = 0.0;
        nlohmann::json j;
         if (parse_json_string(msg, j)) {
            AINFO<< __func__<<" parse json msg failed! ";
            icmsg_654_.valid = 2;
            return;
        }
        PayloadInfo payInfo = j.get<PayloadInfo>();
        if (payInfo.valid == false) {
            AINFO<<__func__<<" msg invalid!";
             icmsg_654_.valid = 3;
            return;
        }

        if(payInfo.time == last_time){
            AINFO<<__func__<<" time stamp not changed!";
            icmsg_654_.valid = 4;
            return;
        }
        icmsg_654_.valid = 1;

        icmsg_654_.measurement_time = payInfo.time;
        auto extension = payInfo.extension;
        icmsg_654_.Ult_Probe_info1 = extension["ICMSG_654/Ult_Probe_info1"];
        icmsg_654_.Ult_Probe_info2 = extension["ICMSG_654/Ult_Probe_info2"];
        icmsg_654_.Ult_Probe_info3 = extension["ICMSG_654/Ult_Probe_info3"];
        icmsg_654_.Ult_Probe_info4 = extension["ICMSG_654/Ult_Probe_info4"];
        icmsg_654_.Ult_Probe_info5 = extension["ICMSG_654/Ult_Probe_info5"];
        icmsg_654_.Ult_Probe_info6 = extension["ICMSG_654/Ult_Probe_info6"];
        icmsg_654_.Ult_Probe_info7 = extension["ICMSG_654/Ult_Probe_info7"];
        icmsg_654_.Ult_Probe_info8 = extension["ICMSG_654/Ult_Probe_info8"];
        icmsg_654_.Ult_Probe_info9 = extension["ICMSG_654/Ult_Probe_info9"];
        icmsg_654_.Ult_Probe_info10 = extension["ICMSG_654/Ult_Probe_info10"];
        icmsg_654_.Ult_Probe_info11 = extension["ICMSG_654/Ult_Probe_info11"];
        icmsg_654_.Ult_Probe_info12 = extension["ICMSG_654/Ult_Probe_info12"];
        icmsg_654_.ICMSG_TimeStamp = extension["ICMSG_654/ICMSG_TimeStamp"];

        last_time = payInfo.time;
    }

    void IOServer::pubMsgInitBaseDBC(CDC_247& cdc_247, CDC_2C6& cdc_2c6){
        pubMegaIpcMsg247(cdc_247);
        pubMegaIpcMsg2C6(cdc_2c6);
    }

    void IOServer::pubMegaIpcMsg247(CDC_247& cdc_247){
        struct timeval tv1;
        gettimeofday(&tv1, NULL);
        long cur_time1 = tv1.tv_sec * 1000 + tv1.tv_usec / 1000;   //conver to ms

        nlohmann::json j_247,j_extension_247;
        // j_extension_247["CDC_247/APA_SteeringAngleReqProtection"] = cdc_247.APA_SteeringAngleReqProtection;
        static int APA_SteeringAngleReqProtection_last = cdc_247.APA_SteeringAngleReqProtection;
        if(APA_SteeringAngleReqProtection_last != cdc_247.APA_SteeringAngleReqProtection){
            APA_SteeringAngleReqProtection_last = cdc_247.APA_SteeringAngleReqProtection;
            AINFO<<__func__<<": timestamp="<<cur_time1<<", cdc_247.APA_SteeringAngleReqProtection="<<cdc_247.APA_SteeringAngleReqProtection;
            if(1 == cdc_247.APA_SteeringAngleReqProtection){
                exception_handle_info_.steeringreq_one_time = cur_time1;
            }
            if(2 == cdc_247.APA_SteeringAngleReqProtection){
                exception_handle_info_.steeringreq_two_time = cur_time1;
            }
        }

        // static CDC_247
        static bool is_pub = false;
        static bool is_frist = true;

        if(true == is_frist){
            j_extension_247["CDC_247/APA_SteeringAngleReqProtection"] = cdc_247.APA_SteeringAngleReqProtection;
            j_extension_247["CDC_247/APA_ErrorStatus"] = cdc_247.APA_ErrorStatus;
            j_extension_247["CDC_247/APA_indication"] = cdc_247.APA_indication;
            j_extension_247["CDC_247/APA_APAOnOff"] = cdc_247.APA_APAOnOff;
            j_extension_247["CDC_247/APA_EmergenceBrake"] = cdc_247.APA_EmergenceBrake;
            j_extension_247["CDC_247/APA_SteeringAngleReq"] = cdc_247.APA_SteeringAngleReq;
            j_extension_247["CDC_247/APA_RemoteOnOff"] = cdc_247.APA_RemoteOnOff;
            j_extension_247["CDC_247/APA_ButtonPress"] = cdc_247.APA_ButtonPress;
            j_extension_247["CDC_247/APA_IncreasePressureReq"] = cdc_247.APA_IncreasePressureReq;
            j_extension_247["CDC_247/APA_TurnLightsCommand"] = cdc_247.APA_TurnLightsCommand;
            j_extension_247["CDC_247/APA_ParkNotice_4"] = cdc_247.APA_ParkNotice_4;
            j_extension_247["CDC_247/APA_ParkNotice"] = cdc_247.APA_ParkNotice;
            j_extension_247["CDC_247/APA_ParkingPercentage"] = cdc_247.APA_ParkingPercentage;

            j_extension_247["CDC_247/APA_EPBrequest"] = cdc_247.APA_EPBrequest;
            j_extension_247["CDC_247/APA_EPBrequestValid"] = cdc_247.APA_EPBrequestValid;
            j_extension_247["CDC_247/APA_TargetAccelerationValid"] = cdc_247.APA_TargetAccelerationValid;
            j_extension_247["CDC_247/APA_TransPRNDShiftRequest"] = cdc_247.APA_TransPRNDShiftRequest;
            j_extension_247["CDC_247/APA_TransPRNDShiftReqValid"] = cdc_247.APA_TransPRNDShiftReqValid;
            j_extension_247["CDC_247/APA_TargetAcceleration"] = cdc_247.APA_TargetAcceleration;
            j_extension_247["CDC_247/APA_EngTorqReq"] = cdc_247.APA_EngTorqReq;
            j_extension_247["CDC_247/APA_Activation_Status"] = cdc_247.APA_Activation_Status;
            j_extension_247["CDC_247/APA_TransPRNDShiftEnable"] = cdc_247.APA_TransPRNDShiftEnable;
            j_extension_247["CDC_247/APA_LSCAction"] = cdc_247.APA_LSCAction;
            j_extension_247["CDC_247/APA_HSAHDforbidden"] = cdc_247.APA_HSAHDforbidden;
            j_extension_247["CDC_247/APA_EngineTrqReqEnable"] = cdc_247.APA_EngineTrqReqEnable;
            j_extension_247["CDC_247/APA_AccPedShieldReq"] = cdc_247.APA_AccPedShieldReq;
            j_extension_247["CDC_247/APA_ESPDecompressionModel"] = cdc_247.APA_ESPDecompressionModel;
            j_extension_247["CDC_247/APA_PrefillReq"] = cdc_247.APA_PrefillReq;
            j_extension_247["CDC_247/APA_DynamicSlotWarning"] = cdc_247.APA_DynamicSlotWarning;
            j_extension_247["CDC_247/APA_SlotNotice"] = cdc_247.APA_SlotNotice;
            j_extension_247["CDC_247/APA_TCUClutchCombinationReq"] = cdc_247.APA_TCUClutchCombinationReq;
            j_extension_247["CDC_247/APA_TrqHoldForTCUCl"] = cdc_247.APA_TrqHoldForTCUCl;

            j_extension_247["CDC_247/APA_PtTorqReq"] = cdc_247.APA_PtTorqReq;
            j_extension_247["CDC_247/APA_ESPDistToStop"] = cdc_247.APA_ESPDistToStop;
            j_extension_247["CDC_247/APA_ESP_BrakeFunctionMode"] = cdc_247.APA_ESP_BrakeFunctionMode;
            j_extension_247["CDC_247/APA_PtTrqReqValid"] = cdc_247.APA_PtTrqReqValid;
            j_extension_247["CDC_247/APA_VCUReadyReq"] = cdc_247.APA_VCUReadyReq;
            j_extension_247["CDC_247/APA_ESP_StandstillRequest"] = cdc_247.APA_ESP_StandstillRequest;
        }
        else{
            if(cdc_247_pre_.APA_SteeringAngleReqProtection != cdc_247.APA_SteeringAngleReqProtection){
                is_pub = true;
                j_extension_247["CDC_247/APA_SteeringAngleReqProtection"] = cdc_247.APA_SteeringAngleReqProtection;
            }
            if(cdc_247_pre_.APA_ErrorStatus != cdc_247.APA_ErrorStatus){
                is_pub = true;
                j_extension_247["CDC_247/APA_ErrorStatus"] = cdc_247.APA_ErrorStatus;
            }
            if(cdc_247_pre_.APA_indication != cdc_247.APA_indication){
                is_pub = true;
                j_extension_247["CDC_247/APA_indication"] = cdc_247.APA_indication;
            }
            if(cdc_247_pre_.APA_APAOnOff != cdc_247.APA_APAOnOff){
                is_pub = true;
                j_extension_247["CDC_247/APA_APAOnOff"] = cdc_247.APA_APAOnOff;
            }
            if(cdc_247_pre_.APA_EmergenceBrake != cdc_247.APA_EmergenceBrake){
                is_pub = true;
                j_extension_247["CDC_247/APA_EmergenceBrake"] = cdc_247.APA_EmergenceBrake;
            }
            if(cdc_247_pre_.APA_SteeringAngleReq != cdc_247.APA_SteeringAngleReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_SteeringAngleReq"] = cdc_247.APA_SteeringAngleReq;
            }
            if(cdc_247_pre_.APA_RemoteOnOff != cdc_247.APA_RemoteOnOff){
                is_pub = true;
                j_extension_247["CDC_247/APA_RemoteOnOff"] = cdc_247.APA_RemoteOnOff;
            }
            if(cdc_247_pre_.APA_ButtonPress != cdc_247.APA_ButtonPress){
                is_pub = true;
                j_extension_247["CDC_247/APA_ButtonPress"] = cdc_247.APA_ButtonPress;
            }
            if(cdc_247_pre_.APA_IncreasePressureReq != cdc_247.APA_IncreasePressureReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_IncreasePressureReq"] = cdc_247.APA_IncreasePressureReq;
            }
            if(cdc_247_pre_.APA_TurnLightsCommand != cdc_247.APA_TurnLightsCommand){
                is_pub = true;
                j_extension_247["CDC_247/APA_TurnLightsCommand"] = cdc_247.APA_TurnLightsCommand;
            }
            if(cdc_247_pre_.APA_ParkNotice_4 != cdc_247.APA_ParkNotice_4){
                is_pub = true;
                j_extension_247["CDC_247/APA_ParkNotice_4"] = cdc_247.APA_ParkNotice_4;
            }
            if(cdc_247_pre_.APA_ParkNotice != cdc_247.APA_ParkNotice){
                is_pub = true;
                j_extension_247["CDC_247/APA_ParkNotice"] = cdc_247.APA_ParkNotice;
            }
            if(cdc_247_pre_.APA_ParkingPercentage != cdc_247.APA_ParkingPercentage){
                is_pub = true;
                j_extension_247["CDC_247/APA_ParkingPercentage"] = cdc_247.APA_ParkingPercentage;
            }

            if(cdc_247_pre_.APA_EPBrequest != cdc_247.APA_EPBrequest){
                is_pub = true;
                j_extension_247["CDC_247/APA_EPBrequest"] = cdc_247.APA_EPBrequest;
            }
            if(cdc_247_pre_.APA_EPBrequestValid != cdc_247.APA_EPBrequestValid){
                is_pub = true;
                j_extension_247["CDC_247/APA_EPBrequestValid"] = cdc_247.APA_EPBrequestValid;
            }
            if(cdc_247_pre_.APA_TargetAccelerationValid != cdc_247.APA_TargetAccelerationValid){
                is_pub = true;
                j_extension_247["CDC_247/APA_TargetAccelerationValid"] = cdc_247.APA_TargetAccelerationValid;
            }
            if(cdc_247_pre_.APA_TransPRNDShiftRequest != cdc_247.APA_TransPRNDShiftRequest){
                is_pub = true;
                j_extension_247["CDC_247/APA_TransPRNDShiftRequest"] = cdc_247.APA_TransPRNDShiftRequest;
            }
            if(cdc_247_pre_.APA_TransPRNDShiftReqValid != cdc_247.APA_TransPRNDShiftReqValid){
                is_pub = true;
                j_extension_247["CDC_247/APA_TransPRNDShiftReqValid"] = cdc_247.APA_TransPRNDShiftReqValid;
            }
            if(cdc_247_pre_.APA_TargetAcceleration != cdc_247.APA_TargetAcceleration){
                is_pub = true;
                j_extension_247["CDC_247/APA_TargetAcceleration"] = cdc_247.APA_TargetAcceleration;
            }
            if(cdc_247_pre_.APA_EngTorqReq != cdc_247.APA_EngTorqReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_EngTorqReq"] = cdc_247.APA_EngTorqReq;
            }
            if(cdc_247_pre_.APA_Activation_Status != cdc_247.APA_Activation_Status){
                is_pub = true;
                j_extension_247["CDC_247/APA_Activation_Status"] = cdc_247.APA_Activation_Status;
            }
            if(cdc_247_pre_.APA_TransPRNDShiftEnable != cdc_247.APA_TransPRNDShiftEnable){
                is_pub = true;
                j_extension_247["CDC_247/APA_TransPRNDShiftEnable"] = cdc_247.APA_TransPRNDShiftEnable;
            }
            if(cdc_247_pre_.APA_LSCAction != cdc_247.APA_LSCAction){
                is_pub = true;
                j_extension_247["CDC_247/APA_LSCAction"] = cdc_247.APA_LSCAction;
            }
            if(cdc_247_pre_.APA_HSAHDforbidden != cdc_247.APA_HSAHDforbidden){
                is_pub = true;
                j_extension_247["CDC_247/APA_HSAHDforbidden"] = cdc_247.APA_HSAHDforbidden;
            }
            if(cdc_247_pre_.APA_EngineTrqReqEnable != cdc_247.APA_EngineTrqReqEnable){
                is_pub = true;
                j_extension_247["CDC_247/APA_EngineTrqReqEnable"] = cdc_247.APA_EngineTrqReqEnable;
            }
            if(cdc_247_pre_.APA_AccPedShieldReq != cdc_247.APA_AccPedShieldReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_AccPedShieldReq"] = cdc_247.APA_AccPedShieldReq;
            }
            if(cdc_247_pre_.APA_ESPDecompressionModel != cdc_247.APA_ESPDecompressionModel){
                is_pub = true;
                j_extension_247["CDC_247/APA_ESPDecompressionModel"] = cdc_247.APA_ESPDecompressionModel;
            }
            if(cdc_247_pre_.APA_PrefillReq != cdc_247.APA_PrefillReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_PrefillReq"] = cdc_247.APA_PrefillReq;
            }
            if(cdc_247_pre_.APA_DynamicSlotWarning != cdc_247.APA_DynamicSlotWarning){
                is_pub = true;
                j_extension_247["CDC_247/APA_DynamicSlotWarning"] = cdc_247.APA_DynamicSlotWarning;
            }
            if(cdc_247_pre_.APA_SlotNotice != cdc_247.APA_SlotNotice){
                is_pub = true;
                j_extension_247["CDC_247/APA_SlotNotice"] = cdc_247.APA_SlotNotice;
            }
            if(cdc_247_pre_.APA_TCUClutchCombinationReq != cdc_247.APA_TCUClutchCombinationReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_TCUClutchCombinationReq"] = cdc_247.APA_TCUClutchCombinationReq;
            }
            if(cdc_247_pre_.APA_TrqHoldForTCUCl != cdc_247.APA_TrqHoldForTCUCl){
                is_pub = true;
                j_extension_247["CDC_247/APA_TrqHoldForTCUCl"] = cdc_247.APA_TrqHoldForTCUCl;
            }

            if(cdc_247_pre_.APA_PtTorqReq != cdc_247.APA_PtTorqReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_PtTorqReq"] = cdc_247.APA_PtTorqReq;
            }
            if(cdc_247_pre_.APA_ESPDistToStop != cdc_247.APA_ESPDistToStop){
                is_pub = true;
                j_extension_247["CDC_247/APA_ESPDistToStop"] = cdc_247.APA_ESPDistToStop;
            }
            if(cdc_247_pre_.APA_ESP_BrakeFunctionMode != cdc_247.APA_ESP_BrakeFunctionMode){
                is_pub = true;
                j_extension_247["CDC_247/APA_ESP_BrakeFunctionMode"] = cdc_247.APA_ESP_BrakeFunctionMode;
            }
            if(cdc_247_pre_.APA_PtTrqReqValid != cdc_247.APA_PtTrqReqValid){
                is_pub = true;
                j_extension_247["CDC_247/APA_PtTrqReqValid"] = cdc_247.APA_PtTrqReqValid;
            }
            if(cdc_247_pre_.APA_VCUReadyReq != cdc_247.APA_VCUReadyReq){
                is_pub = true;
                j_extension_247["CDC_247/APA_VCUReadyReq"] = cdc_247.APA_VCUReadyReq;
            }
            if(cdc_247_pre_.APA_ESP_StandstillRequest != cdc_247.APA_ESP_StandstillRequest){
                is_pub = true;
                j_extension_247["CDC_247/APA_ESP_StandstillRequest"] = cdc_247.APA_ESP_StandstillRequest;
            }
        }

        cdc_247_pre_ = cdc_247;//用于对比

        j_247["extension"] = "";
        j_247["relative"] = false;
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms
        j_247["time"] = cur_time;
        j_247["valid"] = true;
        j_247["value"] = j_extension_247;
        string topic_247 = pub_topic_247_;
        // AINFO<<__func__<<": "<<topic_247.c_str()<<", is_pub="<<is_pub<<" Pub: "<<j_247.dump().c_str();
        if(true == is_frist){
            is_frist = false;
            megaIpcPublish(topic_247,j_247.dump());
        }
        else{
            if(true == is_pub){
                is_pub = false;
                megaIpcPublish(topic_247,j_247.dump());
            }
        }
    }

    void IOServer::pubMegaIpcMsg2C6(CDC_2C6& cdc_2c6){
        nlohmann::json j_2c6,j_extension_2c6;

        static bool is_pub = false;
        static bool is_frist = true;

        if(true == is_frist){
            j_extension_2c6["CDC_2C6/APA_ParkNotice_5"] = cdc_2c6.APA_ParkNotice_5;
            j_extension_2c6["CDC_2C6/APA_LAEBReq"] = cdc_2c6.APA_LAEBReq;
            j_extension_2c6["CDC_2C6/APA_LAEBStatus"] = cdc_2c6.APA_LAEBStatus;
            j_extension_2c6["CDC_2C6/APA_RemoteParkingUsingRemind"] = cdc_2c6.APA_RemoteParkingUsingRemind;
            j_extension_2c6["CDC_2C6/APA_ASPAvailableStatus"] = cdc_2c6.APA_ASPAvailableStatus;
            j_extension_2c6["CDC_2C6/APA_ASPStatus"] = cdc_2c6.APA_ASPStatus;
            j_extension_2c6["CDC_2C6/APA_CrossModeSelectReq"] = cdc_2c6.APA_CrossModeSelectReq;
            j_extension_2c6["CDC_2C6/APA_BCMHornCommand"] = cdc_2c6.APA_BCMHornCommand;
            j_extension_2c6["CDC_2C6/APA_vehicleFrontdetect"] = cdc_2c6.APA_vehicleFrontdetect;
            j_extension_2c6["CDC_2C6/APA_ReleasePressureReq"] = cdc_2c6.APA_ReleasePressureReq;
            j_extension_2c6["CDC_2C6/APA_PEPS_EngineOffLockRequest"] = cdc_2c6.APA_PEPS_EngineOffLockRequest;
            j_extension_2c6["CDC_2C6/APA_RADSNotice"] = cdc_2c6.APA_RADSNotice;
            j_extension_2c6["CDC_2C6/APA_PEPS_EngineOffRequest"] = cdc_2c6.APA_PEPS_EngineOffRequest;
        }
        else{
            if(cdc_2c6_pre_.APA_ParkNotice_5 != cdc_2c6.APA_ParkNotice_5){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_ParkNotice_5"] = cdc_2c6.APA_ParkNotice_5;
            }
            if(cdc_2c6_pre_.APA_LAEBReq != cdc_2c6.APA_LAEBReq){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_LAEBReq"] = cdc_2c6.APA_LAEBReq;
            }
            if(cdc_2c6_pre_.APA_LAEBStatus != cdc_2c6.APA_LAEBStatus){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_LAEBStatus"] = cdc_2c6.APA_LAEBStatus;
            }
            if(cdc_2c6_pre_.APA_RemoteParkingUsingRemind != cdc_2c6.APA_RemoteParkingUsingRemind){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_RemoteParkingUsingRemind"] = cdc_2c6.APA_RemoteParkingUsingRemind;
            }
            if(cdc_2c6_pre_.APA_ASPAvailableStatus != cdc_2c6.APA_ASPAvailableStatus){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_ASPAvailableStatus"] = cdc_2c6.APA_ASPAvailableStatus;
            }
            if(cdc_2c6_pre_.APA_ASPStatus != cdc_2c6.APA_ASPStatus){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_ASPStatus"] = cdc_2c6.APA_ASPStatus;
            }
            if(cdc_2c6_pre_.APA_CrossModeSelectReq != cdc_2c6.APA_CrossModeSelectReq){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_CrossModeSelectReq"] = cdc_2c6.APA_CrossModeSelectReq;
            }
            if(cdc_2c6_pre_.APA_BCMHornCommand != cdc_2c6.APA_BCMHornCommand){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_BCMHornCommand"] = cdc_2c6.APA_BCMHornCommand;
            }
            if(cdc_2c6_pre_.APA_vehicleFrontdetect != cdc_2c6.APA_vehicleFrontdetect){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_vehicleFrontdetect"] = cdc_2c6.APA_vehicleFrontdetect;
            }
            if(cdc_2c6_pre_.APA_ReleasePressureReq != cdc_2c6.APA_ReleasePressureReq){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_ReleasePressureReq"] = cdc_2c6.APA_ReleasePressureReq;
            }
            if(cdc_2c6_pre_.APA_PEPS_EngineOffLockRequest != cdc_2c6.APA_PEPS_EngineOffLockRequest){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_PEPS_EngineOffLockRequest"] = cdc_2c6.APA_PEPS_EngineOffLockRequest;
            }
            if(cdc_2c6_pre_.APA_RADSNotice != cdc_2c6.APA_RADSNotice){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_RADSNotice"] = cdc_2c6.APA_RADSNotice;
            }
            if(cdc_2c6_pre_.APA_PEPS_EngineOffRequest != cdc_2c6.APA_PEPS_EngineOffRequest){
                is_pub = true;
                j_extension_2c6["CDC_2C6/APA_PEPS_EngineOffRequest"] = cdc_2c6.APA_PEPS_EngineOffRequest;
            }
        }

        cdc_2c6_pre_ = cdc_2c6;//用于对比

        j_2c6["extension"] = "";
        j_2c6["relative"] = false;
        struct timeval tv_2c6;
        gettimeofday(&tv_2c6, NULL);
        long cur_time_2c6 = tv_2c6.tv_sec * 1000 + tv_2c6.tv_usec / 1000;   //conver to ms
        j_2c6["time"] = cur_time_2c6;
        j_2c6["valid"] = true;
        j_2c6["value"] = j_extension_2c6;
        string topic_2c6 = pub_topic_2c6_;
        // AINFO<<__func__<<": "<<topic_2c6.c_str()<<", is_pub="<<is_pub<<" Pub: "<<j_2c6.dump().c_str();
        if(true == is_frist){
            is_frist = false;
            megaIpcPublish(topic_2c6,j_2c6.dump());
        }
        else{
            if(true == is_pub){
                is_pub = false;
                megaIpcPublish(topic_2c6,j_2c6.dump());
            }
        }

    }

    void IOServer::pubMegaIpcMsg2B9(CDC_2B9& cdc_2b9){
        nlohmann::json j_2b9,j_extension_2b9;
        j_extension_2b9["CDC_2B9/APA_FunctionOnOffSts"] = cdc_2b9.APA_FunctionOnOffSts;
        j_extension_2b9["CDC_2B9/APA_Condition_Notice"] = cdc_2b9.APA_Condition_Notice;
        j_extension_2b9["CDC_2B9/APA_TouchInfOnOffRes"] = cdc_2b9.APA_TouchInfOnOffRes;
        j_extension_2b9["CDC_2B9/APA_AVP_Notice"] = cdc_2b9.APA_AVP_Notice;
        j_extension_2b9["CDC_2B9/APA_HZP_Notice"] = cdc_2b9.APA_HZP_Notice;
        j_extension_2b9["CDC_2B9/APA_ViewActual"] = cdc_2b9.APA_ViewActual;
        j_extension_2b9["CDC_2B9/APA_Summon_Notice"] = cdc_2b9.APA_Summon_Notice;
        j_extension_2b9["CDC_2B9/APA_ActivationSts"] = cdc_2b9.APA_ActivationSts;
        j_extension_2b9["CDC_2B9/APA_ReadySts"] = cdc_2b9.APA_ReadySts;
        j_extension_2b9["CDC_2B9/APA_ParkingSlot_ExtraFeature"] = cdc_2b9.APA_ParkingSlot_ExtraFeature;
        j_extension_2b9["CDC_2B9/APA_ParkingSlot_Type"] = cdc_2b9.APA_ParkingSlot_Type;
        j_extension_2b9["CDC_2B9/APA_TurnOnMode"] = cdc_2b9.APA_TurnOnMode;

        j_2b9["extension"] = "";
        j_2b9["relative"] = false;
        struct timeval tv_2b9;
        gettimeofday(&tv_2b9, NULL);
        long cur_time_2b9 = tv_2b9.tv_sec * 1000 + tv_2b9.tv_usec / 1000;   //conver to ms
        j_2b9["time"] = cur_time_2b9;
        j_2b9["valid"] = true;
        j_2b9["value"] = j_extension_2b9;
        string topic_2b9 = pub_topic_2b9_;
        // AINFO<<__func__<<": "<<topic_2b9.c_str()<<" Pub: "<<j_2b9.dump().c_str();
        megaIpcPublish(topic_2b9,j_2b9.dump());
    }

    bool IOServer::initPubMsgBaseDBC(){
        ioConfPtr_.reset(new JsonParser(io_cfg_path_));
        // JsonParser ioConf(io_cfg_path_);
        if (!ioConfPtr_->isValid()) {
            AERROR<<__func__<<" E: Prase json file failed."<<std::endl;
            return false;
        }
        auto io_conf = ioConfPtr_->getJsonParser();
        auto dbc_init = io_conf["DBC_INIT"];
        auto pub_topic = io_conf["Pub_Topic"];
        pub_topic_247_ = pub_topic["topic_247"];
        pub_topic_2c6_ = pub_topic["topic_2c6"];
        pub_topic_601_ = pub_topic["topic_601"];
        pub_topic_2b9_ = pub_topic["topic_2b9"];

        //init CDC_247 base dbc
        initCDC247BaseDBC(dbc_init);

        //init CDC_2C6 base dbc
        initCDC2C6BaseDBC(dbc_init);

        //int CDC_601 base ult doc
        initCDC601BaseDBC(dbc_init);

        //int CDC_2B9 base dbc
        initCDC2B9BaseDBC(dbc_init);

        //init param
        auto param = io_conf["Param"];
        initIOLimitParam(param);
        return true;
    }

    bool IOServer::resetPubMsgBaseDBC(double cur_angle){
        if (!ioConfPtr_->isValid()) {
            AERROR<<__func__<<" E: Prase json file failed.";
            return false;
        }
        auto io_conf = ioConfPtr_->getJsonParser();
        auto dbc_init = io_conf["DBC_INIT"];
        //init CDC_247 base dbc
        initCDC247BaseDBC(dbc_init);
        cdc_247_.APA_SteeringAngleReq = cur_angle;
        //init CDC_2C6 base dbc
        initCDC2C6BaseDBC(dbc_init);
        return true;
    }

    void IOServer::initCDC247BaseDBC(nlohmann::json init_data){
        cdc_247_.APA_SteeringAngleReqProtection = init_data["CDC_247"]["APA_SteeringAngleReqProtection"];
        cdc_247_.APA_ErrorStatus = init_data["CDC_247"]["APA_ErrorStatus"];
        cdc_247_.APA_indication = init_data["CDC_247"]["APA_indication"];
        cdc_247_.APA_APAOnOff = init_data["CDC_247"]["APA_APAOnOff"];
        cdc_247_.APA_EmergenceBrake = init_data["CDC_247"]["APA_EmergenceBrake"];
        cdc_247_.APA_SteeringAngleReq = init_data["CDC_247"]["APA_SteeringAngleReq"];
        cdc_247_.APA_RemoteOnOff = init_data["CDC_247"]["APA_RemoteOnOff"];
        cdc_247_.APA_ButtonPress = init_data["CDC_247"]["APA_ButtonPress"];
        cdc_247_.APA_IncreasePressureReq = init_data["CDC_247"]["APA_IncreasePressureReq"];
        cdc_247_.APA_TurnLightsCommand = init_data["CDC_247"]["APA_TurnLightsCommand"];
        cdc_247_.APA_ParkNotice_4 = init_data["CDC_247"]["APA_ParkNotice_4"];
        cdc_247_.APA_ParkNotice = init_data["CDC_247"]["APA_ParkNotice"];
        cdc_247_.APA_ParkingPercentage = init_data["CDC_247"]["APA_ParkingPercentage"];

        cdc_247_.APA_EPBrequest = init_data["CDC_247"]["APA_EPBrequest"];
        cdc_247_.APA_EPBrequestValid = init_data["CDC_247"]["APA_EPBrequestValid"];
        cdc_247_.APA_TargetAccelerationValid = init_data["CDC_247"]["APA_TargetAccelerationValid"];
        cdc_247_.APA_TransPRNDShiftRequest = init_data["CDC_247"]["APA_TransPRNDShiftRequest"];
        cdc_247_.APA_TransPRNDShiftReqValid = init_data["CDC_247"]["APA_TransPRNDShiftReqValid"];
        cdc_247_.APA_TargetAcceleration = init_data["CDC_247"]["APA_TargetAcceleration"];
        cdc_247_.APA_EngTorqReq = init_data["CDC_247"]["APA_EngTorqReq"];
        cdc_247_.APA_Activation_Status = init_data["CDC_247"]["APA_Activation_Status"];
        cdc_247_.APA_TransPRNDShiftEnable = init_data["CDC_247"]["APA_TransPRNDShiftEnable"];
        cdc_247_.APA_LSCAction = init_data["CDC_247"]["APA_LSCAction"];
        cdc_247_.APA_HSAHDforbidden = init_data["CDC_247"]["APA_HSAHDforbidden"];
        cdc_247_.APA_EngineTrqReqEnable = init_data["CDC_247"]["APA_EngineTrqReqEnable"];
        cdc_247_.APA_AccPedShieldReq = init_data["CDC_247"]["APA_AccPedShieldReq"];
        cdc_247_.APA_ESPDecompressionModel = init_data["CDC_247"]["APA_ESPDecompressionModel"];
        cdc_247_.APA_PrefillReq = init_data["CDC_247"]["APA_PrefillReq"];
        cdc_247_.APA_DynamicSlotWarning = init_data["CDC_247"]["APA_DynamicSlotWarning"];
        cdc_247_.APA_SlotNotice = init_data["CDC_247"]["APA_SlotNotice"];
        cdc_247_.APA_TCUClutchCombinationReq = init_data["CDC_247"]["APA_TCUClutchCombinationReq"];
        cdc_247_.APA_TrqHoldForTCUCl = init_data["CDC_247"]["APA_TrqHoldForTCUCl"];

        cdc_247_.APA_PtTorqReq = init_data["CDC_247"]["APA_PtTorqReq"];
        cdc_247_.APA_ESPDistToStop = init_data["CDC_247"]["APA_ESPDistToStop"];
        cdc_247_.APA_ESP_BrakeFunctionMode = init_data["CDC_247"]["APA_ESP_BrakeFunctionMode"];
        cdc_247_.APA_PtTrqReqValid = init_data["CDC_247"]["APA_PtTrqReqValid"];
        cdc_247_.APA_VCUReadyReq = init_data["CDC_247"]["APA_VCUReadyReq"];
        cdc_247_.APA_ESP_StandstillRequest = init_data["CDC_247"]["APA_ESP_StandstillRequest"];
    }

    void IOServer::initCDC2C6BaseDBC(nlohmann::json init_data){
        cdc_2c6_.APA_ParkNotice_5 = init_data["CDC_2C6"]["APA_ParkNotice_5"];
        cdc_2c6_.APA_LAEBReq = init_data["CDC_2C6"]["APA_LAEBReq"];
        cdc_2c6_.APA_LAEBStatus = init_data["CDC_2C6"]["APA_LAEBStatus"];
        cdc_2c6_.APA_RemoteParkingUsingRemind = init_data["CDC_2C6"]["APA_RemoteParkingUsingRemind"];
        cdc_2c6_.APA_ASPAvailableStatus = init_data["CDC_2C6"]["APA_ASPAvailableStatus"];
        cdc_2c6_.APA_ASPStatus = init_data["CDC_2C6"]["APA_ASPStatus"];
        cdc_2c6_.APA_CrossModeSelectReq = init_data["CDC_2C6"]["APA_CrossModeSelectReq"];
        cdc_2c6_.APA_BCMHornCommand = init_data["CDC_2C6"]["APA_BCMHornCommand"];
        cdc_2c6_.APA_vehicleFrontdetect = init_data["CDC_2C6"]["APA_vehicleFrontdetect"];
        cdc_2c6_.APA_ReleasePressureReq = init_data["CDC_2C6"]["APA_ReleasePressureReq"];
        cdc_2c6_.APA_PEPS_EngineOffLockRequest = init_data["CDC_2C6"]["APA_PEPS_EngineOffLockRequest"];
        cdc_2c6_.APA_RADSNotice = init_data["CDC_2C6"]["APA_RADSNotice"];
        cdc_2c6_.APA_PEPS_EngineOffRequest = init_data["CDC_2C6"]["APA_PEPS_EngineOffRequest"];
    }

    void IOServer::initCDC601BaseDBC(nlohmann::json init_data){
        cdc_601_.VCU_Distance_CMD = init_data["CDC_601"]["VCU_Distance_CMD"];
        cdc_601_.VCU_ProbeCMD_NUM9 = init_data["CDC_601"]["VCU_ProbeCMD_NUM9"];
        cdc_601_.VCU_ProbeCMD_NUM10 = init_data["CDC_601"]["VCU_ProbeCMD_NUM10"];
        cdc_601_.VCU_ProbeCMD_NUM11 = init_data["CDC_601"]["VCU_ProbeCMD_NUM11"];
        cdc_601_.VCU_ProbeCMD_NUM12 = init_data["CDC_601"]["VCU_ProbeCMD_NUM12"];
        cdc_601_.VCU_Machine_NUM = init_data["CDC_601"]["VCU_Machine_NUM"];
        cdc_601_.VCU_ProbeCMD_NUM1 = init_data["CDC_601"]["VCU_ProbeCMD_NUM1"];
        cdc_601_.VCU_ProbeCMD_NUM2 = init_data["CDC_601"]["VCU_ProbeCMD_NUM2"];
        cdc_601_.VCU_ProbeCMD_NUM3 = init_data["CDC_601"]["VCU_ProbeCMD_NUM3"];
        cdc_601_.VCU_ProbeCMD_NUM4 = init_data["CDC_601"]["VCU_ProbeCMD_NUM4"];
        cdc_601_.VCU_ProbeCMD_NUM5 = init_data["CDC_601"]["VCU_ProbeCMD_NUM5"];
        cdc_601_.VCU_ProbeCMD_NUM6 = init_data["CDC_601"]["VCU_ProbeCMD_NUM6"];
        cdc_601_.VCU_ProbeCMD_NUM7 = init_data["CDC_601"]["VCU_ProbeCMD_NUM7"];
        cdc_601_.VCU_ProbeCMD_NUM8 = init_data["CDC_601"]["VCU_ProbeCMD_NUM8"];
    }

    void IOServer::initCDC2B9BaseDBC(nlohmann::json init_data){
        cdc_2b9_.APA_FunctionOnOffSts = init_data["CDC_2B9"]["APA_FunctionOnOffSts"];
        cdc_2b9_.APA_Condition_Notice = init_data["CDC_2B9"]["APA_Condition_Notice"];
        cdc_2b9_.APA_TouchInfOnOffRes = init_data["CDC_2B9"]["APA_TouchInfOnOffRes"];
        cdc_2b9_.APA_AVP_Notice = init_data["CDC_2B9"]["APA_AVP_Notice"];
        cdc_2b9_.APA_HZP_Notice = init_data["CDC_2B9"]["APA_HZP_Notice"];
        cdc_2b9_.APA_ViewActual = init_data["CDC_2B9"]["APA_ViewActual"];
        cdc_2b9_.APA_Summon_Notice = init_data["CDC_2B9"]["APA_Summon_Notice"];
        cdc_2b9_.APA_ActivationSts = init_data["CDC_2B9"]["APA_ActivationSts"];
        cdc_2b9_.APA_ReadySts = init_data["CDC_2B9"]["APA_ReadySts"];
        cdc_2b9_.APA_ParkingSlot_ExtraFeature = init_data["CDC_2B9"]["APA_ParkingSlot_ExtraFeature"];
        cdc_2b9_.APA_ParkingSlot_Type = init_data["CDC_2B9"]["APA_ParkingSlot_Type"];
        cdc_2b9_.APA_TurnOnMode = init_data["CDC_2B9"]["APA_TurnOnMode"];
    }

    void IOServer::initIOLimitParam(nlohmann::json param){
        steering_rate_limit_ = param["steering_rate_limit"];
        acc_set_ = param["acc_set"];
        apa_esp_breakfunctionmode_ = param["apa_esp_breakfunctionmode"];
        filter_window_size_ = param["filter_window_size"];
        filter_pickup_index_ = param["filter_pickup_index"];
        uss_valid_distance_cm_ = param["uss_valid_distance_cm"];
    }

    //handshake 每次握手是否需要重置？
    void IOServer::handShakeStatusInit(){
        EPS_handshake_ = HandShakeStatus::DEFAULT;
        ESP_handshake_ = HandShakeStatus::DEFAULT;
        VCU_handshake_ = HandShakeStatus::DEFAULT;
        Gear_handshake_ = HandShakeStatus::DEFAULT;
    }

    void IOServer::EPSHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::DEFAULT == EPS_handshake_ || HandShakeStatus::HANDSHAKEQUIT == EPS_handshake_){
            cdc_247_.APA_SteeringAngleReqProtection = 0x1;//Request EPS Control;
            EPS_handshake_ = HandShakeStatus::HANDSHAKEREQ;
        }
        if(HandShakeStatus::HANDSHAKEREQ == EPS_handshake_){
            if(0x1 == io_chassis_info_debug.eps_apa_control_feedback){//Control enabled
                if(0x2 == cdc_247_.APA_SteeringAngleReqProtection && 0 == io_chassis_info_debug.eps_apa_epasfailed){
                    EPS_handshake_ = HandShakeStatus::HANDSHAKESUS;
                }
                cdc_247_.APA_SteeringAngleReqProtection = 0x2;//EPS control active
                cdc_247_.APA_SteeringAngleReq = io_chassis_info_debug.steering_angle;
                steering_angle_pre_ = io_chassis_info_debug.steering_angle;
            }
        }
    }

    void IOServer::EPSHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::HANDSHAKESUS == EPS_handshake_){
            cdc_247_.APA_SteeringAngleReqProtection = 0x0;//No Request;
            EPS_handshake_ = HandShakeStatus::HANDSHAKEREQQUIT;
        }
        if(HandShakeStatus::HANDSHAKEREQQUIT == EPS_handshake_){
            if(0x0 == io_chassis_info_debug.eps_apa_control_feedback){//Control disable
                EPS_handshake_ = HandShakeStatus::HANDSHAKEQUIT;
            }
        }
    }

    void IOServer::ESPHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::DEFAULT == ESP_handshake_  || HandShakeStatus::HANDSHAKEQUIT == ESP_handshake_){
            if(0x1 == io_chassis_info_debug.ibcu_apc_reducedfunc_avail
                && 0x1 == io_chassis_info_debug.ibcu_apc_fullfunc_avail ){//available
                ESP_handshake_ = HandShakeStatus::COUNTERPARTSTATUS;
            }
        }
        if(HandShakeStatus::COUNTERPARTSTATUS == ESP_handshake_){
            // cdc_247_.APA_ESP_BrakeFunctionMode = 0x1;//Reduce Function
            cdc_247_.APA_ESP_BrakeFunctionMode = apa_esp_breakfunctionmode_;//Full Function
            ESP_handshake_ = HandShakeStatus::HANDSHAKEREQ;
        }
        if(HandShakeStatus::HANDSHAKEREQ == ESP_handshake_){
            if(0x1 == io_chassis_info_debug.ibcu_apc_reducedfunc_avail
                && 0x1 == io_chassis_info_debug.ibcu_apc_fullfunc_avail ){//Reduce Function Active
                ESP_handshake_ = HandShakeStatus::HANDSHAKESUS;
            }
        }
    }

    void IOServer::ESPHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_247_.APA_ESP_BrakeFunctionMode = 0x0;//None
            ESP_handshake_ = HandShakeStatus::HANDSHAKEREQQUIT;
        }
        if(HandShakeStatus::HANDSHAKEREQQUIT == ESP_handshake_){
            // if(0x0 == io_chassis_info_debug.ibcu_apc_active_status){//Deactive
                ESP_handshake_ = HandShakeStatus::HANDSHAKEQUIT;
            // }
        }
    }

    void IOServer::VCUHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::DEFAULT == VCU_handshake_ || HandShakeStatus::HANDSHAKEQUIT == VCU_handshake_){
            if(0x1 == io_chassis_info_debug.vcu_apa_torq_requesta_vailable){//available
                VCU_handshake_ = HandShakeStatus::COUNTERPARTSTATUS;
            }
        }
        if(HandShakeStatus::COUNTERPARTSTATUS == VCU_handshake_){
            cdc_247_.APA_LSCAction = 0x1;//On
            cdc_247_.APA_AccPedShieldReq = 0x1;//Shield
            VCU_handshake_ = HandShakeStatus::HANDSHAKEREQ;
        }
        if(HandShakeStatus::HANDSHAKEREQ == VCU_handshake_){
            if(0x1 == io_chassis_info_debug.vcu_accped_shield &&
                0x1 == io_chassis_info_debug.vcu_rdy_sts){//Shield && Ready
                VCU_handshake_ = HandShakeStatus::HANDSHAKESUS;
            }
        }
    }

    void IOServer::VCUHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::HANDSHAKESUS == VCU_handshake_){
            cdc_247_.APA_LSCAction = 0x0;//OFF
            cdc_247_.APA_AccPedShieldReq = 0x0;//Unshield
            VCU_handshake_ = HandShakeStatus::HANDSHAKEREQQUIT;
        }
        if(HandShakeStatus::HANDSHAKEREQQUIT == VCU_handshake_){
            if(0x0 == io_chassis_info_debug.vcu_accped_shield ){//Unshield && no Ready
                VCU_handshake_ = HandShakeStatus::HANDSHAKEQUIT;
            }
        }
    }

    //VCUAPARequestEnable 0x0:no request 0x1:Control enabled 0x2:Control disable 0x3:invalid
    void IOServer::GearHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::DEFAULT == Gear_handshake_ || HandShakeStatus::HANDSHAKEQUIT == Gear_handshake_){
            cdc_247_.APA_LSCAction = 0x1;//On
            cdc_247_.APA_TransPRNDShiftEnable = 0x1;//Enable
            Gear_handshake_ = HandShakeStatus::HANDSHAKEREQ;
        }
        if(HandShakeStatus::HANDSHAKEREQ == Gear_handshake_){
            if(0x1 == io_chassis_info_debug.vcu_apa_request_enable){//Control enabled
                Gear_handshake_ = HandShakeStatus::HANDSHAKESUS;
            }
        }
    }

    void IOServer::GearHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::HANDSHAKESUS == Gear_handshake_){
            // cdc_247_.APA_LSCAction = 0x0;//On
            cdc_247_.APA_TransPRNDShiftEnable = 0x0;//Not Enable
            Gear_handshake_ = HandShakeStatus::HANDSHAKEREQQUIT;
        }
        if(HandShakeStatus::HANDSHAKEREQQUIT == Gear_handshake_){
            if(0x0 == io_chassis_info_debug.vcu_apa_request_enable){//Control disable
                Gear_handshake_ = HandShakeStatus::HANDSHAKEQUIT;
            }
        }
    }

    //control

    void IOServer::pressureMaintain(double deceleration){
        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_247_.APA_TargetAccelerationValid = 0x1;
            if(deceleration>0) {deceleration = -deceleration;}
            cdc_247_.APA_TargetAcceleration = deceleration;
        }
    }

    // EpbSts 0x0:both brakes released 0x1:both brakes applied
    //                0x2:both brakes Releasing 0x3:both brakes Locking 0x4:unknown
    //APA_EPBrequest 0x0:No Request 0x1:RequestBrake 0x2:RequestRelase
    void IOServer::braeakOrReleaseEPB(int epb_req){
        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_247_.APA_EPBrequestValid = 0x1;
            cdc_247_.APA_EPBrequest = epb_req;
        }
    }

    /*
    0x0:No Request 0x1:Park  0x2:Reverse
    0x3:Neutral 0x4:Drive 0x5:invalid 0x6~0x7:reserved
    */
    void IOServer::transPRNDShift(uint32_t req_gear){
        if(HandShakeStatus::HANDSHAKESUS == VCU_handshake_){
            // if(APATransPRNDShiftRequest::NOREQUEST == req_gear){

            // }
            cdc_247_.APA_TransPRNDShiftReqValid = 0x1;
            cdc_247_.APA_TransPRNDShiftRequest = req_gear;
        }
    }

    /*
    APA_ReleasePressureReq: 0x0:No Request 0x1:Request
    */
    void IOServer::startingPressureRelief(double acc,uint32_t req){
        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_2c6_.APA_ReleasePressureReq = req;
            cdc_247_.APA_TargetAccelerationValid = 0x1;
            if(1 == req){
                if(acc<0){acc=-acc;}
                cdc_247_.APA_TargetAcceleration = acc;//0.25
            }
        }
    }

    void IOServer::torqueControl(int req_torq,const StruIOChassisInfoDebug& io_chassis_info_debug, double acc ){
        if(HandShakeStatus::HANDSHAKESUS == VCU_handshake_
            && HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_247_.APA_TargetAccelerationValid = 0x1;
            if(acc<0){acc=-acc;}
            cdc_247_.APA_TargetAcceleration = acc;//0.25

            cdc_247_.APA_PtTrqReqValid = 0x1;
            req_torq = my_clamp(req_torq,io_chassis_info_debug.min_torque,io_chassis_info_debug.max_torque);
            cdc_247_.APA_PtTorqReq = req_torq;
        }
    }

    /*
    APA_SteeringAngleReqProtection:
    0x0:No Request 0x1:Request EPS control 0x2:EPS control active
    */
    void IOServer::steeringAngleControl(double req_angle,const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(HandShakeStatus::HANDSHAKESUS == EPS_handshake_){
            // static double steering_angle_pre = io_chassis_info_debug.steering_angle;
            cdc_247_.APA_SteeringAngleReqProtection = 0x2;
            if(req_angle > steering_angle_pre_ + steering_rate_limit_){//单个周期20ms(247周期)内变化率小于20°/20ms
                req_angle = steering_angle_pre_ + steering_rate_limit_;
            }
            else if(req_angle < steering_angle_pre_ - steering_rate_limit_){
                req_angle = steering_angle_pre_ - steering_rate_limit_;
            }
            cdc_247_.APA_SteeringAngleReq = req_angle;
            steering_angle_pre_ = req_angle;
        }
    }

    void IOServer::decelerationControl(double deceleration){
        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_247_.APA_TargetAccelerationValid = 0x1;
            if(deceleration>0) {deceleration = -deceleration;}
            cdc_247_.APA_TargetAcceleration = deceleration;
        }
    }

    void IOServer::standstillControl(double deceleration, int cmd_req){
        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_){
            cdc_247_.APA_TargetAccelerationValid = 0x1;
            if(deceleration>0) {deceleration = -deceleration;}
            cdc_247_.APA_TargetAcceleration = deceleration;
            cdc_247_.APA_ESP_StandstillRequest = cmd_req;//0: not request 1: request
        }
        else{
            AINFO<<__func__<<" failed, ESP_handshake_="<<static_cast<int>(ESP_handshake_);
        }
    }

    /*
    EspAutoHoldActvSts:0x0:Not active 0x1:active
    APA_HSAHDforbidden:0x0:Not request 0x1:Request
    */
    void IOServer::autoHoldForbidden(const StruIOChassisInfoDebug& io_chassis_info_debug, int hsahd_req){
        // if(0x1 == io_chassis_info_debug.esp_autohold_active_sts){
            // cdc_247_.APA_HSAHDforbidden = 0x1;
            cdc_247_.APA_HSAHDforbidden = hsahd_req;
        // }
    }

    /*
    APA_EmergenceBrake: 0x0:Not request 0x1:Request
    */
    void IOServer::emergencyBrake(){
        cdc_247_.APA_EmergenceBrake = 0x1;
    }

    //处理保存的topic数据，解析为cyber消息
    void IOServer::processSavedTopicData(){
        if(1 == icmsg_650_.valid){
            // output_data_imu_info_.measurement_time = static_cast<double>(icmsg_650_.INS_IMU_Time)/1000.0;
            output_data_imu_info_.measurement_time = static_cast<double>(icmsg_650_.ICMSG_TimeStamp)/1000.0;
            // output_data_imu_info_.measurement_span = 0.0;
            output_data_imu_info_.linear_acceleration.x = icmsg_650_.INS_Current_Pos_X_Accel;
            output_data_imu_info_.linear_acceleration.y = icmsg_650_.INS_Current_Pos_Y_Accel;
            output_data_imu_info_.linear_acceleration.z = icmsg_650_.INS_Current_Pos_Z_Accel;
            output_data_imu_info_.angular_velocity.x = icmsg_650_.INS_Current_Pos_X_Rate;
            output_data_imu_info_.angular_velocity.y = icmsg_650_.INS_Current_Pos_Y_Rate;
            output_data_imu_info_.angular_velocity.z = icmsg_650_.INS_Current_Pos_Z_Rate;
            // output_data_imu_info_.location = ;
            // output_data_imu_info_.rotation.x = ;
            // output_data_imu_info_.rotation.y = ;
            // output_data_imu_info_.rotation.z = ;
        }

        if(1 == icmsg_651_.valid){
            if(0 == icmsg_651_.EpsSasSteerAgVld ){
                output_data_chassis_.LatDa_steering_angle = icmsg_651_.EpsSasSteerAg;
            }
            output_data_chassis_.LatDa_steering_velocity = icmsg_651_.EpsSteerAgRate;

            if(0 == icmsg_651_.VcuPtTqLimMinVld){//无效如何处理？
                output_data_chassis_.LonDa_min_torque = icmsg_651_.VcuPtTqLimMin;
            }
            if(0 == icmsg_651_.VcuPtTqLimMaxVld){//无效如何处理？
                output_data_chassis_.LonDa_max_torque = icmsg_651_.VcuPtTqLimMax;
            }
            if(0 == icmsg_651_.VcuPtTqRealVld){//无效如何处理？
                output_data_chassis_.LonDa_cur_torque = icmsg_651_.VcuPtTqReal;
            }
            output_data_chassis_.LonDa_gear_pos = icmsg_651_.VcuGearPosn;

            if(0x0 == icmsg_651_.VcuRdySts){
                output_data_system_check_.is_check_ok = CommonBool::FALSE;
                output_data_system_check_.check_err_reason = SystemCheckErrReason::VCU_NOT_READY;
            }
            else if(0x1 == icmsg_651_.VcuRdySts){
                output_data_system_check_.is_check_ok = CommonBool::TRUE;
                output_data_system_check_.check_err_reason = SystemCheckErrReason::NONE;
            }
        }

        if(1 == icmsg_652_.valid){
            if(0 == icmsg_652_.EspVehStandstill){
                output_data_chassis_.LonDa_stand_still = CommonBool::FALSE;
            }
            else if(1 == icmsg_652_.EspVehStandstill){
                output_data_chassis_.LonDa_stand_still = CommonBool::TRUE;
            }

        }

        if(1 == icmsg_653_.valid){
            output_data_chassis_.LatDa_steering_torque = icmsg_653_.EPS_SteeringTorque;

            output_data_wheel_speed_.measurement_time = static_cast<double>(icmsg_653_.ICMSG_TimeStamp)/1000.0;
            output_data_wheel_speed_.valid = CommonBool::TRUE;
            if(0 == icmsg_653_.WhlSpdLeFrntDataVld){
                output_data_wheel_speed_.wheelspeed_fl = icmsg_653_.WhlSpdLeFrntData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdRiFrntDataVld){
                output_data_wheel_speed_.wheelspeed_fr = icmsg_653_.WhlSpdRiFrntData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdLeReDataVld){
                output_data_wheel_speed_.wheelspeed_rl = icmsg_653_.WhlSpdLeReData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdRiReDataVld){
                output_data_wheel_speed_.wheelspeed_rr = icmsg_653_.WhlSpdRiReData/3.6;
            }
            output_data_wheel_speed_.wheelsign_fl = changanToWMWheelDir(icmsg_653_.WhlSpdLeFrntDir);
            output_data_wheel_speed_.wheelsign_fr = changanToWMWheelDir(icmsg_653_.WhlSpdRiFrntDir);
            output_data_wheel_speed_.wheelsign_rl = changanToWMWheelDir(icmsg_653_.WhlSpdLeReDir);
            output_data_wheel_speed_.wheelsign_rr = changanToWMWheelDir(icmsg_653_.WhlSpdRiReDir);

            output_data_wheel_speed_.wheel_edgessum_fl = icmsg_653_.WhlSpdFrntLePls;
            output_data_wheel_speed_.wheel_edgessum_fr = icmsg_653_.WhlSpdFrntRiPls;
            output_data_wheel_speed_.wheel_edgessum_rl = icmsg_653_.WhlSpdReLePls;
            output_data_wheel_speed_.wheel_edgessum_rr = icmsg_653_.WhlSpdReRiPls;

            if(1 == icmsg_653_.EpbSts){//break
                output_data_chassis_.epb_sys_st = CommonBool::TRUE;
            }else{
                output_data_chassis_.epb_sys_st = CommonBool::FALSE;
            }

            if(0 == icmsg_653_.WhlSpdLeReDataVld && 0 == icmsg_653_.WhlSpdRiReDataVld){
                double _WhlSpdLeReData,_WhlSpdRiReData;
                _WhlSpdLeReData = (1 == icmsg_653_.WhlSpdLeReDir)?(-icmsg_653_.WhlSpdLeReData):icmsg_653_.WhlSpdLeReData;
                _WhlSpdRiReData = (1 == icmsg_653_.WhlSpdRiReDir)?(-icmsg_653_.WhlSpdRiReData):icmsg_653_.WhlSpdRiReData;
                output_data_chassis_.LonDa_speed = (_WhlSpdLeReData + _WhlSpdRiReData)/2.0/3.6;
            }

            if(0x1 == icmsg_653_.ESP_APA_DriverOverride){
                output_data_chassis_.LonSt_esp_valid = CommonBool::FALSE;
                output_data_chassis_.LonSt_esp_err_reason = EspErrReason::HUMAN_TAKE_OVER;
            }
            else if(0x0 == icmsg_653_.ESP_APA_DriverOverride){
                output_data_chassis_.LonSt_esp_valid = CommonBool::TRUE;
                output_data_chassis_.LonSt_esp_err_reason = EspErrReason::NONE;
            }

            if(1 == icmsg_653_.IBCU_PlungerBrakePressureValid){
                output_data_chassis_.LonDa_ibcu_pressure = icmsg_653_.IBCU_PlungerBrakePressure;
            }
        }
        else{
            output_data_wheel_speed_.valid = CommonBool::FALSE;
        }

        if(1 == icmsg_654_.valid){
            ult_enable_ = 2;
            icmsg_654_.Ult_Probe_info1 = processUltDistance(icmsg_654_.Ult_Probe_info1);
            icmsg_654_.Ult_Probe_info2 = processUltDistance(icmsg_654_.Ult_Probe_info2);
            icmsg_654_.Ult_Probe_info3 = processUltDistance(icmsg_654_.Ult_Probe_info3);
            icmsg_654_.Ult_Probe_info4 = processUltDistance(icmsg_654_.Ult_Probe_info4);
            icmsg_654_.Ult_Probe_info5 = processUltDistance(icmsg_654_.Ult_Probe_info5);
            icmsg_654_.Ult_Probe_info6 = processUltDistance(icmsg_654_.Ult_Probe_info6);
            icmsg_654_.Ult_Probe_info7 = processUltDistance(icmsg_654_.Ult_Probe_info7);
            icmsg_654_.Ult_Probe_info8 = processUltDistance(icmsg_654_.Ult_Probe_info8);
            icmsg_654_.Ult_Probe_info9 = processUltDistance(icmsg_654_.Ult_Probe_info9);
            icmsg_654_.Ult_Probe_info10 = processUltDistance(icmsg_654_.Ult_Probe_info10);
            icmsg_654_.Ult_Probe_info11 = processUltDistance(icmsg_654_.Ult_Probe_info11);
            icmsg_654_.Ult_Probe_info12 = processUltDistance(icmsg_654_.Ult_Probe_info12);
        }

        output_data_chassis_.LonSt_epb_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_epb_err_reason = EpbErrReason::NONE;
        output_data_chassis_.LonSt_vcu_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_vcu_err_reason = VcuErrReason::NONE;

        if(HandShakeStatus::HANDSHAKESUS == ESP_handshake_ &&
        HandShakeStatus::HANDSHAKESUS == VCU_handshake_ &&
        HandShakeStatus::HANDSHAKESUS == Gear_handshake_){
            output_data_shake_hand_.long_active = CommonBool::TRUE;
            output_data_shake_hand_.long_shakehand_err_type = CommonErrorType::NONE;
            output_data_shake_hand_.long_shakehand_err_reason = LonShakeHandErrReason::NONE;
        }
        else{
            output_data_shake_hand_.long_active = CommonBool::FALSE;
        }
        if(HandShakeStatus::HANDSHAKESUS == EPS_handshake_){
            output_data_shake_hand_.lat_active = CommonBool::TRUE;
            output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::NONE;
            output_data_shake_hand_.lat_shakehand_err_reason = LatShakeHandErrReason::NONE;
        }
        else{
            output_data_shake_hand_.lat_active = CommonBool::FALSE;
        }

        //chassis debug info
        std::unique_lock<std::mutex> prase_data_lock(prase_data_mtx_, std::defer_lock);
        prase_data_lock.lock();

        if(1 == icmsg_650_.valid){
            io_chassis_info_debug_.icmsg650_timestamp = icmsg_650_.ICMSG_TimeStamp;
            io_chassis_info_debug_.linear_acceleration.x = icmsg_650_.INS_Current_Pos_X_Accel;
            io_chassis_info_debug_.linear_acceleration.y = icmsg_650_.INS_Current_Pos_Y_Accel;
            io_chassis_info_debug_.linear_acceleration.z = icmsg_650_.INS_Current_Pos_Z_Accel;
            io_chassis_info_debug_.angular_velocity.x = icmsg_650_.INS_Current_Pos_X_Rate;
            io_chassis_info_debug_.angular_velocity.y = icmsg_650_.INS_Current_Pos_Y_Rate;
            io_chassis_info_debug_.angular_velocity.z = icmsg_650_.INS_Current_Pos_Z_Rate;
            io_chassis_info_debug_.quaternions.w = icmsg_650_.INS_Quaternion_W;
            io_chassis_info_debug_.quaternions.x = icmsg_650_.INS_Quaternion_X;
            io_chassis_info_debug_.quaternions.y = icmsg_650_.INS_Quaternion_Y;
            io_chassis_info_debug_.quaternions.z = icmsg_650_.INS_Quaternion_Z;
            io_chassis_info_debug_.ins_roll_mis_angle_to_veh = icmsg_650_.INS_Roll_Mis_Angle_to_Veh;
            io_chassis_info_debug_.ins_pitch_mis_angle_to_veh = icmsg_650_.INS_Pitch_Mis_Angle_to_Veh;
            io_chassis_info_debug_.ins_yaw_mis_angle_to_veh = icmsg_650_.INS_Yaw_Mis_Angle_to_Veh;
        }

        if(1 == icmsg_651_.valid){
            io_chassis_info_debug_.icmsg651_timestamp = icmsg_651_.ICMSG_TimeStamp;
            if(0 == icmsg_651_.VcuPtTqLimMinVld){//无效如何处理？
                io_chassis_info_debug_.min_torque = icmsg_651_.VcuPtTqLimMin;
            }
            else{AINFO<<__func__<<": VcuPtTqLimMinVld invalid.";}
            if(0 == icmsg_651_.VcuPtTqLimMaxVld){//无效如何处理？
                io_chassis_info_debug_.max_torque = icmsg_651_.VcuPtTqLimMax;
            }
            else{AINFO<<__func__<<": VcuPtTqLimMaxVld invalid.";}
            if(0 == icmsg_651_.VcuPtTqRealVld){//无效如何处理？
                io_chassis_info_debug_.cur_torque = icmsg_651_.VcuPtTqReal;
            }
            else{AINFO<<__func__<<": VcuPtTqRealVld invalid.";}
            io_chassis_info_debug_.gear_pos = icmsg_651_.VcuGearPosn;
            io_chassis_info_debug_.vcu_apa_torq_requesta_vailable = icmsg_651_.VcuAPATorqRequestAvailable;
            io_chassis_info_debug_.vcu_rdy_sts = icmsg_651_.VcuRdySts;
            io_chassis_info_debug_.vcu_accped_shield = icmsg_651_.VCUAccPedShield;
            io_chassis_info_debug_.vcu_apa_request_enable = icmsg_651_.VCUAPARequestEnable;

            if(0 == icmsg_651_.EpsSasSteerAgVld ){
                io_chassis_info_debug_.steering_angle = icmsg_651_.EpsSasSteerAg;
            }
            else{AINFO<<__func__<<": EpsSasSteerAgVld invalid.";}
            io_chassis_info_debug_.steering_velocity = icmsg_651_.EpsSteerAgRate;
            io_chassis_info_debug_.vcu_com_flt_sts = icmsg_651_.VcuComFltSts;
            io_chassis_info_debug_.vcu_apa_driver_Interruption = icmsg_651_.VCUAPAdriverInterruption;
        }

        if(1 == icmsg_652_.valid){
            io_chassis_info_debug_.icmsg652_timestamp = icmsg_652_.ICMSG_TimeStamp;
            if(0 == icmsg_652_.EspVehSpdVld){
                io_chassis_info_debug_.velocity = icmsg_652_.EspVehSpd/3.6;
            }
            else{AINFO<<__func__<<": EspVehSpdVld invalid.";}

            io_chassis_info_debug_.esp_autohold_active_sts = icmsg_652_.EspAutoHoldActvSts;
            io_chassis_info_debug_.stand_still = icmsg_652_.EspVehStandstill;
            io_chassis_info_debug_.ibcu_communication_invalid = icmsg_652_.IBCU_CommunicationInvalid;
        }

        if(1 == icmsg_653_.valid){
            io_chassis_info_debug_.icmsg653_timestamp = icmsg_653_.ICMSG_TimeStamp;
            struct timeval tv;
            gettimeofday(&tv, NULL);
            long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms

            io_chassis_info_debug_.eps_apa_abort_feedback = icmsg_653_.EPS_APA_Abortfeedback;
            io_chassis_info_debug_.steering_torque = icmsg_653_.EPS_SteeringTorque;
            io_chassis_info_debug_.eps_apa_epasfailed = icmsg_653_.EPS_APA_EpasFAILED;
            io_chassis_info_debug_.ibcu_adc_rampoff_suspendstate = icmsg_653_.IBCU_ADCRampOffSuspendState;

            static int EPS_APA_EpasFAILED_last = icmsg_653_.EPS_APA_ControlFeedback;
            if(EPS_APA_EpasFAILED_last != icmsg_653_.EPS_APA_EpasFAILED){
                EPS_APA_EpasFAILED_last = icmsg_653_.EPS_APA_EpasFAILED;
                AINFO<<__func__<<": timestamp="<<cur_time<<", icmsg_653_.EPS_APA_EpasFAILED="<<icmsg_653_.EPS_APA_EpasFAILED;
                exception_handle_info_.steeringreq_fail_time = cur_time;
            }

            io_chassis_info_debug_.eps_apa_control_feedback = icmsg_653_.EPS_APA_ControlFeedback;

            static int EPS_APA_ControlFeedback_last = icmsg_653_.EPS_APA_ControlFeedback;
            if(EPS_APA_ControlFeedback_last != icmsg_653_.EPS_APA_ControlFeedback){
                EPS_APA_ControlFeedback_last = icmsg_653_.EPS_APA_ControlFeedback;
                AINFO<<__func__<<": timestamp="<<cur_time<<", icmsg_653_.EPS_APA_ControlFeedback="<<icmsg_653_.EPS_APA_ControlFeedback;
                exception_handle_info_.steeringreq_controlfeedback_time = cur_time;
            }

            io_chassis_info_debug_.epb_sys_st = icmsg_653_.EpbSts;

            io_chassis_info_debug_.ibcu_apc_reducedfunc_avail = icmsg_653_.IBCU_APCReducedFuncAvail;
            io_chassis_info_debug_.ibcu_apc_fullfunc_avail = icmsg_653_.IBCU_APCFullFuncAvail;
            io_chassis_info_debug_.ibcu_apc_active_status = icmsg_653_.IBCU_APCActiveStatus;
            io_chassis_info_debug_.esp_apa_driveroverrode = icmsg_653_.ESP_APA_DriverOverride;

            // io_chassis_info_debug_.valid = CommonBool::TRUE;
            if(0 == icmsg_653_.WhlSpdLeFrntDataVld){
                io_chassis_info_debug_.wheelspeed_fl = icmsg_653_.WhlSpdLeFrntData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdRiFrntDataVld){
                io_chassis_info_debug_.wheelspeed_fr = icmsg_653_.WhlSpdRiFrntData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdLeReDataVld){
                io_chassis_info_debug_.wheelspeed_rl = icmsg_653_.WhlSpdLeReData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdRiReDataVld){
                io_chassis_info_debug_.wheelspeed_rr = icmsg_653_.WhlSpdRiReData/3.6;
            }
            if(0 == icmsg_653_.WhlSpdLeReDataVld && 0 == icmsg_653_.WhlSpdRiReDataVld){
                double _WhlSpdLeReData,_WhlSpdRiReData;
                _WhlSpdLeReData = (1 == icmsg_653_.WhlSpdLeReDir)?(-icmsg_653_.WhlSpdLeReData):icmsg_653_.WhlSpdLeReData;
                _WhlSpdRiReData = (1 == icmsg_653_.WhlSpdRiReDir)?(-icmsg_653_.WhlSpdRiReData):icmsg_653_.WhlSpdRiReData;
                io_chassis_info_debug_.velocity_base_whl = (_WhlSpdLeReData + _WhlSpdRiReData)/2.0/3.6;
            }
            io_chassis_info_debug_.wheelsign_fl = changanToWMWheelDir(icmsg_653_.WhlSpdLeFrntDir);
            io_chassis_info_debug_.wheelsign_fr = changanToWMWheelDir(icmsg_653_.WhlSpdRiFrntDir);
            io_chassis_info_debug_.wheelsign_rl = changanToWMWheelDir(icmsg_653_.WhlSpdLeReDir);
            io_chassis_info_debug_.wheelsign_rr = changanToWMWheelDir(icmsg_653_.WhlSpdRiReDir);

            io_chassis_info_debug_.wheel_edgessum_fl = icmsg_653_.WhlSpdFrntLePls;
            io_chassis_info_debug_.wheel_edgessum_fr = icmsg_653_.WhlSpdFrntRiPls;
            io_chassis_info_debug_.wheel_edgessum_rl = icmsg_653_.WhlSpdReLePls;
            io_chassis_info_debug_.wheel_edgessum_rr = icmsg_653_.WhlSpdReRiPls;

            io_chassis_info_debug_.esp_brake_force = icmsg_653_.ESP_BrakeForce;

            if(1 == icmsg_653_.IBCU_PlungerBrakePressureValid){
                io_chassis_info_debug_.ibcu_plunger_brake_pressure = icmsg_653_.IBCU_PlungerBrakePressure;
            }
            else{
                AINFO<<__func__<<": IBCU_PlungerBrakePressureValid invalid.";
            }
        }

        if(1 == icmsg_654_.valid){
            io_chassis_info_debug_.icmsg654_timestamp = icmsg_654_.ICMSG_TimeStamp;
        }

        if(HandShakeStatus::HANDSHAKESUS == EPS_handshake_ &&
        HandShakeStatus::HANDSHAKESUS == ESP_handshake_ &&
        HandShakeStatus::HANDSHAKESUS == VCU_handshake_ &&
        HandShakeStatus::HANDSHAKESUS == Gear_handshake_){
            io_chassis_info_debug_.takeover_stat = 1;
        }
        else if(HandShakeStatus::HANDSHAKEQUIT == EPS_handshake_ &&
        HandShakeStatus::HANDSHAKEQUIT == ESP_handshake_ &&
        HandShakeStatus::HANDSHAKEQUIT == VCU_handshake_ &&
        HandShakeStatus::HANDSHAKEQUIT == Gear_handshake_){
            io_chassis_info_debug_.takeover_stat = 2;
        }

        io_chassis_info_debug_.eps_takeover_stat = static_cast<uint32_t>(EPS_handshake_);
        io_chassis_info_debug_.esp_takeover_stat = static_cast<uint32_t>(ESP_handshake_);
        io_chassis_info_debug_.vcu_takeover_stat = static_cast<uint32_t>(VCU_handshake_);
        io_chassis_info_debug_.gear_takeover_stat = static_cast<uint32_t>(Gear_handshake_);

        io_chassis_info_debug_.uss_enable = ult_enable_;

        prase_data_lock.unlock();
    }

    int IOServer::changanToWMWheelDir(int dir){
        if(0 == dir){
            return 1;
        }
        else if(1 == dir){
            return -1;
        }
        else if(2 == dir){
            return 0;
        }
        return dir;
    }

    void IOServer::recordIOChassisDebugInfo(const StruIOChassisInfoDebug& io_chassis_info_debug){
        AINFO<<__func__
                    // <<": d_velocity="<<io_chassis_info_debug.velocity
                    // <<", d_min_torque="<<io_chassis_info_debug.min_torque
                    // <<", d_max_torque="<<io_chassis_info_debug.max_torque
                    <<", d_ct="<<io_chassis_info_debug.cur_torque
                    <<", d_epb_st="<<io_chassis_info_debug.epb_sys_st
                    <<", d_gp="<<io_chassis_info_debug.gear_pos
                    <<", d_eps_f="<<io_chassis_info_debug.eps_apa_abort_feedback
                    <<", d_s_a="<<io_chassis_info_debug.steering_angle
                    // <<", d_sv="<<io_chassis_info_debug.steering_velocity
                    // <<", d_st="<<io_chassis_info_debug.steering_torque
                    <<", d_ts="<<io_chassis_info_debug.takeover_stat
                    <<", d_eps_ts="<<io_chassis_info_debug.eps_takeover_stat
                    <<", d_esp_ts="<<io_chassis_info_debug.esp_takeover_stat
                    <<", d_vcu_ts="<<io_chassis_info_debug.vcu_takeover_stat
                    <<", d_gear_ts="<<io_chassis_info_debug.gear_takeover_stat
                    // <<", d_esp_autohold_active_sts="<<io_chassis_info_debug.esp_autohold_active_sts
                    <<", d_stand_still="<<io_chassis_info_debug.stand_still
                    <<", d_eps_fld="<<io_chassis_info_debug.eps_apa_epasfailed
                    <<", d_eps_ctl_f="<<io_chassis_info_debug.eps_apa_control_feedback
                    <<", d_ibcu_rfunc="<<io_chassis_info_debug.ibcu_apc_reducedfunc_avail
                    <<", d_ibcu_ffunc="<<io_chassis_info_debug.ibcu_apc_fullfunc_avail
                    <<", d_ibcu_ast="<<io_chassis_info_debug.ibcu_apc_active_status
                    <<", esp_overrode="<<io_chassis_info_debug.esp_apa_driveroverrode
                    <<", d_vcu_torq_req_vl="<<io_chassis_info_debug.vcu_apa_torq_requesta_vailable
                    <<", d_vcu_rdy="<<io_chassis_info_debug.vcu_rdy_sts
                    <<", d_vcu_accsld="<<io_chassis_info_debug.vcu_accped_shield
                    <<", d_vcu_reqable="<<io_chassis_info_debug.vcu_apa_request_enable
                    <<", d_ibcu_plubra_pre="<<io_chassis_info_debug.ibcu_plunger_brake_pressure
                    <<", d_ussable="<<io_chassis_info_debug.uss_enable
                    <<", d_vcu_com_flt="<<io_chassis_info_debug.vcu_com_flt_sts
                    <<", d_vcu_overrode="<<io_chassis_info_debug.vcu_apa_driver_Interruption
                    // <<", d_ibcu_adc_rampoff_suspendstate="<<io_chassis_info_debug.ibcu_adc_rampoff_suspendstate
                    // <<", d_ibcu_communication_invalid="<<io_chassis_info_debug.ibcu_communication_invalid
                    <<", d_v_whl="<<io_chassis_info_debug.velocity_base_whl
                    // <<", d_wheelspeed_fl="<<io_chassis_info_debug.wheelspeed_fl
                    // <<", d_wheelspeed_fr="<<io_chassis_info_debug.wheelspeed_fr
                    // <<", d_wheelspeed_rl="<<io_chassis_info_debug.wheelspeed_rl
                    // <<", d_wheelspeed_rr="<<io_chassis_info_debug.wheelspeed_rr
                    // <<", d_wheel_edgessum_fl="<<io_chassis_info_debug.wheel_edgessum_fl
                    // <<", d_wheel_edgessum_fr="<<io_chassis_info_debug.wheel_edgessum_fr
                    // <<", d_wheel_edgessum_rl="<<io_chassis_info_debug.wheel_edgessum_rl
                    // <<", d_wheel_edgessum_rr="<<io_chassis_info_debug.wheel_edgessum_rr
                    // <<", d_icmsg650_timestamp="<<io_chassis_info_debug.icmsg650_timestamp
                    // <<", d_icmsg651_timestamp="<<io_chassis_info_debug.icmsg651_timestamp
                    // <<", d_icmsg652_timestamp="<<io_chassis_info_debug.icmsg652_timestamp
                    // <<", d_icmsg653_timestamp="<<io_chassis_info_debug.icmsg653_timestamp
                    // <<", d_icmsg654_timestamp="<<io_chassis_info_debug.icmsg654_timestamp
                    ;
    }

    //need by cyber
    void IOServer::initCyberPub(){
#if USE_IO_CHECK
       if(pWriterIOChassisInfoDebug == nullptr){
        auto test_chassis_info_debug = nodeCfg.getPubChannel("test_chassis_info_debug");
        pWriterIOChassisInfoDebug = pNode->CreateWriter<IOChassisInfoDebug>(test_chassis_info_debug.name);
       }
#endif
       //output
       if(pWriterImuInfo == nullptr){
        auto imu = nodeCfg.getPubChannel("imu");
        pWriterImuInfo = pNode->CreateWriter<Imu>(imu.name);
       }
       if(pWriterUltrasonicInfo == nullptr){
        auto ultrasonic = nodeCfg.getPubChannel("driver_ultrasonic");
        pWriterUltrasonicInfo = pNode->CreateWriter<Ultrasonic>(ultrasonic.name);
       }
       if(pWriterSystemCheckResultInfo == nullptr){
        auto io_out_system_check_ret = nodeCfg.getPubChannel("io_out_system_check_ret");
        pWriterSystemCheckResultInfo = pNode->CreateWriter<SystemCheckResultInfo>(io_out_system_check_ret.name);
       }
       if(pWriterShakeHandStateInfo == nullptr){
        auto io_out_shake_hand_ret = nodeCfg.getPubChannel("io_out_shake_hand_ret");
        pWriterShakeHandStateInfo = pNode->CreateWriter<ShakeHandStateInfo>(io_out_shake_hand_ret.name);
       }
       if(pWriterVehicleBodyInfo == nullptr){
        auto vehicle_body_info = nodeCfg.getPubChannel("vehicle_body_info");
        pWriterVehicleBodyInfo = pNode->CreateWriter<VehicleBodyInfo>(vehicle_body_info.name);
       }
       if(pWriterChassisLatInfo == nullptr){
        auto io_out_chassisinfo_lat = nodeCfg.getPubChannel("io_out_chassisinfo_lat");
        pWriterChassisLatInfo = pNode->CreateWriter<ChassisLatInfo>(io_out_chassisinfo_lat.name);
       }
       if(pWriterChassisLongInfo == nullptr){
        auto io_out_chassisinfo_lon = nodeCfg.getPubChannel("io_out_chassisinfo_lon");
        pWriterChassisLongInfo = pNode->CreateWriter<ChassisLongInfo>(io_out_chassisinfo_lon.name);
       }
       if(pWriterWheelSpeedInfo == nullptr){
        auto io_wheelspeed = nodeCfg.getPubChannel("io_wheelspeed");
        pWriterWheelSpeedInfo = pNode->CreateWriter<WheelSpeedInfo>(io_wheelspeed.name);
       }
       if(pWriterChassisShiftGearInfo == nullptr){
        auto gear_event_info = nodeCfg.getPubChannel("io_out_chassis_gearshift");
        pWriterChassisShiftGearInfo = pNode->CreateWriter<ChassisShiftGearInfo>(gear_event_info.name);
       }

    }

    void IOServer::initCyberSub(){
#if USE_IO_CHECK
        auto test_io_takeover = nodeCfg.getSubChannel("test_io_takeover");
        auto pReaderIOTakeover = pNode->CreateReader<IOTakeover>(test_io_takeover.name,
                                                                                std::bind(&IOServer::IOTakeoverCallback,this,std::placeholders::_1));
        auto test_io_control = nodeCfg.getSubChannel("test_io_control");
        auto pReaderIOControl = pNode->CreateReader<IOControl>(test_io_control.name,
                                                                                std::bind(&IOServer::IOControlCallback,this,std::placeholders::_1));
        auto test_unit_control = nodeCfg.getSubChannel("test_unit_control");
        auto pReaderIOUnitTest = pNode->CreateReader<IOControlUnitTest>(test_unit_control.name,
                                                                                std::bind(&IOServer::IOUnitTestCallback,this,std::placeholders::_1));
        auto test_enable_uss = nodeCfg.getSubChannel("test_enable_uss");
        auto pReaderEnableUSSUlt = pNode->CreateReader<EnableUSSUlt>(test_enable_uss.name,
                                                                                std::bind(&IOServer::EnableUSSCallback,this,std::placeholders::_1));
#endif
        //input
        auto io_in_chassisctl = nodeCfg.getSubChannel("io_in_chassisctl");
        auto pReaderLatAndLongControlCmd = pNode->CreateReader<LatAndLongControlCmd>(io_in_chassisctl.name,
                                                                                std::bind(&IOServer::LatAndLongControlCmdCallback,this,std::placeholders::_1));
        auto io_in_shake_hand_req = nodeCfg.getSubChannel("io_in_shake_hand_req");
        auto pReaderLatAndLongShakeHandCmd = pNode->CreateReader<LatAndLongShakeHandCmd>(io_in_shake_hand_req.name,
                                                                                std::bind(&IOServer::LatAndLongShakeHandCmdCallback,this,std::placeholders::_1));
        auto hold_pressure = nodeCfg.getSubChannel("io_in_changan_holdpressure");
        auto pReaderHoldPressureChangan = pNode->CreateReader<HoldPressureChangan>(hold_pressure.name,
                                                                                std::bind(&IOServer::HoldPressureChanganCallback,this,std::placeholders::_1));
        auto io_in_system_check_req = nodeCfg.getSubChannel("io_in_system_check_req");
        auto pReaderSystemCheckCmd = pNode->CreateReader<SystemCheckCmd>(io_in_system_check_req.name,
                                                                                std::bind(&IOServer::SystemCheckCmdCallback,this,std::placeholders::_1));
    }

    void IOServer::IOTakeoverCallback(const std::shared_ptr <IOTakeover>& io_takeover_cmd){
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*io_takeover_cmd, &json_string);
        AINFO<<__func__<<", io_takeover_cmd: "<<json_string.c_str();
        takeover_cmd_.apa_takeover = io_takeover_cmd->apa_takeover();
        takeover_cmd_.eps_takeover = io_takeover_cmd->eps_takeover();
        takeover_cmd_.esp_takeover = io_takeover_cmd->esp_takeover();
        takeover_cmd_.vcu_takeover = io_takeover_cmd->vcu_takeover();
        takeover_cmd_.gear_takeover = io_takeover_cmd->gear_takeover();
        takeover_cmd_.is_update = true;
    }

    void IOServer::takeoverCmdHandle(const TakeOverCmd& takeover_cmd,
                                                                                const StruIOChassisInfoDebug& io_chassis_info_debug){
        static bool handshake_flag = false;
        if(1 == takeover_cmd.apa_takeover){
            if(false == handshake_flag){
                handshake_flag = true;
                resetPubMsgBaseDBC(io_chassis_info_debug.steering_angle);//reset global variable cdc_247_ and cdc_2c6_
            }
            EPSHandShake(io_chassis_info_debug);
            ESPHandShake(io_chassis_info_debug);
            VCUHandShake(io_chassis_info_debug);
            GearHandShake(io_chassis_info_debug);
        }
        else if(2 == takeover_cmd.apa_takeover){
             if(true == handshake_flag){
                handshake_flag = false;
            }
            EPSHandShakeQuit(io_chassis_info_debug);
            ESPHandShakeQuit(io_chassis_info_debug);
            VCUHandShakeQuit(io_chassis_info_debug);
            GearHandShakeQuit(io_chassis_info_debug);
        }
        else{
            if(1 == takeover_cmd.eps_takeover){
                EPSHandShake(io_chassis_info_debug);
            }
            else if(2 == takeover_cmd.eps_takeover){
                EPSHandShakeQuit(io_chassis_info_debug);
            }

            if(1 == takeover_cmd.esp_takeover){
                ESPHandShake(io_chassis_info_debug);
            }
            else if(2 == takeover_cmd.esp_takeover){
                ESPHandShakeQuit(io_chassis_info_debug);
            }

            if(1 == takeover_cmd.vcu_takeover){
                VCUHandShake(io_chassis_info_debug);
            }
            else if(2 == takeover_cmd.vcu_takeover){
                VCUHandShakeQuit(io_chassis_info_debug);
            }

            if(1 == takeover_cmd.gear_takeover){
                GearHandShake(io_chassis_info_debug);
            }
            else if(2 == takeover_cmd.gear_takeover){
                GearHandShakeQuit(io_chassis_info_debug);
            }
        }
    }

    void IOServer::IOControlCallback(const std::shared_ptr <IOControl>& io_control_cmd){
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*io_control_cmd, &json_string);
        AINFO<<__func__<<" ,io_control_cmd: "<<json_string.c_str();

        io_control_cmd_.is_braking = io_control_cmd->is_braking();
        io_control_cmd_.tar_deceleration = io_control_cmd->tar_deceleration();
        io_control_cmd_.is_driving = io_control_cmd->is_driving();
        io_control_cmd_.tar_torque = io_control_cmd->tar_torque();
        io_control_cmd_.is_steering = io_control_cmd->is_steering();
        io_control_cmd_.tar_steer_angle = io_control_cmd->tar_steer_angle();
        io_control_cmd_.is_gear = io_control_cmd->is_gear();
        io_control_cmd_.tar_gear = commonGearToChanganGear(io_control_cmd->tar_gear());
        io_control_cmd_.is_update = true;
    }

    void IOServer::ioControlCmdHandle(const IOControlCmd& io_control_cmd,
                                                                                const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(true == io_control_cmd.is_braking){
            breakingProcess(io_control_cmd.tar_deceleration);
        }

        if(true == io_control_cmd.is_driving){
            drivingProcess(io_control_cmd.tar_torque,io_chassis_info_debug, io_control_cmd.tar_deceleration);
        }

        if(true == io_control_cmd.is_steering){
            steeringProcess(io_control_cmd.tar_steer_angle,io_chassis_info_debug);
        }

        if(true == io_control_cmd.is_gear){
            gearTransProcess(io_control_cmd.tar_gear,io_chassis_info_debug);
        }
        else if(false == io_control_cmd.is_gear){
            gear_event_info_.gear_trans_valid = false;
            gear_event_info_.req_gear = io_control_cmd.tar_gear;
            gear_event_info_.cur_gear = io_chassis_info_debug.gear_pos;
            time_set_flag_ = false;
        }
    }

    void IOServer::IOUnitTestCallback(const std::shared_ptr <IOControlUnitTest>& io_unittest_cmd){
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*io_unittest_cmd, &json_string);
        AINFO<<__func__<<", io_unittest_cmd: "<<json_string.c_str();

        io_unittest_cmd_.is_autohold = io_unittest_cmd->is_autohold();
        io_unittest_cmd_.autohold = io_unittest_cmd->autohold();
        io_unittest_cmd_.is_holdpressure = io_unittest_cmd->is_holdpressure();
        io_unittest_cmd_.holdpressure_dece = io_unittest_cmd->holdpressure_dece();
        io_unittest_cmd_.is_epb_control = io_unittest_cmd->is_epb_control();
        io_unittest_cmd_.epb_control = io_unittest_cmd->epb_control();
        io_unittest_cmd_.is_gear = commonGearToChanganGear(io_unittest_cmd->is_gear());
        io_unittest_cmd_.gear = io_unittest_cmd->gear();
        io_unittest_cmd_.is_start_release_pressure = io_unittest_cmd->is_start_release_pressure();
        io_unittest_cmd_.start_release_pressure_acc = io_unittest_cmd->start_release_pressure_acc();
        io_unittest_cmd_.is_driving = io_unittest_cmd->is_driving();
        io_unittest_cmd_.tar_torque = io_unittest_cmd->tar_torque();
        io_unittest_cmd_.is_breaking = io_unittest_cmd->is_breaking();
        io_unittest_cmd_.tar_deceleration = io_unittest_cmd->tar_deceleration();
        io_unittest_cmd_.is_steering = io_unittest_cmd->is_steering();
        io_unittest_cmd_.tar_steer_angle = io_unittest_cmd->tar_steer_angle();
        io_unittest_cmd_.is_standstill = io_unittest_cmd->is_standstill();
        io_unittest_cmd_.standstill_dece = io_unittest_cmd->standstill_dece();
        io_unittest_cmd_.is_emergency = io_unittest_cmd->is_emergency();
        io_unittest_cmd_.emergency = io_unittest_cmd->emergency();
        io_unittest_cmd_.is_update = true;
    }

    void IOServer::EnableUSSCallback(const std::shared_ptr <EnableUSSUlt>& enable_uss_cmd){
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*enable_uss_cmd, &json_string);
        AINFO<<__func__<<", enable_uss_cmd: "<<json_string.c_str();

        cdc_601_.VCU_Distance_CMD = enable_uss_cmd->vcu_distance_cmd();
        cdc_601_.VCU_ProbeCMD_NUM9 = enable_uss_cmd->vcu_probecmd_num9();
        cdc_601_.VCU_ProbeCMD_NUM10 = enable_uss_cmd->vcu_probecmd_num10();
        cdc_601_.VCU_ProbeCMD_NUM11 = enable_uss_cmd->vcu_probecmd_num11();
        cdc_601_.VCU_ProbeCMD_NUM12 = enable_uss_cmd->vcu_probecmd_num12();
        cdc_601_.VCU_Machine_NUM = enable_uss_cmd->vcu_machine_num();
        cdc_601_.VCU_ProbeCMD_NUM1 = enable_uss_cmd->vcu_probecmd_num1();
        cdc_601_.VCU_ProbeCMD_NUM2 = enable_uss_cmd->vcu_probecmd_num2();
        cdc_601_.VCU_ProbeCMD_NUM3 = enable_uss_cmd->vcu_probecmd_num3();
        cdc_601_.VCU_ProbeCMD_NUM4 = enable_uss_cmd->vcu_probecmd_num4();
        cdc_601_.VCU_ProbeCMD_NUM5 = enable_uss_cmd->vcu_probecmd_num5();
        cdc_601_.VCU_ProbeCMD_NUM6 = enable_uss_cmd->vcu_probecmd_num6();
        cdc_601_.VCU_ProbeCMD_NUM7 = enable_uss_cmd->vcu_probecmd_num7();
        cdc_601_.VCU_ProbeCMD_NUM8 = enable_uss_cmd->vcu_probecmd_num8();

        pubMegaIpcMsg601(cdc_601_);
    }

    void IOServer::EnableUSSBaseInit(){
        pubMegaIpcMsg601(cdc_601_);
    }

    void IOServer::ioUnitTestHandle(const IOUnitTestCmd& io_unittest_cmd,
                                        const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(io_unittest_cmd.is_autohold){
            autoHoldForbidden(io_chassis_info_debug);
        }
        if(io_unittest_cmd.is_holdpressure){
            pressureMaintain(io_unittest_cmd.holdpressure_dece);
        }
        if(io_unittest_cmd.is_epb_control){
            braeakOrReleaseEPB(io_unittest_cmd.epb_control);
        }
        if(io_unittest_cmd.is_gear){
            transPRNDShift(io_unittest_cmd.gear);
        }
        if(io_unittest_cmd.is_start_release_pressure){
            startingPressureRelief(io_unittest_cmd.start_release_pressure_acc,0x1);
        }
        if(io_unittest_cmd.is_driving){
            torqueControl(io_unittest_cmd.tar_torque, io_chassis_info_debug);
        }
        if(io_unittest_cmd.is_breaking){
            decelerationControl(io_unittest_cmd.tar_deceleration);
        }
        if(io_unittest_cmd.is_steering){
            steeringAngleControl(io_unittest_cmd.tar_steer_angle,io_chassis_info_debug);
        }
        if(io_unittest_cmd.is_standstill){
            standstillControl(io_unittest_cmd.standstill_dece, io_unittest_cmd.is_standstill);
        }
        if(io_unittest_cmd.is_emergency){
            emergencyBrake();
        }
    }

    int IOServer::commonGearToChanganGear(int common_gear){
        int res = 0;
        if(static_cast<int>(CommonGearPosition::NONE) == common_gear)
        {res = static_cast<int>(APATransPRNDShiftRequest::NOREQUEST);}
        if(static_cast<int>(CommonGearPosition::NEUTRAL) == common_gear)
        {res = static_cast<int>(APATransPRNDShiftRequest::N);}
        if(static_cast<int>(CommonGearPosition::DRIVING) == common_gear)
        {res = static_cast<int>(APATransPRNDShiftRequest::D);}
        if(static_cast<int>(CommonGearPosition::REVERSE) == common_gear)
        {res = static_cast<int>(APATransPRNDShiftRequest::R);}
        if(static_cast<int>(CommonGearPosition::PARKING) == common_gear)
        {res = static_cast<int>(APATransPRNDShiftRequest::P);}
        return res;
    }

    int IOServer::changanGearToCommonGear(int changan_gear){
        int res = 0;
        if(static_cast<int>(APATransPRNDShiftRequest::INVALID) == changan_gear)
        {res = static_cast<int>(CommonGearPosition::NONE);}
        if(static_cast<int>(APATransPRNDShiftRequest::P) == changan_gear)
        {res = static_cast<int>(CommonGearPosition::PARKING);}
        if(static_cast<int>(APATransPRNDShiftRequest::R) == changan_gear)
        {res = static_cast<int>(CommonGearPosition::REVERSE);}
        if(static_cast<int>(APATransPRNDShiftRequest::N) == changan_gear)
        {res = static_cast<int>(CommonGearPosition::NEUTRAL);}
        if(static_cast<int>(APATransPRNDShiftRequest::D) == changan_gear)
        {res = static_cast<int>(CommonGearPosition::DRIVING);}
        if(static_cast<int>(APATransPRNDShiftRequest::RESERVED) == changan_gear)
        {res = static_cast<int>(CommonGearPosition::NONE);}
        return res;
    }

    void IOServer::resetRevCmd(double cur_angle){
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        input_lat_lon_control_.is_driving = CommonBool::NONE;
        input_lat_lon_control_.is_braking = CommonBool::NONE;
        input_lat_lon_control_.is_steering = CommonBool::NONE;
        input_lat_lon_control_.tar_steer_angle = cur_angle;
        input_lat_lon_control_.put_gear = CommonBool::NONE;
        input_lat_lon_control_.hold = CommonBool::NONE;
        input_lat_lon_control_.release = CommonBool::NONE;
        gear_event_info_.gear_trans_valid = false;
    }

    void IOServer::LatAndLongControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& lat_lon_control_cmd){
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        // std::string json_string;
        // google::protobuf::util::MessageToJsonString(*lat_lon_control_cmd, &json_string);
        // AINFO<<__func__<<", lat_lon_control_cmd: "<<json_string.c_str();

        if(!lat_lon_control_cmd->has_data()){
            AINFO<<__func__<<": not has data.";
            return;
        }
        if(lat_lon_control_cmd->data().Is<TorqueControlData>()){
            TorqueControlData torque_control_data;
            lat_lon_control_cmd->data().UnpackTo(&torque_control_data);
            input_lat_lon_control_.is_driving = torque_control_data.is_driving();
            input_lat_lon_control_.is_braking = torque_control_data.is_braking();
            input_lat_lon_control_.tar_torque = torque_control_data.tar_torque();
            input_lat_lon_control_.tar_deceleration = torque_control_data.tar_deceleration();
            input_lat_lon_control_.is_steering = torque_control_data.is_steering();
            input_lat_lon_control_.tar_steer_angle = torque_control_data.tar_steer_angle();
            input_lat_lon_control_.put_gear = torque_control_data.put_gear();
            input_lat_lon_control_.tar_gear = commonGearToChanganGear(torque_control_data.tar_gear());
        }
            input_lat_lon_control_.hold = lat_lon_control_cmd->hold();
            input_lat_lon_control_.release = lat_lon_control_cmd->release();
            input_lat_lon_control_.is_update = true;

            AINFO<<__func__<<", lat_lon_control_cmd: is_driving="<<static_cast<int>(input_lat_lon_control_.is_driving)
                        <<", is_braking="<<static_cast<int>(input_lat_lon_control_.is_braking)
                        <<", tar_torque="<<input_lat_lon_control_.tar_torque
                        <<", tar_deceleration="<<input_lat_lon_control_.tar_deceleration
                        <<", is_steering="<<static_cast<int>(input_lat_lon_control_.is_steering)
                        <<", tar_steer_angle="<<input_lat_lon_control_.tar_steer_angle
                        <<", put_gear="<<static_cast<int>(input_lat_lon_control_.put_gear)
                        <<", tar_gear="<<static_cast<int>(input_lat_lon_control_.tar_gear)
                        <<", hold="<<static_cast<int>(input_lat_lon_control_.hold)
                        <<", release="<<static_cast<int>(input_lat_lon_control_.release)<<",";
    }

    void IOServer::LatAndLongShakeHandCmdCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& shake_hand_cmd){
        static CommonBool::Enum activate_lat_last = CommonBool::NONE;
        static CommonBool::Enum activate_long_last = CommonBool::NONE;
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        
        input_shake_hand_.activate_lat = shake_hand_cmd->activate_lat();
        input_shake_hand_.activate_long = shake_hand_cmd->activate_long();
        // input_shake_hand_.enable_p_gear = shake_hand_cmd->enable_p_gear();
        input_shake_hand_.is_update = true;

         AINFO<<__func__<<", shake_hand_cmd: activate_lat="<<static_cast<int>(input_shake_hand_.activate_lat)
                      <<",  activate_long="<<static_cast<int>(input_shake_hand_.activate_long)<<",";

        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms

        exception_handle_info_.current_hanshake_req_time = cur_time;
        if(CommonBool::FALSE == input_shake_hand_.activate_long
            &&  CommonBool::FALSE == input_shake_hand_.activate_lat){
                IOExceptionReset();
                if(input_shake_hand_.activate_lat != activate_lat_last
                    && input_shake_hand_.activate_long != activate_long_last){
                    resetOutputShakeHand();
                    resetOutputDataChassis();
                }
        }
        if(CommonBool::TRUE == input_shake_hand_.activate_long
            &&  CommonBool::TRUE == input_shake_hand_.activate_lat
            &&  0x0 == io_chassis_info_debug_.eps_apa_epasfailed
            &&  0x0 == io_chassis_info_debug_.eps_apa_abort_feedback){
                IOExceptionReset();
                if(input_shake_hand_.activate_lat != activate_lat_last
                    && input_shake_hand_.activate_long != activate_long_last){
                    resetOutputShakeHand();
                    resetOutputDataChassis();
                }
        }
        ShakeHandStateInfoPub();
        activate_lat_last = input_shake_hand_.activate_lat;
        activate_long_last = input_shake_hand_.activate_long;
    }

    void IOServer::HoldPressureChanganCallback(const std::shared_ptr <HoldPressureChangan>& hold_pres_cmd){
        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*hold_pres_cmd, &json_string);
        AINFO<<__func__<<", hold_pres_cmd: "<<json_string.c_str();

        input_hold_pressure_.hold = hold_pres_cmd->hold();
        input_hold_pressure_.is_update = true;

        // AINFO<<__func__<<", hold_pres_cmd: hold="<<input_hold_pressure_.hold;
    }

    void IOServer::SystemCheckCmdCallback(const std::shared_ptr <SystemCheckCmd>& sys_chk_cmd){
        // std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        std::string json_string;
        google::protobuf::util::MessageToJsonString(*sys_chk_cmd, &json_string);
        AINFO<<__func__<<", sys_chk_cmd: "<<json_string.c_str();

        input_system_check_.request_check = sys_chk_cmd->request_check();
        input_system_check_.is_update = true;
        systemCheckCmdHandle(input_system_check_);
    }

    void IOServer::breakingProcess(double deceleration){
        //TODO: Do  some conditional check?
        decelerationControl(deceleration);
    }

    void IOServer::drivingProcess(int req_torq, const StruIOChassisInfoDebug& io_chassis_info_debug, double acc){
        //TODO: Do  some conditional check?
        torqueControl(req_torq, io_chassis_info_debug, acc);
    }

    void IOServer::steeringProcess(double req_angle,const StruIOChassisInfoDebug& io_chassis_info_debug){
        //TODO: Do  some conditional check?
        steeringAngleControl(req_angle,io_chassis_info_debug);
    }

    void IOServer::gearTransProcess(uint32_t gear, const StruIOChassisInfoDebug& io_chassis_info_debug){
        double maintain_time=0.0;
        static long frist_time = 0;
        if(static_cast<uint32_t>(APATransPRNDShiftRequest::D) == gear || static_cast<uint32_t>(APATransPRNDShiftRequest::R) == gear ){
            //保压，EPB释放，换挡，起步泄压
            pressureMaintain(-0.25);//保压减速度，当执行起步泄压时，该减速度会被覆盖，不需要做判断处理
            if(1 == io_chassis_info_debug.stand_still){
                if(gear == static_cast<uint32_t>(io_chassis_info_debug.gear_pos) && 0 == io_chassis_info_debug.epb_sys_st){
                    transPRNDShift(0x0);
                    struct timeval tv;
                    gettimeofday(&tv, NULL);
                    long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms
                    if(false == time_set_flag_){
                        frist_time = cur_time;
                        time_set_flag_ = true;
                    }
                    maintain_time = cur_time - frist_time;
                    startingPressureRelief(0.25,0x1);
                    if(maintain_time>500 /* && 0x0 == io_chassis_info_debug.esp_brake_force */){//泄压起步保持一定时间
                        //反馈档位状态
                        gear_event_info_.gear_trans_valid = true;
                        gear_event_info_.req_gear = gear;
                        gear_event_info_.cur_gear = io_chassis_info_debug.gear_pos;
                    }
                }
                else{
                    if(1 == io_chassis_info_debug.epb_sys_st){//EPB加紧
                        braeakOrReleaseEPB(0x2);//EPB释放
                    }
                    else if(0 == io_chassis_info_debug.epb_sys_st){
                        braeakOrReleaseEPB(0x0);
                        transPRNDShift(gear);
                    }
                }
            }

        }
        else if(static_cast<uint32_t>(APATransPRNDShiftRequest::P) == gear){
            //换挡，EPB拉起
            pressureMaintain(-0.25);//默认保压
            if(0 == io_chassis_info_debug.epb_sys_st){
                    braeakOrReleaseEPB(0x1);//EPB拉起
            }
            else if(1 == io_chassis_info_debug.epb_sys_st){//EPB加紧
                braeakOrReleaseEPB(0x0);
                if(gear == static_cast<uint32_t>(io_chassis_info_debug.gear_pos)){
                    transPRNDShift(0x0);
                    gear_event_info_.gear_trans_valid = true;
                    gear_event_info_.req_gear = gear;
                    gear_event_info_.cur_gear = io_chassis_info_debug.gear_pos;
                }
                else{
                    transPRNDShift(gear);
                }
            }
        }
    }

    void IOServer::latLonControlCmdHandle(const StruInputLatAndLongControlCmd& lat_lon_control_cmd,
                                                                                          const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(CommonBool::TRUE == lat_lon_control_cmd.is_braking){
            breakingProcess(lat_lon_control_cmd.tar_deceleration);
        }

        if(CommonBool::TRUE == lat_lon_control_cmd.is_driving){
            drivingProcess(lat_lon_control_cmd.tar_torque,io_chassis_info_debug, lat_lon_control_cmd.tar_deceleration);
        }

        if(CommonBool::TRUE == lat_lon_control_cmd.is_steering){
            steeringProcess(lat_lon_control_cmd.tar_steer_angle,io_chassis_info_debug);
        }

        if(CommonBool::TRUE == lat_lon_control_cmd.put_gear){
            gearTransProcess(lat_lon_control_cmd.tar_gear,io_chassis_info_debug);
        }
        else if(CommonBool::FALSE == lat_lon_control_cmd.put_gear){
            gear_event_info_.gear_trans_valid = false;
            gear_event_info_.req_gear = lat_lon_control_cmd.tar_gear;
            gear_event_info_.cur_gear = io_chassis_info_debug.gear_pos;
            time_set_flag_ = false;
        }
    }

    void IOServer::shakeHandCmdHandle(const StruInputLatAndLongShakeHandCmd& shake_hand_cmd,
                                                                                    const StruIOChassisInfoDebug& io_chassis_info_debug,
                                                                                    const ExceptionHandleInfo& exception_handle_info){
        if(0 == io_chassis_info_debug.vcu_rdy_sts){
            // AERROR<<__func__<<"Please set vcu_rdy_sts is true!!!!!!!!!!! And current vcu_rdy_sts="
            // <<io_chassis_info_debug.vcu_rdy_sts;
            return;
        }

        if(CommonBool::TRUE == shake_hand_cmd.activate_lat ||
            CommonBool::TRUE == shake_hand_cmd.activate_long){
                cdc_247_.APA_APAOnOff = 0x1;
        }

        static bool handshake_flag = false;
        if(CommonBool::TRUE == shake_hand_cmd.activate_lat &&
            CommonBool::TRUE == shake_hand_cmd.activate_long){
            if(false == handshake_flag){
                handshake_flag = true;
                resetPubMsgBaseDBC(io_chassis_info_debug.steering_angle);//reset global variable cdc_247_ and cdc_2c6_
                resetRevCmd(io_chassis_info_debug.steering_angle);
            }
        }
        if(CommonBool::FALSE == shake_hand_cmd.activate_lat &&
            CommonBool::FALSE == shake_hand_cmd.activate_long){
            if(true == handshake_flag){
                handshake_flag = false;
            }

            if(2 == io_chassis_info_debug.takeover_stat){//all quit shakeover
                resetPubMsgBaseDBC(io_chassis_info_debug.steering_angle);//reset global variable cdc_247_ and cdc_2c6_
                cdc_247_.APA_APAOnOff = 0x0;
            }
        }


        if(CommonBool::FALSE == shake_hand_cmd.activate_lat
                     || 1 == exception_handle_info.eps_fail
                     || 2 == exception_handle_info.eps_fail){
            EPSHandShakeQuit(io_chassis_info_debug);
        }
        else if(CommonBool::TRUE == shake_hand_cmd.activate_lat ){
            EPSHandShake(io_chassis_info_debug);
        }
        // else{
        //     AINFO<<__func__<<": lat shake hand invalid, activate_lat="<<shake_hand_cmd.activate_lat;
        // }
        if(CommonBool::TRUE == shake_hand_cmd.activate_long){
            cdc_247_.APA_TargetAccelerationValid = 0x1;
            cdc_247_.APA_PtTrqReqValid = 0x1;
            autoHoldForbidden(io_chassis_info_debug,0x1);
            ESPHandShake(io_chassis_info_debug);
            VCUHandShake(io_chassis_info_debug);
            GearHandShake(io_chassis_info_debug);
        }
        else if(CommonBool::FALSE == shake_hand_cmd.activate_long){
            if(CommonBool::TRUE == shake_hand_cmd.enable_p_gear){
                static uint32_t delay_times = 60;
                if(static_cast<int>(VcuGearPosn::P) == io_chassis_info_debug.gear_pos){
                    delay_times = 60;
                    ESPHandShakeQuit(io_chassis_info_debug);
                    VCUHandShakeQuit(io_chassis_info_debug);
                    GearHandShakeQuit(io_chassis_info_debug);
                    autoHoldForbidden(io_chassis_info_debug,0x0);
                    cdc_247_.APA_TargetAccelerationValid = 0x0;
                    cdc_247_.APA_PtTrqReqValid = 0x0;
                }
                else{
                    delay_times++;
                    if(delay_times > 50){
                        AERROR<<__func__<<"waiting gear P... debug_wait_time="<<delay_times;
                        delay_times = 0;
                    }
                }
            }
            else{
                ESPHandShakeQuit(io_chassis_info_debug);
                VCUHandShakeQuit(io_chassis_info_debug);
                GearHandShakeQuit(io_chassis_info_debug);
                autoHoldForbidden(io_chassis_info_debug,0x0);
                cdc_247_.APA_TargetAccelerationValid = 0x0;
                cdc_247_.APA_PtTrqReqValid = 0x0;
            }

        }
        // else{
        //     AINFO<<__func__<<": lon shake hand invalid, activate_long="<<shake_hand_cmd.activate_long;
        // }

    }

    void IOServer::holdPressureCmdHandle(const StruInputHoldPressureChangan& hold_pres_cmd){
        if(CommonBool::TRUE == hold_pres_cmd.hold){
            standstillControl(hold_pres_cmd.deacc,1);
        }
        else if(CommonBool::FALSE == hold_pres_cmd.hold){
            standstillControl(hold_pres_cmd.deacc,0);
        }
        // else{
        //     AINFO<<__func__<<": hold pressure invalid, hold="<<hold_pres_cmd.hold;
        // }
    }

    void IOServer::holdOrReleaseCmdHandle(const StruInputLatAndLongControlCmd& lat_lon_control_cmd,
                                                                                            const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(CommonBool::TRUE == lat_lon_control_cmd.hold){
            standstillControl(lat_lon_control_cmd.tar_deceleration,1);
        }
        else if(CommonBool::FALSE == lat_lon_control_cmd.hold){
            standstillControl(lat_lon_control_cmd.tar_deceleration,0);
        }
        // else{
        //     AINFO<<__func__<<": standstill control invalid, hold="<<lat_lon_control_cmd.hold;
        // }

        if(CommonBool::TRUE == lat_lon_control_cmd.release){
            startingPressureRelief(0.25,1);
        }
        else if(CommonBool::FALSE == lat_lon_control_cmd.release){
            startingPressureRelief(0.25,0);
        }
        // else{
        //     AINFO<<__func__<<": hold pressure invalid, hold="<<lat_lon_control_cmd.release;
        // }
    }

    void IOServer::systemCheckCmdHandle(const StruInputSystemCheckCmd& sys_chk_cmd){
        if(CommonBool::TRUE == sys_chk_cmd.request_check){
            SystemCheckResultInfoPub();
        }
        else if(CommonBool::FALSE == sys_chk_cmd.request_check){

        }
        // else{
        //     AINFO<<__func__<<": system check invalid, request_check="<<sys_chk_cmd.request_check;
        // }
    }

    double IOServer::getTimeS(struct timeval * pt)
    {
        double N = 1000.0;
        struct timeval tv;
        if(pt==nullptr)
            gettimeofday(&tv, NULL); // get current time
        else
            tv = *pt;
        double milliseconds = tv.tv_sec * N + tv.tv_usec / N; // calculate milliseconds
        return milliseconds / 1000;
    }

    void IOServer::ioChassisInfoDebugPub(const StruIOChassisInfoDebug& io_chassis_info_debug){
        IOChassisInfoDebug cmd;
        static int64_t seq=0;
        auto header = cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        cmd.set_velocity(io_chassis_info_debug.velocity);
        cmd.set_min_torque(io_chassis_info_debug.min_torque);
        cmd.set_max_torque(io_chassis_info_debug.max_torque);
        cmd.set_cur_torque(io_chassis_info_debug.cur_torque);
        cmd.set_epb_sys_st(io_chassis_info_debug.epb_sys_st);
        cmd.set_gear_pos(changanGearToCommonGear(io_chassis_info_debug.gear_pos));
        cmd.set_eps_apa_abort_feedback(io_chassis_info_debug.eps_apa_abort_feedback);
        cmd.set_steering_angle(io_chassis_info_debug.steering_angle);
        cmd.set_steering_velocity(io_chassis_info_debug.steering_velocity);
        cmd.set_steering_torque(io_chassis_info_debug.steering_torque);
        cmd.set_right_turn_lamp_st(io_chassis_info_debug.right_turn_lamp_st);
        cmd.set_left_turn_lamp_st(io_chassis_info_debug.left_turn_lamp_st);
        cmd.set_rl_turn_lamp_st(io_chassis_info_debug.rl_turn_lamp_st);
        cmd.set_takeover_stat(io_chassis_info_debug.takeover_stat);
        cmd.set_eps_takeover_stat(io_chassis_info_debug.eps_takeover_stat);
        cmd.set_esp_takeover_stat(io_chassis_info_debug.esp_takeover_stat);
        cmd.set_vcu_takeover_stat(io_chassis_info_debug.vcu_takeover_stat);
        cmd.set_gear_takeover_stat(io_chassis_info_debug.gear_takeover_stat);
        auto linear_acceleration = cmd.mutable_linear_acceleration();
        linear_acceleration->set_x(io_chassis_info_debug.linear_acceleration.x);
        linear_acceleration->set_y(io_chassis_info_debug.linear_acceleration.y);
        linear_acceleration->set_z(io_chassis_info_debug.linear_acceleration.z);
        auto angular_velocity = cmd.mutable_angular_velocity();
        angular_velocity->set_x(io_chassis_info_debug.angular_velocity.x);
        angular_velocity->set_y(io_chassis_info_debug.angular_velocity.y);
        angular_velocity->set_z(io_chassis_info_debug.angular_velocity.z);
        auto quaternions = cmd.mutable_quaternions();
        quaternions->set_w(io_chassis_info_debug.quaternions.w);
        quaternions->set_x(io_chassis_info_debug.quaternions.x);
        quaternions->set_y(io_chassis_info_debug.quaternions.y);
        quaternions->set_z(io_chassis_info_debug.quaternions.z);
        cmd.set_ins_roll_mis_angle_to_veh(io_chassis_info_debug.ins_roll_mis_angle_to_veh);
        cmd.set_ins_pitch_mis_angle_to_veh(io_chassis_info_debug.ins_pitch_mis_angle_to_veh);
        cmd.set_ins_yaw_mis_angle_to_veh(io_chassis_info_debug.ins_yaw_mis_angle_to_veh);
        cmd.set_esp_autohold_active_sts(io_chassis_info_debug.esp_autohold_active_sts);
        cmd.set_stand_still(io_chassis_info_debug.stand_still);

        cmd.set_eps_apa_epasfailed(io_chassis_info_debug.eps_apa_epasfailed);
        cmd.set_eps_apa_control_feedback(io_chassis_info_debug.eps_apa_control_feedback);
        cmd.set_ibcu_apc_reducedfunc_avail(io_chassis_info_debug.ibcu_apc_reducedfunc_avail);
        cmd.set_ibcu_apc_fullfunc_avail(io_chassis_info_debug.ibcu_apc_fullfunc_avail);
        cmd.set_ibcu_apc_active_status(io_chassis_info_debug.ibcu_apc_active_status);
        cmd.set_vcu_apa_torq_requesta_vailable(io_chassis_info_debug.vcu_apa_torq_requesta_vailable);
        cmd.set_vcu_rdy_sts(io_chassis_info_debug.vcu_rdy_sts);
        cmd.set_vcu_accped_shield(io_chassis_info_debug.vcu_accped_shield);
        cmd.set_vcu_apa_request_enable(io_chassis_info_debug.vcu_apa_request_enable);
        cmd.set_uss_enable(io_chassis_info_debug.uss_enable);

        pWriterIOChassisInfoDebug->Write(cmd);
    }

    void IOServer::ChassisLongInfoPub(){
        ChassisLongInfo lon_cmd;
        static int64_t seq=0;
        auto header = lon_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        auto data = lon_cmd.mutable_data();
        data->set_stand_still(output_data_chassis_.LonDa_stand_still);
        data->set_esp_brakeforce(output_data_chassis_.epb_sys_st);
        data->set_speed(output_data_chassis_.LonDa_speed);
        // data->set_lon_acceleration(output_data_chassis_.lon_acceleration);
        data->set_cur_torque(output_data_chassis_.LonDa_cur_torque);
        data->set_min_torque(output_data_chassis_.LonDa_min_torque);
        data->set_max_torque(output_data_chassis_.LonDa_max_torque);
        data->set_gear_pos((CommonGearPosition::Enum)changanGearToCommonGear(output_data_chassis_.LonDa_gear_pos));
        data->set_ibcu_pressure(output_data_chassis_.LonDa_ibcu_pressure);

        auto state = lon_cmd.mutable_state();
        state->set_esp_valid(output_data_chassis_.LonSt_esp_valid);
        state->set_esp_err_type(output_data_chassis_.LonSt_esp_err_type);
        state->set_esp_err_reason(output_data_chassis_.LonSt_esp_err_reason);
        state->set_epb_valid(output_data_chassis_.LonSt_epb_valid);
        state->set_epb_err_type(output_data_chassis_.LonSt_epb_err_type);
        state->set_epb_err_reason(output_data_chassis_.LonSt_epb_err_reason);
        state->set_vcu_valid(output_data_chassis_.LonSt_vcu_valid);
        state->set_vcu_err_type(output_data_chassis_.LonSt_vcu_err_type);
        state->set_vcu_err_reason(output_data_chassis_.LonSt_vcu_err_reason);

        pWriterChassisLongInfo->Write(lon_cmd);
    }

    void IOServer::WheelSpeedInfoPub(){
        WheelSpeedInfo speed_cmd;
        static int64_t seq=0;
        auto header = speed_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        speed_cmd.set_valid(output_data_wheel_speed_.valid);
        speed_cmd.set_wheelspeed_fl(output_data_wheel_speed_.wheelspeed_fl);
        speed_cmd.set_wheelspeed_fr(output_data_wheel_speed_.wheelspeed_fr);
        speed_cmd.set_wheelspeed_rl(output_data_wheel_speed_.wheelspeed_rl);
        speed_cmd.set_wheelspeed_rr(output_data_wheel_speed_.wheelspeed_rr);

        speed_cmd.set_wheelsign_fl(output_data_wheel_speed_.wheelsign_fl);
        speed_cmd.set_wheelsign_fr(output_data_wheel_speed_.wheelsign_fr);
        speed_cmd.set_wheelsign_rl(output_data_wheel_speed_.wheelsign_rl);
        speed_cmd.set_wheelsign_rr(output_data_wheel_speed_.wheelsign_rr);
        speed_cmd.set_wheel_edgessum_fl(output_data_wheel_speed_.wheel_edgessum_fl);
        speed_cmd.set_wheel_edgessum_fr(output_data_wheel_speed_.wheel_edgessum_fr);
        speed_cmd.set_wheel_edgessum_rl(output_data_wheel_speed_.wheel_edgessum_rl);
        speed_cmd.set_wheel_edgessum_rr(output_data_wheel_speed_.wheel_edgessum_rr);
        speed_cmd.set_measurement_time(output_data_wheel_speed_.measurement_time);

        // std::string json_string;
        // google::protobuf::util::MessageToJsonString(speed_cmd, &json_string);
        // AINFO << __FUNCTION__ <<" : "<< json_string;

        pWriterWheelSpeedInfo->Write(speed_cmd);
    }

    void IOServer::ChassisShiftGearInfoPub(){
        // if(true == gear_event_info_.gear_trans_valid){
            ChassisShiftGearInfo ge_cmd;
            static int64_t seq=0;
            auto header = ge_cmd.mutable_header();
            header->set_timestamp(getTimeS(nullptr));
            header->set_module_name(nodeCfg.getName());
            header->set_sequence_num(seq++);
            header->set_version(0);
            CommonBool::Enum tmp_enum = CommonBool::NONE;
            if(true == gear_event_info_.gear_trans_valid){
                tmp_enum = CommonBool::TRUE;
            }
            if(false == gear_event_info_.gear_trans_valid){
                tmp_enum = CommonBool::FALSE;
            }
            ge_cmd.set_gear_shifted(tmp_enum);
            // ge_cmd.set_req_gear(changanGearToCommonGear(gear_event_info_.req_gear));
            // ge_cmd.set_cur_gear(changanGearToCommonGear(gear_event_info_.cur_gear));

            pWriterChassisShiftGearInfo->Write(ge_cmd);
        // }
    }

    void IOServer::ChassisLatInfoPub(){
        ChassisLatInfo lat_cmd;
        static int64_t seq=0;
        auto header = lat_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        auto data = lat_cmd.mutable_data();
        data->set_steering_angle(output_data_chassis_.LatDa_steering_angle);
        data->set_steering_velocity(output_data_chassis_.LatDa_steering_velocity);
        data->set_steering_torque(output_data_chassis_.LatDa_steering_torque);

        auto state = lat_cmd.mutable_state();
        state->set_eps_valid(output_data_chassis_.LatSt_eps_valid);
        state->set_eps_err_type(output_data_chassis_.LatSt_eps_err_type);
        state->set_eps_err_reason(output_data_chassis_.LatSt_eps_err_reason);
        pWriterChassisLatInfo->Write(lat_cmd);
    }

    void IOServer::SystemCheckResultInfoPub(){
        SystemCheckResultInfo sc_cmd;
        static int64_t seq=0;
        auto header = sc_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        sc_cmd.set_is_check_ok(output_data_system_check_.is_check_ok);
        sc_cmd.set_check_err_reason(output_data_system_check_.check_err_reason);
        pWriterSystemCheckResultInfo->Write(sc_cmd);
    }

    void IOServer::ShakeHandStateInfoPub(){
        ShakeHandStateInfo sh_cmd;
        static int64_t seq=0;
        auto header = sh_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        sh_cmd.set_lat_active(output_data_shake_hand_.lat_active);
        sh_cmd.set_lat_err_type(output_data_shake_hand_.lat_shakehand_err_type);
        sh_cmd.set_lat_err_reason(output_data_shake_hand_.lat_shakehand_err_reason);
        sh_cmd.set_long_active(output_data_shake_hand_.long_active);
        sh_cmd.set_long_err_type(output_data_shake_hand_.long_shakehand_err_type);
        sh_cmd.set_long_err_reason(output_data_shake_hand_.long_shakehand_err_reason);
        pWriterShakeHandStateInfo->Write(sh_cmd);
    }

    void IOServer::VehicleBodyInfoPub(){
        VehicleBodyInfo vb_cmd;
        pWriterVehicleBodyInfo->Write(vb_cmd);
    }

    void IOServer::ImuInfoPub(){
        Imu imu_cmd;
        static int64_t seq=0;
        auto header = imu_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);
        imu_cmd.set_measurement_time(output_data_imu_info_.measurement_time);
        auto linear_acceleration = imu_cmd.mutable_linear_acceleration();
        linear_acceleration->set_x(output_data_imu_info_.linear_acceleration.x);
        linear_acceleration->set_y(output_data_imu_info_.linear_acceleration.y);
        linear_acceleration->set_z(output_data_imu_info_.linear_acceleration.z);
        auto angular_velocity = imu_cmd.mutable_angular_velocity();
        angular_velocity->set_x(output_data_imu_info_.angular_velocity.x);
        angular_velocity->set_y(output_data_imu_info_.angular_velocity.y);
        angular_velocity->set_z(output_data_imu_info_.angular_velocity.z);
        pWriterImuInfo->Write(imu_cmd);
    }


    void IOServer::UltrasonicInfoPub(){
        Ultrasonic ultrasonic_cmd;
        static int64_t seq=0;
        auto header = ultrasonic_cmd.mutable_header();
        header->set_timestamp(getTimeS(nullptr));
        header->set_module_name(nodeCfg.getName());
        header->set_sequence_num(seq++);
        header->set_version(0);

        double node[12];

        node[0] = icmsg_654_.Ult_Probe_info12 / 10.0;
        node[1] = icmsg_654_.Ult_Probe_info11 / 10.0;
        node[2] = icmsg_654_.Ult_Probe_info10 / 10.0;
        node[3] = icmsg_654_.Ult_Probe_info9 / 10.0;
        node[4] = icmsg_654_.Ult_Probe_info8 / 10.0;
        node[5] = icmsg_654_.Ult_Probe_info7 / 10.0;
        node[6] = icmsg_654_.Ult_Probe_info1 / 10.0;
        node[7] = icmsg_654_.Ult_Probe_info2 / 10.0;
        node[8] = icmsg_654_.Ult_Probe_info3 / 10.0;
        node[9] = icmsg_654_.Ult_Probe_info4 / 10.0;
        node[10] = icmsg_654_.Ult_Probe_info5 / 10.0;
        node[11] = icmsg_654_.Ult_Probe_info6 / 10.0;

        // string uss_info_str = "";
        for (int i=0; i < 12; ++i) {
            if (node[i] > 0)
                AINFO << "io_uss_node_" << std::to_string(i) << "=" << node[i]/100.0;
            uls_filter_[i].SetFilterPara(filter_window_size_, filter_pickup_index_);
            node[i] = uls_filter_[i].Run(node[i]);
            if (node[i] >= uss_valid_distance_cm_)
                node[i] = 0.0;

            // if (node[i] > 0)
                // AINFO << "io_uss_filter_node_" << std::to_string(i) << "=" << node[i]/100.0;
        }

        ultrasonic_cmd.set_fd_frontls(node[0]);//FFLS:1->12
        ultrasonic_cmd.set_ra_frontl(node[1]);//RFL:2->11
        ultrasonic_cmd.set_rb_frontlm(node[2]);//RFLM:3->10
        ultrasonic_cmd.set_rc_frontrm(node[3]);//RFRM:4->9
        ultrasonic_cmd.set_rd_frontr(node[4]);//RFR:5->8
        ultrasonic_cmd.set_fa_frontrs(node[5]);//FFRS:6->7
        ultrasonic_cmd.set_fa_rearls(node[6]);//FRLS:12->1
        ultrasonic_cmd.set_rd_rearl(node[7]);//RRRL:11->2
        ultrasonic_cmd.set_rc_rearlm(node[8]);//RRLM:10->3
        ultrasonic_cmd.set_rb_rearrm(node[9]);//RRRM:9->4
        ultrasonic_cmd.set_ra_rearr(node[10]);//RRR:8->5
        ultrasonic_cmd.set_fd_rearrs(node[11]);//FRRS:7->6
        // ultrasonic_cmd.set_reserved_1(0);//
        // ultrasonic_cmd.set_reserved_2(0);//
        // ultrasonic_cmd.set_reserved_3(0);//
        // ultrasonic_cmd.set_reserved_4(0);//

        //std::string json_string;
        //google::protobuf::util::MessageToJsonString(ultrasonic_cmd, &json_string);
        //AINFO << __FUNCTION__ <<" : "<< json_string;

        pWriterUltrasonicInfo->Write(ultrasonic_cmd);
    }

    void IOServer::IOExceptionHandle(const StruIOChassisInfoDebug& io_chassis_info_debug){
        // std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_);
        latExceptionHandle(io_chassis_info_debug);
        lonExceptionHandle(io_chassis_info_debug);
        IOExceptionOutputHandle(io_chassis_info_debug);
    }

    void IOServer::latExceptionHandle(const StruIOChassisInfoDebug& io_chassis_info_debug){
        if(0x0 == io_chassis_info_debug.eps_apa_control_feedback
            && 0x1 == io_chassis_info_debug.eps_apa_abort_feedback ){//DRIVER_RECOVERY
            exception_handle_info_.eps_fail = 1;
            EPSHandShakeQuit(io_chassis_info_debug);
            AINFO<<__FUNCTION__<<"failed first type, manual intervention and quit eps handshake!"<<", eps_fail="<<exception_handle_info_.eps_fail;
        }
        else if(0x0 != io_chassis_info_debug.eps_apa_abort_feedback
                    && 1 == exception_handle_info_.eps_fail){//状态流转只能从1->2
            exception_handle_info_.eps_fail = 2;
            AINFO<<__FUNCTION__<<" failed first type!"<<", eps_fail="<<exception_handle_info_.eps_fail;
        }

        if(0x0 != io_chassis_info_debug.eps_apa_abort_feedback
            && 0x1 != io_chassis_info_debug.eps_apa_abort_feedback
            && 0x7 != io_chassis_info_debug.eps_apa_abort_feedback){
            exception_handle_info_.eps_fail = 3;
            AINFO<<__FUNCTION__<<" other failed third type!"<<", eps_fail="<<exception_handle_info_.eps_fail<<" eps_apa_abort_feedback="<<io_chassis_info_debug.eps_apa_abort_feedback;
        }
        else if(0x7 == io_chassis_info_debug.eps_apa_abort_feedback){
            if(0 == io_chassis_info_debug.eps_apa_epasfailed){
                exception_handle_info_.eps_fail = 4;
                AINFO<<__FUNCTION__<<" other failed fourth type!"<<", eps_fail="<<exception_handle_info_.eps_fail<<" eps_apa_abort_feedback="<<io_chassis_info_debug.eps_apa_abort_feedback;
            }
            else{
                long spantime1 = exception_handle_info_.steeringreq_two_time - exception_handle_info_.steeringreq_one_time;
                long spantime2 = exception_handle_info_.steeringreq_fail_time - exception_handle_info_.steeringreq_one_time;
                long spantime3 = exception_handle_info_.steeringreq_two_time - exception_handle_info_.steeringreq_fail_time;
                if((spantime1 > 500 && spantime2 > 0 && spantime3 > 0) || (exception_handle_info_.steeringreq_one_time>1)){
                    exception_handle_info_.eps_fail = 5;
                    AINFO<<__FUNCTION__<<" handshake overtime! spantime="<<spantime1<<", eps_fail="<<exception_handle_info_.eps_fail;
                    AINFO<<__FUNCTION__<<"All time record: req_time="<<exception_handle_info_.current_hanshake_req_time
                                <<"req_one_time="<<exception_handle_info_.steeringreq_one_time
                                <<"req_feedback_time="<<exception_handle_info_.steeringreq_controlfeedback_time
                                <<"req_two_time="<<exception_handle_info_.steeringreq_two_time
                                <<"req_fail_time="<<exception_handle_info_.steeringreq_fail_time;
                }
            }
        }


    }

    void IOServer::lonExceptionHandle(const StruIOChassisInfoDebug& io_chassis_info_debug){
        //do nothing ,decision to handle
        if(0x1 == io_chassis_info_debug.esp_apa_driveroverrode && 1 == io_chassis_info_debug.takeover_stat){//在接管状态下，驾驶员干预踩刹车
            exception_handle_info_.esp_fail  = 1;
            AINFO<<__FUNCTION__<<" driver overrode and esp_apa_driveroverrode="<<io_chassis_info_debug.esp_apa_driveroverrode;
        }
        if(0x0 == io_chassis_info_debug.esp_apa_driveroverrode
            && 1 == exception_handle_info_.esp_fail
            && 2 == io_chassis_info_debug.takeover_stat){//
            exception_handle_info_.esp_fail  = 0;
            AINFO<<__FUNCTION__<<" reset driver overrode status and esp_apa_driveroverrode="<<io_chassis_info_debug.esp_apa_driveroverrode;
        }
    }

    // Shakes

    void IOServer::IOExceptionOutputHandle(const StruIOChassisInfoDebug& io_chassis_info_debug){
        //EPS Exception Handle
        if(0 == exception_handle_info_.eps_fail){
            output_data_chassis_.LatSt_eps_valid = CommonBool::TRUE;
            output_data_chassis_.LatSt_eps_err_type = CommonErrorType::NONE;
            output_data_chassis_.LatSt_eps_err_reason = EpsErrReason::NONE;
        }
        else if(1 == exception_handle_info_.eps_fail || 2 == exception_handle_info_.eps_fail){
            output_data_chassis_.LatSt_eps_valid = CommonBool::FALSE;
            output_data_chassis_.LatSt_eps_err_type = CommonErrorType::CURRENT_TASK_CYCLE_RECOVERABLE;
            output_data_chassis_.LatSt_eps_err_reason = EpsErrReason::HUMAN_TAKE_OVER;

            output_data_shake_hand_.lat_active = CommonBool::FALSE;
            output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::CURRENT_TASK_CYCLE_RECOVERABLE;
            output_data_shake_hand_.lat_shakehand_err_reason = LatShakeHandErrReason::HUMAN_TAKE_OVER;
        }
        else if(3 == exception_handle_info_.eps_fail){
            output_data_chassis_.LatSt_eps_valid = CommonBool::FALSE;
            output_data_chassis_.LatSt_eps_err_type = CommonErrorType::CURRENT_IGNITION_CYCLE_UNRECOVERABLE;
            output_data_chassis_.LatSt_eps_err_reason = static_cast<EpsErrReason::Enum>(io_chassis_info_debug.eps_apa_abort_feedback);

            output_data_shake_hand_.lat_active = CommonBool::FALSE;
            output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::CURRENT_IGNITION_CYCLE_UNRECOVERABLE;
            output_data_shake_hand_.lat_shakehand_err_reason = static_cast<LatShakeHandErrReason::Enum>(io_chassis_info_debug.eps_apa_abort_feedback);
        }
        else if(4 == exception_handle_info_.eps_fail){
            output_data_chassis_.LatSt_eps_valid = CommonBool::FALSE;
            output_data_chassis_.LatSt_eps_err_type = CommonErrorType::CURRENT_TASK_CYCLE_RECOVERABLE;
            output_data_chassis_.LatSt_eps_err_reason = static_cast<EpsErrReason::Enum>(io_chassis_info_debug.eps_apa_abort_feedback);

            output_data_shake_hand_.lat_active = CommonBool::FALSE;
            output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::CURRENT_TASK_CYCLE_RECOVERABLE;
            output_data_shake_hand_.lat_shakehand_err_reason = static_cast<LatShakeHandErrReason::Enum>(io_chassis_info_debug.eps_apa_abort_feedback);
        }
        else if(5 == exception_handle_info_.eps_fail){
            output_data_chassis_.LatSt_eps_valid = CommonBool::FALSE;
            output_data_chassis_.LatSt_eps_err_type = CommonErrorType::CURRENT_IGNITION_CYCLE_UNRECOVERABLE;
            output_data_chassis_.LatSt_eps_err_reason = EpsErrReason::TIME_OUT;

            output_data_shake_hand_.lat_active = CommonBool::FALSE;
            output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::CURRENT_IGNITION_CYCLE_UNRECOVERABLE;
            output_data_shake_hand_.lat_shakehand_err_reason = LatShakeHandErrReason::TIME_OUT;
        }
        //ESP Exception Handle
        if(0 == exception_handle_info_.esp_fail){
            output_data_chassis_.LonSt_esp_valid = CommonBool::TRUE;
            output_data_chassis_.LonSt_esp_err_type = CommonErrorType::NONE;
            output_data_chassis_.LonSt_esp_err_reason = EspErrReason::NONE;
        }
        else if(1 == exception_handle_info_.esp_fail){
            output_data_chassis_.LonSt_esp_valid = CommonBool::FALSE;
            output_data_chassis_.LonSt_esp_err_type = CommonErrorType::NONE;
            output_data_chassis_.LonSt_esp_err_reason = EspErrReason::HUMAN_TAKE_OVER;
        }
        output_data_chassis_.LonSt_epb_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_epb_err_type = CommonErrorType::NONE;
        output_data_chassis_.LonSt_epb_err_reason = EpbErrReason::NONE;
        output_data_chassis_.LonSt_vcu_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_vcu_err_type = CommonErrorType::NONE;
        output_data_chassis_.LonSt_vcu_err_reason = VcuErrReason::NONE;
    }

    void IOServer::IOExceptionReset(){
        exception_handle_info_.eps_fail = 0;
        exception_handle_info_.esp_fail = 0;
    }

    void IOServer::resetOutputShakeHand(){
        output_data_shake_hand_.long_active = CommonBool::NONE;
        output_data_shake_hand_.long_shakehand_err_type = CommonErrorType::NONE;
        output_data_shake_hand_.long_shakehand_err_reason = LonShakeHandErrReason::NONE;

        output_data_shake_hand_.lat_active = CommonBool::NONE;
        output_data_shake_hand_.lat_shakehand_err_type = CommonErrorType::NONE;
        output_data_shake_hand_.lat_shakehand_err_reason = LatShakeHandErrReason::NONE;
    }

    void IOServer::resetOutputDataChassis(){
        output_data_chassis_.LatSt_eps_valid = CommonBool::TRUE;
        output_data_chassis_.LatSt_eps_err_type = CommonErrorType::NONE;
        output_data_chassis_.LatSt_eps_err_reason = EpsErrReason::NONE;

        output_data_chassis_.LonSt_esp_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_esp_err_type = CommonErrorType::NONE;
        output_data_chassis_.LonSt_esp_err_reason = EspErrReason::NONE;

        output_data_chassis_.LonSt_epb_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_epb_err_type = CommonErrorType::NONE;
        output_data_chassis_.LonSt_epb_err_reason = EpbErrReason::NONE;

        output_data_chassis_.LonSt_vcu_valid = CommonBool::TRUE;
        output_data_chassis_.LonSt_vcu_err_type = CommonErrorType::NONE;
        output_data_chassis_.LonSt_vcu_err_reason = VcuErrReason::NONE;
    }

    void IOServer::io_stop(){
        if (pTimerControl != nullptr){
            pTimerControl->Stop();
        }
        if (pTimerProcessTopic != nullptr){
            pTimerProcessTopic->Stop();
        }
        if (pTimerDebug != nullptr){
            pTimerDebug->Stop();
        }
        ic_callback_run_ = false;
    }
    void IOServer::io_start(){
        pTimerControl->Start();
        pTimerProcessTopic->Start();
        pTimerDebug->Start();
        ic_callback_run_ = true;
    }

    void IOServer::timerControlHandler(){
        //pub process
        std::unique_lock<std::mutex> prase_data_lock(prase_data_mtx_, std::defer_lock);
        prase_data_lock.lock();
        StruIOChassisInfoDebug io_chassis_info_debug_tmp = io_chassis_info_debug_;
        prase_data_lock.unlock();

        static int seq = 0;
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms
        static long start_time = 0;
        static bool time_record_flag = false;
        if(false == time_record_flag){
            time_record_flag = true;
            start_time = cur_time;
        }
        if(cur_time-start_time >= 1000){
            time_record_flag = false;
            AINFO<<__func__<<
            ": seq="<<seq<<", HANDSHAKE: all="<<io_chassis_info_debug_tmp.takeover_stat
             <<", eps="<<io_chassis_info_debug_tmp.eps_takeover_stat
            <<", esp="<<io_chassis_info_debug_tmp.esp_takeover_stat
            <<", vcu="<<io_chassis_info_debug_tmp.vcu_takeover_stat
            <<", gear="<<io_chassis_info_debug_tmp.gear_takeover_stat
            <<", ult="<<io_chassis_info_debug_tmp.uss_enable;
            seq++;
            if(seq>1000){
                seq=0;
            }
        }

         if(2 != ult_enable_){//make sure ult enable
            static long start_time_ult = cur_time;
            if(cur_time-start_time_ult >= 1000){
                start_time_ult = cur_time;
                EnableUSSBaseInit();//使能ult
                ult_enable_ = 1;
            }
        }

        std::unique_lock<std::mutex> cyber_sub_lock(cyber_sub_mtx_, std::defer_lock);
        cyber_sub_lock.lock();
    #if USE_IO_CHECK
        // if(false == takeover_cmd_.is_update /* && false == io_control_cmd_.is_update */ && false == io_unittest_cmd_.is_update){
        //     cyber_sub_lock.unlock();
        //     return;//not have control cmd update
        // }
        takeover_cmd_.is_update = false;
        io_control_cmd_.is_update = false;
        // io_unittest_cmd_.is_update = false;

        TakeOverCmd takeover_cmd_tmp = takeover_cmd_;
        IOControlCmd io_control_cmd_tmp = io_control_cmd_;
        // IOUnitTestCmd io_unit_test_cmd_tmp = io_unittest_cmd_;
    #else
        // if(false == input_lat_lon_control_.is_update && false == input_shake_hand_.is_update &&
        //     false == input_hold_pressure_.is_update && false == input_system_check_.is_update ){
        //     cyber_sub_lock.unlock();
        //     return;//not have control cmd update
        // }
        input_lat_lon_control_.is_update = false;
        input_shake_hand_.is_update = false;
        input_hold_pressure_.is_update = false;
        input_system_check_.is_update = false;

        StruInputLatAndLongControlCmd input_lat_lon_control_tmp = input_lat_lon_control_;
        StruInputLatAndLongShakeHandCmd input_shake_hand_tmp = input_shake_hand_;
        // StruInputHoldPressureChangan input_hold_pressure_tmp = input_hold_pressure_;
        // input_hold_pressure_tmp.deacc = input_lat_lon_control_.tar_deceleration;
        ExceptionHandleInfo exception_handle_info_tmp = exception_handle_info_;
    #endif
        cyber_sub_lock.unlock();

        if(0 == io_chassis_info_debug_tmp.stand_still){//Not standstill
            startingPressureRelief(0.25,0x0);
        }
    #if USE_IO_CHECK
        autoHoldForbidden(io_chassis_info_debug_tmp,0x1);
        takeoverCmdHandle(takeover_cmd_tmp,io_chassis_info_debug_tmp);
        ioControlCmdHandle(io_control_cmd_tmp,io_chassis_info_debug_tmp);
        // ioUnitTestHandle(io_unit_test_cmd_tmp,io_chassis_info_debug_tmp);
    #else
        
        latLonControlCmdHandle(input_lat_lon_control_tmp,io_chassis_info_debug_tmp);
        shakeHandCmdHandle(input_shake_hand_tmp,io_chassis_info_debug_tmp,exception_handle_info_tmp);
        // holdPressureCmdHandle(input_hold_pressure_tmp);
        holdOrReleaseCmdHandle(input_lat_lon_control_tmp,io_chassis_info_debug_tmp);
    #endif
        ChassisShiftGearInfoPub();
        pubMsgInitBaseDBC(cdc_247_,cdc_2c6_);

    }

    void IOServer::timerProcessTopicHandler(){

        //sub process
        step_process_topic_ = 0;
        praseAllTopic();
        step_process_topic_ = 1;
        processSavedTopicData();

        // std::unique_lock<std::mutex> prase_data_lock(prase_data_mtx_, std::defer_lock);
        // prase_data_lock.lock();
        StruIOChassisInfoDebug io_chassis_info_debug_tmp = io_chassis_info_debug_;
        // prase_data_lock.unlock();
        IOExceptionHandle(io_chassis_info_debug_tmp);

        static int seq = 0;
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms
        static long start_time = 0;
        static bool time_record_flag = false;
        if(false == time_record_flag){
            time_record_flag = true;
            start_time = cur_time;
        }
        if(cur_time-start_time >= 1000){
            time_record_flag = false;
            seq_process_topic_ = seq;
            AINFO<<__func__<<": seq="<<seq<<", DEBUG_INFO: "
            <<"vcu_rdy_sts="<<io_chassis_info_debug_tmp.vcu_rdy_sts
            <<", eps_fail="<<exception_handle_info_.eps_fail<<", esp_fail="<<exception_handle_info_.esp_fail;

            seq++;
            if(seq>1000){
                seq=0;
            }
        }

        step_process_topic_ = 2;

        static int seq_pub = 0;
        if(++seq_pub >=2){
            seq_pub = 0;
#if USE_IO_CHECK
            ioChassisInfoDebugPub(io_chassis_info_debug_tmp);
#endif
            recordIOChassisDebugInfo(io_chassis_info_debug_tmp);

            ImuInfoPub();
            // VehicleBodyInfoPub();
            // ShakeHandStateInfoPub();
            // SystemCheckResultInfoPub();
            ChassisLatInfoPub();
            ChassisLongInfoPub();
            WheelSpeedInfoPub();
        }

        static int ult_seq_pub = 0;
        if(++ult_seq_pub >=10){
            ult_seq_pub = 0;
            if(2 == io_chassis_info_debug_tmp.uss_enable){
                UltrasonicInfoPub();
            }
        }

    }

    void IOServer::timerDebugHandler(){
        static int seq_process_topic_pre = 0;
        static int seq_control_pre = 0;

        struct timeval tv;
        gettimeofday(&tv, NULL);
        long cur_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;   //conver to ms

        static long start_time = cur_time;
        if(seq_process_topic_pre == seq_process_topic_){
            if(cur_time-start_time >= 10000){
                AERROR<<"timerProcessTopicHandler stop in"<<step_process_topic_<<" step.";
                if(0 == step_process_topic_){
                    AERROR<<"ERROR: praseAllTopic and topic is"<<prase_rev_topic_<<
                                    ", content is"<<prase_rev_topic_content_;
                }
            }
        }
        else{
            start_time = cur_time;
            seq_process_topic_pre = seq_process_topic_;
        }
    }

}

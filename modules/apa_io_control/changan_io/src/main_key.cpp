#include "io_server.h"
#include <stdio.h>

using namespace io_server;

std::string str = "************************************************************\n\
* q:    quit code\n\
* z:    breaking -1.0m/ss\n\
* x:    breaking -0.5m/ss\n\
* u:    enable ult\n\
* a:    shake hand all\n\
* g:    shake hand lat\n\
* k:    shake hand lon\n\
* h:    shake hand all quit\n\
* b:    eps control 10.0deg\n\
* n:    eps control 0.0deg\n\
* d:    gear D\n\
* r:     gear R\n\
* p:    gear P\n\
* y:    steering angle control mode, input steering angle to contorl steering angle\n\
* t:    torque control mode, input torque to contorl acc\n\
************************************************************";

IOControlCmd io_control_data_;
TakeOverCmd takeover_data_;
CDC_601 cdc_601_;

std::string data_path = "conf/io_control_test.json";
std::string node_cfg = "conf/io_server.json";
std::shared_ptr< Writer<EnableUSSUlt> > pWriterEnableUSSUlt = nullptr;
std::shared_ptr< Writer<IOTakeover> > pWriterIOTakeover = nullptr;
std::shared_ptr< Writer<IOControl> > pWriterIOControl = nullptr;

std::shared_ptr< Writer<LatAndLongShakeHandCmd> > pWriterLatAndLongShakeHandCmd = nullptr;
std::shared_ptr< Writer<LatAndLongControlCmd> > pWriterLatAndLongControlCmd = nullptr;



double getTimeS(struct timeval * pt)
{
    double N = 1000.0;
    struct timeval tv;
    if(pt==nullptr)
        gettimeofday(&tv, NULL); // get current time
    else
        tv = *pt;
    double milliseconds = tv.tv_sec * N + tv.tv_usec / N; // calculate milliseconds
    return milliseconds / 1000;
}

void pubEnableUSS(){
    EnableUSSUlt cmd;
    cmd.set_vcu_distance_cmd(cdc_601_.VCU_Distance_CMD);
    cmd.set_vcu_probecmd_num9(cdc_601_.VCU_ProbeCMD_NUM9);
    cmd.set_vcu_probecmd_num10(cdc_601_.VCU_ProbeCMD_NUM10);
    cmd.set_vcu_probecmd_num11(cdc_601_.VCU_ProbeCMD_NUM11);
    cmd.set_vcu_probecmd_num12(cdc_601_.VCU_ProbeCMD_NUM12);
    cmd.set_vcu_machine_num(cdc_601_.VCU_Machine_NUM);
    cmd.set_vcu_probecmd_num1(cdc_601_.VCU_ProbeCMD_NUM1);
    cmd.set_vcu_probecmd_num2(cdc_601_.VCU_ProbeCMD_NUM2);
    cmd.set_vcu_probecmd_num3(cdc_601_.VCU_ProbeCMD_NUM3);
    cmd.set_vcu_probecmd_num4(cdc_601_.VCU_ProbeCMD_NUM4);
    cmd.set_vcu_probecmd_num5(cdc_601_.VCU_ProbeCMD_NUM5);
    cmd.set_vcu_probecmd_num6(cdc_601_.VCU_ProbeCMD_NUM6);
    cmd.set_vcu_probecmd_num7(cdc_601_.VCU_ProbeCMD_NUM7);
    cmd.set_vcu_probecmd_num8(cdc_601_.VCU_ProbeCMD_NUM8);
    pWriterEnableUSSUlt->Write(cmd);
}

void pubTakeOver(int apa_takeover, int eps_takeover, int esp_takeover,
                                       int  vcu_takeover, int gear_takeover){
    IOTakeover cmd;
    static int64_t seq=0;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);
    cmd.set_apa_takeover(apa_takeover);
    cmd.set_eps_takeover(eps_takeover);
    cmd.set_esp_takeover(esp_takeover);
    cmd.set_vcu_takeover(vcu_takeover);
    cmd.set_gear_takeover(gear_takeover);
    pWriterIOTakeover->Write(cmd);
}

void pubShakehand(int lat_shakehnd, int lon_shakehand){
    LatAndLongShakeHandCmd cmd;
    static int64_t seq=0;
    CommonBool::Enum enum_tmp = CommonBool::NONE;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);
    if(0 == lat_shakehnd){enum_tmp = CommonBool::NONE;}
    else if(1 == lat_shakehnd){enum_tmp = CommonBool::TRUE;}
    else if(2 == lat_shakehnd){enum_tmp = CommonBool::FALSE;}
    cmd.set_activate_lat(enum_tmp);
    if(0 == lon_shakehand){enum_tmp = CommonBool::NONE;}
    else if(1 == lon_shakehand){enum_tmp = CommonBool::TRUE;}
    else if(2 == lon_shakehand){enum_tmp = CommonBool::FALSE;}
    cmd.set_activate_long(enum_tmp);
    pWriterLatAndLongShakeHandCmd->Write(cmd);
}

void pubIOControl(bool is_braking, double tar_deceleration, bool is_driving, double tar_torque,
                                       bool  is_steering, double tar_steer_angle, bool is_gear, int tar_gear){
    IOControl cmd;
    static int64_t seq=0;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);
    cmd.set_is_braking(is_braking);
    cmd.set_tar_deceleration(tar_deceleration);
    cmd.set_is_driving(is_driving);
    cmd.set_tar_torque(tar_torque);
    cmd.set_is_steering(is_steering);
    cmd.set_tar_steer_angle(tar_steer_angle);
    cmd.set_is_gear(is_gear);
    cmd.set_tar_gear(tar_gear);
    pWriterIOControl->Write(cmd);
}

void pubTorqueControlData(bool is_braking, double tar_deceleration, bool is_driving, double tar_torque,
                                       bool  is_steering, double tar_steer_angle, bool is_gear, int tar_gear){
    LatAndLongControlCmd cmd;
    static int64_t seq=0;
    CommonBool::Enum enum_tmp = CommonBool::NONE;
    auto header = cmd.mutable_header();
    header->set_timestamp(getTimeS(nullptr));
    header->set_module_name("key_io_server");
    header->set_sequence_num(seq++);
    header->set_version(0);

    TorqueControlData data_cmd;
    enum_tmp = (false == is_braking) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_driving(enum_tmp);
    data_cmd.set_tar_deceleration(tar_deceleration);
    enum_tmp = (false == is_driving) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_driving(enum_tmp);
    data_cmd.set_tar_torque(tar_torque);
    enum_tmp = (false == is_steering) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_is_steering(enum_tmp);
    data_cmd.set_tar_steer_angle(tar_steer_angle);
    enum_tmp = (false == is_gear) ? CommonBool::FALSE :CommonBool::TRUE;
    data_cmd.set_put_gear(enum_tmp);

    CommonGearPosition::Enum gear_tmp = CommonGearPosition::NONE;
    if(0 == tar_gear){gear_tmp = CommonGearPosition::NONE;}
    else if(1 == tar_gear){gear_tmp = CommonGearPosition::NEUTRAL;}
    else if(2 == tar_gear){gear_tmp = CommonGearPosition::DRIVING;}
    else if(3 == tar_gear){gear_tmp = CommonGearPosition::REVERSE;}
    else if(4 == tar_gear){gear_tmp = CommonGearPosition::PARKING;}
    data_cmd.set_tar_gear(gear_tmp);

    cmd.mutable_data()->PackFrom(data_cmd);
    pWriterLatAndLongControlCmd->Write(cmd);
}

void praseJson(){
    io_server::JsonParser ioControlConf(data_path);
    if (!ioControlConf.isValid()) {
        std::cout<<__func__<<": prase failed..."<<std::endl;
        // return false;
    }
    auto io_control_conf = ioControlConf.getJsonParser();

    cdc_601_.VCU_Distance_CMD = io_control_conf["CDC_601"]["VCU_Distance_CMD"];
    cdc_601_.VCU_ProbeCMD_NUM9 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM9"];
    cdc_601_.VCU_ProbeCMD_NUM10 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM10"];
    cdc_601_.VCU_ProbeCMD_NUM11 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM11"];
    cdc_601_.VCU_ProbeCMD_NUM12 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM12"];
    cdc_601_.VCU_Machine_NUM = io_control_conf["CDC_601"]["VCU_Machine_NUM"];
    cdc_601_.VCU_ProbeCMD_NUM1 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM1"];
    cdc_601_.VCU_ProbeCMD_NUM2 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM2"];
    cdc_601_.VCU_ProbeCMD_NUM3 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM3"];
    cdc_601_.VCU_ProbeCMD_NUM4 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM4"];
    cdc_601_.VCU_ProbeCMD_NUM5 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM5"];
    cdc_601_.VCU_ProbeCMD_NUM6 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM6"];
    cdc_601_.VCU_ProbeCMD_NUM7 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM7"];
    cdc_601_.VCU_ProbeCMD_NUM8 = io_control_conf["CDC_601"]["VCU_ProbeCMD_NUM8"];

    takeover_data_.apa_takeover = io_control_conf["TakeOver"]["apa_takeover"];
    takeover_data_.eps_takeover = io_control_conf["TakeOver"]["eps_takeover"];
    takeover_data_.esp_takeover = io_control_conf["TakeOver"]["esp_takeover"];
    takeover_data_.vcu_takeover = io_control_conf["TakeOver"]["vcu_takeover"];
    takeover_data_.gear_takeover = io_control_conf["TakeOver"]["gear_takeover"];

    io_control_data_.is_braking = io_control_conf["IOControl"]["is_braking"] == 0?false:true;
    io_control_data_.tar_deceleration = io_control_conf["IOControl"]["tar_deceleration"];
    io_control_data_.is_driving = io_control_conf["IOControl"]["is_driving"] == 0?false:true;
    io_control_data_.tar_torque = io_control_conf["IOControl"]["tar_torque"];
    io_control_data_.is_steering = io_control_conf["IOControl"]["is_steering"] == 0?false:true;
    io_control_data_.tar_steer_angle = io_control_conf["IOControl"]["tar_steer_angle"];
    io_control_data_.is_gear = io_control_conf["IOControl"]["is_gear"] == 0?false:true;
    io_control_data_.tar_gear = io_control_conf["IOControl"]["tar_gear"];

    std::cout<<"TakeOverCmd: "<<"apa_takeover="<<takeover_data_.apa_takeover<<", eps_takeover="<<takeover_data_.eps_takeover
                    <<", esp_takeover="<<takeover_data_.esp_takeover<<", vcu_takeover="<<takeover_data_.vcu_takeover
                    <<", gear_takeover="<<takeover_data_.gear_takeover<<std::endl;
    std::cout<<"IOControlCmd: "<<"is_braking="<<io_control_data_.is_braking<<", tar_deceleration="<<io_control_data_.tar_deceleration
                    <<", is_driving="<<io_control_data_.is_driving<<", tar_torque="<<io_control_data_.tar_torque
                    <<", is_steering="<<io_control_data_.is_steering<<", tar_steer_angle="<<io_control_data_.tar_steer_angle
                    <<", is_gear="<<io_control_data_.is_gear<<", tar_gear="<<io_control_data_.tar_gear<<std::endl;
}

int main(int argc, char *argv[]) {

    std::cout<<"Enter io_control_test_node..."<<std::endl;

    NodeCfg nodeCfg(node_cfg);
    if (!nodeCfg.isValid()) {
        std::cout<<"node_cfg prase failed!";
        return 0;
    }

    apollo::cyber::Init(argv[0]);
    std::shared_ptr <Node> node(apollo::cyber::CreateNode("io_control_test_node"));

    if(pWriterEnableUSSUlt == nullptr){
        auto test_enable_uss = nodeCfg.getSubChannel("test_enable_uss");
        pWriterEnableUSSUlt = node->CreateWriter<EnableUSSUlt>(test_enable_uss.name);
    }
#if USE_IO_CHECK
    if(pWriterIOTakeover == nullptr){
        auto test_io_takeover = nodeCfg.getSubChannel("test_io_takeover");
        pWriterIOTakeover = node->CreateWriter<IOTakeover>(test_io_takeover.name);
    }
    if(pWriterIOControl == nullptr){
        auto test_io_control = nodeCfg.getSubChannel("test_io_control");
        pWriterIOControl = node->CreateWriter<IOControl>(test_io_control.name);
    }
#else
    if(pWriterLatAndLongControlCmd == nullptr){
        auto io_in_chassisctl = nodeCfg.getSubChannel("io_in_chassisctl");
        pWriterLatAndLongControlCmd = node->CreateWriter<LatAndLongControlCmd>(io_in_chassisctl.name);
    }
    if(pWriterLatAndLongShakeHandCmd == nullptr){
        auto io_in_shake_hand_req = nodeCfg.getSubChannel("io_in_shake_hand_req");
        pWriterLatAndLongShakeHandCmd = node->CreateWriter<LatAndLongShakeHandCmd>(io_in_shake_hand_req.name);
    }
#endif
    praseJson();
    std::cout<<str<<std::endl;

    char ch;
    std::cout<<"[Prompt] continue test or not?  [q] for Quit: "<<std::endl;
    // scanf("%c",&ch);
    std::cin>>ch;
    // q z x u a g k h b n d r p y t
    while (ch != 'q')
    {
        /* code */
        if(ch == 'z'){//breaking -1.0m/ss
        #if USE_IO_CHECK
            pubIOControl(1,-1.0,0,0,0,0,0,0);
        #else
            pubTorqueControlData(1,-1.0,0,0,0,0,0,0);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'x'){//breaking -0.5m/ss
        #if USE_IO_CHECK
            pubIOControl(1,-0.5,0,0,0,0,0,0);
        #else
            pubTorqueControlData(1,-0.5,0,0,0,0,0,0);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'u'){
            pubEnableUSS();
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'a'){//all takeover
        #if USE_IO_CHECK
            pubTakeOver(1,0,0,0,0);
        #else
            pubShakehand(1,1);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'g'){//lat takeover
        #if USE_IO_CHECK
            pubTakeOver(0,1,0,0,0);
        #else
            pubShakehand(1,0);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'k'){//lon takeover
        #if USE_IO_CHECK
            pubTakeOver(0,0,1,1,1);
        #else
            pubShakehand(0,1);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'h'){//quit takeover
        #if USE_IO_CHECK
            pubTakeOver(2,2,2,2,2);
        #else
            pubShakehand(2,2);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'b'){//eps 10.0
        #if USE_IO_CHECK
            pubIOControl(0,0,0,0,1,10,0,0);
        #else
            pubTorqueControlData(0,0,0,0,1,10,0,0);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'n'){//eps 0.0
        #if USE_IO_CHECK
            pubIOControl(0,0,0,0,1,0,0,0);
        #else
            pubTorqueControlData(0,0,0,0,1,0,0,0);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'd'){//gear D
        #if USE_IO_CHECK
            pubIOControl(0,0,0,0,0,0,1,2);
        #else
            pubTorqueControlData(0,0,0,0,0,0,1,2);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'r'){//gear R
        #if USE_IO_CHECK
            pubIOControl(0,0,0,0,0,0,1,3);
        #else
            pubTorqueControlData(0,0,0,0,0,0,1,3);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'p'){//gear P
        #if USE_IO_CHECK
            pubIOControl(0,0,0,0,0,0,1,4);
        #else
            pubTorqueControlData(0,0,0,0,0,0,1,4);
        #endif
            std::cout <<ch<<std::endl;
        }
        else if(ch == 'y'){//steering angle control
            double steering_angle = 0;
            static double steering_angle_pre = 0;
            std::cout<<"[Prompt]  Steering angle control mode, Please input steering angle(double). [99] for Quit this loop"<<std::endl;
            std::cin>>steering_angle;
            if(abs(steering_angle_pre-steering_angle) >0.1 && abs(99-steering_angle) >0.01){
            #if USE_IO_CHECK
                pubIOControl(0,0,0,0,1,steering_angle,0,0);
            #else
                pubTorqueControlData(0,0,0,0,1,steering_angle,0,0);
            #endif
            }
        }
        else if(ch == 't'){//torque angle control
            double tar_torque = 0;
            static int torque_pre = 0;
            std::cout<<"[Prompt]  Torque control mode, Please input torque(uint).[99] for Quit this loop "<<std::endl;
            std::cin>>tar_torque;
            if(abs(torque_pre-tar_torque) >0.01 && abs(99-tar_torque) >0.01){
            #if USE_IO_CHECK
                pubIOControl(0,0,1,tar_torque,0,0,0,0);
            #else
                pubTorqueControlData(0,0,1,tar_torque,0,0,0,0);
            #endif
            }
        }
        else{
             std::cout <<"other char"<<std::endl;
        }
        std::cout<<"[Prompt] continue test or not?  [q] for Quit: "<<std::endl;
        std::cin>>ch;

    }
    std::cout<<"Quit io_control_test_node..."<<std::endl;

    return 0;
}


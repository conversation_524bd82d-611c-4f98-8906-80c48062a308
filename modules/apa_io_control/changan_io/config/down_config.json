{"DrivingInfo/Speed": "CDC_522/EspVehSpd", "GW_1C2/EspVehSpdVld": "CDC_522/EspVehSpdVld", "DrivingInfo/PowerStatus": "CDC_522/BcmPwrStsFb", "VCU_2A1/VCUGearShiftPositon": "CDC_522/VCUGearShiftPositon", "VCU_2A1/VCUShiftPostionValid": "CDC_522/VCUShiftPostionValid", "VCU_2A1/VcuRdySts": "CDC_522/VcuRdySts", "BodyInfo/LowSpeedPedestrianAlarmType/Set": "CDC_519/HuVsapSndSet", "GW_1E5/EmsEngSts": "CDC_519/EmsEngSts", "Light/HazardWarningAcoust/Set": {"CdcAcoustCtrWarnLamp": {"comments": "语音控制危险报警灯"}}, "DrivingInfo/ResetTotalDrivingInfo": {"CdcLongDrvInfoRst": {"comments": "长期行驶信息纯电复位信号(CdcLongDrvEvInfoRst-EVE)"}, "CdcLongDrvFuelInfoRst": {"comments": "长期行驶信息燃油复位信号"}}, "DrivingInfo/ResetTripA/Set": {"CdcSmallMilgRst": {"comments": "小计行驶信息纯电复位信号(CdcSmallMilgEvRst-EVE)"}, "CdcSmallMilgFuelRst": {"comments": "小计行驶信息燃油复位信号"}}, "DrivingInfo/FuelTotalDriveRange": {"CDC_614/CdcOilTotMilg": {"comments": "燃料模式总计行驶里程"}}, "DrivingInfo/EVLongDriveRange": {"CDC_356/CdcLongEvlMilg": {"comments": "长期纯电行驶距离"}, "CDC_2E2/CdcLongEvlMilg": {}}, "DrivingInfo/EVTripADriveRange": {"CDC_356/VcuSmallEvMilg": {"comments": "小计里程纯电行驶距离"}, "CDC_2E2/VcuSmallEvMilg": {}}, "DrivingInfo/EVTripDriveRange": {"CDC_356/CdcAftStrtEvMilg": {"comments": "自启动后纯电行驶距离"}, "CDC_2E2/CdcAftStrtEvMilg": {}}, "DrivingInfo/EVLastChargeDriveRange": {"CDC_356/CdcAftPwrSplyEvMilg": {"comments": "补能后纯电行驶距离"}, "CDC_2E2/CdcAftPwrSplyEvMilg": {}}, "DMS/DetectedStatus": {"DMS/DetectedStatus": {"comments": "DMS识别结果"}}, "AC/FastHeat/Set": {"CdcAcFastHeatSetSts": {"comments": "空调快速升温开关"}}, "C385/WinterMode/Set": {"C385/WinterMode/Set": {"comments": "冬季模式"}}, "DMS/SmokeModeAirClean/Set": {"DmsSmokeModAirCleanReq": {"comments": "抽烟空气净化请求"}}, "DMS/SmokeModeWindowDown/Set": {"DmsSmokeWinDownReq": {"comments": "抽烟降窗请求"}}, "DMS/FatigueDetectSwitch/Set": {"DMS/FatigueDetectSwitch/Set": {"comments": "疲劳检测开关 Set"}}, "CDC_356/CdcSmokeACStartReq/Set": "CDC_356/CdcSmokeACStartReq", "CDC_356/CdcSmokeOuterCircReq/Set": "CDC_356/CdcSmokeOuterCircReq", "ControlScreen/ControlScreenCalibration/Set": {"CDC_52C/CdcCtrlTarAg": {"comments": "旋转机构校准"}, "CDC_52C/CdcStsSyncReq": {"comments": ""}}, "DrivingInfo/DrivingInfoType": {"DrivingInfo/DrivingInfoType": {"comments": "行驶信息设置"}}, "DrivingInfo/RealPowerStatus": {"CDC_2D2/CdcOffgasDetnSetSts": {"valueMap": {"0": 1}, "defaultValueType": 1, "comments": "当电源为0ff是，发送尾气检测为关闭"}}, "CDC_2D2/CdcFaceRegistExitSts/Set": "CDC_2D2/CdcFaceRegistExitSts", "CDC_2D2/CdcFaceRegistAgainSts/Set": "CDC_2D2/CdcFaceRegistAgainSts", "CDC_2D2/CdcFaceRegistOkSts/Set": "CDC_2D2/CdcFaceRegistOkSts", "CDC_2D2/DmsFaceAccountReq/Set": "CDC_2D2/DmsFaceAccountReq", "CDC_2D2/CdcFaceAccountID/Set": "CDC_2D2/CdcFaceAccountID", "CDC_2D2/CdcCurrAccountFaceID/Set": "CDC_2D2/CdcCurrAccountFaceID", "CDC_2D2/CdcFaceRegistReq/Set": "CDC_2D2/CdcFaceRegistReq", "CDC_2D2/CdcFaceRecognizeNoSts/Set": "CDC_2D2/CdcFaceRecognizeNoSts", "CDC_2D2/CdcFaceRecognizeYesSts/Set": "CDC_2D2/CdcFaceRecognizeYesSts", "CDC_3AF/CDC_DVR_SdcardErrorStatus/Set": "CDC_3AF/CDC_DVR_SdcardErrorStatus", "CDC_3AF/CDC_DVR_Error/Set": "CDC_3AF/CDC_DVR_Error", "CDC_507/HU_DDSAS_Enable/Set": "CDC_507/HU_DDSAS_Enable", "CDC_507/HU_CompanionAssitModeEnable/Set": "CDC_507/HU_CompanionAssitModeEnable", "CDC_507/HU_Parking_Asistant_Syste_Enable/Set": "CDC_507/HU_Parking_Asistant_Syste_Enable", "CDC_507/HU_Automated_Valet_Parking_Enable/Set": "CDC_507/HU_Automated_Valet_Parking_Enable", "CDC_507/HU_Novice_Enable/Set": "CDC_507/HU_Novice_Enable", "CDC_507/HU_SEAEnable/Set": "CDC_507/HU_SEAEnable", "CDC_507/HU_LaneChangeStyle/Set": "CDC_507/HU_LaneChangeStyle", "CDC_507/HU_NDAAudioWarEnable/Set": "CDC_507/HU_NDAAudioWarEnable", "CDC_394/HU_ADSLaneChangeVoiceEnable/Set": "CDC_394/HU_ADSLaneChangeVoiceEnable", "CDC_394/HU_ADSSafeVoiceEnable/Set": "CDC_394/HU_ADSSafeVoiceEnable", "CDC_394/HU_ADSRoadVoiceEnable/Set": "CDC_394/HU_ADSRoadVoiceEnable", "CDC_507/HU_NDANavEnable/Set": "CDC_507/HU_NDANavEnable", "CDC_507/HU_NDASetSpdMode/Set": "CDC_507/HU_NDASetSpdMode", "CDC_2D2/CdcRespiteModOffReq/Set": "CDC_2D2/CdcRespiteModOffReq", "CDC_3BD/HU_ScreenOperateStatus/Set": "CDC_3BD/HU_ScreenOperateStatus", "CDC_2D2/CdcFaceRegistContinSts/Set": "CDC_2D2/CdcFaceRegistContinSts", "CDC_2D2/CdcDrvrFatigueDetdReq/Set": "CDC_2D2/CdcDrvrFatigueDetdReq", "CDC_2D2/CdcAttentionDetdSet/Set": "CDC_2D2/CdcAttentionDetdSet", "CDC_2D2/CdcSmokeModSetSts/Set": "CDC_2D2/CdcSmokeModSetSts", "CDC_507/HU_DVREmergencyRecord/Set": "CDC_507/HU_DVREmergencyRecord", "CDC_2F2/CdcACEnergyModSetReq/Set": "CDC_2F2/CdcACEnergyModSetReq", "CDC_37A/HU_WirelessChargingSet/Set": "CDC_37A/HU_WirelessChargingSet", "BluetoothPhone/PrivacyModeText/Set": {"BluetoothPhone/PrivacyModeText": {"defaultValueType": 2, "comments": "隐私模式"}}, "BodyInfo/FrontRadar/Set": {"BodyInfo/FrontRadar": {"comments": "前雷达的开关"}}, "CDC_37A/CDC_IntLampFunSet/Set": "CDC_37A/CDC_IntLampFunSet", "CDC_3BD/HU_dayandnight_mode/Set": "CDC_3BD/HU_dayandnight_mode", "CDC_3BD/HU_AVMCloseMethod/Set": "CDC_3BD/HU_AVMCloseMethod", "CDC_3BD/HU_DVRTriggerMethod/Set": "CDC_3BD/HU_DVRTriggerMethod", "CDC_507/HU_DVRFormatReq/Set": "CDC_507/HU_DVRFormatReq", "CDC_2D2/CdcH2ChrgDoorLckReq/Set": "CDC_2D2/CdcH2ChrgDoorLckReq", "CDC_2D2/CdcH2ChrgDoorLckEndReq/Set": "CDC_2D2/CdcH2ChrgDoorLckEndReq", "APA/APAEnable/Set": "CDC_507/HU_APAOnOff", "HUD_CDC_5C5/HudSwVers/Set": {"HUD_CDC_5C5/HudSwVers3": {}, "HUD_CDC_5C5/HudSwVers5": {}, "HUD_CDC_5C5/HudSwVers0": {}, "HUD_CDC_5C5/HudSwVers1": {}, "HUD_CDC_5C5/HudSwVers2": {}, "HUD_CDC_5C5/HudSwVers4": {}, "HUD_CDC_5C5/HudSwVers6": {}}, "HUD_CDC_5C5/HudHwVers/Set": {"HUD_CDC_5C5/HudHwVers3": {}, "HUD_CDC_5C5/HudHwVers5": {}, "HUD_CDC_5C5/HudHwVers0": {}, "HUD_CDC_5C5/HudHwVers1": {}, "HUD_CDC_5C5/HudHwVers2": {}, "HUD_CDC_5C5/HudHwVers4": {}, "HUD_CDC_5C5/HudHwVers6": {}}, "HUD_CDC_660/HudDtc1/Set": {"HUD_CDC_660/HudDtc1LoByte": {}, "HUD_CDC_660/HudDtc1HiByte": {}, "HUD_CDC_660/HudDtc1MidByte": {}, "HUD_CDC_660/HudDtc1Sts": {}}, "HUD_CDC_660/HudDtc2/Set": {"HUD_CDC_660/HudDtc2LoByte": {}, "HUD_CDC_660/HudDtc2HiByte": {}, "HUD_CDC_660/HudDtc2MidByte": {}, "HUD_CDC_660/HudDtc2Sts": {}}, "CDC_2D2/CdcFaceDectSetReq/Set": "CDC_2D2/CdcFaceDectSetReq", "CDC_2D2/CdcBlindZoneFunSet/Set": "CDC_2D2/CdcBlindZoneFunSet", "CDC_2D2/CdcBattPlsHeatgSet/Set": "CDC_2D2/CdcBattPlsHeatgSet", "CDC_37A/CdcFrntWiperSensitSet/Set": "CDC_37A/CdcFrntWiperSensitSet", "CDC_2D2/CdcRemVideoPwrUpReq/Set": "CDC_2D2/CdcRemVideoPwrUpReq", "CDC_2D2/CdcSentryModeRemindReq/Set": "CDC_2D2/CdcSentryModeRemindReq", "CDC_2D2/CdcRearWiperSet/Set": "CDC_2D2/CdcRearWiperSet", "CDC_2D2/CdcRearWshrReq/Set": "CDC_2D2/CdcRearWshrReq", "CDC_2D2/CdcSentryModeWorkReq/Set": "CDC_2D2/CdcSentryModeWorkReq", "CDC_2D2/CST_OFF_Switch/Set": "CDC_2D2/CST_OFF_Switch", "CDC_507/HU_SmartDrivingStyle/Set": "CDC_507/HU_SmartDrivingStyle", "CDC_52C/CdcTurnVoiceCtrl/Set": "CDC_52C/CdcTurnVoiceCtrl", "CDC_52C/CdcTurnExpandSet/Set": "CDC_52C/CdcTurnExpandSet", "CDC_52C/CdcTurnFoldSet/Set": "CDC_52C/CdcTurnFoldSet", "CDC_37A/CdcTurnTvModSet/Set": "CDC_37A/CdcTurnTvModSet", "CDC_2D2/CdcDrvrModUseSts/Set": "CDC_2D2/CdcDrvrModUseSts", "SunShade/ControlReq1/Set": "CDC_LIN_1/CDC_SunShade_ControlReq1", "SunShade/ControlReq2/Set": "CDC_LIN_1/CDC_SunShade_ControlReq2", "CDC_2D2/CdcFaceRecManuReq/Set": "CDC_2D2/CdcFaceRecManuReq", "CDC_3BD/HU_AVMRearMirrorSetReady/Set": "CDC_3BD/HU_AVMRearMirrorSetReady", "CDC_3BD/HU_AVMReviewMirrorWindowSts/Set": "CDC_3BD/HU_AVMReviewMirrorWindowSts", "CDC_2D2/CdcDrvrModResetReq/Set": "CDC_2D2/CdcDrvrModResetReq", "CDC_507/HU_VC_CruiseSetDistance/Set": "CDC_507/HU_VC_CruiseSetDistance", "CDC_5F7/ADCSwVers": {"CDC_5F7/ADCSwVers0": {}, "CDC_5F7/ADCSwVers1": {}, "CDC_5F7/ADCSwVers2": {}, "CDC_5F7/ADCSwVers3": {}, "CDC_5F7/ADCSwVers4": {}, "CDC_5F7/ADCSwVers5": {}, "CDC_5F7/ADCSwVers6": {}, "CDC_5F7/ADCHwVers0": {}, "CDC_5F7/ADCHwVers1": {}, "CDC_5F7/ADCHwVers2": {}, "CDC_5F7/ADCHwVers3": {}, "CDC_5F7/ADCHwVers4": {}, "CDC_5F7/ADCHwVers5": {}, "CDC_5F7/ADCHwVers6": {}}, "CDC_312/ADS": {"CDC_312/ADS_UDLCVoiceRecStatus": {}, "CDC_312/ADS_ALCStatus": {}}, "CDC_244/ACC": {"CDC_244/ACC_ACCTargetAcceleration": {}, "CDC_244/ACC_LDWVibrationWarningReq": {}, "CDC_244/ADCReqMode": {}, "CDC_244/ACC_LDWShakeLevStatus": {}, "CDC_244/ACC_DecToStop": {}, "CDC_244/ACC_CDDActive": {}, "CDC_244/ACC_RollingCounter_24E": {}, "CDC_244/ACC_ACCMode": {}, "CDC_244/ACC_Driveoff_Request": {}, "CDC_244/ACC_CRCCheck_24E": {}, "CDC_244/ACC_AEBTargetDeceleration": {}, "CDC_244/ACC_AWBlevel": {}, "CDC_244/ACC_ABAActive": {}, "CDC_244/ACC_ABAlevel": {}, "CDC_244/ACC_AEBActive": {}, "CDC_244/ACC_AccTrqReq": {}, "CDC_244/ACC_AEBVehilceHoldReq": {}, "CDC_244/ACC_PrefillActive": {}, "CDC_244/ACC_AWBActive": {}, "CDC_244/ACC_AEBCtrlType": {}, "CDC_244/ACC_AccTrqReqActive": {}, "CDC_244/ACC_RollingCounter_25E": {}, "CDC_244/ACC_CRCCheck_25E": {}, "CDC_244/ADS_DDSASafetyStopCallReq": {}, "CDC_244/ADS_RollingCounter_244": {}, "CDC_244/ADC_DDSAStatus": {}, "CDC_244/ADS_CRCCheck_244": {}}, "CDC_307/ACC": {"CDC_307/ACC_ACCEPBrequest": {}, "CDC_307/ACC_SaftyBeltVibrationReq": {}, "CDC_307/ACC_LngTakeOverReqReason": {}, "CDC_307/ACC_RollingCounter_307": {}, "CDC_307/ACC_CRCCheck_307": {}}, "CDC_6F9/ADS_DTC": {"CDC_6F9/ADS_DTC1_HighByte": {}, "CDC_6F9/ADS_DTC1_MiddByte": {}, "CDC_6F9/ADS_DTC1_LowByte": {}, "CDC_6F9/ADS_DTC1_Status": {}, "CDC_6F9/ADS_DTC2_HighByte": {}, "CDC_6F9/ADS_DTC2_MiddByte": {}, "CDC_6F9/ADS_DTC2_LowByte": {}, "CDC_6F9/ADS_DTC2_Status": {}}, "CDC_247/APA": {"CDC_247/APA_SteeringAngleReqProtection": {}, "CDC_247/APA_ErrorStatus": {}, "CDC_247/APA_indication": {}, "CDC_247/APA_APAOnOff": {}, "CDC_247/APA_EmergenceBrake": {}, "CDC_247/APA_SteeringAngleReq": {}, "CDC_247/APA_RemoteOnOff": {}, "CDC_247/APA_ButtonPress": {}, "CDC_247/APA_IncreasePressureReq": {}, "CDC_247/APA_TurnLightsCommand": {}, "CDC_247/APA_ParkNotice_4": {}, "CDC_247/APA_ParkNotice": {}, "CDC_247/APA_ParkingPercentage": {}, "CDC_247/APA_RollingCounter_264": {}, "CDC_247/APA_CRCCheck_264": {}, "CDC_247/APA_EPBrequest": {}, "CDC_247/APA_EPBrequestValid": {}, "CDC_247/APA_TargetAccelerationValid": {}, "CDC_247/APA_TransPRNDShiftRequest": {}, "CDC_247/APA_TransPRNDShiftReqValid": {}, "CDC_247/APA_TargetAcceleration": {}, "CDC_247/APA_EngTorqReq": {}, "CDC_247/APA_Activation_Status": {}, "CDC_247/APA_TransPRNDShiftEnable": {}, "CDC_247/APA_LSCAction": {}, "CDC_247/APA_HSAHDforbidden": {}, "CDC_247/APA_EngineTrqReqEnable": {}, "CDC_247/APA_AccPedShieldReq": {}, "CDC_247/APA_ESPDecompressionModel": {}, "CDC_247/APA_PrefillReq": {}, "CDC_247/APA_DynamicSlotWarning": {}, "CDC_247/APA_SlotNotice": {}, "CDC_247/APA_TCUClutchCombinationReq": {}, "CDC_247/APA_TrqHoldForTCUCl": {}, "CDC_247/APA_RollingCounter_26C": {}, "CDC_247/APA_CRCCheck_26C": {}, "CDC_247/APA_PtTorqReq": {}, "CDC_247/APA_ESPDistToStop": {}, "CDC_247/APA_ESP_BrakeFunctionMode": {}, "CDC_247/APA_PtTrqReqValid": {}, "CDC_247/APA_VCUReadyReq": {}, "CDC_247/APA_ESP_StandstillRequest": {}, "CDC_247/APA_RollingCounter_236": {}, "CDC_247/APA_CRC_Checksum_236": {}, "CDC_247/APA_RollingCounter_247": {}, "CDC_247/APA_CRCCheck_247": {}}, "CDC_2C6/APA": {"CDC_2C6/APA_ParkNotice_5": {}, "CDC_2C6/APA_LAEBReq": {}, "CDC_2C6/APA_LAEBStatus": {}, "CDC_2C6/APA_BLEConnectionRemind": {}, "CDC_2C6/APA_LAEBNotice": {}, "CDC_2C6/APA_RemoteParkingUsingRemind": {}, "CDC_2C6/APA_ASPAvailableStatus": {}, "CDC_2C6/APA_ASPStatus": {}, "CDC_2C6/APA_CrossModeSelectReq": {}, "CDC_2C6/APA_BCMHornCommand": {}, "CDC_2C6/APA_vehicleFrontdetect": {}, "CDC_2C6/APA_ReleasePressureReq": {}, "CDC_2C6/APA_PEPS_EngineOffLockRequest": {}, "CDC_2C6/APA_RADSNotice": {}, "CDC_2C6/APA_RollingCounter_2D4": {}, "CDC_2C6/APA_PEPS_EngineOffRequest": {}, "CDC_2C6/APA_CRCCheck_2D4": {}, "CDC_2C6/APA_RollingCounter_2C6": {}, "CDC_2C6/APA_CRCCheck_2C6": {}}, "CDC_6E0/APA_AuthenticationStatus": "CDC_6E0/APA_AuthenticationStatus", "CDC_31E/RR": {"CDC_31E/RRS_RRSDistance": {}, "CDC_31E/RRS_SideZoneStatus_Front": {}, "CDC_31E/RRS_WarningFrequency": {}, "CDC_31E/RRS_SideZoneStatus_Rear": {}, "CDC_31E/RRS_WarningType": {}, "CDC_31E/RRS_SwitchIndicatorError": {}, "CDC_31E/APA_SystemFailureFlag": {}}, "CDC_1BA/ACC": {"CDC_1BA/ACC_MotorTorqueMaxLimitRequest": {}, "CDC_1BA/ACC_MotorTorqueMinLimitRequest": {}, "CDC_1BA/ACC_LatAngReq": {}, "CDC_1BA/ADS_ESSActive": {}, "CDC_1BA/ACC_LatAngReqActive": {}, "CDC_1BA/ACC_RollingCounter_1BA_0": {}, "CDC_1BA/ACC_CRCCheck_1BA_0": {}, "CDC_1BA/ADS_ErrorStatus": {}, "CDC_1BA/ACC_ADCReqType": {}, "CDC_1BA/ADS_Reqmode": {}, "CDC_1BA/ACC_RollingCounter_1BA_1": {}, "CDC_1BA/ACC_CRCCheck_1BA_1": {}}, "CDC_693/FR": {"CDC_693/FR_DTC1_HighByte": {}, "CDC_693/FR_DTC1_MiddByte": {}, "CDC_693/FR_DTC1_LowByte": {}, "CDC_693/FR_DTC1_Status": {}, "CDC_693/FR_DTC2_HighByte": {}, "CDC_693/FR_DTC2_MiddByte": {}, "CDC_693/FR_DTC2_LowByte": {}, "CDC_693/FR_DTC2_Status": {}}, "CDC_6E3/FLR": {"CDC_6E3/FLR_DTC1_HighByte": {}, "CDC_6E3/FLR_DTC1_MiddByte": {}, "CDC_6E3/FLR_DTC1_LowByte": {}, "CDC_6E3/FLR_DTC1_Status": {}, "CDC_6E3/FLR_DTC2_HighByte": {}, "CDC_6E3/FLR_DTC2_MiddByte": {}, "CDC_6E3/FLR_DTC2_LowByte": {}, "CDC_6E3/FLR_DTC2_Status": {}}, "CDC_697/RLR": {"CDC_697/RLR_DTC1_HighByte": {}, "CDC_697/RLR_DTC1_MiddByte": {}, "CDC_697/RLR_DTC1_LowByte": {}, "CDC_697/RLR_DTC1_Status": {}, "CDC_697/RLR_DTC2_HighByte": {}, "CDC_697/RLR_DTC2_MiddByte": {}, "CDC_697/RLR_DTC2_LowByte": {}, "CDC_697/RLR_DTC2_Status": {}}, "CDC_6F0/FRR": {"CDC_6F0/FRR_DTC1_HighByte": {}, "CDC_6F0/FRR_DTC1_MiddByte": {}, "CDC_6F0/FRR_DTC1_LowByte": {}, "CDC_6F0/FRR_DTC1_Status": {}, "CDC_6F0/FRR_DTC2_HighByte": {}, "CDC_6F0/FRR_DTC2_MiddByte": {}, "CDC_6F0/FRR_DTC2_LowByte": {}, "CDC_6F0/FRR_DTC2_Status": {}}, "CDC_6DB/RRR": {"CDC_6DB/RRR_DTC1_HighByte": {}, "CDC_6DB/RRR_DTC1_MiddByte": {}, "CDC_6DB/RRR_DTC1_LowByte": {}, "CDC_6DB/RRR_DTC1_Status": {}, "CDC_6DB/RRR_DTC2_HighByte": {}, "CDC_6DB/RRR_DTC2_MiddByte": {}, "CDC_6DB/RRR_DTC2_LowByte": {}, "CDC_6DB/RRR_DTC2_Status": {}}, "CDC_2B9/APA": {"CDC_2B9/APA_FunctionOnOffSts": {}, "CDC_2B9/APA_Condition_Notice": {}, "CDC_2B9/APA_TouchInfOnOffRes": {}, "CDC_2B9/APA_AVP_Notice": {}, "CDC_2B9/APA_HZP_Notice": {}, "CDC_2B9/APA_ViewActual": {}, "CDC_2B9/APA_Summon_Notice": {}, "CDC_2B9/APA_ActivationSts": {}, "CDC_2B9/APA_ReadySts": {}, "CDC_2B9/APA_ParkingSlot_ExtraFeature": {}, "CDC_2B9/APA_ParkingSlot_Type": {}, "CDC_2B9/APA_TurnOnMode": {}}, "CDC_286/ADS": {"CDC_286/ADS_SYNC_Type": {}, "CDC_286/ADS_SYNC_CRC": {}, "CDC_286/ADS_SYNC_SequenceCnt": {}, "CDC_286/ADS_SYNC_TimeDomain": {}, "CDC_286/ADS_SYNC_OVS_SGW": {}, "CDC_286/ADS_SYNC_SyncTime": {}}, "CDC_572/FRSwVers": {"CDC_572/FRSwVers0": {}, "CDC_572/FRSwVers1": {}, "CDC_572/FRSwVers2": {}, "CDC_572/FRSwVers3": {}, "CDC_572/FRSwVers4": {}, "CDC_572/FRSwVers5": {}, "CDC_572/FRSwVers6": {}, "CDC_572/FRHwVers0": {}, "CDC_572/FRHwVers1": {}, "CDC_572/FRHwVers2": {}, "CDC_572/FRHwVers3": {}, "CDC_572/FRHwVers4": {}, "CDC_572/FRHwVers5": {}, "CDC_572/FRHwVers6": {}}, "CDC_573/FLRSwVers": {"CDC_573/FLRSwVers0": {}, "CDC_573/FLRSwVers1": {}, "CDC_573/FLRSwVers2": {}, "CDC_573/FLRSwVers3": {}, "CDC_573/FLRSwVers4": {}, "CDC_573/FLRSwVers5": {}, "CDC_573/FLRSwVers6": {}, "CDC_573/FLRHwVers0": {}, "CDC_573/FLRHwVers1": {}, "CDC_573/FLRHwVers2": {}, "CDC_573/FLRHwVers3": {}, "CDC_573/FLRHwVers4": {}, "CDC_573/FLRHwVers5": {}, "CDC_573/FLRHwVers6": {}}, "CDC_575/RLRSwVers": {"CDC_575/RLRSwVers0": {}, "CDC_575/RLRSwVers1": {}, "CDC_575/RLRSwVers2": {}, "CDC_575/RLRSwVers3": {}, "CDC_575/RLRSwVers4": {}, "CDC_575/RLRSwVers5": {}, "CDC_575/RLRSwVers6": {}, "CDC_575/RLRHwVers0": {}, "CDC_575/RLRHwVers1": {}, "CDC_575/RLRHwVers2": {}, "CDC_575/RLRHwVers3": {}, "CDC_575/RLRHwVers4": {}, "CDC_575/RLRHwVers5": {}, "CDC_575/RLRHwVers6": {}}, "CDC_574/FRRSwVers": {"CDC_574/FRRSwVers0": {}, "CDC_574/FRRSwVers1": {}, "CDC_574/FRRSwVers2": {}, "CDC_574/FRRSwVers3": {}, "CDC_574/FRRSwVers4": {}, "CDC_574/FRRSwVers5": {}, "CDC_574/FRRSwVers6": {}, "CDC_574/FRRHwVers0": {}, "CDC_574/FRRHwVers1": {}, "CDC_574/FRRHwVers2": {}, "CDC_574/FRRHwVers3": {}, "CDC_574/FRRHwVers4": {}, "CDC_574/FRRHwVers5": {}, "CDC_574/FRRHwVers6": {}}, "CDC_581/RRRSwVers": {"CDC_581/RRRSwVers0": {}, "CDC_581/RRRSwVers1": {}, "CDC_581/RRRSwVers2": {}, "CDC_581/RRRSwVers3": {}, "CDC_581/RRRSwVers4": {}, "CDC_581/RRRSwVers5": {}, "CDC_581/RRRSwVers6": {}, "CDC_581/RRRHwVers0": {}, "CDC_581/RRRHwVers1": {}, "CDC_581/RRRHwVers2": {}, "CDC_581/RRRHwVers3": {}, "CDC_581/RRRHwVers4": {}, "CDC_581/RRRHwVers5": {}, "CDC_581/RRRHwVers6": {}}, "CDC_31A/ACC_LatTakeoverReqReason": "CDC_31A/ACC_LatTakeoverReqReason", "CDC_594/FR": {"CDC_594/FR_WakeUp_BYTE4BIT0": {}, "CDC_594/FR_WakeUp_BYTE4BIT1": {}, "CDC_594/FR_NotSleep_BYTE7BIT0": {}, "CDC_594/FR_NotSleep_BYTE7BIT1": {}}, "CDC_598/FRR": {"CDC_598/FRR_WakeUp_BYTE4BIT0": {}, "CDC_598/FRR_WakeUp_BYTE4BIT1": {}, "CDC_598/FRR_NotSleep_BYTE7BIT0": {}, "CDC_598/FRR_NotSleep_BYTE7BIT1": {}}, "CDC_5A2/FLR": {"CDC_5A2/FLR_WakeUp_BYTE4BIT0": {}, "CDC_5A2/FLR_WakeUp_BYTE4BIT1": {}, "CDC_5A2/FLR_NotSleep_BYTE7BIT0": {}, "CDC_5A2/FLR_NotSleep_BYTE7BIT1": {}}, "CDC_596/RLR": {"CDC_596/RLR_WakeUp_BYTE4BIT0": {}, "CDC_596/RLR_WakeUp_BYTE4BIT1": {}, "CDC_596/RLR_NotSleep_BYTE7BIT0": {}, "CDC_596/RLR_NotSleep_BYTE7BIT1": {}}, "CDC_597/RRR": {"CDC_597/RRR_WakeUp_BYTE4BIT0": {}, "CDC_597/RRR_WakeUp_BYTE4BIT1": {}, "CDC_597/RRR_NotSleep_BYTE7BIT0": {}, "CDC_597/RRR_NotSleep_BYTE7BIT1": {}}, "CDC_58F/ADC": {"CDC_58F/ADC_WakeUp_BYTE4BIT0": {}, "CDC_58F/ADC_WakeUp_BYTE4BIT1": {}, "CDC_58F/ADC_NotSleep_BYTE7BIT0": {}, "CDC_58F/ADC_NotSleep_BYTE7BIT1": {}}, "CDC_325/ADC": {"CDC_325/ADC_UTCTiYear": {}, "CDC_325/ADC_UTCTiDate": {}, "CDC_325/ADC_UTCTiMth": {}, "CDC_325/ADC_UTCTiMins": {}, "CDC_325/ADC_UTCTiHr": {}, "CDC_325/ADC_UTCTiSec": {}, "CDC_325/ADC_UTCTiVld": {}}, "CDC_525/TC397_TimeSyncInit": "CDC_525/TC397_TimeSyncInit", "CDC_303/ADC_PowerSwitchFlag": "CDC_303/ADC_PowerSwitchFlag", "CDC_601/VCU": {"CDC_601/VCU_Distance_CMD": {}, "CDC_601/VCU_ProbeCMD_NUM9": {}, "CDC_601/VCU_ProbeCMD_NUM10": {}, "CDC_601/VCU_ProbeCMD_NUM11": {}, "CDC_601/VCU_ProbeCMD_NUM12": {}, "CDC_601/VCU_Machine_NUM": {}, "CDC_601/VCU_ProbeCMD_NUM1": {}, "CDC_601/VCU_ProbeCMD_NUM2": {}, "CDC_601/VCU_ProbeCMD_NUM3": {}, "CDC_601/VCU_ProbeCMD_NUM4": {}, "CDC_601/VCU_ProbeCMD_NUM5": {}, "CDC_601/VCU_ProbeCMD_NUM6": {}, "CDC_601/VCU_ProbeCMD_NUM7": {}, "CDC_601/VCU_ProbeCMD_NUM8": {}}}
#ifndef __APA_IO_TOPIC_H__
#define __APA_IO_TOPIC_H__

#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/common/proto/header.pb.h"
using rainbowdash::common::CommonBool;
using rainbowdash::common::CommonErrorType;
using rainbowdash::control_by_wire::TorqueControlData;
using rainbowdash::control_by_wire::SystemCheckErrReason;
using rainbowdash::control_by_wire::EpsErrReason;
using rainbowdash::control_by_wire::EpbErrReason;
using rainbowdash::control_by_wire::EspErrReason;
using rainbowdash::control_by_wire::VcuErrReason;
using rainbowdash::control_by_wire::LatShakeHandErrReason;
using rainbowdash::control_by_wire::LonShakeHandErrReason;


#define SUB_INS_146                                                                              "INS_146"
#define SUB_VCU_161                                                                             "VCU_161"
#define SUB_GW_170                                                                              "GW_170"
#define SUB_GW_180                                                                              "GW_180"
#define SUB_INS_271                                                                              "INS_271"
#define SUB_GW_17A                                                                              "GW_17A"
#define SUB_GW_17E                                                                              "GW_17E"
#define SUB_INS_17F                                                                              "INS_17F"
#define SUB_GW_1C2                                                                              "GW_1C2"
#define SUB_GW_20B                                                                              "GW_20B"
#define SUB_GW_24F                                                                              "GW_24F"
#define SUB_GW_2C2                                                                              "GW_2C2"
#define SUB_GW_3C2                                                                              "GW_3C2"
#define SUB_INS_5B3                                                                             "INS_5B3"
#define SUB_USS_611_USS_Ult_Probe_infoA                             "USS_611"
#define SUB_USS_612_USS_Ult_Probe_infoB                             "USS_612"
#define SUB_USS_613_USS_Ult_Probe_infoB                             "USS_613"

#define SUB_ICMSG_650                             "ICMSG_650"
#define SUB_ICMSG_651                             "ICMSG_651"
#define SUB_ICMSG_652                             "ICMSG_652"
#define SUB_ICMSG_653                             "ICMSG_653"
#define SUB_ICMSG_654                             "ICMSG_654"

namespace io_server{

template <typename T> 
class ZeroStruct 
{ 
public: 
    ZeroStruct() 
    { 
        memset(this,0,sizeof(T)); 
    } 
};

struct Sub_USS_611_USS_Ult_Probe_infoA
{
    int Ult_Probe_info1;//1
    int Ult_Probe_info2;//2
    int Ult_Probe_info3;//3
    int Ult_Probe_info4;//4
};

struct Sub_USS_612_USS_Ult_Probe_infoB
{
    int Ult_Probe_info5;//5
    int Ult_Probe_info6;//6
    int Ult_Probe_info7;//
    int Ult_Probe_info8;//
};

struct Sub_USS_613_USS_Ult_Probe_infoB
{
    int Ult_Probe_info9;//
    int Ult_Probe_info10;//
    int Ult_Probe_info11;//
    int Ult_Probe_info12;//
};

/* needby chang an topic */
struct INS_146
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double INS_Current_Pos_X_Accel;//X向加速度 m/s2
    double INS_Current_Pos_Y_Accel;//Y向加速度
    double INS_Pos_X_Accel_Bias;//X向加速度零偏值
    double INS_Pos_Y_Accel_Bias;//Y向加速度零偏值
    double INS_Current_Pos_Z_Accel;//Z向加速度
    double INS_Pos_Z_Accel_Bias;
    double INS_Current_Pos_X_Rate;//陀螺X轴角速度
    double INS_Pos_X_Rate_Bias;//X向角速度零偏值
    double INS_Current_Pos_Y_Rate;//陀螺Y轴角速度
    double INS_Pos_Y_Rate_Bias;//Y向加速度零偏值
    double INS_Current_Pos_Z_Rate;//陀螺Z轴角速度
    double INS_Pos_Z_Rate_Bias;//Z向角速度零偏值
    double INS_Quaternion_X;//姿态四元数x
    double INS_Quaternion_Y;//姿态四元数y
    double INS_Quaternion_Z;//姿态四元数z
    double INS_Quaternion_W;//姿态四元数w
    int INS_IMU_Time;//IMU时间
    int INS_TiOut;//INS时间同步超时
    int INS_TiLeap;//INS时间同步差值确认
    int INS_TiBas;//INS同步确认
    int INS_IMU_Valid;//IMU输出有效位
    double INS_Roll_Mis_Angle_to_Veh;//INS到车体失准角-roll
    double INS_Pitch_Mis_Angle_to_Veh;//INS到车体失准角-pitch
    double INS_Yaw_Mis_Angle_to_Veh;//INS到车体失准角-yaw
    double INS_TiStamp;//INS时间戳
    int INS_RollingCounter_146;
    int INS_CRCCheck_146;
};

struct VCU_161
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    int VcuAccrMod;//
    int VCUShiftPostionValid;//换挡器位置有效(VCU use)
    int VCUAPARequestEnable;//APA控制使能(VCU use)0x0:no request 0x1:Control enabled 0x2:Control disable 0x3:invalid(USE in xml)
    int VCUAPAdriverInterruption;//驾驶员干预换挡(VCU use)(USE in xml)
    int VCUAccPedShield;//加速踏板屏蔽响应(VCU use)0x0:Unshield 0x1:Shield(USE in xml)
    int VcuAccrPedlPosn;//加速踏板实际位置 %(USE in xml)
    double VcuCalcnAccrPedlPosn;//虚拟加速踏板位置(USE in doc)
    int VcuAPATorqRequestAvailable;//当前可以响应APA的扭矩请求(VCU use)(USE in xml)
    int VcuAccrPedlPosnVld;//加速踏板实际位置有效(USE in xml)
    int VcuCalcnAccrPedlPosnVld;//虚拟加速踏板位置有效(USE in doc)
    int VcuEnyRecyclMod;//能量回收模式
    int VcuComFltSts;//信息错误状态(USE in doc)
    int VcuSimnEpbSwtSts;//模拟EPB开关状态
    int VcuSimnEpbSwtStsVld;//模拟EPB开关状态有效
    int VcuGearPosn;//整车实际挡位(VCU use) 0x0:Invalid 0x1:P 0x2:R 0x3:N 0x4:D 0x5:reserved(USE in xml)
    int VcuPtTqReqAvl;//动力系统扭矩可用状态
    int VcuPtTqRealVld;//动力总成真实轮端扭矩有效(VCU use)(USE in xml)
    int VcuPtTqLimMinVld;//动力总成最小允许输出扭矩有效(USE in xml)0x0:Valid 0x1:Invalid 
    int VcuPtTqLimMaxVld;//动力总成最大允许输出扭矩有效(USE in xml)
    int VcuOvrdModReq;//超越模式请求
    int VcuPtTqLimMax;//动力总成最大允许输出扭矩(VCU use)(USE in xml)
    int VcuPtTqLimMin;//动力总成最小允许输出扭矩(VCU use) Nm(USE in xml)
    int VcuShiftLvlPosn;//电子换挡器挡位请求
    int VcuPtTqReal;//动力总成真实轮端扭矩(VCU use)(USE in xml)
    int VcuVehWhlReqTqVld;//整车需求轮端扭矩有效(USE in xml)
    int VcuVehGearPosnVld;//整车实际挡位有效(USE in xml)
    int VcuRdySts;//整车可行驶状态(VCU use) 0x0:no Ready 0x1:Ready(USE in xml)
    int VcuVehWhlReqTq;//整车需求轮端扭矩(USE in xml)
    double VcuBattlowU;
    int VcuResiMilg;
    int GW_ADSSecOCVerifyFailureFlag;//网关_ADS信息校验失败标志
    int VcuSwVers0;
    int VcuSwVers1;
    int VcuSwVers2;
    int VcuSwVers3;
    int VcuSwVers4;
    int VcuSwVers5;
    int VcuSwVers6;
    int VcuCycCntr161;
    int VcuCrcChk161;
};

struct GW_170
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double EPS_ActualMotorTorq;
    double EPS_MaxSafetyTorsionBarTorq;
    double EPS_MinSafetyTorsionBarTorq;
    double EPS_ActualTorsionBarTorq;
    int EPS_RollingCounter_170;
    int EPS_fault_state;//EPS故障状态
    int EPS_CRCCheck_170;
};

struct GW_180
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double EpsSasSteerAg;//方向盘转向角度 degree(EPS use?)(USE in xml)
    int EpsSteerAgRate;//方向盘转角速率 deg/s(EPS use?)(USE in xml)
    int EpsSasCalSts;//SAS标定状态
    int EpsSteerAgSensFilr;//转角传感器失效(USE in xml)0x0:Valid 0x1:Invalid
    int EpsSasSteerAgVld;//方向盘转角有效(EPS use?)(USE in xml)0x0:Valid 0x1:Invalid
    int EpsCycCntr180;
    int EpsCrcChk180;
};

struct CDC_247
{
    int APA_SteeringAngleReqProtection;//APA转向控制请求(EPS use)(USE in xml)
    int APA_ErrorStatus;//自动泊车故障信号0x0:No Error 0x1:Error (EPS use?)(USE in xml)
    int APA_indication;
    int APA_APAOnOff;//APA功能开启状态
    int APA_EmergenceBrake;//紧急制动请求(ESP use)(USE in xml)
    double APA_SteeringAngleReq;//APA转向角度请求 degree(EPS use)(USE in xml)
    int APA_RemoteOnOff;
    int APA_ButtonPress;//APA开关状态
    int APA_IncreasePressureReq;//APA请求主动建压请求
    int APA_TurnLightsCommand;//转向灯控制命令0x0:No Request 0x1:LeftLight On 0x2:RightLight On 0x3:DoubleLights On  
    int APA_ParkNotice_4;
    int APA_ParkNotice;
    int APA_ParkingPercentage;
    int APA_RollingCounter_264;
    int APA_CRCCheck_264;
    int APA_EPBrequest;//电子手刹请求(ESP use)0x0:No Request 0x1:RequestBrake 0x2:RequestRelase(USE in xml)
    int APA_EPBrequestValid;//电子手刹请求有效(ESP use)(USE in xml)
    int APA_TargetAccelerationValid;//APA目标加速度有效信号(ESP use)(USE in xml)
    int APA_TransPRNDShiftRequest;//目标档位请求(VCU use)(USE in xml)
    int APA_TransPRNDShiftReqValid;//目标档位请求有效(VCU use)(USE in xml)
    double APA_TargetAcceleration;//APA目标加速度(ESP use)(USE in xml)
    double APA_EngTorqReq;//APA扭矩请求(发动机接收)(应该不使用该命令)
    int APA_Activation_Status;//APA激活状态
    int APA_TransPRNDShiftEnable;//换档控制使能(VCU use)(USE in xml)
    int APA_LSCAction;//LSC功能开启状态(USE in xml)
    int APA_HSAHDforbidden;//AUtoHold功能、坡起辅助功能禁止请求(ESP use)(USE in xml)
    int APA_EngineTrqReqEnable;//扭矩请求使能(USE in xml)
    int APA_AccPedShieldReq;//加速踏板屏蔽请求(VCU use)(USE in xml)
    int APA_ESPDecompressionModel;//ESP泄压方式
    int APA_PrefillReq;//预夹紧请求
    int APA_DynamicSlotWarning;
    int APA_SlotNotice;
    int APA_TCUClutchCombinationReq;//离合器结合请求
    int APA_TrqHoldForTCUCl;//TCU扭矩稳定标志
    int APA_RollingCounter_26C;
    int APA_CRCCheck_26C;
    int APA_PtTorqReq;//APA扭矩请求(VCU use)(VCU接收)(USE in xml)
    int APA_ESPDistToStop;//APA目标停车距离
    int APA_ESP_BrakeFunctionMode;//APA功能控制状态(ESP use)0x0:None 0x1:ReducedFunction 0x2:FullFunction 0x3:Invalid(USE in xml)
    int APA_PtTrqReqValid;//扭矩请求有效标志位(VCU use)(USE in xml)
    int APA_VCUReadyReq;//APA请求可行驶(USE in xml)
    int APA_ESP_StandstillRequest;//APA静态保压请求(ESP use)(USE in xml)
    // int APA_RollingCounter_236;
    // int APA_CRC_Checksum_236;
    // int APA_RollingCounter_247;
    // int APA_CRCCheck_247;
};

struct INS_271
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double INS_TiStamp;
    int INS_TiOut;
    int INS_TiLeap;
    int INS_TiBas;
    int INS_TIleap_Difference;
    int INS_RollingCounter_271;
    int INS_CRCCheck_271;
};

struct CDC_525
{
    int TC397_TimeSyncInit;//TC397时间同步初始化
};

struct GW_17A
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double EspVehSpd;//车速信号(USE in xml)
    int EspVehSpdVld;//车速有效(USE in xml)0x0:Valid 0x1:Invalid
    int EPB_APArequest_Available;//APA请求拉EPB可用(USE in xml)
    int EPB_AchievedClampForce_Primary;
    int EPB_AchievedClampForce;//EPB夹紧力
    int EPB_FailStatuss_Primary;
    double WheelPressureFrontRight_Model;
    double WheelPressureRearLeft_Model;
    double WheelPressureRearRight_Model;
    double WheelPressureFrontLeft_Model;
    int ESP_RollingCounter_17A;
    int ESP_CRCCheck_17A;
};

struct GW_17E
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double EPS_MeasuredTorsionBarTorque;
    int EPS_ADS_Abortfeedback;
    double EPS_Pinionang;
    int EPS_Pinionang_Valid;
    double EPS_Handwheel_Relang;
    int EPS_MeasuredTorsionBarTorqValid;
    int EPS_RollingCounter_17E;
    int EPS_LatCtrlAvailabilityStatus;//EPS横向控制可用状态
    int EPS_LatCtrlActive;//EPS横向控制激活
    int EPS_Handwheel_Relang_Valid;
    int EPS_CRCCheck_17E;
};

struct INS_17F
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    int INS_GPS_Week;
    int INS_GPS_Time;
    int INS_Current_Pos_Long_GNSS;
    int INS_Current_Pos_Lat_GNSS;
    int INS_Current_Pos_Long_GNSS_Err;
    int INS_Current_Pos_Lat_GNSS_Err;
    double FL_wheel_vel_for_IPC;
    double INS_Current_Pos_Heading;
    int INS_GNSS_Mode;
    double INS_Current_Pos_Heading_Accuracy;
    double INS_GNSS_Height;
    int INS_GNSS_Height_Err;
    double L_wheel_factor;
    double R_wheel_factor;
    double INS_Current_Pos_Pitch_Accuracy;
    double INS_Current_Pos_Roll_Accuracy;
    double INS_Current_Pos_Pitch;
    double INS_Current_Pos_Roll;
    double FR_wheel_vel_for_IPC;
    double RL_wheel_vel_for_IPC;
    double RR_wheel_vel_for_IPC;
    int INS_Current_Pos_Pitch_Confidence;
    int  INS_Current_Pos_Heading_Confiden;
    int INS_Current_Pos_Long_Confidence;
    int INS_Current_Pos_Roll_Confidence;
    int INS_Current_Pos_Height_Confidenc;
    int INS_Current_Pos_Lat_Confidence;
    double INS_TiStamp;
    int INS_Initiation_Mark;
    int INS_TiOut;
    int INS_TiLeap;
    int INS_TiBas;
    int INS_RollingCounter_17F;
    int INS_POS_Match_RTK_POS_Mark;
    int INS_CRCCheck_17F;
};

struct GW_1C2
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    int EspReWhlIncTarTqActv;//后轴增扭激活标志位(USE in xml)
    int EspReWhlDecTarTqActv;//后轴降扭激活标志位(USE in xml)
    int EspTcsActvSts;//TCS激活状态0x0:Not active 0x1:active
    int EspTcsFailr;//TCS/ESP故障
    int EspFctOpenSts;//ESP功能开启状态
    int EspActvSts;//ESP激活状态0x0:Not active 0x1:active
    int EspAbsActv;//ABS激活
    int EspAbsFailr;//ABS故障
    int EspEbdFailr;//EBD故障
    int EspReWhlDecTarTq;//后轴降扭轮端扭矩(USE in xml)
    int EspReWhlIncTarTq;//后轴增扭轮端扭矩(USE in xml)
    int EspVehStandstill;//车辆静止(ESP use?)0x0:Not standstill 0x1:standstill(USE in xml)
    int EspAutoHoldActvSts;//AutoHold功能状态 0x0:Not active 0x1:active
    int IBCU_BrakeDiscHighTempWarning;
    int EspBrkLiOnReq;
    int MbRgnTarWhlQlfr;
    int EspVdcActvSts;//VDC激活状态
    int MbRgnTarWhl;
    int IBCU_ADCActiveState;//IBCU激活状态0x0:ADC_NotActive 0x1:ADC_ActiveReduce 0x2:ADC_ActiveFull 0x3:reserved
    int IBCU_CommunicationInvalid;//IBCU与ADC在主CAN通讯异常
    int IBCU_ADCFullFuncAvail;//IBCU全功能可用状态(USE in xml)
    int IBCU_ADCReducedFuncAvail;//IBCU降级功能可用状态0x0：not Available 0x1：Available 0x2：reserved 0x3：reserved(USE in xml)
    int EspCycCntr1C2;
    int EspCrcChk1C2;
};

struct GW_20B
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double WhlSpdRiFrntData;//右前轮速信号(USE in xml)
    int WhlSpdRiFrntDir;//右前轮方向信号0x0:Forward 0x1:backward 0x2:Standstill 0x3:Invalid(USE in xml)
    int WhlSpdRiFrntDataVld;//右前轮速数据有效(USE in xml)0x0:Valid 0x1:Invalid 
    double WhlSpdLeFrntData;//左前轮速信号(USE in xml)
    int WhlSpdLeFrntDir;//左前轮方向信号(USE in xml)
    int WhlSpdLeFrntDataVld;//左前轮速数据有效(USE in xml)
    int IBCU_APCActiveStatus;//ESP减速度激活(EPS use)0x0: Deactive 0x1: ReducedFunction Active 0x2: FullFunction Active 0x3: Reserved(USE in xml)
    double WhlSpdRiReData;//右后轮速信号(USE in xml)
    int WhlSpdRiReDir;//右后轮方向信号(USE in xml)
    int WhlSpdRiReDataVld;//右后轮速数据有效0x0:Valid 0x1:Invalid (USE in xml)
    double WhlSpdLeReData;//左后轮速信号(USE in xml)
    int WhlSpdLeReDir;//左后轮方向信号(USE in xml)
    int WhlSpdLeReDataVld;//左后轮速数据有效(USE in xml)
    int ESP_UrgencyBrakeAlarm;
    int ESP_APA_DriverOverride;//驾驶员超越(USE in xml)
    int WhlSpdFrntLePls;//左前轮速脉冲(USE in xml)
    int WhlSpdFrntRiPls;//右前轮速脉冲(USE in xml)
    int WhlSpdReRiPls;//右后轮速脉冲(USE in xml)
    int WhlSpdReLePls;//左后轮速脉冲(USE in xml)
    int ESP_AWBactive;
    int ESP_AEBdecActive;
    int ESP_AEBAvailable;
    int ESP_PrefillAvailable;//预制动有效
    int ESP_PrefillActive;//预制动激活
    int ESP_ABAavailable;
    int ESP_ABAactive;
    int ESP_BrakeForce;//制动系统压力
    int ESP_BrakeOverHeatBrakeDiscTemp;
    int ESP_AWBavailable;
    int IBCU_APCReducedFuncAvail;//纵向执行器降级功能可用(ESP use)0x0:notavailable 0x1:available(USE in xml)
    int ESP_QDCACC;
    int IBCU_APCFullFuncAvail;//纵向执行器全功能可用(ESP use)(USE in xml)
    int IbcuCycCntr20B;
    int IBCU_ADCRampOffSuspendState;//ADC功能退出抑制状态
    int IbcuCrcChk20B;
};

struct GW_24F
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    int EPS_ElectPowerConsumption;//助力转向电流消耗
    int EPS_TorqSensorStatus;
    int EPS_APA_EpasFAILED;//EPS临时故障0x0:No Fail 0x1:Fail(EPS use)(USE in xml)
    int EPS_ModeSwitchSt;
    int EPS_APA_Abortfeedback;//APA控制中断反馈(EPS use)0x0:NO_DRIVING_INTERUPTION 0x1:DRIVER_RECOVERY 0x2:TOO_HIGH_VEHICLE_SPEED 0x3:TOO_IMPORTANT_ANGULAR_ERROR(USE in xml)
    double EPS_SteeringTorque;//转向力矩 Nm(EPS use)
    int  EPS_IACC_abortreason;//IACC控制中断反馈
    int EPS_APA_ControlFeedback;//APA请求转向控制反馈0x0:Control disable 0x1:Control enabled(EPS use)(USE in xml)
    int EPS_LDW_ShakeLevStatus;
    int EPS_ADASActiveMode;
    int EPS_ADS_ControlFeedback;//ADS请求转向控制反馈
    int EpsFaild;
    int EPS_SystemSt;//EPS系统状态0x0:disable 0x1:off 0x2:enable 0x3:warm int 0x4~0xF : Reserved
    int EPS_RollingCounter_24F;
    int EPS_ConcussAvailabilityStatus;//
    int EpsSteerModFb;
    int EPS_CRCCheck_24F;
};

struct GW_2C2
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    int IbBrkPedlStsGb;//IB制动踏板状态(国标)(USE in xml)
    double IbBrkPedlTrvlDrvr;
    int IbBrkPedlStsGbVld;//IB制动踏板状态有效(USE in xml)
    int IbBrkPedlTrvlDrvrVld;
    int IBBrkPedlModSts;
    int IBBrkPedlModSwtAvl;
    double IBCU_PFSBrakePressure;
    int IBCU_PFSBrakePressureValid;
    double IBCU_PlungerBrakePressure;
    int IBCU_PlungerBrakePressureValid;
    int IbCycCntr2C2;
    int IbCrcChk2C2;
};

struct CDC_2C6
{
    int APA_ParkNotice_5;
    int APA_LAEBReq;//LAEB请求
    int APA_LAEBStatus;//LAEB工作状态(USE in doc)
    int APA_BLEConnectionRemind;
    int APA_LAEBNotice;//LAEB 提示
    int APA_RemoteParkingUsingRemind;
    int APA_ASPAvailableStatus;
    int APA_ASPStatus;
    int APA_CrossModeSelectReq;
    int APA_BCMHornCommand;
    int APA_vehicleFrontdetect;
    int APA_ReleasePressureReq;//APA泄压起步请求(ESP use)0x0:No Request 0x1:Request(USE in xml)
    int APA_PEPS_EngineOffLockRequest;
    int APA_RADSNotice;
    // int APA_RollingCounter_2D4;
    int APA_PEPS_EngineOffRequest;
    // int APA_CRCCheck_2D4;
    // int APA_RollingCounter_2C6;
    // int APA_CRCCheck_2C6;
};

struct GW_3C2
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    int EpbFailrSts;//EPB故障状态(USE in xml)
    int EpbSts;//EPB状态(ESP use)0x0:both brakes released 0x1:both brakes applied 0x2:both brakes Releasing 0x3:both brakes Locking 0x4:unknown(USE in xml)
    int EpbFctLamp;
    int EpbFailrLamp;
    int EspEpbReqAvl;//EPB外部请求可用(USE in xml)
    int EpbFctLamp_Primary;
    int EpbFailrLamp_Primary;
    int EpbStsPrimary;//EPB状态_Primary
    int EspCycCntr3C2;
    int EspCrcChk3C2;
};

struct INS_5B3
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double INS_Wheel_Scale_factor;
    double INS_IMU_Temp;
    int INS_CorrelationSystem_failure_st;
    int INS_Sts;
    int INS_Latitude_Hemisphere;
    int INS_Longitude_Hemisphere;
    int INS_RollingCounter_5B3;
    int INS_CRCCheck_5B3;
};

struct CDC_601
{
    int VCU_Distance_CMD;
    int VCU_ProbeCMD_NUM9;
    int VCU_ProbeCMD_NUM10;
    int VCU_ProbeCMD_NUM11;
    int VCU_ProbeCMD_NUM12;
    int VCU_Machine_NUM;
    int VCU_ProbeCMD_NUM1;
    int VCU_ProbeCMD_NUM2;
    int VCU_ProbeCMD_NUM3;
    int VCU_ProbeCMD_NUM4;
    int VCU_ProbeCMD_NUM5;
    int VCU_ProbeCMD_NUM6;
    int VCU_ProbeCMD_NUM7;
    int VCU_ProbeCMD_NUM8;
};

struct CDC_2B9
{
    int APA_FunctionOnOffSts;
    int APA_Condition_Notice;
    int APA_TouchInfOnOffRes;
    int APA_AVP_Notice;
    int APA_HZP_Notice;
    int APA_ViewActual;
    int APA_Summon_Notice;
    int APA_ActivationSts;
    int APA_ReadySts;
    int APA_ParkingSlot_ExtraFeature;
    int APA_ParkingSlot_Type;
    int APA_TurnOnMode;
};

struct ICMSG_650
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double measurement_time;
    double INS_Current_Pos_X_Accel;
    double INS_Current_Pos_X_Rate;
    double INS_Current_Pos_Y_Accel;
    double INS_Current_Pos_Y_Rate;
    double INS_Current_Pos_Z_Accel;
    double INS_Current_Pos_Z_Rate;
    int INS_IMU_Time;
    int INS_IMU_Valid;
    double INS_Pos_X_Accel_Bias;
    double INS_Pos_X_Rate_Bias;
    double INS_Pos_Y_Accel_Bias;
    double INS_Pos_Y_Rate_Bias;
    double INS_Pos_Z_Accel_Bias;
    double INS_Pos_Z_Rate_Bias;
    double INS_Quaternion_W;
    double INS_Quaternion_X;
    double INS_Quaternion_Y;
    double INS_Quaternion_Z;
    double INS_Roll_Mis_Angle_to_Veh;
    double INS_Pitch_Mis_Angle_to_Veh;
    double INS_Yaw_Mis_Angle_to_Veh;
    // double INS_TiStamp;
    int INS_RollingCounter_146;
    int INS_TiOut;
    int INS_TiLeap;
    int INS_TiBas;
    int INS_CRCCheck_146;
    uint64_t ICMSG_TimeStamp;
};

struct ICMSG_651
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double measurement_time;
    int VCUAPARequestEnable;
    int VCUAPAdriverInterruption;
    int VCUAccPedShield;
    int VcuAPATorqRequestAvailable;
    int VCUShiftPostionValid;
    double VcuCalcnAccrPedlPosn;
    int VcuEnyRecyclMod;
    int VcuGearPosn;
    int VcuPtTqLimMax;
    int VcuComFltSts;
    int VcuCalcnAccrPedlPosnVld;
    int VcuPtTqLimMaxVld;
    int VcuPtTqLimMin;
    int VcuPtTqReal;
    int VcuPtTqLimMinVld;
    int VcuPtTqReqAvl;
    int VcuPtTqRealVld;
    int VcuVehWhlReqTq;
    int VcuVehGearPosnVld;
    int VcuShiftLvlPosn;
    int VcuRdySts;
    int VcuVehWhlReqTqVld;
    int VcuCrcChk161;
    int VcuSimnEpbSwtStsVld;
    int VcuSimnEpbSwtSts;
    int VcuCycCntr161;
    int EPS_fault_state;
    int EPS_RollingCounter_170;
    int EpsCrcChk180;
    int EpsSasCalSts;
    int EpsCycCntr180;
    double EpsSasSteerAg;
    int EpsSasSteerAgVld;
    int EpsSteerAgRate;
    int EpsSteerAgSensFilr;
    int VcuAccrPedlPosn;
    int GW_ADSSecOCVerifyFailureFlag;
    int VcuOvrdModReq;
    int VcuAccrPedlPosnVld;
    double INS_TiStamp;
    int INS_TIleap_Difference;
    int INS_TiOut;
    int INS_TiLeap;
    int INS_TiBas;
    int INS_RollingCounter_271;
    int INS_CRCCheck_271;
    uint64_t ICMSG_TimeStamp;
};

struct ICMSG_652
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double measurement_time;
    int EspAbsFailr;
    int EspAbsActv;
    int EspReWhlDecTarTq;
    int EspReWhlDecTarTqActv;
    int EspReWhlIncTarTq;
    int EspEbdFailr;
    int EspTcsActvSts;
    int EspVdcActvSts;
    int EspReWhlIncTarTqActv;
    double EspVehSpd;
    int EspVehSpdVld;
    int ESP_CRCCheck_17A;
    int ESP_RollingCounter_17A;
    int EPB_AchievedClampForce;
    int EPB_AchievedClampForce_Primary;
    int EPB_FailStatuss_Primary;
    int EPB_APArequest_Available;
    int EPS_CRCCheck_17E;
    int EPS_LatCtrlAvailabilityStatus;
    int EPS_LatCtrlActive;
    double INS_Current_Pos_Heading;
    int EPS_RollingCounter_17E;
    double INS_Current_Pos_Heading_Accuracy;
    double INS_Current_Pos_Pitch;
    int INS_Current_Pos_Heading_Confiden;
    double INS_Current_Pos_Pitch_Accuracy;
    double INS_Current_Pos_Roll;
    int INS_Current_Pos_Pitch_Confidence;
    double INS_Current_Pos_Roll_Accuracy;
    int INS_Current_Pos_Roll_Confidence;
    double FL_wheel_vel_for_IPC;
    double FR_wheel_vel_for_IPC;
    double RL_wheel_vel_for_IPC;
    double RR_wheel_vel_for_IPC;
    double L_wheel_factor;
    double R_wheel_factor;
    // double INS_TiStamp;
    // int INS_RollingCounter_17F;
    // int INS_TiOut;
    // int INS_TiLeap;
    // int INS_TiBas;
    // int INS_CRCCheck_17F;
    // int INS_GPS_Time;
    int EspAutoHoldActvSts;
    int EspTcsFailr;
    int EspFctOpenSts;
    int EspActvSts;
    int EspVehStandstill;
    int IBCU_ADCActiveState;
    int IBCU_CommunicationInvalid;
    int IBCU_ADCFullFuncAvail;
    int IBCU_ADCReducedFuncAvail;
    uint64_t ICMSG_TimeStamp;
};

struct ICMSG_653
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double measurement_time;
    int ESP_BrakeForce;
    double EPS_SteeringTorque;
    int ESP_APA_DriverOverride;
    int ESP_PrefillAvailable;
    int ESP_PrefillActive;
    int WhlSpdFrntLePls;
    int WhlSpdFrntRiPls;
    double WhlSpdLeFrntData;
    double WhlSpdLeReData;
    int WhlSpdLeFrntDir;
    int WhlSpdLeFrntDataVld;
    int WhlSpdLeReDir;
    int WhlSpdLeReDataVld;
    int WhlSpdReLePls;
    int WhlSpdReRiPls;
    double WhlSpdRiFrntData;
    double WhlSpdRiReData;
    int WhlSpdRiFrntDir;
    int WhlSpdRiFrntDataVld;
    int WhlSpdRiReDir;
    int WhlSpdRiReDataVld;
    int IbcuCrcChk20B;
    int IbcuCycCntr20B;
    int IBCU_ADCRampOffSuspendState;
    int IBCU_APCActiveStatus;
    int IBCU_APCReducedFuncAvail;
    int IBCU_APCFullFuncAvail;
    int EPS_ConcussAvailabilityStatus;
    int EPS_APA_EpasFAILED;
    int EPS_APA_ControlFeedback;
    int EPS_APA_Abortfeedback;
    int EPS_CRCCheck_24F;
    int EPS_ElectPowerConsumption;
    int EPS_IACC_abortreason;
    int EPS_SystemSt;
    int EPS_RollingCounter_24F;
    int EPS_ADS_ControlFeedback;
    int EPS_ADASActiveMode;
    int IbBrkPedlStsGb;
    double IBCU_PFSBrakePressure;
    int IbBrkPedlStsGbVld;
    double IBCU_PlungerBrakePressure;
    int IBCU_PlungerBrakePressureValid;
    int IbCrcChk2C2;
    int IbCycCntr2C2;
    int EpbStsPrimary;
    int EpbSts;
    int EpbFailrSts;
    int EspCrcChk3C2;
    int EspCycCntr3C2;
    double INS_Wheel_Scale_factor;
    int EspEpbReqAvl;
    uint64_t ICMSG_TimeStamp;
};

struct ICMSG_654
{
    int seq;//自定义系列号，这里根据保存的数据的时间进行自增，默认认为IC转发没有遗漏(0-1000000)
    int valid;//根据seq对比进行赋值，0:default 1:有效 2:json failed 3:msg invalid 4:seq not changed
    double measurement_time;
    int Ult_Probe_info1;
    int Ult_Probe_info2;
    int Ult_Probe_info3;
    int Ult_Probe_info4;
    int Ult_Probe_info5;
    int Ult_Probe_info6;
    int Ult_Probe_info7;
    int Ult_Probe_info8;
    int Ult_Probe_info9;
    int Ult_Probe_info10;
    int Ult_Probe_info11;
    int Ult_Probe_info12;
    uint64_t ICMSG_TimeStamp;
};

enum class HandShakeStatus:int{
    DEFAULT=0,
    COUNTERPARTSTATUS=1,
    HANDSHAKEREQ=2,
    HANDSHAKESUS=3,
    HANDSHAKEFAILED=4,
    INTERRUPT=5,
    HANDSHAKEREQQUIT=6,
    HANDSHAKEQUIT=7
};

enum class GearTransStatus:int{
    DEFAULT=0,
    GETTRANSGEARREQ=1,

};

enum class VcuGearPosn:int{
    INVALID=0,
    P=1,
    R=2,
    N=3,
    D=4,
    RESERVED=5
};

enum class APATransPRNDShiftRequest:uint32_t{
    NOREQUEST=0,
    P=1,
    R=2,
    N=3,
    D=4,
    INVALID=5,
    RESERVED=6
};

// enum class MyCommonBool:int {
//     NONE = 0,
//     TRUE = 1,
//     FALSE = 2
// };

struct TakeOverCmd
{
    bool is_update;
    int apa_takeover;//0:default 1:apa all takeover 2:apa all quit takeover
    int eps_takeover;
    int esp_takeover;
    int vcu_takeover;
    int gear_takeover;
};

struct IOControlCmd
{
    bool is_update;
    bool is_braking;
    double tar_deceleration;
    bool is_driving;
    double  tar_torque;
    bool is_steering;
    double tar_steer_angle;
    bool is_gear;
    int tar_gear;
};

struct IOUnitTestCmd
{
    bool is_update;
    bool is_autohold;
    int autohold;
    bool is_holdpressure;
    double holdpressure_dece;
    bool is_epb_control;
    int epb_control;
    bool is_gear;
    int gear;
    bool is_start_release_pressure;
    double start_release_pressure_acc;
    bool is_driving ;
    double tar_torque;
    bool is_breaking;
    double tar_deceleration;
    bool is_steering;
    double tar_steer_angle;
    bool is_standstill;
    double standstill_dece;
    bool is_emergency;
    int emergency;
};

struct StruOutputDataChassis:ZeroStruct<StruOutputDataChassis>
{
    CommonBool::Enum LatSt_eps_valid;
    CommonErrorType::Enum LatSt_eps_err_type;
    EpsErrReason::Enum LatSt_eps_err_reason;

    double LatDa_steering_angle;
    double LatDa_steering_velocity;
    double LatDa_steering_torque;

    CommonBool::Enum LonSt_esp_valid;
    CommonErrorType::Enum LonSt_esp_err_type;
    EspErrReason::Enum LonSt_esp_err_reason;
    CommonBool::Enum LonSt_epb_valid;
    CommonErrorType::Enum LonSt_epb_err_type;
    EpbErrReason::Enum LonSt_epb_err_reason;
    CommonBool::Enum LonSt_vcu_valid;
    CommonErrorType::Enum LonSt_vcu_err_type;
    VcuErrReason::Enum LonSt_vcu_err_reason;

    CommonBool::Enum LonDa_stand_still;
    CommonBool::Enum epb_sys_st;
    double LonDa_lon_acceleration;
    double LonDa_speed;
    double LonDa_cur_torque;
    double LonDa_min_torque;
    double LonDa_max_torque;
    uint32_t LonDa_gear_pos;
    double LonDa_ibcu_pressure;
};

struct StruOutputDataShakeHand:ZeroStruct<StruOutputDataShakeHand>
{
    CommonBool::Enum lat_active;
    CommonErrorType::Enum lat_shakehand_err_type;
    LatShakeHandErrReason::Enum lat_shakehand_err_reason;
    CommonBool::Enum long_active;
    CommonErrorType::Enum long_shakehand_err_type;
    LonShakeHandErrReason::Enum long_shakehand_err_reason;
};

struct StruOutputDataWheelSpeedInfo:ZeroStruct<StruOutputDataWheelSpeedInfo>
{
    CommonBool::Enum valid;
    double wheelspeed_fl;
    double wheelspeed_fr;
    double wheelspeed_rl;
    double wheelspeed_rr;
    int wheelsign_fl;
    int wheelsign_fr;
    int wheelsign_rl;
    int wheelsign_rr;
    int wheel_edgessum_fl;
    int wheel_edgessum_fr;
    int wheel_edgessum_rl;
    int wheel_edgessum_rr;
    double measurement_time;
};

struct StruOutputDataSystemCheck:ZeroStruct<StruOutputDataSystemCheck>
{
    CommonBool::Enum is_check_ok ;//车门状态等辅助信息
    SystemCheckErrReason::Enum check_err_reason ; //TODO: define enum
};

struct StruOutputDataVehicleBod:ZeroStruct<StruOutputDataVehicleBod>
{
    CommonBool::Enum right_turn_lamp_st ;
    CommonBool::Enum left_turn_lamp_st ;
    CommonBool::Enum rl_turn_lamp_st ;
};

struct StruOutputDataInsInfo:ZeroStruct<StruOutputDataInsInfo>
{
    CommonBool::Enum valid ;
    double lon_acceleration ;
    double lat_acceleration ;

};

struct StruInsGps
{
    double height;
    double time;
};
struct StruInsGnns
{
    double height;
    double week;
};
struct StruPoint3D
{
    double x;
    double y;
    double z;
};
struct StruOutputDataImuInfo:ZeroStruct<StruOutputDataImuInfo>
{
    double measurement_time;
    float measurement_span;
    StruPoint3D linear_acceleration;
    StruPoint3D angular_velocity;

    StruPoint3D location;
    StruPoint3D rotation;
    StruInsGps ins_gps;
    StruInsGnns ins_gnns;
    int seq_num;
};

struct StruChassisShiftGearInfo:ZeroStruct<StruChassisShiftGearInfo> 
{
    bool gear_trans_valid;
    uint32_t req_gear;
    uint32_t cur_gear;
};

struct StruInputSystemCheckCmd:ZeroStruct<StruInputSystemCheckCmd>
{
    bool is_update;
    CommonBool::Enum request_check;
};

struct StruInputLatAndLongShakeHandCmd:ZeroStruct<StruInputLatAndLongShakeHandCmd>
{
    bool is_update;
    CommonBool::Enum activate_lat;
    CommonBool::Enum activate_long;
    CommonBool::Enum enable_p_gear;
};

struct StruInputLatAndLongControlCmd:ZeroStruct<StruInputLatAndLongControlCmd>
{
    bool is_update;
    CommonBool::Enum is_driving;
    CommonBool::Enum is_braking;
    double tar_torque;
    double tar_deceleration;
    CommonBool::Enum is_steering;
    double tar_steer_angle;
    CommonBool::Enum put_gear;
    uint32_t tar_gear;
    CommonBool::Enum hold;
    CommonBool::Enum release;
};

struct StruInputHoldPressureChangan:ZeroStruct<StruInputHoldPressureChangan>
{
    bool is_update;
    CommonBool::Enum hold;
    double deacc;
};

struct Quaternions{
    double w;
    double x;
    double y;
    double z;
};
struct StruIOChassisInfoDebug:ZeroStruct<StruIOChassisInfoDebug>
{
    double velocity;

    int min_torque; //VcuPtTqLimMin 161
    int max_torque; //VcuPtTqLimMax 161
    int cur_torque; //VcuPtTqReal 11

    int epb_sys_st; //EpbSts 3c2

    int gear_pos;  //VcuGearPosn 161

    int eps_apa_abort_feedback; //EPS_APA_Abortfeedback 24f 
    double steering_angle; //EpsSasSteerAg 180
    double steering_velocity; //EpsSteerAgRate 180
    double steering_torque; //EPS_SteeringTorque 24f

    bool right_turn_lamp_st;
    bool left_turn_lamp_st;
    bool rl_turn_lamp_st;

    int takeover_stat;//0:default 1:all takeover 2:all quit takeover
    int eps_takeover_stat ;
    int esp_takeover_stat;
    int vcu_takeover_stat;
    int gear_takeover_stat;

    StruPoint3D linear_acceleration;
    StruPoint3D angular_velocity;
    Quaternions quaternions;
    double ins_roll_mis_angle_to_veh;
    double ins_pitch_mis_angle_to_veh;
    double ins_yaw_mis_angle_to_veh;

    int esp_autohold_active_sts; //EspAutoHoldActvSts 1c2

    int stand_still; //EspVehStandstill 1c2 

    //新增握手判断信号
    int eps_apa_epasfailed; //EPS_APA_EpasFAILED 24f
    int eps_apa_control_feedback; //EPS_APA_ControlFeedback 24f

    int ibcu_apc_reducedfunc_avail; //IBCU_APCReducedFuncAvail 20b
    int ibcu_apc_fullfunc_avail; //IBCU_APCFullFuncAvail 20b
    int ibcu_apc_active_status; //IBCU_APCActiveStatus 20b
    int esp_apa_driveroverrode;//ESP_APA_DriverOverride 20b
    int esp_brake_force;//ESP_BrakeForce 20b

    int vcu_apa_torq_requesta_vailable; //VcuAPATorqRequestAvailable 161
    int vcu_rdy_sts; //VcuRdySts 161
    int vcu_accped_shield; //VCUAccPedShield 161
    int vcu_apa_request_enable; //VCUAPARequestEnable 161

    double ibcu_plunger_brake_pressure;//IBCU_PlungerBrakePressure 2c2

    int uss_enable; // uss enable 0:default 1:req 2:sucess(not use)

    int vcu_com_flt_sts;//VcuComFltSts 161
    int vcu_apa_driver_Interruption;//VCUAPAdriverInterruption 161
    int ibcu_adc_rampoff_suspendstate;//IBCU_ADCRampOffSuspendState 20B
    int ibcu_communication_invalid;//IBCU_CommunicationInvalid 1c2



    //轮速信号(只用于log打印，不用于cyber发布)
    double velocity_base_whl;
    double wheelspeed_fl;
    double wheelspeed_fr;
    double wheelspeed_rl;
    double wheelspeed_rr;
    int wheelsign_fl;
    int wheelsign_fr;
    int wheelsign_rl;
    int wheelsign_rr;
    int wheel_edgessum_fl;
    int wheel_edgessum_fr;
    int wheel_edgessum_rl;
    int wheel_edgessum_rr;

    int64_t icmsg650_timestamp;
    int64_t icmsg651_timestamp;
    int64_t icmsg652_timestamp;
    int64_t icmsg653_timestamp;
    int64_t icmsg654_timestamp;

};

struct ExceptionHandleInfo:ZeroStruct<ExceptionHandleInfo>
{
    int eps_fail;//0:default 1:driver overrode 2:handshake overtime 3:other failed 4:other failed
    long current_hanshake_req_time;// the time of current handshake request 
    long steeringreq_one_time;//steering handshank req 0x1 
    long steeringreq_two_time;//steering handshank req 0x2 
    long steeringreq_controlfeedback_time;//steering handshank req feedback
    long steeringreq_fail_time;//steering handshank req fail 
    int esp_fail;//0:default 1:driver overrode
};
}

#endif

#ifndef __IO_SERVER_H__
#define __IO_SERVER_H__
#define USE_IO_CHECK 0
#define USE_DEBUG 1

#include "ipc/megaipc_api.h"
#include <sys/time.h>
#include "payload_info.h"
#include <fstream>
#include <iomanip>
#include "nlohmann/json.hpp"
#include "json_parser.hpp"
#include <vector>
#include <map>
#include "apa_io_topic.h"
#include <iostream>
#include <atomic>

#include "thirdparty/recommend_protocols/planning/proto/decision.pb.h"
#include "cyber/cyber.h"
#include "cyber/node/writer.h"
#include "cyber/timer/timer.h"
#include "thirdparty/recommend_protocols/apa_io_control/proto/io_proto.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/imu.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_output.pb.h"
#include "thirdparty/recommend_protocols/drivers/io/proto/control_by_wire_input.pb.h"
#include "thirdparty/recommend_protocols/drivers/proto/ultrasonic.pb.h"
#include "base/util/config_parser.hpp"
#include "base/util/module_base.hpp"
#include "base/util/app_template.hpp"
#include <glog/logging.h>
#include <google/protobuf/util/json_util.h>
#include <algorithm>

// using rainbowdash::planning::HMIControlParking;
using namespace std;
using namespace nlohmann;

using apollo::cyber::Node;
using apollo::cyber::Writer;
using changan::io::MessageTest;
using changan::io::IOChassisInfoDebug;
using changan::io::IOTakeover;
using changan::io::IOControl;
using changan::io::IOControlUnitTest;
using changan::io::EnableUSSUlt;
using changan::io::GearEventInfo;
//output
// using rainbowdash::common::CommonBool;
using rainbowdash::common::CommonGearPosition;
using rainbowdash::drivers::Imu;
using rainbowdash::drivers::Ultrasonic;
#define io rainbowdash::control_by_wire
using io::EspErrReason;
using rainbowdash::control_by_wire::SystemCheckResultInfo;
using rainbowdash::control_by_wire::ShakeHandStateInfo;
using rainbowdash::control_by_wire::VehicleBodyInfo;
using rainbowdash::control_by_wire::ChassisLatInfo;
using rainbowdash::control_by_wire::ChassisLongInfo;
using rainbowdash::control_by_wire::WheelSpeedInfo;
using rainbowdash::control_by_wire::ChassisShiftGearInfo;
//input
using rainbowdash::control_by_wire::LatAndLongControlCmd;
using rainbowdash::control_by_wire::HoldPressureChangan;
using rainbowdash::control_by_wire::LatAndLongShakeHandCmd;
using rainbowdash::control_by_wire::SystemCheckCmd;


namespace io_server{
    class Filter
    {
        public:
            void SetFilterPara(uint32_t window_size, uint32_t index)
            {
                window_size_ = window_size;
                index_ = index;
            }

            double Run(double idata)
            {
                #define FILTER_WINDOW_SIZE window_size_
                //#define FILTER_WINDOW_SIZE 20

                if (filter_buf.size() >= FILTER_WINDOW_SIZE) {
                    //double min_data = filter_buf[0];
                    std::vector<double> sorted_buf(filter_buf);
                    std::sort(sorted_buf.begin(), sorted_buf.end());


                    for (uint32_t i=0; i < FILTER_WINDOW_SIZE-1; i++) {
                        //if (filter_buf[i+1] < min_data)
                            //min_data = filter_buf[i+1];

                        filter_buf[i] = filter_buf[i+1];
                    }
                    filter_buf[FILTER_WINDOW_SIZE-1] = idata;

                    //return min_data;
                    return sorted_buf[index_];

                } else {
                    filter_buf.push_back(idata);
                    return idata;
                }

            }

        private:
            std::vector<double> filter_buf;
            uint32_t window_size_;
            uint32_t index_;
    };

    class IOServer : public megaipc::PubSubListenerInterface
    {
    //need by qnx
    private:
        //data
        megaipc::MegaIpcApi *api;
        std::mutex qnx_sub_mtx_;

        //prase and save topic
        ICMSG_650 icmsg_650_;
        ICMSG_651 icmsg_651_;
        ICMSG_652 icmsg_652_;
        ICMSG_653 icmsg_653_;
        ICMSG_654 icmsg_654_;

        //pub message
        CDC_247 cdc_247_;
        CDC_247 cdc_247_pre_;
        CDC_2C6 cdc_2c6_;
        CDC_2C6 cdc_2c6_pre_;
        CDC_601 cdc_601_;
        CDC_2B9 cdc_2b9_;
        std::string pub_topic_247_;
        std::string pub_topic_2c6_;
        std::string pub_topic_601_;
        std::string pub_topic_2b9_;

    public:
        IOServer(std::shared_ptr <Node> node, NodeCfg& nodecfg, string io_cfg_path);
        ~IOServer();

        //重载
        void onMessageArrival(const string &topic, const IpcMessage &msg) override;
        void onMessageDelivered(const string &msgId) override;
        void onConnectStateChanged(const string &nodeId, const ConnectState &state) override;

    private:
        //init
        void saveTopic(const string &topic, const IpcMessage &msg);
        void setSub();
        bool megaIpcPublish(const string &topic, const string &str_msg) ;
        bool praseJson(const string io_server_cfg);
        void setSubTopic();
        map<string, string> getRevTopicMap();

        //prase topic
        void praseAllTopic();
        //uss
        int processUltDistance(int s_dis);
        void pubUltDistance();
        std::atomic<int> ult_enable_{0};//0:default 1:req 2:sucess

        //prase changan topic
        void praseICMSG650(const string &msg);
        void praseICMSG651(const string &msg);
        void praseICMSG652(const string &msg);
        void praseICMSG653(const string &msg);
        void praseICMSG654(const string &msg);

        //init
        typedef void (IOServer::*pFun)(const string &msg);
        void setTopicMessageMap();
        int parse_json_string(const std::string cstrInput, json& outJson);

        //dbc pub data init and pub
        std::unique_ptr<JsonParser> ioConfPtr_;
        void pubMsgInitBaseDBC(CDC_247& cdc_247, CDC_2C6& cdc_2c6);
        void pubMegaIpcMsg247(CDC_247& cdc_247);
        void pubMegaIpcMsg2C6(CDC_2C6& cdc_2c6);
        void pubMegaIpcMsg2B9(CDC_2B9& cdc_2b9);
        void pubMegaIpcMsg601(CDC_601& cdc_601);
        bool initPubMsgBaseDBC();
        bool resetPubMsgBaseDBC(double cur_angle);
        void initCDC247BaseDBC(nlohmann::json init_data);
        void initCDC2C6BaseDBC(nlohmann::json init_data);
        void initCDC601BaseDBC(nlohmann::json init_data);
        void initCDC2B9BaseDBC(nlohmann::json init_data);
        void initIOLimitParam(nlohmann::json param);
        double steering_rate_limit_ = 9.0;
        double acc_set_ = 1.0;
        int apa_esp_breakfunctionmode_ = 0;
        uint32_t filter_window_size_ = 20;
        uint32_t filter_pickup_index_ = 3;
        double uss_valid_distance_cm_ = 300.0;

        //handshake
        enum HandShakeStatus EPS_handshake_,ESP_handshake_,VCU_handshake_,Gear_handshake_;
        // bool handshake_flag_ = false;
        void handShakeStatusInit();
        void EPSHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void ESPHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void VCUHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void GearHandShake(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void EPSHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void ESPHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void VCUHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void GearHandShakeQuit(const StruIOChassisInfoDebug& io_chassis_info_debug);

        //control
        void pressureMaintain(double deceleration);
        void braeakOrReleaseEPB(int epb_req);
        void transPRNDShift(uint32_t req_gear);
        void startingPressureRelief(double acc,uint32_t req);
        void torqueControl(int req_torq, const StruIOChassisInfoDebug& io_chassis_info_debug, double acc = 0.25);
        double steering_angle_pre_ = 0.0;
        void steeringAngleControl(double req_angle,const StruIOChassisInfoDebug& io_chassis_info_debug);
        void decelerationControl(double deceleration);
        void standstillControl(double deceleration, int cmd_req);
        void autoHoldForbidden(const StruIOChassisInfoDebug& io_chassis_info_debug, int hsahd_req = 0x1);
        void emergencyBrake();

        template<typename T>
        T my_clamp(T source, T min_value, T max_value){
            T res = 0;
            if(source > max_value){
                res = max_value;
            }
            else if(source < min_value){
                res = min_value;
            }
            else{
                res = source;
            }
            return res;
        }

        void processSavedTopicData();
        int changanToWMWheelDir(int dir);
        void recordIOChassisDebugInfo(const StruIOChassisInfoDebug& io_chassis_info_debug);
        std::mutex prase_data_mtx_;//处理后的数据互斥量，针对全局变量StruIOChassisInfoDebug
        StruOutputDataChassis output_data_chassis_;
        StruOutputDataWheelSpeedInfo output_data_wheel_speed_;
        StruOutputDataShakeHand output_data_shake_hand_;
        StruOutputDataSystemCheck output_data_system_check_;
        StruOutputDataImuInfo output_data_imu_info_;

        StruInputLatAndLongControlCmd input_lat_lon_control_;
        StruInputLatAndLongShakeHandCmd input_shake_hand_;
        StruInputHoldPressureChangan input_hold_pressure_;
        StruInputSystemCheckCmd input_system_check_;

        StruChassisShiftGearInfo gear_event_info_;
        StruIOChassisInfoDebug io_chassis_info_debug_;

    public:
        vector<string> sub_msgs_;
        map<string, string> topic_map_;
        map<string, pFun> topic_msg_map_;

    //need by cyber
    public:
        void io_stop();
        void io_start();
        std::atomic<bool> ic_callback_run_{false};
    private:
        std::shared_ptr <Node> pNode = nullptr;
        std::shared_ptr <apollo::cyber::Timer> pTimerControl = nullptr;
        std::shared_ptr <apollo::cyber::Timer> pTimerProcessTopic = nullptr;
        std::shared_ptr <apollo::cyber::Timer> pTimerDebug = nullptr;
        std::shared_ptr< Writer<MessageTest> > pWriterMessageTest = nullptr;
        std::shared_ptr< Writer<IOChassisInfoDebug> > pWriterIOChassisInfoDebug = nullptr;

        std::shared_ptr< Writer<Imu> > pWriterImuInfo = nullptr;
        std::shared_ptr< Writer<Ultrasonic> > pWriterUltrasonicInfo = nullptr;
        std::shared_ptr< Writer<SystemCheckResultInfo> > pWriterSystemCheckResultInfo = nullptr;
        std::shared_ptr< Writer<ShakeHandStateInfo> > pWriterShakeHandStateInfo = nullptr;
        std::shared_ptr< Writer<VehicleBodyInfo> > pWriterVehicleBodyInfo = nullptr;
        std::shared_ptr< Writer<ChassisLatInfo> > pWriterChassisLatInfo = nullptr;
        std::shared_ptr< Writer<ChassisLongInfo> > pWriterChassisLongInfo = nullptr;
        std::shared_ptr< Writer<WheelSpeedInfo> > pWriterWheelSpeedInfo = nullptr;
        std::shared_ptr< Writer<ChassisShiftGearInfo> > pWriterChassisShiftGearInfo = nullptr;

        NodeCfg nodeCfg;
        string io_cfg_path_;
        TakeOverCmd takeover_cmd_;
        IOControlCmd io_control_cmd_;
        IOUnitTestCmd io_unittest_cmd_;
        ExceptionHandleInfo exception_handle_info_;
        void IOExceptionHandle(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void latExceptionHandle(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void lonExceptionHandle(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void IOExceptionOutputHandle(const StruIOChassisInfoDebug& io_chassis_info_debug);
        void IOExceptionReset();
        void resetOutputShakeHand();
        void resetOutputDataChassis();

        bool time_set_flag_ = false; //use by gearTransProcess function to calculate maintain_time
        std::mutex cyber_sub_mtx_; //for cyber message callback function
        Filter uls_filter_[12];

    private:
        void timerProcessTopicHandler();
        void timerControlHandler();
        void timerDebugHandler();
        void initCyberPub();
        void initCyberSub();

        double getTimeS(struct timeval * pt);
        //io control debug
        void IOTakeoverCallback(const std::shared_ptr <IOTakeover>& io_takeover_cmd);
        void takeoverCmdHandle(const TakeOverCmd& takeover_cmd,
                                                               const StruIOChassisInfoDebug& io_chassis_info_debug);
        void IOControlCallback(const std::shared_ptr <IOControl>& io_control_cmd);
        void ioControlCmdHandle(const IOControlCmd& io_control_cmd,
                                                                const StruIOChassisInfoDebug& io_chassis_info_debug);
        void IOUnitTestCallback(const std::shared_ptr <IOControlUnitTest>& io_unittest_cmd);
        void EnableUSSCallback(const std::shared_ptr <EnableUSSUlt>& enable_uss_cmd);
        void EnableUSSBaseInit();
        void ioUnitTestHandle(const IOUnitTestCmd& io_unittest_cmd,
                                                        const StruIOChassisInfoDebug& io_chassis_info_debug);
        //input control callback
        void resetRevCmd(double cur_angle);
        void LatAndLongControlCmdCallback(const std::shared_ptr <LatAndLongControlCmd>& lat_lon_control_cmd);
        void latLonControlCmdHandle(const StruInputLatAndLongControlCmd& lat_lon_control_cmd,
                                                                         const StruIOChassisInfoDebug& io_chassis_info_debug);
        void holdOrReleaseCmdHandle(const StruInputLatAndLongControlCmd& lat_lon_control_cmd,
                                                                                            const StruIOChassisInfoDebug& io_chassis_info_debug);
        void LatAndLongShakeHandCmdCallback(const std::shared_ptr <LatAndLongShakeHandCmd>& shake_hand_cmd);
        void shakeHandCmdHandle(const StruInputLatAndLongShakeHandCmd& shake_hand_cmd,
                                                                    const StruIOChassisInfoDebug& io_chassis_info_debug,
                                                                    const ExceptionHandleInfo& exception_handle_info);
        void HoldPressureChanganCallback(const std::shared_ptr <HoldPressureChangan>& hold_pres_cmd);
        void holdPressureCmdHandle(const StruInputHoldPressureChangan& hold_pres_cmd);
        void SystemCheckCmdCallback(const std::shared_ptr <SystemCheckCmd>& sys_chk_cmd);
        void systemCheckCmdHandle(const StruInputSystemCheckCmd& sys_chk_cmd);
        //cmd process
        void breakingProcess(double deceleration);
        void drivingProcess(int req_torq, const StruIOChassisInfoDebug& io_chassis_info_debug, double acc=0.25);
        void steeringProcess(double req_angle, const StruIOChassisInfoDebug& io_chassis_info_debug);
        void gearTransProcess(uint32_t gear, const StruIOChassisInfoDebug& io_chassis_info_debug);

        int commonGearToChanganGear(int common_gear);
        int changanGearToCommonGear(int changan_gear);

        //io contorl debug
        void ioChassisInfoDebugPub(const StruIOChassisInfoDebug& io_chassis_info_debug);
        //output feedback info
        void ImuInfoPub();
        void UltrasonicInfoPub();
        void VehicleBodyInfoPub();
        void ShakeHandStateInfoPub();
        void SystemCheckResultInfoPub();
        void ChassisLatInfoPub();
        void ChassisLongInfoPub();
        void WheelSpeedInfoPub();
        void ChassisShiftGearInfoPub();

#if USE_DEBUG
    private:
    int seq_process_topic_;
    int seq_control_;
    int step_process_topic_;
    int step_control_;
    long time_process_topic_;
    long time_control_;
    int prase_rev_topic_idx_;
    string prase_rev_topic_;
    string prase_rev_topic_content_;



#endif
    };

}

#endif

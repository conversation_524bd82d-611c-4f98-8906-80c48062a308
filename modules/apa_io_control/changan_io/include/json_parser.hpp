#ifndef __JSONPARSER_H__
#define __JSONPARSER_H__

#include <string>
#include <vector>
#include "nlohmann/json.hpp"
#include <fstream>
#include <iostream>
#include "cyber/cyber.h"

namespace io_server{
    class JsonParser
    {
    public:
        JsonParser(const std::string& fileName):isValied_(false)
        {
            std::cout<< __func__<<": Config file path is: " << fileName.c_str() <<std::endl;
            _fileName = fileName;
            std::ifstream ifs(fileName);
            if (!ifs.is_open()) {
                AINFO<< __func__<<" E: Can not find config file: " << fileName.c_str();
                std::cout<< __func__<<" E: Can not find config file: " << fileName.c_str() <<std::endl;
                return;
            }
            ifs >> _info;
            ifs.close();
            std::cout<< __func__<<": " << _info.dump().c_str() <<std::endl;

            isValied_ = true;

        }

        bool isValid(){
            return isValied_;
        }

        nlohmann::json getJsonParser(){
            return _info;
        }

    private:
        /* data */
        nlohmann::json _info;
        bool isValied_;
        std::string _fileName;

    };
}
#endif


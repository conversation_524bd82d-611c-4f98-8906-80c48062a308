set(CMAKE_SYSTEM_NAME QNX)
set(CMAKE_SYSTEM_PROCESSOR aarch64)

set(QNX_PATH /usr/local/qnx710)
set(QNX_THIRD /usr/local/third)

set(CMAKE_PREFIX_PATH 
    ${QNX_THIRD}/aarch64/fastrtps_1_8_0
    ${QNX_THIRD}/aarch64/fastcdr_1_8_0
    ${QNX_THIRD}/aarch64/protobuf
    ${QNX_THIRD}/aarch64/gflags
    ${QNX_THIRD}/aarch64/glog
    ${QNX_THIRD}/aarch64/tinyxml
    ${QNX_THIRD}/aarch64/uuid
    ${QNX_THIRD}/aarch64/boost
    ${QNX_THIRD}/aarch64/cyber_qnx
    ${QNX_THIRD}/aarch64/osqp
    ${QNX_THIRD}/aarch64/osqp-eigen
    ${QNX_THIRD}/aarch64/eigen
    ${QNX_THIRD}/aarch64/opencv
    ${QNX_THIRD}/aarch64/flat
    ${QNX_THIRD}/aarch64/ceres-solver
    ${QNX_THIRD}/aarch64/yaml
    ${QNX_THIRD}/aarch64/toml
    ${QNX_THIRD}/aarch64/websocketpp
    ${QNX_THIRD}/aarch64/fmt/
    ${QNX_THIRD}/aarch64/tinyxml
    ${QNX_THIRD}/aarch64/spdlog
    ${QNX_THIRD}/aarch64/gtest
    ${QNX_THIRD}/aarch64/libzip
    ${QNX_THIRD}/aarch64/ceres-solver
    ${QNX_PATH}/hlos_dev_qnx/apps/qnx_ap/prebuilt/aarch64le
    ${QNX_PATH}/hlos_dev_qnx/apps/qnx_ap/prebuilt/aarch64le/usr/lib/graphics/qc
    ${QNX_PATH}/mega/prebuilts/common/mega_ipc
    ${QNX_PATH}/mega/prebuilts/common/iceoryx
)

include_directories(
    ${QNX_THIRD}/aarch64/fastrtps_1_8_0/include
    ${QNX_THIRD}/aarch64/fastcdr_1_8_0/include
    ${QNX_THIRD}/aarch64/glog/include
    ${QNX_THIRD}/aarch64/gflags/include
    ${QNX_THIRD}/aarch64/tinyxml/include
    ${QNX_THIRD}/aarch64/uuid/include
    ${QNX_THIRD}/aarch64/cyber_qnx/include
    ${QNX_THIRD}/aarch64/osqp/include
    ${QNX_THIRD}/aarch64/osqp-eigen/include
    ${QNX_THIRD}/aarch64/eigen/include
    ${QNX_THIRD}/aarch64/boost/include
    ${QNX_THIRD}/aarch64/opencv/include
    ${QNX_THIRD}/aarch64/flat/include
    ${QNX_THIRD}/aarch64/ceres-solver/include
    ${QNX_THIRD}/aarch64/yaml/include
    ${QNX_THIRD}/aarch64/toml/include
    ${QNX_THIRD}/aarch64/websocketpp/include
    ${QNX_THIRD}/aarch64/fmt/include
    ${QNX_THIRD}/aarch64/tinyxml/include
    ${QNX_THIRD}/aarch64/spdlog/include
    ${QNX_THIRD}/aarch64/gtest/include
    ${QNX_THIRD}/aarch64/libzip/include
    ${QNX_THIRD}/aarch64/ceres-solver/include
    ${QNX_PATH}/mega/prebuilts/common/mega_ipc/include
    ${QNX_PATH}/mega/prebuilts/common/mega_ipc/include/common
    ${QNX_PATH}/mega/prebuilts/common/iceoryx/include
)
set(CPULIST aarch64)
set(QNX_ROOT ${QNX_PATH})
set(QNX_HOST ${QNX_PATH}/host/linux/x86_64)
set(QNX_TARGET ${QNX_PATH}/target/qnx7)

set(CMAKE_C_COMPILER ${QNX_HOST}/usr/bin/aarch64-unknown-nto-qnx7.1.0-gcc)
message(STATUS "CMAKE_C_COMPILER ${QNX_HOST}/usr/bin/aarch64-unknown-nto-qnx7.1.0-gcc")
set(CMAKE_CXX_COMPILER ${QNX_HOST}/usr/bin/aarch64-unknown-nto-qnx7.1.0-g++)
#set(CMAKE_SYSROOT ${QNX_TARGET}/aarch64le)
set(QNX_BASE ${QNX_PATH})
set(QNX_BUILD_PACKAGE BSP)


set(MAKE_FIND_ROOT_PATH ${QNX_TARGER}/aarch64le)
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=gnu++1z")
add_definitions("-D_QNX_SOURCE -DASIO_HAS_PTHREADS")

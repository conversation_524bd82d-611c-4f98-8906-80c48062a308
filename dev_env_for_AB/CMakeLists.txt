project(ADAS-CORE LANGUAGES C CXX)
cmake_minimum_required(VERSION 3.14)
option(NO_USE_CUDA "use cuda" OFF)
option(BUILD_BST_COMPILE "compile bst mode" OFF)
option(BUILD_ACC_COMPILE "compile acc x86 mode" OFF)

include_directories(/usr/local/cyber_from_source/include)
# usr/local/include)
link_directories(/usr/local/cyber_from_source/lib)


find_package(fastrtps REQUIRED)
find_package(fastcdr REQUIRED)
find_package(Flatbuffers REQUIRED)
find_package(Protobuf REQUIRED)
find_package(GLog REQUIRED)
find_package(GFlags REQUIRED)
include_directories(${Protobuf_INCLUDE_DIRS})
include_directories(${CMAKE_CURRENT_BINARY_DIR})
include_directories(${PROJECT_SOURCE_DIR})

if(CMAKE_SYSTEM_NAME MATCHES QNX )
  find_library(LIBUUID_LIBRARY librst_uuid.so)
  find_library(LIBCYBER_LIBRARY libcyber.so)
  find_library(SOCKET_LIB socket PATHS  /usr/local/qnx710/target/qnx7/aarch64le/io-sock/lib/)
endif()


message(STATUS "DEPS_DIR:${DEPS_DIR} NO_DEBUG:${NO_DEBUG}")
file(GLOB_RECURSE PROTO_FILES RELATIVE ${CMAKE_SOURCE_DIR}
    "modules/*.proto"
    "thirdparty/recommend_protocols/*.proto"
    "thirdparty/avp-slam/third_party/*.proto"
    )
message("found files ${PROTO_FILES}")

set(FLATBUF_FILES "thirdparty/recommend_protocols/drivers/flatbuf/pointcloud.fbs")
message("found files ${FLATBUF_FILES}")

# Below snip is from grpc.cmake and modified to work with this project.
# Use this function to keep dir structure of generated files.

if(CMAKE_CROSSCOMPILING)
  find_program(_PROTOBUF_PROTOC protoc)
  find_program(_FLATBUF_FLATC flatc)
else()
  set(_PROTOBUF_PROTOC $<TARGET_FILE:protobuf::protoc>)
  set(_FLATBUF_FLATC $<TARGET_FILE:flatbuffers::flatc>)
endif()


function(protobuf_generate_cc PROTO_FILES PROTO_CC_FILES)
  foreach(_TARGET_PROTO ${PROTO_FILES})
    get_filename_component(_TARGET ${_TARGET_PROTO} NAME_WE)
    get_filename_component(_TARGET_DIR ${_TARGET_PROTO} DIRECTORY)
    # Generated sources
    string(REGEX REPLACE "[.]proto$" ".pb.cc" _TARGET_PROTO_SRCS ${_TARGET_PROTO})
    string(REGEX REPLACE "[.]proto$" ".pb.h" _TARGET_PROTO_HDRS ${_TARGET_PROTO})

    #NOTE: CMAKE_SOURCE_DIR is added to specify absolute path for protoc.
    add_custom_command(
      OUTPUT "${CMAKE_BINARY_DIR}/${_TARGET_PROTO_SRCS}" "${_TARGET_PROTO_HDRS}"
      COMMAND ${_PROTOBUF_PROTOC}
      ARGS --cpp_out "${CMAKE_CURRENT_BINARY_DIR}"
           -I "${PROJECT_SOURCE_DIR}"  -I  "${Protobuf_INCLUDE_DIRS}"
       "${CMAKE_SOURCE_DIR}/${_TARGET_PROTO}"
      DEPENDS "${CMAKE_SOURCE_DIR}/${_TARGET_PROTO}"
      COMMENT "Running C++ protocol buffer compiler on ${_TARGET_PROTO}"
      VERBATIM)
    set(LOCAL_PROTO_CC_FILES ${LOCAL_PROTO_CC_FILES} "${CMAKE_BINARY_DIR}/${_TARGET_PROTO_SRCS}")
  endforeach()
  set(${PROTO_CC_FILES} ${LOCAL_PROTO_CC_FILES} PARENT_SCOPE)
endfunction()

protobuf_generate_cc("${PROTO_FILES}" PROTO_CC_FILES)
add_custom_target(force_generate_proto ALL
    DEPENDS ${PROTO_CC_FILES}
    COMMENT ">>protoc finished<<"
)
message(STATUS "gen proto cc files ${PROTO_CC_FILES}")




# An empty target to trigger python file generation.
MACRO(include_module module_control_val module_source_path)
    #message(STATUS "*(INFO) ${module_control_val} : ${${module_control_val}}")
    if(${${module_control_val}})
        message(STATUS "*(INFO) module_source_path : ${module_source_path}")
        include("${module_source_path}/dep.cmake")
    endif()
ENDMACRO(include_module)

MACRO(add_sub_module module_control_val module_source_path)
    #message(STATUS "*(INFO) ${module_control_val} : ${${module_control_val}}")
    if(${${module_control_val}})
        message(STATUS "*(INFO) module_source_path : ${module_source_path}")
        add_subdirectory(${module_source_path})
    endif()
ENDMACRO(add_sub_module)


file(GLOB_RECURSE EXAMPLE_PROTO_FILES RELATIVE ${CMAKE_SOURCE_DIR} "examples/*.proto")
message("found files ${EXAMPLE_PROTO_FILES}")
protobuf_generate_cc("${EXAMPLE_PROTO_FILES}" EXAMPLE_PROTO_CC_FILES)
add_custom_target(force_generate_exmaple_proto ALL
    DEPENDS ${EXAMPLE_PROTO_FILES}
    COMMENT ">>example protoc finished<<"
)



if(CMAKE_SYSTEM_NAME MATCHES QNX)
set(qnx_path ${QNX_PATH})

  add_executable(listener ${CMAKE_SOURCE_DIR}/examples/listener.cc ${EXAMPLE_PROTO_CC_FILES})
  target_link_libraries(listener c ${SOCKET_LIB} ${LIBCYBER_LIBRARY}  ${LIBUUID_LIBRARY} ${PROTOBUF_LIBRARY} glog::glog atomic fastrtps gflags fastcdr )
  add_executable(talker ${CMAKE_SOURCE_DIR}/examples/talker.cc ${EXAMPLE_PROTO_CC_FILES})
  target_link_libraries(talker c ${SOCKET_LIB} ${LIBCYBER_LIBRARY} ${LIBUUID_LIBRARY}  ${PROTOBUF_LIBRARY} glog::glog atomic fastrtps gflags fastcdr   )

  add_custom_command(
    TARGET listener
    POST_BUILD
    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/lib
    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/conf
    COMMAND cp ${PROJECT_SOURCE_DIR}/script/set_*.sh ${PROJECT_BINARY_DIR}/output
    COMMAND mv ${PROJECT_BINARY_DIR}/listener ${PROJECT_BINARY_DIR}/output/bin
    COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py ${PROJECT_BINARY_DIR}/output/bin/listener  -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so:libslog2.so -o ${PROJECT_BINARY_DIR}/output/lib
  )
  add_custom_command(
    TARGET listener
    POST_BUILD
    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
    COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
    COMMAND cp /usr/local/third/aarch64/cyber_qnx/bin/cyber_recorder ${PROJECT_BINARY_DIR}/output/bin/
    COMMAND cp /usr/local/third/aarch64/cyber_qnx/bin/cyber_monitor ${PROJECT_BINARY_DIR}/output/bin/
    COMMAND python ${PROJECT_SOURCE_DIR}/script/copy_qnx_depends.py /usr/local/third/aarch64/cyber_qnx/bin/cyber_monitor  -e ${qnx_path}/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-readelf -L ${qnx_path}/target/qnx7/aarch64le/lib/:${qnx_path}/target/qnx7/aarch64le/usr/lib/ -I libc++.so:libm.so:libc.so:libsocket.so:libslog2.so -o ${PROJECT_BINARY_DIR}/output/lib
    COMMAND cp /usr/local/third/aarch64/cyber_qnx/include/cyber/conf/cyber.pb.conf ${PROJECT_BINARY_DIR}/output/conf/
    COMMAND cp /usr/local/third/aarch64/cyber_qnx/include/cyber/conf/cyber_.pb.conf ${PROJECT_BINARY_DIR}/output/conf/
  )
add_custom_command(
  TARGET talker
  POST_BUILD
  COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output
  COMMAND mkdir -p ${PROJECT_BINARY_DIR}/output/bin
  COMMAND mv ${PROJECT_BINARY_DIR}/talker ${PROJECT_BINARY_DIR}/output/bin
  )
else()
  add_executable(listener ${CMAKE_SOURCE_DIR}/examples/listener.cc ${EXAMPLE_PROTO_CC_FILES})
  target_link_libraries(listener PUBLIC cyber protobuf protobuf-lite fastrtps fastcdr pthread m z rt gflags glog atomic uuid)
  add_executable(talker ${CMAKE_SOURCE_DIR}/examples/talker.cc ${EXAMPLE_PROTO_CC_FILES})
  target_link_libraries(talker PUBLIC cyber protobuf protobuf-lite fastrtps fastcdr pthread m z rt gflags glog atomic uuid)
endif()


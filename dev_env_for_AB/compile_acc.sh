#!/bin/bash
set -e
export LANG=en_US.UTF-8
SCRIPT=$(readlink -f $0)
BASEDIR=$(dirname $SCRIPT)
CURR_BASE_NAME=$(basename $BASEDIR)
echo $BASEDIR
echo $CURR_BASE_NAME

BASEDIR_=${BASEDIR}/../
cd $BASEDIR
CURR_TAG_VERSION=$(git rev-parse --short HEAD)
CURR_USER=${USER}
RUNNING_CONTAINER=""
IF_NEW_CONTAINER=0
DEPENDS="DEPENDS"

ADAS_BUILD_DOCKER_URL='harbor-local.i-tetris.com/adas'
ADAS_CORE_ARM_GPU_NAME="adas-gpu-aarch64"
ARM_DOCKER_VERSION='74c1be7'
ACC_DOCKER_VERSION='carla-bst.0.5'
ACC_ADAS_CORE_CPU_IMAGE="carla-adas-cpu-release"
ACC_ADAS_CORE_GPU_IMAGE="carla-adas-gpu-release"


# set coredump path
set +e
cat /proc/sys/kernel/core_pattern | grep zyf.core
ret=$?
if [ $ret -ne 0 ];then
    sudo bash -c "echo zyf.core.%t.%p.%e >> /proc/sys/kernel/core_pattern"
fi
set -e

warn_print(){
    echo -e "\033[33mWARN: $1 \033[0m"
}

fatal_print(){
    echo -e "\033[31mFATAL: $1 \033[0m"
}

info_print(){
    echo -e "\033[32mINFO: $1 \033[0m"
}

echo_print(){
    echo -e "\033[31m$1 \033[0m"
}


h_help() {
    echo_print "usage : ./compile.sh to build ${CURR_BASE_NAME}."
    echo_print "ex [ -h used to show help ]: ./compile.sh -h"
    echo_print "ex [ -v used to set tag version ]: ./compile.sh -v tag.xx"
    echo_print "ex [ -u used to set user name ]: ./compile.sh -u xiaoyang"
    echo_print "ex [ -i used to choose compile method ]: ./compile.sh -i"
    echo_print "ex [ -p used to open googletools ]: ./compile.sh -p"
    echo_print "ex [ -c used to choose cpu image, default gpu ]: ./compile.sh -c"
    echo_print "ex [ -s used to clean compile container && exit ]: ./compile.sh -s"
    echo_print "ex [ -a used to cross compile aarch64]: ./compile.sh -a"
    echo_print "ex [ -q used to cross compile qnx]: ./compile.sh -q"
    echo_print "ex [ -t used to compile test and tessy version]: ./compile.sh -t"
    echo_print "ex [ -d used to compile debug and asan version]: ./compile.sh -d"
    echo_print "ex [ -r used to compile release version ]: ./compile.sh -r"
    echo_print "ex [ -m used to compile multi-process version ]: ./compile.sh -m"
    echo_print "ex [ -n used to compile asan version ]: ./compile.sh -n"
    echo_print "ex [ -f used to choose compile car type ]: ./compile.sh -f CHANGAN_385"
}



checkout_tag(){
    pushd ${BASEDIR}
    tag=$1
    git fetch -t && git checkout $tag
    popd
}


prepare_docker(){
    l_docker_version=""
    l_adas_core_gpu_image=""
    l_adas_core_cpu_image=""
    l_docker_version="${ACC_DOCKER_VERSION}"
    l_adas_core_gpu_image="${ACC_ADAS_CORE_GPU_IMAGE}"
    l_adas_core_cpu_image="${ACC_ADAS_CORE_CPU_IMAGE}"
    gpu_image_build=$1
    clean_runing_container=$2
    enter_runing_container=$3
    use_image_name=$4
    ac=$5
    use_container_name=$6
    docker_image_name=""
    docker_container_name=""
    docker_volumes_gpu=""
    #echo $gpu_image_build && exit 0
    if [[ $gpu_image_build -eq 1 ]];then
        docker_volumes_gpu="--gpus all"
        docker_image_name=""
        docker_container_name="${CURR_USER}-${CURR_BASE_NAME}-container-gpu-${ac}.${l_docker_version}"
        if [[ $ac = "aarch64" ]];then
            docker_image_name=$(docker images --filter=reference=${ADAS_BUILD_DOCKER_URL}/${ADAS_CORE_ARM_GPU_NAME}:${ARM_DOCKER_VERSION})
            image_line=$(echo "$docker_image_name" | sed -n '2p')
            if [[ -z $image_line ]];then
                info_print "docker pull ${ADAS_BUILD_DOCKER_URL}/${ADAS_CORE_ARM_GPU_NAME}:${ARM_DOCKER_VERSION}"
                docker pull ${ADAS_BUILD_DOCKER_URL}/${ADAS_CORE_ARM_GPU_NAME}:${ARM_DOCKER_VERSION}
            fi
            docker_image_name="${ADAS_BUILD_DOCKER_URL}/${ADAS_CORE_ARM_GPU_NAME}:${ARM_DOCKER_VERSION}"
            docker_container_name="${CURR_USER}-${CURR_BASE_NAME}-container-gpu-${ac}.${ARM_DOCKER_VERSION}"
        else
            docker_image_name=$(docker images --filter=reference=${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version})
            image_line=$(echo "$docker_image_name" | sed -n '2p')
            if [[ -z $image_line ]];then
                image_tar="${ADAS_BUILD_DOCKER_URL}-${l_adas_core_gpu_image}.${l_docker_version}.tar"
                #echo "docker load $image_tar"
                #wget "http://10.25.8.222/${image_tar}"
                #docker load -i ${image_tar}
                #rm -rf ${image_tar}
                info_print "docker pull ${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version}"
                docker pull ${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version}
            fi
            docker_image_name="${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version}"
        fi
    else
        docker_container_name="${CURR_USER}-${CURR_BASE_NAME}-container-cpu-${ac}.${l_docker_version}"
        docker_image_name=$(docker images --filter=reference=${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version})
        image_line=$(echo "$docker_image_name" | sed -n '2p')
        if [[ -z $image_line ]];then
            image_tar="${ADAS_BUILD_DOCKER_URL}-${l_adas_core_gpu_image}.${l_docker_version}.tar"
            info_print "docker pull ${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version}"
            docker pull ${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version}
        fi
        docker_image_name="${ADAS_BUILD_DOCKER_URL}/${l_adas_core_gpu_image}:${l_docker_version}"
    fi

    set +e
    if [[ ! $use_image_name = "X" ]];then
        docker_image_name=$use_image_name
        docker_container_name=$use_container_name
        ret=$(docker images --filter=reference=$docker_image_name)
        image_line=$(echo "$ret" | sed -n '2p')
        if [[ -z $image_line ]];then
            info_print "docker pull $docker_iamge_name"
            docker pull $docker_image_name
        fi
    fi

    info_print "using image : ${docker_image_name}"
    info_print "using container : ${docker_container_name}"
    docker ps -a | grep -w $docker_container_name
    if [[ $? -ne 0 ]];then
        if [[ $clean_runing_container -eq 1 ]];then
            echo "docker stop container $docker_container_name"
            exit 0
        fi
        set -e
        info_print "docker run new container"
        local display="${DISPLAY:-:0}"
        if [[ $ac = "aarch64" ]];then
            docker run -dt ${docker_volumes_gpu} --name $docker_container_name                          \
                -e DISPLAY=${display}                                                                   \
                -v ${BASEDIR_}:/workdir                                                                 \
                -v /tmp/.X11-unix:/tmp/.X11-unix:rw                                                     \
                -w /workdir/${CURR_BASE_NAME}                                                           \
                --hostname "${CURR_BASE_NAME}-host"                                                     \
                --add-host "$(hostname):127.0.0.1"                                                      \
                --net=host                                                                              \
                --user 0                                                                                \
                --ipc=host                                                                              \
                --shm-size '2G'                                                                         \
                --env QT_X11_NO_MITSHM=1                                                                \
                ${docker_image_name}
        else
            docker run -dt ${docker_volumes_gpu} --name $docker_container_name --privileged             \
                -e DISPLAY=${display} -e USE_GPU_HOST=${USE_GPU_HOST} -e NVIDIA_VISIBLE_DEVICES=all     \
                -e NVIDIA_DRIVER_CAPABILITIES=compute,video,graphics,utility                            \
                -e OMP_NUM_THREADS=1 -e DOCKER_IMG="${docker_image_name}"                               \
                -v ${BASEDIR_}:/workdir -v /dev:/dev                                                    \
                -v /etc/localtime:/etc/localtime:rw                                                     \
                -v /dev/null:/dev/raw1394                                                               \
                -v /media:/media                                                                        \
                -v /usr/share/fonts/truetype:/usr/share/fonts/truetype/                                 \
                -v /tmp/.X11-unix:/tmp/.X11-unix:rw                                                     \
                -v /lib/modules:/lib/modules                                                            \
                -w /workdir/${CURR_BASE_NAME}                                                           \
                --add-host "$(hostname):127.0.0.1"                                                      \
                --hostname "${CURR_BASE_NAME}-host"                                                     \
                --net=host                                                                              \
                --ipc=host                                                                              \
                --shm-size '2G'                                                                         \
                --env QT_X11_NO_MITSHM=1                                                                \
                --user 0                                                                                \
                --pid=host                                                                              \
                ${docker_image_name}
        fi
        if [[ $enter_runing_container -eq 1 ]];then
            docker exec -it ${docker_container_name} env LANG=C.UTF-8 /bin/bash
            exit $?
        fi
    else
        if [[ $clean_runing_container -eq 1 ]];then
            docker stop $docker_container_name
            docker rm $docker_container_name
            info_print "docker stop container $docker_container_name"
            exit $?
        fi
        info_print "docker start container $docker_container_name"
        docker start $docker_container_name
        if [[ $enter_runing_container -eq 1 ]];then
            docker exec -it ${docker_container_name} env LANG=C.UTF-8 /bin/bash
            exit $?
        fi
    fi
    RUNNING_CONTAINER="$docker_container_name"
}


Main(){
    local l_base_dir=${BASEDIR}
    local l_debug=0
    local l_release=1
    local l_asan=0
    local l_test=0
    local l_example=0
    local l_perf=0
    local l_arm=0
    local l_qnx=0
    local l_code_coverage=0
    local build_type=Release
    local build_dir=build
    local built_dir=built
    local l_incremental=1
    local l_gpu_compile=1
    local l_clean_container=0
    local l_enter_container=0
    local l_more_module=0
    local l_only_compile_module="ALL"
    local l_choose_image="X"
    local cmake_build_test="-DBUILD_TEST=OFF -DBUILD_CODE_COVERAGE=OFF"
    local cmake_build_example="-DBUILD_EXAMPLE=OFF"
    local cmake_build_perf="-DBUILD_PERF=OFF"
    local cmake_build_single="-DBUILD_SINGLE_PROCESS=ON -DUSE_SINGLE=ON"
    local cartype="WEIMA"
    local cmake_cartype="-DCAR_WEIMA=ON"
    local l_cpu_num=1
    local l_download_model=0
    local l_BST=0
    cpus=$(nproc --all)
    ((cpus=cpus/2))
    l_cpu_num=${cpus}
    while getopts dj:hv:u:g:iteqpsoncabmrzlxf: OPTION
    do
        case ${OPTION} in
            h) info_print ">>>>>>>>>>>>>>>>>>HELP>>>>>>>>>>>>>>>>>."
                h_help $?
                exit
                ;;
            v) CURR_TAG_VERSION=${OPTARG}
                checkout_tag $CURR_TAG_VERSION
                ;;
            u) CURR_USER=${OPTARG}
                ;;
            a) l_arm=1
                ;;
            b) l_BST=1
                build_dir="build-bst"
                built_dir='built-bst'
                ;;
            n) l_asan=1
                build_type="Asan"
                build_dir="build-asan"
                ;;
            r) l_release=1
                ;;
            p) l_perf=1
                build_type="GPerf"
                build_dir="build-gperf"
                ;;
            q) l_qnx=1
                build_dir="build-qnx"
                built_dir='built-qnx'
                ;;
            i) l_incremental=0
                ;;
            t) l_test=1
                cmake_build_test="-DBUILD_TEST=ON"
                ;;
            e) l_example=1
                cmake_build_example="-DBUILD_EXAMPLE=ON"
                ;;
            c) l_gpu_compile=0
                ;;
            s) l_clean_container=1
                ;;
            o) l_enter_container=1
                ;;
            g) l_choose_image=${OPTARG}
                ;;
            m) l_more_module=1
                cmake_build_single="-DBUILD_SINGLE_PROCESS=OFF -DUSE_SINGLE=OFF"
                ;;
            d) l_debug=1
                build_type="Debug"
                build_dir="build-debug"
                ;;
            j) l_cpu_num=${OPTARG}
                ;;
            x) l_download_model=1
                ;;
            f) cmake_cartype="-DCAR_${OPTARG}=ON"
                cartype="${OPTARG}"
                ;;
            z) l_code_coverage=1
                cmake_build_test="-DBUILD_TEST=ON -DBUILD_CODE_COVERAGE=ON"
                ;;
        esac
    done

    if [[ $l_code_coverage -eq 1 ]] && [[ $l_test -eq 0 ]];then
        l_test=1
        info_print "due to l_code_coverage is $l_code_coverage, set l_test to $l_test !!! cmake_build_test=${cmake_build_test}"
    fi

    if [ $l_cpu_num -lt 8 ];
    then
        l_cpu_num=8
    fi
    ac=$(uname -m)
    BUILD_MODULE=""

    start=`date +%s`
    #time=`echo $start $end | awk '{print $2-$1}'`
    #echo $time
    info_print "welcome to compile adas, ${CURR_USER}. system arch:$ac, use cpu core:${l_cpu_num}, pwd:$(pwd), date:$(date), more_process:$l_more_module"


    if [[ $l_qnx -eq 1 ]];then
        l_gpu_compile=0
        qnx_image_tag=cyber.0.5.2-8255
        if [ $l_debug -eq 1 ];then
            #qnx_image_tag="${qnx_image_tag}_debug"
            echo "debug image"
        fi
        l_choose_image=${ADAS_BUILD_DOCKER_URL}/adas-qnx-release:${qnx_image_tag}
        l_choose_container="${CURR_USER}-${CURR_BASE_NAME}-container-qnx-${ac}.${qnx_image_tag}"
        prepare_docker $l_gpu_compile $l_clean_container $l_enter_container $l_choose_image ${ac} $l_choose_container

        if [[ $? -ne 0 ]] || [[ -z $RUNNING_CONTAINER ]];then
            fatal_print "FATAL"
            exit -1
        fi
        local qnx_path='/usr/local/qnx710'
	echo "-DCMAKE_TOOLCHAIN_FILE=/workdir/${CURR_BASE_NAME}/qnx710_docker.cmake"
        if [[ ${l_incremental} -eq 0 ]] || [[ ! -f $l_base_dir/$build_dir/Makefile ]];then
            sudo rm -rf $l_base_dir/$build_dir && mkdir -p $l_base_dir/$build_dir
            docker exec -t -w /workdir/${CURR_BASE_NAME}/$build_dir $RUNNING_CONTAINER /bin/bash -c "source ~/.bashrc \
                && cmake .. -DCMAKE_BUILD_TYPE=${build_type}                                          \
                -DCMAKE_VERBOSE_MAKEFILE=OFF                                                          \
                -DQNX_PATH=${qnx_path}                                                                \
                -DCMAKE_TOOLCHAIN_FILE=/workdir/${CURR_BASE_NAME}/qnx710_docker.cmake                 \
                -DCMAKE_INSTALL_PREFIX=${BASEDIR}/${built_dir}                                        \
                -DBUILD_SHARED_LIBS=ON ${BUILD_MODULE} ${cmake_build_test} ${build_mode}"
        else
            sudo rm -rf $l_base_dir/$build_dir/output/lib
            sudo rm -rf $l_base_dir/$build_dir/apa_external
        fi
        set -e
        docker exec -t -w /workdir/${CURR_BASE_NAME}/$build_dir ${RUNNING_CONTAINER} env LANG=C.UTF-8 /bin/bash -c "source ~/.bashrc && make -j$l_cpu_num"
        if [ $l_download_model -eq 1 ];then
            model_start=`date +%s`
            dir=model/perception/avm
            mkdir -p $dir
            python3 script/download_perception_model.py -o $dir
            sudo mv model $l_base_dir/$build_dir/output/
            model_end=`date +%s`
            time=`echo $model_start $model_end | awk '{print $2-$1}'`
            info_print "download model : $(date), cost time : ${time}s"
        fi
        end=`date +%s`
        time=`echo $start $end | awk '{print $2-$1}'`
        info_print "compile adas whoami, ${CURR_USER}. system arch:$ac, use cpu core:${l_cpu_num}, pwd:$(pwd), date:$(date), more_process:$l_more_module, build type:${build_type}, build dir:${build_dir}"
        info_print "qnx cross compile adas finish, current time : $(date), cost time : ${time}s"
        info_print "CAR TYPE --> ${cartype}"
        exit
    fi

    if [[ $ac = "x86_64" ]];then
        prepare_docker $l_gpu_compile $l_clean_container $l_enter_container $l_choose_image "x86_64"
        echo "x86_64"

        sleep 1
    elif [[ $ac = "aarch64" ]];then
        l_gpu_compile=1
        prepare_docker $l_gpu_compile $l_clean_container $l_enter_container $l_choose_image "aarch64"
        info_print "aarch64"
        sleep 1
    else
        warn_print "unknow machine type"
        sleep 1
        exit 1
    fi

    if [[ $? -ne 0 ]] || [[ -z $RUNNING_CONTAINER ]];then
        fatal_print "FATAL"
        exit -1
    fi


    if [[ ${l_incremental} -eq 0 ]] || [[ ! -f $l_base_dir/$build_dir/Makefile ]];then
        sudo rm -rf $l_base_dir/$build_dir && mkdir -p $l_base_dir/$build_dir
        #echo "$l_incremental" && exit 1
        #sudo rm -rf $l_base_dir/$built_dir && mkdir -p $l_base_dir/$built_dir
        cd $l_base_dir
        codeREV=$(git log -n 5 |grep commit |head -1 |awk '{print $2}')
        docker exec -t -w /workdir/${CURR_BASE_NAME}/$build_dir $RUNNING_CONTAINER /bin/bash      \
            -c "source ~/.bashrc && cmake .. -DCMAKE_BUILD_TYPE=${build_type}                     \
            -DCMAKE_VERBOSE_MAKEFILE=OFF                                                          \
            ${BUILD_MODULE}                                                                       \
            ${cmake_build_test}                                                                   \
            -Wno-dev                                                                              \
            -DBUILD_SHARED_LIBS=ON                                                                \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=1                                                     \
            -DARCHITECTURE_ID=${ac}                                                               \
            -DCMAKE_INSTALL_PREFIX=../$built_dir"
    else
        sudo rm -rf $l_base_dir/$build_dir/apa_external
    fi
    set -e
    docker exec -t -w /workdir/${CURR_BASE_NAME}/$build_dir ${RUNNING_CONTAINER} env LANG=C.UTF-8 /bin/bash -c "source ~/.bashrc && make -j$l_cpu_num"
    if [[ $l_release_version -eq 1 ]];
    then
        set -e
        docker exec -t -w /workdir/${CURR_BASE_NAME}/$build_dir ${RUNNING_CONTAINER} /bin/bash -c \
            "source ~/.bashrc && ../script/copy_depends.sh control lib" #>/dev/null 2>&1"
        ret=$?
        if [[ $ret -eq 0 ]];then
            info_print "--libs install ok!"
        else
            fatal_print "--libs install failed!"
            exit $ret
        fi
    fi
    if [ $l_download_model -eq 1 ];then
        model_start=`date +%s`
        dir=model/perception/avm
        mkdir -p $dir
        python3 script/download_perception_model.py -o $dir -m trt
        sudo mv model $l_base_dir/$build_dir/output/
        model_end=`date +%s`
        time=`echo $model_start $model_end | awk '{print $2-$1}'`
        info_print "download model : $(date), cost time : ${time}s"
    fi
    end=`date +%s`
    time=`echo $start $end | awk '{print $2-$1}'`
    info_print "compile adas whoami, ${CURR_USER}. system arch:$ac, use cpu core:${l_cpu_num}, pwd:$(pwd), date:$(date), more_process:$l_more_module, build type:${build_type}, build dir:${build_dir}"
    info_print "compile adas finish, current time : $(date), cost time : ${time}s"
    info_print "CAR TYPE --> ${cartype}"
    exit $?
}

Main "$@"

exit "$?"

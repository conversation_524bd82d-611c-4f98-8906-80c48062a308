#!/usr/bin/env python2.7
# -*- encoding: utf-8 -*-
# -*- mode: python -*-

from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

class CommandExecutor(object):
    """
    qnx copy dylib
    """
    import string
    __SAFE_CHARS = string.ascii_letters + string.digits + '+-_=/%@:,.'
    __SAFE_CHARS = frozenset(__SAFE_CHARS)
    del string

    def quote(self, arg):
        for c in arg:
            if c not in self.__SAFE_CHARS:
                break
        else:
            if not arg:
                return "''"
            else:
                return arg
        if len(arg) == 1:
            if arg == "'":
                return '"\'"'
            else:
                return "'%c'" % arg
        prev_index = None
        string = ''
        for index, c in enumerate(arg):
            if c == "'":
                if prev_index is not None:
                    prev_index = None
                    string += "'"
                string += '"\'"'
            else:
                if prev_index is None:
                    prev_index = index
                    string += "'"
                string += c
        if prev_index is not None:
            string += "'"
        return string

    def join(self, args):
        if not args:
            return "''"
        if len(args) == 1:
            return self.quote(args[0])
        return ' '.join(self.quote(arg) for arg in args)

    def combine(self, commands):
        return '; '.join(self.join(x) for x in commands)

    def log_message(self, color, message):
        import sys
        prompt = '\033[%sm%s\033[m' % (color, message)
        print(prompt, file=sys.stderr)

    def log_error(self, message):
        self.log_message('38;5;196', message)

    def log_warning(self, message):
        self.log_message('38;5;051', message)

    def log_info(self, message):
        self.log_message('38;5;231', message)

    def log_debug(self, message):
        self.log_message('38;5;240', message)

    def execute(self, args):
        import subprocess
        message = self.join(args)
        self.log_debug(message)
        subprocess.check_call(args)

class ElfBinaryFile(object):
    def __init__(self):
        self.path = None
        self.soname = None
        self.rpath = None
        self.runpath = None
        self.needed = None

    def __str__(self):
        import json
        obj = {'path': self.path,
                'soname': self.soname,
                'rpath': self.rpath,
                'runpath': self.runpath,
                'needed': self.needed}
        return json.dumps(obj, separators=(',', ': '), indent=4)

class ElfDependencyFinder(CommandExecutor):
    def __init__(self):
        import re
        import collections
        SONAME_PATTERN  = r'^ \w+? \(SONAME\) +? Library soname: \[(\S+?)\]$'
        RPATH_PATTERN   = r'^ \w+? \(RPATH\) +? Library rpath: \[(\S+?)\]$'
        RUNPATH_PATTERN = r'^ \w+? \(RUNPATH\) +? Library runpath: \[(\S+?)\]$'
        NEEDED_PATTERN  = r'^ \w+? \(NEEDED\) +? Shared library: \[(\S+?)\]$'
        self.soname_pattern  = re.compile(SONAME_PATTERN, re.M)
        self.rpath_pattern   = re.compile(RPATH_PATTERN, re.M)
        self.runpath_pattern = re.compile(RUNPATH_PATTERN, re.M)
        self.needed_pattern  = re.compile(NEEDED_PATTERN, re.M)
        self.args = None
        self.shared_set  = set()
        self.shared_list = []
        self.shared_dict = collections.OrderedDict()

    def log_debug(self, args):
        if self.args.debug_mode:
            message = self.join(args)
            super(ElfDependencyFinder, self).log_debug(message)

    def parse_arguments(self):
        import argparse
        parser = argparse.ArgumentParser(description='copy elf dependencies')
        parser.add_argument('binary', type=str, help='start elf binary')
        parser.add_argument('-d', '--debug-mode', action='store_true',
                help='debug mode; logging important commands and messages')
        parser.add_argument('-e', '--readelf-command', type=str, default='readelf',
                help='specify readelf command; default to "readelf"')
        parser.add_argument('-o', '--output-directory', type=str, default=None,
                help='output directory; default to directory of the executable or '
                'derive from rpath')
        parser.add_argument('-i', '--ignore-check',action='store_true',
                help='ignore check_consistency')
        ignore_list = 'libsocket.so:libm.so:libc.so:'
        parser.add_argument('-I', '--ignore-list', type=str, default=ignore_list,
                help='ignore list; default to "%s"' % ignore_list)
        parser.add_argument('-L', '--extra-library-path', type=str, default='',
                help='specify extra library path; default to ""')
        parser.add_argument('-E', '--use-ld-library-path', action='store_true',
                help='use LD_LIBRARY_PATH to search shared libraries')
        syslib_path = '/qnx710/target/x86_64/lib'
        parser.add_argument('-S', '--system-library-path', type=str, default=syslib_path,
                help='specify system library path; default to "%s"' % syslib_path)
        args = parser.parse_args()
        return args

    def as_string(self, source, encoding='utf-8'):
        if isinstance(source, str):
            return source
        elif str is bytes:
            return source.encode(encoding)
        else:
            return source.decode(encoding)

    def check_output(self, args):
        import subprocess
        self.log_debug(args)
        output = subprocess.check_output(args)
        return self.as_string(output)

    def load_binary(self, path):
        args = [self.args.readelf_command, '-d', path]
        output = self.check_output(args)
        binary = ElfBinaryFile()
        binary.path = path
        match = self.soname_pattern.search(output)
        if match is not None:
            binary.soname = match.group(1)
        match = self.rpath_pattern.search(output)
        if match is not None:
            binary.rpath = match.group(1)
        match = self.runpath_pattern.search(output)
        if match is not None:
            binary.runpath = match.group(1)
        binary.needed = []
        for match in self.needed_pattern.finditer(output):
            item = match.group(1)
            binary.needed.append(item)

        binary.needed = tuple(binary.needed)
        #print("output : ", output)
        #print("binary : ", binary)
        return binary

    def get_initial_library_path(self):
        import os
        path = []
        if self.args.extra_library_path:
            path += self.args.extra_library_path.split(':')
        if self.args.use_ld_library_path:
            ld_library_path = os.environ.get('LD_LIBRARY_PATH')
            if ld_library_path:
                path += ld_library_path.split(':')
        if self.args.system_library_path:
            path += self.args.system_library_path.split(':')
        path = [x.strip() for x in path]
        path = [x for x in path if x not in ('', '.', '..')]
        path = [x for x in path if os.path.isabs(x)]
        path.reverse()
        return tuple(path)

    def expand_rpath(self, binary_path, rpath_spec):
        import os
        if rpath_spec is None:
            return ()
        binary_path = os.path.abspath(binary_path)
        binary_path = os.path.dirname(binary_path)
        rpath = rpath_spec.replace('$ORIGIN', binary_path)
        rpath = rpath.split(':')
        rpath = [os.path.normpath(x) for x in rpath]
        rpath.reverse()
        return tuple(rpath)

    def extend_library_path(self, path, binary):
        path += self.expand_rpath(binary.path, binary.rpath)
        path += self.expand_rpath(binary.path, binary.runpath)
        return path

    def find_dependencies(self, path, binary):
        import os
        import sys
        # print("binary needed : ", binary.needed)
        # print("path path : ", list(reversed(path)))
        for item in binary.needed:
            for dir_path in reversed(path):
                exclude_path="/usr/local/qnx710/target/qnx7/aarch64le"
                if exclude_path in dir_path:
                    break
                file_path = os.path.join(dir_path, item)
                if os.path.isfile(file_path):
                    found = self.load_binary(file_path)
                    if file_path not in self.shared_set:
                        self.shared_set.add(file_path)
                        self.shared_list.append(file_path)
                    if found.soname:
                        if found.soname not in self.shared_dict:
                            self.shared_dict[found.soname] = [found]
                        else:
                            self.shared_dict[found.soname].append(found)
                    new_path = self.extend_library_path(path, found)
                    self.find_dependencies(new_path, found)
                    break
            else:
                message = "shared library '%s' is not found " % item
                message += "in the following directories:\n"
                message += '\n'.join(reversed(path))
                #print(message, file=sys.stderr)
                message = "shared library '%s' is not found" % item
                raise RuntimeError(message)

    def check_consistency(self):
        import sys
        for soname in self.shared_dict:
            binaries = self.shared_dict[soname]
            if len(binaries) >= 2:
                binary_set = set()
                binary_list = []
                for binary in binaries:
                    if binary.path not in binary_set:
                        binary_set.add(binary.path)
                        binary_list.append(binary.path)
                if len(binary_set) >= 3:
                    message = "shared library '%s' is loaded from different places:\n" % soname
                    message += '\n'.join(binary_list)
                    print(message, file=sys.stderr)
                    message = "shared library '%s' is loaded from different places" % soname
                    raise RuntimeError(message)

    def get_output_directory(self, binary):
        import os
        if self.args.output_directory is not None:
            return self.args.output_directory
        path = ()
        path = self.extend_library_path(path, binary)
        if path:
            return path[-1]
        binary_path = binary.path
        binary_path = os.path.abspath(binary_path)
        binary_path = os.path.dirname(binary_path)
        return binary_path

    def copy_dependencies(self, out_dir):
        import os
        import shutil
        ignore_list = self.args.ignore_list.split(':')
        #print("copy_dependencies :  ",self.shared_list)
        for item in self.shared_list:
            ignored = False
            file_name = os.path.basename(item)
            for ignore_item in ignore_list:
                if file_name.startswith(ignore_item):
                    ignored = True
                    break
            if ignored:
                args = ['ignoring', item]
                self.log_debug(args)
                continue
            file_path = os.path.join(out_dir, file_name)
            if os.path.isfile(file_path):
                args = ['skipping', item]
                self.log_debug(args)
            else:
                if not os.path.isdir(out_dir):
                    args = ['mkdir', '-p', out_dir]
                    self.log_debug(args)
                    os.makedirs(out_dir)
                args = ['copying', item]
                self.log_debug(args)
                shutil.copy(item, file_path)

    def run(self):
        self.args = self.parse_arguments()
        binary = self.load_binary(self.args.binary)
        path = self.get_initial_library_path()
        path = self.extend_library_path(path, binary)
        self.find_dependencies(path, binary)
        if not self.args.ignore_check:
            self.check_consistency()
        out_dir = self.get_output_directory(binary)
        self.copy_dependencies(out_dir)

def main():
    finder = ElfDependencyFinder()
    finder.run()

if __name__ == '__main__':
    main()

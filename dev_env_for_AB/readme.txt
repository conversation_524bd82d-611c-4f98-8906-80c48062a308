├── 8255 
│   ├── 8255_cross_compile_docker.tar #8255 docker镜像
│   └── mega_8255_output # mega 8255 可执行文件
├── CMakeLists.txt
├── compile_acc.sh
├── examples   # cyber 示例代码
│   ├── listener.cc
│   ├── proto
│   └── talker.cc
├── qnx710_docker.cmake
├── readme.txt
├── script
│   ├── copy_qnx_depends.py
│   └── set_env.sh
└── x86
    ├── mega_x86_excutable_output # mega x86 可执行文件
    └── x86_docker.tar #x86 docker镜像


-----------------------------------------------------------------------------------------
1.解压镜像8255_cross_compile_docker.tar  x86_docker.tar
2.compile_acc.sh用于编译

./compile_acc.sh  编译x86，编译结果在build目录下
./compile_acc.sh  -o 进入x86镜像

./compile_acc.sh  -q 编译8255，编译结果在build-qnx目录下
./compile_acc.sh  -qo 进入8255镜像


-----------------------------------------------------------------------------------------
x86 cyber example
./compile_acc.sh  #编译x86
./compile_acc.sh  -o #进入x86镜像
在容器内进入build目录运行./talker和./listener就可以互相通信了

x86 运行mega程序
./compile_acc.sh  -o 进入x86镜像
容器内进入mega_x86_excutable_output目录
运行./adas_launch start adas_acc.launch 

-----------------------------------------------------------------------------------------
8255 <USER> <GROUP> example
./compile_acc.sh  -q 编译8255，编译结果在build-qnx目录下
./compile_acc.sh  -qo 进入8255镜像
build-qnx目录下的output为可执行文件和需要的动态库

把build-qnx/output推到板子上运行
adb root #root权限
adb push 8255/build/output /data

adb shell #进入android
su
start mount_qlog
cp -R /data/output /qlog  

telnet cdc-qnx #在安卓下通过telnet进入qnx 
root   #telnet login
cd /log/qlog/output
运行set_env.sh里面的命令

运行talker和listener就可以互相通信了



8255运行mega程序
not ready

-----------------------------------------------------------------------------------------


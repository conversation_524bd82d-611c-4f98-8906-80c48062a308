from genericpath import exists
import tkinter as tk
import re
import os, sys, shutil
from tkinter import IntVar
import matplotlib.pyplot as plt
import json
import numpy as np

from components.info_node import InfoNode
from components.draw_line import DrawLine
from components.abstract_info import AbstractInfo
from components.detail_abstract import DetailAbstract
from components.curve_operation import CurveOperation
from components.multi_select_box import MultiSelectCombobox


class AbstractComponent():
    def __init__(self):
        pass

    def searchFile(self, in_folder, in_file_name):
        if in_file_name == '':
            print(f'InFileName is Empty')

        in_file_names = os.listdir(in_folder)
        filter = re.compile(in_file_name)
        for in_file in in_file_names:
            ret = filter.findall(in_file)
            if ret == []:
                continue
            out_file_name = in_folder + '/' + in_file
            break
        print(f'SearchFile is {out_file_name}')
        return out_file_name

    def searchKeyInfo(self, fold_name, info_nodes, obsId=None):
        for info_node in info_nodes:
            if info_node.val()['isSelect'].get() == False:
                continue
            absInfo = AbstractInfo(fold_name, info_node, obsId)
            ret = absInfo.run()
            info_node.setSaveStatus(ret)
        pass


class BasicFunc():
    # 初始化
    def __init__(self, window, info_dict, operation_cfg_area_x_end, operation_cmd_area_x_end, option_area_height):
        self.curve_operation = CurveOperation(window=window)
        self.select_component = MultiSelectCombobox(window=window)
        self.abstract_component = AbstractComponent()
        self.detail_abstract = DetailAbstract("", 0, 0, 0)

        self.window = window
        self.operation_cfg_area_x_end = operation_cfg_area_x_end
        self.operation_cmd_area_x_end = operation_cmd_area_x_end
        self.option_area_height = option_area_height

        self.info_dict = info_dict
        self.line_type = IntVar(self.window, 2)
        self.time_type = IntVar(self.window, 2)
        self.line_type.set(1)
        self.time_type.set(1)

        file = "planning_info.json"
        dirpath = os.path.dirname(os.path.realpath(__file__))
        with open(dirpath + '/../config/' + file, "r") as json_file:
            self.json_dict = json.load(json_file)

        self.Layout()

    def Layout(self):
        self.optionLayout()  # 复选框的layout
        self.operationLayout(self.option_area_height + 70)  # 按钮的layout
        self.quickKeyLayout()
        pass

    ## option layout
    def optionLayout(self):
        yoffset = 10
        xoffset = 20
        for key in self.json_dict:  # 遍历外层字典，一般一个文件中只有一个key
            info_cb = tk.Label(self.window, text=key, font=('Helvetica', 12))
            info_cb.place(x=xoffset, y=yoffset)
            yoffset = yoffset + 2
            # for option_group in self.json_dict[key]: #中间层遍历，遍历option组
            for option_item in self.json_dict[key]:  # 最内层遍历，拿到的items就是要显示的CheckBox
                yoffset = yoffset + 20  # CheckBox纵向间隔20个像素
                if (yoffset / self.option_area_height >= 1):
                    xoffset = xoffset + 180
                    yoffset = 20
                try:
                    polygon_vertex = option_item['polygon_vertex']
                except:
                    polygon_vertex = 0
                self.add_infoNode(option_item['name'], option_item['regex'], polygon_vertex, option_item['split'],
                                  xoffset, yoffset)
            yoffset = yoffset + 30

        pass

    def close_dropdown(self):
        self.dropdown.withdraw()
        pass

    def add_infoNode(self, info_name, info_regularation, polygon_vertex, split_para, x_location, y_location):
        def debugCb():
            for info_name, info_node in self.info_dict.items():
                if '#x' in info_name:
                    regex = "(.*)#x"
                    pattern = re.compile(regex)
                    main_info_name = pattern.findall(info_name)
                    if info_node.getCheckStatus():
                        self.info_dict[main_info_name[0] + "#y"].setCheckStatus(True)
                        self.info_dict[main_info_name[0] + "#x"].setCheckStatus(True)
                        break
                    else:
                        self.info_dict[main_info_name[0] + "#y"].setCheckStatus(False)
                        self.info_dict[main_info_name[0] + "#x"].setCheckStatus(False)
            pass

        regex_patch = "\s*[:=[\"]*\s*(-*\d+\.?\d*e?[-|+]?\d*)"
        infoNode = InfoNode(info_name, info_regularation + regex_patch, split_para, polygon_vertex)
        self.info_dict[info_name] = infoNode
        info_Cb = tk.Checkbutton(self.window, text=info_name, variable=self.info_dict[info_name].val()['isSelect'],
                                 command=debugCb)
        info_Cb.place(x=x_location, y=y_location)
        return info_Cb

    # operation layout    
    def operationLayout(self, y_offset: int):
        self.addInputFileName(y_offset)
        self.addAbstractOperation(self.operation_cfg_area_x_end, y_offset)
        self.addDetailOperation(y_offset)
        self.curve_operation.addCurveOperationButton(self.operation_cmd_area_x_end, y_offset)
        self.select_component.addListBox()
        pass

    def addInputFileName(self, y_offset: int):
        label = tk.Label(self.window, text='Folder')
        label.place(x=20, y=y_offset)
        self.inputfold = tk.Entry(self.window, show=None, font=('Arial', 12), width=10)
        self.inputfold.insert(0, './')
        self.inputfold.place(x=65, y=y_offset, width=65)

        label = tk.Label(self.window, text='Files')
        label.place(x=150, y=y_offset)
        self.inputfiles = tk.Entry(self.window, show=None, font=('Arial', 12), width=10)
        self.inputfiles.insert(0, 'acc_planning_server.INFO$')
        self.inputfiles.place(x=190, y=y_offset, width=70)

        label = tk.Label(self.window, text='TrjId')
        label.place(x=270, y=y_offset)
        self.inputTrjId = tk.Entry(self.window, show=None, font=('Arial', 12), width=10)
        self.inputTrjId.insert(0, '506')
        self.inputTrjId.place(x=310, y=y_offset, width=60)
        
        label = tk.Label(self.window, text='Left')
        label.place(x=380, y=y_offset)
        self.inputTrjLeft = tk.Entry(self.window, show=None, font=('Arial', 12), width=10)
        self.inputTrjLeft.insert(0, '0')
        self.inputTrjLeft.place(x=420, y=y_offset, width=40)

        label = tk.Label(self.window, text='TrjIdSta')
        label.place(x=20, y=y_offset + 35)
        self.inputTrjIdStart = tk.Entry(self.window, show=None, font=('Arial', 12), width=15)
        self.inputTrjIdStart.insert(0, '1')
        self.inputTrjIdStart.place(x=65, y=y_offset + 35, width=65)

        label = tk.Label(self.window, text='TrjIdEnd')
        label.place(x=150, y=y_offset + 35)
        self.inputTrjIdEnd = tk.Entry(self.window, show=None, font=('Arial', 12), width=15)
        self.inputTrjIdEnd.insert(0, '1')
        self.inputTrjIdEnd.place(x=190, y=y_offset + 35, width=70)

        label = tk.Label(self.window, text='ObsId')
        label.place(x=270, y=y_offset + 35)
        self.obsId = tk.Entry(self.window, show=None, font=('Arial', 12), width=15)
        self.obsId.place(x=310, y=y_offset + 35, width=60)
        
        label = tk.Label(self.window, text='Right')
        label.place(x=380, y=y_offset + 35)
        self.inputTrjRight = tk.Entry(self.window, show=None, font=('Arial', 12), width=10)
        self.inputTrjRight.insert(0, '0')
        self.inputTrjRight.place(x=420, y=y_offset + 35, width=40)        
        pass

    def addDetailOperation(self, y_offset: int):
        bt = tk.Button(self.window, text='DetailAbstract', width=13, height=1, command=self.onDetailInfoNode)
        bt.place(x=20, y=y_offset + 70)
        bt = tk.Button(self.window, text='DetailDrawYX', width=13, height=1,
                       command=self.onDetailDrawYX)
        bt.place(x=170, y=y_offset + 70)
        bt = tk.Button(self.window, text='RangeDrawYX', width=13, height=1,
                       command=self.detailRangeDraw)
        bt.place(x=330, y=y_offset + 70)
        pass

    def onDetailDrawYX(self, save=False, file_name=""):
        self.onDetailInfoNode()
        self.OnDetailDrawYX(save, file_name)
        pass

    def detailRangeDraw(self):
        start_key = int(self.inputTrjIdStart.get())
        end_key = int(self.inputTrjIdEnd.get())

        # create folder
        output_drawdir = './tmp/tmp/'
        try:
            if os.path.isdir(output_drawdir) == True:
                shutil.rmtree(output_drawdir)
            os.mkdir(output_drawdir)
        except Exception as e:
            print(f"Failed to create {output_drawdir}. Reason: {e}")
            pass

        # create img
        for i in range(start_key, end_key):
            file_name = output_drawdir + str(i) + '.jpg'
            self.inputTrjId.delete(0, tk.END)
            self.inputTrjId.insert(0, str(i))
            self.onDetailDrawYX(True, file_name)
        pass

    def addAbstractOperation(self, x_offset: int, y_offset: int):
        labelSelect2LineType = tk.Label(self.window, text='Line Type:')
        labelSelect2LineType.place(x=x_offset + 20, y=y_offset)
        radioButton = tk.Radiobutton(self.window, text='line_type', variable=self.line_type, value=1)
        radioButton.place(x=x_offset + 20, y=y_offset)
        radioButton = tk.Radiobutton(self.window, text='dot_type', variable=self.line_type, value=2)
        radioButton.place(x=x_offset + 20 + 100, y=y_offset)
        radioButton = tk.Radiobutton(self.window, text='time_type', variable=self.time_type, value=1)
        radioButton.place(x=x_offset + 20 + 200, y=y_offset)
        radioButton = tk.Radiobutton(self.window, text='stamp_type', variable=self.time_type, value=2)
        radioButton.place(x=x_offset + 20 + 300, y=y_offset)

        bt = tk.Button(self.window, text='DeleteAll', width=10, height=1, command=self.onDeleteAllData)
        bt.place(x=x_offset + 20, y=y_offset + 40)
        bt = tk.Button(self.window, text='ResetBox', width=10, height=1, command=self.onResetBox)
        bt.place(x=x_offset + 20, y=y_offset + 80)
        bt = tk.Button(self.window, text="ClosePlot", width=10, height=1, command=plt.close('all'))
        bt.place(x=x_offset + 20, y=y_offset + 120)

        bt = tk.Button(self.window, text='AbstractDrawYT', width=15, height=1,
                       command=self.onAbstractDrawYT)
        bt.place(x=x_offset + 20 + 110, y=y_offset + 40)
        bt = tk.Button(self.window, text='DrawYT', width=10, height=1, command=self.drawYTPlot)
        bt.place(x=x_offset + 20 + 110 + 150, y=y_offset + 40)
        bt = tk.Button(self.window, text='AbstractDrawYX', width=15, height=1,
                       command=self.onAbstractDrawYX)
        bt.place(x=x_offset + 20 + 110, y=y_offset + 40 + 40)
        bt = tk.Button(self.window, text='DrawYX', width=15, height=1,
                       command=self.drawYXPlot)
        bt.place(x=x_offset + 20 + 110 + 150, y=y_offset + 40 + 40)
        pass

    def onDeleteAllData(self):
        inFolder = './tmp/'
        shutil.rmtree(inFolder)
        pass

    def onResetBox(self):
        for name, info in self.info_dict.items():
            info.setCheckStatus(False)
        self.curve_operation.resetCheckBox()
        self.curve_operation.resetNo()
        pass

    def onAbstractDrawYT(self):
        self.abstractInfoNode()
        self.drawYTPlot()
        pass

    def onAbstractDrawYX(self):
        self.abstractInfoNode()
        self.drawYXPlot()
        pass

    # 快捷键
    def on_right_click(self):
        ori_key = int(self.inputTrjId.get())
        new_key = ori_key + 1
        self.inputTrjId.delete(0, tk.END)
        self.inputTrjId.insert(0, str(new_key))

    def on_left_click(self):
        ori_key = int(self.inputTrjId.get())
        new_key = ori_key - 1
        self.inputTrjId.delete(0, tk.END)
        self.inputTrjId.insert(0, str(new_key))

    def mouseWheel(self, event):
        if event.delta > 0:
            self.on_right_click()
        else:
            self.on_left_click()
        pass

    def right_draw(self):
        self.on_right_click()
        self.onDetailDrawYX()
        pass

    def left_draw(self):
        self.on_left_click()
        self.onDetailDrawYX()
        pass

    def close_with_shortcut(self, event=None):
        self.window.destroy()
        pass

    def quickKeyLayout(self):
        self.window.bind("<Right>", lambda event: self.on_right_click())
        self.window.bind("<Left>", lambda event: self.on_left_click())
        self.window.bind("<MouseWheel>", lambda event: self.mouseWheel())
        self.window.bind("<Button-4>", lambda event: self.on_right_click())
        self.window.bind("<Button-5>", lambda event: self.on_left_click())
        self.window.bind("<Alt-a>", lambda event: self.onDetailDrawYX())
        self.window.bind("<Alt-r>", lambda event: self.right_draw())
        self.window.bind("<Alt-l>", lambda event: self.left_draw())
        self.window.bind("<Alt-z>", lambda event: plt.close('all'))
        self.window.bind("<Control-q>", lambda event: self.close_with_shortcut())
        self.select_component.entry.bind("<Button-1>", self.updateInfoNodeSelections)
        self.select_component.dropdown.bind("<FocusOut>", self.select_component.close_dropdown)
        self.select_component.listbox.bind("<<ListboxSelect>>", self.select_component.update_selection)
        pass

    def updateInfoNodeSelections(self, event=None):
        options = []
        for infoNode in self.info_dict.values():
            if infoNode.val()['isSelect'].get() == True:
                options.append(infoNode.val()['fileName'])
        self.select_component.update_options(options)
        self.select_component.toggle_dropdown()
        pass

    def abstractInfoNode(self):
        fold_name = self.abstract_component.searchFile(self.inputfold.get(), self.inputfiles.get())
        self.abstract_component.searchKeyInfo(fold_name, self.info_dict.values(), self.obsId.get())
        pass

    def searchY(self, name, x, data):
        first_col = np.abs(data[:, 0] - x)
        closest_index = np.argmin(first_col)
        second_value = data[closest_index, 1]
        print(f'time: {x}, {name}: {second_value}')
        pass

    def drawYXPlot(self):
        def onDrawClick(event):
            if event.inaxes and event.button == 1:
                print(f'点击坐标: x={event.xdata:.4f}, y={event.ydata:.4f}')
                # print(f'{self.select_component.get_selected()}')
                for infoNode in self.info_dict.values():
                    if infoNode.val()['fileName'] in self.select_component.get_selected():
                        saveFileName = './tmp/' + infoNode.val()['fileName']
                        lst = np.loadtxt(saveFileName + '.csv')
                        if lst.ndim != 2 or lst.size == 0:
                            pass
                        self.searchY(infoNode.val()['fileName'], event.xdata, lst)
            pass

        lengendList = []
        fig, ax = plt.subplots()
        for infoNode in self.info_dict.values():
            if '#x' not in infoNode.val()['fileName']:
                continue
            if not exists('./tmp/' + infoNode.val()['fileName'] + '.csv'):
                continue

            drawLine = DrawLine(infoNode, 0, self.line_type.get(), self.time_type.get())
            ret = drawLine.DrawYX(ax)
            if ret:
                lengendList.append(infoNode.val()['fileName'])
        cid = fig.canvas.mpl_connect("button_press_event", onDrawClick)
        fig.legend(lengendList)
        plt.title(self.inputTrjId.get())
        plt.show()
        pass

    def drawYTPlot(self):
        def onDrawClick(event):
            if event.inaxes and event.button == 1:
                print(f'点击坐标: x={event.xdata:.4f}, y={event.ydata:.4f}')
                # print(f'{self.select_component.get_selected()}')
                for infoNode in self.info_dict.values():
                    if infoNode.val()['fileName'] in self.select_component.get_selected():
                        saveFileName = './tmp/' + infoNode.val()['fileName']
                        lst = np.loadtxt(saveFileName + '.csv')
                        if lst.ndim != 2 or lst.size == 0:
                            pass
                        self.searchY(infoNode.val()['fileName'], event.xdata, lst)
            if event.inaxes and event.button == 3:
                if ax.get_zorder() == 0:
                    ax.set_zorder(1)
                    ax_right.set_zorder(0)
                    ax.patch.set_visible(False)
                else:
                    ax.set_zorder(0)
                    ax_right.set_zorder(1)
                    ax_right.patch.set_visible(False)
            pass

        fig, ax = plt.subplots()
        ax_right = ax.twinx()
        for infoNode in self.info_dict.values():
            if not exists('./tmp/' + infoNode.val()['fileName'] + '.csv'):
                continue

            drawLine = DrawLine(infoNode, 0, self.line_type.get(), self.time_type.get())

            if infoNode.val()['fileName'] in self.select_component.get_selected():
                drawLine.DrawYT(fig, ax_right, infoNode.val()['fileName'])
            else:
                drawLine.DrawYT(fig, ax, infoNode.val()['fileName'])

        fig.canvas.mpl_connect("button_press_event", onDrawClick)
        h1, l1 = ax.get_legend_handles_labels()
        h2, l2 = ax_right.get_legend_handles_labels()
        ax.legend(h1 + h2, l1 + l2)

        plt.show()
        pass

    def onDetailInfoNode(self):
        file_name = self.abstract_component.searchFile(self.inputfold.get(), self.inputfiles.get())

        # 提取关键id的相关信息
        self.detail_abstract = DetailAbstract(file_name, self.inputTrjId.get(), self.inputTrjLeft.get(), self.inputTrjRight.get())
        self.detail_abstract.abstractLines(search_direction=0)
        self.detail_abstract.writeLineResult("./tmp.txt")

        # 提取infonode相关的csv
        for infoNode in self.info_dict.values():
            ret = self.detail_abstract.abstractKeyCsv(infoNode=infoNode)
            infoNode.setSaveStatus(ret)
        pass

    def OnDetailDrawYX(self, save=False, file_name=""):
        fig, ax = plt.subplots()
        for infoNode in self.info_dict.values():
            if '#x' not in infoNode.val()['fileName'] or infoNode.val()['isSelect'].get() == False:
                continue
            self.detail_abstract.drawKeyYXPlot(infoNode, self.line_type.get(), ax)
        fig.legend()
        plt.title(self.inputTrjId.get())

        if save == True:
            fig.savefig('{}'.format(file_name))
            plt.close()
        else:
            plt.show()
        pass

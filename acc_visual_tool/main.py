# -*- coding: utf-8 -*-
import os
# os.environ["LD_LIBRARY_PATH"] = "/opt/ros/noetic/lib/"
os.environ["CYBER_IP"] = "*************"
import sys
import glob
sys.path.append("/opt/ros/noetic/lib/python3/dist-packages")
sys.path.insert(0, "/root/secondary_build_ws/devel/lib/python3/dist-packages")
from cv_bridge import CvBridge
from genpy import Time

import copy
import time
import numpy as np
import cv2
import argparse

from cyber.python.cyber_py3 import cyber
from perception.proto import PerceptionObstacle, PerceptionObstacles
from perception.proto import RadarPcloud
from perception.proto import Image as cyberImage
from perception.proto import CompressedImage, AionChassisInfo
from perception.proto import RadarPcloud
from perception.proto import Point3D
from perception.proto import PredictionObstacles
from perception.proto import RadarDetections
from perception.proto import ObstaclesInMap
from control.proto.CarlaWheelSpeed_pb2 import WheelSpeedInfo

import rospy
from sensor_msgs.msg import Image
from sensor_msgs.msg import PointCloud2
from sensor_msgs.msg import PointField
from geometry_msgs.msg import AccelStamped
from geometry_msgs.msg import PointStamped
from geometry_msgs.msg import Point
from visualization_msgs.msg import Marker
from visualization_msgs.msg import MarkerArray

from tf2_ros import TransformBroadcaster

from geometry_msgs.msg import TransformStamped
from scipy.spatial.transform import Rotation
from mega_obstacle import draw_obstacles, draw_pathpoint, boxes3d_corners

cyber.init()
cyber_node = cyber.Node("ros_trans")
rospy.init_node("ros_trans", anonymous=True)

class msg_Trans(object):
    def __init__(self,):
        # sensor
        self.intrinsics = np.float32([[1248.0375, 0, 987.3255],
                                        [0, 1191.109, 497.659],
                                        [0, 0, 1]])

        self.radar_to_ego = np.matrix( [[-0.01749597,  0.98665179,  0.16190164, 0.10543036],
                                    [-0.99971917, -0.01985145,  0.01294249, 1.03604798],
                                    [ 0.01598371, -0.16162973,  0.98672202, 1.07081435],
                                    [0, 0, 0, 1]])

        self.wheel_speed = 0
        self.image = None
        self.timestamp = 0

    def set_cyber_reader(self,):
        # sensor
        self.image_viewer_reader = cyber_node.create_reader("/mipi_camera/cam0", CompressedImage, self.sensors_camera_callback)
        self.radar_sensor_reader = cyber_node.create_reader("sensors/radar", RadarPcloud, self.sensors_radar_callback)

        # perception
        self.obstacle_radar_reader = cyber_node.create_reader("radar/detections", RadarDetections, self.radar_obstacles_callback)
        self.obstacle_camera_reader = cyber_node.create_reader("perception/camera_detect", PerceptionObstacles, self.camera_detect_callback)
        self.fusion_reader = cyber_node.create_reader("perception/fusion_obstacles", PerceptionObstacles, self.fusion_obstacles_callback)

        # debug
        self.obstacle_camera_reader = cyber_node.create_reader("perception/camera_detect_onnx", PerceptionObstacles, self.camera_detect_onnx_callback)

        # ego vehicle
        self.velocity_reader = cyber_node.create_reader("channel/wheelspeed", WheelSpeedInfo, self.wheel_speed_callback)

        # location
        self.obstalce_in_map_reader = cyber_node.create_reader("acc/predict/obstacles_clines_in_map", ObstaclesInMap, self.obstacle_in_map_callback)

    def set_ros_publiser(self,):
        # Sensor
        self.image_viewer_publisher = rospy.Publisher("/sensor/camera/image_view", Image, queue_size=1)
        self.obstacle_viewer_publisher = rospy.Publisher("/sensor/camera/obstacle_view", Image, queue_size=1)
        self.radar_point_publisher = rospy.Publisher("/sensor/radar/radar_points", PointCloud2, queue_size=1)
        self.obstacle_viewer_onnx_publisher = rospy.Publisher("/sensor/camera/obstacle_view_onnx", Image, queue_size=1)

        # perception
        self.obstacle_rviz_publisher = rospy.Publisher("/obstacles/perception/obstacles_marker", MarkerArray, queue_size=1)
        self.radar_obstacle_rviz_publisher = rospy.Publisher("/obstacles/perception/radar_marker", MarkerArray, queue_size=1)

        self.vehicle_velocity_publisher = rospy.Publisher("/vehicle/chassis/speed", AccelStamped, queue_size=1)
        # debug lane 3d
        self.lane3d_rviz_publisher = rospy.Publisher("/obstacles/perception/lane3d_marker", MarkerArray, queue_size=1)

        #location
        self.obstacle_inmap_rviz_publisher = rospy.Publisher("/obstacles/location/obstacle_in_map", MarkerArray, queue_size=1)

    def sensors_camera_callback(self, image_msg):
        height, width = 1024, 1920
        buffer = np.frombuffer(image_msg.data, dtype=np.uint8)
        image = None
        if len(buffer) != height * width * 3:
            sensor_image = cv2.imdecode(buffer, cv2.IMREAD_COLOR)
            image = sensor_image[:, :, ::-1]
        else:
            sensor_image = buffer.reshape(height, width, 3)
            image = sensor_image[:, :, ::-1]
        self.image = image.copy()
        # image_viewer
        img_msg = Image()
        img_msg.height = image.shape[0]
        img_msg.width = image.shape[1]
        img_msg.encoding = "rgb8"
        img_msg.data = image.tobytes()
        img_msg.step = len(img_msg.data) // height
        self.image_viewer_publisher.publish(img_msg)

    def sensors_radar_callback(self, radar_msg):
        msg = PointCloud2()
        msg.header.stamp = Time.from_sec(radar_msg.header.timestamp)
        msg.header.frame_id = "map"

        origin_points = []
        save_points = []
        for radar_p in radar_msg.pts:
            # for radar visual  ego frame
            origin_points.append([radar_p.xyz_local.x, radar_p.xyz_local.y, radar_p.xyz_local.z, radar_p.doppler])

        # origin radar point cloud
        msg.height = 1
        msg.width = radar_msg.pts_len

        msg.fields = [
            PointField('x', 0, PointField.FLOAT32, 1),
            PointField('y', 4, PointField.FLOAT32, 1),
            PointField('z', 8, PointField.FLOAT32, 1),
            PointField('i', 12, PointField.FLOAT32, 1)]
        msg.is_bigendian = False
        msg.point_step = 16
        msg.row_step = msg.point_step * len(origin_points)
        msg.is_dense = False
        msg.data = np.asarray(origin_points, np.float32).tobytes()
        self.radar_point_publisher.publish(msg)

    def radar_obstacles_callback(self, obstacles_msg):
        # For 3D rviz visual
        obstacle_marker_array = MarkerArray()

        start_point = [0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3]
        end_point = [1, 2, 3, 0, 5, 6, 7, 4, 4, 5, 6, 7]
        for i in range(len(obstacles_msg.detection_list)):
            obstacle = obstacles_msg.detection_list[i]

            obstacle_marker = Marker()
            obstacle_marker.header.frame_id = "map"
            obstacle_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)
            obstacle_marker.id = i
            obstacle_marker.action = Marker.ADD
            obstacle_marker.type = Marker.CUBE
            obstacle_marker.lifetime = rospy.Duration(0.05);
            obstacle_marker.color.r = 0.5;
            obstacle_marker.color.g = 0.5;
            obstacle_marker.color.b = 0.2;
            obstacle_marker.color.a = 0.5;

            obstacle_marker.pose.position.x = obstacle.position.x
            obstacle_marker.pose.position.y = obstacle.position.y
            obstacle_marker.pose.position.z = obstacle.position.z  # 障碍物高度


            rotation = Rotation.from_euler('xyz', [0, obstacle.theta, 0]).as_quat()
            obstacle_marker.pose.orientation.x = rotation[0]
            obstacle_marker.pose.orientation.y = rotation[1]
            obstacle_marker.pose.orientation.z = rotation[2]
            obstacle_marker.pose.orientation.w = rotation[3]
            obstacle_marker.scale.x = obstacle.length  # 长度
            obstacle_marker.scale.y = obstacle.width  # 宽度
            obstacle_marker.scale.z = obstacle.height  # 高度

        self.radar_obstacle_rviz_publisher.publish(obstacle_marker_array)

    def camera_detect_callback(self, obstacles_msg, carla=False):
        # Draw 2D obstacle and lane visual
        if self.image is not None:
            image = copy.deepcopy(self.image)
            draw_obstacles(image, obstacles_msg, self.intrinsics, carla=carla)

            img_msg = Image()
            img_msg.height = image.shape[0]
            img_msg.width = image.shape[1]
            img_msg.encoding = "rgb8"
            img_msg.data = image.tobytes()
            img_msg.step = len(img_msg.data) // img_msg.height
            self.obstacle_viewer_publisher.publish(img_msg)

        if carla:
            marker_array = MarkerArray()
            colors_ = [(255,0,0), (0,255,0), (0,0,255), (125,125,125)]
            id = 0
            for i in range(4):
                ind = i - 4
                obstacle = obstacles_msg.perception_obstacle[ind]
                for j in range(len(obstacle.trajectory.trajectory_point)):
                    path_point = obstacle.trajectory.trajectory_point[j].path_point
                    bevPoint = Point(path_point.y, -1 * path_point.x,  0)
                    marker = Marker()
                    marker.header.frame_id = "map"
                    marker.id = id
                    id+=1
                    marker.type = Marker.SPHERE
                    marker.action = Marker.ADD
                    marker.pose.position = bevPoint
                    marker.pose.orientation.w = 1.0
                    marker.scale.x = 0.5
                    marker.scale.y = 0.5
                    marker.scale.z = 0.5
                    marker.color.r = colors_[i][0]
                    marker.color.g = colors_[i][1]
                    marker.color.b = colors_[i][2]
                    marker.color.a = 1.0
                    marker.lifetime = rospy.Duration(0.15)
                    marker_array.markers.append(marker)
            self.lane3d_rviz_publisher.publish(marker_array)
        else:
            # Debug 3d lane point
            IPMMatrix = np.matrix([[-7.96640332e-03, -4.77807396e-02, 4.03492746e+01],
                                [2.98899659e-03, -3.80170036e-01,  2.65744971e+02],
                                [8.93158004e-06, -1.48431553e-03,  1.00000000e+00]]);
            marker_array = MarkerArray()
            colors_ = [(255,0,0), (0,255,0), (0,0,255), (125,125,125)]
            id = 0
            for i in range(4):
                ind = i - 4
                obstacle = obstacles_msg.perception_obstacle[ind]
                for j in range(len(obstacle.trajectory.trajectory_point)):
                    path_point = obstacle.trajectory.trajectory_point[j].path_point
                    point = np.array([[path_point.x], [path_point.y], [1]])
                    ipmPoint = IPMMatrix * point
                    # y, x

                    bevPoint = Point((256 - (ipmPoint[1,0] / ipmPoint[2,0]))/4, -1*((ipmPoint[0,0] / ipmPoint[2,0]) - 32)/4,  0)
                    marker = Marker()
                    marker.header.frame_id = "map"
                    marker.id = id
                    id+=1
                    marker.type = Marker.SPHERE
                    marker.action = Marker.ADD
                    marker.pose.position = bevPoint
                    marker.pose.orientation.w = 1.0
                    marker.scale.x = 0.5
                    marker.scale.y = 0.5
                    marker.scale.z = 0.5
                    marker.color.r = colors_[i][0]
                    marker.color.g = colors_[i][1]
                    marker.color.b = colors_[i][2]
                    marker.color.a = 1.0
                    marker.lifetime = rospy.Duration(0.15)
                    marker_array.markers.append(marker)
            self.lane3d_rviz_publisher.publish(marker_array)

    def camera_detect_onnx_callback(self, obstacles_msg, carla=False):
        # Draw 2D obstacle and lane visual
        if self.image is not None:
            image = copy.deepcopy(self.image)
            draw_obstacles(image, obstacles_msg, self.intrinsics, carla=carla)

            img_msg = Image()
            img_msg.height = image.shape[0]
            img_msg.width = image.shape[1]
            img_msg.encoding = "rgb8"
            img_msg.data = image.tobytes()
            img_msg.step = len(img_msg.data) // img_msg.height
            self.obstacle_viewer_onnx_publisher.publish(img_msg)

    def fusion_obstacles_callback(self, obstacles_msg):
        # For 3D rviz visual
        obstacle_marker_array = MarkerArray()
        self.timestamp = obstacles_msg.header.timestamp

        start_point = [0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3]
        end_point = [1, 2, 3, 0, 5, 6, 7, 4, 4, 5, 6, 7]

        laneWidth = 3.5
        lr_curve = [[laneWidth * 0.5, 0, 0, 0], [laneWidth * -0.5, 0, 0, 0]]

        for i in range(len(obstacles_msg.perception_obstacle)):
            obstacle = obstacles_msg.perception_obstacle[i]

            obstacle_marker = Marker()
            obstacle_marker.header.frame_id = "map"
            obstacle_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)
            obstacle_marker.id = i
            obstacle_marker.action = Marker.ADD
            obstacle_marker.type = Marker.CUBE
            obstacle_marker.lifetime = rospy.Duration(0.05);

            if obstacle.id == obstacles_msg.cipv_info.cipv_id:
                obstacle_marker.color.r = 1;
                obstacle_marker.color.g = 0;
            elif obstacle.id in obstacles_msg.cipv_info.potential_cipv_id:
                obstacle_marker.color.r = 1;
                obstacle_marker.color.g = 1;
            else:
                obstacle_marker.color.r = 0;
                obstacle_marker.color.g = 1;
            obstacle_marker.color.b = 0;
            obstacle_marker.color.a = 0.5;

            obstacle_marker.pose.position.x = obstacle.position.x
            obstacle_marker.pose.position.y = obstacle.position.y
            obstacle_marker.pose.position.z = obstacle.position.z  # 障碍物高度
            rotation = Rotation.from_euler('xyz', [0, obstacle.theta, 0]).as_quat()
            obstacle_marker.pose.orientation.x = rotation[0]
            obstacle_marker.pose.orientation.y = rotation[1]
            obstacle_marker.pose.orientation.z = rotation[2]
            obstacle_marker.pose.orientation.w = rotation[3]
            obstacle_marker.scale.x = obstacle.length  # 长度
            obstacle_marker.scale.y = obstacle.width  # 宽度
            obstacle_marker.scale.z = obstacle.height  # 高度

            obstacle_marker_array.markers.append(obstacle_marker)

        # 3D lane fitting curve
        if True:
            all_lane_marker = [obstacles_msg.lane_marker.next_left_lane_marker,
                                obstacles_msg.lane_marker.left_lane_marker,
                                obstacles_msg.lane_marker.right_lane_marker,
                                obstacles_msg.lane_marker.next_right_lane_marker]

            for i in [1,2]:
                cure_lane = all_lane_marker[i]
                if cure_lane.quality == 0.0:
                    continue

                lane_marker = Marker()
                lane_marker.header.frame_id = "map"
                lane_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)

                lane_marker.id = len(obstacles_msg.perception_obstacle) + i
                lane_marker.action = Marker.ADD
                lane_marker.type = Marker.LINE_LIST
                lane_marker.lifetime = rospy.Duration(0.05);
                lane_marker.color.r = 1;
                lane_marker.color.g = 0;
                lane_marker.color.b = 1;
                lane_marker.color.a = 1;
                lane_marker.scale.x = 0.2;

                marker_points = []
                c0, c1, c2, c3 = cure_lane.c0_position, cure_lane.c1_heading_angle, cure_lane.c2_curvature, cure_lane.c3_curvature_derivative
                lr_curve[i-1] = [c0, c1, c2, c3]

                delta_x = (cure_lane.longitude_end - cure_lane.longitude_start) / 20
                for x_i in np.arange(cure_lane.longitude_start, cure_lane.longitude_end+delta_x, delta_x):
                    marker_point = Point()
                    marker_point.x = x_i
                    marker_point.y = c0 + c1 * x_i + c2 * (x_i ** 2) + c3 * (x_i ** 3)
                    marker_point.z = 0
                    marker_points.append(marker_point)

                for k in range(len(marker_points)-1):
                    lane_marker.points.append(marker_points[k])
                    lane_marker.points.append(marker_points[k+1])

                obstacle_marker_array.markers.append(lane_marker)

        if self.wheel_speed > 2:
            [c0, c1, c2, c3] = np.mean(lr_curve, axis=0)
            ttc_marker = Marker()
            ttc_marker.header.frame_id = "map"
            ttc_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)

            ttc_marker.id = len(obstacles_msg.perception_obstacle) + 5
            ttc_marker.action = Marker.ADD
            ttc_marker.type = Marker.LINE_LIST
            ttc_marker.lifetime = rospy.Duration(0.05);
            ttc_marker.color.r = 0;
            ttc_marker.color.g = 1;
            ttc_marker.color.b = 0;
            ttc_marker.color.a = 0.1;
            ttc_marker.scale.x = lr_curve[0][0] - lr_curve[1][0];

            marker_points = []
            ttc_length = 3.0 * self.wheel_speed
            delta_x = ttc_length / 20

            for x_i in np.arange(0, ttc_length, delta_x):
                marker_point = Point()
                marker_point.x = x_i
                marker_point.y = c0 + c1 * x_i + c2 * (x_i ** 2) + c3 * (x_i ** 3)
                marker_point.z = 0
                marker_points.append(marker_point)

            for k in range(len(marker_points)-1):
                ttc_marker.points.append(marker_points[k])
                ttc_marker.points.append(marker_points[k+1])

            obstacle_marker_array.markers.append(ttc_marker)

        self.obstacle_rviz_publisher.publish(obstacle_marker_array)

    def wheel_speed_callback(self, wheel_msg):
        self.wheel_speed = wheel_msg.wheelspeed_fl
        vehicle_velocity = AccelStamped()
        vehicle_velocity.header.stamp = Time.from_sec(time.time())
        vehicle_velocity.accel.linear.x = wheel_msg.wheelspeed_fl * 3.6
        self.vehicle_velocity_publisher.publish(vehicle_velocity)

    def obstacle_in_map_callback(self, obstacles_msg):
        duration = 0.05
        # For 3D rviz visual
        broadcaster = TransformBroadcaster()

        # 创建变换消息
        transform = TransformStamped()
        transform.header.stamp = rospy.Time.now()
        transform.header.frame_id = "map"  # 原坐标系
        transform.child_frame_id = "map1"  # 新坐标系

        # 动态更新原点位置
        transform.transform.translation.x = obstacles_msg.pose.location.position.x  # 示例：使用时间作为 x 值
        transform.transform.translation.y = obstacles_msg.pose.location.position.y
        transform.transform.translation.z = 0.0

        # 设置旋转
        rotation = Rotation.from_euler('xyz', [0, 0, 0]).as_quat()
        transform.transform.rotation.x = rotation[0]
        transform.transform.rotation.y = rotation[1]
        transform.transform.rotation.z = rotation[2]
        transform.transform.rotation.w = rotation[3]

        # 发布变换
        broadcaster.sendTransform(transform)


        start_point = [0, 1, 2, 3, 4, 5, 6, 7, 0, 1, 2, 3]
        end_point = [1, 2, 3, 0, 5, 6, 7, 4, 4, 5, 6, 7]
        # egoCar
        obstacle_marker = Marker()
        obstacle_marker.header.frame_id = "map"
        obstacle_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)
        obstacle_marker.id = 1000
        obstacle_marker.action = Marker.ADD
        obstacle_marker.type = Marker.LINE_LIST
        obstacle_marker.lifetime = rospy.Duration(duration);

        obstacle_marker.color.r = 1;
        obstacle_marker.color.g = 1;
        obstacle_marker.color.b = 1;
        obstacle_marker.color.a = 1;
        obstacle_marker.scale.x = 0.5;

        polygon_point = boxes3d_corners(np.array([[obstacles_msg.pose.location.position.x],
                                                    [obstacles_msg.pose.location.position.y],
                                                    [0]]),
                                        obstacles_msg.pose.location.theta, np.array([5.0, 1.8, 1.6]))

        marker_points = []
        for j in range(8):
            marker_point = Point()
            marker_point.x = polygon_point[j][0]
            marker_point.y = polygon_point[j][1]
            marker_point.z = polygon_point[j][2]
            marker_points.append(marker_point)

        for k in range(len(start_point)):
            obstacle_marker.points.append(marker_points[start_point[k]])
            obstacle_marker.points.append(marker_points[end_point[k]])

        # obstacle
        obstacle_marker_array = MarkerArray()
        self.timestamp = obstacles_msg.header.timestamp
        obstacle_marker_array.markers.append(obstacle_marker)

        laneWidth = 3.5
        lr_curve = [[laneWidth * 0.5, 0, 0, 0], [laneWidth * -0.5, 0, 0, 0]]

        for i in range(len(obstacles_msg.perception_obstacle)):
            obstacle = obstacles_msg.perception_obstacle[i]

            obstacle_marker = Marker()
            obstacle_marker.header.frame_id = "map"
            obstacle_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)
            obstacle_marker.id = i
            obstacle_marker.action = Marker.ADD
            obstacle_marker.type = Marker.LINE_LIST
            obstacle_marker.lifetime = rospy.Duration(duration);

            if obstacle.id == obstacles_msg.cipv_info.cipv_id:
                obstacle_marker.color.r = 1;
                obstacle_marker.color.g = 0;
            elif obstacle.id in obstacles_msg.cipv_info.potential_cipv_id:
                obstacle_marker.color.r = 1;
                obstacle_marker.color.g = 1;
            else:
                obstacle_marker.color.r = 0;
                obstacle_marker.color.g = 1;
            obstacle_marker.color.b = 0;
            obstacle_marker.color.a = 1;
            obstacle_marker.scale.x = 0.2;

            polygon_point = boxes3d_corners(np.array([[obstacle.position.x],
                                                        [obstacle.position.y],
                                                        [obstacle.position.z]]),
                                            obstacles_msg.pose.location.theta,
                                            np.array([obstacle.length,
                                                         obstacle.width,
                                                        obstacle.height]))

            marker_points = []
            for j in range(8):
                marker_point = Point()
                marker_point.x = polygon_point[j][0]
                marker_point.y = polygon_point[j][1]
                marker_point.z = polygon_point[j][2]
                marker_points.append(marker_point)

            for k in range(len(start_point)):
                obstacle_marker.points.append(marker_points[start_point[k]])
                obstacle_marker.points.append(marker_points[end_point[k]])

            obstacle_marker_array.markers.append(obstacle_marker)

            # predict
            if True:
                prediction_marker = Marker()
                prediction_marker.header.frame_id = "map"
                prediction_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)

                prediction_marker.id = obstacle.id + 256
                prediction_marker.action = Marker.ADD
                prediction_marker.type = Marker.LINE_LIST
                prediction_marker.lifetime = rospy.Duration(duration);

                if obstacle.id in obstacles_msg.cipv_info.potential_cipv_id:
                    prediction_marker.color.r = 1;
                    prediction_marker.color.g = 1;
                else:
                    prediction_marker.color.r = 1;
                    prediction_marker.color.g = 0;
                prediction_marker.color.b = 0.25;
                prediction_marker.color.a = 1;
                prediction_marker.scale.x = 0.2;

                marker_points = []
                for path_point in obstacle.trajectory.trajectory_point:
                    marker_point = Point()
                    marker_point.x = path_point.path_point.x
                    marker_point.y = path_point.path_point.y
                    marker_point.z = path_point.path_point.z
                    marker_points.append(marker_point)

                for k in range(len(marker_points)-1):
                    prediction_marker.points.append(marker_points[k])
                    prediction_marker.points.append(marker_points[k+1])

                obstacle_marker_array.markers.append(prediction_marker)

        # lane curve
        if True:
            all_lane_marker = [obstacles_msg.lane_info.lane_marker.next_left_lane_marker,
                                obstacles_msg.lane_info.lane_marker.left_lane_marker,
                                obstacles_msg.lane_info.lane_marker.right_lane_marker,
                                obstacles_msg.lane_info.lane_marker.next_right_lane_marker]

            for i in [1,2]:
                cure_lane = all_lane_marker[i]
                if cure_lane.quality == 0.0:
                    continue

                lane_marker = Marker()
                lane_marker.header.frame_id = "map"
                lane_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)

                lane_marker.id = len(obstacles_msg.perception_obstacle) + i
                lane_marker.action = Marker.ADD
                lane_marker.type = Marker.LINE_LIST
                lane_marker.lifetime = rospy.Duration(duration);
                lane_marker.color.r = 1;
                lane_marker.color.g = 0;
                lane_marker.color.b = 1;
                lane_marker.color.a = 1;
                lane_marker.scale.x = 0.2;

                marker_points = []
                c0, c1, c2, c3 = cure_lane.c0_position, cure_lane.c1_heading_angle, cure_lane.c2_curvature, cure_lane.c3_curvature_derivative
                lr_curve[i-1] = [c0, c1, c2, c3]

                delta_x = (cure_lane.longitude_end - cure_lane.longitude_start) / 20
                for x_i in np.arange(cure_lane.longitude_start, cure_lane.longitude_end+delta_x, delta_x):
                    marker_point = Point()
                    marker_point.x = x_i
                    marker_point.y = c0 + c1 * x_i + c2 * (x_i ** 2) + c3 * (x_i ** 3)
                    marker_point.z = 0
                    marker_points.append(marker_point)

                for k in range(len(marker_points)-1):
                    lane_marker.points.append(marker_points[k])
                    lane_marker.points.append(marker_points[k+1])

                obstacle_marker_array.markers.append(lane_marker)

        # lane center line curve
        if True:
            all_lane_marker = [obstacles_msg.lane_info.center_lines.left_center_line,
                                obstacles_msg.lane_info.center_lines.current_center_line,
                                obstacles_msg.lane_info.center_lines.right_center_line]

            for i in [0, 1, 2]:
                center_line = all_lane_marker[i]

                lane_marker = Marker()
                lane_marker.header.frame_id = "map"
                lane_marker.header.stamp = Time.from_sec(obstacles_msg.header.timestamp)

                lane_marker.id = len(obstacles_msg.perception_obstacle) + i + 256 * 2
                lane_marker.action = Marker.ADD
                lane_marker.type = Marker.LINE_LIST
                lane_marker.lifetime = rospy.Duration(duration);
                lane_marker.color.r = 0;
                lane_marker.color.g = 1;
                lane_marker.color.b = 1;
                lane_marker.color.a = 1;
                lane_marker.scale.x = 0.2;

                marker_points = []
                for pts in center_line.pts_center:
                    marker_point = Point()
                    marker_point.x = pts.x
                    marker_point.y = pts.y
                    marker_point.z = 0
                    marker_points.append(marker_point)

                for k in range(len(marker_points)-1):
                    lane_marker.points.append(marker_points[k])
                    lane_marker.points.append(marker_points[k+1])

                obstacle_marker_array.markers.append(lane_marker)

        self.obstacle_inmap_rviz_publisher.publish(obstacle_marker_array)



if __name__ == '__main__':
    trans = msg_Trans()
    trans.set_ros_publiser()
    trans.set_cyber_reader()
    rospy.spin()
    for file in glob.glob("cyber_py.*"):
        os.remove(file)


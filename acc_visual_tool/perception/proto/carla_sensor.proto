syntax = "proto3";
package rainbowdash.drivers;

import "control/proto/header.proto";
import "perception/proto/geometry.proto";

message Vector3D{
    float x = 1;
    float y = 2;
    float z = 3;
}

message InsGps{
  double height = 1;
  double time = 2;
}

message InsGnns{
  double height = 1;
  double week = 2;
}

message Imu{
  rainbowdash.common.Header header = 1;
  //measurement time in seconds since GPS epoch (Jan 6, 1980). 
  //see http://wiki.gis.com/wiki/index.php/Global_Positioning_System#GPS_time_and_date
  double measurement_time = 2;
  
  //0 means instantaneous metric, otherwise it means the metric is averaged during measurement span.
  float measurement_span = 3;
  
  //forward/left/up in m/s^2
  rainbowdash.common.Point3D linear_acceleration = 4;
  
  //around forward/left/up in randians/second
  rainbowdash.common.Point3D angular_velocity = 5;


  //ground truth from carla
  rainbowdash.common.Point3D location = 6;
  rainbowdash.common.Point3D rotation = 7;
  InsGps ins_gps = 8;
  InsGnns ins_gnns = 9;
  int32 seq_num = 10;
}

message Gnss{
  rainbowdash.common.Header header = 1;
  //measurement time in seconds since GPS epoch (Jan 6, 1980). 
  //see http://wiki.gis.com/wiki/index.php/Global_Positioning_System#GPS_time_and_date
  double measurement_time = 2;
  
  double longitude = 3;
  double latitude = 4;
  double altitude = 5;
}

message Bestpos{
  rainbowdash.common.Header header = 1;
  //measurement time in seconds since GPS epoch (Jan 6, 1980).
  //see http://wiki.gis.com/wiki/index.php/Global_Positioning_System#GPS_time_and_date
  double measurement_time = 2;
  uint32 solution_status = 3;
  uint32 position_type = 4;
  float latitude = 5;
  float longitude = 6;
  float altitude = 7;
  float undulation = 8;
  uint32 datum_id = 9;

  float latitude_std = 10;
  float longitude_std = 11;
  float altitude_std = 12;

  float diff_age = 13;
  float sol_age = 14;

  uint32 svs = 15;
  uint32 sol_svs = 16;
  uint32 l1_svs = 17;
  uint32 mult_svs = 18;
  uint32 reserved = 19;
  uint32 extsolstat = 20;
  uint32 gal_sig_mask = 21;
  uint32 sig_mask = 22;
}

message Image {
  rainbowdash.common.Header header = 1;
  double measurement_time = 2; //time in seconds
  string encoding = 3;  //type of encoding. currently only "RGB8" and "RGBA8" supported.
  uint32 height = 4;
  uint32 width = 5;
  uint32 depth = 6; //bytes per pixel. It's redundant to encoding.
  bytes data = 7;
  string start = 8; //start from which corner. support "topleft" and "bottomleft"
}

message radar_sph_coord{
  float r = 1;
  float el = 2;
  float az = 3;
}

message radar_xyz_coord{
  float x = 1;
  float y = 2;
  float z = 3;
}

message RadarPoint{
    float power = 1;
    float doppler = 2;
    radar_sph_coord sph_local = 3;
    radar_sph_coord sph_global = 4;
    radar_xyz_coord xyz_local = 5;
    radar_xyz_coord xyz_global = 6;
}

message RadarPcloud {
  rainbowdash.common.Header header = 1;
  double radar_time = 2;
  double qcmd_time = 3;
  repeated RadarPoint pts = 4;
  uint32 pts_len = 5;
  uint32 rid = 6;
  uint32 lost_frames = 7;
}
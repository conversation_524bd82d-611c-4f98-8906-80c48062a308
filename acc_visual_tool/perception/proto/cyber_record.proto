syntax = "proto3";
package rainbowdash.drivers;

import "control/proto/header.proto";

message CompressedImage {
    rainbowdash.common.Header header = 1;
    double measurement_time = 2;
    string format = 3; //type of encoding:jpeg, png
    bytes data = 4;
}

message AionChassisInfo{
    rainbowdash.common.Header header = 1;
    double velocity = 2;

    double lon_acceleration = 3;
    double lat_acceleration = 4;
    double yaw_rate = 5;

    bool brake_system_status = 6;
    double brake_padal_position = 7;
    
    double eps_steer_angle = 8;
    int32 eps_steer_angle_speed = 9;
    bool eps_fault_status = 10;
    int32 eps_lat_ctl_mode = 11;

    int32 vcu_current_gear_level = 12;
    int32 vcu_actual_vehicle_wheel_torque = 13;

    int32 bcm_turn_lamp_status = 14;

    double wheel_speed_front_left = 15;
    double wheel_speed_front_right = 16;
    double wheel_speed_rear_left = 17;
    double wheel_speed_rear_right = 18;
    double speed = 19;

    int32 eps_takeover_status = 20;
    int32 lon_takeover_status = 21;
    int32 exception_type = 22;
    int32 eps_angctrl_abort_feedback = 23;

}
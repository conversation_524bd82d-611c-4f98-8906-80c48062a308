# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/carla_sensor.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2
from perception.proto import geometry_pb2 as perception_dot_proto_dot_geometry__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/carla_sensor.proto',
  package='rainbowdash.drivers',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n#perception/proto/carla_sensor.proto\x12\x13rainbowdash.drivers\x1a\x1a\x63ontrol/proto/header.proto\x1a\x1fperception/proto/geometry.proto\"+\n\x08Vector3D\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\"&\n\x06InsGps\x12\x0e\n\x06height\x18\x01 \x01(\x01\x12\x0c\n\x04time\x18\x02 \x01(\x01\"\'\n\x07InsGnns\x12\x0e\n\x06height\x18\x01 \x01(\x01\x12\x0c\n\x04week\x18\x02 \x01(\x01\"\xa3\x03\n\x03Imu\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x18\n\x10measurement_time\x18\x02 \x01(\x01\x12\x18\n\x10measurement_span\x18\x03 \x01(\x02\x12\x38\n\x13linear_acceleration\x18\x04 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x35\n\x10\x61ngular_velocity\x18\x05 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12-\n\x08location\x18\x06 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12-\n\x08rotation\x18\x07 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12,\n\x07ins_gps\x18\x08 \x01(\x0b\x32\x1b.rainbowdash.drivers.InsGps\x12.\n\x08ins_gnns\x18\t \x01(\x0b\x32\x1c.rainbowdash.drivers.InsGnns\x12\x0f\n\x07seq_num\x18\n \x01(\x05\"\x83\x01\n\x04Gnss\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x18\n\x10measurement_time\x18\x02 \x01(\x01\x12\x11\n\tlongitude\x18\x03 \x01(\x01\x12\x10\n\x08latitude\x18\x04 \x01(\x01\x12\x10\n\x08\x61ltitude\x18\x05 \x01(\x01\"\xd0\x03\n\x07\x42\x65stpos\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x18\n\x10measurement_time\x18\x02 \x01(\x01\x12\x17\n\x0fsolution_status\x18\x03 \x01(\r\x12\x15\n\rposition_type\x18\x04 \x01(\r\x12\x10\n\x08latitude\x18\x05 \x01(\x02\x12\x11\n\tlongitude\x18\x06 \x01(\x02\x12\x10\n\x08\x61ltitude\x18\x07 \x01(\x02\x12\x12\n\nundulation\x18\x08 \x01(\x02\x12\x10\n\x08\x64\x61tum_id\x18\t \x01(\r\x12\x14\n\x0clatitude_std\x18\n \x01(\x02\x12\x15\n\rlongitude_std\x18\x0b \x01(\x02\x12\x14\n\x0c\x61ltitude_std\x18\x0c \x01(\x02\x12\x10\n\x08\x64iff_age\x18\r \x01(\x02\x12\x0f\n\x07sol_age\x18\x0e \x01(\x02\x12\x0b\n\x03svs\x18\x0f \x01(\r\x12\x0f\n\x07sol_svs\x18\x10 \x01(\r\x12\x0e\n\x06l1_svs\x18\x11 \x01(\r\x12\x10\n\x08mult_svs\x18\x12 \x01(\r\x12\x10\n\x08reserved\x18\x13 \x01(\r\x12\x12\n\nextsolstat\x18\x14 \x01(\r\x12\x14\n\x0cgal_sig_mask\x18\x15 \x01(\r\x12\x10\n\x08sig_mask\x18\x16 \x01(\r\"\xaa\x01\n\x05Image\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x18\n\x10measurement_time\x18\x02 \x01(\x01\x12\x10\n\x08\x65ncoding\x18\x03 \x01(\t\x12\x0e\n\x06height\x18\x04 \x01(\r\x12\r\n\x05width\x18\x05 \x01(\r\x12\r\n\x05\x64\x65pth\x18\x06 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x07 \x01(\x0c\x12\r\n\x05start\x18\x08 \x01(\t\"4\n\x0fradar_sph_coord\x12\t\n\x01r\x18\x01 \x01(\x02\x12\n\n\x02\x65l\x18\x02 \x01(\x02\x12\n\n\x02\x61z\x18\x03 \x01(\x02\"2\n\x0fradar_xyz_coord\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\"\x92\x02\n\nRadarPoint\x12\r\n\x05power\x18\x01 \x01(\x02\x12\x0f\n\x07\x64oppler\x18\x02 \x01(\x02\x12\x37\n\tsph_local\x18\x03 \x01(\x0b\x32$.rainbowdash.drivers.radar_sph_coord\x12\x38\n\nsph_global\x18\x04 \x01(\x0b\x32$.rainbowdash.drivers.radar_sph_coord\x12\x37\n\txyz_local\x18\x05 \x01(\x0b\x32$.rainbowdash.drivers.radar_xyz_coord\x12\x38\n\nxyz_global\x18\x06 \x01(\x0b\x32$.rainbowdash.drivers.radar_xyz_coord\"\xc1\x01\n\x0bRadarPcloud\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x12\n\nradar_time\x18\x02 \x01(\x01\x12\x11\n\tqcmd_time\x18\x03 \x01(\x01\x12,\n\x03pts\x18\x04 \x03(\x0b\x32\x1f.rainbowdash.drivers.RadarPoint\x12\x0f\n\x07pts_len\x18\x05 \x01(\r\x12\x0b\n\x03rid\x18\x06 \x01(\r\x12\x13\n\x0blost_frames\x18\x07 \x01(\rb\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,perception_dot_proto_dot_geometry__pb2.DESCRIPTOR,])




_VECTOR3D = _descriptor.Descriptor(
  name='Vector3D',
  full_name='rainbowdash.drivers.Vector3D',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.drivers.Vector3D.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.drivers.Vector3D.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='rainbowdash.drivers.Vector3D.z', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=164,
)


_INSGPS = _descriptor.Descriptor(
  name='InsGps',
  full_name='rainbowdash.drivers.InsGps',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.drivers.InsGps.height', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='time', full_name='rainbowdash.drivers.InsGps.time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=166,
  serialized_end=204,
)


_INSGNNS = _descriptor.Descriptor(
  name='InsGnns',
  full_name='rainbowdash.drivers.InsGnns',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.drivers.InsGnns.height', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week', full_name='rainbowdash.drivers.InsGnns.week', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=206,
  serialized_end=245,
)


_IMU = _descriptor.Descriptor(
  name='Imu',
  full_name='rainbowdash.drivers.Imu',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.Imu.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.drivers.Imu.measurement_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_span', full_name='rainbowdash.drivers.Imu.measurement_span', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='linear_acceleration', full_name='rainbowdash.drivers.Imu.linear_acceleration', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='angular_velocity', full_name='rainbowdash.drivers.Imu.angular_velocity', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='location', full_name='rainbowdash.drivers.Imu.location', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rotation', full_name='rainbowdash.drivers.Imu.rotation', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ins_gps', full_name='rainbowdash.drivers.Imu.ins_gps', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ins_gnns', full_name='rainbowdash.drivers.Imu.ins_gnns', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='seq_num', full_name='rainbowdash.drivers.Imu.seq_num', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=248,
  serialized_end=667,
)


_GNSS = _descriptor.Descriptor(
  name='Gnss',
  full_name='rainbowdash.drivers.Gnss',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.Gnss.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.drivers.Gnss.measurement_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='rainbowdash.drivers.Gnss.longitude', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='latitude', full_name='rainbowdash.drivers.Gnss.latitude', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='altitude', full_name='rainbowdash.drivers.Gnss.altitude', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=670,
  serialized_end=801,
)


_BESTPOS = _descriptor.Descriptor(
  name='Bestpos',
  full_name='rainbowdash.drivers.Bestpos',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.Bestpos.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.drivers.Bestpos.measurement_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='solution_status', full_name='rainbowdash.drivers.Bestpos.solution_status', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_type', full_name='rainbowdash.drivers.Bestpos.position_type', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='latitude', full_name='rainbowdash.drivers.Bestpos.latitude', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='rainbowdash.drivers.Bestpos.longitude', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='altitude', full_name='rainbowdash.drivers.Bestpos.altitude', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='undulation', full_name='rainbowdash.drivers.Bestpos.undulation', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='datum_id', full_name='rainbowdash.drivers.Bestpos.datum_id', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='latitude_std', full_name='rainbowdash.drivers.Bestpos.latitude_std', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longitude_std', full_name='rainbowdash.drivers.Bestpos.longitude_std', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='altitude_std', full_name='rainbowdash.drivers.Bestpos.altitude_std', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_age', full_name='rainbowdash.drivers.Bestpos.diff_age', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sol_age', full_name='rainbowdash.drivers.Bestpos.sol_age', index=13,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='svs', full_name='rainbowdash.drivers.Bestpos.svs', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sol_svs', full_name='rainbowdash.drivers.Bestpos.sol_svs', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='l1_svs', full_name='rainbowdash.drivers.Bestpos.l1_svs', index=16,
      number=17, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mult_svs', full_name='rainbowdash.drivers.Bestpos.mult_svs', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reserved', full_name='rainbowdash.drivers.Bestpos.reserved', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extsolstat', full_name='rainbowdash.drivers.Bestpos.extsolstat', index=19,
      number=20, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gal_sig_mask', full_name='rainbowdash.drivers.Bestpos.gal_sig_mask', index=20,
      number=21, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sig_mask', full_name='rainbowdash.drivers.Bestpos.sig_mask', index=21,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=804,
  serialized_end=1268,
)


_IMAGE = _descriptor.Descriptor(
  name='Image',
  full_name='rainbowdash.drivers.Image',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.Image.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.drivers.Image.measurement_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encoding', full_name='rainbowdash.drivers.Image.encoding', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.drivers.Image.height', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='rainbowdash.drivers.Image.width', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='depth', full_name='rainbowdash.drivers.Image.depth', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='rainbowdash.drivers.Image.data', index=6,
      number=7, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='rainbowdash.drivers.Image.start', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1271,
  serialized_end=1441,
)


_RADAR_SPH_COORD = _descriptor.Descriptor(
  name='radar_sph_coord',
  full_name='rainbowdash.drivers.radar_sph_coord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='r', full_name='rainbowdash.drivers.radar_sph_coord.r', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='el', full_name='rainbowdash.drivers.radar_sph_coord.el', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='az', full_name='rainbowdash.drivers.radar_sph_coord.az', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1443,
  serialized_end=1495,
)


_RADAR_XYZ_COORD = _descriptor.Descriptor(
  name='radar_xyz_coord',
  full_name='rainbowdash.drivers.radar_xyz_coord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.drivers.radar_xyz_coord.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.drivers.radar_xyz_coord.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='rainbowdash.drivers.radar_xyz_coord.z', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1497,
  serialized_end=1547,
)


_RADARPOINT = _descriptor.Descriptor(
  name='RadarPoint',
  full_name='rainbowdash.drivers.RadarPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='power', full_name='rainbowdash.drivers.RadarPoint.power', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doppler', full_name='rainbowdash.drivers.RadarPoint.doppler', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sph_local', full_name='rainbowdash.drivers.RadarPoint.sph_local', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sph_global', full_name='rainbowdash.drivers.RadarPoint.sph_global', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='xyz_local', full_name='rainbowdash.drivers.RadarPoint.xyz_local', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='xyz_global', full_name='rainbowdash.drivers.RadarPoint.xyz_global', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1550,
  serialized_end=1824,
)


_RADARPCLOUD = _descriptor.Descriptor(
  name='RadarPcloud',
  full_name='rainbowdash.drivers.RadarPcloud',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.RadarPcloud.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='radar_time', full_name='rainbowdash.drivers.RadarPcloud.radar_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qcmd_time', full_name='rainbowdash.drivers.RadarPcloud.qcmd_time', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pts', full_name='rainbowdash.drivers.RadarPcloud.pts', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pts_len', full_name='rainbowdash.drivers.RadarPcloud.pts_len', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rid', full_name='rainbowdash.drivers.RadarPcloud.rid', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lost_frames', full_name='rainbowdash.drivers.RadarPcloud.lost_frames', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1827,
  serialized_end=2020,
)

_IMU.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_IMU.fields_by_name['linear_acceleration'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_IMU.fields_by_name['angular_velocity'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_IMU.fields_by_name['location'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_IMU.fields_by_name['rotation'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_IMU.fields_by_name['ins_gps'].message_type = _INSGPS
_IMU.fields_by_name['ins_gnns'].message_type = _INSGNNS
_GNSS.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_BESTPOS.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_IMAGE.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_RADARPOINT.fields_by_name['sph_local'].message_type = _RADAR_SPH_COORD
_RADARPOINT.fields_by_name['sph_global'].message_type = _RADAR_SPH_COORD
_RADARPOINT.fields_by_name['xyz_local'].message_type = _RADAR_XYZ_COORD
_RADARPOINT.fields_by_name['xyz_global'].message_type = _RADAR_XYZ_COORD
_RADARPCLOUD.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_RADARPCLOUD.fields_by_name['pts'].message_type = _RADARPOINT
DESCRIPTOR.message_types_by_name['Vector3D'] = _VECTOR3D
DESCRIPTOR.message_types_by_name['InsGps'] = _INSGPS
DESCRIPTOR.message_types_by_name['InsGnns'] = _INSGNNS
DESCRIPTOR.message_types_by_name['Imu'] = _IMU
DESCRIPTOR.message_types_by_name['Gnss'] = _GNSS
DESCRIPTOR.message_types_by_name['Bestpos'] = _BESTPOS
DESCRIPTOR.message_types_by_name['Image'] = _IMAGE
DESCRIPTOR.message_types_by_name['radar_sph_coord'] = _RADAR_SPH_COORD
DESCRIPTOR.message_types_by_name['radar_xyz_coord'] = _RADAR_XYZ_COORD
DESCRIPTOR.message_types_by_name['RadarPoint'] = _RADARPOINT
DESCRIPTOR.message_types_by_name['RadarPcloud'] = _RADARPCLOUD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Vector3D = _reflection.GeneratedProtocolMessageType('Vector3D', (_message.Message,), dict(
  DESCRIPTOR = _VECTOR3D,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.Vector3D)
  ))
_sym_db.RegisterMessage(Vector3D)

InsGps = _reflection.GeneratedProtocolMessageType('InsGps', (_message.Message,), dict(
  DESCRIPTOR = _INSGPS,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.InsGps)
  ))
_sym_db.RegisterMessage(InsGps)

InsGnns = _reflection.GeneratedProtocolMessageType('InsGnns', (_message.Message,), dict(
  DESCRIPTOR = _INSGNNS,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.InsGnns)
  ))
_sym_db.RegisterMessage(InsGnns)

Imu = _reflection.GeneratedProtocolMessageType('Imu', (_message.Message,), dict(
  DESCRIPTOR = _IMU,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.Imu)
  ))
_sym_db.RegisterMessage(Imu)

Gnss = _reflection.GeneratedProtocolMessageType('Gnss', (_message.Message,), dict(
  DESCRIPTOR = _GNSS,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.Gnss)
  ))
_sym_db.RegisterMessage(Gnss)

Bestpos = _reflection.GeneratedProtocolMessageType('Bestpos', (_message.Message,), dict(
  DESCRIPTOR = _BESTPOS,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.Bestpos)
  ))
_sym_db.RegisterMessage(Bestpos)

Image = _reflection.GeneratedProtocolMessageType('Image', (_message.Message,), dict(
  DESCRIPTOR = _IMAGE,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.Image)
  ))
_sym_db.RegisterMessage(Image)

radar_sph_coord = _reflection.GeneratedProtocolMessageType('radar_sph_coord', (_message.Message,), dict(
  DESCRIPTOR = _RADAR_SPH_COORD,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.radar_sph_coord)
  ))
_sym_db.RegisterMessage(radar_sph_coord)

radar_xyz_coord = _reflection.GeneratedProtocolMessageType('radar_xyz_coord', (_message.Message,), dict(
  DESCRIPTOR = _RADAR_XYZ_COORD,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.radar_xyz_coord)
  ))
_sym_db.RegisterMessage(radar_xyz_coord)

RadarPoint = _reflection.GeneratedProtocolMessageType('RadarPoint', (_message.Message,), dict(
  DESCRIPTOR = _RADARPOINT,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.RadarPoint)
  ))
_sym_db.RegisterMessage(RadarPoint)

RadarPcloud = _reflection.GeneratedProtocolMessageType('RadarPcloud', (_message.Message,), dict(
  DESCRIPTOR = _RADARPCLOUD,
  __module__ = 'perception.proto.carla_sensor_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.RadarPcloud)
  ))
_sym_db.RegisterMessage(RadarPcloud)


# @@protoc_insertion_point(module_scope)

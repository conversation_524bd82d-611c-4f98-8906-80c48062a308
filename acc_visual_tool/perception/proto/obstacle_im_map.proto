
syntax = "proto3";

package rainbowdash.location;

import "control/proto/header.proto";
import "perception/proto/perception_obstacle.proto";
import "perception/proto/location.proto";
import "perception/proto/geometry.proto";

message CenterLine {
  repeated rainbowdash.common.Point3D pts_center = 7;    // 地图坐标系下的当前车道线中心线点集
  repeated double headings_center = 8;    // 地图坐标系下的当前车道线中心线朝向角
}

message CenterLines {
  CenterLine left_center_line = 1;
  CenterLine current_center_line = 2;
  CenterLine right_center_line = 3;
}

message LaneLineInfo {
  rainbowdash.drivers.LaneMarkers lane_marker = 1;
  CenterLines center_lines = 2;
}

message ObstaclesInMap {
  repeated rainbowdash.drivers.PerceptionObstacle perception_obstacle = 1;  // An array of obstacles
  rainbowdash.common.Header header = 2;        // Header
  // rainbowdash.common.ErrorCode error_code = 3 [default = OK];
  LaneLineInfo lane_info = 4;
  rainbowdash.drivers.CIPVInfo cipv_info = 5;  // Closest In Path Vehicle (CIPV)
  SlamFrame pose = 6;
}
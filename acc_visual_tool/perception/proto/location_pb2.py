# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/location.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2
from perception.proto import geometry_pb2 as perception_dot_proto_dot_geometry__pb2
from perception.proto import pose_pb2 as perception_dot_proto_dot_pose__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/location.proto',
  package='rainbowdash.location',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1fperception/proto/location.proto\x12\x14rainbowdash.location\x1a\x1a\x63ontrol/proto/header.proto\x1a\x1fperception/proto/geometry.proto\x1a\x1bperception/proto/pose.proto\"w\n\x0b\x42\x45VLocation\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12-\n\x08position\x18\x02 \x01(\x0b\x32\x1b.rainbowdash.common.Point2D\x12\r\n\x05theta\x18\x03 \x01(\x01\"\xbd\x02\n\x0bUncertainty\x12\x35\n\x10position_std_dev\x18\x01 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x38\n\x13orientation_std_dev\x18\x02 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12<\n\x17linear_velocity_std_dev\x18\x03 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12@\n\x1blinear_acceleration_std_dev\x18\x04 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12=\n\x18\x61ngular_velocity_std_dev\x18\x05 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\"\xbe\x01\n\x14LocalizationEstimate\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12(\n\x04pose\x18\x02 \x01(\x0b\x32\x1a.rainbowdash.location.Pose\x12\x36\n\x0buncertainty\x18\x03 \x01(\x0b\x32!.rainbowdash.location.Uncertainty\x12\x18\n\x10measurement_time\x18\x04 \x01(\x01\"\xa7\x02\n\x12LocalizationStatus\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x39\n\rfusion_status\x18\x02 \x01(\x0e\x32\".rainbowdash.location.MeasureState\x12;\n\x0bgnss_status\x18\x03 \x01(\x0e\x32\".rainbowdash.location.MeasureStateB\x02\x18\x01\x12<\n\x0clidar_status\x18\x04 \x01(\x0e\x32\".rainbowdash.location.MeasureStateB\x02\x18\x01\x12\x18\n\x10measurement_time\x18\x05 \x01(\x01\x12\x15\n\rstate_message\x18\x06 \x01(\t\"\xd4\x02\n\tSlamFrame\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x33\n\x08location\x18\x02 \x01(\x0b\x32!.rainbowdash.location.BEVLocation\x12-\n\x08velocity\x18\x03 \x01(\x0b\x32\x1b.rainbowdash.common.Point2D\x12\x30\n\naccelerate\x18\x04 \x01(\x0b\x32\x1c.rainbowdash.common.PointLLH\x12\x10\n\x08yaw_rate\x18\x05 \x01(\x01\x12\x37\n\x0cori_location\x18\x06 \x01(\x0b\x32!.rainbowdash.location.BEVLocation\x12:\n\x0freplan_location\x18\x07 \x01(\x0b\x32!.rainbowdash.location.BEVLocation\"\x87\x01\n\x0bLocationErr\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x18\n\x10measurement_time\x18\x02 \x01(\x01\x12\x0f\n\x07lat_err\x18\x03 \x01(\x01\x12\x10\n\x08long_err\x18\x04 \x01(\x01\x12\x0f\n\x07yaw_err\x18\x05 \x01(\x01*T\n\x0cMeasureState\x12\x06\n\x02OK\x10\x00\x12\x0c\n\x08WARNNING\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x12\n\x0e\x43RITICAL_ERROR\x10\x03\x12\x0f\n\x0b\x46\x41TAL_ERROR\x10\x04\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,perception_dot_proto_dot_geometry__pb2.DESCRIPTOR,perception_dot_proto_dot_pose__pb2.DESCRIPTOR,])

_MEASURESTATE = _descriptor.EnumDescriptor(
  name='MeasureState',
  full_name='rainbowdash.location.MeasureState',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OK', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WARNNING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ERROR', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CRITICAL_ERROR', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FATAL_ERROR', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1560,
  serialized_end=1644,
)
_sym_db.RegisterEnumDescriptor(_MEASURESTATE)

MeasureState = enum_type_wrapper.EnumTypeWrapper(_MEASURESTATE)
OK = 0
WARNNING = 1
ERROR = 2
CRITICAL_ERROR = 3
FATAL_ERROR = 4



_BEVLOCATION = _descriptor.Descriptor(
  name='BEVLocation',
  full_name='rainbowdash.location.BEVLocation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.BEVLocation.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position', full_name='rainbowdash.location.BEVLocation.position', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta', full_name='rainbowdash.location.BEVLocation.theta', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=147,
  serialized_end=266,
)


_UNCERTAINTY = _descriptor.Descriptor(
  name='Uncertainty',
  full_name='rainbowdash.location.Uncertainty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_std_dev', full_name='rainbowdash.location.Uncertainty.position_std_dev', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orientation_std_dev', full_name='rainbowdash.location.Uncertainty.orientation_std_dev', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='linear_velocity_std_dev', full_name='rainbowdash.location.Uncertainty.linear_velocity_std_dev', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='linear_acceleration_std_dev', full_name='rainbowdash.location.Uncertainty.linear_acceleration_std_dev', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='angular_velocity_std_dev', full_name='rainbowdash.location.Uncertainty.angular_velocity_std_dev', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=269,
  serialized_end=586,
)


_LOCALIZATIONESTIMATE = _descriptor.Descriptor(
  name='LocalizationEstimate',
  full_name='rainbowdash.location.LocalizationEstimate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.LocalizationEstimate.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pose', full_name='rainbowdash.location.LocalizationEstimate.pose', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uncertainty', full_name='rainbowdash.location.LocalizationEstimate.uncertainty', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.location.LocalizationEstimate.measurement_time', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=589,
  serialized_end=779,
)


_LOCALIZATIONSTATUS = _descriptor.Descriptor(
  name='LocalizationStatus',
  full_name='rainbowdash.location.LocalizationStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.LocalizationStatus.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fusion_status', full_name='rainbowdash.location.LocalizationStatus.fusion_status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gnss_status', full_name='rainbowdash.location.LocalizationStatus.gnss_status', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lidar_status', full_name='rainbowdash.location.LocalizationStatus.lidar_status', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=_b('\030\001'), file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.location.LocalizationStatus.measurement_time', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state_message', full_name='rainbowdash.location.LocalizationStatus.state_message', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=782,
  serialized_end=1077,
)


_SLAMFRAME = _descriptor.Descriptor(
  name='SlamFrame',
  full_name='rainbowdash.location.SlamFrame',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.SlamFrame.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='location', full_name='rainbowdash.location.SlamFrame.location', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='rainbowdash.location.SlamFrame.velocity', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accelerate', full_name='rainbowdash.location.SlamFrame.accelerate', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yaw_rate', full_name='rainbowdash.location.SlamFrame.yaw_rate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ori_location', full_name='rainbowdash.location.SlamFrame.ori_location', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='replan_location', full_name='rainbowdash.location.SlamFrame.replan_location', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1080,
  serialized_end=1420,
)


_LOCATIONERR = _descriptor.Descriptor(
  name='LocationErr',
  full_name='rainbowdash.location.LocationErr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.LocationErr.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.location.LocationErr.measurement_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lat_err', full_name='rainbowdash.location.LocationErr.lat_err', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='long_err', full_name='rainbowdash.location.LocationErr.long_err', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yaw_err', full_name='rainbowdash.location.LocationErr.yaw_err', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1423,
  serialized_end=1558,
)

_BEVLOCATION.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_BEVLOCATION.fields_by_name['position'].message_type = perception_dot_proto_dot_geometry__pb2._POINT2D
_UNCERTAINTY.fields_by_name['position_std_dev'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_UNCERTAINTY.fields_by_name['orientation_std_dev'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_UNCERTAINTY.fields_by_name['linear_velocity_std_dev'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_UNCERTAINTY.fields_by_name['linear_acceleration_std_dev'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_UNCERTAINTY.fields_by_name['angular_velocity_std_dev'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_LOCALIZATIONESTIMATE.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_LOCALIZATIONESTIMATE.fields_by_name['pose'].message_type = perception_dot_proto_dot_pose__pb2._POSE
_LOCALIZATIONESTIMATE.fields_by_name['uncertainty'].message_type = _UNCERTAINTY
_LOCALIZATIONSTATUS.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_LOCALIZATIONSTATUS.fields_by_name['fusion_status'].enum_type = _MEASURESTATE
_LOCALIZATIONSTATUS.fields_by_name['gnss_status'].enum_type = _MEASURESTATE
_LOCALIZATIONSTATUS.fields_by_name['lidar_status'].enum_type = _MEASURESTATE
_SLAMFRAME.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_SLAMFRAME.fields_by_name['location'].message_type = _BEVLOCATION
_SLAMFRAME.fields_by_name['velocity'].message_type = perception_dot_proto_dot_geometry__pb2._POINT2D
_SLAMFRAME.fields_by_name['accelerate'].message_type = perception_dot_proto_dot_geometry__pb2._POINTLLH
_SLAMFRAME.fields_by_name['ori_location'].message_type = _BEVLOCATION
_SLAMFRAME.fields_by_name['replan_location'].message_type = _BEVLOCATION
_LOCATIONERR.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
DESCRIPTOR.message_types_by_name['BEVLocation'] = _BEVLOCATION
DESCRIPTOR.message_types_by_name['Uncertainty'] = _UNCERTAINTY
DESCRIPTOR.message_types_by_name['LocalizationEstimate'] = _LOCALIZATIONESTIMATE
DESCRIPTOR.message_types_by_name['LocalizationStatus'] = _LOCALIZATIONSTATUS
DESCRIPTOR.message_types_by_name['SlamFrame'] = _SLAMFRAME
DESCRIPTOR.message_types_by_name['LocationErr'] = _LOCATIONERR
DESCRIPTOR.enum_types_by_name['MeasureState'] = _MEASURESTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BEVLocation = _reflection.GeneratedProtocolMessageType('BEVLocation', (_message.Message,), dict(
  DESCRIPTOR = _BEVLOCATION,
  __module__ = 'perception.proto.location_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.BEVLocation)
  ))
_sym_db.RegisterMessage(BEVLocation)

Uncertainty = _reflection.GeneratedProtocolMessageType('Uncertainty', (_message.Message,), dict(
  DESCRIPTOR = _UNCERTAINTY,
  __module__ = 'perception.proto.location_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.Uncertainty)
  ))
_sym_db.RegisterMessage(Uncertainty)

LocalizationEstimate = _reflection.GeneratedProtocolMessageType('LocalizationEstimate', (_message.Message,), dict(
  DESCRIPTOR = _LOCALIZATIONESTIMATE,
  __module__ = 'perception.proto.location_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.LocalizationEstimate)
  ))
_sym_db.RegisterMessage(LocalizationEstimate)

LocalizationStatus = _reflection.GeneratedProtocolMessageType('LocalizationStatus', (_message.Message,), dict(
  DESCRIPTOR = _LOCALIZATIONSTATUS,
  __module__ = 'perception.proto.location_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.LocalizationStatus)
  ))
_sym_db.RegisterMessage(LocalizationStatus)

SlamFrame = _reflection.GeneratedProtocolMessageType('SlamFrame', (_message.Message,), dict(
  DESCRIPTOR = _SLAMFRAME,
  __module__ = 'perception.proto.location_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.SlamFrame)
  ))
_sym_db.RegisterMessage(SlamFrame)

LocationErr = _reflection.GeneratedProtocolMessageType('LocationErr', (_message.Message,), dict(
  DESCRIPTOR = _LOCATIONERR,
  __module__ = 'perception.proto.location_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.LocationErr)
  ))
_sym_db.RegisterMessage(LocationErr)


_LOCALIZATIONSTATUS.fields_by_name['gnss_status']._options = None
_LOCALIZATIONSTATUS.fields_by_name['lidar_status']._options = None
# @@protoc_insertion_point(module_scope)

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/cyber_record.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/cyber_record.proto',
  package='rainbowdash.drivers',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n#perception/proto/cyber_record.proto\x12\x13rainbowdash.drivers\x1a\x1a\x63ontrol/proto/header.proto\"u\n\x0f\x43ompressedImage\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x18\n\x10measurement_time\x18\x02 \x01(\x01\x12\x0e\n\x06\x66ormat\x18\x03 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\x0c\"\xa8\x05\n\x0f\x41ionChassisInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x10\n\x08velocity\x18\x02 \x01(\x01\x12\x18\n\x10lon_acceleration\x18\x03 \x01(\x01\x12\x18\n\x10lat_acceleration\x18\x04 \x01(\x01\x12\x10\n\x08yaw_rate\x18\x05 \x01(\x01\x12\x1b\n\x13\x62rake_system_status\x18\x06 \x01(\x08\x12\x1c\n\x14\x62rake_padal_position\x18\x07 \x01(\x01\x12\x17\n\x0f\x65ps_steer_angle\x18\x08 \x01(\x01\x12\x1d\n\x15\x65ps_steer_angle_speed\x18\t \x01(\x05\x12\x18\n\x10\x65ps_fault_status\x18\n \x01(\x08\x12\x18\n\x10\x65ps_lat_ctl_mode\x18\x0b \x01(\x05\x12\x1e\n\x16vcu_current_gear_level\x18\x0c \x01(\x05\x12\'\n\x1fvcu_actual_vehicle_wheel_torque\x18\r \x01(\x05\x12\x1c\n\x14\x62\x63m_turn_lamp_status\x18\x0e \x01(\x05\x12\x1e\n\x16wheel_speed_front_left\x18\x0f \x01(\x01\x12\x1f\n\x17wheel_speed_front_right\x18\x10 \x01(\x01\x12\x1d\n\x15wheel_speed_rear_left\x18\x11 \x01(\x01\x12\x1e\n\x16wheel_speed_rear_right\x18\x12 \x01(\x01\x12\r\n\x05speed\x18\x13 \x01(\x01\x12\x1b\n\x13\x65ps_takeover_status\x18\x14 \x01(\x05\x12\x1b\n\x13lon_takeover_status\x18\x15 \x01(\x05\x12\x16\n\x0e\x65xception_type\x18\x16 \x01(\x05\x12\"\n\x1a\x65ps_angctrl_abort_feedback\x18\x17 \x01(\x05\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])




_COMPRESSEDIMAGE = _descriptor.Descriptor(
  name='CompressedImage',
  full_name='rainbowdash.drivers.CompressedImage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.CompressedImage.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.drivers.CompressedImage.measurement_time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='format', full_name='rainbowdash.drivers.CompressedImage.format', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='rainbowdash.drivers.CompressedImage.data', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=88,
  serialized_end=205,
)


_AIONCHASSISINFO = _descriptor.Descriptor(
  name='AionChassisInfo',
  full_name='rainbowdash.drivers.AionChassisInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.AionChassisInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='rainbowdash.drivers.AionChassisInfo.velocity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lon_acceleration', full_name='rainbowdash.drivers.AionChassisInfo.lon_acceleration', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lat_acceleration', full_name='rainbowdash.drivers.AionChassisInfo.lat_acceleration', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yaw_rate', full_name='rainbowdash.drivers.AionChassisInfo.yaw_rate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='brake_system_status', full_name='rainbowdash.drivers.AionChassisInfo.brake_system_status', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='brake_padal_position', full_name='rainbowdash.drivers.AionChassisInfo.brake_padal_position', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eps_steer_angle', full_name='rainbowdash.drivers.AionChassisInfo.eps_steer_angle', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eps_steer_angle_speed', full_name='rainbowdash.drivers.AionChassisInfo.eps_steer_angle_speed', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eps_fault_status', full_name='rainbowdash.drivers.AionChassisInfo.eps_fault_status', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eps_lat_ctl_mode', full_name='rainbowdash.drivers.AionChassisInfo.eps_lat_ctl_mode', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vcu_current_gear_level', full_name='rainbowdash.drivers.AionChassisInfo.vcu_current_gear_level', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vcu_actual_vehicle_wheel_torque', full_name='rainbowdash.drivers.AionChassisInfo.vcu_actual_vehicle_wheel_torque', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bcm_turn_lamp_status', full_name='rainbowdash.drivers.AionChassisInfo.bcm_turn_lamp_status', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wheel_speed_front_left', full_name='rainbowdash.drivers.AionChassisInfo.wheel_speed_front_left', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wheel_speed_front_right', full_name='rainbowdash.drivers.AionChassisInfo.wheel_speed_front_right', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wheel_speed_rear_left', full_name='rainbowdash.drivers.AionChassisInfo.wheel_speed_rear_left', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wheel_speed_rear_right', full_name='rainbowdash.drivers.AionChassisInfo.wheel_speed_rear_right', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='speed', full_name='rainbowdash.drivers.AionChassisInfo.speed', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eps_takeover_status', full_name='rainbowdash.drivers.AionChassisInfo.eps_takeover_status', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lon_takeover_status', full_name='rainbowdash.drivers.AionChassisInfo.lon_takeover_status', index=20,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exception_type', full_name='rainbowdash.drivers.AionChassisInfo.exception_type', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eps_angctrl_abort_feedback', full_name='rainbowdash.drivers.AionChassisInfo.eps_angctrl_abort_feedback', index=22,
      number=23, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=888,
)

_COMPRESSEDIMAGE.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_AIONCHASSISINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
DESCRIPTOR.message_types_by_name['CompressedImage'] = _COMPRESSEDIMAGE
DESCRIPTOR.message_types_by_name['AionChassisInfo'] = _AIONCHASSISINFO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CompressedImage = _reflection.GeneratedProtocolMessageType('CompressedImage', (_message.Message,), dict(
  DESCRIPTOR = _COMPRESSEDIMAGE,
  __module__ = 'perception.proto.cyber_record_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.CompressedImage)
  ))
_sym_db.RegisterMessage(CompressedImage)

AionChassisInfo = _reflection.GeneratedProtocolMessageType('AionChassisInfo', (_message.Message,), dict(
  DESCRIPTOR = _AIONCHASSISINFO,
  __module__ = 'perception.proto.cyber_record_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.AionChassisInfo)
  ))
_sym_db.RegisterMessage(AionChassisInfo)


# @@protoc_insertion_point(module_scope)

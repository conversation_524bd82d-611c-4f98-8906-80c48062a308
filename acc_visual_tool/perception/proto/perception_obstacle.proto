syntax = "proto3";
package rainbowdash.drivers;

import "control/proto/header.proto";
import "perception/proto/geometry.proto";
import "control/proto/pnc_point.proto";

message BBox2D {
  double xmin = 1;  // in pixels.
  double ymin = 2;  // in pixels.
  double xmax = 3;  // in pixels.
  double ymax = 4;  // in pixels.
}

message LightStatus {
  double brake_visible = 1;
  double brake_switch_on = 2;
  double left_turn_visible = 3;
  double left_turn_switch_on = 4;
  double right_turn_visible = 5;
  double right_turn_switch_on = 6;
}


message RadarObstacle {
  enum Status {
    NO_TARGET = 0;
    NEW_TARGET = 1;
    NEW_UPDATED_TARGET = 2;
    UPDATED_TARGET = 3;
    COASTED_TARGET = 4;
    MERGED_TARGET = 5;
    INVALID_COASTED_TARGET = 6;
    NEW_COASTED_TARGET = 7;
  }

  enum MovingStatus {
    STATIONARY = 0;
    NEARING = 1;
    AWAYING = 2;
    NONE = 3;
  }

  // obstacle ID.
  int32 id = 1;
  // obstacle position in the sl coordinate system.
  rainbowdash.common.Point3D relative_position = 2;
  // obstacle relative velocity.
  rainbowdash.common.Point2D relative_velocity = 3;
  // radar signal intensity.
  double rcs = 4;
  // whether this obstacle is able to move.
  MovingStatus moving_status = 5;
  double width = 6;
  double length = 7;
  double height = 8;
  double theta = 9;
  // obstacle position in map coordinate system
  rainbowdash.common.Point2D absolute_position = 10;
  // obstacle position in map coordinate system
  rainbowdash.common.Point2D absolute_velocity = 11;
  int32 count = 12;
  int32 moving_frames_count = 13;
  Status status = 14;
}

message RadarObstacles {
  map<int32, RadarObstacle> radar_obstacle = 1;  // An array of obstacles
  rainbowdash.common.Header header = 2;      // Header
  // apollo.common.ErrorCode error_code = 3 [default = OK];
}

message V2XInformation {
  enum V2XType {
    NONE = 0;
    ZOMBIES_CAR = 1;    // while(!apollo::cyber::IsShutdown()){
      //     std::cout << "callback.." << std::endl;
      //     rate.Sleep();
      // }
    BLIND_ZONE = 2;
  };
  V2XType v2x_type = 1;
}

message SensorMeasurement {
  string sensor_id = 1;
  int32 id = 2;

  rainbowdash.common.Point3D position = 3;
  double theta = 4;
  double length = 5;
  double width = 6;
  double height = 7;

  rainbowdash.common.Point3D velocity = 8;

  PerceptionObstacle.Type type = 9;
  PerceptionObstacle.SubType sub_type = 10;
  double timestamp = 11;
  BBox2D box = 12;  // only for camera measurements
}

message PerceptionObstacle {
  int32 id = 1;  // obstacle ID.

  // obstacle position in the world coordinate system.
  rainbowdash.common.Point3D position = 2;

  double theta = 3;  // heading in the world coordinate system.
  rainbowdash.common.Point3D velocity = 4;  // obstacle velocity.

  // Size of obstacle bounding box.
  double length = 5;  // obstacle length.
  double width = 6;   // obstacle width.
  double height = 7;  // obstacle height.

  repeated rainbowdash.common.Point3D polygon_point = 8;  // obstacle corner points.

  // duration of an obstacle since detection in s.
  double tracking_time = 9;

  enum Type {
    UNKNOWN = 0;
    UNKNOWN_MOVABLE = 1;
    UNKNOWN_UNMOVABLE = 2;
    PEDESTRIAN = 3;  // Pedestrian, usually determined by moving behavior.
    BICYCLE = 4;     // bike, motor bike
    VEHICLE = 5;     // Passenger car or truck.
  };
  Type type = 10;         // obstacle type
  double timestamp = 11;  // GPS time in seconds.

  // Just for offline debugging, will not fill this field on board.
  // Format: [x0, y0, z0, x1, y1, z1...]
  double point_cloud = 12;

  double confidence = 13;
  enum ConfidenceType {
    CONFIDENCE_UNKNOWN = 0;
    CONFIDENCE_CNN = 1;
    CONFIDENCE_RADAR = 2;
  };
  ConfidenceType confidence_type = 14;
  // trajectory of object.
  rainbowdash.common.Point3D drops = 15;

  // The following fields are new added in Apollo 4.0
  rainbowdash.common.Point3D acceleration = 16;  // obstacle acceleration

  // a stable obstacle point in the world coordinate system
  // position defined above is the obstacle bounding box ground center
  rainbowdash.common.Point3D anchor_point = 17;

  enum SubType {
    ST_UNKNOWN = 0;
    ST_UNKNOWN_MOVABLE = 1;
    ST_UNKNOWN_UNMOVABLE = 2;
    ST_CAR = 3;
    ST_VAN = 4;
    ST_TRUCK = 5;
    ST_BUS = 6;
    ST_CYCLIST = 7;
    ST_MOTORCYCLIST = 8;
    ST_TRICYCLIST = 9;
    ST_PEDESTRIAN = 10;
    ST_TRAFFICCONE = 11;
  };
  SubType sub_type = 19;  // obstacle sub_type

  SensorMeasurement measurements = 20;  // sensor measurements

  // orthogonal distance between obstacle lowest point and ground plane
  double height_above_ground = 21;

  // position covariance which is a row-majored 3x3 matrix
  double position_covariance = 22;
  // velocity covariance which is a row-majored 3x3 matrix
  double velocity_covariance = 23;
  // acceleration covariance which is a row-majored 3x3 matrix
  double acceleration_covariance = 24;

  // lights of vehicles
  LightStatus light_status = 25;

  // Debug Message
  // optional DebugMessage msg = 26;

  enum Source {
    HOST_VEHICLE = 0;
    V2X = 1;
  };

  Source source = 27;
  // optional V2XInformation v2x_info = 28;

  // the length of the time for this prediction (e.g. 10s)
  double predicted_period = 28;
  // can have multiple trajectories per obstacle
  rainbowdash.planning.Trajectory trajectory = 29;
}

message PerceptionObstacleRaws {
  rainbowdash.common.Header header = 1;
  repeated PerceptionObstacle obstacle_raws = 2;
  int32 obstacle_nums = 3;
}

message LaneBoundaryType {
  enum Type {
    UNKNOWN = 0;
    DOTTED_YELLOW = 1;
    DOTTED_WHITE = 2;
    SOLID_YELLOW = 3;
    SOLID_WHITE = 4;
    DOUBLE_YELLOW = 5;
    CURB = 6;
  }
  // Offset relative to the starting point of boundary
  double s = 1;
  // support multiple types
  repeated Type types = 2;
}

message LaneMarker {
  LaneBoundaryType.Type lane_type = 1;
  double quality = 2;  // range = [0,1]; 1 = the best quality
  int32 model_degree = 3;

  // equation X = c3 * Z^3 + c2 * Z^2 + c1 * Z + c0
  double c0_position = 4;
  double c1_heading_angle = 5;
  double c2_curvature = 6;
  double c3_curvature_derivative = 7;
  double view_range = 8;
  double longitude_start = 9;
  double longitude_end = 10;
}

message LaneMarkers {
  LaneMarker left_lane_marker = 1;
  LaneMarker right_lane_marker = 2;
  LaneMarker next_left_lane_marker = 3;
  LaneMarker next_right_lane_marker = 4;
}

message CIPVInfo {
  int32 cipv_id = 1;
  repeated int32 potential_cipv_id = 2;
}

message PerceptionObstacles {
  repeated PerceptionObstacle perception_obstacle = 1;  // An array of obstacles
  rainbowdash.common.Header header = 2;        // Header
  // rainbowdash.common.ErrorCode error_code = 3 [default = OK];
  LaneMarkers lane_marker = 4;
  CIPVInfo cipv_info = 5;  // Closest In Path Vehicle (CIPV)
}
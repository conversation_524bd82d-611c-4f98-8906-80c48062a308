# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/obstacle.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ACC_control.proto import header_pb2 as ACC__control_dot_proto_dot_header__pb2
from perception.proto import geometry_pb2 as perception_dot_proto_dot_geometry__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/obstacle.proto',
  package='rainbowdash.drivers',
  syntax='proto3',
  serialized_pb=_b('\n\x1fperception/proto/obstacle.proto\x12\x13rainbowdash.drivers\x1a\x1e\x41\x43\x43_control/proto/header.proto\x1a\x1fperception/proto/geometry.proto\"@\n\x06\x42\x42ox2D\x12\x0c\n\x04xmin\x18\x01 \x01(\x01\x12\x0c\n\x04ymin\x18\x02 \x01(\x01\x12\x0c\n\x04xmax\x18\x03 \x01(\x01\x12\x0c\n\x04ymax\x18\x04 \x01(\x01\"\xaf\x01\n\x0bLightStatus\x12\x15\n\rbrake_visible\x18\x01 \x01(\x01\x12\x17\n\x0f\x62rake_switch_on\x18\x02 \x01(\x01\x12\x19\n\x11left_turn_visible\x18\x03 \x01(\x01\x12\x1b\n\x13left_turn_switch_on\x18\x04 \x01(\x01\x12\x1a\n\x12right_turn_visible\x18\x05 \x01(\x01\x12\x1c\n\x14right_turn_switch_on\x18\x06 \x01(\x01\"\x85\x01\n\x0eV2XInformation\x12=\n\x08v2x_type\x18\x01 \x01(\x0e\x32+.rainbowdash.drivers.V2XInformation.V2XType\"4\n\x07V2XType\x12\x08\n\x04NONE\x10\x00\x12\x0f\n\x0bZOMBIES_CAR\x10\x01\x12\x0e\n\nBLIND_ZONE\x10\x02\"\x8a\x03\n\x11SensorMeasurement\x12\x11\n\tsensor_id\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x05\x12-\n\x08position\x18\x03 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\r\n\x05theta\x18\x04 \x01(\x01\x12\x0e\n\x06length\x18\x05 \x01(\x01\x12\r\n\x05width\x18\x06 \x01(\x01\x12\x0e\n\x06height\x18\x07 \x01(\x01\x12-\n\x08velocity\x18\x08 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12:\n\x04type\x18\t \x01(\x0e\x32,.rainbowdash.drivers.PerceptionObstacle.Type\x12\x41\n\x08sub_type\x18\n \x01(\x0e\x32/.rainbowdash.drivers.PerceptionObstacle.SubType\x12\x11\n\ttimestamp\x18\x0b \x01(\x01\x12(\n\x03\x62ox\x18\x0c \x01(\x0b\x32\x1b.rainbowdash.drivers.BBox2D\"\x96\x0b\n\x12PerceptionObstacle\x12\n\n\x02id\x18\x01 \x01(\x05\x12-\n\x08position\x18\x02 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\r\n\x05theta\x18\x03 \x01(\x01\x12-\n\x08velocity\x18\x04 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06length\x18\x05 \x01(\x01\x12\r\n\x05width\x18\x06 \x01(\x01\x12\x0e\n\x06height\x18\x07 \x01(\x01\x12\x32\n\rpolygon_point\x18\x08 \x03(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x15\n\rtracking_time\x18\t \x01(\x01\x12:\n\x04type\x18\n \x01(\x0e\x32,.rainbowdash.drivers.PerceptionObstacle.Type\x12\x11\n\ttimestamp\x18\x0b \x01(\x01\x12\x13\n\x0bpoint_cloud\x18\x0c \x01(\x01\x12\x12\n\nconfidence\x18\r \x01(\x01\x12O\n\x0f\x63onfidence_type\x18\x0e \x01(\x0e\x32\x36.rainbowdash.drivers.PerceptionObstacle.ConfidenceType\x12*\n\x05\x64rops\x18\x0f \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x31\n\x0c\x61\x63\x63\x65leration\x18\x10 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x31\n\x0c\x61nchor_point\x18\x11 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x41\n\x08sub_type\x18\x13 \x01(\x0e\x32/.rainbowdash.drivers.PerceptionObstacle.SubType\x12<\n\x0cmeasurements\x18\x14 \x01(\x0b\x32&.rainbowdash.drivers.SensorMeasurement\x12\x1b\n\x13height_above_ground\x18\x15 \x01(\x01\x12\x1b\n\x13position_covariance\x18\x16 \x01(\x01\x12\x1b\n\x13velocity_covariance\x18\x17 \x01(\x01\x12\x1f\n\x17\x61\x63\x63\x65leration_covariance\x18\x18 \x01(\x01\x12\x36\n\x0clight_status\x18\x19 \x01(\x0b\x32 .rainbowdash.drivers.LightStatus\x12>\n\x06source\x18\x1b \x01(\x0e\x32..rainbowdash.drivers.PerceptionObstacle.Source\"i\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x13\n\x0fUNKNOWN_MOVABLE\x10\x01\x12\x15\n\x11UNKNOWN_UNMOVABLE\x10\x02\x12\x0e\n\nPEDESTRIAN\x10\x03\x12\x0b\n\x07\x42ICYCLE\x10\x04\x12\x0b\n\x07VEHICLE\x10\x05\"R\n\x0e\x43onfidenceType\x12\x16\n\x12\x43ONFIDENCE_UNKNOWN\x10\x00\x12\x12\n\x0e\x43ONFIDENCE_CNN\x10\x01\x12\x14\n\x10\x43ONFIDENCE_RADAR\x10\x02\"\xdc\x01\n\x07SubType\x12\x0e\n\nST_UNKNOWN\x10\x00\x12\x16\n\x12ST_UNKNOWN_MOVABLE\x10\x01\x12\x18\n\x14ST_UNKNOWN_UNMOVABLE\x10\x02\x12\n\n\x06ST_CAR\x10\x03\x12\n\n\x06ST_VAN\x10\x04\x12\x0c\n\x08ST_TRUCK\x10\x05\x12\n\n\x06ST_BUS\x10\x06\x12\x0e\n\nST_CYCLIST\x10\x07\x12\x13\n\x0fST_MOTORCYCLIST\x10\x08\x12\x11\n\rST_TRICYCLIST\x10\t\x12\x11\n\rST_PEDESTRIAN\x10\n\x12\x12\n\x0eST_TRAFFICCONE\x10\x0b\"#\n\x06Source\x12\x10\n\x0cHOST_VEHICLE\x10\x00\x12\x07\n\x03V2X\x10\x01\"\x9b\x01\n\x16PerceptionObstacleRaws\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12>\n\robstacle_raws\x18\x02 \x03(\x0b\x32\'.rainbowdash.drivers.PerceptionObstacle\x12\x15\n\robstacle_nums\x18\x03 \x01(\x05\"\x8a\x01\n\x0fPerceptionRadar\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\npoint_nums\x18\x02 \x01(\x02\x12+\n\x06\x63\x65nter\x18\x03 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12*\n\x05speed\x18\x04 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\"\x95\x01\n\x13PerceptionRadarRaws\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12;\n\robstacle_raws\x18\x02 \x03(\x0b\x32$.rainbowdash.drivers.PerceptionRadar\x12\x15\n\robstacle_nums\x18\x03 \x01(\x05\x62\x06proto3')
  ,
  dependencies=[ACC__control_dot_proto_dot_header__pb2.DESCRIPTOR,perception_dot_proto_dot_geometry__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)



_V2XINFORMATION_V2XTYPE = _descriptor.EnumDescriptor(
  name='V2XType',
  full_name='rainbowdash.drivers.V2XInformation.V2XType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ZOMBIES_CAR', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BLIND_ZONE', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=447,
  serialized_end=499,
)
_sym_db.RegisterEnumDescriptor(_V2XINFORMATION_V2XTYPE)

_PERCEPTIONOBSTACLE_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='rainbowdash.drivers.PerceptionObstacle.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN_MOVABLE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN_UNMOVABLE', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PEDESTRIAN', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BICYCLE', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='VEHICLE', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1880,
  serialized_end=1985,
)
_sym_db.RegisterEnumDescriptor(_PERCEPTIONOBSTACLE_TYPE)

_PERCEPTIONOBSTACLE_CONFIDENCETYPE = _descriptor.EnumDescriptor(
  name='ConfidenceType',
  full_name='rainbowdash.drivers.PerceptionObstacle.ConfidenceType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CONFIDENCE_UNKNOWN', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIDENCE_CNN', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIDENCE_RADAR', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1987,
  serialized_end=2069,
)
_sym_db.RegisterEnumDescriptor(_PERCEPTIONOBSTACLE_CONFIDENCETYPE)

_PERCEPTIONOBSTACLE_SUBTYPE = _descriptor.EnumDescriptor(
  name='SubType',
  full_name='rainbowdash.drivers.PerceptionObstacle.SubType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ST_UNKNOWN', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_UNKNOWN_MOVABLE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_UNKNOWN_UNMOVABLE', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_CAR', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_VAN', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_TRUCK', index=5, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_BUS', index=6, number=6,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_CYCLIST', index=7, number=7,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_MOTORCYCLIST', index=8, number=8,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_TRICYCLIST', index=9, number=9,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_PEDESTRIAN', index=10, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ST_TRAFFICCONE', index=11, number=11,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2072,
  serialized_end=2292,
)
_sym_db.RegisterEnumDescriptor(_PERCEPTIONOBSTACLE_SUBTYPE)

_PERCEPTIONOBSTACLE_SOURCE = _descriptor.EnumDescriptor(
  name='Source',
  full_name='rainbowdash.drivers.PerceptionObstacle.Source',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='HOST_VEHICLE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='V2X', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2294,
  serialized_end=2329,
)
_sym_db.RegisterEnumDescriptor(_PERCEPTIONOBSTACLE_SOURCE)


_BBOX2D = _descriptor.Descriptor(
  name='BBox2D',
  full_name='rainbowdash.drivers.BBox2D',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='xmin', full_name='rainbowdash.drivers.BBox2D.xmin', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ymin', full_name='rainbowdash.drivers.BBox2D.ymin', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='xmax', full_name='rainbowdash.drivers.BBox2D.xmax', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ymax', full_name='rainbowdash.drivers.BBox2D.ymax', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=185,
)


_LIGHTSTATUS = _descriptor.Descriptor(
  name='LightStatus',
  full_name='rainbowdash.drivers.LightStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='brake_visible', full_name='rainbowdash.drivers.LightStatus.brake_visible', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brake_switch_on', full_name='rainbowdash.drivers.LightStatus.brake_switch_on', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='left_turn_visible', full_name='rainbowdash.drivers.LightStatus.left_turn_visible', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='left_turn_switch_on', full_name='rainbowdash.drivers.LightStatus.left_turn_switch_on', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='right_turn_visible', full_name='rainbowdash.drivers.LightStatus.right_turn_visible', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='right_turn_switch_on', full_name='rainbowdash.drivers.LightStatus.right_turn_switch_on', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=188,
  serialized_end=363,
)


_V2XINFORMATION = _descriptor.Descriptor(
  name='V2XInformation',
  full_name='rainbowdash.drivers.V2XInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='v2x_type', full_name='rainbowdash.drivers.V2XInformation.v2x_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _V2XINFORMATION_V2XTYPE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=366,
  serialized_end=499,
)


_SENSORMEASUREMENT = _descriptor.Descriptor(
  name='SensorMeasurement',
  full_name='rainbowdash.drivers.SensorMeasurement',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_id', full_name='rainbowdash.drivers.SensorMeasurement.sensor_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='id', full_name='rainbowdash.drivers.SensorMeasurement.id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='position', full_name='rainbowdash.drivers.SensorMeasurement.position', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theta', full_name='rainbowdash.drivers.SensorMeasurement.theta', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='length', full_name='rainbowdash.drivers.SensorMeasurement.length', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='width', full_name='rainbowdash.drivers.SensorMeasurement.width', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.drivers.SensorMeasurement.height', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='rainbowdash.drivers.SensorMeasurement.velocity', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='rainbowdash.drivers.SensorMeasurement.type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='rainbowdash.drivers.SensorMeasurement.sub_type', index=9,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='rainbowdash.drivers.SensorMeasurement.timestamp', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='box', full_name='rainbowdash.drivers.SensorMeasurement.box', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=502,
  serialized_end=896,
)


_PERCEPTIONOBSTACLE = _descriptor.Descriptor(
  name='PerceptionObstacle',
  full_name='rainbowdash.drivers.PerceptionObstacle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='rainbowdash.drivers.PerceptionObstacle.id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='position', full_name='rainbowdash.drivers.PerceptionObstacle.position', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theta', full_name='rainbowdash.drivers.PerceptionObstacle.theta', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='rainbowdash.drivers.PerceptionObstacle.velocity', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='length', full_name='rainbowdash.drivers.PerceptionObstacle.length', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='width', full_name='rainbowdash.drivers.PerceptionObstacle.width', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.drivers.PerceptionObstacle.height', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='polygon_point', full_name='rainbowdash.drivers.PerceptionObstacle.polygon_point', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tracking_time', full_name='rainbowdash.drivers.PerceptionObstacle.tracking_time', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='rainbowdash.drivers.PerceptionObstacle.type', index=9,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='rainbowdash.drivers.PerceptionObstacle.timestamp', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='point_cloud', full_name='rainbowdash.drivers.PerceptionObstacle.point_cloud', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='confidence', full_name='rainbowdash.drivers.PerceptionObstacle.confidence', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='confidence_type', full_name='rainbowdash.drivers.PerceptionObstacle.confidence_type', index=13,
      number=14, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='drops', full_name='rainbowdash.drivers.PerceptionObstacle.drops', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='acceleration', full_name='rainbowdash.drivers.PerceptionObstacle.acceleration', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='anchor_point', full_name='rainbowdash.drivers.PerceptionObstacle.anchor_point', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='rainbowdash.drivers.PerceptionObstacle.sub_type', index=17,
      number=19, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='measurements', full_name='rainbowdash.drivers.PerceptionObstacle.measurements', index=18,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='height_above_ground', full_name='rainbowdash.drivers.PerceptionObstacle.height_above_ground', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='position_covariance', full_name='rainbowdash.drivers.PerceptionObstacle.position_covariance', index=20,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='velocity_covariance', full_name='rainbowdash.drivers.PerceptionObstacle.velocity_covariance', index=21,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='acceleration_covariance', full_name='rainbowdash.drivers.PerceptionObstacle.acceleration_covariance', index=22,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='light_status', full_name='rainbowdash.drivers.PerceptionObstacle.light_status', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='source', full_name='rainbowdash.drivers.PerceptionObstacle.source', index=24,
      number=27, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _PERCEPTIONOBSTACLE_TYPE,
    _PERCEPTIONOBSTACLE_CONFIDENCETYPE,
    _PERCEPTIONOBSTACLE_SUBTYPE,
    _PERCEPTIONOBSTACLE_SOURCE,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=899,
  serialized_end=2329,
)


_PERCEPTIONOBSTACLERAWS = _descriptor.Descriptor(
  name='PerceptionObstacleRaws',
  full_name='rainbowdash.drivers.PerceptionObstacleRaws',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.PerceptionObstacleRaws.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obstacle_raws', full_name='rainbowdash.drivers.PerceptionObstacleRaws.obstacle_raws', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obstacle_nums', full_name='rainbowdash.drivers.PerceptionObstacleRaws.obstacle_nums', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2332,
  serialized_end=2487,
)


_PERCEPTIONRADAR = _descriptor.Descriptor(
  name='PerceptionRadar',
  full_name='rainbowdash.drivers.PerceptionRadar',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='rainbowdash.drivers.PerceptionRadar.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='point_nums', full_name='rainbowdash.drivers.PerceptionRadar.point_nums', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='center', full_name='rainbowdash.drivers.PerceptionRadar.center', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed', full_name='rainbowdash.drivers.PerceptionRadar.speed', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2490,
  serialized_end=2628,
)


_PERCEPTIONRADARRAWS = _descriptor.Descriptor(
  name='PerceptionRadarRaws',
  full_name='rainbowdash.drivers.PerceptionRadarRaws',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.PerceptionRadarRaws.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obstacle_raws', full_name='rainbowdash.drivers.PerceptionRadarRaws.obstacle_raws', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obstacle_nums', full_name='rainbowdash.drivers.PerceptionRadarRaws.obstacle_nums', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2631,
  serialized_end=2780,
)

_V2XINFORMATION.fields_by_name['v2x_type'].enum_type = _V2XINFORMATION_V2XTYPE
_V2XINFORMATION_V2XTYPE.containing_type = _V2XINFORMATION
_SENSORMEASUREMENT.fields_by_name['position'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_SENSORMEASUREMENT.fields_by_name['velocity'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_SENSORMEASUREMENT.fields_by_name['type'].enum_type = _PERCEPTIONOBSTACLE_TYPE
_SENSORMEASUREMENT.fields_by_name['sub_type'].enum_type = _PERCEPTIONOBSTACLE_SUBTYPE
_SENSORMEASUREMENT.fields_by_name['box'].message_type = _BBOX2D
_PERCEPTIONOBSTACLE.fields_by_name['position'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONOBSTACLE.fields_by_name['velocity'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONOBSTACLE.fields_by_name['polygon_point'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONOBSTACLE.fields_by_name['type'].enum_type = _PERCEPTIONOBSTACLE_TYPE
_PERCEPTIONOBSTACLE.fields_by_name['confidence_type'].enum_type = _PERCEPTIONOBSTACLE_CONFIDENCETYPE
_PERCEPTIONOBSTACLE.fields_by_name['drops'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONOBSTACLE.fields_by_name['acceleration'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONOBSTACLE.fields_by_name['anchor_point'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONOBSTACLE.fields_by_name['sub_type'].enum_type = _PERCEPTIONOBSTACLE_SUBTYPE
_PERCEPTIONOBSTACLE.fields_by_name['measurements'].message_type = _SENSORMEASUREMENT
_PERCEPTIONOBSTACLE.fields_by_name['light_status'].message_type = _LIGHTSTATUS
_PERCEPTIONOBSTACLE.fields_by_name['source'].enum_type = _PERCEPTIONOBSTACLE_SOURCE
_PERCEPTIONOBSTACLE_TYPE.containing_type = _PERCEPTIONOBSTACLE
_PERCEPTIONOBSTACLE_CONFIDENCETYPE.containing_type = _PERCEPTIONOBSTACLE
_PERCEPTIONOBSTACLE_SUBTYPE.containing_type = _PERCEPTIONOBSTACLE
_PERCEPTIONOBSTACLE_SOURCE.containing_type = _PERCEPTIONOBSTACLE
_PERCEPTIONOBSTACLERAWS.fields_by_name['header'].message_type = ACC__control_dot_proto_dot_header__pb2._HEADER
_PERCEPTIONOBSTACLERAWS.fields_by_name['obstacle_raws'].message_type = _PERCEPTIONOBSTACLE
_PERCEPTIONRADAR.fields_by_name['center'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONRADAR.fields_by_name['speed'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_PERCEPTIONRADARRAWS.fields_by_name['header'].message_type = ACC__control_dot_proto_dot_header__pb2._HEADER
_PERCEPTIONRADARRAWS.fields_by_name['obstacle_raws'].message_type = _PERCEPTIONRADAR
DESCRIPTOR.message_types_by_name['BBox2D'] = _BBOX2D
DESCRIPTOR.message_types_by_name['LightStatus'] = _LIGHTSTATUS
DESCRIPTOR.message_types_by_name['V2XInformation'] = _V2XINFORMATION
DESCRIPTOR.message_types_by_name['SensorMeasurement'] = _SENSORMEASUREMENT
DESCRIPTOR.message_types_by_name['PerceptionObstacle'] = _PERCEPTIONOBSTACLE
DESCRIPTOR.message_types_by_name['PerceptionObstacleRaws'] = _PERCEPTIONOBSTACLERAWS
DESCRIPTOR.message_types_by_name['PerceptionRadar'] = _PERCEPTIONRADAR
DESCRIPTOR.message_types_by_name['PerceptionRadarRaws'] = _PERCEPTIONRADARRAWS

BBox2D = _reflection.GeneratedProtocolMessageType('BBox2D', (_message.Message,), dict(
  DESCRIPTOR = _BBOX2D,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.BBox2D)
  ))
_sym_db.RegisterMessage(BBox2D)

LightStatus = _reflection.GeneratedProtocolMessageType('LightStatus', (_message.Message,), dict(
  DESCRIPTOR = _LIGHTSTATUS,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.LightStatus)
  ))
_sym_db.RegisterMessage(LightStatus)

V2XInformation = _reflection.GeneratedProtocolMessageType('V2XInformation', (_message.Message,), dict(
  DESCRIPTOR = _V2XINFORMATION,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.V2XInformation)
  ))
_sym_db.RegisterMessage(V2XInformation)

SensorMeasurement = _reflection.GeneratedProtocolMessageType('SensorMeasurement', (_message.Message,), dict(
  DESCRIPTOR = _SENSORMEASUREMENT,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.SensorMeasurement)
  ))
_sym_db.RegisterMessage(SensorMeasurement)

PerceptionObstacle = _reflection.GeneratedProtocolMessageType('PerceptionObstacle', (_message.Message,), dict(
  DESCRIPTOR = _PERCEPTIONOBSTACLE,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.PerceptionObstacle)
  ))
_sym_db.RegisterMessage(PerceptionObstacle)

PerceptionObstacleRaws = _reflection.GeneratedProtocolMessageType('PerceptionObstacleRaws', (_message.Message,), dict(
  DESCRIPTOR = _PERCEPTIONOBSTACLERAWS,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.PerceptionObstacleRaws)
  ))
_sym_db.RegisterMessage(PerceptionObstacleRaws)

PerceptionRadar = _reflection.GeneratedProtocolMessageType('PerceptionRadar', (_message.Message,), dict(
  DESCRIPTOR = _PERCEPTIONRADAR,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.PerceptionRadar)
  ))
_sym_db.RegisterMessage(PerceptionRadar)

PerceptionRadarRaws = _reflection.GeneratedProtocolMessageType('PerceptionRadarRaws', (_message.Message,), dict(
  DESCRIPTOR = _PERCEPTIONRADARRAWS,
  __module__ = 'perception.proto.obstacle_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.PerceptionRadarRaws)
  ))
_sym_db.RegisterMessage(PerceptionRadarRaws)


# @@protoc_insertion_point(module_scope)

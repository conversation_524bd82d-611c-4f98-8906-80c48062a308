syntax = "proto3";
package rainbowdash.drivers;

import "control/proto/header.proto";


message UDPPacket {
  rainbowdash.common.Header header = 1;
  bytes data = 2;
}

message CFAR {
  bool cfar_3d_enabled = 1;
  bool cfar_4d_enabled = 2;
  bool send_metadata = 3;
}

message NTC {
  bool ntc_3d_enabled = 1;
  bool ntc_4d_enabled = 2;
  uint32 ntc_percentage = 3;
  bool send_metadata = 4;
}

message Thresholds {
  float static_threshold = 1;
  float dynamic_azimuth = 2;
  float dynamic_elevation = 3;
}

message SequenceType {
  enum type{
    IDLESEQ = 0;
    COARSESHORTSEQ = 1;
    COARSEMIDSEQ = 2;
    COARSELONGSEQ = 3;
    FINESHORTSEQ = 4;
    FINEMIDSEQ = 5;
    FINELONGSEQ = 6;
    CALIBRATIONSEQ = 7;
    LABSEQ = 8;
    DELAYCALIBRATIONSEQ = 9;
    COARSEULTRALONGSEQ = 10;
    FINEULTRALONGSEQ = 11;
    USERCONFIGURESEQ1 = 12;
    USERCONFIGURESEQ2 = 13;
    CALIBRATIONFASTSEQ = 14;
  }
  type seqtype = 1;
  uint32 data = 2;
}

message ControlState {
  rainbowdash.common.Header header = 1;
  bool tx_enabled = 2;
  SequenceType active_seq = 3;
  Thresholds thresholds = 4;
  CFAR cfar = 5;
  NTC ntc = 6;
  bool adt_enabled = 7;
  bool local_max_enabled = 8;
  uint64 timestamp_ms = 9;
}

message NewRadarPoint{
    float x = 1;
    float y = 2;
    float z = 3;
    float phase = 4;
    float power = 5;
    float elevation = 6;
    float azimuth = 7;
    float doppler = 8;
    float range = 9;
    float delta_velocity = 10;
    uint32 motion_status = 11;
    uint32 available_for_tracker = 12;
    uint32 cluster_idx = 13;
    uint32 annotation_cluster_idx = 14;
    uint32 annotation_class = 15;
}

message NewRadarPcloud {
  rainbowdash.common.Header header = 1;
  repeated NewRadarPoint pts = 2;
  uint32 width = 3;
  uint32 height = 4;
}

message RadarEgoMotion {
  rainbowdash.common.Header header = 1;
  bool is_valid = 2;
  float vel_x = 3;
  float vel_y = 4;
  float vel_z = 5;
}

message RadarDetection {
  float range = 1;
  float azimuth = 2;
  float elevation = 3;
  float radial_speed = 4;
  float power = 5;
  float power_std = 6;
  float doppler_std = 7;
  repeated float position = 8;
  //# Row major order from a 3x3 covariance matrix
  repeated float covariance = 9;
  uint32 type = 10;
  float type_probability = 11;
  uint32 label_id = 12;
  uint32 num_valid_detections = 13;
}

message RadarDetections {
  rainbowdash.common.Header header = 1;
  repeated RadarDetection detection_list = 2;
}
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/geometry.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/geometry.proto',
  package='rainbowdash.common',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1fperception/proto/geometry.proto\x12\x12rainbowdash.common\"+\n\x08PointENU\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"4\n\x08PointLLH\x12\x0b\n\x03lon\x18\x01 \x01(\x01\x12\x0b\n\x03lat\x18\x02 \x01(\x01\x12\x0e\n\x06height\x18\x03 \x01(\x01\"\x1f\n\x07Point2D\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\"*\n\x07Point3D\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"<\n\nQuaternion\x12\n\n\x02qx\x18\x01 \x01(\x01\x12\n\n\x02qy\x18\x02 \x01(\x01\x12\n\n\x02qz\x18\x03 \x01(\x01\x12\n\n\x02qw\x18\x04 \x01(\x01\"5\n\x07Polygon\x12*\n\x05point\x18\x01 \x03(\x0b\x32\x1b.rainbowdash.common.Point3Db\x06proto3')
)




_POINTENU = _descriptor.Descriptor(
  name='PointENU',
  full_name='rainbowdash.common.PointENU',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.common.PointENU.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.common.PointENU.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='rainbowdash.common.PointENU.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=98,
)


_POINTLLH = _descriptor.Descriptor(
  name='PointLLH',
  full_name='rainbowdash.common.PointLLH',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lon', full_name='rainbowdash.common.PointLLH.lon', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lat', full_name='rainbowdash.common.PointLLH.lat', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.common.PointLLH.height', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=100,
  serialized_end=152,
)


_POINT2D = _descriptor.Descriptor(
  name='Point2D',
  full_name='rainbowdash.common.Point2D',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.common.Point2D.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.common.Point2D.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=154,
  serialized_end=185,
)


_POINT3D = _descriptor.Descriptor(
  name='Point3D',
  full_name='rainbowdash.common.Point3D',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.common.Point3D.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.common.Point3D.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='rainbowdash.common.Point3D.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=187,
  serialized_end=229,
)


_QUATERNION = _descriptor.Descriptor(
  name='Quaternion',
  full_name='rainbowdash.common.Quaternion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='qx', full_name='rainbowdash.common.Quaternion.qx', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qy', full_name='rainbowdash.common.Quaternion.qy', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qz', full_name='rainbowdash.common.Quaternion.qz', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qw', full_name='rainbowdash.common.Quaternion.qw', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=231,
  serialized_end=291,
)


_POLYGON = _descriptor.Descriptor(
  name='Polygon',
  full_name='rainbowdash.common.Polygon',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='point', full_name='rainbowdash.common.Polygon.point', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=293,
  serialized_end=346,
)

_POLYGON.fields_by_name['point'].message_type = _POINT3D
DESCRIPTOR.message_types_by_name['PointENU'] = _POINTENU
DESCRIPTOR.message_types_by_name['PointLLH'] = _POINTLLH
DESCRIPTOR.message_types_by_name['Point2D'] = _POINT2D
DESCRIPTOR.message_types_by_name['Point3D'] = _POINT3D
DESCRIPTOR.message_types_by_name['Quaternion'] = _QUATERNION
DESCRIPTOR.message_types_by_name['Polygon'] = _POLYGON
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PointENU = _reflection.GeneratedProtocolMessageType('PointENU', (_message.Message,), dict(
  DESCRIPTOR = _POINTENU,
  __module__ = 'perception.proto.geometry_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.PointENU)
  ))
_sym_db.RegisterMessage(PointENU)

PointLLH = _reflection.GeneratedProtocolMessageType('PointLLH', (_message.Message,), dict(
  DESCRIPTOR = _POINTLLH,
  __module__ = 'perception.proto.geometry_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.PointLLH)
  ))
_sym_db.RegisterMessage(PointLLH)

Point2D = _reflection.GeneratedProtocolMessageType('Point2D', (_message.Message,), dict(
  DESCRIPTOR = _POINT2D,
  __module__ = 'perception.proto.geometry_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.Point2D)
  ))
_sym_db.RegisterMessage(Point2D)

Point3D = _reflection.GeneratedProtocolMessageType('Point3D', (_message.Message,), dict(
  DESCRIPTOR = _POINT3D,
  __module__ = 'perception.proto.geometry_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.Point3D)
  ))
_sym_db.RegisterMessage(Point3D)

Quaternion = _reflection.GeneratedProtocolMessageType('Quaternion', (_message.Message,), dict(
  DESCRIPTOR = _QUATERNION,
  __module__ = 'perception.proto.geometry_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.Quaternion)
  ))
_sym_db.RegisterMessage(Quaternion)

Polygon = _reflection.GeneratedProtocolMessageType('Polygon', (_message.Message,), dict(
  DESCRIPTOR = _POLYGON,
  __module__ = 'perception.proto.geometry_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.Polygon)
  ))
_sym_db.RegisterMessage(Polygon)


# @@protoc_insertion_point(module_scope)

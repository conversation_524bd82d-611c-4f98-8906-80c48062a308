# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/prediction_obstacles.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2
from perception.proto import perception_obstacle_pb2 as perception_dot_proto_dot_perception__obstacle__pb2
from control.proto import pnc_point_pb2 as control_dot_proto_dot_pnc__point__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/prediction_obstacles.proto',
  package='rainbowdash.prediction',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n+perception/proto/prediction_obstacles.proto\x12\x16rainbowdash.prediction\x1a\x1a\x63ontrol/proto/header.proto\x1a*perception/proto/perception_obstacle.proto\x1a\x1d\x63ontrol/proto/pnc_point.proto\"\xf2\x01\n\x0eObstacleIntent\x12\x39\n\x04type\x18\x01 \x01(\x0e\x32+.rainbowdash.prediction.ObstacleIntent.Type\"\xa4\x01\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04STOP\x10\x01\x12\x0e\n\nSTATIONARY\x10\x02\x12\n\n\x06MOVING\x10\x03\x12\x0f\n\x0b\x43HANGE_LANE\x10\x04\x12\x14\n\x10LOW_ACCELERATION\x10\x05\x12\x15\n\x11HIGH_ACCELERATION\x10\x06\x12\x14\n\x10LOW_DECELERATION\x10\x07\x12\x15\n\x11HIGH_DECELERATION\x10\x08\"w\n\x06Intent\x12\x31\n\x04type\x18\x01 \x01(\x0e\x32#.rainbowdash.prediction.Intent.Type\":\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04STOP\x10\x01\x12\n\n\x06\x43RUISE\x10\x02\x12\x0f\n\x0b\x43HANGE_LANE\x10\x03\"\x88\x02\n\x12PredictionObstacle\x12\x44\n\x13perception_obstacle\x18\x01 \x01(\x0b\x32\'.rainbowdash.drivers.PerceptionObstacle\x12\x11\n\ttimestamp\x18\x02 \x01(\x01\x12\x18\n\x10predicted_period\x18\x03 \x01(\x01\x12\x34\n\ntrajectory\x18\x04 \x03(\x0b\x32 .rainbowdash.planning.Trajectory\x12\x36\n\x06intent\x18\x05 \x01(\x0b\x32&.rainbowdash.prediction.ObstacleIntent\x12\x11\n\tis_static\x18\x07 \x01(\x08\"\xea\x01\n\x13PredictionObstacles\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12G\n\x13prediction_obstacle\x18\x02 \x03(\x0b\x32*.rainbowdash.prediction.PredictionObstacle\x12\x17\n\x0fstart_timestamp\x18\x04 \x01(\x01\x12\x15\n\rend_timestamp\x18\x05 \x01(\x01\x12.\n\x06intent\x18\x06 \x01(\x0b\x32\x1e.rainbowdash.prediction.Intentb\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,perception_dot_proto_dot_perception__obstacle__pb2.DESCRIPTOR,control_dot_proto_dot_pnc__point__pb2.DESCRIPTOR,])



_OBSTACLEINTENT_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='rainbowdash.prediction.ObstacleIntent.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STOP', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STATIONARY', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MOVING', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CHANGE_LANE', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LOW_ACCELERATION', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HIGH_ACCELERATION', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LOW_DECELERATION', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HIGH_DECELERATION', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=253,
  serialized_end=417,
)
_sym_db.RegisterEnumDescriptor(_OBSTACLEINTENT_TYPE)

_INTENT_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='rainbowdash.prediction.Intent.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STOP', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CRUISE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CHANGE_LANE', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=480,
  serialized_end=538,
)
_sym_db.RegisterEnumDescriptor(_INTENT_TYPE)


_OBSTACLEINTENT = _descriptor.Descriptor(
  name='ObstacleIntent',
  full_name='rainbowdash.prediction.ObstacleIntent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='rainbowdash.prediction.ObstacleIntent.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _OBSTACLEINTENT_TYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=175,
  serialized_end=417,
)


_INTENT = _descriptor.Descriptor(
  name='Intent',
  full_name='rainbowdash.prediction.Intent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='rainbowdash.prediction.Intent.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _INTENT_TYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=419,
  serialized_end=538,
)


_PREDICTIONOBSTACLE = _descriptor.Descriptor(
  name='PredictionObstacle',
  full_name='rainbowdash.prediction.PredictionObstacle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='perception_obstacle', full_name='rainbowdash.prediction.PredictionObstacle.perception_obstacle', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='rainbowdash.prediction.PredictionObstacle.timestamp', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='predicted_period', full_name='rainbowdash.prediction.PredictionObstacle.predicted_period', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trajectory', full_name='rainbowdash.prediction.PredictionObstacle.trajectory', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='intent', full_name='rainbowdash.prediction.PredictionObstacle.intent', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_static', full_name='rainbowdash.prediction.PredictionObstacle.is_static', index=5,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=541,
  serialized_end=805,
)


_PREDICTIONOBSTACLES = _descriptor.Descriptor(
  name='PredictionObstacles',
  full_name='rainbowdash.prediction.PredictionObstacles',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.prediction.PredictionObstacles.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prediction_obstacle', full_name='rainbowdash.prediction.PredictionObstacles.prediction_obstacle', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_timestamp', full_name='rainbowdash.prediction.PredictionObstacles.start_timestamp', index=2,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_timestamp', full_name='rainbowdash.prediction.PredictionObstacles.end_timestamp', index=3,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='intent', full_name='rainbowdash.prediction.PredictionObstacles.intent', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=808,
  serialized_end=1042,
)

_OBSTACLEINTENT.fields_by_name['type'].enum_type = _OBSTACLEINTENT_TYPE
_OBSTACLEINTENT_TYPE.containing_type = _OBSTACLEINTENT
_INTENT.fields_by_name['type'].enum_type = _INTENT_TYPE
_INTENT_TYPE.containing_type = _INTENT
_PREDICTIONOBSTACLE.fields_by_name['perception_obstacle'].message_type = perception_dot_proto_dot_perception__obstacle__pb2._PERCEPTIONOBSTACLE
_PREDICTIONOBSTACLE.fields_by_name['trajectory'].message_type = control_dot_proto_dot_pnc__point__pb2._TRAJECTORY
_PREDICTIONOBSTACLE.fields_by_name['intent'].message_type = _OBSTACLEINTENT
_PREDICTIONOBSTACLES.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_PREDICTIONOBSTACLES.fields_by_name['prediction_obstacle'].message_type = _PREDICTIONOBSTACLE
_PREDICTIONOBSTACLES.fields_by_name['intent'].message_type = _INTENT
DESCRIPTOR.message_types_by_name['ObstacleIntent'] = _OBSTACLEINTENT
DESCRIPTOR.message_types_by_name['Intent'] = _INTENT
DESCRIPTOR.message_types_by_name['PredictionObstacle'] = _PREDICTIONOBSTACLE
DESCRIPTOR.message_types_by_name['PredictionObstacles'] = _PREDICTIONOBSTACLES
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ObstacleIntent = _reflection.GeneratedProtocolMessageType('ObstacleIntent', (_message.Message,), dict(
  DESCRIPTOR = _OBSTACLEINTENT,
  __module__ = 'perception.proto.prediction_obstacles_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.prediction.ObstacleIntent)
  ))
_sym_db.RegisterMessage(ObstacleIntent)

Intent = _reflection.GeneratedProtocolMessageType('Intent', (_message.Message,), dict(
  DESCRIPTOR = _INTENT,
  __module__ = 'perception.proto.prediction_obstacles_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.prediction.Intent)
  ))
_sym_db.RegisterMessage(Intent)

PredictionObstacle = _reflection.GeneratedProtocolMessageType('PredictionObstacle', (_message.Message,), dict(
  DESCRIPTOR = _PREDICTIONOBSTACLE,
  __module__ = 'perception.proto.prediction_obstacles_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.prediction.PredictionObstacle)
  ))
_sym_db.RegisterMessage(PredictionObstacle)

PredictionObstacles = _reflection.GeneratedProtocolMessageType('PredictionObstacles', (_message.Message,), dict(
  DESCRIPTOR = _PREDICTIONOBSTACLES,
  __module__ = 'perception.proto.prediction_obstacles_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.prediction.PredictionObstacles)
  ))
_sym_db.RegisterMessage(PredictionObstacles)


# @@protoc_insertion_point(module_scope)


syntax = "proto3";

package rainbowdash.location;

import "control/proto/header.proto";
import "perception/proto/geometry.proto";

message Pose {
  rainbowdash.common.Header header = 1;
  // Position of the vehicle reference point (VRP) in the map reference frame.
  // The VRP is the center of rear axle.
   rainbowdash.common.PointENU position = 2;

  // A quaternion that represents the rotation from the IMU coordinate
  // (Right/Forward/Up) to the
  // world coordinate (East/North/Up).
   rainbowdash.common.Quaternion orientation = 3;

  // Linear velocity of the VRP in the map reference frame.
  // East/north/up in meters per second.
   rainbowdash.common.Point3D linear_velocity = 4;

  // Linear acceleration of the VRP in the map reference frame.
  // East/north/up in meters per square second.
   rainbowdash.common.Point3D linear_acceleration = 5;

  // Angular velocity of the vehicle in the map reference frame.
  // Around east/north/up axes in radians per second.
   rainbowdash.common.Point3D angular_velocity = 6;

  // Heading
  // The heading is zero when the car is facing East and positive when facing
  // North.
   double heading = 7;

  // Linear acceleration of the VRP in the vehicle reference frame.
  // Right/forward/up in meters per square second.
   rainbowdash.common.Point3D linear_acceleration_vrf = 8;

  // Angular velocity of the VRP in the vehicle reference frame.
  // Around right/forward/up axes in radians per second.
   rainbowdash.common.Point3D angular_velocity_vrf = 9;

  // Roll/pitch/yaw that represents a rotation with intrinsic sequence z-x-y.
  // in world coordinate (East/North/Up)
  // The roll, in (-pi/2, pi/2), corresponds to a rotation around the y-axis.
  // The pitch, in [-pi, pi), corresponds to a rotation around the x-axis.
  // The yaw, in [-pi, pi), corresponds to a rotation around the z-axis.
  // The direction of rotation follows the right-hand rule.
   rainbowdash.common.Point3D euler_angles = 10;

   // 定位状态是否可用
   bool pose_state = 11;
}

message WayPoints {
  rainbowdash.common.Header header = 1;
  repeated Pose poses = 2; // 20 * 7 poses info in 7s motion
}

message WayPointsSlim {
  // point0
  rainbowdash.common.Header header = 1;
  rainbowdash.common.Point3D position0 = 2;  //3D position
  double theta0 = 3; //direction on x-y plan

  // point1
  rainbowdash.common.Point3D position1 = 4;  //3D position
  double theta1 = 5; //direction on x-y plan

  // point2
  rainbowdash.common.Point3D position2 = 6;  //3D position
  double theta2 = 7; //direction on x-y plan

  // point3
  rainbowdash.common.Point3D position3 = 8;  //3D position
  double theta3 = 9; //direction on x-y plan

  // point4
  rainbowdash.common.Point3D position4 = 10;  //3D position
  double theta4 = 11; //direction on x-y plan

  // point5
  rainbowdash.common.Point3D position5 = 12;  //3D position
  double theta5 = 13; //direction on x-y plan

  // point6
  rainbowdash.common.Point3D position6 = 14;  //3D position
  double theta6 = 15; //direction on x-y plan

  // point7
  rainbowdash.common.Point3D position7 = 16;  //3D position
  double theta7 = 17; //direction on x-y plan

  // point8
  rainbowdash.common.Point3D position8 = 18;  //3D position
  double theta8 = 19; //direction on x-y plan

  // point9
  rainbowdash.common.Point3D position9 = 20;  //3D position
  double theta9 = 21; //direction on x-y plan

  // point10
  rainbowdash.common.Point3D position10 = 22;  //3D position
  double theta10 = 23; //direction on x-y plan

  // point11
  rainbowdash.common.Point3D position11 = 24;  //3D position
  double theta11 = 25; //direction on x-y plan

  // point12
  rainbowdash.common.Point3D position12 = 26;  //3D position
  double theta12 = 27; //direction on x-y plan

  // point13
  rainbowdash.common.Point3D position13 = 28;  //3D position
  double theta13 = 29; //direction on x-y plan

  // point14
  rainbowdash.common.Point3D position14 = 30;  //3D position
  double theta14 = 31; //direction on x-y plan
}

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/pose.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2
from perception.proto import geometry_pb2 as perception_dot_proto_dot_geometry__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/pose.proto',
  package='rainbowdash.location',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1bperception/proto/pose.proto\x12\x14rainbowdash.location\x1a\x1a\x63ontrol/proto/header.proto\x1a\x1fperception/proto/geometry.proto\"\x8f\x04\n\x04Pose\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12.\n\x08position\x18\x02 \x01(\x0b\x32\x1c.rainbowdash.common.PointENU\x12\x33\n\x0borientation\x18\x03 \x01(\x0b\x32\x1e.rainbowdash.common.Quaternion\x12\x34\n\x0flinear_velocity\x18\x04 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x38\n\x13linear_acceleration\x18\x05 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x35\n\x10\x61ngular_velocity\x18\x06 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0f\n\x07heading\x18\x07 \x01(\x01\x12<\n\x17linear_acceleration_vrf\x18\x08 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x39\n\x14\x61ngular_velocity_vrf\x18\t \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x31\n\x0c\x65uler_angles\x18\n \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x12\n\npose_state\x18\x0b \x01(\x08\"b\n\tWayPoints\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12)\n\x05poses\x18\x02 \x03(\x0b\x32\x1a.rainbowdash.location.Pose\"\x85\x08\n\rWayPointsSlim\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12.\n\tposition0\x18\x02 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta0\x18\x03 \x01(\x01\x12.\n\tposition1\x18\x04 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta1\x18\x05 \x01(\x01\x12.\n\tposition2\x18\x06 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta2\x18\x07 \x01(\x01\x12.\n\tposition3\x18\x08 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta3\x18\t \x01(\x01\x12.\n\tposition4\x18\n \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta4\x18\x0b \x01(\x01\x12.\n\tposition5\x18\x0c \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta5\x18\r \x01(\x01\x12.\n\tposition6\x18\x0e \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta6\x18\x0f \x01(\x01\x12.\n\tposition7\x18\x10 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta7\x18\x11 \x01(\x01\x12.\n\tposition8\x18\x12 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta8\x18\x13 \x01(\x01\x12.\n\tposition9\x18\x14 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0e\n\x06theta9\x18\x15 \x01(\x01\x12/\n\nposition10\x18\x16 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0f\n\x07theta10\x18\x17 \x01(\x01\x12/\n\nposition11\x18\x18 \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0f\n\x07theta11\x18\x19 \x01(\x01\x12/\n\nposition12\x18\x1a \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0f\n\x07theta12\x18\x1b \x01(\x01\x12/\n\nposition13\x18\x1c \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0f\n\x07theta13\x18\x1d \x01(\x01\x12/\n\nposition14\x18\x1e \x01(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x0f\n\x07theta14\x18\x1f \x01(\x01\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,perception_dot_proto_dot_geometry__pb2.DESCRIPTOR,])




_POSE = _descriptor.Descriptor(
  name='Pose',
  full_name='rainbowdash.location.Pose',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.Pose.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position', full_name='rainbowdash.location.Pose.position', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orientation', full_name='rainbowdash.location.Pose.orientation', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='linear_velocity', full_name='rainbowdash.location.Pose.linear_velocity', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='linear_acceleration', full_name='rainbowdash.location.Pose.linear_acceleration', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='angular_velocity', full_name='rainbowdash.location.Pose.angular_velocity', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='heading', full_name='rainbowdash.location.Pose.heading', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='linear_acceleration_vrf', full_name='rainbowdash.location.Pose.linear_acceleration_vrf', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='angular_velocity_vrf', full_name='rainbowdash.location.Pose.angular_velocity_vrf', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='euler_angles', full_name='rainbowdash.location.Pose.euler_angles', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pose_state', full_name='rainbowdash.location.Pose.pose_state', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=115,
  serialized_end=642,
)


_WAYPOINTS = _descriptor.Descriptor(
  name='WayPoints',
  full_name='rainbowdash.location.WayPoints',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.WayPoints.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='poses', full_name='rainbowdash.location.WayPoints.poses', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=644,
  serialized_end=742,
)


_WAYPOINTSSLIM = _descriptor.Descriptor(
  name='WayPointsSlim',
  full_name='rainbowdash.location.WayPointsSlim',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.WayPointsSlim.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position0', full_name='rainbowdash.location.WayPointsSlim.position0', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta0', full_name='rainbowdash.location.WayPointsSlim.theta0', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position1', full_name='rainbowdash.location.WayPointsSlim.position1', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta1', full_name='rainbowdash.location.WayPointsSlim.theta1', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position2', full_name='rainbowdash.location.WayPointsSlim.position2', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta2', full_name='rainbowdash.location.WayPointsSlim.theta2', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position3', full_name='rainbowdash.location.WayPointsSlim.position3', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta3', full_name='rainbowdash.location.WayPointsSlim.theta3', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position4', full_name='rainbowdash.location.WayPointsSlim.position4', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta4', full_name='rainbowdash.location.WayPointsSlim.theta4', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position5', full_name='rainbowdash.location.WayPointsSlim.position5', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta5', full_name='rainbowdash.location.WayPointsSlim.theta5', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position6', full_name='rainbowdash.location.WayPointsSlim.position6', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta6', full_name='rainbowdash.location.WayPointsSlim.theta6', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position7', full_name='rainbowdash.location.WayPointsSlim.position7', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta7', full_name='rainbowdash.location.WayPointsSlim.theta7', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position8', full_name='rainbowdash.location.WayPointsSlim.position8', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta8', full_name='rainbowdash.location.WayPointsSlim.theta8', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position9', full_name='rainbowdash.location.WayPointsSlim.position9', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta9', full_name='rainbowdash.location.WayPointsSlim.theta9', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position10', full_name='rainbowdash.location.WayPointsSlim.position10', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta10', full_name='rainbowdash.location.WayPointsSlim.theta10', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position11', full_name='rainbowdash.location.WayPointsSlim.position11', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta11', full_name='rainbowdash.location.WayPointsSlim.theta11', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position12', full_name='rainbowdash.location.WayPointsSlim.position12', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta12', full_name='rainbowdash.location.WayPointsSlim.theta12', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position13', full_name='rainbowdash.location.WayPointsSlim.position13', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta13', full_name='rainbowdash.location.WayPointsSlim.theta13', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position14', full_name='rainbowdash.location.WayPointsSlim.position14', index=29,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theta14', full_name='rainbowdash.location.WayPointsSlim.theta14', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=745,
  serialized_end=1774,
)

_POSE.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_POSE.fields_by_name['position'].message_type = perception_dot_proto_dot_geometry__pb2._POINTENU
_POSE.fields_by_name['orientation'].message_type = perception_dot_proto_dot_geometry__pb2._QUATERNION
_POSE.fields_by_name['linear_velocity'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_POSE.fields_by_name['linear_acceleration'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_POSE.fields_by_name['angular_velocity'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_POSE.fields_by_name['linear_acceleration_vrf'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_POSE.fields_by_name['angular_velocity_vrf'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_POSE.fields_by_name['euler_angles'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTS.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_WAYPOINTS.fields_by_name['poses'].message_type = _POSE
_WAYPOINTSSLIM.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_WAYPOINTSSLIM.fields_by_name['position0'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position1'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position2'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position3'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position4'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position5'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position6'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position7'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position8'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position9'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position10'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position11'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position12'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position13'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_WAYPOINTSSLIM.fields_by_name['position14'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
DESCRIPTOR.message_types_by_name['Pose'] = _POSE
DESCRIPTOR.message_types_by_name['WayPoints'] = _WAYPOINTS
DESCRIPTOR.message_types_by_name['WayPointsSlim'] = _WAYPOINTSSLIM
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pose = _reflection.GeneratedProtocolMessageType('Pose', (_message.Message,), dict(
  DESCRIPTOR = _POSE,
  __module__ = 'perception.proto.pose_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.Pose)
  ))
_sym_db.RegisterMessage(Pose)

WayPoints = _reflection.GeneratedProtocolMessageType('WayPoints', (_message.Message,), dict(
  DESCRIPTOR = _WAYPOINTS,
  __module__ = 'perception.proto.pose_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.WayPoints)
  ))
_sym_db.RegisterMessage(WayPoints)

WayPointsSlim = _reflection.GeneratedProtocolMessageType('WayPointsSlim', (_message.Message,), dict(
  DESCRIPTOR = _WAYPOINTSSLIM,
  __module__ = 'perception.proto.pose_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.WayPointsSlim)
  ))
_sym_db.RegisterMessage(WayPointsSlim)


# @@protoc_insertion_point(module_scope)

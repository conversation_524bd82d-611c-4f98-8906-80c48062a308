syntax = "proto3";
package rainbowdash.location;

//import "thirdparty/recommend_protocols/common/proto/header.proto";
import "control/proto/header.proto";
//import "thirdparty/recommend_protocols/common/proto/geometry.proto";
import "perception/proto/geometry.proto";
//import "thirdparty/recommend_protocols/location/proto/pose.proto";
import "perception/proto/pose.proto";

message BEVLocation {
  rainbowdash.common.Header header = 1;
  rainbowdash.common.Point2D position = 2;  //2D position at BEV map.
  double theta = 3; //direction on x-y plan
}

//the following messages are copied from the Apollo localization module

message Uncertainty {
  // Standard deviation of position, east/north/up in meters.
   rainbowdash.common.Point3D position_std_dev = 1;

  // Standard deviation of quaternion qx/qy/qz, unitless.
   rainbowdash.common.Point3D orientation_std_dev = 2;

  // Standard deviation of linear velocity, east/north/up in meters per second.
   rainbowdash.common.Point3D linear_velocity_std_dev = 3;

  // Standard deviation of linear acceleration, right/forward/up in meters per
  // square second.
   rainbowdash.common.Point3D linear_acceleration_std_dev = 4;

  // Standard deviation of angular velocity, right/forward/up in radians per
  // second.
   rainbowdash.common.Point3D angular_velocity_std_dev = 5;

  // TODO: Define covariance items when needed.
}

message LocalizationEstimate {
   rainbowdash.common.Header header = 1;
   rainbowdash.location.Pose pose = 2;
   Uncertainty uncertainty = 3;

  // The time of pose measurement, seconds since 1970-1-1 (UNIX time).
   double measurement_time = 4;  // In seconds.

  // Future trajectory actually driven by the drivers
  //repeated apollo.common.TrajectoryPoint trajectory_point = 5;

  // msf status
  // MsfStatus msf_status = 6;
  // msf quality
  // MsfSensorMsgStatus sensor_status = 7;
}

enum MeasureState {
  OK = 0;
  WARNNING = 1;
  ERROR = 2;
  CRITICAL_ERROR = 3;
  FATAL_ERROR = 4;
}

message LocalizationStatus {
   rainbowdash.common.Header header = 1;
   MeasureState fusion_status = 2;
   MeasureState gnss_status = 3 [deprecated = true];
   MeasureState lidar_status = 4 [deprecated = true];
  // The time of pose measurement, seconds since 1970-1-1 (UNIX time).
   double measurement_time = 5;  // In seconds.
   string state_message = 6;
}

message SlamFrame{
  rainbowdash.common.Header header = 1;
  rainbowdash.location.BEVLocation location = 2;
  rainbowdash.common.Point2D velocity = 3;
  rainbowdash.common.PointLLH accelerate = 4;
  double yaw_rate = 5;
  rainbowdash.location.BEVLocation ori_location=6;
  rainbowdash.location.BEVLocation replan_location=7;
}

message LocationErr {
  rainbowdash.common.Header header = 1;
  double measurement_time = 2;  // In seconds.
  double lat_err = 3; // unit m
  double long_err = 4;
  double yaw_err = 5; 
}

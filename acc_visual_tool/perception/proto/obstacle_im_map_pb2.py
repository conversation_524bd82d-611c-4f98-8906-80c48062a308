# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/obstacle_im_map.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2
from perception.proto import perception_obstacle_pb2 as perception_dot_proto_dot_perception__obstacle__pb2
from perception.proto import location_pb2 as perception_dot_proto_dot_location__pb2
from perception.proto import geometry_pb2 as perception_dot_proto_dot_geometry__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/obstacle_im_map.proto',
  package='rainbowdash.location',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n&perception/proto/obstacle_im_map.proto\x12\x14rainbowdash.location\x1a\x1a\x63ontrol/proto/header.proto\x1a*perception/proto/perception_obstacle.proto\x1a\x1fperception/proto/location.proto\x1a\x1fperception/proto/geometry.proto\"V\n\nCenterLine\x12/\n\npts_center\x18\x07 \x03(\x0b\x32\x1b.rainbowdash.common.Point3D\x12\x17\n\x0fheadings_center\x18\x08 \x03(\x01\"\xc5\x01\n\x0b\x43\x65nterLines\x12:\n\x10left_center_line\x18\x01 \x01(\x0b\x32 .rainbowdash.location.CenterLine\x12=\n\x13\x63urrent_center_line\x18\x02 \x01(\x0b\x32 .rainbowdash.location.CenterLine\x12;\n\x11right_center_line\x18\x03 \x01(\x0b\x32 .rainbowdash.location.CenterLine\"~\n\x0cLaneLineInfo\x12\x35\n\x0blane_marker\x18\x01 \x01(\x0b\x32 .rainbowdash.drivers.LaneMarkers\x12\x37\n\x0c\x63\x65nter_lines\x18\x02 \x01(\x0b\x32!.rainbowdash.location.CenterLines\"\x9a\x02\n\x0eObstaclesInMap\x12\x44\n\x13perception_obstacle\x18\x01 \x03(\x0b\x32\'.rainbowdash.drivers.PerceptionObstacle\x12*\n\x06header\x18\x02 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x35\n\tlane_info\x18\x04 \x01(\x0b\x32\".rainbowdash.location.LaneLineInfo\x12\x30\n\tcipv_info\x18\x05 \x01(\x0b\x32\x1d.rainbowdash.drivers.CIPVInfo\x12-\n\x04pose\x18\x06 \x01(\x0b\x32\x1f.rainbowdash.location.SlamFrameb\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,perception_dot_proto_dot_perception__obstacle__pb2.DESCRIPTOR,perception_dot_proto_dot_location__pb2.DESCRIPTOR,perception_dot_proto_dot_geometry__pb2.DESCRIPTOR,])




_CENTERLINE = _descriptor.Descriptor(
  name='CenterLine',
  full_name='rainbowdash.location.CenterLine',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pts_center', full_name='rainbowdash.location.CenterLine.pts_center', index=0,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='headings_center', full_name='rainbowdash.location.CenterLine.headings_center', index=1,
      number=8, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=288,
)


_CENTERLINES = _descriptor.Descriptor(
  name='CenterLines',
  full_name='rainbowdash.location.CenterLines',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='left_center_line', full_name='rainbowdash.location.CenterLines.left_center_line', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='current_center_line', full_name='rainbowdash.location.CenterLines.current_center_line', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='right_center_line', full_name='rainbowdash.location.CenterLines.right_center_line', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=291,
  serialized_end=488,
)


_LANELINEINFO = _descriptor.Descriptor(
  name='LaneLineInfo',
  full_name='rainbowdash.location.LaneLineInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lane_marker', full_name='rainbowdash.location.LaneLineInfo.lane_marker', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center_lines', full_name='rainbowdash.location.LaneLineInfo.center_lines', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=490,
  serialized_end=616,
)


_OBSTACLESINMAP = _descriptor.Descriptor(
  name='ObstaclesInMap',
  full_name='rainbowdash.location.ObstaclesInMap',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='perception_obstacle', full_name='rainbowdash.location.ObstaclesInMap.perception_obstacle', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.location.ObstaclesInMap.header', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lane_info', full_name='rainbowdash.location.ObstaclesInMap.lane_info', index=2,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cipv_info', full_name='rainbowdash.location.ObstaclesInMap.cipv_info', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pose', full_name='rainbowdash.location.ObstaclesInMap.pose', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=619,
  serialized_end=901,
)

_CENTERLINE.fields_by_name['pts_center'].message_type = perception_dot_proto_dot_geometry__pb2._POINT3D
_CENTERLINES.fields_by_name['left_center_line'].message_type = _CENTERLINE
_CENTERLINES.fields_by_name['current_center_line'].message_type = _CENTERLINE
_CENTERLINES.fields_by_name['right_center_line'].message_type = _CENTERLINE
_LANELINEINFO.fields_by_name['lane_marker'].message_type = perception_dot_proto_dot_perception__obstacle__pb2._LANEMARKERS
_LANELINEINFO.fields_by_name['center_lines'].message_type = _CENTERLINES
_OBSTACLESINMAP.fields_by_name['perception_obstacle'].message_type = perception_dot_proto_dot_perception__obstacle__pb2._PERCEPTIONOBSTACLE
_OBSTACLESINMAP.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_OBSTACLESINMAP.fields_by_name['lane_info'].message_type = _LANELINEINFO
_OBSTACLESINMAP.fields_by_name['cipv_info'].message_type = perception_dot_proto_dot_perception__obstacle__pb2._CIPVINFO
_OBSTACLESINMAP.fields_by_name['pose'].message_type = perception_dot_proto_dot_location__pb2._SLAMFRAME
DESCRIPTOR.message_types_by_name['CenterLine'] = _CENTERLINE
DESCRIPTOR.message_types_by_name['CenterLines'] = _CENTERLINES
DESCRIPTOR.message_types_by_name['LaneLineInfo'] = _LANELINEINFO
DESCRIPTOR.message_types_by_name['ObstaclesInMap'] = _OBSTACLESINMAP
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CenterLine = _reflection.GeneratedProtocolMessageType('CenterLine', (_message.Message,), dict(
  DESCRIPTOR = _CENTERLINE,
  __module__ = 'perception.proto.obstacle_im_map_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.CenterLine)
  ))
_sym_db.RegisterMessage(CenterLine)

CenterLines = _reflection.GeneratedProtocolMessageType('CenterLines', (_message.Message,), dict(
  DESCRIPTOR = _CENTERLINES,
  __module__ = 'perception.proto.obstacle_im_map_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.CenterLines)
  ))
_sym_db.RegisterMessage(CenterLines)

LaneLineInfo = _reflection.GeneratedProtocolMessageType('LaneLineInfo', (_message.Message,), dict(
  DESCRIPTOR = _LANELINEINFO,
  __module__ = 'perception.proto.obstacle_im_map_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.LaneLineInfo)
  ))
_sym_db.RegisterMessage(LaneLineInfo)

ObstaclesInMap = _reflection.GeneratedProtocolMessageType('ObstaclesInMap', (_message.Message,), dict(
  DESCRIPTOR = _OBSTACLESINMAP,
  __module__ = 'perception.proto.obstacle_im_map_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.location.ObstaclesInMap)
  ))
_sym_db.RegisterMessage(ObstaclesInMap)


# @@protoc_insertion_point(module_scope)

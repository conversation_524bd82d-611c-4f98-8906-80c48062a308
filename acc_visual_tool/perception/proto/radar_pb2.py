# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: perception/proto/radar.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='perception/proto/radar.proto',
  package='rainbowdash.drivers',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1cperception/proto/radar.proto\x12\x13rainbowdash.drivers\x1a\x1a\x63ontrol/proto/header.proto\"E\n\tUDPPacket\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\"O\n\x04\x43\x46\x41R\x12\x17\n\x0f\x63\x66\x61r_3d_enabled\x18\x01 \x01(\x08\x12\x17\n\x0f\x63\x66\x61r_4d_enabled\x18\x02 \x01(\x08\x12\x15\n\rsend_metadata\x18\x03 \x01(\x08\"d\n\x03NTC\x12\x16\n\x0entc_3d_enabled\x18\x01 \x01(\x08\x12\x16\n\x0entc_4d_enabled\x18\x02 \x01(\x08\x12\x16\n\x0entc_percentage\x18\x03 \x01(\r\x12\x15\n\rsend_metadata\x18\x04 \x01(\x08\"Z\n\nThresholds\x12\x18\n\x10static_threshold\x18\x01 \x01(\x02\x12\x17\n\x0f\x64ynamic_azimuth\x18\x02 \x01(\x02\x12\x19\n\x11\x64ynamic_elevation\x18\x03 \x01(\x02\"\x84\x03\n\x0cSequenceType\x12\x37\n\x07seqtype\x18\x01 \x01(\x0e\x32&.rainbowdash.drivers.SequenceType.type\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\r\"\xac\x02\n\x04type\x12\x0b\n\x07IDLESEQ\x10\x00\x12\x12\n\x0e\x43OARSESHORTSEQ\x10\x01\x12\x10\n\x0c\x43OARSEMIDSEQ\x10\x02\x12\x11\n\rCOARSELONGSEQ\x10\x03\x12\x10\n\x0c\x46INESHORTSEQ\x10\x04\x12\x0e\n\nFINEMIDSEQ\x10\x05\x12\x0f\n\x0b\x46INELONGSEQ\x10\x06\x12\x12\n\x0e\x43\x41LIBRATIONSEQ\x10\x07\x12\n\n\x06LABSEQ\x10\x08\x12\x17\n\x13\x44\x45LAYCALIBRATIONSEQ\x10\t\x12\x16\n\x12\x43OARSEULTRALONGSEQ\x10\n\x12\x14\n\x10\x46INEULTRALONGSEQ\x10\x0b\x12\x15\n\x11USERCONFIGURESEQ1\x10\x0c\x12\x15\n\x11USERCONFIGURESEQ2\x10\r\x12\x16\n\x12\x43\x41LIBRATIONFASTSEQ\x10\x0e\"\xd0\x02\n\x0c\x43ontrolState\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x12\n\ntx_enabled\x18\x02 \x01(\x08\x12\x35\n\nactive_seq\x18\x03 \x01(\x0b\x32!.rainbowdash.drivers.SequenceType\x12\x33\n\nthresholds\x18\x04 \x01(\x0b\x32\x1f.rainbowdash.drivers.Thresholds\x12\'\n\x04\x63\x66\x61r\x18\x05 \x01(\x0b\x32\x19.rainbowdash.drivers.CFAR\x12%\n\x03ntc\x18\x06 \x01(\x0b\x32\x18.rainbowdash.drivers.NTC\x12\x13\n\x0b\x61\x64t_enabled\x18\x07 \x01(\x08\x12\x19\n\x11local_max_enabled\x18\x08 \x01(\x08\x12\x14\n\x0ctimestamp_ms\x18\t \x01(\x04\"\xaf\x02\n\rNewRadarPoint\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\x12\r\n\x05phase\x18\x04 \x01(\x02\x12\r\n\x05power\x18\x05 \x01(\x02\x12\x11\n\televation\x18\x06 \x01(\x02\x12\x0f\n\x07\x61zimuth\x18\x07 \x01(\x02\x12\x0f\n\x07\x64oppler\x18\x08 \x01(\x02\x12\r\n\x05range\x18\t \x01(\x02\x12\x16\n\x0e\x64\x65lta_velocity\x18\n \x01(\x02\x12\x15\n\rmotion_status\x18\x0b \x01(\r\x12\x1d\n\x15\x61vailable_for_tracker\x18\x0c \x01(\r\x12\x13\n\x0b\x63luster_idx\x18\r \x01(\r\x12\x1e\n\x16\x61nnotation_cluster_idx\x18\x0e \x01(\r\x12\x18\n\x10\x61nnotation_class\x18\x0f \x01(\r\"\x8c\x01\n\x0eNewRadarPcloud\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12/\n\x03pts\x18\x02 \x03(\x0b\x32\".rainbowdash.drivers.NewRadarPoint\x12\r\n\x05width\x18\x03 \x01(\r\x12\x0e\n\x06height\x18\x04 \x01(\r\"{\n\x0eRadarEgoMotion\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x10\n\x08is_valid\x18\x02 \x01(\x08\x12\r\n\x05vel_x\x18\x03 \x01(\x02\x12\r\n\x05vel_y\x18\x04 \x01(\x02\x12\r\n\x05vel_z\x18\x05 \x01(\x02\"\x8e\x02\n\x0eRadarDetection\x12\r\n\x05range\x18\x01 \x01(\x02\x12\x0f\n\x07\x61zimuth\x18\x02 \x01(\x02\x12\x11\n\televation\x18\x03 \x01(\x02\x12\x14\n\x0cradial_speed\x18\x04 \x01(\x02\x12\r\n\x05power\x18\x05 \x01(\x02\x12\x11\n\tpower_std\x18\x06 \x01(\x02\x12\x13\n\x0b\x64oppler_std\x18\x07 \x01(\x02\x12\x10\n\x08position\x18\x08 \x03(\x02\x12\x12\n\ncovariance\x18\t \x03(\x02\x12\x0c\n\x04type\x18\n \x01(\r\x12\x18\n\x10type_probability\x18\x0b \x01(\x02\x12\x10\n\x08label_id\x18\x0c \x01(\r\x12\x1c\n\x14num_valid_detections\x18\r \x01(\r\"z\n\x0fRadarDetections\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12;\n\x0e\x64\x65tection_list\x18\x02 \x03(\x0b\x32#.rainbowdash.drivers.RadarDetectionb\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])



_SEQUENCETYPE_TYPE = _descriptor.EnumDescriptor(
  name='type',
  full_name='rainbowdash.drivers.SequenceType.type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IDLESEQ', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='COARSESHORTSEQ', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='COARSEMIDSEQ', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='COARSELONGSEQ', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FINESHORTSEQ', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FINEMIDSEQ', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FINELONGSEQ', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CALIBRATIONSEQ', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LABSEQ', index=8, number=8,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DELAYCALIBRATIONSEQ', index=9, number=9,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='COARSEULTRALONGSEQ', index=10, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FINEULTRALONGSEQ', index=11, number=11,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='USERCONFIGURESEQ1', index=12, number=12,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='USERCONFIGURESEQ2', index=13, number=13,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CALIBRATIONFASTSEQ', index=14, number=14,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=516,
  serialized_end=816,
)
_sym_db.RegisterEnumDescriptor(_SEQUENCETYPE_TYPE)


_UDPPACKET = _descriptor.Descriptor(
  name='UDPPacket',
  full_name='rainbowdash.drivers.UDPPacket',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.UDPPacket.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='rainbowdash.drivers.UDPPacket.data', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=81,
  serialized_end=150,
)


_CFAR = _descriptor.Descriptor(
  name='CFAR',
  full_name='rainbowdash.drivers.CFAR',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cfar_3d_enabled', full_name='rainbowdash.drivers.CFAR.cfar_3d_enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cfar_4d_enabled', full_name='rainbowdash.drivers.CFAR.cfar_4d_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_metadata', full_name='rainbowdash.drivers.CFAR.send_metadata', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=152,
  serialized_end=231,
)


_NTC = _descriptor.Descriptor(
  name='NTC',
  full_name='rainbowdash.drivers.NTC',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ntc_3d_enabled', full_name='rainbowdash.drivers.NTC.ntc_3d_enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ntc_4d_enabled', full_name='rainbowdash.drivers.NTC.ntc_4d_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ntc_percentage', full_name='rainbowdash.drivers.NTC.ntc_percentage', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_metadata', full_name='rainbowdash.drivers.NTC.send_metadata', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=233,
  serialized_end=333,
)


_THRESHOLDS = _descriptor.Descriptor(
  name='Thresholds',
  full_name='rainbowdash.drivers.Thresholds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='static_threshold', full_name='rainbowdash.drivers.Thresholds.static_threshold', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dynamic_azimuth', full_name='rainbowdash.drivers.Thresholds.dynamic_azimuth', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dynamic_elevation', full_name='rainbowdash.drivers.Thresholds.dynamic_elevation', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=335,
  serialized_end=425,
)


_SEQUENCETYPE = _descriptor.Descriptor(
  name='SequenceType',
  full_name='rainbowdash.drivers.SequenceType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='seqtype', full_name='rainbowdash.drivers.SequenceType.seqtype', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='rainbowdash.drivers.SequenceType.data', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _SEQUENCETYPE_TYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=428,
  serialized_end=816,
)


_CONTROLSTATE = _descriptor.Descriptor(
  name='ControlState',
  full_name='rainbowdash.drivers.ControlState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.ControlState.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tx_enabled', full_name='rainbowdash.drivers.ControlState.tx_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='active_seq', full_name='rainbowdash.drivers.ControlState.active_seq', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='thresholds', full_name='rainbowdash.drivers.ControlState.thresholds', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cfar', full_name='rainbowdash.drivers.ControlState.cfar', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ntc', full_name='rainbowdash.drivers.ControlState.ntc', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adt_enabled', full_name='rainbowdash.drivers.ControlState.adt_enabled', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='local_max_enabled', full_name='rainbowdash.drivers.ControlState.local_max_enabled', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='rainbowdash.drivers.ControlState.timestamp_ms', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=819,
  serialized_end=1155,
)


_NEWRADARPOINT = _descriptor.Descriptor(
  name='NewRadarPoint',
  full_name='rainbowdash.drivers.NewRadarPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.drivers.NewRadarPoint.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.drivers.NewRadarPoint.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='z', full_name='rainbowdash.drivers.NewRadarPoint.z', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='phase', full_name='rainbowdash.drivers.NewRadarPoint.phase', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='power', full_name='rainbowdash.drivers.NewRadarPoint.power', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='elevation', full_name='rainbowdash.drivers.NewRadarPoint.elevation', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='azimuth', full_name='rainbowdash.drivers.NewRadarPoint.azimuth', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doppler', full_name='rainbowdash.drivers.NewRadarPoint.doppler', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='range', full_name='rainbowdash.drivers.NewRadarPoint.range', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delta_velocity', full_name='rainbowdash.drivers.NewRadarPoint.delta_velocity', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='motion_status', full_name='rainbowdash.drivers.NewRadarPoint.motion_status', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='available_for_tracker', full_name='rainbowdash.drivers.NewRadarPoint.available_for_tracker', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cluster_idx', full_name='rainbowdash.drivers.NewRadarPoint.cluster_idx', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='annotation_cluster_idx', full_name='rainbowdash.drivers.NewRadarPoint.annotation_cluster_idx', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='annotation_class', full_name='rainbowdash.drivers.NewRadarPoint.annotation_class', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1158,
  serialized_end=1461,
)


_NEWRADARPCLOUD = _descriptor.Descriptor(
  name='NewRadarPcloud',
  full_name='rainbowdash.drivers.NewRadarPcloud',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.NewRadarPcloud.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pts', full_name='rainbowdash.drivers.NewRadarPcloud.pts', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='rainbowdash.drivers.NewRadarPcloud.width', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='rainbowdash.drivers.NewRadarPcloud.height', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1464,
  serialized_end=1604,
)


_RADAREGOMOTION = _descriptor.Descriptor(
  name='RadarEgoMotion',
  full_name='rainbowdash.drivers.RadarEgoMotion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.RadarEgoMotion.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_valid', full_name='rainbowdash.drivers.RadarEgoMotion.is_valid', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vel_x', full_name='rainbowdash.drivers.RadarEgoMotion.vel_x', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vel_y', full_name='rainbowdash.drivers.RadarEgoMotion.vel_y', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vel_z', full_name='rainbowdash.drivers.RadarEgoMotion.vel_z', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1606,
  serialized_end=1729,
)


_RADARDETECTION = _descriptor.Descriptor(
  name='RadarDetection',
  full_name='rainbowdash.drivers.RadarDetection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='range', full_name='rainbowdash.drivers.RadarDetection.range', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='azimuth', full_name='rainbowdash.drivers.RadarDetection.azimuth', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='elevation', full_name='rainbowdash.drivers.RadarDetection.elevation', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='radial_speed', full_name='rainbowdash.drivers.RadarDetection.radial_speed', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='power', full_name='rainbowdash.drivers.RadarDetection.power', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='power_std', full_name='rainbowdash.drivers.RadarDetection.power_std', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doppler_std', full_name='rainbowdash.drivers.RadarDetection.doppler_std', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position', full_name='rainbowdash.drivers.RadarDetection.position', index=7,
      number=8, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='covariance', full_name='rainbowdash.drivers.RadarDetection.covariance', index=8,
      number=9, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='rainbowdash.drivers.RadarDetection.type', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type_probability', full_name='rainbowdash.drivers.RadarDetection.type_probability', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label_id', full_name='rainbowdash.drivers.RadarDetection.label_id', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_valid_detections', full_name='rainbowdash.drivers.RadarDetection.num_valid_detections', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1732,
  serialized_end=2002,
)


_RADARDETECTIONS = _descriptor.Descriptor(
  name='RadarDetections',
  full_name='rainbowdash.drivers.RadarDetections',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.RadarDetections.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detection_list', full_name='rainbowdash.drivers.RadarDetections.detection_list', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2004,
  serialized_end=2126,
)

_UDPPACKET.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_SEQUENCETYPE.fields_by_name['seqtype'].enum_type = _SEQUENCETYPE_TYPE
_SEQUENCETYPE_TYPE.containing_type = _SEQUENCETYPE
_CONTROLSTATE.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_CONTROLSTATE.fields_by_name['active_seq'].message_type = _SEQUENCETYPE
_CONTROLSTATE.fields_by_name['thresholds'].message_type = _THRESHOLDS
_CONTROLSTATE.fields_by_name['cfar'].message_type = _CFAR
_CONTROLSTATE.fields_by_name['ntc'].message_type = _NTC
_NEWRADARPCLOUD.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_NEWRADARPCLOUD.fields_by_name['pts'].message_type = _NEWRADARPOINT
_RADAREGOMOTION.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_RADARDETECTIONS.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_RADARDETECTIONS.fields_by_name['detection_list'].message_type = _RADARDETECTION
DESCRIPTOR.message_types_by_name['UDPPacket'] = _UDPPACKET
DESCRIPTOR.message_types_by_name['CFAR'] = _CFAR
DESCRIPTOR.message_types_by_name['NTC'] = _NTC
DESCRIPTOR.message_types_by_name['Thresholds'] = _THRESHOLDS
DESCRIPTOR.message_types_by_name['SequenceType'] = _SEQUENCETYPE
DESCRIPTOR.message_types_by_name['ControlState'] = _CONTROLSTATE
DESCRIPTOR.message_types_by_name['NewRadarPoint'] = _NEWRADARPOINT
DESCRIPTOR.message_types_by_name['NewRadarPcloud'] = _NEWRADARPCLOUD
DESCRIPTOR.message_types_by_name['RadarEgoMotion'] = _RADAREGOMOTION
DESCRIPTOR.message_types_by_name['RadarDetection'] = _RADARDETECTION
DESCRIPTOR.message_types_by_name['RadarDetections'] = _RADARDETECTIONS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UDPPacket = _reflection.GeneratedProtocolMessageType('UDPPacket', (_message.Message,), dict(
  DESCRIPTOR = _UDPPACKET,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.UDPPacket)
  ))
_sym_db.RegisterMessage(UDPPacket)

CFAR = _reflection.GeneratedProtocolMessageType('CFAR', (_message.Message,), dict(
  DESCRIPTOR = _CFAR,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.CFAR)
  ))
_sym_db.RegisterMessage(CFAR)

NTC = _reflection.GeneratedProtocolMessageType('NTC', (_message.Message,), dict(
  DESCRIPTOR = _NTC,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.NTC)
  ))
_sym_db.RegisterMessage(NTC)

Thresholds = _reflection.GeneratedProtocolMessageType('Thresholds', (_message.Message,), dict(
  DESCRIPTOR = _THRESHOLDS,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.Thresholds)
  ))
_sym_db.RegisterMessage(Thresholds)

SequenceType = _reflection.GeneratedProtocolMessageType('SequenceType', (_message.Message,), dict(
  DESCRIPTOR = _SEQUENCETYPE,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.SequenceType)
  ))
_sym_db.RegisterMessage(SequenceType)

ControlState = _reflection.GeneratedProtocolMessageType('ControlState', (_message.Message,), dict(
  DESCRIPTOR = _CONTROLSTATE,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.ControlState)
  ))
_sym_db.RegisterMessage(ControlState)

NewRadarPoint = _reflection.GeneratedProtocolMessageType('NewRadarPoint', (_message.Message,), dict(
  DESCRIPTOR = _NEWRADARPOINT,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.NewRadarPoint)
  ))
_sym_db.RegisterMessage(NewRadarPoint)

NewRadarPcloud = _reflection.GeneratedProtocolMessageType('NewRadarPcloud', (_message.Message,), dict(
  DESCRIPTOR = _NEWRADARPCLOUD,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.NewRadarPcloud)
  ))
_sym_db.RegisterMessage(NewRadarPcloud)

RadarEgoMotion = _reflection.GeneratedProtocolMessageType('RadarEgoMotion', (_message.Message,), dict(
  DESCRIPTOR = _RADAREGOMOTION,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.RadarEgoMotion)
  ))
_sym_db.RegisterMessage(RadarEgoMotion)

RadarDetection = _reflection.GeneratedProtocolMessageType('RadarDetection', (_message.Message,), dict(
  DESCRIPTOR = _RADARDETECTION,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.RadarDetection)
  ))
_sym_db.RegisterMessage(RadarDetection)

RadarDetections = _reflection.GeneratedProtocolMessageType('RadarDetections', (_message.Message,), dict(
  DESCRIPTOR = _RADARDETECTIONS,
  __module__ = 'perception.proto.radar_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.RadarDetections)
  ))
_sym_db.RegisterMessage(RadarDetections)


# @@protoc_insertion_point(module_scope)

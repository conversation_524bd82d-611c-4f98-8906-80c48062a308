1. 一键安装ros(https://blog.csdn.net/u014374826/article/details/132266704)

sudo apt-get update
wget http://fishros.com/install -O fishros && . fishros

推荐安装 noetic

2. 启动 roscore
3. 启动 rqt，  perspectives -> Import ,选择 drive-core/acc_visual_tool/Default.perspective
4. cd acc_visual_tool 运行 python main.py  主要程序内 os.environ["CYBER_IP"]的设置

5. rviz状态栏
    5.1  设置Grid， cellsize: 10、 cellcount: 20
    5.2  设置Axis， length: 5、 radius: 0.5
    5.3  设置视图， 右边状态栏的Type选择 TopDownOrtho(rviz)， Angle：-1.57， 鼠标微调
    5.4  image_view 自动创建，无需Add
    5.5  Rviz窗口， Add -> By topic -> obstacle/obstacle_view
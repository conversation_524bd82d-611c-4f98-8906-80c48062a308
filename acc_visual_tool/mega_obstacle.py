
import os
import cv2
import numpy as np
from scipy.spatial.transform import Rotation as R

_COLORS = np.array(
    [
        [0.635, 0.078, 0.184],
        [0.300, 0.300, 0.300],
        [0.600, 0.600, 0.600],
        [0.000, 0.000, 0.800],
        [0.749, 0.749, 0.000],
        [0.000, 1.000, 0.000],
        [0.700, 0.100, 1.000],
        [0.667, 0.000, 1.000],
        [0.333, 0.333, 0.000],
        [0.333, 0.667, 0.000],
        [0.333, 1.000, 0.000],
        [0.667, 0.333, 0.000]]) * 255

line_thickness = 2
    
def draw_obstacles(sensor_image, obstacle_raws, K, carla=False):
    # draw 2D obstacles
    for i in range(len(obstacle_raws.perception_obstacle)-4):
        obstacle = obstacle_raws.perception_obstacle[i]
        color = _COLORS[obstacle.sub_type]
        cords_x_y_z = np.array([[obstacle.polygon_point[j].x, obstacle.polygon_point[j].y, obstacle.polygon_point[j].z] for j in range(8)])
        corners_rgb, _ = cv2.projectPoints(cords_x_y_z, np.zeros((3, 1), dtype=np.float32), np.zeros(3, dtype=np.float32), K, np.zeros(5, dtype=np.float32))
        corners_rgb = corners_rgb.astype(np.int32)

        # Draw the sides (first)
        for k in range(4):
            cv2.line(sensor_image, tuple(corners_rgb[k][0]), tuple(corners_rgb[k+4][0]), color,
                    thickness=line_thickness)

        # Draw front (in red) and back (in blue) face.
        cv2.polylines(sensor_image, [corners_rgb[:4].astype(np.int32)], True, color, thickness=line_thickness)
        cv2.polylines(sensor_image, [corners_rgb[4:].astype(np.int32)], True, color, thickness=line_thickness)
    
    # debug 用 2D 检测点
    colors_ = [(255,0,0), (0, 255, 0), (0,0,255), (125,125,125)]
    # carla 是 3d -> 2d, camera 是 2D 监测点 
    matrix = np.float32([[ 2.45140999e-01, -3.02700183e-01,  8.63369976e+02],
                    [-8.12048841e-15, -1.60710221e-01,  5.29500157e+02],
                    [-1.31190904e-17, -3.14352921e-04,  1.00000000e+00]])
    
    for i in range(4):
        ind = i - 4
        obstacle = obstacle_raws.perception_obstacle[ind]
        if carla:
            lane_point_ipm = []
            for j in range(len(obstacle.trajectory.trajectory_point)):
                path_point = obstacle.trajectory.trajectory_point[j].path_point
                lane_point_ipm.append([(path_point.x * 10 + 100) * 4, (-1 * (path_point.y * 10 - 800)) * 4])
            if len(lane_point_ipm) == 0:
                continue
            points = np.float32(lane_point_ipm).reshape(-1,1,2)
            lane_point_2d = cv2.perspectiveTransform(points, matrix).reshape(-1, 2)
            for point in lane_point_2d:
                cv2.circle(sensor_image, (int(point[0]), int(point[1])), 2, colors_[i], thickness=line_thickness+5)

        else:
            for j in range(len(obstacle.trajectory.trajectory_point)):
                path_point = obstacle.trajectory.trajectory_point[j].path_point
                cv2.circle(sensor_image, (int(path_point.x), int(path_point.y)), 2, colors_[i], thickness=line_thickness+5)

def draw_pathpoint(sensor_image, pathpoint, K):
    cords_x_y_z = np.array(pathpoint)
    corners_rgb, _ = cv2.projectPoints(cords_x_y_z, np.zeros((3, 1), dtype=np.float32), np.zeros(3, dtype=np.float32), K, np.zeros(5, dtype=np.float32))
    corners_rgb = corners_rgb.astype(np.int32)

    # Draw the sides (first)
    for k in range(len(corners_rgb)-1):
        cv2.line(sensor_image, tuple(corners_rgb[k][0]), tuple(corners_rgb[k+1][0]), (0, 0, 255),
                thickness=line_thickness)

def boxes3d_corners(location, theta, dimensions):
    corners = np.array([[0.5, 0.5, 0.5, 0.5, -0.5, -0.5, -0.5, -0.5],
                        [0.5, -0.5, -0.5, 0.5, 0.5, -0.5, -0.5, 0.5],
                        [0.5, 0.5, -0.5, -0.5, 0.5, 0.5, -0.5, -0.5]]) # 3x8 
    corners = np.diag(dimensions) @ corners;  #l, w, h
    r = R.from_euler('z', theta, degrees=False)
    corners = r.as_matrix() @ corners
    corners += location
    return corners.transpose()
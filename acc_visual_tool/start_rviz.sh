#!/bin/bash

# Start roscore in the background
source /opt/ros/melodic/setup.sh

# Function to check if roscore is running
is_roscore_running() {
    pgrep -x "roscore" > /dev/null
}

# Function to start roscore
start_roscore() {
    echo "Starting roscore..."
    roscore &
    ROSCORE_PID=$!
    # Optionally wait a few seconds to ensure roscore has time to start
    sleep 5
}

# Check if roscore is running
if is_roscore_running; then
    echo "roscore is already running."
else
    echo "roscore is not running."
    start_roscore
fi

# Wait a few seconds for roscore to start
sleep 5

# Start rqt
source /opt/ros/melodic/setup.sh
rqt --perspective-file Default.perspective &

# sleep 20
# wmctrl -l >> 1.txt
# wmctrl -r "Default.perspective - rqt" -e 0,1280,0,1280,500 >> 1.txt

# Run your Python script
source /opt/ros/melodic/setup.sh
source ~/secondary_build_ws/devel/setup.bash --extend
export PYTHONPATH=/tmp/linux_cyber_1_8:/tmp/linux_cyber_1_8/linux_build/python/cyber/python/internal/:/usr/local/third/x86_64/cyber_linux/lib/:$PYTHONPATH
export PYTHONPATH=~/secondary_build_ws/devel/lib/python3/dist-packages:$PYTHONPATH
export LD_LIBRARY_PATH=/tmp/linux_cyber_1_8/linux_build/output/lib/:$LD_LIBRARY_PATH
python3 main.py

kill $ROSCORE_PID

{"keys": {}, "groups": {"mainwindow": {"keys": {"geometry": {"repr(QByteArray.hex)": "QtCore.QByteArray(b'01d9d0cb00030000000000480000001b000009ff0000059f0000004800000040000009ff0000059f00000000000000000a000000004800000040000009ff0000059f')", "type": "repr(QByteArray.hex)", "pretty-print": "     H       H @          H @    "}, "state": {"repr(QByteArray.hex)": "QtCore.QByteArray(b'000000ff00000000fd0000000100000003000009b800000534fc0100000004fc000000000000045c000000d700fffffffc0200000003fb0000005a007200710074005f0069006d006100670065005f0076006900650077005f005f0049006d0061006700650056006900650077005f005f0031005f005f0049006d00610067006500560069006500770057006900640067006500740100000016000002fe0000005100fffffffb00000042007200710074005f0070006c006f0074005f005f0050006c006f0074005f005f0031005f005f00440061007400610050006c006f007400570069006400670065007401000002d4000002760000000000000000fb0000004c007200710074005f0074006f007000690063005f005f0054006f0070006900630050006c007500670069006e005f005f0031005f005f0054006f007000690063005700690064006700650074010000031a000002300000006d00fffffffc0000032a000002b00000000000fffffffc0200000001fb00000046004d0075006c007400690070006c006f00740050006c007500670069006e005f005f0031005f005f004d0075006c007400690070006c006f007400570069006400670065007401000003590000020c0000000000000000fb00000042007200710074005f006e00610076005f0076006900650077005f005f004e0061007600690067006100740069006f006e0056006900650077005f005f0031005f005f01000008c3000000f50000000000000000fc0000046200000556000003bc00fffffffc0200000002fb00000026007200710074005f007200760069007a005f005f005200560069007a005f005f0031005f005f01000000160000028c0000008600fffffffb00000026007200710074005f007200760069007a005f005f005200560069007a005f005f0032005f005f01000002a8000002a2000000c500ffffff000009b80000000000000004000000040000000800000008fc00000001000000030000000100000036004d0069006e0069006d0069007a006500640044006f0063006b00570069006400670065007400730054006f006f006c0062006100720000000000ffffffff0000000000000000')", "type": "repr(QByteArray.hex)", "pretty-print": "                   \\                                                            Q                                         v                                                0 m               FMultiplotPlugin__1__MultiplotWidget          Brqt_nav_view__NavigationView__1__          b V                                                                                                                           "}}, "groups": {"toolbar_areas": {"keys": {"MinimizedDockWidgetsToolbar": {"repr": "8", "type": "repr"}}, "groups": {}}}}, "pluginmanager": {"keys": {"running-plugins": {"repr": "{'rqt_image_view/ImageView': [1]}", "type": "repr"}}, "groups": {"plugin__rqt_image_view__ImageView__1": {"keys": {}, "groups": {"dock_widget__ImageViewWidget": {"keys": {"dock_widget_title": {"repr": "'Image View'", "type": "repr"}, "dockable": {"repr": "True", "type": "repr"}, "parent": {"repr": "None", "type": "repr"}}, "groups": {}}, "plugin": {"keys": {"dynamic_range": {"repr": "False", "type": "repr"}, "max_range": {"repr": "10.0", "type": "repr"}, "mouse_pub_topic": {"repr": "'/sensor/camera/image_view_mouse_left'", "type": "repr"}, "num_gridlines": {"repr": "0", "type": "repr"}, "publish_click_location": {"repr": "False", "type": "repr"}, "rotate": {"repr": "0", "type": "repr"}, "smooth_image": {"repr": "False", "type": "repr"}, "toolbar_hidden": {"repr": "False", "type": "repr"}, "topic": {"repr": "'/sensor/camera/image_view'", "type": "repr"}, "zoom1": {"repr": "False", "type": "repr"}}, "groups": {}}}}}}}}
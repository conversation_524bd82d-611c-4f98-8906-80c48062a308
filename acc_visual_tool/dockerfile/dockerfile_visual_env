FROM osrf/ros:melodic-desktop-full
RUN apt-get update && apt-get upgrade -y && apt-get install --assume-yes apt-utils && env DEBIAN_FRONTEND=noninteractive apt-get install -y tzdata
RUN apt-get install -y --fix-missing                   \
            build-essential                       \
            wget                                  \
            git                                   \
            autoconf                              \
            automake                              \
            autopoint                             \
            iputils-ping                          \
            net-tools                             \
            python3-pip                           \
            python-numpy                    
RUN pip3 install pyyaml && pip3 install numpy && pip3 install scikit-build
RUN  pip3 install --upgrade pip setuptools wheel
RUN pip3 install opencv-python && pip3 install pycyber
RUN pip3 install rospkg && pip3 install scipy
#!/bin/bash
echo `pwd`

set -x
xhost +
display="${DISPLAY:-:0}"
docker_tag='20250122'
docker_image_name="harbor-local.i-tetris.com/adas/acc_visual_tool:${docker_tag}"
# docker_image_name="osrf/ros:melodic-desktop-full"
SCRIPT=$(readlink -f $0)
BASEDIR=$(dirname $SCRIPT)

docker_container_name="acc-visual-${docker_tag}"



filter_docker_image_name=$(docker images --filter=reference=$docker_image_name)
echo $filter_docker_image_name
image_line=$(echo "$filter_docker_image_name" | sed -n '2p')
if [[ -z $image_line ]];then
    info_print "docker pull $docker_image_name"
    docker pull $docker_image_name
fi


error_print(){
    echo -e "\033[33m$1 \033[0m"
}

fatal_print(){
    echo -e "\033[31m$1 \033[0m"
}

info_print(){
    echo -e "\033[32m$1 \033[0m"
}


docker_clean(){
    docker stop $docker_container_name
    docker rm $docker_container_name
}

docker_run(){
    docker ps -aq | grep $docker_container_name
    if [ $? -eq 0 ];
    then
        docker_clean
    fi
    docker run -dt --name $docker_container_name                                  \
                -e DISPLAY=${display}            \
                -e DOCKER_IMG="${docker_image_name}"                                                    \
                --privileged                                                                            \
                --net=host                                                                              \
                --ipc=host                                                                              \
                --shm-size '2G'                                                                         \
                --env QT_X11_NO_MITSHM=1                                                                \
                --pid=host                                                                              \
                -w /acc_visual_tool                                                     \
                -v $(pwd):/acc_visual_tool:rw                                        \
                ${docker_image_name}
}

docker_exec(){
    docker exec -dt -w /acc_visual_tool $docker_container_name ./start_rviz.sh &
    info_print "docker launch"
}

docker_stop(){
    #docker exec -dt -w /workdir/adas-core/build $docker_container_name bash -c "./adas_launch stop debug_apa_hmi.launch"
    info_print "docker stop"
    #docker exec -dt -w /noa_data_collection/code/lidar $docker_container_name bash -c "kill `pgrep -f ./build/livox_cyber_transmitter`"
}


docker_entry(){
    docker exec -it $docker_container_name /bin/bash
}


if [ "X$1" = "X-r" ]
then
    info_print "-r run all modules"
    docker_clean
    docker ps | grep $docker_container_name
    if [ $? -eq 0 ];
    then
        docker_exec
    else
        docker_run
        docker_exec
    fi
    if [ $? -eq 0 ];
    then
        info_print "run success!"
    else
        fatal_print "run failed!"
    fi
fi

if [ "X$1" = "X-c" ]
then
    info_print "-c clean docker container!"
    docker_clean
    if [ $? -eq 0 ];
    then
        info_print "clean success!"
    else
        fatal_print "clean failed!"
    fi
fi


if [ "X$1" = "X-e" ]
then
    info_print "-e entry docker container!"
    docker ps | grep $docker_container_name
    if [ $? -eq 0 ];
    then
        docker_entry
    else
        docker_run
        docker_entry
    fi
    if [ $? -eq 0 ];
    then
        info_print "run success!"
    else
        fatal_print "run failed!"
    fi
fi

if [ "X$1" = "X-s" ]
then
    info_print "-s stop modules!"
    docker ps | grep $docker_container_name
    if [ $? -eq 0 ];
    then
        docker_stop
    fi
    if [ $? -eq 0 ];
    then
        info_print "stop success!"
    else
        fatal_print "stop failed!"
    fi
fi

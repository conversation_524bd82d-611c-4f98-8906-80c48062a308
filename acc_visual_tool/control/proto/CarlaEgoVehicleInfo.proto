syntax = "proto3";
package carla_msgs;

message CarlaEgoVehicleInfo {
    int32 id = 1;
    string type = 2;
    string rolename = 3;
    repeated CarlaEgoVehicleInfoWheel wheels = 4;
    double max_rpm = 5;
    double moi = 6;
    double damping_rate_full_throttle = 7;
    double damping_rate_zero_throttle_clutch_engaged = 8;
    double damping_rate_zero_throttle_clutch_disengaged = 9;
    bool use_gear_autobox = 10;
    double gear_switch_time = 11;
    double clutch_strength = 12;
    double mass = 13;
    double drag_coefficient = 14;
    Vec3 center_of_mass = 15;
}

message CarlaEgoVehicleInfoWheel {
    double tire_friction = 1;
    double damping_rate = 2;
    double max_steer_angle = 3;
    double radius = 4;
    double max_brake_torque = 5;
    double max_handbrake_torque = 6;
    Vec3 position = 7;
}

message Vec3 {
    double x = 1;
    double y = 2;
    double z = 3;
}
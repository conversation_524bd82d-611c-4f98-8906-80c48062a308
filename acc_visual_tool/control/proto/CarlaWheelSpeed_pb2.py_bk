# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/CarlaWheelSpeed.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/CarlaWheelSpeed.proto',
  package='rainbowdash.drivers',
  syntax='proto3',
  serialized_pb=_b('\n#control/proto/CarlaWheelSpeed.proto\x12\x13rainbowdash.drivers\x1a\x1a\x63ontrol/proto/header.proto\"\xa6\x03\n\nWheelSpeed\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x32\n\x05valid\x18\x02 \x01(\x0e\x32#.rainbowdash.common.CommonBool.Enum\x12\x15\n\rwheelspeed_fl\x18\x03 \x01(\x01\x12\x15\n\rwheelspeed_fr\x18\x04 \x01(\x01\x12\x15\n\rwheelspeed_rl\x18\x05 \x01(\x01\x12\x15\n\rwheelspeed_rr\x18\x06 \x01(\x01\x12\x14\n\x0cwheelsign_fl\x18\x07 \x01(\x05\x12\x14\n\x0cwheelsign_fr\x18\x08 \x01(\x05\x12\x14\n\x0cwheelsign_rl\x18\t \x01(\x05\x12\x14\n\x0cwheelsign_rr\x18\n \x01(\x05\x12\x19\n\x11wheel_edgessum_fl\x18\x0b \x01(\r\x12\x19\n\x11wheel_edgessum_fr\x18\x0c \x01(\r\x12\x19\n\x11wheel_edgessum_rl\x18\r \x01(\r\x12\x19\n\x11wheel_edgessum_rr\x18\x0e \x01(\r\x12\x18\n\x10measurement_time\x18\x0f \x01(\x01\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_WHEELSPEED = _descriptor.Descriptor(
  name='WheelSpeed',
  full_name='rainbowdash.drivers.WheelSpeed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='rainbowdash.drivers.WheelSpeed.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='valid', full_name='rainbowdash.drivers.WheelSpeed.valid', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelspeed_fl', full_name='rainbowdash.drivers.WheelSpeed.wheelspeed_fl', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelspeed_fr', full_name='rainbowdash.drivers.WheelSpeed.wheelspeed_fr', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelspeed_rl', full_name='rainbowdash.drivers.WheelSpeed.wheelspeed_rl', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelspeed_rr', full_name='rainbowdash.drivers.WheelSpeed.wheelspeed_rr', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelsign_fl', full_name='rainbowdash.drivers.WheelSpeed.wheelsign_fl', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelsign_fr', full_name='rainbowdash.drivers.WheelSpeed.wheelsign_fr', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelsign_rl', full_name='rainbowdash.drivers.WheelSpeed.wheelsign_rl', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheelsign_rr', full_name='rainbowdash.drivers.WheelSpeed.wheelsign_rr', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheel_edgessum_fl', full_name='rainbowdash.drivers.WheelSpeed.wheel_edgessum_fl', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheel_edgessum_fr', full_name='rainbowdash.drivers.WheelSpeed.wheel_edgessum_fr', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheel_edgessum_rl', full_name='rainbowdash.drivers.WheelSpeed.wheel_edgessum_rl', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheel_edgessum_rr', full_name='rainbowdash.drivers.WheelSpeed.wheel_edgessum_rr', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='measurement_time', full_name='rainbowdash.drivers.WheelSpeed.measurement_time', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=511,
)

_WHEELSPEED.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_WHEELSPEED.fields_by_name['valid'].enum_type = control_dot_proto_dot_header__pb2._COMMONBOOL_ENUM
DESCRIPTOR.message_types_by_name['WheelSpeed'] = _WHEELSPEED

WheelSpeed = _reflection.GeneratedProtocolMessageType('WheelSpeed', (_message.Message,), dict(
  DESCRIPTOR = _WHEELSPEED,
  __module__ = 'control.proto.CarlaWheelSpeed_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.drivers.WheelSpeed)
  ))
_sym_db.RegisterMessage(WheelSpeed)


# @@protoc_insertion_point(module_scope)

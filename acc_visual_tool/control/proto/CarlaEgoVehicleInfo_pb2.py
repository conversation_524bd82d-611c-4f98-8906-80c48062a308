# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/CarlaEgoVehicleInfo.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/CarlaEgoVehicleInfo.proto',
  package='carla_msgs',
  syntax='proto3',
  serialized_pb=_b('\n\'control/proto/CarlaEgoVehicleInfo.proto\x12\ncarla_msgs\"\xc1\x03\n\x13\x43\x61rlaEgoVehicleInfo\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x10\n\x08rolename\x18\x03 \x01(\t\x12\x34\n\x06wheels\x18\x04 \x03(\x0b\x32$.carla_msgs.CarlaEgoVehicleInfoWheel\x12\x0f\n\x07max_rpm\x18\x05 \x01(\x01\x12\x0b\n\x03moi\x18\x06 \x01(\x01\x12\"\n\x1a\x64\x61mping_rate_full_throttle\x18\x07 \x01(\x01\x12\x31\n)damping_rate_zero_throttle_clutch_engaged\x18\x08 \x01(\x01\x12\x34\n,damping_rate_zero_throttle_clutch_disengaged\x18\t \x01(\x01\x12\x18\n\x10use_gear_autobox\x18\n \x01(\x08\x12\x18\n\x10gear_switch_time\x18\x0b \x01(\x01\x12\x17\n\x0f\x63lutch_strength\x18\x0c \x01(\x01\x12\x0c\n\x04mass\x18\r \x01(\x01\x12\x18\n\x10\x64rag_coefficient\x18\x0e \x01(\x01\x12(\n\x0e\x63\x65nter_of_mass\x18\x0f \x01(\x0b\x32\x10.carla_msgs.Vec3\"\xcc\x01\n\x18\x43\x61rlaEgoVehicleInfoWheel\x12\x15\n\rtire_friction\x18\x01 \x01(\x01\x12\x14\n\x0c\x64\x61mping_rate\x18\x02 \x01(\x01\x12\x17\n\x0fmax_steer_angle\x18\x03 \x01(\x01\x12\x0e\n\x06radius\x18\x04 \x01(\x01\x12\x18\n\x10max_brake_torque\x18\x05 \x01(\x01\x12\x1c\n\x14max_handbrake_torque\x18\x06 \x01(\x01\x12\"\n\x08position\x18\x07 \x01(\x0b\x32\x10.carla_msgs.Vec3\"\'\n\x04Vec3\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\x62\x06proto3')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_CARLAEGOVEHICLEINFO = _descriptor.Descriptor(
  name='CarlaEgoVehicleInfo',
  full_name='carla_msgs.CarlaEgoVehicleInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carla_msgs.CarlaEgoVehicleInfo.id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='type', full_name='carla_msgs.CarlaEgoVehicleInfo.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='rolename', full_name='carla_msgs.CarlaEgoVehicleInfo.rolename', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheels', full_name='carla_msgs.CarlaEgoVehicleInfo.wheels', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_rpm', full_name='carla_msgs.CarlaEgoVehicleInfo.max_rpm', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='moi', full_name='carla_msgs.CarlaEgoVehicleInfo.moi', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='damping_rate_full_throttle', full_name='carla_msgs.CarlaEgoVehicleInfo.damping_rate_full_throttle', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='damping_rate_zero_throttle_clutch_engaged', full_name='carla_msgs.CarlaEgoVehicleInfo.damping_rate_zero_throttle_clutch_engaged', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='damping_rate_zero_throttle_clutch_disengaged', full_name='carla_msgs.CarlaEgoVehicleInfo.damping_rate_zero_throttle_clutch_disengaged', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='use_gear_autobox', full_name='carla_msgs.CarlaEgoVehicleInfo.use_gear_autobox', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gear_switch_time', full_name='carla_msgs.CarlaEgoVehicleInfo.gear_switch_time', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='clutch_strength', full_name='carla_msgs.CarlaEgoVehicleInfo.clutch_strength', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='mass', full_name='carla_msgs.CarlaEgoVehicleInfo.mass', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='drag_coefficient', full_name='carla_msgs.CarlaEgoVehicleInfo.drag_coefficient', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='center_of_mass', full_name='carla_msgs.CarlaEgoVehicleInfo.center_of_mass', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=56,
  serialized_end=505,
)


_CARLAEGOVEHICLEINFOWHEEL = _descriptor.Descriptor(
  name='CarlaEgoVehicleInfoWheel',
  full_name='carla_msgs.CarlaEgoVehicleInfoWheel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tire_friction', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.tire_friction', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='damping_rate', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.damping_rate', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_steer_angle', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.max_steer_angle', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='radius', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.radius', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_brake_torque', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.max_brake_torque', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_handbrake_torque', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.max_handbrake_torque', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='position', full_name='carla_msgs.CarlaEgoVehicleInfoWheel.position', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=508,
  serialized_end=712,
)


_VEC3 = _descriptor.Descriptor(
  name='Vec3',
  full_name='carla_msgs.Vec3',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='carla_msgs.Vec3.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='y', full_name='carla_msgs.Vec3.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='z', full_name='carla_msgs.Vec3.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=714,
  serialized_end=753,
)

_CARLAEGOVEHICLEINFO.fields_by_name['wheels'].message_type = _CARLAEGOVEHICLEINFOWHEEL
_CARLAEGOVEHICLEINFO.fields_by_name['center_of_mass'].message_type = _VEC3
_CARLAEGOVEHICLEINFOWHEEL.fields_by_name['position'].message_type = _VEC3
DESCRIPTOR.message_types_by_name['CarlaEgoVehicleInfo'] = _CARLAEGOVEHICLEINFO
DESCRIPTOR.message_types_by_name['CarlaEgoVehicleInfoWheel'] = _CARLAEGOVEHICLEINFOWHEEL
DESCRIPTOR.message_types_by_name['Vec3'] = _VEC3

CarlaEgoVehicleInfo = _reflection.GeneratedProtocolMessageType('CarlaEgoVehicleInfo', (_message.Message,), dict(
  DESCRIPTOR = _CARLAEGOVEHICLEINFO,
  __module__ = 'control.proto.CarlaEgoVehicleInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.CarlaEgoVehicleInfo)
  ))
_sym_db.RegisterMessage(CarlaEgoVehicleInfo)

CarlaEgoVehicleInfoWheel = _reflection.GeneratedProtocolMessageType('CarlaEgoVehicleInfoWheel', (_message.Message,), dict(
  DESCRIPTOR = _CARLAEGOVEHICLEINFOWHEEL,
  __module__ = 'control.proto.CarlaEgoVehicleInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.CarlaEgoVehicleInfoWheel)
  ))
_sym_db.RegisterMessage(CarlaEgoVehicleInfoWheel)

Vec3 = _reflection.GeneratedProtocolMessageType('Vec3', (_message.Message,), dict(
  DESCRIPTOR = _VEC3,
  __module__ = 'control.proto.CarlaEgoVehicleInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.Vec3)
  ))
_sym_db.RegisterMessage(Vec3)


# @@protoc_insertion_point(module_scope)

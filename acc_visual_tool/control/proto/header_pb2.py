# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/header.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/header.proto',
  package='rainbowdash.common',
  syntax='proto3',
  serialized_pb=_b('\n\x1a\x63ontrol/proto/header.proto\x12\x12rainbowdash.common\"t\n\x0c\x46\x41paScenario\"d\n\x04\x45num\x12\x08\n\x04NONE\x10\x00\x12\x18\n\x14TRACKING_AND_REVERSE\x10\x01\x12\x0e\n\nPARKING_IN\x10\x02\x12\x0f\n\x0bPARKING_OUT\x10\x03\x12\x17\n\x13\x43USTOM_DEFINE_STALL\x10\x04\"Q\n\x13ParkingOutDirection\":\n\x04\x45num\x12\x08\n\x04NONE\x10\x00\x12\t\n\x05\x46RONT\x10\x01\x12\x08\n\x04REAR\x10\x02\x12\x08\n\x04LEFT\x10\x03\x12\t\n\x05RIGHT\x10\x04\"Z\n\x12\x43ommonGearPosition\"D\n\x04\x45num\x12\x08\n\x04NONE\x10\x00\x12\x0b\n\x07NEUTRAL\x10\x01\x12\x0b\n\x07\x44RIVING\x10\x02\x12\x0b\n\x07REVERSE\x10\x03\x12\x0b\n\x07PARKING\x10\x04\"3\n\nCommonBool\"%\n\x04\x45num\x12\x08\n\x04NONE\x10\x00\x12\x08\n\x04TRUE\x10\x01\x12\t\n\x05\x46\x41LSE\x10\x02\"[\n\x08SlotType\"O\n\x04\x45num\x12\x08\n\x04NONE\x10\x00\x12\x0c\n\x08VERTICAL\x10\x01\x12\x0c\n\x08PARALLEL\x10\x02\x12\x0b\n\x07OBLIQUE\x10\x03\x12\t\n\x05TRACK\x10\x04\x12\t\n\x05OTHER\x10\n\"\xb7\x01\n\x0f\x43ommonErrorType\"\xa3\x01\n\x04\x45num\x12\x08\n\x04NONE\x10\x00\x12(\n$CURRENT_IGNITION_CYCLE_UNRECOVERABLE\x10\x01\x12\x43\n?CURRENT_IGNITION_CYCLE_RECOVERABLE_BUT_TASK_CYCLE_UNRECOVERABLE\x10\x02\x12\"\n\x1e\x43URRENT_TASK_CYCLE_RECOVERABLE\x10\x03\"\x94\x01\n\x06Header\x12\x11\n\ttimestamp\x18\x01 \x01(\x01\x12\x13\n\x0bmodule_name\x18\x02 \x01(\t\x12\x14\n\x0csequence_num\x18\x03 \x01(\r\x12\x10\n\x08\x66rame_id\x18\x04 \x01(\t\x12\x15\n\rtrigger_frame\x18\x05 \x01(\t\x12\x0f\n\x07version\x18\x06 \x01(\r\x12\x12\n\nsession_id\x18\x07 \x01(\x04\"\x15\n\x06Verify\x12\x0b\n\x03val\x18\x01 \x01(\rb\x06proto3')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)



_FAPASCENARIO_ENUM = _descriptor.EnumDescriptor(
  name='Enum',
  full_name='rainbowdash.common.FApaScenario.Enum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRACKING_AND_REVERSE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PARKING_IN', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PARKING_OUT', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CUSTOM_DEFINE_STALL', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=66,
  serialized_end=166,
)
_sym_db.RegisterEnumDescriptor(_FAPASCENARIO_ENUM)

_PARKINGOUTDIRECTION_ENUM = _descriptor.EnumDescriptor(
  name='Enum',
  full_name='rainbowdash.common.ParkingOutDirection.Enum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FRONT', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REAR', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEFT', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RIGHT', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=191,
  serialized_end=249,
)
_sym_db.RegisterEnumDescriptor(_PARKINGOUTDIRECTION_ENUM)

_COMMONGEARPOSITION_ENUM = _descriptor.EnumDescriptor(
  name='Enum',
  full_name='rainbowdash.common.CommonGearPosition.Enum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NEUTRAL', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DRIVING', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REVERSE', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PARKING', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=273,
  serialized_end=341,
)
_sym_db.RegisterEnumDescriptor(_COMMONGEARPOSITION_ENUM)

_COMMONBOOL_ENUM = _descriptor.EnumDescriptor(
  name='Enum',
  full_name='rainbowdash.common.CommonBool.Enum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRUE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FALSE', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=357,
  serialized_end=394,
)
_sym_db.RegisterEnumDescriptor(_COMMONBOOL_ENUM)

_SLOTTYPE_ENUM = _descriptor.EnumDescriptor(
  name='Enum',
  full_name='rainbowdash.common.SlotType.Enum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='VERTICAL', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PARALLEL', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OBLIQUE', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRACK', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OTHER', index=5, number=10,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=408,
  serialized_end=487,
)
_sym_db.RegisterEnumDescriptor(_SLOTTYPE_ENUM)

_COMMONERRORTYPE_ENUM = _descriptor.EnumDescriptor(
  name='Enum',
  full_name='rainbowdash.common.CommonErrorType.Enum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CURRENT_IGNITION_CYCLE_UNRECOVERABLE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CURRENT_IGNITION_CYCLE_RECOVERABLE_BUT_TASK_CYCLE_UNRECOVERABLE', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CURRENT_TASK_CYCLE_RECOVERABLE', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=510,
  serialized_end=673,
)
_sym_db.RegisterEnumDescriptor(_COMMONERRORTYPE_ENUM)


_FAPASCENARIO = _descriptor.Descriptor(
  name='FApaScenario',
  full_name='rainbowdash.common.FApaScenario',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _FAPASCENARIO_ENUM,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=50,
  serialized_end=166,
)


_PARKINGOUTDIRECTION = _descriptor.Descriptor(
  name='ParkingOutDirection',
  full_name='rainbowdash.common.ParkingOutDirection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _PARKINGOUTDIRECTION_ENUM,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=168,
  serialized_end=249,
)


_COMMONGEARPOSITION = _descriptor.Descriptor(
  name='CommonGearPosition',
  full_name='rainbowdash.common.CommonGearPosition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _COMMONGEARPOSITION_ENUM,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=251,
  serialized_end=341,
)


_COMMONBOOL = _descriptor.Descriptor(
  name='CommonBool',
  full_name='rainbowdash.common.CommonBool',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _COMMONBOOL_ENUM,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=343,
  serialized_end=394,
)


_SLOTTYPE = _descriptor.Descriptor(
  name='SlotType',
  full_name='rainbowdash.common.SlotType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _SLOTTYPE_ENUM,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=396,
  serialized_end=487,
)


_COMMONERRORTYPE = _descriptor.Descriptor(
  name='CommonErrorType',
  full_name='rainbowdash.common.CommonErrorType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _COMMONERRORTYPE_ENUM,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=490,
  serialized_end=673,
)


_HEADER = _descriptor.Descriptor(
  name='Header',
  full_name='rainbowdash.common.Header',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='rainbowdash.common.Header.timestamp', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='module_name', full_name='rainbowdash.common.Header.module_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sequence_num', full_name='rainbowdash.common.Header.sequence_num', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='frame_id', full_name='rainbowdash.common.Header.frame_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='trigger_frame', full_name='rainbowdash.common.Header.trigger_frame', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='version', full_name='rainbowdash.common.Header.version', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='session_id', full_name='rainbowdash.common.Header.session_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=676,
  serialized_end=824,
)


_VERIFY = _descriptor.Descriptor(
  name='Verify',
  full_name='rainbowdash.common.Verify',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='val', full_name='rainbowdash.common.Verify.val', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=826,
  serialized_end=847,
)

_FAPASCENARIO_ENUM.containing_type = _FAPASCENARIO
_PARKINGOUTDIRECTION_ENUM.containing_type = _PARKINGOUTDIRECTION
_COMMONGEARPOSITION_ENUM.containing_type = _COMMONGEARPOSITION
_COMMONBOOL_ENUM.containing_type = _COMMONBOOL
_SLOTTYPE_ENUM.containing_type = _SLOTTYPE
_COMMONERRORTYPE_ENUM.containing_type = _COMMONERRORTYPE
DESCRIPTOR.message_types_by_name['FApaScenario'] = _FAPASCENARIO
DESCRIPTOR.message_types_by_name['ParkingOutDirection'] = _PARKINGOUTDIRECTION
DESCRIPTOR.message_types_by_name['CommonGearPosition'] = _COMMONGEARPOSITION
DESCRIPTOR.message_types_by_name['CommonBool'] = _COMMONBOOL
DESCRIPTOR.message_types_by_name['SlotType'] = _SLOTTYPE
DESCRIPTOR.message_types_by_name['CommonErrorType'] = _COMMONERRORTYPE
DESCRIPTOR.message_types_by_name['Header'] = _HEADER
DESCRIPTOR.message_types_by_name['Verify'] = _VERIFY

FApaScenario = _reflection.GeneratedProtocolMessageType('FApaScenario', (_message.Message,), dict(
  DESCRIPTOR = _FAPASCENARIO,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.FApaScenario)
  ))
_sym_db.RegisterMessage(FApaScenario)

ParkingOutDirection = _reflection.GeneratedProtocolMessageType('ParkingOutDirection', (_message.Message,), dict(
  DESCRIPTOR = _PARKINGOUTDIRECTION,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.ParkingOutDirection)
  ))
_sym_db.RegisterMessage(ParkingOutDirection)

CommonGearPosition = _reflection.GeneratedProtocolMessageType('CommonGearPosition', (_message.Message,), dict(
  DESCRIPTOR = _COMMONGEARPOSITION,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.CommonGearPosition)
  ))
_sym_db.RegisterMessage(CommonGearPosition)

CommonBool = _reflection.GeneratedProtocolMessageType('CommonBool', (_message.Message,), dict(
  DESCRIPTOR = _COMMONBOOL,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.CommonBool)
  ))
_sym_db.RegisterMessage(CommonBool)

SlotType = _reflection.GeneratedProtocolMessageType('SlotType', (_message.Message,), dict(
  DESCRIPTOR = _SLOTTYPE,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.SlotType)
  ))
_sym_db.RegisterMessage(SlotType)

CommonErrorType = _reflection.GeneratedProtocolMessageType('CommonErrorType', (_message.Message,), dict(
  DESCRIPTOR = _COMMONERRORTYPE,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.CommonErrorType)
  ))
_sym_db.RegisterMessage(CommonErrorType)

Header = _reflection.GeneratedProtocolMessageType('Header', (_message.Message,), dict(
  DESCRIPTOR = _HEADER,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.Header)
  ))
_sym_db.RegisterMessage(Header)

Verify = _reflection.GeneratedProtocolMessageType('Verify', (_message.Message,), dict(
  DESCRIPTOR = _VERIFY,
  __module__ = 'control.proto.header_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.common.Verify)
  ))
_sym_db.RegisterMessage(Verify)


# @@protoc_insertion_point(module_scope)

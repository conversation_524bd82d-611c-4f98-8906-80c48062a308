# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/pnc_point.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/pnc_point.proto',
  package='rainbowdash.planning',
  syntax='proto3',
  serialized_pb=_b('\n\x1d\x63ontrol/proto/pnc_point.proto\x12\x14rainbowdash.planning\"\x1f\n\x07SLPoint\x12\t\n\x01s\x18\x01 \x01(\x01\x12\t\n\x01l\x18\x02 \x01(\x01\"A\n\x10\x46renetFramePoint\x12\t\n\x01s\x18\x01 \x01(\x01\x12\t\n\x01l\x18\x02 \x01(\x01\x12\n\n\x02\x64l\x18\x03 \x01(\x01\x12\x0b\n\x03\x64\x64l\x18\x04 \x01(\x01\"D\n\nSpeedPoint\x12\t\n\x01s\x18\x01 \x01(\x01\x12\t\n\x01t\x18\x02 \x01(\x01\x12\t\n\x01v\x18\x03 \x01(\x01\x12\t\n\x01\x61\x18\x04 \x01(\x01\x12\n\n\x02\x64\x61\x18\x05 \x01(\x01\"\xb3\x01\n\tPathPoint\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\x12\r\n\x05theta\x18\x04 \x01(\x01\x12\r\n\x05kappa\x18\x05 \x01(\x01\x12\t\n\x01s\x18\x06 \x01(\x01\x12\x0e\n\x06\x64kappa\x18\x07 \x01(\x01\x12\x0f\n\x07\x64\x64kappa\x18\x08 \x01(\x01\x12\x0f\n\x07lane_id\x18\t \x01(\t\x12\x14\n\x0cx_derivative\x18\n \x01(\x01\x12\x14\n\x0cy_derivative\x18\x0b \x01(\x01\"I\n\x04Path\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x33\n\npath_point\x18\x02 \x03(\x0b\x32\x1f.rainbowdash.planning.PathPoint\"\xc9\x01\n\x0fTrajectoryPoint\x12\x33\n\npath_point\x18\x01 \x01(\x0b\x32\x1f.rainbowdash.planning.PathPoint\x12\t\n\x01v\x18\x02 \x01(\x01\x12\t\n\x01\x61\x18\x03 \x01(\x01\x12\x15\n\rrelative_time\x18\x04 \x01(\x01\x12\n\n\x02\x64\x61\x18\x05 \x01(\x01\x12\r\n\x05steer\x18\x06 \x01(\x01\x12\x39\n\rgaussian_info\x18\x07 \x01(\x0b\x32\".rainbowdash.planning.GaussianInfo\"[\n\nTrajectory\x12\x0c\n\x04name\x18\x01 \x01(\t\x12?\n\x10trajectory_point\x18\x02 \x03(\x0b\x32%.rainbowdash.planning.TrajectoryPoint\"d\n\x12VehicleMotionPoint\x12?\n\x10trajectory_point\x18\x01 \x01(\x0b\x32%.rainbowdash.planning.TrajectoryPoint\x12\r\n\x05steer\x18\x02 \x01(\x01\"e\n\rVehicleMotion\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x46\n\x14vehicle_motion_point\x18\x02 \x03(\x0b\x32(.rainbowdash.planning.VehicleMotionPoint\"\x96\x01\n\x0cGaussianInfo\x12\x0f\n\x07sigma_x\x18\x01 \x01(\x01\x12\x0f\n\x07sigma_y\x18\x02 \x01(\x01\x12\x13\n\x0b\x63orrelation\x18\x03 \x01(\x01\x12\x18\n\x10\x61rea_probability\x18\x04 \x01(\x01\x12\x11\n\tellipse_a\x18\x05 \x01(\x01\x12\x11\n\tellipse_b\x18\x06 \x01(\x01\x12\x0f\n\x07theta_a\x18\x07 \x01(\x01\x62\x06proto3')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_SLPOINT = _descriptor.Descriptor(
  name='SLPoint',
  full_name='rainbowdash.planning.SLPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='s', full_name='rainbowdash.planning.SLPoint.s', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='l', full_name='rainbowdash.planning.SLPoint.l', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=86,
)


_FRENETFRAMEPOINT = _descriptor.Descriptor(
  name='FrenetFramePoint',
  full_name='rainbowdash.planning.FrenetFramePoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='s', full_name='rainbowdash.planning.FrenetFramePoint.s', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='l', full_name='rainbowdash.planning.FrenetFramePoint.l', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dl', full_name='rainbowdash.planning.FrenetFramePoint.dl', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ddl', full_name='rainbowdash.planning.FrenetFramePoint.ddl', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=88,
  serialized_end=153,
)


_SPEEDPOINT = _descriptor.Descriptor(
  name='SpeedPoint',
  full_name='rainbowdash.planning.SpeedPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='s', full_name='rainbowdash.planning.SpeedPoint.s', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='t', full_name='rainbowdash.planning.SpeedPoint.t', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v', full_name='rainbowdash.planning.SpeedPoint.v', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='a', full_name='rainbowdash.planning.SpeedPoint.a', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='da', full_name='rainbowdash.planning.SpeedPoint.da', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=155,
  serialized_end=223,
)


_PATHPOINT = _descriptor.Descriptor(
  name='PathPoint',
  full_name='rainbowdash.planning.PathPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='rainbowdash.planning.PathPoint.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='y', full_name='rainbowdash.planning.PathPoint.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='z', full_name='rainbowdash.planning.PathPoint.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theta', full_name='rainbowdash.planning.PathPoint.theta', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='kappa', full_name='rainbowdash.planning.PathPoint.kappa', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='s', full_name='rainbowdash.planning.PathPoint.s', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dkappa', full_name='rainbowdash.planning.PathPoint.dkappa', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ddkappa', full_name='rainbowdash.planning.PathPoint.ddkappa', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lane_id', full_name='rainbowdash.planning.PathPoint.lane_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='x_derivative', full_name='rainbowdash.planning.PathPoint.x_derivative', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='y_derivative', full_name='rainbowdash.planning.PathPoint.y_derivative', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=405,
)


_PATH = _descriptor.Descriptor(
  name='Path',
  full_name='rainbowdash.planning.Path',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='rainbowdash.planning.Path.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path_point', full_name='rainbowdash.planning.Path.path_point', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=407,
  serialized_end=480,
)


_TRAJECTORYPOINT = _descriptor.Descriptor(
  name='TrajectoryPoint',
  full_name='rainbowdash.planning.TrajectoryPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='path_point', full_name='rainbowdash.planning.TrajectoryPoint.path_point', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='v', full_name='rainbowdash.planning.TrajectoryPoint.v', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='a', full_name='rainbowdash.planning.TrajectoryPoint.a', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='relative_time', full_name='rainbowdash.planning.TrajectoryPoint.relative_time', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='da', full_name='rainbowdash.planning.TrajectoryPoint.da', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='steer', full_name='rainbowdash.planning.TrajectoryPoint.steer', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gaussian_info', full_name='rainbowdash.planning.TrajectoryPoint.gaussian_info', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=483,
  serialized_end=684,
)


_TRAJECTORY = _descriptor.Descriptor(
  name='Trajectory',
  full_name='rainbowdash.planning.Trajectory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='rainbowdash.planning.Trajectory.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='trajectory_point', full_name='rainbowdash.planning.Trajectory.trajectory_point', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=686,
  serialized_end=777,
)


_VEHICLEMOTIONPOINT = _descriptor.Descriptor(
  name='VehicleMotionPoint',
  full_name='rainbowdash.planning.VehicleMotionPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trajectory_point', full_name='rainbowdash.planning.VehicleMotionPoint.trajectory_point', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='steer', full_name='rainbowdash.planning.VehicleMotionPoint.steer', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=779,
  serialized_end=879,
)


_VEHICLEMOTION = _descriptor.Descriptor(
  name='VehicleMotion',
  full_name='rainbowdash.planning.VehicleMotion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='rainbowdash.planning.VehicleMotion.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vehicle_motion_point', full_name='rainbowdash.planning.VehicleMotion.vehicle_motion_point', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=881,
  serialized_end=982,
)


_GAUSSIANINFO = _descriptor.Descriptor(
  name='GaussianInfo',
  full_name='rainbowdash.planning.GaussianInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sigma_x', full_name='rainbowdash.planning.GaussianInfo.sigma_x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sigma_y', full_name='rainbowdash.planning.GaussianInfo.sigma_y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='correlation', full_name='rainbowdash.planning.GaussianInfo.correlation', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='area_probability', full_name='rainbowdash.planning.GaussianInfo.area_probability', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ellipse_a', full_name='rainbowdash.planning.GaussianInfo.ellipse_a', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='ellipse_b', full_name='rainbowdash.planning.GaussianInfo.ellipse_b', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theta_a', full_name='rainbowdash.planning.GaussianInfo.theta_a', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=985,
  serialized_end=1135,
)

_PATH.fields_by_name['path_point'].message_type = _PATHPOINT
_TRAJECTORYPOINT.fields_by_name['path_point'].message_type = _PATHPOINT
_TRAJECTORYPOINT.fields_by_name['gaussian_info'].message_type = _GAUSSIANINFO
_TRAJECTORY.fields_by_name['trajectory_point'].message_type = _TRAJECTORYPOINT
_VEHICLEMOTIONPOINT.fields_by_name['trajectory_point'].message_type = _TRAJECTORYPOINT
_VEHICLEMOTION.fields_by_name['vehicle_motion_point'].message_type = _VEHICLEMOTIONPOINT
DESCRIPTOR.message_types_by_name['SLPoint'] = _SLPOINT
DESCRIPTOR.message_types_by_name['FrenetFramePoint'] = _FRENETFRAMEPOINT
DESCRIPTOR.message_types_by_name['SpeedPoint'] = _SPEEDPOINT
DESCRIPTOR.message_types_by_name['PathPoint'] = _PATHPOINT
DESCRIPTOR.message_types_by_name['Path'] = _PATH
DESCRIPTOR.message_types_by_name['TrajectoryPoint'] = _TRAJECTORYPOINT
DESCRIPTOR.message_types_by_name['Trajectory'] = _TRAJECTORY
DESCRIPTOR.message_types_by_name['VehicleMotionPoint'] = _VEHICLEMOTIONPOINT
DESCRIPTOR.message_types_by_name['VehicleMotion'] = _VEHICLEMOTION
DESCRIPTOR.message_types_by_name['GaussianInfo'] = _GAUSSIANINFO

SLPoint = _reflection.GeneratedProtocolMessageType('SLPoint', (_message.Message,), dict(
  DESCRIPTOR = _SLPOINT,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.SLPoint)
  ))
_sym_db.RegisterMessage(SLPoint)

FrenetFramePoint = _reflection.GeneratedProtocolMessageType('FrenetFramePoint', (_message.Message,), dict(
  DESCRIPTOR = _FRENETFRAMEPOINT,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.FrenetFramePoint)
  ))
_sym_db.RegisterMessage(FrenetFramePoint)

SpeedPoint = _reflection.GeneratedProtocolMessageType('SpeedPoint', (_message.Message,), dict(
  DESCRIPTOR = _SPEEDPOINT,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.SpeedPoint)
  ))
_sym_db.RegisterMessage(SpeedPoint)

PathPoint = _reflection.GeneratedProtocolMessageType('PathPoint', (_message.Message,), dict(
  DESCRIPTOR = _PATHPOINT,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.PathPoint)
  ))
_sym_db.RegisterMessage(PathPoint)

Path = _reflection.GeneratedProtocolMessageType('Path', (_message.Message,), dict(
  DESCRIPTOR = _PATH,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.Path)
  ))
_sym_db.RegisterMessage(Path)

TrajectoryPoint = _reflection.GeneratedProtocolMessageType('TrajectoryPoint', (_message.Message,), dict(
  DESCRIPTOR = _TRAJECTORYPOINT,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.TrajectoryPoint)
  ))
_sym_db.RegisterMessage(TrajectoryPoint)

Trajectory = _reflection.GeneratedProtocolMessageType('Trajectory', (_message.Message,), dict(
  DESCRIPTOR = _TRAJECTORY,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.Trajectory)
  ))
_sym_db.RegisterMessage(Trajectory)

VehicleMotionPoint = _reflection.GeneratedProtocolMessageType('VehicleMotionPoint', (_message.Message,), dict(
  DESCRIPTOR = _VEHICLEMOTIONPOINT,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.VehicleMotionPoint)
  ))
_sym_db.RegisterMessage(VehicleMotionPoint)

VehicleMotion = _reflection.GeneratedProtocolMessageType('VehicleMotion', (_message.Message,), dict(
  DESCRIPTOR = _VEHICLEMOTION,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.VehicleMotion)
  ))
_sym_db.RegisterMessage(VehicleMotion)

GaussianInfo = _reflection.GeneratedProtocolMessageType('GaussianInfo', (_message.Message,), dict(
  DESCRIPTOR = _GAUSSIANINFO,
  __module__ = 'control.proto.pnc_point_pb2'
  # @@protoc_insertion_point(class_scope:rainbowdash.planning.GaussianInfo)
  ))
_sym_db.RegisterMessage(GaussianInfo)


# @@protoc_insertion_point(module_scope)

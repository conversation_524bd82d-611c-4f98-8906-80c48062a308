syntax = "proto3";
package carla_msgs;

import "control/proto/header.proto";
import "control/proto/CarlaEgoVehicleStatus.proto";

message EgoVehicleControlInfo {
    rainbowdash.common.Header header = 1;
    EgoVehicleControlMaxima restrictions = 2;
    EgoVehicleControlTarget target = 3;
    EgoVehicleControlCurrent current = 4;
    EgoVehicleControlStatus status = 5;
    CarlaEgoVehicleControl output = 6;
}

message EgoVehicleControlMaxima {
    double max_steering_angle = 1;
    double max_speed = 2;
    double max_accel = 3;
    double max_decel = 4;
    double min_accel = 5;
    double max_pedal = 6;
}

message EgoVehicleControlTarget {
    double steering_angle = 1;
    double speed = 2;
    double speed_abs = 3;
    double accel = 4;
    double jerk = 5;
}

message EgoVehicleControlCurrent {
    double time_sec = 1;
    double speed = 2;
    double speed_abs = 3;
    double accel = 4;
}

message EgoVehicleControlStatus {
    string status = 1;
    uint32 speed_control_activation_count = 2;
    double speed_control_accel_delta = 3;
    double speed_control_accel_target = 4;
    double accel_control_pedal_delta = 5;
    double accel_control_pedal_target = 6;
    double brake_upper_border = 7;
    double throttle_lower_border = 8;
}


syntax = "proto3";
package rainbowdash.control_by_wire;

import "control/proto/header.proto";

message WheelSpeedInfo {
  rainbowdash.common.Header header = 1;
  rainbowdash.common.CommonBool.Enum valid = 2;
  double wheelspeed_fl = 3;
  double wheelspeed_fr = 4;
  double wheelspeed_rl = 5;
  double wheelspeed_rr = 6;
  int32 wheelsign_fl = 7;
  int32 wheelsign_fr = 8;
  int32 wheelsign_rl = 9;
  int32 wheelsign_rr = 10;
  uint32 wheel_edgessum_fl = 11;
  uint32 wheel_edgessum_fr = 12;
  uint32 wheel_edgessum_rl = 13;
  uint32 wheel_edgessum_rr = 14;

  double measurement_time = 15;
}
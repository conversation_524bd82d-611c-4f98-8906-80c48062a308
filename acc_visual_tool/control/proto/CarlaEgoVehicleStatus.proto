syntax = "proto3";
package carla_msgs;

import "control/proto/header.proto";

message CarlaEgoVehicleStatus {
    rainbowdash.common.Header header = 1;
    double velocity = 2;
    double acceleration = 3;
    double pitch = 4;
    double yaw = 5;
    double roll = 6;
    CarlaEgoVehicleControl control = 7;
}

message CarlaEgoVehicleControl {
    rainbowdash.common.Header header = 1;
    double throttle = 2;
    double steer = 3;
    double brake = 4;
    bool hand_brake = 5;
    bool reverse = 6;
    int32 gear = 7;
    bool manual_gear_shift = 8;
}
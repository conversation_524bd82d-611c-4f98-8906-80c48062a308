# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/follow_debug_info.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/follow_debug_info.proto',
  package='ACC_control',
  syntax='proto3',
  serialized_pb=_b('\n%control/proto/follow_debug_info.proto\x12\x0b\x41\x43\x43_control\x1a\x1a\x63ontrol/proto/header.proto\"\xfc\x01\n\x0f\x46ollowDebugInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x16\n\x0etarget_car_pos\x18\x02 \x01(\x01\x12\x13\n\x0b\x63ur_car_pos\x18\x03 \x01(\x01\x12\x11\n\tcur_car_v\x18\x04 \x01(\x01\x12\x11\n\tcur_car_a\x18\x05 \x01(\x01\x12\x11\n\tcur_car_j\x18\x06 \x01(\x01\x12\x0f\n\x07\x64is_car\x18\x07 \x01(\x01\x12\x10\n\x08reserve1\x18\x08 \x01(\x01\x12\x10\n\x08reserve2\x18\t \x01(\x01\x12\x10\n\x08reserve3\x18\n \x01(\x01\x12\x10\n\x08reserve4\x18\x0b \x01(\x01\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_FOLLOWDEBUGINFO = _descriptor.Descriptor(
  name='FollowDebugInfo',
  full_name='ACC_control.FollowDebugInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='ACC_control.FollowDebugInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='target_car_pos', full_name='ACC_control.FollowDebugInfo.target_car_pos', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cur_car_pos', full_name='ACC_control.FollowDebugInfo.cur_car_pos', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cur_car_v', full_name='ACC_control.FollowDebugInfo.cur_car_v', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cur_car_a', full_name='ACC_control.FollowDebugInfo.cur_car_a', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cur_car_j', full_name='ACC_control.FollowDebugInfo.cur_car_j', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dis_car', full_name='ACC_control.FollowDebugInfo.dis_car', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserve1', full_name='ACC_control.FollowDebugInfo.reserve1', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserve2', full_name='ACC_control.FollowDebugInfo.reserve2', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserve3', full_name='ACC_control.FollowDebugInfo.reserve3', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserve4', full_name='ACC_control.FollowDebugInfo.reserve4', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=83,
  serialized_end=335,
)

_FOLLOWDEBUGINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
DESCRIPTOR.message_types_by_name['FollowDebugInfo'] = _FOLLOWDEBUGINFO

FollowDebugInfo = _reflection.GeneratedProtocolMessageType('FollowDebugInfo', (_message.Message,), dict(
  DESCRIPTOR = _FOLLOWDEBUGINFO,
  __module__ = 'control.proto.follow_debug_info_pb2'
  # @@protoc_insertion_point(class_scope:ACC_control.FollowDebugInfo)
  ))
_sym_db.RegisterMessage(FollowDebugInfo)


# @@protoc_insertion_point(module_scope)

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/sim_vehicle.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/sim_vehicle.proto',
  package='sim_vehicle.control_io',
  syntax='proto3',
  serialized_pb=_b('\n\x1f\x63ontrol/proto/sim_vehicle.proto\x12\x16sim_vehicle.control_io\x1a\x1a\x63ontrol/proto/header.proto\"\x8f\x01\n\x16SimVehicleContorlInput\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\r\n\x05\x61\x63\x63_i\x18\x02 \x01(\x01\x12\x0e\n\x06gear_i\x18\x03 \x01(\x05\x12\x13\n\x0bis_lat_ctrl\x18\x04 \x01(\x08\x12\x15\n\rwheel_angle_i\x18\x05 \x01(\x01\"\xb5\x01\n\x17SimVehicleContorlOutput\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x0f\n\x07speed_o\x18\x02 \x01(\x01\x12\r\n\x05\x61\x63\x63_o\x18\x03 \x01(\x01\x12\x15\n\rwheel_angle_o\x18\x04 \x01(\x01\x12\x0b\n\x03x_o\x18\x05 \x01(\x01\x12\x0b\n\x03y_o\x18\x06 \x01(\x01\x12\r\n\x05yaw_o\x18\x07 \x01(\x01\x12\x0e\n\x06gear_o\x18\x08 \x01(\x01\"\x9b\x01\n\rSimStatusInit\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x0e\n\x06init_x\x18\x02 \x01(\x01\x12\x0e\n\x06init_y\x18\x03 \x01(\x01\x12\x10\n\x08init_yaw\x18\x04 \x01(\x01\x12\x12\n\ninit_speed\x18\x05 \x01(\x01\x12\x18\n\x10init_wheel_angle\x18\x06 \x01(\x01\"r\n\x10\x43utInVehicleInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x0f\n\x07\x64\x65lta_s\x18\x02 \x01(\x01\x12\r\n\x05speed\x18\x03 \x01(\x01\x12\x12\n\ncutin_flag\x18\x04 \x01(\x08\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_SIMVEHICLECONTORLINPUT = _descriptor.Descriptor(
  name='SimVehicleContorlInput',
  full_name='sim_vehicle.control_io.SimVehicleContorlInput',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='sim_vehicle.control_io.SimVehicleContorlInput.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='acc_i', full_name='sim_vehicle.control_io.SimVehicleContorlInput.acc_i', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gear_i', full_name='sim_vehicle.control_io.SimVehicleContorlInput.gear_i', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='is_lat_ctrl', full_name='sim_vehicle.control_io.SimVehicleContorlInput.is_lat_ctrl', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheel_angle_i', full_name='sim_vehicle.control_io.SimVehicleContorlInput.wheel_angle_i', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=88,
  serialized_end=231,
)


_SIMVEHICLECONTORLOUTPUT = _descriptor.Descriptor(
  name='SimVehicleContorlOutput',
  full_name='sim_vehicle.control_io.SimVehicleContorlOutput',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.speed_o', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='acc_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.acc_o', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='wheel_angle_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.wheel_angle_o', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='x_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.x_o', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='y_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.y_o', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='yaw_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.yaw_o', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gear_o', full_name='sim_vehicle.control_io.SimVehicleContorlOutput.gear_o', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=234,
  serialized_end=415,
)


_SIMSTATUSINIT = _descriptor.Descriptor(
  name='SimStatusInit',
  full_name='sim_vehicle.control_io.SimStatusInit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='sim_vehicle.control_io.SimStatusInit.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='init_x', full_name='sim_vehicle.control_io.SimStatusInit.init_x', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='init_y', full_name='sim_vehicle.control_io.SimStatusInit.init_y', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='init_yaw', full_name='sim_vehicle.control_io.SimStatusInit.init_yaw', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='init_speed', full_name='sim_vehicle.control_io.SimStatusInit.init_speed', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='init_wheel_angle', full_name='sim_vehicle.control_io.SimStatusInit.init_wheel_angle', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=418,
  serialized_end=573,
)


_CUTINVEHICLEINFO = _descriptor.Descriptor(
  name='CutInVehicleInfo',
  full_name='sim_vehicle.control_io.CutInVehicleInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='sim_vehicle.control_io.CutInVehicleInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='delta_s', full_name='sim_vehicle.control_io.CutInVehicleInfo.delta_s', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed', full_name='sim_vehicle.control_io.CutInVehicleInfo.speed', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cutin_flag', full_name='sim_vehicle.control_io.CutInVehicleInfo.cutin_flag', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=575,
  serialized_end=689,
)

_SIMVEHICLECONTORLINPUT.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_SIMVEHICLECONTORLOUTPUT.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_SIMSTATUSINIT.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_CUTINVEHICLEINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
DESCRIPTOR.message_types_by_name['SimVehicleContorlInput'] = _SIMVEHICLECONTORLINPUT
DESCRIPTOR.message_types_by_name['SimVehicleContorlOutput'] = _SIMVEHICLECONTORLOUTPUT
DESCRIPTOR.message_types_by_name['SimStatusInit'] = _SIMSTATUSINIT
DESCRIPTOR.message_types_by_name['CutInVehicleInfo'] = _CUTINVEHICLEINFO

SimVehicleContorlInput = _reflection.GeneratedProtocolMessageType('SimVehicleContorlInput', (_message.Message,), dict(
  DESCRIPTOR = _SIMVEHICLECONTORLINPUT,
  __module__ = 'control.proto.sim_vehicle_pb2'
  # @@protoc_insertion_point(class_scope:sim_vehicle.control_io.SimVehicleContorlInput)
  ))
_sym_db.RegisterMessage(SimVehicleContorlInput)

SimVehicleContorlOutput = _reflection.GeneratedProtocolMessageType('SimVehicleContorlOutput', (_message.Message,), dict(
  DESCRIPTOR = _SIMVEHICLECONTORLOUTPUT,
  __module__ = 'control.proto.sim_vehicle_pb2'
  # @@protoc_insertion_point(class_scope:sim_vehicle.control_io.SimVehicleContorlOutput)
  ))
_sym_db.RegisterMessage(SimVehicleContorlOutput)

SimStatusInit = _reflection.GeneratedProtocolMessageType('SimStatusInit', (_message.Message,), dict(
  DESCRIPTOR = _SIMSTATUSINIT,
  __module__ = 'control.proto.sim_vehicle_pb2'
  # @@protoc_insertion_point(class_scope:sim_vehicle.control_io.SimStatusInit)
  ))
_sym_db.RegisterMessage(SimStatusInit)

CutInVehicleInfo = _reflection.GeneratedProtocolMessageType('CutInVehicleInfo', (_message.Message,), dict(
  DESCRIPTOR = _CUTINVEHICLEINFO,
  __module__ = 'control.proto.sim_vehicle_pb2'
  # @@protoc_insertion_point(class_scope:sim_vehicle.control_io.CutInVehicleInfo)
  ))
_sym_db.RegisterMessage(CutInVehicleInfo)


# @@protoc_insertion_point(module_scope)

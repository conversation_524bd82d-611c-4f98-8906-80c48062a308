# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/chassis.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1b\x63ontrol/proto/chassis.proto\x12\x0cmega.chassis\x1a\x1a\x63ontrol/proto/header.proto\"\xaf\x01\n\x07\x43hassis\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x11\n\tspeed_mps\x18\x02 \x01(\x02\x12\x14\n\x0c\x64riving_mode\x18\x03 \x01(\r\x12\x0f\n\x07\x63ur_acc\x18\x04 \x01(\x02\x12\x1a\n\x12\x63ur_steering_angle\x18\x05 \x01(\x02\x12\x10\n\x08yaw_rate\x18\x06 \x01(\x02\x12\x10\n\x08gear_pos\x18\x07 \x01(\rb\x06proto3')



_CHASSIS = DESCRIPTOR.message_types_by_name['Chassis']
Chassis = _reflection.GeneratedProtocolMessageType('Chassis', (_message.Message,), {
  'DESCRIPTOR' : _CHASSIS,
  '__module__' : 'control.proto.chassis_pb2'
  # @@protoc_insertion_point(class_scope:mega.chassis.Chassis)
  })
_sym_db.RegisterMessage(Chassis)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _CHASSIS._serialized_start=74
  _CHASSIS._serialized_end=249
# @@protoc_insertion_point(module_scope)

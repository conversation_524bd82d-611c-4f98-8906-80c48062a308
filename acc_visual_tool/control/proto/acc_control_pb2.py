# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/acc_control.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/acc_control.proto',
  package='ACC_control',
  syntax='proto3',
  serialized_pb=_b('\n\x1f\x63ontrol/proto/acc_control.proto\x12\x0b\x41\x43\x43_control\x1a\x1a\x63ontrol/proto/header.proto\"\x98\x01\n\x0eObjVehicleInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x12\n\ncheck_flag\x18\x02 \x01(\x08\x12\x0f\n\x07\x64\x65lta_s\x18\x03 \x01(\x01\x12\x11\n\tobj_speed\x18\x04 \x01(\x01\x12\x16\n\x0eobj_accelerate\x18\x05 \x01(\x01\x12\n\n\x02id\x18\x06 \x01(\x05\"\x9c\x01\n\x13\x41\x43\x43\x43ontrolConfigure\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x14\n\x0c\x63ontrol_mode\x18\x02 \x01(\x05\x12\x15\n\rmin_safe_time\x18\x03 \x01(\x01\x12\x19\n\x11min_stop_distance\x18\x04 \x01(\x01\x12\x11\n\tset_speed\x18\x05 \x01(\x01\"\x83\x01\n\x0cPIDDebugInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\r\n\x05\x65rror\x18\x02 \x01(\x01\x12\x10\n\x08integral\x18\x03 \x01(\x01\x12\x12\n\nderivative\x18\x04 \x01(\x01\x12\x12\n\npid_output\x18\x05 \x01(\x01\"\xd5\x04\n\x0c\x41\x43\x43\x44\x65\x62ugInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x14\n\x0c\x63\x61l_acc_mode\x18\x02 \x01(\x05\x12\x15\n\rcal_desired_s\x18\x03 \x01(\x01\x12\x17\n\x0f\x63\x61l_first_speed\x18\x04 \x01(\x01\x12\x1c\n\x14\x63\x61l_warning_distance\x18\x05 \x01(\x01\x12\x1a\n\x12\x63\x61l_break_distance\x18\x06 \x01(\x01\x12\x18\n\x10\x63\x61l_follow_speed\x18\x07 \x01(\x01\x12\x1e\n\x16\x63\x66g_safe_distance_mode\x18\x08 \x01(\x05\x12\x15\n\rcfg_set_speed\x18\t \x01(\x01\x12\x19\n\x11\x63\x66g_min_safe_time\x18\n \x01(\x01\x12\x1d\n\x15\x63\x66g_min_stop_distance\x18\x0b \x01(\x01\x12\x18\n\x10\x63\x66g_control_mode\x18\x0c \x01(\x05\x12\x11\n\tcur_speed\x18\r \x01(\x01\x12\x14\n\x0c\x63ur_plan_acc\x18\x0e \x01(\x01\x12\x16\n\x0eobj_check_flag\x18\x0f \x01(\x08\x12\x13\n\x0bobj_delta_s\x18\x10 \x01(\x01\x12\x11\n\tobj_speed\x18\x11 \x01(\x01\x12\x0f\n\x07obj_acc\x18\x12 \x01(\x01\x12\x15\n\rreserved_int1\x18\x13 \x01(\x05\x12\x15\n\rreserved_int2\x18\x14 \x01(\x05\x12\x18\n\x10reserved_double1\x18\x15 \x01(\x01\x12\x18\n\x10reserved_double2\x18\x16 \x01(\x01\x12\x18\n\x10reserved_double3\x18\x17 \x01(\x01\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_OBJVEHICLEINFO = _descriptor.Descriptor(
  name='ObjVehicleInfo',
  full_name='ACC_control.ObjVehicleInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='ACC_control.ObjVehicleInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='check_flag', full_name='ACC_control.ObjVehicleInfo.check_flag', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='delta_s', full_name='ACC_control.ObjVehicleInfo.delta_s', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj_speed', full_name='ACC_control.ObjVehicleInfo.obj_speed', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj_accelerate', full_name='ACC_control.ObjVehicleInfo.obj_accelerate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='id', full_name='ACC_control.ObjVehicleInfo.id', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=77,
  serialized_end=229,
)


_ACCCONTROLCONFIGURE = _descriptor.Descriptor(
  name='ACCControlConfigure',
  full_name='ACC_control.ACCControlConfigure',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='ACC_control.ACCControlConfigure.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='control_mode', full_name='ACC_control.ACCControlConfigure.control_mode', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='min_safe_time', full_name='ACC_control.ACCControlConfigure.min_safe_time', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='min_stop_distance', full_name='ACC_control.ACCControlConfigure.min_stop_distance', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='set_speed', full_name='ACC_control.ACCControlConfigure.set_speed', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=232,
  serialized_end=388,
)


_PIDDEBUGINFO = _descriptor.Descriptor(
  name='PIDDebugInfo',
  full_name='ACC_control.PIDDebugInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='ACC_control.PIDDebugInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='error', full_name='ACC_control.PIDDebugInfo.error', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='integral', full_name='ACC_control.PIDDebugInfo.integral', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='derivative', full_name='ACC_control.PIDDebugInfo.derivative', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pid_output', full_name='ACC_control.PIDDebugInfo.pid_output', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=391,
  serialized_end=522,
)


_ACCDEBUGINFO = _descriptor.Descriptor(
  name='ACCDebugInfo',
  full_name='ACC_control.ACCDebugInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='ACC_control.ACCDebugInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cal_acc_mode', full_name='ACC_control.ACCDebugInfo.cal_acc_mode', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cal_desired_s', full_name='ACC_control.ACCDebugInfo.cal_desired_s', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cal_first_speed', full_name='ACC_control.ACCDebugInfo.cal_first_speed', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cal_warning_distance', full_name='ACC_control.ACCDebugInfo.cal_warning_distance', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cal_break_distance', full_name='ACC_control.ACCDebugInfo.cal_break_distance', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cal_follow_speed', full_name='ACC_control.ACCDebugInfo.cal_follow_speed', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cfg_safe_distance_mode', full_name='ACC_control.ACCDebugInfo.cfg_safe_distance_mode', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cfg_set_speed', full_name='ACC_control.ACCDebugInfo.cfg_set_speed', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cfg_min_safe_time', full_name='ACC_control.ACCDebugInfo.cfg_min_safe_time', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cfg_min_stop_distance', full_name='ACC_control.ACCDebugInfo.cfg_min_stop_distance', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cfg_control_mode', full_name='ACC_control.ACCDebugInfo.cfg_control_mode', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cur_speed', full_name='ACC_control.ACCDebugInfo.cur_speed', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cur_plan_acc', full_name='ACC_control.ACCDebugInfo.cur_plan_acc', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj_check_flag', full_name='ACC_control.ACCDebugInfo.obj_check_flag', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj_delta_s', full_name='ACC_control.ACCDebugInfo.obj_delta_s', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj_speed', full_name='ACC_control.ACCDebugInfo.obj_speed', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='obj_acc', full_name='ACC_control.ACCDebugInfo.obj_acc', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserved_int1', full_name='ACC_control.ACCDebugInfo.reserved_int1', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserved_int2', full_name='ACC_control.ACCDebugInfo.reserved_int2', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserved_double1', full_name='ACC_control.ACCDebugInfo.reserved_double1', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserved_double2', full_name='ACC_control.ACCDebugInfo.reserved_double2', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reserved_double3', full_name='ACC_control.ACCDebugInfo.reserved_double3', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=525,
  serialized_end=1122,
)

_OBJVEHICLEINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_ACCCONTROLCONFIGURE.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_PIDDEBUGINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_ACCDEBUGINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
DESCRIPTOR.message_types_by_name['ObjVehicleInfo'] = _OBJVEHICLEINFO
DESCRIPTOR.message_types_by_name['ACCControlConfigure'] = _ACCCONTROLCONFIGURE
DESCRIPTOR.message_types_by_name['PIDDebugInfo'] = _PIDDEBUGINFO
DESCRIPTOR.message_types_by_name['ACCDebugInfo'] = _ACCDEBUGINFO

ObjVehicleInfo = _reflection.GeneratedProtocolMessageType('ObjVehicleInfo', (_message.Message,), dict(
  DESCRIPTOR = _OBJVEHICLEINFO,
  __module__ = 'control.proto.acc_control_pb2'
  # @@protoc_insertion_point(class_scope:ACC_control.ObjVehicleInfo)
  ))
_sym_db.RegisterMessage(ObjVehicleInfo)

ACCControlConfigure = _reflection.GeneratedProtocolMessageType('ACCControlConfigure', (_message.Message,), dict(
  DESCRIPTOR = _ACCCONTROLCONFIGURE,
  __module__ = 'control.proto.acc_control_pb2'
  # @@protoc_insertion_point(class_scope:ACC_control.ACCControlConfigure)
  ))
_sym_db.RegisterMessage(ACCControlConfigure)

PIDDebugInfo = _reflection.GeneratedProtocolMessageType('PIDDebugInfo', (_message.Message,), dict(
  DESCRIPTOR = _PIDDEBUGINFO,
  __module__ = 'control.proto.acc_control_pb2'
  # @@protoc_insertion_point(class_scope:ACC_control.PIDDebugInfo)
  ))
_sym_db.RegisterMessage(PIDDebugInfo)

ACCDebugInfo = _reflection.GeneratedProtocolMessageType('ACCDebugInfo', (_message.Message,), dict(
  DESCRIPTOR = _ACCDEBUGINFO,
  __module__ = 'control.proto.acc_control_pb2'
  # @@protoc_insertion_point(class_scope:ACC_control.ACCDebugInfo)
  ))
_sym_db.RegisterMessage(ACCDebugInfo)


# @@protoc_insertion_point(module_scope)

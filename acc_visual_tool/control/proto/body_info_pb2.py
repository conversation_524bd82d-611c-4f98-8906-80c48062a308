# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/body_info.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/body_info.proto',
  package='mega.body',
  syntax='proto3',
  serialized_pb=_b('\n\x1d\x63ontrol/proto/body_info.proto\x12\tmega.body\x1a\x1a\x63ontrol/proto/header.proto\"\xea\x03\n\x0e\x42odyButtonInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x14\n\x0c\x62utton_press\x18\x02 \x01(\x08\x12=\n\rbutton_enable\x18\x03 \x01(\x0e\x32&.mega.body.BodyButtonInfo.ButtonEnable\x12=\n\rbutton_signal\x18\x04 \x01(\x0e\x32&.mega.body.BodyButtonInfo.ButtonSignal\x12\x39\n\x0bturn_signal\x18\x05 \x01(\x0e\x32$.mega.body.BodyButtonInfo.TurnSignal\"B\n\x0c\x42uttonEnable\x12\x0f\n\x0b\x45NABLE_NONE\x10\x00\x12\x0f\n\x0b\x45NABLE_ONCE\x10\x01\x12\x10\n\x0c\x45NABLE_TWICE\x10\x02\"]\n\x0c\x42uttonSignal\x12\x0e\n\nPRESS_NONE\x10\x00\x12\x0c\n\x08PRESS_UP\x10\x01\x12\x0e\n\nPRESS_DOWN\x10\x02\x12\x0e\n\nPRESS_LEFT\x10\x03\x12\x0f\n\x0bPRESS_RIGHT\x10\x04\":\n\nTurnSignal\x12\r\n\tTURN_NONE\x10\x00\x12\r\n\tTURN_LEFT\x10\x01\x12\x0e\n\nTURN_RIGHT\x10\x02\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)



_BODYBUTTONINFO_BUTTONENABLE = _descriptor.EnumDescriptor(
  name='ButtonEnable',
  full_name='mega.body.BodyButtonInfo.ButtonEnable',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ENABLE_NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENABLE_ONCE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENABLE_TWICE', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=342,
  serialized_end=408,
)
_sym_db.RegisterEnumDescriptor(_BODYBUTTONINFO_BUTTONENABLE)

_BODYBUTTONINFO_BUTTONSIGNAL = _descriptor.EnumDescriptor(
  name='ButtonSignal',
  full_name='mega.body.BodyButtonInfo.ButtonSignal',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PRESS_NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PRESS_UP', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PRESS_DOWN', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PRESS_LEFT', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PRESS_RIGHT', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=410,
  serialized_end=503,
)
_sym_db.RegisterEnumDescriptor(_BODYBUTTONINFO_BUTTONSIGNAL)

_BODYBUTTONINFO_TURNSIGNAL = _descriptor.EnumDescriptor(
  name='TurnSignal',
  full_name='mega.body.BodyButtonInfo.TurnSignal',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TURN_NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TURN_LEFT', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TURN_RIGHT', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=505,
  serialized_end=563,
)
_sym_db.RegisterEnumDescriptor(_BODYBUTTONINFO_TURNSIGNAL)


_BODYBUTTONINFO = _descriptor.Descriptor(
  name='BodyButtonInfo',
  full_name='mega.body.BodyButtonInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='mega.body.BodyButtonInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='button_press', full_name='mega.body.BodyButtonInfo.button_press', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='button_enable', full_name='mega.body.BodyButtonInfo.button_enable', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='button_signal', full_name='mega.body.BodyButtonInfo.button_signal', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='turn_signal', full_name='mega.body.BodyButtonInfo.turn_signal', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _BODYBUTTONINFO_BUTTONENABLE,
    _BODYBUTTONINFO_BUTTONSIGNAL,
    _BODYBUTTONINFO_TURNSIGNAL,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=73,
  serialized_end=563,
)

_BODYBUTTONINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_BODYBUTTONINFO.fields_by_name['button_enable'].enum_type = _BODYBUTTONINFO_BUTTONENABLE
_BODYBUTTONINFO.fields_by_name['button_signal'].enum_type = _BODYBUTTONINFO_BUTTONSIGNAL
_BODYBUTTONINFO.fields_by_name['turn_signal'].enum_type = _BODYBUTTONINFO_TURNSIGNAL
_BODYBUTTONINFO_BUTTONENABLE.containing_type = _BODYBUTTONINFO
_BODYBUTTONINFO_BUTTONSIGNAL.containing_type = _BODYBUTTONINFO
_BODYBUTTONINFO_TURNSIGNAL.containing_type = _BODYBUTTONINFO
DESCRIPTOR.message_types_by_name['BodyButtonInfo'] = _BODYBUTTONINFO

BodyButtonInfo = _reflection.GeneratedProtocolMessageType('BodyButtonInfo', (_message.Message,), dict(
  DESCRIPTOR = _BODYBUTTONINFO,
  __module__ = 'control.proto.body_info_pb2'
  # @@protoc_insertion_point(class_scope:mega.body.BodyButtonInfo)
  ))
_sym_db.RegisterMessage(BodyButtonInfo)


# @@protoc_insertion_point(module_scope)

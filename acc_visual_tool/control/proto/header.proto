syntax = "proto3";

package rainbowdash.common;

message FApaScenario {enum Enum{
    NONE = 0;
    TRACKING_AND_REVERSE = 1;
    PARKING_IN = 2;
    PARKING_OUT = 3;
    CUSTOM_DEFINE_STALL = 4;
}}

message ParkingOutDirection{enum Enum{
    NONE = 0;
    FRONT = 1;
    REAR = 2;
    LEFT = 3;
    RIGHT = 4;
}}

message CommonGearPosition{
enum Enum {
  NONE = 0;
  NEUTRAL = 1;
  DRIVING = 2;
  REVERSE = 3;
  PARKING = 4;
}
}

message CommonBool {
enum Enum {
  NONE = 0;
  TRUE = 1;
  FALSE = 2;
}
}

message SlotType {
enum Enum {
  NONE = 0;
  VERTICAL = 1;
  PARALLEL = 2;
  OBLIQUE = 3;
  TRACK = 4;
  OTHER = 10;
}
}

message CommonErrorType {
enum Enum {
  NONE = 0;
  CURRENT_IGNITION_CYCLE_UNRECOVERABLE = 1;
  CURRENT_IGNITION_CYCLE_RECOVERABLE_BUT_TASK_CYCLE_UNRECOVERABLE = 2;
  CURRENT_TASK_CYCLE_RECOVERABLE =3;
}
}

message Header {
  //Message passing time in seconds
  double timestamp = 1;
  string module_name = 2;
  //Sequence number maintained by each module from boot.
  uint32 sequence_num = 3;
  //Unique id from each frame. Can be generated by tools such as boost::uuids
  string frame_id = 4;
  //If not empty, it means the source data for current output frame
  string trigger_frame = 5;
  uint32 version = 6;
  uint64 session_id = 7; 
}

message Verify {
  uint32 val = 1;
}


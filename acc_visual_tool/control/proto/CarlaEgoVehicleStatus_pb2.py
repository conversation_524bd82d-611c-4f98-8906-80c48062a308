# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/CarlaEgoVehicleStatus.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/CarlaEgoVehicleStatus.proto',
  package='carla_msgs',
  syntax='proto3',
  serialized_pb=_b('\n)control/proto/CarlaEgoVehicleStatus.proto\x12\ncarla_msgs\x1a\x1a\x63ontrol/proto/header.proto\"\xca\x01\n\x15\x43\x61rlaEgoVehicleStatus\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x10\n\x08velocity\x18\x02 \x01(\x01\x12\x14\n\x0c\x61\x63\x63\x65leration\x18\x03 \x01(\x01\x12\r\n\x05pitch\x18\x04 \x01(\x01\x12\x0b\n\x03yaw\x18\x05 \x01(\x01\x12\x0c\n\x04roll\x18\x06 \x01(\x01\x12\x33\n\x07\x63ontrol\x18\x07 \x01(\x0b\x32\".carla_msgs.CarlaEgoVehicleControl\"\xc2\x01\n\x16\x43\x61rlaEgoVehicleControl\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x10\n\x08throttle\x18\x02 \x01(\x01\x12\r\n\x05steer\x18\x03 \x01(\x01\x12\r\n\x05\x62rake\x18\x04 \x01(\x01\x12\x12\n\nhand_brake\x18\x05 \x01(\x08\x12\x0f\n\x07reverse\x18\x06 \x01(\x08\x12\x0c\n\x04gear\x18\x07 \x01(\x05\x12\x19\n\x11manual_gear_shift\x18\x08 \x01(\x08\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_CARLAEGOVEHICLESTATUS = _descriptor.Descriptor(
  name='CarlaEgoVehicleStatus',
  full_name='carla_msgs.CarlaEgoVehicleStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='carla_msgs.CarlaEgoVehicleStatus.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='carla_msgs.CarlaEgoVehicleStatus.velocity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='acceleration', full_name='carla_msgs.CarlaEgoVehicleStatus.acceleration', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pitch', full_name='carla_msgs.CarlaEgoVehicleStatus.pitch', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='yaw', full_name='carla_msgs.CarlaEgoVehicleStatus.yaw', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='roll', full_name='carla_msgs.CarlaEgoVehicleStatus.roll', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='control', full_name='carla_msgs.CarlaEgoVehicleStatus.control', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=86,
  serialized_end=288,
)


_CARLAEGOVEHICLECONTROL = _descriptor.Descriptor(
  name='CarlaEgoVehicleControl',
  full_name='carla_msgs.CarlaEgoVehicleControl',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='carla_msgs.CarlaEgoVehicleControl.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='throttle', full_name='carla_msgs.CarlaEgoVehicleControl.throttle', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='steer', full_name='carla_msgs.CarlaEgoVehicleControl.steer', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brake', full_name='carla_msgs.CarlaEgoVehicleControl.brake', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='hand_brake', full_name='carla_msgs.CarlaEgoVehicleControl.hand_brake', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reverse', full_name='carla_msgs.CarlaEgoVehicleControl.reverse', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='gear', full_name='carla_msgs.CarlaEgoVehicleControl.gear', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='manual_gear_shift', full_name='carla_msgs.CarlaEgoVehicleControl.manual_gear_shift', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=291,
  serialized_end=485,
)

_CARLAEGOVEHICLESTATUS.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_CARLAEGOVEHICLESTATUS.fields_by_name['control'].message_type = _CARLAEGOVEHICLECONTROL
_CARLAEGOVEHICLECONTROL.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
DESCRIPTOR.message_types_by_name['CarlaEgoVehicleStatus'] = _CARLAEGOVEHICLESTATUS
DESCRIPTOR.message_types_by_name['CarlaEgoVehicleControl'] = _CARLAEGOVEHICLECONTROL

CarlaEgoVehicleStatus = _reflection.GeneratedProtocolMessageType('CarlaEgoVehicleStatus', (_message.Message,), dict(
  DESCRIPTOR = _CARLAEGOVEHICLESTATUS,
  __module__ = 'control.proto.CarlaEgoVehicleStatus_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.CarlaEgoVehicleStatus)
  ))
_sym_db.RegisterMessage(CarlaEgoVehicleStatus)

CarlaEgoVehicleControl = _reflection.GeneratedProtocolMessageType('CarlaEgoVehicleControl', (_message.Message,), dict(
  DESCRIPTOR = _CARLAEGOVEHICLECONTROL,
  __module__ = 'control.proto.CarlaEgoVehicleStatus_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.CarlaEgoVehicleControl)
  ))
_sym_db.RegisterMessage(CarlaEgoVehicleControl)


# @@protoc_insertion_point(module_scope)

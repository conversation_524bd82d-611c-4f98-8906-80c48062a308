syntax = "proto3";
package mega.body;

import "control/proto/header.proto";

//   q w e
//   a s d f

//  turn_l  up  turn_r
//   left  down right enable

message BodyButtonInfo {
    rainbowdash.common.Header header = 1;
    bool button_press = 2;
    enum ButtonEnable {
        ENABLE_NONE = 0;
        ENABLE_ONCE = 1;
        ENABLE_TWICE = 2;
    };
    ButtonEnable button_enable = 3;
    enum ButtonSignal {
        PRESS_NONE = 0;
        PRESS_UP = 1;
        PRESS_DOWN = 2;
        PRESS_LEFT = 3;
        PRESS_RIGHT = 4;
    };
    ButtonSignal button_signal = 4;
    enum TurnSignal {
        TURN_NONE = 0;
        TURN_LEFT = 1;
        TURN_RIGHT = 2;
    };
    TurnSignal turn_signal = 5;
}
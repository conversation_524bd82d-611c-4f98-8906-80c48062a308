syntax = "proto3";
package mega.control;

import "control/proto/pnc_point.proto";
import "control/proto/header.proto";

message SimpleLongitudinalDebug {
  double station_reference = 1;
  double station_error = 2;
  double station_error_limited = 3;
  double preview_station_error = 4;
  double speed_reference = 5;
  double speed_error = 6;
  double speed_controller_input_limited = 7;
  double preview_speed_reference = 8;
  double preview_speed_error = 9;
  double preview_acceleration_reference = 10;
  double acceleration_cmd_closeloop = 11;
  double acceleration_cmd = 12;
  double acceleration_lookup = 13;
  double speed_lookup = 14;
  double calibration_value = 15;
  double throttle_cmd = 16;
  double brake_cmd = 17;
  bool is_full_stop = 18;
  double slope_offset_compensation = 19;
  double current_station = 20;
  double path_remain = 21;
  int32 pid_saturation_status = 22;
  int32 leadlag_saturation_status = 23;
  double speed_offset = 24;
  double current_speed = 25;
  double acceleration_reference = 26;
  double current_acceleration = 27;
  double acceleration_error = 28;
  double jerk_reference = 29;
  double current_jerk = 30;
  double jerk_error = 31;
  rainbowdash.planning.TrajectoryPoint current_matched_point = 32;
  rainbowdash.planning.TrajectoryPoint current_reference_point = 33;
  rainbowdash.planning.TrajectoryPoint preview_reference_point = 34;
}

message SimpleLateralDebug {
  double lateral_error = 1;
  double ref_heading = 2;
  double heading = 3;
  double heading_error = 4;
  double heading_error_rate = 5;
  double lateral_error_rate = 6;
  double curvature = 7;
  double steer_angle = 8;
  double steer_angle_feedforward = 9;
  double steer_angle_lateral_contribution = 10;
  double steer_angle_lateral_rate_contribution = 11;
  double steer_angle_heading_contribution = 12;
  double steer_angle_heading_rate_contribution = 13;
  double steer_angle_feedback = 14;
  double steering_position = 15;
  double ref_speed = 16;
  double steer_angle_limited = 17;

  // time derivative of lateral error rate, in m/s^2
  double lateral_acceleration = 18;
  // second time derivative of lateral error rate, in m/s^3
  double lateral_jerk = 19;

  double ref_heading_rate = 20;
  double heading_rate = 21;

  // heading_acceleration, as known as yaw acceleration, is the time derivative
  // of heading rate,  in rad/s^2
  double ref_heading_acceleration = 22;
  double heading_acceleration = 23;
  double heading_error_acceleration = 24;

  // heading_jerk, as known as yaw jerk, is the second time derivative of
  // heading rate, in rad/s^3
  double ref_heading_jerk = 25;
  double heading_jerk = 26;
  double heading_error_jerk = 27;

  // modified lateral_error and heading_error with look-ahead or look-back
  // station, as the feedback term for control usage
  double lateral_error_feedback = 28;
  double heading_error_feedback = 29;

  // current planning target point
  rainbowdash.planning.TrajectoryPoint current_target_point = 30;

  // Augmented feedback control term in addition to LQR control
  double steer_angle_feedback_augment = 31;

}

message SimpleMPCDebug {
  double lateral_error = 1;
  double ref_heading = 2;
  double heading = 3;
  double heading_error = 4;
  double heading_error_rate = 5;
  double lateral_error_rate = 6;
  double curvature = 7;
  double steer_angle = 8;
  double steer_angle_feedforward = 9;
  double steer_angle_lateral_contribution = 10;
  double steer_angle_lateral_rate_contribution = 11;
  double steer_angle_heading_contribution = 12;
  double steer_angle_heading_rate_contribution = 13;
  double steer_angle_feedback = 14;
  double steering_position = 15;
  double ref_speed = 16;
  double steer_angle_limited = 17;
  double station_reference = 18;
  double station_error = 19;
  double speed_reference = 20;
  double speed_error = 21;
  double acceleration_reference = 22;
  bool is_full_stop = 23;
  double station_feedback = 24;
  double speed_feedback = 25;
  double acceleration_cmd_closeloop = 26;
  double acceleration_cmd = 27;
  double acceleration_lookup = 28;
  double speed_lookup = 29;
  double calibration_value = 30;
  double steer_unconstrained_control_diff = 31;
  double steer_angle_feedforward_compensation = 32;
  repeated double matrix_q_updated = 33;  // matrix_q_updated_ size = 6
  repeated double matrix_r_updated = 34;  // matrix_r_updated_ size = 2

  // time derivative of lateral error rate, in m/s^2
  double lateral_acceleration = 35;
  // second time derivative of lateral error rate, in m/s^3
  double lateral_jerk = 36;

  double ref_heading_rate = 37;
  double heading_rate = 38;

  // heading_acceleration, as known as yaw acceleration, is the time derivative
  // of heading rate,  in rad/s^2
  double ref_heading_acceleration = 39;
  double heading_acceleration = 40;
  double heading_error_acceleration = 41;

  // heading_jerk, as known as yaw jerk, is the second time derivative of
  // heading rate, in rad/s^3
  double ref_heading_jerk = 42;
  double heading_jerk = 43;
  double heading_error_jerk = 44;

  double acceleration_feedback = 45;
  double acceleration_error = 46;
  double jerk_reference = 47;
  double jerk_feedback = 48;
  double jerk_error = 49;
}

message ControlCommand {
  rainbowdash.common.Header header = 1;
  uint32 is_driving =2;
  double tar_acc = 3;
  uint64 tar_torque = 4;
  uint32 is_steering = 5;
  double steering_target = 6;
  uint32 is_breaking = 7;
  double tar_deacc = 8;
  uint32 is_gear = 9;
  uint32 tar_gear = 10;
}


# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/control_cmd.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import pnc_point_pb2 as control_dot_proto_dot_pnc__point__pb2
from control.proto import header_pb2 as control_dot_proto_dot_header__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1f\x63ontrol/proto/control_cmd.proto\x12\x0cmega.control\x1a\x1d\x63ontrol/proto/pnc_point.proto\x1a\x1a\x63ontrol/proto/header.proto\"\xc2\x08\n\x17SimpleLongitudinalDebug\x12\x19\n\x11station_reference\x18\x01 \x01(\x01\x12\x15\n\rstation_error\x18\x02 \x01(\x01\x12\x1d\n\x15station_error_limited\x18\x03 \x01(\x01\x12\x1d\n\x15preview_station_error\x18\x04 \x01(\x01\x12\x17\n\x0fspeed_reference\x18\x05 \x01(\x01\x12\x13\n\x0bspeed_error\x18\x06 \x01(\x01\x12&\n\x1espeed_controller_input_limited\x18\x07 \x01(\x01\x12\x1f\n\x17preview_speed_reference\x18\x08 \x01(\x01\x12\x1b\n\x13preview_speed_error\x18\t \x01(\x01\x12&\n\x1epreview_acceleration_reference\x18\n \x01(\x01\x12\"\n\x1a\x61\x63\x63\x65leration_cmd_closeloop\x18\x0b \x01(\x01\x12\x18\n\x10\x61\x63\x63\x65leration_cmd\x18\x0c \x01(\x01\x12\x1b\n\x13\x61\x63\x63\x65leration_lookup\x18\r \x01(\x01\x12\x14\n\x0cspeed_lookup\x18\x0e \x01(\x01\x12\x19\n\x11\x63\x61libration_value\x18\x0f \x01(\x01\x12\x14\n\x0cthrottle_cmd\x18\x10 \x01(\x01\x12\x11\n\tbrake_cmd\x18\x11 \x01(\x01\x12\x14\n\x0cis_full_stop\x18\x12 \x01(\x08\x12!\n\x19slope_offset_compensation\x18\x13 \x01(\x01\x12\x17\n\x0f\x63urrent_station\x18\x14 \x01(\x01\x12\x13\n\x0bpath_remain\x18\x15 \x01(\x01\x12\x1d\n\x15pid_saturation_status\x18\x16 \x01(\x05\x12!\n\x19leadlag_saturation_status\x18\x17 \x01(\x05\x12\x14\n\x0cspeed_offset\x18\x18 \x01(\x01\x12\x15\n\rcurrent_speed\x18\x19 \x01(\x01\x12\x1e\n\x16\x61\x63\x63\x65leration_reference\x18\x1a \x01(\x01\x12\x1c\n\x14\x63urrent_acceleration\x18\x1b \x01(\x01\x12\x1a\n\x12\x61\x63\x63\x65leration_error\x18\x1c \x01(\x01\x12\x16\n\x0ejerk_reference\x18\x1d \x01(\x01\x12\x14\n\x0c\x63urrent_jerk\x18\x1e \x01(\x01\x12\x12\n\njerk_error\x18\x1f \x01(\x01\x12\x44\n\x15\x63urrent_matched_point\x18  \x01(\x0b\x32%.rainbowdash.planning.TrajectoryPoint\x12\x46\n\x17\x63urrent_reference_point\x18! \x01(\x0b\x32%.rainbowdash.planning.TrajectoryPoint\x12\x46\n\x17preview_reference_point\x18\" \x01(\x0b\x32%.rainbowdash.planning.TrajectoryPoint\"\xc3\x07\n\x12SimpleLateralDebug\x12\x15\n\rlateral_error\x18\x01 \x01(\x01\x12\x13\n\x0bref_heading\x18\x02 \x01(\x01\x12\x0f\n\x07heading\x18\x03 \x01(\x01\x12\x15\n\rheading_error\x18\x04 \x01(\x01\x12\x1a\n\x12heading_error_rate\x18\x05 \x01(\x01\x12\x1a\n\x12lateral_error_rate\x18\x06 \x01(\x01\x12\x11\n\tcurvature\x18\x07 \x01(\x01\x12\x13\n\x0bsteer_angle\x18\x08 \x01(\x01\x12\x1f\n\x17steer_angle_feedforward\x18\t \x01(\x01\x12(\n steer_angle_lateral_contribution\x18\n \x01(\x01\x12-\n%steer_angle_lateral_rate_contribution\x18\x0b \x01(\x01\x12(\n steer_angle_heading_contribution\x18\x0c \x01(\x01\x12-\n%steer_angle_heading_rate_contribution\x18\r \x01(\x01\x12\x1c\n\x14steer_angle_feedback\x18\x0e \x01(\x01\x12\x19\n\x11steering_position\x18\x0f \x01(\x01\x12\x11\n\tref_speed\x18\x10 \x01(\x01\x12\x1b\n\x13steer_angle_limited\x18\x11 \x01(\x01\x12\x1c\n\x14lateral_acceleration\x18\x12 \x01(\x01\x12\x14\n\x0clateral_jerk\x18\x13 \x01(\x01\x12\x18\n\x10ref_heading_rate\x18\x14 \x01(\x01\x12\x14\n\x0cheading_rate\x18\x15 \x01(\x01\x12 \n\x18ref_heading_acceleration\x18\x16 \x01(\x01\x12\x1c\n\x14heading_acceleration\x18\x17 \x01(\x01\x12\"\n\x1aheading_error_acceleration\x18\x18 \x01(\x01\x12\x18\n\x10ref_heading_jerk\x18\x19 \x01(\x01\x12\x14\n\x0cheading_jerk\x18\x1a \x01(\x01\x12\x1a\n\x12heading_error_jerk\x18\x1b \x01(\x01\x12\x1e\n\x16lateral_error_feedback\x18\x1c \x01(\x01\x12\x1e\n\x16heading_error_feedback\x18\x1d \x01(\x01\x12\x43\n\x14\x63urrent_target_point\x18\x1e \x01(\x0b\x32%.rainbowdash.planning.TrajectoryPoint\x12$\n\x1csteer_angle_feedback_augment\x18\x1f \x01(\x01\"\xf2\n\n\x0eSimpleMPCDebug\x12\x15\n\rlateral_error\x18\x01 \x01(\x01\x12\x13\n\x0bref_heading\x18\x02 \x01(\x01\x12\x0f\n\x07heading\x18\x03 \x01(\x01\x12\x15\n\rheading_error\x18\x04 \x01(\x01\x12\x1a\n\x12heading_error_rate\x18\x05 \x01(\x01\x12\x1a\n\x12lateral_error_rate\x18\x06 \x01(\x01\x12\x11\n\tcurvature\x18\x07 \x01(\x01\x12\x13\n\x0bsteer_angle\x18\x08 \x01(\x01\x12\x1f\n\x17steer_angle_feedforward\x18\t \x01(\x01\x12(\n steer_angle_lateral_contribution\x18\n \x01(\x01\x12-\n%steer_angle_lateral_rate_contribution\x18\x0b \x01(\x01\x12(\n steer_angle_heading_contribution\x18\x0c \x01(\x01\x12-\n%steer_angle_heading_rate_contribution\x18\r \x01(\x01\x12\x1c\n\x14steer_angle_feedback\x18\x0e \x01(\x01\x12\x19\n\x11steering_position\x18\x0f \x01(\x01\x12\x11\n\tref_speed\x18\x10 \x01(\x01\x12\x1b\n\x13steer_angle_limited\x18\x11 \x01(\x01\x12\x19\n\x11station_reference\x18\x12 \x01(\x01\x12\x15\n\rstation_error\x18\x13 \x01(\x01\x12\x17\n\x0fspeed_reference\x18\x14 \x01(\x01\x12\x13\n\x0bspeed_error\x18\x15 \x01(\x01\x12\x1e\n\x16\x61\x63\x63\x65leration_reference\x18\x16 \x01(\x01\x12\x14\n\x0cis_full_stop\x18\x17 \x01(\x08\x12\x18\n\x10station_feedback\x18\x18 \x01(\x01\x12\x16\n\x0espeed_feedback\x18\x19 \x01(\x01\x12\"\n\x1a\x61\x63\x63\x65leration_cmd_closeloop\x18\x1a \x01(\x01\x12\x18\n\x10\x61\x63\x63\x65leration_cmd\x18\x1b \x01(\x01\x12\x1b\n\x13\x61\x63\x63\x65leration_lookup\x18\x1c \x01(\x01\x12\x14\n\x0cspeed_lookup\x18\x1d \x01(\x01\x12\x19\n\x11\x63\x61libration_value\x18\x1e \x01(\x01\x12(\n steer_unconstrained_control_diff\x18\x1f \x01(\x01\x12,\n$steer_angle_feedforward_compensation\x18  \x01(\x01\x12\x18\n\x10matrix_q_updated\x18! \x03(\x01\x12\x18\n\x10matrix_r_updated\x18\" \x03(\x01\x12\x1c\n\x14lateral_acceleration\x18# \x01(\x01\x12\x14\n\x0clateral_jerk\x18$ \x01(\x01\x12\x18\n\x10ref_heading_rate\x18% \x01(\x01\x12\x14\n\x0cheading_rate\x18& \x01(\x01\x12 \n\x18ref_heading_acceleration\x18\' \x01(\x01\x12\x1c\n\x14heading_acceleration\x18( \x01(\x01\x12\"\n\x1aheading_error_acceleration\x18) \x01(\x01\x12\x18\n\x10ref_heading_jerk\x18* \x01(\x01\x12\x14\n\x0cheading_jerk\x18+ \x01(\x01\x12\x1a\n\x12heading_error_jerk\x18, \x01(\x01\x12\x1d\n\x15\x61\x63\x63\x65leration_feedback\x18- \x01(\x01\x12\x1a\n\x12\x61\x63\x63\x65leration_error\x18. \x01(\x01\x12\x16\n\x0ejerk_reference\x18/ \x01(\x01\x12\x15\n\rjerk_feedback\x18\x30 \x01(\x01\x12\x12\n\njerk_error\x18\x31 \x01(\x01\"\xee\x01\n\x0e\x43ontrolCommand\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x12\n\nis_driving\x18\x02 \x01(\r\x12\x0f\n\x07tar_acc\x18\x03 \x01(\x01\x12\x12\n\ntar_torque\x18\x04 \x01(\x04\x12\x13\n\x0bis_steering\x18\x05 \x01(\r\x12\x17\n\x0fsteering_target\x18\x06 \x01(\x01\x12\x13\n\x0bis_breaking\x18\x07 \x01(\r\x12\x11\n\ttar_deacc\x18\x08 \x01(\x01\x12\x0f\n\x07is_gear\x18\t \x01(\r\x12\x10\n\x08tar_gear\x18\n \x01(\rb\x06proto3')



_SIMPLELONGITUDINALDEBUG = DESCRIPTOR.message_types_by_name['SimpleLongitudinalDebug']
_SIMPLELATERALDEBUG = DESCRIPTOR.message_types_by_name['SimpleLateralDebug']
_SIMPLEMPCDEBUG = DESCRIPTOR.message_types_by_name['SimpleMPCDebug']
_CONTROLCOMMAND = DESCRIPTOR.message_types_by_name['ControlCommand']
SimpleLongitudinalDebug = _reflection.GeneratedProtocolMessageType('SimpleLongitudinalDebug', (_message.Message,), {
  'DESCRIPTOR' : _SIMPLELONGITUDINALDEBUG,
  '__module__' : 'control.proto.control_cmd_pb2'
  # @@protoc_insertion_point(class_scope:mega.control.SimpleLongitudinalDebug)
  })
_sym_db.RegisterMessage(SimpleLongitudinalDebug)

SimpleLateralDebug = _reflection.GeneratedProtocolMessageType('SimpleLateralDebug', (_message.Message,), {
  'DESCRIPTOR' : _SIMPLELATERALDEBUG,
  '__module__' : 'control.proto.control_cmd_pb2'
  # @@protoc_insertion_point(class_scope:mega.control.SimpleLateralDebug)
  })
_sym_db.RegisterMessage(SimpleLateralDebug)

SimpleMPCDebug = _reflection.GeneratedProtocolMessageType('SimpleMPCDebug', (_message.Message,), {
  'DESCRIPTOR' : _SIMPLEMPCDEBUG,
  '__module__' : 'control.proto.control_cmd_pb2'
  # @@protoc_insertion_point(class_scope:mega.control.SimpleMPCDebug)
  })
_sym_db.RegisterMessage(SimpleMPCDebug)

ControlCommand = _reflection.GeneratedProtocolMessageType('ControlCommand', (_message.Message,), {
  'DESCRIPTOR' : _CONTROLCOMMAND,
  '__module__' : 'control.proto.control_cmd_pb2'
  # @@protoc_insertion_point(class_scope:mega.control.ControlCommand)
  })
_sym_db.RegisterMessage(ControlCommand)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SIMPLELONGITUDINALDEBUG._serialized_start=109
  _SIMPLELONGITUDINALDEBUG._serialized_end=1199
  _SIMPLELATERALDEBUG._serialized_start=1202
  _SIMPLELATERALDEBUG._serialized_end=2165
  _SIMPLEMPCDEBUG._serialized_start=2168
  _SIMPLEMPCDEBUG._serialized_end=3562
  _CONTROLCOMMAND._serialized_start=3565
  _CONTROLCOMMAND._serialized_end=3803
# @@protoc_insertion_point(module_scope)

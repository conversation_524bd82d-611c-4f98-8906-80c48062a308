syntax = "proto3";
package sim_vehicle.control_io;

import "control/proto/header.proto";


message SimVehicleContorlInput {
  rainbowdash.common.Header header = 1;
  double acc_i = 2;
  int32 gear_i = 3;
  bool is_lat_ctrl = 4;
  double wheel_angle_i = 5;
}


message SimVehicleContorlOutput {
  rainbowdash.common.Header header = 1;
  double speed_o = 2;
  double acc_o = 3; 
  double wheel_angle_o = 4;
  double x_o = 5;
  double y_o = 6;
  double yaw_o =7;
  double gear_o = 8;
}

message SimStatusInit {
  rainbowdash.common.Header header = 1;
  double init_x = 2;
  double init_y = 3;
  double init_yaw = 4;
  double init_speed = 5;
  double init_wheel_angle = 6;
}

message CutInVehicleInfo {
  rainbowdash.common.Header header = 1;
  double delta_s = 2;
  double speed = 3;
  bool cutin_flag = 4;
}


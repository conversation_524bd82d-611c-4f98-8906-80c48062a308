syntax = "proto3";
package ACC_control;

import "control/proto/header.proto";

message ObjVehicleInfo {
    rainbowdash.common.Header header = 1;
    bool check_flag = 2;//是否检测到目标车
    double delta_s = 3;//和目标车的相对距离
    double obj_speed = 4;//目标车的绝对速度
    double obj_accelerate = 5;//目标车的绝对加速度（可以不提供）
    int32 id = 6;
}

//车辆ACC配置
message ACCControlConfigure {
    rainbowdash.common.Header header = 1;
    int32 control_mode = 2;//0:default_mode, 1:cc_mode, 2:acc_mode, 3+:other_modes
    double min_safe_time = 3;//安全时间，默认为2.0s
    double min_stop_distance = 4;//安全停止距离，默认为10.0m
    double set_speed = 5;//ACC模式，车辆最高跟随速度
}

message PIDDebugInfo {
    rainbowdash.common.Header header = 1;
    double error = 2;
    double integral = 3;
    double derivative = 4;
    double pid_output = 5;
}

message ACCDebugInfo {
    rainbowdash.common.Header header = 1;
    int32 cal_acc_mode = 2;//0:CC 1:ACC 2:ACC_CC
    double cal_desired_s = 3;
    double cal_first_speed = 4;
    double cal_warning_distance = 5;
    double cal_break_distance = 6;
    double cal_follow_speed = 7;

    int32 cfg_safe_distance_mode = 8;
    double cfg_set_speed = 9;
    double cfg_min_safe_time = 10;
    double cfg_min_stop_distance = 11;
    int32 cfg_control_mode = 12;

    double cur_speed = 13;
    double cur_plan_acc = 14;

    bool obj_check_flag = 15;
    double obj_delta_s = 16;
    double obj_speed = 17;
    double obj_acc = 18;

    int32 reserved_int1 = 19;
    int32 reserved_int2 = 20;
    double reserved_double1 = 21;
    double reserved_double2 = 22;
    double reserved_double3 = 23;
}



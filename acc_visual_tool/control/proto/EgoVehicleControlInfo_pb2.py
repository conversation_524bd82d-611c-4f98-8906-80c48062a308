# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: control/proto/EgoVehicleControlInfo.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from control.proto import header_pb2 as control_dot_proto_dot_header__pb2
from control.proto import CarlaEgoVehicleStatus_pb2 as control_dot_proto_dot_CarlaEgoVehicleStatus__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='control/proto/EgoVehicleControlInfo.proto',
  package='carla_msgs',
  syntax='proto3',
  serialized_pb=_b('\n)control/proto/EgoVehicleControlInfo.proto\x12\ncarla_msgs\x1a\x1a\x63ontrol/proto/header.proto\x1a)control/proto/CarlaEgoVehicleStatus.proto\"\xd3\x02\n\x15\x45goVehicleControlInfo\x12*\n\x06header\x18\x01 \x01(\x0b\x32\x1a.rainbowdash.common.Header\x12\x39\n\x0crestrictions\x18\x02 \x01(\x0b\x32#.carla_msgs.EgoVehicleControlMaxima\x12\x33\n\x06target\x18\x03 \x01(\x0b\x32#.carla_msgs.EgoVehicleControlTarget\x12\x35\n\x07\x63urrent\x18\x04 \x01(\x0b\x32$.carla_msgs.EgoVehicleControlCurrent\x12\x33\n\x06status\x18\x05 \x01(\x0b\x32#.carla_msgs.EgoVehicleControlStatus\x12\x32\n\x06output\x18\x06 \x01(\x0b\x32\".carla_msgs.CarlaEgoVehicleControl\"\x94\x01\n\x17\x45goVehicleControlMaxima\x12\x1a\n\x12max_steering_angle\x18\x01 \x01(\x01\x12\x11\n\tmax_speed\x18\x02 \x01(\x01\x12\x11\n\tmax_accel\x18\x03 \x01(\x01\x12\x11\n\tmax_decel\x18\x04 \x01(\x01\x12\x11\n\tmin_accel\x18\x05 \x01(\x01\x12\x11\n\tmax_pedal\x18\x06 \x01(\x01\"p\n\x17\x45goVehicleControlTarget\x12\x16\n\x0esteering_angle\x18\x01 \x01(\x01\x12\r\n\x05speed\x18\x02 \x01(\x01\x12\x11\n\tspeed_abs\x18\x03 \x01(\x01\x12\r\n\x05\x61\x63\x63\x65l\x18\x04 \x01(\x01\x12\x0c\n\x04jerk\x18\x05 \x01(\x01\"]\n\x18\x45goVehicleControlCurrent\x12\x10\n\x08time_sec\x18\x01 \x01(\x01\x12\r\n\x05speed\x18\x02 \x01(\x01\x12\x11\n\tspeed_abs\x18\x03 \x01(\x01\x12\r\n\x05\x61\x63\x63\x65l\x18\x04 \x01(\x01\"\x9a\x02\n\x17\x45goVehicleControlStatus\x12\x0e\n\x06status\x18\x01 \x01(\t\x12&\n\x1espeed_control_activation_count\x18\x02 \x01(\r\x12!\n\x19speed_control_accel_delta\x18\x03 \x01(\x01\x12\"\n\x1aspeed_control_accel_target\x18\x04 \x01(\x01\x12!\n\x19\x61\x63\x63\x65l_control_pedal_delta\x18\x05 \x01(\x01\x12\"\n\x1a\x61\x63\x63\x65l_control_pedal_target\x18\x06 \x01(\x01\x12\x1a\n\x12\x62rake_upper_border\x18\x07 \x01(\x01\x12\x1d\n\x15throttle_lower_border\x18\x08 \x01(\x01\x62\x06proto3')
  ,
  dependencies=[control_dot_proto_dot_header__pb2.DESCRIPTOR,control_dot_proto_dot_CarlaEgoVehicleStatus__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)




_EGOVEHICLECONTROLINFO = _descriptor.Descriptor(
  name='EgoVehicleControlInfo',
  full_name='carla_msgs.EgoVehicleControlInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='carla_msgs.EgoVehicleControlInfo.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='restrictions', full_name='carla_msgs.EgoVehicleControlInfo.restrictions', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='target', full_name='carla_msgs.EgoVehicleControlInfo.target', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='current', full_name='carla_msgs.EgoVehicleControlInfo.current', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='status', full_name='carla_msgs.EgoVehicleControlInfo.status', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='output', full_name='carla_msgs.EgoVehicleControlInfo.output', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=129,
  serialized_end=468,
)


_EGOVEHICLECONTROLMAXIMA = _descriptor.Descriptor(
  name='EgoVehicleControlMaxima',
  full_name='carla_msgs.EgoVehicleControlMaxima',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_steering_angle', full_name='carla_msgs.EgoVehicleControlMaxima.max_steering_angle', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_speed', full_name='carla_msgs.EgoVehicleControlMaxima.max_speed', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_accel', full_name='carla_msgs.EgoVehicleControlMaxima.max_accel', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_decel', full_name='carla_msgs.EgoVehicleControlMaxima.max_decel', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='min_accel', full_name='carla_msgs.EgoVehicleControlMaxima.min_accel', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='max_pedal', full_name='carla_msgs.EgoVehicleControlMaxima.max_pedal', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=471,
  serialized_end=619,
)


_EGOVEHICLECONTROLTARGET = _descriptor.Descriptor(
  name='EgoVehicleControlTarget',
  full_name='carla_msgs.EgoVehicleControlTarget',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='steering_angle', full_name='carla_msgs.EgoVehicleControlTarget.steering_angle', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed', full_name='carla_msgs.EgoVehicleControlTarget.speed', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed_abs', full_name='carla_msgs.EgoVehicleControlTarget.speed_abs', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accel', full_name='carla_msgs.EgoVehicleControlTarget.accel', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='jerk', full_name='carla_msgs.EgoVehicleControlTarget.jerk', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=621,
  serialized_end=733,
)


_EGOVEHICLECONTROLCURRENT = _descriptor.Descriptor(
  name='EgoVehicleControlCurrent',
  full_name='carla_msgs.EgoVehicleControlCurrent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time_sec', full_name='carla_msgs.EgoVehicleControlCurrent.time_sec', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed', full_name='carla_msgs.EgoVehicleControlCurrent.speed', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed_abs', full_name='carla_msgs.EgoVehicleControlCurrent.speed_abs', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accel', full_name='carla_msgs.EgoVehicleControlCurrent.accel', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=735,
  serialized_end=828,
)


_EGOVEHICLECONTROLSTATUS = _descriptor.Descriptor(
  name='EgoVehicleControlStatus',
  full_name='carla_msgs.EgoVehicleControlStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='carla_msgs.EgoVehicleControlStatus.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed_control_activation_count', full_name='carla_msgs.EgoVehicleControlStatus.speed_control_activation_count', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed_control_accel_delta', full_name='carla_msgs.EgoVehicleControlStatus.speed_control_accel_delta', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speed_control_accel_target', full_name='carla_msgs.EgoVehicleControlStatus.speed_control_accel_target', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accel_control_pedal_delta', full_name='carla_msgs.EgoVehicleControlStatus.accel_control_pedal_delta', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accel_control_pedal_target', full_name='carla_msgs.EgoVehicleControlStatus.accel_control_pedal_target', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brake_upper_border', full_name='carla_msgs.EgoVehicleControlStatus.brake_upper_border', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='throttle_lower_border', full_name='carla_msgs.EgoVehicleControlStatus.throttle_lower_border', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=831,
  serialized_end=1113,
)

_EGOVEHICLECONTROLINFO.fields_by_name['header'].message_type = control_dot_proto_dot_header__pb2._HEADER
_EGOVEHICLECONTROLINFO.fields_by_name['restrictions'].message_type = _EGOVEHICLECONTROLMAXIMA
_EGOVEHICLECONTROLINFO.fields_by_name['target'].message_type = _EGOVEHICLECONTROLTARGET
_EGOVEHICLECONTROLINFO.fields_by_name['current'].message_type = _EGOVEHICLECONTROLCURRENT
_EGOVEHICLECONTROLINFO.fields_by_name['status'].message_type = _EGOVEHICLECONTROLSTATUS
_EGOVEHICLECONTROLINFO.fields_by_name['output'].message_type = control_dot_proto_dot_CarlaEgoVehicleStatus__pb2._CARLAEGOVEHICLECONTROL
DESCRIPTOR.message_types_by_name['EgoVehicleControlInfo'] = _EGOVEHICLECONTROLINFO
DESCRIPTOR.message_types_by_name['EgoVehicleControlMaxima'] = _EGOVEHICLECONTROLMAXIMA
DESCRIPTOR.message_types_by_name['EgoVehicleControlTarget'] = _EGOVEHICLECONTROLTARGET
DESCRIPTOR.message_types_by_name['EgoVehicleControlCurrent'] = _EGOVEHICLECONTROLCURRENT
DESCRIPTOR.message_types_by_name['EgoVehicleControlStatus'] = _EGOVEHICLECONTROLSTATUS

EgoVehicleControlInfo = _reflection.GeneratedProtocolMessageType('EgoVehicleControlInfo', (_message.Message,), dict(
  DESCRIPTOR = _EGOVEHICLECONTROLINFO,
  __module__ = 'control.proto.EgoVehicleControlInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.EgoVehicleControlInfo)
  ))
_sym_db.RegisterMessage(EgoVehicleControlInfo)

EgoVehicleControlMaxima = _reflection.GeneratedProtocolMessageType('EgoVehicleControlMaxima', (_message.Message,), dict(
  DESCRIPTOR = _EGOVEHICLECONTROLMAXIMA,
  __module__ = 'control.proto.EgoVehicleControlInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.EgoVehicleControlMaxima)
  ))
_sym_db.RegisterMessage(EgoVehicleControlMaxima)

EgoVehicleControlTarget = _reflection.GeneratedProtocolMessageType('EgoVehicleControlTarget', (_message.Message,), dict(
  DESCRIPTOR = _EGOVEHICLECONTROLTARGET,
  __module__ = 'control.proto.EgoVehicleControlInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.EgoVehicleControlTarget)
  ))
_sym_db.RegisterMessage(EgoVehicleControlTarget)

EgoVehicleControlCurrent = _reflection.GeneratedProtocolMessageType('EgoVehicleControlCurrent', (_message.Message,), dict(
  DESCRIPTOR = _EGOVEHICLECONTROLCURRENT,
  __module__ = 'control.proto.EgoVehicleControlInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.EgoVehicleControlCurrent)
  ))
_sym_db.RegisterMessage(EgoVehicleControlCurrent)

EgoVehicleControlStatus = _reflection.GeneratedProtocolMessageType('EgoVehicleControlStatus', (_message.Message,), dict(
  DESCRIPTOR = _EGOVEHICLECONTROLSTATUS,
  __module__ = 'control.proto.EgoVehicleControlInfo_pb2'
  # @@protoc_insertion_point(class_scope:carla_msgs.EgoVehicleControlStatus)
  ))
_sym_db.RegisterMessage(EgoVehicleControlStatus)


# @@protoc_insertion_point(module_scope)
